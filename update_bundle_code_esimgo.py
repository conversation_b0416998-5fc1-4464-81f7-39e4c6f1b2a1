#!/usr//venv3.7/bin/python
from app_models.consumer_models import Profiles
from app_models.reseller_models import Order_history
from main import app

with app.app_context():
    docs_with_plus_orders = Order_history.objects(bundle_code__contains='+')
    for doc in docs_with_plus_orders:
        doc.update(set__bundle_code=doc.bundle_code.replace('+', ''), set__bundle_data__bundle_code=doc.bundle_code.replace('+', ''))

    docs_with_plus_profiles = Profiles.objects(bundle_code__contains='+')
    for doc in docs_with_plus_profiles:
        doc.update(set__bundle_code=doc.bundle_code.replace('+', ''))

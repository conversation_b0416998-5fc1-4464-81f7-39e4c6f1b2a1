# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class ResellerContact(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, emails: List[str]=None, phones: List[str]=None, website: str=None, address: str=None):  # noqa: E501
        """ResellerContact - a model defined in Swagger

        :param emails: The emails of this ResellerContact.  # noqa: E501
        :type emails: List[str]
        :param phones: The phones of this ResellerContact.  # noqa: E501
        :type phones: List[str]
        :param website: The website of this ResellerContact.  # noqa: E501
        :type website: str
        :param address: The address of this ResellerContact.  # noqa: E501
        :type address: str
        """
        self.swagger_types = {
            'emails': List[str],
            'phones': List[str],
            'website': str,
            'address': str
        }

        self.attribute_map = {
            'emails': 'emails',
            'phones': 'phones',
            'website': 'website',
            'address': 'address'
        }
        self._emails = emails
        self._phones = phones
        self._website = website
        self._address = address

    @classmethod
    def from_dict(cls, dikt) -> 'ResellerContact':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Reseller_contact of this ResellerContact.  # noqa: E501
        :rtype: ResellerContact
        """
        return util.deserialize_model(dikt, cls)

    @property
    def emails(self) -> List[str]:
        """Gets the emails of this ResellerContact.


        :return: The emails of this ResellerContact.
        :rtype: List[str]
        """
        return self._emails

    @emails.setter
    def emails(self, emails: List[str]):
        """Sets the emails of this ResellerContact.


        :param emails: The emails of this ResellerContact.
        :type emails: List[str]
        """
        if emails is None:
            raise ValueError("Invalid value for `emails`, must not be `None`")  # noqa: E501

        self._emails = emails

    @property
    def phones(self) -> List[str]:
        """Gets the phones of this ResellerContact.


        :return: The phones of this ResellerContact.
        :rtype: List[str]
        """
        return self._phones

    @phones.setter
    def phones(self, phones: List[str]):
        """Sets the phones of this ResellerContact.


        :param phones: The phones of this ResellerContact.
        :type phones: List[str]
        """
        if phones is None:
            raise ValueError("Invalid value for `phones`, must not be `None`")  # noqa: E501

        self._phones = phones

    @property
    def website(self) -> str:
        """Gets the website of this ResellerContact.


        :return: The website of this ResellerContact.
        :rtype: str
        """
        return self._website

    @website.setter
    def website(self, website: str):
        """Sets the website of this ResellerContact.


        :param website: The website of this ResellerContact.
        :type website: str
        """

        self._website = website

    @property
    def address(self) -> str:
        """Gets the address of this ResellerContact.


        :return: The address of this ResellerContact.
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address: str):
        """Sets the address of this ResellerContact.


        :param address: The address of this ResellerContact.
        :type address: str
        """

        self._address = address

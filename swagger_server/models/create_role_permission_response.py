# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class CreateRolePermissionResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, message: str=None, permission_id: str=None):  # noqa: E501
        """CreateRolePermissionResponse - a model defined in Swagger

        :param message: The message of this CreateRolePermissionResponse.  # noqa: E501
        :type message: str
        :param permission_id: The permission_id of this CreateRolePermissionResponse.  # noqa: E501
        :type permission_id: str
        """
        self.swagger_types = {
            'message': str,
            'permission_id': str
        }

        self.attribute_map = {
            'message': 'message',
            'permission_id': 'permission_id'
        }
        self._message = message
        self._permission_id = permission_id

    @classmethod
    def from_dict(cls, dikt) -> 'CreateRolePermissionResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CreateRolePermissionResponse of this CreateRolePermissionResponse.  # noqa: E501
        :rtype: CreateRolePermissionResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def message(self) -> str:
        """Gets the message of this CreateRolePermissionResponse.


        :return: The message of this CreateRolePermissionResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this CreateRolePermissionResponse.


        :param message: The message of this CreateRolePermissionResponse.
        :type message: str
        """

        self._message = message

    @property
    def permission_id(self) -> str:
        """Gets the permission_id of this CreateRolePermissionResponse.


        :return: The permission_id of this CreateRolePermissionResponse.
        :rtype: str
        """
        return self._permission_id

    @permission_id.setter
    def permission_id(self, permission_id: str):
        """Sets the permission_id of this CreateRolePermissionResponse.


        :param permission_id: The permission_id of this CreateRolePermissionResponse.
        :type permission_id: str
        """

        self._permission_id = permission_id

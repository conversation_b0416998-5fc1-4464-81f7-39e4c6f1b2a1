# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.create_role_request_permissions import CreateRoleRequestPermissions  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class EditRoleRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, name: str=None, permission_level: int=None, access_level: str=None, description: str=None, permissions: List[CreateRoleRequestPermissions]=None):  # noqa: E501
        """EditRoleRequest - a model defined in Swagger

        :param name: The name of this EditRoleRequest.  # noqa: E501
        :type name: str
        :param permission_level: The permission_level of this EditRoleRequest.  # noqa: E501
        :type permission_level: int
        :param access_level: The access_level of this EditRoleRequest.  # noqa: E501
        :type access_level: str
        :param description: The description of this EditRoleRequest.  # noqa: E501
        :type description: str
        :param permissions: The permissions of this EditRoleRequest.  # noqa: E501
        :type permissions: List[CreateRoleRequestPermissions]
        """
        self.swagger_types = {
            'name': str,
            'permission_level': int,
            'access_level': str,
            'description': str,
            'permissions': List[CreateRoleRequestPermissions]
        }

        self.attribute_map = {
            'name': 'name',
            'permission_level': 'permission_level',
            'access_level': 'access_level',
            'description': 'description',
            'permissions': 'permissions'
        }
        self._name = name
        self._permission_level = permission_level
        self._access_level = access_level
        self._description = description
        self._permissions = permissions

    @classmethod
    def from_dict(cls, dikt) -> 'EditRoleRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The EditRoleRequest of this EditRoleRequest.  # noqa: E501
        :rtype: EditRoleRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def name(self) -> str:
        """Gets the name of this EditRoleRequest.


        :return: The name of this EditRoleRequest.
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name: str):
        """Sets the name of this EditRoleRequest.


        :param name: The name of this EditRoleRequest.
        :type name: str
        """

        self._name = name

    @property
    def permission_level(self) -> int:
        """Gets the permission_level of this EditRoleRequest.


        :return: The permission_level of this EditRoleRequest.
        :rtype: int
        """
        return self._permission_level

    @permission_level.setter
    def permission_level(self, permission_level: int):
        """Sets the permission_level of this EditRoleRequest.


        :param permission_level: The permission_level of this EditRoleRequest.
        :type permission_level: int
        """

        self._permission_level = permission_level

    @property
    def access_level(self) -> str:
        """Gets the access_level of this EditRoleRequest.


        :return: The access_level of this EditRoleRequest.
        :rtype: str
        """
        return self._access_level

    @access_level.setter
    def access_level(self, access_level: str):
        """Sets the access_level of this EditRoleRequest.


        :param access_level: The access_level of this EditRoleRequest.
        :type access_level: str
        """
        allowed_values = ["basic", "medium", "sensitive"]  # noqa: E501
        if access_level not in allowed_values:
            raise ValueError(
                "Invalid value for `access_level` ({0}), must be one of {1}"
                .format(access_level, allowed_values)
            )

        self._access_level = access_level

    @property
    def description(self) -> str:
        """Gets the description of this EditRoleRequest.


        :return: The description of this EditRoleRequest.
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description: str):
        """Sets the description of this EditRoleRequest.


        :param description: The description of this EditRoleRequest.
        :type description: str
        """

        self._description = description

    @property
    def permissions(self) -> List[CreateRoleRequestPermissions]:
        """Gets the permissions of this EditRoleRequest.


        :return: The permissions of this EditRoleRequest.
        :rtype: List[CreateRoleRequestPermissions]
        """
        return self._permissions

    @permissions.setter
    def permissions(self, permissions: List[CreateRoleRequestPermissions]):
        """Sets the permissions of this EditRoleRequest.


        :param permissions: The permissions of this EditRoleRequest.
        :type permissions: List[CreateRoleRequestPermissions]
        """

        self._permissions = permissions

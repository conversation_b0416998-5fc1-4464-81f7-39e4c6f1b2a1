# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.reseller_contact import ResellerContact  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class AgentReseller(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reseller_id: str=None, reseller_name: str=None, reseller_type: str='prepaid', support_topup: bool=False, is_active: bool=False, date_created: date=None, currency_code: str='USD', balance: float=None, rate_revenue: float=None, contact: ResellerContact=None):  # noqa: E501
        """AgentReseller - a model defined in Swagger

        :param reseller_id: The reseller_id of this AgentReseller.  # noqa: E501
        :type reseller_id: str
        :param reseller_name: The reseller_name of this AgentReseller.  # noqa: E501
        :type reseller_name: str
        :param reseller_type: The reseller_type of this AgentReseller.  # noqa: E501
        :type reseller_type: str
        :param support_topup: The support_topup of this AgentReseller.  # noqa: E501
        :type support_topup: bool
        :param is_active: The is_active of this AgentReseller.  # noqa: E501
        :type is_active: bool
        :param date_created: The date_created of this AgentReseller.  # noqa: E501
        :type date_created: date
        :param currency_code: The currency_code of this AgentReseller.  # noqa: E501
        :type currency_code: str
        :param balance: The balance of this AgentReseller.  # noqa: E501
        :type balance: float
        :param rate_revenue: The rate_revenue of this AgentReseller.  # noqa: E501
        :type rate_revenue: float
        :param contact: The contact of this AgentReseller.  # noqa: E501
        :type contact: ResellerContact
        """
        self.swagger_types = {
            'reseller_id': str,
            'reseller_name': str,
            'reseller_type': str,
            'support_topup': bool,
            'is_active': bool,
            'date_created': date,
            'currency_code': str,
            'balance': float,
            'rate_revenue': float,
            'contact': ResellerContact
        }

        self.attribute_map = {
            'reseller_id': 'reseller_id',
            'reseller_name': 'reseller_name',
            'reseller_type': 'reseller_type',
            'support_topup': 'support_topup',
            'is_active': 'is_active',
            'date_created': 'date_created',
            'currency_code': 'currency_code',
            'balance': 'balance',
            'rate_revenue': 'rate_revenue',
            'contact': 'contact'
        }
        self._reseller_id = reseller_id
        self._reseller_name = reseller_name
        self._reseller_type = reseller_type
        self._support_topup = support_topup
        self._is_active = is_active
        self._date_created = date_created
        self._currency_code = currency_code
        self._balance = balance
        self._rate_revenue = rate_revenue
        self._contact = contact

    @classmethod
    def from_dict(cls, dikt) -> 'AgentReseller':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Agent_reseller of this AgentReseller.  # noqa: E501
        :rtype: AgentReseller
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this AgentReseller.


        :return: The reseller_id of this AgentReseller.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this AgentReseller.


        :param reseller_id: The reseller_id of this AgentReseller.
        :type reseller_id: str
        """
        if reseller_id is None:
            raise ValueError("Invalid value for `reseller_id`, must not be `None`")  # noqa: E501

        self._reseller_id = reseller_id

    @property
    def reseller_name(self) -> str:
        """Gets the reseller_name of this AgentReseller.


        :return: The reseller_name of this AgentReseller.
        :rtype: str
        """
        return self._reseller_name

    @reseller_name.setter
    def reseller_name(self, reseller_name: str):
        """Sets the reseller_name of this AgentReseller.


        :param reseller_name: The reseller_name of this AgentReseller.
        :type reseller_name: str
        """
        if reseller_name is None:
            raise ValueError("Invalid value for `reseller_name`, must not be `None`")  # noqa: E501

        self._reseller_name = reseller_name

    @property
    def reseller_type(self) -> str:
        """Gets the reseller_type of this AgentReseller.


        :return: The reseller_type of this AgentReseller.
        :rtype: str
        """
        return self._reseller_type

    @reseller_type.setter
    def reseller_type(self, reseller_type: str):
        """Sets the reseller_type of this AgentReseller.


        :param reseller_type: The reseller_type of this AgentReseller.
        :type reseller_type: str
        """
        allowed_values = ["prepaid", "postpaid"]  # noqa: E501
        if reseller_type not in allowed_values:
            raise ValueError(
                "Invalid value for `reseller_type` ({0}), must be one of {1}"
                .format(reseller_type, allowed_values)
            )

        self._reseller_type = reseller_type

    @property
    def support_topup(self) -> bool:
        """Gets the support_topup of this AgentReseller.


        :return: The support_topup of this AgentReseller.
        :rtype: bool
        """
        return self._support_topup

    @support_topup.setter
    def support_topup(self, support_topup: bool):
        """Sets the support_topup of this AgentReseller.


        :param support_topup: The support_topup of this AgentReseller.
        :type support_topup: bool
        """
        if support_topup is None:
            raise ValueError("Invalid value for `support_topup`, must not be `None`")  # noqa: E501

        self._support_topup = support_topup

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this AgentReseller.


        :return: The is_active of this AgentReseller.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this AgentReseller.


        :param is_active: The is_active of this AgentReseller.
        :type is_active: bool
        """
        if is_active is None:
            raise ValueError("Invalid value for `is_active`, must not be `None`")  # noqa: E501

        self._is_active = is_active

    @property
    def date_created(self) -> date:
        """Gets the date_created of this AgentReseller.


        :return: The date_created of this AgentReseller.
        :rtype: date
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: date):
        """Sets the date_created of this AgentReseller.


        :param date_created: The date_created of this AgentReseller.
        :type date_created: date
        """
        if date_created is None:
            raise ValueError("Invalid value for `date_created`, must not be `None`")  # noqa: E501

        self._date_created = date_created

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this AgentReseller.


        :return: The currency_code of this AgentReseller.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this AgentReseller.


        :param currency_code: The currency_code of this AgentReseller.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

    @property
    def balance(self) -> float:
        """Gets the balance of this AgentReseller.


        :return: The balance of this AgentReseller.
        :rtype: float
        """
        return self._balance

    @balance.setter
    def balance(self, balance: float):
        """Sets the balance of this AgentReseller.


        :param balance: The balance of this AgentReseller.
        :type balance: float
        """
        if balance is None:
            raise ValueError("Invalid value for `balance`, must not be `None`")  # noqa: E501

        self._balance = balance

    @property
    def rate_revenue(self) -> float:
        """Gets the rate_revenue of this AgentReseller.

        rate revenue in percent  # noqa: E501

        :return: The rate_revenue of this AgentReseller.
        :rtype: float
        """
        return self._rate_revenue

    @rate_revenue.setter
    def rate_revenue(self, rate_revenue: float):
        """Sets the rate_revenue of this AgentReseller.

        rate revenue in percent  # noqa: E501

        :param rate_revenue: The rate_revenue of this AgentReseller.
        :type rate_revenue: float
        """
        if rate_revenue is None:
            raise ValueError("Invalid value for `rate_revenue`, must not be `None`")  # noqa: E501

        self._rate_revenue = rate_revenue

    @property
    def contact(self) -> ResellerContact:
        """Gets the contact of this AgentReseller.


        :return: The contact of this AgentReseller.
        :rtype: ResellerContact
        """
        return self._contact

    @contact.setter
    def contact(self, contact: ResellerContact):
        """Sets the contact of this AgentReseller.


        :param contact: The contact of this AgentReseller.
        :type contact: ResellerContact
        """
        if contact is None:
            raise ValueError("Invalid value for `contact`, must not be `None`")  # noqa: E501

        self._contact = contact

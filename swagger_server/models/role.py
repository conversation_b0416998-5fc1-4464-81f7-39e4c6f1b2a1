# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class Role(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, role_id: str=None, name: str=None, description: str=None):  # noqa: E501
        """Role - a model defined in Swagger

        :param role_id: The role_id of this Role.  # noqa: E501
        :type role_id: str
        :param name: The name of this Role.  # noqa: E501
        :type name: str
        :param description: The description of this Role.  # noqa: E501
        :type description: str
        """
        self.swagger_types = {
            'role_id': str,
            'name': str,
            'description': str
        }

        self.attribute_map = {
            'role_id': 'role_id',
            'name': 'name',
            'description': 'description'
        }
        self._role_id = role_id
        self._name = name
        self._description = description

    @classmethod
    def from_dict(cls, dikt) -> 'Role':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Role of this Role.  # noqa: E501
        :rtype: Role
        """
        return util.deserialize_model(dikt, cls)

    @property
    def role_id(self) -> str:
        """Gets the role_id of this Role.


        :return: The role_id of this Role.
        :rtype: str
        """
        return self._role_id

    @role_id.setter
    def role_id(self, role_id: str):
        """Sets the role_id of this Role.


        :param role_id: The role_id of this Role.
        :type role_id: str
        """
        if role_id is None:
            raise ValueError("Invalid value for `role_id`, must not be `None`")  # noqa: E501

        self._role_id = role_id

    @property
    def name(self) -> str:
        """Gets the name of this Role.


        :return: The name of this Role.
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name: str):
        """Sets the name of this Role.


        :param name: The name of this Role.
        :type name: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def description(self) -> str:
        """Gets the description of this Role.


        :return: The description of this Role.
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description: str):
        """Sets the description of this Role.


        :param description: The description of this Role.
        :type description: str
        """

        self._description = description

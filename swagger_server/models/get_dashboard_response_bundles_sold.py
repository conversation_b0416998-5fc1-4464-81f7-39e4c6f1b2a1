# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetDashboardResponseBundlesSold(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, _date: str=None, sales_number: int=None):  # noqa: E501
        """GetDashboardResponseBundlesSold - a model defined in Swagger

        :param _date: The _date of this GetDashboardResponseBundlesSold.  # noqa: E501
        :type _date: str
        :param sales_number: The sales_number of this GetDashboardResponseBundlesSold.  # noqa: E501
        :type sales_number: int
        """
        self.swagger_types = {
            '_date': str,
            'sales_number': int
        }

        self.attribute_map = {
            '_date': 'date',
            'sales_number': 'sales_number'
        }
        self.__date = _date
        self._sales_number = sales_number

    @classmethod
    def from_dict(cls, dikt) -> 'GetDashboardResponseBundlesSold':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetDashboardResponse_bundles_sold of this GetDashboardResponseBundlesSold.  # noqa: E501
        :rtype: GetDashboardResponseBundlesSold
        """
        return util.deserialize_model(dikt, cls)

    @property
    def _date(self) -> str:
        """Gets the _date of this GetDashboardResponseBundlesSold.


        :return: The _date of this GetDashboardResponseBundlesSold.
        :rtype: str
        """
        return self.__date

    @_date.setter
    def _date(self, _date: str):
        """Sets the _date of this GetDashboardResponseBundlesSold.


        :param _date: The _date of this GetDashboardResponseBundlesSold.
        :type _date: str
        """

        self.__date = _date

    @property
    def sales_number(self) -> int:
        """Gets the sales_number of this GetDashboardResponseBundlesSold.


        :return: The sales_number of this GetDashboardResponseBundlesSold.
        :rtype: int
        """
        return self._sales_number

    @sales_number.setter
    def sales_number(self, sales_number: int):
        """Sets the sales_number of this GetDashboardResponseBundlesSold.


        :param sales_number: The sales_number of this GetDashboardResponseBundlesSold.
        :type sales_number: int
        """

        self._sales_number = sales_number

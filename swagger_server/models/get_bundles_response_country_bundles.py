# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class GetBundlesResponseCountryBundles(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, bundle_name: str=None, bundle_code: str=None, bundle_marketing_name: str=None, country_code: str=None, country_name: str=None, currency_code_list: List[str]=None, data_unit: str=None, gprs_limit: float=None, subscriber_price: float=None, reseller_retail_price: float=None, validity: int=None):  # noqa: E501
        """GetBundlesResponseCountryBundles - a model defined in Swagger

        :param bundle_name: The bundle_name of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type bundle_name: str
        :param bundle_code: The bundle_code of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type bundle_code: str
        :param bundle_marketing_name: The bundle_marketing_name of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type bundle_marketing_name: str
        :param country_code: The country_code of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type country_code: str
        :param country_name: The country_name of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type country_name: str
        :param currency_code_list: The currency_code_list of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type currency_code_list: List[str]
        :param data_unit: The data_unit of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type data_unit: str
        :param gprs_limit: The gprs_limit of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type gprs_limit: float
        :param subscriber_price: The subscriber_price of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type subscriber_price: float
        :param reseller_retail_price: The reseller_retail_price of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type reseller_retail_price: float
        :param validity: The validity of this GetBundlesResponseCountryBundles.  # noqa: E501
        :type validity: int
        """
        self.swagger_types = {
            'bundle_name': str,
            'bundle_code': str,
            'bundle_marketing_name': str,
            'country_code': str,
            'country_name': str,
            'currency_code_list': List[str],
            'data_unit': str,
            'gprs_limit': float,
            'subscriber_price': float,
            'reseller_retail_price': float,
            'validity': int
        }

        self.attribute_map = {
            'bundle_name': 'bundle_name',
            'bundle_code': 'bundle_code',
            'bundle_marketing_name': 'bundle_marketing_name',
            'country_code': 'country_code',
            'country_name': 'country_name',
            'currency_code_list': 'currency_code_list',
            'data_unit': 'data_unit',
            'gprs_limit': 'gprs_limit',
            'subscriber_price': 'subscriber_price',
            'reseller_retail_price': 'reseller_retail_price',
            'validity': 'validity'
        }
        self._bundle_name = bundle_name
        self._bundle_code = bundle_code
        self._bundle_marketing_name = bundle_marketing_name
        self._country_code = country_code
        self._country_name = country_name
        self._currency_code_list = currency_code_list
        self._data_unit = data_unit
        self._gprs_limit = gprs_limit
        self._subscriber_price = subscriber_price
        self._reseller_retail_price = reseller_retail_price
        self._validity = validity

    @classmethod
    def from_dict(cls, dikt) -> 'GetBundlesResponseCountryBundles':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetBundlesResponse_country_bundles of this GetBundlesResponseCountryBundles.  # noqa: E501
        :rtype: GetBundlesResponseCountryBundles
        """
        return util.deserialize_model(dikt, cls)

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this GetBundlesResponseCountryBundles.


        :return: The bundle_name of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this GetBundlesResponseCountryBundles.


        :param bundle_name: The bundle_name of this GetBundlesResponseCountryBundles.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this GetBundlesResponseCountryBundles.


        :return: The bundle_code of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this GetBundlesResponseCountryBundles.


        :param bundle_code: The bundle_code of this GetBundlesResponseCountryBundles.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def bundle_marketing_name(self) -> str:
        """Gets the bundle_marketing_name of this GetBundlesResponseCountryBundles.


        :return: The bundle_marketing_name of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._bundle_marketing_name

    @bundle_marketing_name.setter
    def bundle_marketing_name(self, bundle_marketing_name: str):
        """Sets the bundle_marketing_name of this GetBundlesResponseCountryBundles.


        :param bundle_marketing_name: The bundle_marketing_name of this GetBundlesResponseCountryBundles.
        :type bundle_marketing_name: str
        """

        self._bundle_marketing_name = bundle_marketing_name

    @property
    def country_code(self) -> str:
        """Gets the country_code of this GetBundlesResponseCountryBundles.


        :return: The country_code of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this GetBundlesResponseCountryBundles.


        :param country_code: The country_code of this GetBundlesResponseCountryBundles.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def country_name(self) -> str:
        """Gets the country_name of this GetBundlesResponseCountryBundles.


        :return: The country_name of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: str):
        """Sets the country_name of this GetBundlesResponseCountryBundles.


        :param country_name: The country_name of this GetBundlesResponseCountryBundles.
        :type country_name: str
        """

        self._country_name = country_name

    @property
    def currency_code_list(self) -> List[str]:
        """Gets the currency_code_list of this GetBundlesResponseCountryBundles.


        :return: The currency_code_list of this GetBundlesResponseCountryBundles.
        :rtype: List[str]
        """
        return self._currency_code_list

    @currency_code_list.setter
    def currency_code_list(self, currency_code_list: List[str]):
        """Sets the currency_code_list of this GetBundlesResponseCountryBundles.


        :param currency_code_list: The currency_code_list of this GetBundlesResponseCountryBundles.
        :type currency_code_list: List[str]
        """
        allowed_values = ["USD"]  # noqa: E501
        if not set(currency_code_list).issubset(set(allowed_values)):
            raise ValueError(
                "Invalid values for `currency_code_list` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(currency_code_list) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._currency_code_list = currency_code_list

    @property
    def data_unit(self) -> str:
        """Gets the data_unit of this GetBundlesResponseCountryBundles.


        :return: The data_unit of this GetBundlesResponseCountryBundles.
        :rtype: str
        """
        return self._data_unit

    @data_unit.setter
    def data_unit(self, data_unit: str):
        """Sets the data_unit of this GetBundlesResponseCountryBundles.


        :param data_unit: The data_unit of this GetBundlesResponseCountryBundles.
        :type data_unit: str
        """
        allowed_values = ["MB", "GB", "TB"]  # noqa: E501
        if data_unit not in allowed_values:
            raise ValueError(
                "Invalid value for `data_unit` ({0}), must be one of {1}"
                .format(data_unit, allowed_values)
            )

        self._data_unit = data_unit

    @property
    def gprs_limit(self) -> float:
        """Gets the gprs_limit of this GetBundlesResponseCountryBundles.


        :return: The gprs_limit of this GetBundlesResponseCountryBundles.
        :rtype: float
        """
        return self._gprs_limit

    @gprs_limit.setter
    def gprs_limit(self, gprs_limit: float):
        """Sets the gprs_limit of this GetBundlesResponseCountryBundles.


        :param gprs_limit: The gprs_limit of this GetBundlesResponseCountryBundles.
        :type gprs_limit: float
        """

        self._gprs_limit = gprs_limit

    @property
    def subscriber_price(self) -> float:
        """Gets the subscriber_price of this GetBundlesResponseCountryBundles.


        :return: The subscriber_price of this GetBundlesResponseCountryBundles.
        :rtype: float
        """
        return self._subscriber_price

    @subscriber_price.setter
    def subscriber_price(self, subscriber_price: float):
        """Sets the subscriber_price of this GetBundlesResponseCountryBundles.


        :param subscriber_price: The subscriber_price of this GetBundlesResponseCountryBundles.
        :type subscriber_price: float
        """

        self._subscriber_price = subscriber_price

    @property
    def reseller_retail_price(self) -> float:
        """Gets the reseller_retail_price of this GetBundlesResponseCountryBundles.


        :return: The reseller_retail_price of this GetBundlesResponseCountryBundles.
        :rtype: float
        """
        return self._reseller_retail_price

    @reseller_retail_price.setter
    def reseller_retail_price(self, reseller_retail_price: float):
        """Sets the reseller_retail_price of this GetBundlesResponseCountryBundles.


        :param reseller_retail_price: The reseller_retail_price of this GetBundlesResponseCountryBundles.
        :type reseller_retail_price: float
        """

        self._reseller_retail_price = reseller_retail_price

    @property
    def validity(self) -> int:
        """Gets the validity of this GetBundlesResponseCountryBundles.

        amount in days of validity  # noqa: E501

        :return: The validity of this GetBundlesResponseCountryBundles.
        :rtype: int
        """
        return self._validity

    @validity.setter
    def validity(self, validity: int):
        """Sets the validity of this GetBundlesResponseCountryBundles.

        amount in days of validity  # noqa: E501

        :param validity: The validity of this GetBundlesResponseCountryBundles.
        :type validity: int
        """

        self._validity = validity

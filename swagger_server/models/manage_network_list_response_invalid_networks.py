# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class ManageNetworkListResponseInvalidNetworks(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, country_code: str=None, vendor_name: str=None):  # noqa: E501
        """ManageNetworkListResponseInvalidNetworks - a model defined in Swagger

        :param country_code: The country_code of this ManageNetworkListResponseInvalidNetworks.  # noqa: E501
        :type country_code: str
        :param vendor_name: The vendor_name of this ManageNetworkListResponseInvalidNetworks.  # noqa: E501
        :type vendor_name: str
        """
        self.swagger_types = {
            'country_code': str,
            'vendor_name': str
        }

        self.attribute_map = {
            'country_code': 'country_code',
            'vendor_name': 'vendor_name'
        }
        self._country_code = country_code
        self._vendor_name = vendor_name

    @classmethod
    def from_dict(cls, dikt) -> 'ManageNetworkListResponseInvalidNetworks':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ManageNetworkListResponse_invalid_networks of this ManageNetworkListResponseInvalidNetworks.  # noqa: E501
        :rtype: ManageNetworkListResponseInvalidNetworks
        """
        return util.deserialize_model(dikt, cls)

    @property
    def country_code(self) -> str:
        """Gets the country_code of this ManageNetworkListResponseInvalidNetworks.


        :return: The country_code of this ManageNetworkListResponseInvalidNetworks.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this ManageNetworkListResponseInvalidNetworks.


        :param country_code: The country_code of this ManageNetworkListResponseInvalidNetworks.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def vendor_name(self) -> str:
        """Gets the vendor_name of this ManageNetworkListResponseInvalidNetworks.


        :return: The vendor_name of this ManageNetworkListResponseInvalidNetworks.
        :rtype: str
        """
        return self._vendor_name

    @vendor_name.setter
    def vendor_name(self, vendor_name: str):
        """Sets the vendor_name of this ManageNetworkListResponseInvalidNetworks.


        :param vendor_name: The vendor_name of this ManageNetworkListResponseInvalidNetworks.
        :type vendor_name: str
        """

        self._vendor_name = vendor_name

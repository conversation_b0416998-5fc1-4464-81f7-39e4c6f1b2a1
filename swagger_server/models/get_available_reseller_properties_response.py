# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetAvailableResellerPropertiesResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, categories: List[str]=None):  # noqa: E501
        """GetAvailableResellerPropertiesResponse - a model defined in Swagger

        :param response_code: The response_code of this GetAvailableResellerPropertiesResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetAvailableResellerPropertiesResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetAvailableResellerPropertiesResponse.  # noqa: E501
        :type title: str
        :param categories: The categories of this GetAvailableResellerPropertiesResponse.  # noqa: E501
        :type categories: List[str]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'categories': List[str]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'categories': 'categories'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._categories = categories

    @classmethod
    def from_dict(cls, dikt) -> 'GetAvailableResellerPropertiesResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetAvailableResellerPropertiesResponse of this GetAvailableResellerPropertiesResponse.  # noqa: E501
        :rtype: GetAvailableResellerPropertiesResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetAvailableResellerPropertiesResponse.


        :return: The response_code of this GetAvailableResellerPropertiesResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetAvailableResellerPropertiesResponse.


        :param response_code: The response_code of this GetAvailableResellerPropertiesResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetAvailableResellerPropertiesResponse.


        :return: The developer_message of this GetAvailableResellerPropertiesResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetAvailableResellerPropertiesResponse.


        :param developer_message: The developer_message of this GetAvailableResellerPropertiesResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetAvailableResellerPropertiesResponse.


        :return: The title of this GetAvailableResellerPropertiesResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetAvailableResellerPropertiesResponse.


        :param title: The title of this GetAvailableResellerPropertiesResponse.
        :type title: str
        """

        self._title = title

    @property
    def categories(self) -> List[str]:
        """Gets the categories of this GetAvailableResellerPropertiesResponse.


        :return: The categories of this GetAvailableResellerPropertiesResponse.
        :rtype: List[str]
        """
        return self._categories

    @categories.setter
    def categories(self, categories: List[str]):
        """Sets the categories of this GetAvailableResellerPropertiesResponse.


        :param categories: The categories of this GetAvailableResellerPropertiesResponse.
        :type categories: List[str]
        """

        self._categories = categories

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class DeleteNetworkListResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, message: str=None):  # noqa: E501
        """DeleteNetworkListResponse - a model defined in Swagger

        :param message: The message of this DeleteNetworkListResponse.  # noqa: E501
        :type message: str
        """
        self.swagger_types = {
            'message': str
        }

        self.attribute_map = {
            'message': 'Message'
        }
        self._message = message

    @classmethod
    def from_dict(cls, dikt) -> 'DeleteNetworkListResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The DeleteNetworkListResponse of this DeleteNetworkListResponse.  # noqa: E501
        :rtype: DeleteNetworkListResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def message(self) -> str:
        """Gets the message of this DeleteNetworkListResponse.


        :return: The message of this DeleteNetworkListResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this DeleteNetworkListResponse.


        :param message: The message of this DeleteNetworkListResponse.
        :type message: str
        """

        self._message = message

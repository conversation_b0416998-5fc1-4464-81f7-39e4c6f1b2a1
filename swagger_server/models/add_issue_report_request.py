# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class AddIssueReportRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, issue_type: str=None, issue_subject: str=None, issue_description: str=None, priority: int=3, attachments: List[str]=None):  # noqa: E501
        """AddIssueReportRequest - a model defined in Swagger

        :param issue_type: The issue_type of this AddIssueReportRequest.  # noqa: E501
        :type issue_type: str
        :param issue_subject: The issue_subject of this AddIssueReportRequest.  # noqa: E501
        :type issue_subject: str
        :param issue_description: The issue_description of this AddIssueReportRequest.  # noqa: E501
        :type issue_description: str
        :param priority: The priority of this AddIssueReportRequest.  # noqa: E501
        :type priority: int
        :param attachments: The attachments of this AddIssueReportRequest.  # noqa: E501
        :type attachments: List[str]
        """
        self.swagger_types = {
            'issue_type': str,
            'issue_subject': str,
            'issue_description': str,
            'priority': int,
            'attachments': List[str]
        }

        self.attribute_map = {
            'issue_type': 'issue_type',
            'issue_subject': 'issue_subject',
            'issue_description': 'issue_description',
            'priority': 'priority',
            'attachments': 'attachments'
        }
        self._issue_type = issue_type
        self._issue_subject = issue_subject
        self._issue_description = issue_description
        self._priority = priority
        self._attachments = attachments

    @classmethod
    def from_dict(cls, dikt) -> 'AddIssueReportRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AddIssueReportRequest of this AddIssueReportRequest.  # noqa: E501
        :rtype: AddIssueReportRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def issue_type(self) -> str:
        """Gets the issue_type of this AddIssueReportRequest.

        Type of the issue (e.g., \"Technical Issue\" or \"Price-related Concern\").  # noqa: E501

        :return: The issue_type of this AddIssueReportRequest.
        :rtype: str
        """
        return self._issue_type

    @issue_type.setter
    def issue_type(self, issue_type: str):
        """Sets the issue_type of this AddIssueReportRequest.

        Type of the issue (e.g., \"Technical Issue\" or \"Price-related Concern\").  # noqa: E501

        :param issue_type: The issue_type of this AddIssueReportRequest.
        :type issue_type: str
        """
        allowed_values = ["Technical Issue", "Price-related Concern"]  # noqa: E501
        if issue_type not in allowed_values:
            raise ValueError(
                "Invalid value for `issue_type` ({0}), must be one of {1}"
                .format(issue_type, allowed_values)
            )

        self._issue_type = issue_type

    @property
    def issue_subject(self) -> str:
        """Gets the issue_subject of this AddIssueReportRequest.

        Summary of Issue  # noqa: E501

        :return: The issue_subject of this AddIssueReportRequest.
        :rtype: str
        """
        return self._issue_subject

    @issue_subject.setter
    def issue_subject(self, issue_subject: str):
        """Sets the issue_subject of this AddIssueReportRequest.

        Summary of Issue  # noqa: E501

        :param issue_subject: The issue_subject of this AddIssueReportRequest.
        :type issue_subject: str
        """

        self._issue_subject = issue_subject

    @property
    def issue_description(self) -> str:
        """Gets the issue_description of this AddIssueReportRequest.

        Detailed description of the issue or concern.  # noqa: E501

        :return: The issue_description of this AddIssueReportRequest.
        :rtype: str
        """
        return self._issue_description

    @issue_description.setter
    def issue_description(self, issue_description: str):
        """Sets the issue_description of this AddIssueReportRequest.

        Detailed description of the issue or concern.  # noqa: E501

        :param issue_description: The issue_description of this AddIssueReportRequest.
        :type issue_description: str
        """
        if issue_description is None:
            raise ValueError("Invalid value for `issue_description`, must not be `None`")  # noqa: E501

        self._issue_description = issue_description

    @property
    def priority(self) -> int:
        """Gets the priority of this AddIssueReportRequest.

        Priority level of the issue (\"Critical :1\", \"High:2\", \"Medium:3\", or \"Low:4\").  # noqa: E501

        :return: The priority of this AddIssueReportRequest.
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority: int):
        """Sets the priority of this AddIssueReportRequest.

        Priority level of the issue (\"Critical :1\", \"High:2\", \"Medium:3\", or \"Low:4\").  # noqa: E501

        :param priority: The priority of this AddIssueReportRequest.
        :type priority: int
        """

        self._priority = priority

    @property
    def attachments(self) -> List[str]:
        """Gets the attachments of this AddIssueReportRequest.

        List of attached files.  # noqa: E501

        :return: The attachments of this AddIssueReportRequest.
        :rtype: List[str]
        """
        return self._attachments

    @attachments.setter
    def attachments(self, attachments: List[str]):
        """Sets the attachments of this AddIssueReportRequest.

        List of attached files.  # noqa: E501

        :param attachments: The attachments of this AddIssueReportRequest.
        :type attachments: List[str]
        """

        self._attachments = attachments

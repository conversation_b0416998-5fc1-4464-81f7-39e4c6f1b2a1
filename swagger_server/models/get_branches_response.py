# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_branches_response_branches import GetBranchesResponseBranches  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetBranchesResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, total_branches_count: int=None, branches: List[GetBranchesResponseBranches]=None):  # noqa: E501
        """GetBranchesResponse - a model defined in Swagger

        :param response_code: The response_code of this GetBranchesResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetBranchesResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetBranchesResponse.  # noqa: E501
        :type title: str
        :param total_branches_count: The total_branches_count of this GetBranchesResponse.  # noqa: E501
        :type total_branches_count: int
        :param branches: The branches of this GetBranchesResponse.  # noqa: E501
        :type branches: List[GetBranchesResponseBranches]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'total_branches_count': int,
            'branches': List[GetBranchesResponseBranches]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'total_branches_count': 'total_branches_count',
            'branches': 'branches'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._total_branches_count = total_branches_count
        self._branches = branches

    @classmethod
    def from_dict(cls, dikt) -> 'GetBranchesResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetBranchesResponse of this GetBranchesResponse.  # noqa: E501
        :rtype: GetBranchesResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetBranchesResponse.


        :return: The response_code of this GetBranchesResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetBranchesResponse.


        :param response_code: The response_code of this GetBranchesResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetBranchesResponse.


        :return: The developer_message of this GetBranchesResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetBranchesResponse.


        :param developer_message: The developer_message of this GetBranchesResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetBranchesResponse.


        :return: The title of this GetBranchesResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetBranchesResponse.


        :param title: The title of this GetBranchesResponse.
        :type title: str
        """

        self._title = title

    @property
    def total_branches_count(self) -> int:
        """Gets the total_branches_count of this GetBranchesResponse.


        :return: The total_branches_count of this GetBranchesResponse.
        :rtype: int
        """
        return self._total_branches_count

    @total_branches_count.setter
    def total_branches_count(self, total_branches_count: int):
        """Sets the total_branches_count of this GetBranchesResponse.


        :param total_branches_count: The total_branches_count of this GetBranchesResponse.
        :type total_branches_count: int
        """

        self._total_branches_count = total_branches_count

    @property
    def branches(self) -> List[GetBranchesResponseBranches]:
        """Gets the branches of this GetBranchesResponse.


        :return: The branches of this GetBranchesResponse.
        :rtype: List[GetBranchesResponseBranches]
        """
        return self._branches

    @branches.setter
    def branches(self, branches: List[GetBranchesResponseBranches]):
        """Sets the branches of this GetBranchesResponse.


        :param branches: The branches of this GetBranchesResponse.
        :type branches: List[GetBranchesResponseBranches]
        """

        self._branches = branches

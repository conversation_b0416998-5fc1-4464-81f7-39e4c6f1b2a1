# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.reseller_contact import ResellerContact  # noqa: F401,E501
from swagger_server.models.reseller_data_consumption_email import ResellerDataConsumptionEmail  # noqa: F401,E501
from swagger_server.models.reseller_data_expired_email import ResellerDataExpiredEmail  # noqa: F401,E501
from swagger_server.models.reseller_email_settings import ResellerEmailSettings  # noqa: F401,E501
from swagger_server.models.reseller_qr_code_email import ResellerQrCodeEmail  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class Reseller(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, reseller_id: str=None, reseller_name: str=None, reseller_type: str='prepaid', callback_url: str=None, support_topup: bool=False, is_active: bool=False, supports_multibranches: bool=False, supports_promo: bool=None, supports_vouchers: bool=None, date_created: date=None, currency_code: str='USD', additional_currency_code: str=None, default_currency_code: str='USD', balance: float=None, balance_in_additional_currency: float=None, balance_warning_limit: float=None, credit_limit: float=0, credit_warning_limit: float=0, credit_limit_in_additional_currency: float=None, rate_revenue: float=0, corp_rate_revenue: float=None, voucher_rate: float=None, contact: ResellerContact=None, email_settings: ResellerEmailSettings=None, custom_email_template_qr: str=None, custom_email_template_data: str=None, custom_email_template_expired: str=None, image: str=None, image_type: str=None, is_whitelabel: bool=False, tenant_name: str=None, reseller_category: str=None, active_vendors_list: List[str]=None, vendors_for_balance_deduction_list: List[str]=None, request_custom_email: bool=True, data_consumption_email: ResellerDataConsumptionEmail=None, data_expired_email: ResellerDataExpiredEmail=None, qr_code_email: ResellerQrCodeEmail=None):  # noqa: E501
        """Reseller - a model defined in Swagger

        :param response_code: The response_code of this Reseller.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this Reseller.  # noqa: E501
        :type developer_message: str
        :param title: The title of this Reseller.  # noqa: E501
        :type title: str
        :param reseller_id: The reseller_id of this Reseller.  # noqa: E501
        :type reseller_id: str
        :param reseller_name: The reseller_name of this Reseller.  # noqa: E501
        :type reseller_name: str
        :param reseller_type: The reseller_type of this Reseller.  # noqa: E501
        :type reseller_type: str
        :param callback_url: The callback_url of this Reseller.  # noqa: E501
        :type callback_url: str
        :param support_topup: The support_topup of this Reseller.  # noqa: E501
        :type support_topup: bool
        :param is_active: The is_active of this Reseller.  # noqa: E501
        :type is_active: bool
        :param supports_multibranches: The supports_multibranches of this Reseller.  # noqa: E501
        :type supports_multibranches: bool
        :param supports_promo: The supports_promo of this Reseller.  # noqa: E501
        :type supports_promo: bool
        :param supports_vouchers: The supports_vouchers of this Reseller.  # noqa: E501
        :type supports_vouchers: bool
        :param date_created: The date_created of this Reseller.  # noqa: E501
        :type date_created: date
        :param currency_code: The currency_code of this Reseller.  # noqa: E501
        :type currency_code: str
        :param additional_currency_code: The additional_currency_code of this Reseller.  # noqa: E501
        :type additional_currency_code: str
        :param default_currency_code: The default_currency_code of this Reseller.  # noqa: E501
        :type default_currency_code: str
        :param balance: The balance of this Reseller.  # noqa: E501
        :type balance: float
        :param balance_in_additional_currency: The balance_in_additional_currency of this Reseller.  # noqa: E501
        :type balance_in_additional_currency: float
        :param balance_warning_limit: The balance_warning_limit of this Reseller.  # noqa: E501
        :type balance_warning_limit: float
        :param credit_limit: The credit_limit of this Reseller.  # noqa: E501
        :type credit_limit: float
        :param credit_warning_limit: The credit_warning_limit of this Reseller.  # noqa: E501
        :type credit_warning_limit: float
        :param credit_limit_in_additional_currency: The credit_limit_in_additional_currency of this Reseller.  # noqa: E501
        :type credit_limit_in_additional_currency: float
        :param rate_revenue: The rate_revenue of this Reseller.  # noqa: E501
        :type rate_revenue: float
        :param corp_rate_revenue: The corp_rate_revenue of this Reseller.  # noqa: E501
        :type corp_rate_revenue: float
        :param voucher_rate: The voucher_rate of this Reseller.  # noqa: E501
        :type voucher_rate: float
        :param contact: The contact of this Reseller.  # noqa: E501
        :type contact: ResellerContact
        :param email_settings: The email_settings of this Reseller.  # noqa: E501
        :type email_settings: ResellerEmailSettings
        :param custom_email_template_qr: The custom_email_template_qr of this Reseller.  # noqa: E501
        :type custom_email_template_qr: str
        :param custom_email_template_data: The custom_email_template_data of this Reseller.  # noqa: E501
        :type custom_email_template_data: str
        :param custom_email_template_expired: The custom_email_template_expired of this Reseller.  # noqa: E501
        :type custom_email_template_expired: str
        :param image: The image of this Reseller.  # noqa: E501
        :type image: str
        :param image_type: The image_type of this Reseller.  # noqa: E501
        :type image_type: str
        :param is_whitelabel: The is_whitelabel of this Reseller.  # noqa: E501
        :type is_whitelabel: bool
        :param tenant_name: The tenant_name of this Reseller.  # noqa: E501
        :type tenant_name: str
        :param reseller_category: The reseller_category of this Reseller.  # noqa: E501
        :type reseller_category: str
        :param active_vendors_list: The active_vendors_list of this Reseller.  # noqa: E501
        :type active_vendors_list: List[str]
        :param vendors_for_balance_deduction_list: The vendors_for_balance_deduction_list of this Reseller.  # noqa: E501
        :type vendors_for_balance_deduction_list: List[str]
        :param request_custom_email: The request_custom_email of this Reseller.  # noqa: E501
        :type request_custom_email: bool
        :param data_consumption_email: The data_consumption_email of this Reseller.  # noqa: E501
        :type data_consumption_email: ResellerDataConsumptionEmail
        :param data_expired_email: The data_expired_email of this Reseller.  # noqa: E501
        :type data_expired_email: ResellerDataExpiredEmail
        :param qr_code_email: The qr_code_email of this Reseller.  # noqa: E501
        :type qr_code_email: ResellerQrCodeEmail
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'reseller_id': str,
            'reseller_name': str,
            'reseller_type': str,
            'callback_url': str,
            'support_topup': bool,
            'is_active': bool,
            'supports_multibranches': bool,
            'supports_promo': bool,
            'supports_vouchers': bool,
            'date_created': date,
            'currency_code': str,
            'additional_currency_code': str,
            'default_currency_code': str,
            'balance': float,
            'balance_in_additional_currency': float,
            'balance_warning_limit': float,
            'credit_limit': float,
            'credit_warning_limit': float,
            'credit_limit_in_additional_currency': float,
            'rate_revenue': float,
            'corp_rate_revenue': float,
            'voucher_rate': float,
            'contact': ResellerContact,
            'email_settings': ResellerEmailSettings,
            'custom_email_template_qr': str,
            'custom_email_template_data': str,
            'custom_email_template_expired': str,
            'image': str,
            'image_type': str,
            'is_whitelabel': bool,
            'tenant_name': str,
            'reseller_category': str,
            'active_vendors_list': List[str],
            'vendors_for_balance_deduction_list': List[str],
            'request_custom_email': bool,
            'data_consumption_email': ResellerDataConsumptionEmail,
            'data_expired_email': ResellerDataExpiredEmail,
            'qr_code_email': ResellerQrCodeEmail
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'reseller_id': 'reseller_id',
            'reseller_name': 'reseller_name',
            'reseller_type': 'reseller_type',
            'callback_url': 'callback_url',
            'support_topup': 'support_topup',
            'is_active': 'is_active',
            'supports_multibranches': 'supports_multibranches',
            'supports_promo': 'supports_promo',
            'supports_vouchers': 'supports_vouchers',
            'date_created': 'date_created',
            'currency_code': 'currency_code',
            'additional_currency_code': 'additional_currency_code',
            'default_currency_code': 'default_currency_code',
            'balance': 'balance',
            'balance_in_additional_currency': 'balance_in_additional_currency',
            'balance_warning_limit': 'balance_warning_limit',
            'credit_limit': 'credit_limit',
            'credit_warning_limit': 'credit_warning_limit',
            'credit_limit_in_additional_currency': 'credit_limit_in_additional_currency',
            'rate_revenue': 'rate_revenue',
            'corp_rate_revenue': 'corp_rate_revenue',
            'voucher_rate': 'voucher_rate',
            'contact': 'contact',
            'email_settings': 'email_settings',
            'custom_email_template_qr': 'custom_email_template_qr',
            'custom_email_template_data': 'custom_email_template_data',
            'custom_email_template_expired': 'custom_email_template_expired',
            'image': 'image',
            'image_type': 'image_type',
            'is_whitelabel': 'is_whitelabel',
            'tenant_name': 'tenant_name',
            'reseller_category': 'reseller_category',
            'active_vendors_list': 'active_vendors_list',
            'vendors_for_balance_deduction_list': 'vendors_for_balance_deduction_list',
            'request_custom_email': 'request_custom_email',
            'data_consumption_email': 'data_consumption_email',
            'data_expired_email': 'data_expired_email',
            'qr_code_email': 'qr_code_email'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._reseller_id = reseller_id
        self._reseller_name = reseller_name
        self._reseller_type = reseller_type
        self._callback_url = callback_url
        self._support_topup = support_topup
        self._is_active = is_active
        self._supports_multibranches = supports_multibranches
        self._supports_promo = supports_promo
        self._supports_vouchers = supports_vouchers
        self._date_created = date_created
        self._currency_code = currency_code
        self._additional_currency_code = additional_currency_code
        self._default_currency_code = default_currency_code
        self._balance = balance
        self._balance_in_additional_currency = balance_in_additional_currency
        self._balance_warning_limit = balance_warning_limit
        self._credit_limit = credit_limit
        self._credit_warning_limit = credit_warning_limit
        self._credit_limit_in_additional_currency = credit_limit_in_additional_currency
        self._rate_revenue = rate_revenue
        self._corp_rate_revenue = corp_rate_revenue
        self._voucher_rate = voucher_rate
        self._contact = contact
        self._email_settings = email_settings
        self._custom_email_template_qr = custom_email_template_qr
        self._custom_email_template_data = custom_email_template_data
        self._custom_email_template_expired = custom_email_template_expired
        self._image = image
        self._image_type = image_type
        self._is_whitelabel = is_whitelabel
        self._tenant_name = tenant_name
        self._reseller_category = reseller_category
        self._active_vendors_list = active_vendors_list
        self._vendors_for_balance_deduction_list = vendors_for_balance_deduction_list
        self._request_custom_email = request_custom_email
        self._data_consumption_email = data_consumption_email
        self._data_expired_email = data_expired_email
        self._qr_code_email = qr_code_email

    @classmethod
    def from_dict(cls, dikt) -> 'Reseller':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Reseller of this Reseller.  # noqa: E501
        :rtype: Reseller
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this Reseller.


        :return: The response_code of this Reseller.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this Reseller.


        :param response_code: The response_code of this Reseller.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this Reseller.


        :return: The developer_message of this Reseller.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this Reseller.


        :param developer_message: The developer_message of this Reseller.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this Reseller.


        :return: The title of this Reseller.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this Reseller.


        :param title: The title of this Reseller.
        :type title: str
        """

        self._title = title

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this Reseller.


        :return: The reseller_id of this Reseller.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this Reseller.


        :param reseller_id: The reseller_id of this Reseller.
        :type reseller_id: str
        """
        if reseller_id is None:
            raise ValueError("Invalid value for `reseller_id`, must not be `None`")  # noqa: E501

        self._reseller_id = reseller_id

    @property
    def reseller_name(self) -> str:
        """Gets the reseller_name of this Reseller.


        :return: The reseller_name of this Reseller.
        :rtype: str
        """
        return self._reseller_name

    @reseller_name.setter
    def reseller_name(self, reseller_name: str):
        """Sets the reseller_name of this Reseller.


        :param reseller_name: The reseller_name of this Reseller.
        :type reseller_name: str
        """
        if reseller_name is None:
            raise ValueError("Invalid value for `reseller_name`, must not be `None`")  # noqa: E501

        self._reseller_name = reseller_name

    @property
    def reseller_type(self) -> str:
        """Gets the reseller_type of this Reseller.


        :return: The reseller_type of this Reseller.
        :rtype: str
        """
        return self._reseller_type

    @reseller_type.setter
    def reseller_type(self, reseller_type: str):
        """Sets the reseller_type of this Reseller.


        :param reseller_type: The reseller_type of this Reseller.
        :type reseller_type: str
        """
        allowed_values = ["prepaid", "postpaid"]  # noqa: E501
        if reseller_type not in allowed_values:
            raise ValueError(
                "Invalid value for `reseller_type` ({0}), must be one of {1}"
                .format(reseller_type, allowed_values)
            )

        self._reseller_type = reseller_type

    @property
    def callback_url(self) -> str:
        """Gets the callback_url of this Reseller.


        :return: The callback_url of this Reseller.
        :rtype: str
        """
        return self._callback_url

    @callback_url.setter
    def callback_url(self, callback_url: str):
        """Sets the callback_url of this Reseller.


        :param callback_url: The callback_url of this Reseller.
        :type callback_url: str
        """

        self._callback_url = callback_url

    @property
    def support_topup(self) -> bool:
        """Gets the support_topup of this Reseller.


        :return: The support_topup of this Reseller.
        :rtype: bool
        """
        return self._support_topup

    @support_topup.setter
    def support_topup(self, support_topup: bool):
        """Sets the support_topup of this Reseller.


        :param support_topup: The support_topup of this Reseller.
        :type support_topup: bool
        """
        if support_topup is None:
            raise ValueError("Invalid value for `support_topup`, must not be `None`")  # noqa: E501

        self._support_topup = support_topup

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this Reseller.


        :return: The is_active of this Reseller.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this Reseller.


        :param is_active: The is_active of this Reseller.
        :type is_active: bool
        """
        if is_active is None:
            raise ValueError("Invalid value for `is_active`, must not be `None`")  # noqa: E501

        self._is_active = is_active

    @property
    def supports_multibranches(self) -> bool:
        """Gets the supports_multibranches of this Reseller.


        :return: The supports_multibranches of this Reseller.
        :rtype: bool
        """
        return self._supports_multibranches

    @supports_multibranches.setter
    def supports_multibranches(self, supports_multibranches: bool):
        """Sets the supports_multibranches of this Reseller.


        :param supports_multibranches: The supports_multibranches of this Reseller.
        :type supports_multibranches: bool
        """

        self._supports_multibranches = supports_multibranches

    @property
    def supports_promo(self) -> bool:
        """Gets the supports_promo of this Reseller.


        :return: The supports_promo of this Reseller.
        :rtype: bool
        """
        return self._supports_promo

    @supports_promo.setter
    def supports_promo(self, supports_promo: bool):
        """Sets the supports_promo of this Reseller.


        :param supports_promo: The supports_promo of this Reseller.
        :type supports_promo: bool
        """

        self._supports_promo = supports_promo

    @property
    def supports_vouchers(self) -> bool:
        """Gets the supports_vouchers of this Reseller.


        :return: The supports_vouchers of this Reseller.
        :rtype: bool
        """
        return self._supports_vouchers

    @supports_vouchers.setter
    def supports_vouchers(self, supports_vouchers: bool):
        """Sets the supports_vouchers of this Reseller.


        :param supports_vouchers: The supports_vouchers of this Reseller.
        :type supports_vouchers: bool
        """

        self._supports_vouchers = supports_vouchers

    @property
    def date_created(self) -> date:
        """Gets the date_created of this Reseller.


        :return: The date_created of this Reseller.
        :rtype: date
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: date):
        """Sets the date_created of this Reseller.


        :param date_created: The date_created of this Reseller.
        :type date_created: date
        """
        if date_created is None:
            raise ValueError("Invalid value for `date_created`, must not be `None`")  # noqa: E501

        self._date_created = date_created

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this Reseller.


        :return: The currency_code of this Reseller.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this Reseller.


        :param currency_code: The currency_code of this Reseller.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

    @property
    def additional_currency_code(self) -> str:
        """Gets the additional_currency_code of this Reseller.


        :return: The additional_currency_code of this Reseller.
        :rtype: str
        """
        return self._additional_currency_code

    @additional_currency_code.setter
    def additional_currency_code(self, additional_currency_code: str):
        """Sets the additional_currency_code of this Reseller.


        :param additional_currency_code: The additional_currency_code of this Reseller.
        :type additional_currency_code: str
        """

        self._additional_currency_code = additional_currency_code

    @property
    def default_currency_code(self) -> str:
        """Gets the default_currency_code of this Reseller.


        :return: The default_currency_code of this Reseller.
        :rtype: str
        """
        return self._default_currency_code

    @default_currency_code.setter
    def default_currency_code(self, default_currency_code: str):
        """Sets the default_currency_code of this Reseller.


        :param default_currency_code: The default_currency_code of this Reseller.
        :type default_currency_code: str
        """

        self._default_currency_code = default_currency_code

    @property
    def balance(self) -> float:
        """Gets the balance of this Reseller.


        :return: The balance of this Reseller.
        :rtype: float
        """
        return self._balance

    @balance.setter
    def balance(self, balance: float):
        """Sets the balance of this Reseller.


        :param balance: The balance of this Reseller.
        :type balance: float
        """
        if balance is None:
            raise ValueError("Invalid value for `balance`, must not be `None`")  # noqa: E501

        self._balance = balance

    @property
    def balance_in_additional_currency(self) -> float:
        """Gets the balance_in_additional_currency of this Reseller.


        :return: The balance_in_additional_currency of this Reseller.
        :rtype: float
        """
        return self._balance_in_additional_currency

    @balance_in_additional_currency.setter
    def balance_in_additional_currency(self, balance_in_additional_currency: float):
        """Sets the balance_in_additional_currency of this Reseller.


        :param balance_in_additional_currency: The balance_in_additional_currency of this Reseller.
        :type balance_in_additional_currency: float
        """

        self._balance_in_additional_currency = balance_in_additional_currency

    @property
    def balance_warning_limit(self) -> float:
        """Gets the balance_warning_limit of this Reseller.

        in dollars  # noqa: E501

        :return: The balance_warning_limit of this Reseller.
        :rtype: float
        """
        return self._balance_warning_limit

    @balance_warning_limit.setter
    def balance_warning_limit(self, balance_warning_limit: float):
        """Sets the balance_warning_limit of this Reseller.

        in dollars  # noqa: E501

        :param balance_warning_limit: The balance_warning_limit of this Reseller.
        :type balance_warning_limit: float
        """

        self._balance_warning_limit = balance_warning_limit

    @property
    def credit_limit(self) -> float:
        """Gets the credit_limit of this Reseller.

        postpaid limit  # noqa: E501

        :return: The credit_limit of this Reseller.
        :rtype: float
        """
        return self._credit_limit

    @credit_limit.setter
    def credit_limit(self, credit_limit: float):
        """Sets the credit_limit of this Reseller.

        postpaid limit  # noqa: E501

        :param credit_limit: The credit_limit of this Reseller.
        :type credit_limit: float
        """

        self._credit_limit = credit_limit

    @property
    def credit_warning_limit(self) -> float:
        """Gets the credit_warning_limit of this Reseller.

        credit warning limit in percentage  # noqa: E501

        :return: The credit_warning_limit of this Reseller.
        :rtype: float
        """
        return self._credit_warning_limit

    @credit_warning_limit.setter
    def credit_warning_limit(self, credit_warning_limit: float):
        """Sets the credit_warning_limit of this Reseller.

        credit warning limit in percentage  # noqa: E501

        :param credit_warning_limit: The credit_warning_limit of this Reseller.
        :type credit_warning_limit: float
        """

        self._credit_warning_limit = credit_warning_limit

    @property
    def credit_limit_in_additional_currency(self) -> float:
        """Gets the credit_limit_in_additional_currency of this Reseller.


        :return: The credit_limit_in_additional_currency of this Reseller.
        :rtype: float
        """
        return self._credit_limit_in_additional_currency

    @credit_limit_in_additional_currency.setter
    def credit_limit_in_additional_currency(self, credit_limit_in_additional_currency: float):
        """Sets the credit_limit_in_additional_currency of this Reseller.


        :param credit_limit_in_additional_currency: The credit_limit_in_additional_currency of this Reseller.
        :type credit_limit_in_additional_currency: float
        """

        self._credit_limit_in_additional_currency = credit_limit_in_additional_currency

    @property
    def rate_revenue(self) -> float:
        """Gets the rate_revenue of this Reseller.

        rate revenue in percent  # noqa: E501

        :return: The rate_revenue of this Reseller.
        :rtype: float
        """
        return self._rate_revenue

    @rate_revenue.setter
    def rate_revenue(self, rate_revenue: float):
        """Sets the rate_revenue of this Reseller.

        rate revenue in percent  # noqa: E501

        :param rate_revenue: The rate_revenue of this Reseller.
        :type rate_revenue: float
        """

        self._rate_revenue = rate_revenue

    @property
    def corp_rate_revenue(self) -> float:
        """Gets the corp_rate_revenue of this Reseller.

        corporate revenue in percent  # noqa: E501

        :return: The corp_rate_revenue of this Reseller.
        :rtype: float
        """
        return self._corp_rate_revenue

    @corp_rate_revenue.setter
    def corp_rate_revenue(self, corp_rate_revenue: float):
        """Sets the corp_rate_revenue of this Reseller.

        corporate revenue in percent  # noqa: E501

        :param corp_rate_revenue: The corp_rate_revenue of this Reseller.
        :type corp_rate_revenue: float
        """

        self._corp_rate_revenue = corp_rate_revenue

    @property
    def voucher_rate(self) -> float:
        """Gets the voucher_rate of this Reseller.


        :return: The voucher_rate of this Reseller.
        :rtype: float
        """
        return self._voucher_rate

    @voucher_rate.setter
    def voucher_rate(self, voucher_rate: float):
        """Sets the voucher_rate of this Reseller.


        :param voucher_rate: The voucher_rate of this Reseller.
        :type voucher_rate: float
        """

        self._voucher_rate = voucher_rate

    @property
    def contact(self) -> ResellerContact:
        """Gets the contact of this Reseller.


        :return: The contact of this Reseller.
        :rtype: ResellerContact
        """
        return self._contact

    @contact.setter
    def contact(self, contact: ResellerContact):
        """Sets the contact of this Reseller.


        :param contact: The contact of this Reseller.
        :type contact: ResellerContact
        """
        if contact is None:
            raise ValueError("Invalid value for `contact`, must not be `None`")  # noqa: E501

        self._contact = contact

    @property
    def email_settings(self) -> ResellerEmailSettings:
        """Gets the email_settings of this Reseller.


        :return: The email_settings of this Reseller.
        :rtype: ResellerEmailSettings
        """
        return self._email_settings

    @email_settings.setter
    def email_settings(self, email_settings: ResellerEmailSettings):
        """Sets the email_settings of this Reseller.


        :param email_settings: The email_settings of this Reseller.
        :type email_settings: ResellerEmailSettings
        """

        self._email_settings = email_settings

    @property
    def custom_email_template_qr(self) -> str:
        """Gets the custom_email_template_qr of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_qr of this Reseller.
        :rtype: str
        """
        return self._custom_email_template_qr

    @custom_email_template_qr.setter
    def custom_email_template_qr(self, custom_email_template_qr: str):
        """Sets the custom_email_template_qr of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_qr: The custom_email_template_qr of this Reseller.
        :type custom_email_template_qr: str
        """

        self._custom_email_template_qr = custom_email_template_qr

    @property
    def custom_email_template_data(self) -> str:
        """Gets the custom_email_template_data of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_data of this Reseller.
        :rtype: str
        """
        return self._custom_email_template_data

    @custom_email_template_data.setter
    def custom_email_template_data(self, custom_email_template_data: str):
        """Sets the custom_email_template_data of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_data: The custom_email_template_data of this Reseller.
        :type custom_email_template_data: str
        """

        self._custom_email_template_data = custom_email_template_data

    @property
    def custom_email_template_expired(self) -> str:
        """Gets the custom_email_template_expired of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_expired of this Reseller.
        :rtype: str
        """
        return self._custom_email_template_expired

    @custom_email_template_expired.setter
    def custom_email_template_expired(self, custom_email_template_expired: str):
        """Sets the custom_email_template_expired of this Reseller.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_expired: The custom_email_template_expired of this Reseller.
        :type custom_email_template_expired: str
        """

        self._custom_email_template_expired = custom_email_template_expired

    @property
    def image(self) -> str:
        """Gets the image of this Reseller.

        The image file of the user  # noqa: E501

        :return: The image of this Reseller.
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image: str):
        """Sets the image of this Reseller.

        The image file of the user  # noqa: E501

        :param image: The image of this Reseller.
        :type image: str
        """

        self._image = image

    @property
    def image_type(self) -> str:
        """Gets the image_type of this Reseller.


        :return: The image_type of this Reseller.
        :rtype: str
        """
        return self._image_type

    @image_type.setter
    def image_type(self, image_type: str):
        """Sets the image_type of this Reseller.


        :param image_type: The image_type of this Reseller.
        :type image_type: str
        """

        self._image_type = image_type

    @property
    def is_whitelabel(self) -> bool:
        """Gets the is_whitelabel of this Reseller.


        :return: The is_whitelabel of this Reseller.
        :rtype: bool
        """
        return self._is_whitelabel

    @is_whitelabel.setter
    def is_whitelabel(self, is_whitelabel: bool):
        """Sets the is_whitelabel of this Reseller.


        :param is_whitelabel: The is_whitelabel of this Reseller.
        :type is_whitelabel: bool
        """

        self._is_whitelabel = is_whitelabel

    @property
    def tenant_name(self) -> str:
        """Gets the tenant_name of this Reseller.


        :return: The tenant_name of this Reseller.
        :rtype: str
        """
        return self._tenant_name

    @tenant_name.setter
    def tenant_name(self, tenant_name: str):
        """Sets the tenant_name of this Reseller.


        :param tenant_name: The tenant_name of this Reseller.
        :type tenant_name: str
        """

        self._tenant_name = tenant_name

    @property
    def reseller_category(self) -> str:
        """Gets the reseller_category of this Reseller.


        :return: The reseller_category of this Reseller.
        :rtype: str
        """
        return self._reseller_category

    @reseller_category.setter
    def reseller_category(self, reseller_category: str):
        """Sets the reseller_category of this Reseller.


        :param reseller_category: The reseller_category of this Reseller.
        :type reseller_category: str
        """

        self._reseller_category = reseller_category

    @property
    def active_vendors_list(self) -> List[str]:
        """Gets the active_vendors_list of this Reseller.


        :return: The active_vendors_list of this Reseller.
        :rtype: List[str]
        """
        return self._active_vendors_list

    @active_vendors_list.setter
    def active_vendors_list(self, active_vendors_list: List[str]):
        """Sets the active_vendors_list of this Reseller.


        :param active_vendors_list: The active_vendors_list of this Reseller.
        :type active_vendors_list: List[str]
        """

        self._active_vendors_list = active_vendors_list

    @property
    def vendors_for_balance_deduction_list(self) -> List[str]:
        """Gets the vendors_for_balance_deduction_list of this Reseller.


        :return: The vendors_for_balance_deduction_list of this Reseller.
        :rtype: List[str]
        """
        return self._vendors_for_balance_deduction_list

    @vendors_for_balance_deduction_list.setter
    def vendors_for_balance_deduction_list(self, vendors_for_balance_deduction_list: List[str]):
        """Sets the vendors_for_balance_deduction_list of this Reseller.


        :param vendors_for_balance_deduction_list: The vendors_for_balance_deduction_list of this Reseller.
        :type vendors_for_balance_deduction_list: List[str]
        """

        self._vendors_for_balance_deduction_list = vendors_for_balance_deduction_list

    @property
    def request_custom_email(self) -> bool:
        """Gets the request_custom_email of this Reseller.


        :return: The request_custom_email of this Reseller.
        :rtype: bool
        """
        return self._request_custom_email

    @request_custom_email.setter
    def request_custom_email(self, request_custom_email: bool):
        """Sets the request_custom_email of this Reseller.


        :param request_custom_email: The request_custom_email of this Reseller.
        :type request_custom_email: bool
        """

        self._request_custom_email = request_custom_email

    @property
    def data_consumption_email(self) -> ResellerDataConsumptionEmail:
        """Gets the data_consumption_email of this Reseller.


        :return: The data_consumption_email of this Reseller.
        :rtype: ResellerDataConsumptionEmail
        """
        return self._data_consumption_email

    @data_consumption_email.setter
    def data_consumption_email(self, data_consumption_email: ResellerDataConsumptionEmail):
        """Sets the data_consumption_email of this Reseller.


        :param data_consumption_email: The data_consumption_email of this Reseller.
        :type data_consumption_email: ResellerDataConsumptionEmail
        """

        self._data_consumption_email = data_consumption_email

    @property
    def data_expired_email(self) -> ResellerDataExpiredEmail:
        """Gets the data_expired_email of this Reseller.


        :return: The data_expired_email of this Reseller.
        :rtype: ResellerDataExpiredEmail
        """
        return self._data_expired_email

    @data_expired_email.setter
    def data_expired_email(self, data_expired_email: ResellerDataExpiredEmail):
        """Sets the data_expired_email of this Reseller.


        :param data_expired_email: The data_expired_email of this Reseller.
        :type data_expired_email: ResellerDataExpiredEmail
        """

        self._data_expired_email = data_expired_email

    @property
    def qr_code_email(self) -> ResellerQrCodeEmail:
        """Gets the qr_code_email of this Reseller.


        :return: The qr_code_email of this Reseller.
        :rtype: ResellerQrCodeEmail
        """
        return self._qr_code_email

    @qr_code_email.setter
    def qr_code_email(self, qr_code_email: ResellerQrCodeEmail):
        """Sets the qr_code_email of this Reseller.


        :param qr_code_email: The qr_code_email of this Reseller.
        :type qr_code_email: ResellerQrCodeEmail
        """

        self._qr_code_email = qr_code_email

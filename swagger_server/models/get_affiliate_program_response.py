# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetAffiliateProgramResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, affiliate_id: str=None, image_url: str=None, promo_code: str=None, discount_rate: float=None, amount: float=None, draft: bool=None):  # noqa: E501
        """GetAffiliateProgramResponse - a model defined in Swagger

        :param affiliate_id: The affiliate_id of this GetAffiliateProgramResponse.  # noqa: E501
        :type affiliate_id: str
        :param image_url: The image_url of this GetAffiliateProgramResponse.  # noqa: E501
        :type image_url: str
        :param promo_code: The promo_code of this GetAffiliateProgramResponse.  # noqa: E501
        :type promo_code: str
        :param discount_rate: The discount_rate of this GetAffiliateProgramResponse.  # noqa: E501
        :type discount_rate: float
        :param amount: The amount of this GetAffiliateProgramResponse.  # noqa: E501
        :type amount: float
        :param draft: The draft of this GetAffiliateProgramResponse.  # noqa: E501
        :type draft: bool
        """
        self.swagger_types = {
            'affiliate_id': str,
            'image_url': str,
            'promo_code': str,
            'discount_rate': float,
            'amount': float,
            'draft': bool
        }

        self.attribute_map = {
            'affiliate_id': 'affiliate_id',
            'image_url': 'image_url',
            'promo_code': 'promo_code',
            'discount_rate': 'discount_rate',
            'amount': 'amount',
            'draft': 'draft'
        }
        self._affiliate_id = affiliate_id
        self._image_url = image_url
        self._promo_code = promo_code
        self._discount_rate = discount_rate
        self._amount = amount
        self._draft = draft

    @classmethod
    def from_dict(cls, dikt) -> 'GetAffiliateProgramResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetAffiliateProgramResponse of this GetAffiliateProgramResponse.  # noqa: E501
        :rtype: GetAffiliateProgramResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def affiliate_id(self) -> str:
        """Gets the affiliate_id of this GetAffiliateProgramResponse.


        :return: The affiliate_id of this GetAffiliateProgramResponse.
        :rtype: str
        """
        return self._affiliate_id

    @affiliate_id.setter
    def affiliate_id(self, affiliate_id: str):
        """Sets the affiliate_id of this GetAffiliateProgramResponse.


        :param affiliate_id: The affiliate_id of this GetAffiliateProgramResponse.
        :type affiliate_id: str
        """

        self._affiliate_id = affiliate_id

    @property
    def image_url(self) -> str:
        """Gets the image_url of this GetAffiliateProgramResponse.


        :return: The image_url of this GetAffiliateProgramResponse.
        :rtype: str
        """
        return self._image_url

    @image_url.setter
    def image_url(self, image_url: str):
        """Sets the image_url of this GetAffiliateProgramResponse.


        :param image_url: The image_url of this GetAffiliateProgramResponse.
        :type image_url: str
        """

        self._image_url = image_url

    @property
    def promo_code(self) -> str:
        """Gets the promo_code of this GetAffiliateProgramResponse.


        :return: The promo_code of this GetAffiliateProgramResponse.
        :rtype: str
        """
        return self._promo_code

    @promo_code.setter
    def promo_code(self, promo_code: str):
        """Sets the promo_code of this GetAffiliateProgramResponse.


        :param promo_code: The promo_code of this GetAffiliateProgramResponse.
        :type promo_code: str
        """

        self._promo_code = promo_code

    @property
    def discount_rate(self) -> float:
        """Gets the discount_rate of this GetAffiliateProgramResponse.


        :return: The discount_rate of this GetAffiliateProgramResponse.
        :rtype: float
        """
        return self._discount_rate

    @discount_rate.setter
    def discount_rate(self, discount_rate: float):
        """Sets the discount_rate of this GetAffiliateProgramResponse.


        :param discount_rate: The discount_rate of this GetAffiliateProgramResponse.
        :type discount_rate: float
        """

        self._discount_rate = discount_rate

    @property
    def amount(self) -> float:
        """Gets the amount of this GetAffiliateProgramResponse.


        :return: The amount of this GetAffiliateProgramResponse.
        :rtype: float
        """
        return self._amount

    @amount.setter
    def amount(self, amount: float):
        """Sets the amount of this GetAffiliateProgramResponse.


        :param amount: The amount of this GetAffiliateProgramResponse.
        :type amount: float
        """

        self._amount = amount

    @property
    def draft(self) -> bool:
        """Gets the draft of this GetAffiliateProgramResponse.


        :return: The draft of this GetAffiliateProgramResponse.
        :rtype: bool
        """
        return self._draft

    @draft.setter
    def draft(self, draft: bool):
        """Sets the draft of this GetAffiliateProgramResponse.


        :param draft: The draft of this GetAffiliateProgramResponse.
        :type draft: bool
        """

        self._draft = draft

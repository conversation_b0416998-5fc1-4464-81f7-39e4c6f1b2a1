# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class AssignNewResellerRoleRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reseller_id: str=None):  # noqa: E501
        """AssignNewResellerRoleRequest - a model defined in Swagger

        :param reseller_id: The reseller_id of this AssignNewResellerRoleRequest.  # noqa: E501
        :type reseller_id: str
        """
        self.swagger_types = {
            'reseller_id': str
        }

        self.attribute_map = {
            'reseller_id': 'reseller_id'
        }
        self._reseller_id = reseller_id

    @classmethod
    def from_dict(cls, dikt) -> 'AssignNewResellerRoleRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AssignNewResellerRoleRequest of this AssignNewResellerRoleRequest.  # noqa: E501
        :rtype: AssignNewResellerRoleRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this AssignNewResellerRoleRequest.


        :return: The reseller_id of this AssignNewResellerRoleRequest.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this AssignNewResellerRoleRequest.


        :param reseller_id: The reseller_id of this AssignNewResellerRoleRequest.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

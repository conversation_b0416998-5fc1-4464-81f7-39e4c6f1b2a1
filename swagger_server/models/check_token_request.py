# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class CheckTokenRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, token: str=None):  # noqa: E501
        """CheckTokenRequest - a model defined in Swagger

        :param token: The token of this CheckTokenRequest.  # noqa: E501
        :type token: str
        """
        self.swagger_types = {
            'token': str
        }

        self.attribute_map = {
            'token': 'token'
        }
        self._token = token

    @classmethod
    def from_dict(cls, dikt) -> 'CheckTokenRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CheckTokenRequest of this CheckTokenRequest.  # noqa: E501
        :rtype: CheckTokenRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def token(self) -> str:
        """Gets the token of this CheckTokenRequest.


        :return: The token of this CheckTokenRequest.
        :rtype: str
        """
        return self._token

    @token.setter
    def token(self, token: str):
        """Sets the token of this CheckTokenRequest.


        :param token: The token of this CheckTokenRequest.
        :type token: str
        """

        self._token = token

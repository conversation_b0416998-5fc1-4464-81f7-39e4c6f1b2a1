# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class CustomizeCurrenciesResponseCurrenciesNotFound(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, currency_code: str=None, currency_name: str=None):  # noqa: E501
        """CustomizeCurrenciesResponseCurrenciesNotFound - a model defined in Swagger

        :param currency_code: The currency_code of this CustomizeCurrenciesResponseCurrenciesNotFound.  # noqa: E501
        :type currency_code: str
        :param currency_name: The currency_name of this CustomizeCurrenciesResponseCurrenciesNotFound.  # noqa: E501
        :type currency_name: str
        """
        self.swagger_types = {
            'currency_code': str,
            'currency_name': str
        }

        self.attribute_map = {
            'currency_code': 'currency_code',
            'currency_name': 'currency_name'
        }
        self._currency_code = currency_code
        self._currency_name = currency_name

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizeCurrenciesResponseCurrenciesNotFound':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizeCurrenciesResponse_currencies_not_found of this CustomizeCurrenciesResponseCurrenciesNotFound.  # noqa: E501
        :rtype: CustomizeCurrenciesResponseCurrenciesNotFound
        """
        return util.deserialize_model(dikt, cls)

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this CustomizeCurrenciesResponseCurrenciesNotFound.


        :return: The currency_code of this CustomizeCurrenciesResponseCurrenciesNotFound.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this CustomizeCurrenciesResponseCurrenciesNotFound.


        :param currency_code: The currency_code of this CustomizeCurrenciesResponseCurrenciesNotFound.
        :type currency_code: str
        """

        self._currency_code = currency_code

    @property
    def currency_name(self) -> str:
        """Gets the currency_name of this CustomizeCurrenciesResponseCurrenciesNotFound.


        :return: The currency_name of this CustomizeCurrenciesResponseCurrenciesNotFound.
        :rtype: str
        """
        return self._currency_name

    @currency_name.setter
    def currency_name(self, currency_name: str):
        """Sets the currency_name of this CustomizeCurrenciesResponseCurrenciesNotFound.


        :param currency_name: The currency_name of this CustomizeCurrenciesResponseCurrenciesNotFound.
        :type currency_name: str
        """

        self._currency_name = currency_name

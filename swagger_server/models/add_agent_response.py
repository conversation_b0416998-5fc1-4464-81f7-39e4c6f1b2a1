# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class AddAgentResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, message: str=None, agent_id: str=None):  # noqa: E501
        """AddAgentResponse - a model defined in Swagger

        :param response_code: The response_code of this AddAgentResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this AddAgentResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this AddAgentResponse.  # noqa: E501
        :type title: str
        :param message: The message of this AddAgentResponse.  # noqa: E501
        :type message: str
        :param agent_id: The agent_id of this AddAgentResponse.  # noqa: E501
        :type agent_id: str
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'message': str,
            'agent_id': str
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'message': 'message',
            'agent_id': 'agent_id'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._message = message
        self._agent_id = agent_id

    @classmethod
    def from_dict(cls, dikt) -> 'AddAgentResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AddAgentResponse of this AddAgentResponse.  # noqa: E501
        :rtype: AddAgentResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this AddAgentResponse.


        :return: The response_code of this AddAgentResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this AddAgentResponse.


        :param response_code: The response_code of this AddAgentResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this AddAgentResponse.


        :return: The developer_message of this AddAgentResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this AddAgentResponse.


        :param developer_message: The developer_message of this AddAgentResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this AddAgentResponse.


        :return: The title of this AddAgentResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this AddAgentResponse.


        :param title: The title of this AddAgentResponse.
        :type title: str
        """

        self._title = title

    @property
    def message(self) -> str:
        """Gets the message of this AddAgentResponse.


        :return: The message of this AddAgentResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this AddAgentResponse.


        :param message: The message of this AddAgentResponse.
        :type message: str
        """

        self._message = message

    @property
    def agent_id(self) -> str:
        """Gets the agent_id of this AddAgentResponse.


        :return: The agent_id of this AddAgentResponse.
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id: str):
        """Sets the agent_id of this AddAgentResponse.


        :param agent_id: The agent_id of this AddAgentResponse.
        :type agent_id: str
        """

        self._agent_id = agent_id

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.issue_report_chat import IssueReportChat  # noqa: F401,E501
from swagger_server.models.issue_report_feedback import IssueReportFeedback  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class IssueReportIssues(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, feedback: IssueReportFeedback=None, report_id: str=None, added_by: str=None, reseller_id: str=None, branch_id: str=None, date_created: date=None, last_modified: date=None, chat: List[IssueReportChat]=None, requester: str=None, assignee: str=None, resolution: str=None, status: str=None, issue_type: str=None, issue_subject: str=None, issue_description: str=None, priority: int=3):  # noqa: E501
        """IssueReportIssues - a model defined in Swagger

        :param feedback: The feedback of this IssueReportIssues.  # noqa: E501
        :type feedback: IssueReportFeedback
        :param report_id: The report_id of this IssueReportIssues.  # noqa: E501
        :type report_id: str
        :param added_by: The added_by of this IssueReportIssues.  # noqa: E501
        :type added_by: str
        :param reseller_id: The reseller_id of this IssueReportIssues.  # noqa: E501
        :type reseller_id: str
        :param branch_id: The branch_id of this IssueReportIssues.  # noqa: E501
        :type branch_id: str
        :param date_created: The date_created of this IssueReportIssues.  # noqa: E501
        :type date_created: date
        :param last_modified: The last_modified of this IssueReportIssues.  # noqa: E501
        :type last_modified: date
        :param chat: The chat of this IssueReportIssues.  # noqa: E501
        :type chat: List[IssueReportChat]
        :param requester: The requester of this IssueReportIssues.  # noqa: E501
        :type requester: str
        :param assignee: The assignee of this IssueReportIssues.  # noqa: E501
        :type assignee: str
        :param resolution: The resolution of this IssueReportIssues.  # noqa: E501
        :type resolution: str
        :param status: The status of this IssueReportIssues.  # noqa: E501
        :type status: str
        :param issue_type: The issue_type of this IssueReportIssues.  # noqa: E501
        :type issue_type: str
        :param issue_subject: The issue_subject of this IssueReportIssues.  # noqa: E501
        :type issue_subject: str
        :param issue_description: The issue_description of this IssueReportIssues.  # noqa: E501
        :type issue_description: str
        :param priority: The priority of this IssueReportIssues.  # noqa: E501
        :type priority: int
        """
        self.swagger_types = {
            'feedback': IssueReportFeedback,
            'report_id': str,
            'added_by': str,
            'reseller_id': str,
            'branch_id': str,
            'date_created': date,
            'last_modified': date,
            'chat': List[IssueReportChat],
            'requester': str,
            'assignee': str,
            'resolution': str,
            'status': str,
            'issue_type': str,
            'issue_subject': str,
            'issue_description': str,
            'priority': int
        }

        self.attribute_map = {
            'feedback': 'feedback',
            'report_id': 'report_id',
            'added_by': 'added_by',
            'reseller_id': 'reseller_id',
            'branch_id': 'branch_id',
            'date_created': 'date_created',
            'last_modified': 'last_modified',
            'chat': 'chat',
            'requester': 'requester',
            'assignee': 'assignee',
            'resolution': 'resolution',
            'status': 'status',
            'issue_type': 'issue_type',
            'issue_subject': 'issue_subject',
            'issue_description': 'issue_description',
            'priority': 'priority'
        }
        self._feedback = feedback
        self._report_id = report_id
        self._added_by = added_by
        self._reseller_id = reseller_id
        self._branch_id = branch_id
        self._date_created = date_created
        self._last_modified = last_modified
        self._chat = chat
        self._requester = requester
        self._assignee = assignee
        self._resolution = resolution
        self._status = status
        self._issue_type = issue_type
        self._issue_subject = issue_subject
        self._issue_description = issue_description
        self._priority = priority

    @classmethod
    def from_dict(cls, dikt) -> 'IssueReportIssues':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The IssueReport_issues of this IssueReportIssues.  # noqa: E501
        :rtype: IssueReportIssues
        """
        return util.deserialize_model(dikt, cls)

    @property
    def feedback(self) -> IssueReportFeedback:
        """Gets the feedback of this IssueReportIssues.


        :return: The feedback of this IssueReportIssues.
        :rtype: IssueReportFeedback
        """
        return self._feedback

    @feedback.setter
    def feedback(self, feedback: IssueReportFeedback):
        """Sets the feedback of this IssueReportIssues.


        :param feedback: The feedback of this IssueReportIssues.
        :type feedback: IssueReportFeedback
        """

        self._feedback = feedback

    @property
    def report_id(self) -> str:
        """Gets the report_id of this IssueReportIssues.


        :return: The report_id of this IssueReportIssues.
        :rtype: str
        """
        return self._report_id

    @report_id.setter
    def report_id(self, report_id: str):
        """Sets the report_id of this IssueReportIssues.


        :param report_id: The report_id of this IssueReportIssues.
        :type report_id: str
        """

        self._report_id = report_id

    @property
    def added_by(self) -> str:
        """Gets the added_by of this IssueReportIssues.


        :return: The added_by of this IssueReportIssues.
        :rtype: str
        """
        return self._added_by

    @added_by.setter
    def added_by(self, added_by: str):
        """Sets the added_by of this IssueReportIssues.


        :param added_by: The added_by of this IssueReportIssues.
        :type added_by: str
        """

        self._added_by = added_by

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this IssueReportIssues.


        :return: The reseller_id of this IssueReportIssues.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this IssueReportIssues.


        :param reseller_id: The reseller_id of this IssueReportIssues.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this IssueReportIssues.


        :return: The branch_id of this IssueReportIssues.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this IssueReportIssues.


        :param branch_id: The branch_id of this IssueReportIssues.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def date_created(self) -> date:
        """Gets the date_created of this IssueReportIssues.


        :return: The date_created of this IssueReportIssues.
        :rtype: date
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: date):
        """Sets the date_created of this IssueReportIssues.


        :param date_created: The date_created of this IssueReportIssues.
        :type date_created: date
        """

        self._date_created = date_created

    @property
    def last_modified(self) -> date:
        """Gets the last_modified of this IssueReportIssues.


        :return: The last_modified of this IssueReportIssues.
        :rtype: date
        """
        return self._last_modified

    @last_modified.setter
    def last_modified(self, last_modified: date):
        """Sets the last_modified of this IssueReportIssues.


        :param last_modified: The last_modified of this IssueReportIssues.
        :type last_modified: date
        """

        self._last_modified = last_modified

    @property
    def chat(self) -> List[IssueReportChat]:
        """Gets the chat of this IssueReportIssues.


        :return: The chat of this IssueReportIssues.
        :rtype: List[IssueReportChat]
        """
        return self._chat

    @chat.setter
    def chat(self, chat: List[IssueReportChat]):
        """Sets the chat of this IssueReportIssues.


        :param chat: The chat of this IssueReportIssues.
        :type chat: List[IssueReportChat]
        """

        self._chat = chat

    @property
    def requester(self) -> str:
        """Gets the requester of this IssueReportIssues.


        :return: The requester of this IssueReportIssues.
        :rtype: str
        """
        return self._requester

    @requester.setter
    def requester(self, requester: str):
        """Sets the requester of this IssueReportIssues.


        :param requester: The requester of this IssueReportIssues.
        :type requester: str
        """

        self._requester = requester

    @property
    def assignee(self) -> str:
        """Gets the assignee of this IssueReportIssues.


        :return: The assignee of this IssueReportIssues.
        :rtype: str
        """
        return self._assignee

    @assignee.setter
    def assignee(self, assignee: str):
        """Sets the assignee of this IssueReportIssues.


        :param assignee: The assignee of this IssueReportIssues.
        :type assignee: str
        """

        self._assignee = assignee

    @property
    def resolution(self) -> str:
        """Gets the resolution of this IssueReportIssues.

        Final resolution before closing the issue.  # noqa: E501

        :return: The resolution of this IssueReportIssues.
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution: str):
        """Sets the resolution of this IssueReportIssues.

        Final resolution before closing the issue.  # noqa: E501

        :param resolution: The resolution of this IssueReportIssues.
        :type resolution: str
        """

        self._resolution = resolution

    @property
    def status(self) -> str:
        """Gets the status of this IssueReportIssues.


        :return: The status of this IssueReportIssues.
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status: str):
        """Sets the status of this IssueReportIssues.


        :param status: The status of this IssueReportIssues.
        :type status: str
        """
        allowed_values = ["Open", "Closed", "Under Review"]  # noqa: E501
        if status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"
                .format(status, allowed_values)
            )

        self._status = status

    @property
    def issue_type(self) -> str:
        """Gets the issue_type of this IssueReportIssues.

        Type of the issue (e.g., \"Technical Issue\" or \"Price-related Concern\").  # noqa: E501

        :return: The issue_type of this IssueReportIssues.
        :rtype: str
        """
        return self._issue_type

    @issue_type.setter
    def issue_type(self, issue_type: str):
        """Sets the issue_type of this IssueReportIssues.

        Type of the issue (e.g., \"Technical Issue\" or \"Price-related Concern\").  # noqa: E501

        :param issue_type: The issue_type of this IssueReportIssues.
        :type issue_type: str
        """
        allowed_values = ["Technical Issue", "Price-related Concern"]  # noqa: E501
        if issue_type not in allowed_values:
            raise ValueError(
                "Invalid value for `issue_type` ({0}), must be one of {1}"
                .format(issue_type, allowed_values)
            )

        self._issue_type = issue_type

    @property
    def issue_subject(self) -> str:
        """Gets the issue_subject of this IssueReportIssues.

        Summary of Issue  # noqa: E501

        :return: The issue_subject of this IssueReportIssues.
        :rtype: str
        """
        return self._issue_subject

    @issue_subject.setter
    def issue_subject(self, issue_subject: str):
        """Sets the issue_subject of this IssueReportIssues.

        Summary of Issue  # noqa: E501

        :param issue_subject: The issue_subject of this IssueReportIssues.
        :type issue_subject: str
        """

        self._issue_subject = issue_subject

    @property
    def issue_description(self) -> str:
        """Gets the issue_description of this IssueReportIssues.

        Detailed description of the issue or concern.  # noqa: E501

        :return: The issue_description of this IssueReportIssues.
        :rtype: str
        """
        return self._issue_description

    @issue_description.setter
    def issue_description(self, issue_description: str):
        """Sets the issue_description of this IssueReportIssues.

        Detailed description of the issue or concern.  # noqa: E501

        :param issue_description: The issue_description of this IssueReportIssues.
        :type issue_description: str
        """

        self._issue_description = issue_description

    @property
    def priority(self) -> int:
        """Gets the priority of this IssueReportIssues.

        Priority level of the issue (\"Critical :1\", \"High:2\", \"Medium:3\", or \"Low:4\").  # noqa: E501

        :return: The priority of this IssueReportIssues.
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority: int):
        """Sets the priority of this IssueReportIssues.

        Priority level of the issue (\"Critical :1\", \"High:2\", \"Medium:3\", or \"Low:4\").  # noqa: E501

        :param priority: The priority of this IssueReportIssues.
        :type priority: int
        """

        self._priority = priority

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server.models.one_of_complete_transaction_response_orders_items import OneOfCompleteTransactionResponseOrdersItems  # noqa: F401,E501
from swagger_server import util


class CompleteTransactionResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, message: str=None, total_orders_count: int=None, orders: List[OneOfCompleteTransactionResponseOrdersItems]=None):  # noqa: E501
        """CompleteTransactionResponse - a model defined in Swagger

        :param response_code: The response_code of this CompleteTransactionResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this CompleteTransactionResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this CompleteTransactionResponse.  # noqa: E501
        :type title: str
        :param message: The message of this CompleteTransactionResponse.  # noqa: E501
        :type message: str
        :param total_orders_count: The total_orders_count of this CompleteTransactionResponse.  # noqa: E501
        :type total_orders_count: int
        :param orders: The orders of this CompleteTransactionResponse.  # noqa: E501
        :type orders: List[OneOfCompleteTransactionResponseOrdersItems]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'message': str,
            'total_orders_count': int,
            'orders': List[OneOfCompleteTransactionResponseOrdersItems]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'message': 'message',
            'total_orders_count': 'total_orders_count',
            'orders': 'orders'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._message = message
        self._total_orders_count = total_orders_count
        self._orders = orders

    @classmethod
    def from_dict(cls, dikt) -> 'CompleteTransactionResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CompleteTransactionResponse of this CompleteTransactionResponse.  # noqa: E501
        :rtype: CompleteTransactionResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this CompleteTransactionResponse.


        :return: The response_code of this CompleteTransactionResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this CompleteTransactionResponse.


        :param response_code: The response_code of this CompleteTransactionResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this CompleteTransactionResponse.


        :return: The developer_message of this CompleteTransactionResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this CompleteTransactionResponse.


        :param developer_message: The developer_message of this CompleteTransactionResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this CompleteTransactionResponse.


        :return: The title of this CompleteTransactionResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this CompleteTransactionResponse.


        :param title: The title of this CompleteTransactionResponse.
        :type title: str
        """

        self._title = title

    @property
    def message(self) -> str:
        """Gets the message of this CompleteTransactionResponse.


        :return: The message of this CompleteTransactionResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this CompleteTransactionResponse.


        :param message: The message of this CompleteTransactionResponse.
        :type message: str
        """

        self._message = message

    @property
    def total_orders_count(self) -> int:
        """Gets the total_orders_count of this CompleteTransactionResponse.


        :return: The total_orders_count of this CompleteTransactionResponse.
        :rtype: int
        """
        return self._total_orders_count

    @total_orders_count.setter
    def total_orders_count(self, total_orders_count: int):
        """Sets the total_orders_count of this CompleteTransactionResponse.


        :param total_orders_count: The total_orders_count of this CompleteTransactionResponse.
        :type total_orders_count: int
        """

        self._total_orders_count = total_orders_count

    @property
    def orders(self) -> List[OneOfCompleteTransactionResponseOrdersItems]:
        """Gets the orders of this CompleteTransactionResponse.


        :return: The orders of this CompleteTransactionResponse.
        :rtype: List[OneOfCompleteTransactionResponseOrdersItems]
        """
        return self._orders

    @orders.setter
    def orders(self, orders: List[OneOfCompleteTransactionResponseOrdersItems]):
        """Sets the orders of this CompleteTransactionResponse.


        :param orders: The orders of this CompleteTransactionResponse.
        :type orders: List[OneOfCompleteTransactionResponseOrdersItems]
        """

        self._orders = orders

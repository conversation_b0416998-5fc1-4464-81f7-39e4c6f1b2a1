# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetPromocodeHistoryResponseOrders(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None, reseller_id: str=None, promocode: str=None, affiliate_program: bool=None, affiliate_marketing_message: str=None, branch_id: str=None, order_status: str=None, plan_uid: str=None, plan_status: str=None, bundle_code: str=None, bundle_marketing_name: str=None, bundle_name: str=None, bundle_category: str=None, country_code: List[str]=None, country_name: List[str]=None, iccid: str=None, bundle_price: float=None, bundle_retail_price: float=None, matching_id: str=None, smdp_address: str=None, activation_code: str=None, client_name: str=None, client_email: str=None, remaining_wallet_balance: float=None, date_created: datetime=None, refund_reason: str=None, commission_rate: float=None, commision_amount: float=None):  # noqa: E501
        """GetPromocodeHistoryResponseOrders - a model defined in Swagger

        :param order_id: The order_id of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type order_id: str
        :param reseller_id: The reseller_id of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type reseller_id: str
        :param promocode: The promocode of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type promocode: str
        :param affiliate_program: The affiliate_program of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type affiliate_program: bool
        :param affiliate_marketing_message: The affiliate_marketing_message of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type affiliate_marketing_message: str
        :param branch_id: The branch_id of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type branch_id: str
        :param order_status: The order_status of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type order_status: str
        :param plan_uid: The plan_uid of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type plan_uid: str
        :param plan_status: The plan_status of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type plan_status: str
        :param bundle_code: The bundle_code of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_code: str
        :param bundle_marketing_name: The bundle_marketing_name of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_marketing_name: str
        :param bundle_name: The bundle_name of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_name: str
        :param bundle_category: The bundle_category of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_category: str
        :param country_code: The country_code of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type country_code: List[str]
        :param country_name: The country_name of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type country_name: List[str]
        :param iccid: The iccid of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type iccid: str
        :param bundle_price: The bundle_price of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_price: float
        :param bundle_retail_price: The bundle_retail_price of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type bundle_retail_price: float
        :param matching_id: The matching_id of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type matching_id: str
        :param smdp_address: The smdp_address of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type smdp_address: str
        :param activation_code: The activation_code of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type activation_code: str
        :param client_name: The client_name of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type client_name: str
        :param client_email: The client_email of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type client_email: str
        :param remaining_wallet_balance: The remaining_wallet_balance of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type remaining_wallet_balance: float
        :param date_created: The date_created of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type date_created: datetime
        :param refund_reason: The refund_reason of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type refund_reason: str
        :param commission_rate: The commission_rate of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type commission_rate: float
        :param commision_amount: The commision_amount of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :type commision_amount: float
        """
        self.swagger_types = {
            'order_id': str,
            'reseller_id': str,
            'promocode': str,
            'affiliate_program': bool,
            'affiliate_marketing_message': str,
            'branch_id': str,
            'order_status': str,
            'plan_uid': str,
            'plan_status': str,
            'bundle_code': str,
            'bundle_marketing_name': str,
            'bundle_name': str,
            'bundle_category': str,
            'country_code': List[str],
            'country_name': List[str],
            'iccid': str,
            'bundle_price': float,
            'bundle_retail_price': float,
            'matching_id': str,
            'smdp_address': str,
            'activation_code': str,
            'client_name': str,
            'client_email': str,
            'remaining_wallet_balance': float,
            'date_created': datetime,
            'refund_reason': str,
            'commission_rate': float,
            'commision_amount': float
        }

        self.attribute_map = {
            'order_id': 'order_id',
            'reseller_id': 'reseller_id',
            'promocode': 'promocode',
            'affiliate_program': 'affiliate_program',
            'affiliate_marketing_message': 'affiliate_marketing_message',
            'branch_id': 'branch_id',
            'order_status': 'order_status',
            'plan_uid': 'plan_uid',
            'plan_status': 'plan_status',
            'bundle_code': 'bundle_code',
            'bundle_marketing_name': 'bundle_marketing_name',
            'bundle_name': 'bundle_name',
            'bundle_category': 'bundle_category',
            'country_code': 'country_code',
            'country_name': 'country_name',
            'iccid': 'iccid',
            'bundle_price': 'bundle_price',
            'bundle_retail_price': 'bundle_retail_price',
            'matching_id': 'matching_id',
            'smdp_address': 'smdp_address',
            'activation_code': 'activation_code',
            'client_name': 'client_name',
            'client_email': 'client_email',
            'remaining_wallet_balance': 'remaining_wallet_balance',
            'date_created': 'date_created',
            'refund_reason': 'refund_reason',
            'commission_rate': 'commission_rate',
            'commision_amount': 'commision_amount'
        }
        self._order_id = order_id
        self._reseller_id = reseller_id
        self._promocode = promocode
        self._affiliate_program = affiliate_program
        self._affiliate_marketing_message = affiliate_marketing_message
        self._branch_id = branch_id
        self._order_status = order_status
        self._plan_uid = plan_uid
        self._plan_status = plan_status
        self._bundle_code = bundle_code
        self._bundle_marketing_name = bundle_marketing_name
        self._bundle_name = bundle_name
        self._bundle_category = bundle_category
        self._country_code = country_code
        self._country_name = country_name
        self._iccid = iccid
        self._bundle_price = bundle_price
        self._bundle_retail_price = bundle_retail_price
        self._matching_id = matching_id
        self._smdp_address = smdp_address
        self._activation_code = activation_code
        self._client_name = client_name
        self._client_email = client_email
        self._remaining_wallet_balance = remaining_wallet_balance
        self._date_created = date_created
        self._refund_reason = refund_reason
        self._commission_rate = commission_rate
        self._commision_amount = commision_amount

    @classmethod
    def from_dict(cls, dikt) -> 'GetPromocodeHistoryResponseOrders':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetPromocodeHistoryResponse_orders of this GetPromocodeHistoryResponseOrders.  # noqa: E501
        :rtype: GetPromocodeHistoryResponseOrders
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this GetPromocodeHistoryResponseOrders.


        :return: The order_id of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this GetPromocodeHistoryResponseOrders.


        :param order_id: The order_id of this GetPromocodeHistoryResponseOrders.
        :type order_id: str
        """

        self._order_id = order_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetPromocodeHistoryResponseOrders.


        :return: The reseller_id of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetPromocodeHistoryResponseOrders.


        :param reseller_id: The reseller_id of this GetPromocodeHistoryResponseOrders.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def promocode(self) -> str:
        """Gets the promocode of this GetPromocodeHistoryResponseOrders.


        :return: The promocode of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._promocode

    @promocode.setter
    def promocode(self, promocode: str):
        """Sets the promocode of this GetPromocodeHistoryResponseOrders.


        :param promocode: The promocode of this GetPromocodeHistoryResponseOrders.
        :type promocode: str
        """

        self._promocode = promocode

    @property
    def affiliate_program(self) -> bool:
        """Gets the affiliate_program of this GetPromocodeHistoryResponseOrders.


        :return: The affiliate_program of this GetPromocodeHistoryResponseOrders.
        :rtype: bool
        """
        return self._affiliate_program

    @affiliate_program.setter
    def affiliate_program(self, affiliate_program: bool):
        """Sets the affiliate_program of this GetPromocodeHistoryResponseOrders.


        :param affiliate_program: The affiliate_program of this GetPromocodeHistoryResponseOrders.
        :type affiliate_program: bool
        """

        self._affiliate_program = affiliate_program

    @property
    def affiliate_marketing_message(self) -> str:
        """Gets the affiliate_marketing_message of this GetPromocodeHistoryResponseOrders.


        :return: The affiliate_marketing_message of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._affiliate_marketing_message

    @affiliate_marketing_message.setter
    def affiliate_marketing_message(self, affiliate_marketing_message: str):
        """Sets the affiliate_marketing_message of this GetPromocodeHistoryResponseOrders.


        :param affiliate_marketing_message: The affiliate_marketing_message of this GetPromocodeHistoryResponseOrders.
        :type affiliate_marketing_message: str
        """

        self._affiliate_marketing_message = affiliate_marketing_message

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this GetPromocodeHistoryResponseOrders.


        :return: The branch_id of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this GetPromocodeHistoryResponseOrders.


        :param branch_id: The branch_id of this GetPromocodeHistoryResponseOrders.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def order_status(self) -> str:
        """Gets the order_status of this GetPromocodeHistoryResponseOrders.


        :return: The order_status of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._order_status

    @order_status.setter
    def order_status(self, order_status: str):
        """Sets the order_status of this GetPromocodeHistoryResponseOrders.


        :param order_status: The order_status of this GetPromocodeHistoryResponseOrders.
        :type order_status: str
        """
        allowed_values = ["Successful", "Refunded"]  # noqa: E501
        if order_status not in allowed_values:
            raise ValueError(
                "Invalid value for `order_status` ({0}), must be one of {1}"
                .format(order_status, allowed_values)
            )

        self._order_status = order_status

    @property
    def plan_uid(self) -> str:
        """Gets the plan_uid of this GetPromocodeHistoryResponseOrders.


        :return: The plan_uid of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._plan_uid

    @plan_uid.setter
    def plan_uid(self, plan_uid: str):
        """Sets the plan_uid of this GetPromocodeHistoryResponseOrders.


        :param plan_uid: The plan_uid of this GetPromocodeHistoryResponseOrders.
        :type plan_uid: str
        """

        self._plan_uid = plan_uid

    @property
    def plan_status(self) -> str:
        """Gets the plan_status of this GetPromocodeHistoryResponseOrders.


        :return: The plan_status of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._plan_status

    @plan_status.setter
    def plan_status(self, plan_status: str):
        """Sets the plan_status of this GetPromocodeHistoryResponseOrders.


        :param plan_status: The plan_status of this GetPromocodeHistoryResponseOrders.
        :type plan_status: str
        """

        self._plan_status = plan_status

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this GetPromocodeHistoryResponseOrders.


        :return: The bundle_code of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this GetPromocodeHistoryResponseOrders.


        :param bundle_code: The bundle_code of this GetPromocodeHistoryResponseOrders.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def bundle_marketing_name(self) -> str:
        """Gets the bundle_marketing_name of this GetPromocodeHistoryResponseOrders.


        :return: The bundle_marketing_name of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._bundle_marketing_name

    @bundle_marketing_name.setter
    def bundle_marketing_name(self, bundle_marketing_name: str):
        """Sets the bundle_marketing_name of this GetPromocodeHistoryResponseOrders.


        :param bundle_marketing_name: The bundle_marketing_name of this GetPromocodeHistoryResponseOrders.
        :type bundle_marketing_name: str
        """

        self._bundle_marketing_name = bundle_marketing_name

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this GetPromocodeHistoryResponseOrders.


        :return: The bundle_name of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this GetPromocodeHistoryResponseOrders.


        :param bundle_name: The bundle_name of this GetPromocodeHistoryResponseOrders.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def bundle_category(self) -> str:
        """Gets the bundle_category of this GetPromocodeHistoryResponseOrders.


        :return: The bundle_category of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._bundle_category

    @bundle_category.setter
    def bundle_category(self, bundle_category: str):
        """Sets the bundle_category of this GetPromocodeHistoryResponseOrders.


        :param bundle_category: The bundle_category of this GetPromocodeHistoryResponseOrders.
        :type bundle_category: str
        """
        allowed_values = ["country", "global", "region", "cruise"]  # noqa: E501
        if bundle_category not in allowed_values:
            raise ValueError(
                "Invalid value for `bundle_category` ({0}), must be one of {1}"
                .format(bundle_category, allowed_values)
            )

        self._bundle_category = bundle_category

    @property
    def country_code(self) -> List[str]:
        """Gets the country_code of this GetPromocodeHistoryResponseOrders.


        :return: The country_code of this GetPromocodeHistoryResponseOrders.
        :rtype: List[str]
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: List[str]):
        """Sets the country_code of this GetPromocodeHistoryResponseOrders.


        :param country_code: The country_code of this GetPromocodeHistoryResponseOrders.
        :type country_code: List[str]
        """

        self._country_code = country_code

    @property
    def country_name(self) -> List[str]:
        """Gets the country_name of this GetPromocodeHistoryResponseOrders.


        :return: The country_name of this GetPromocodeHistoryResponseOrders.
        :rtype: List[str]
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: List[str]):
        """Sets the country_name of this GetPromocodeHistoryResponseOrders.


        :param country_name: The country_name of this GetPromocodeHistoryResponseOrders.
        :type country_name: List[str]
        """

        self._country_name = country_name

    @property
    def iccid(self) -> str:
        """Gets the iccid of this GetPromocodeHistoryResponseOrders.


        :return: The iccid of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._iccid

    @iccid.setter
    def iccid(self, iccid: str):
        """Sets the iccid of this GetPromocodeHistoryResponseOrders.


        :param iccid: The iccid of this GetPromocodeHistoryResponseOrders.
        :type iccid: str
        """

        self._iccid = iccid

    @property
    def bundle_price(self) -> float:
        """Gets the bundle_price of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :return: The bundle_price of this GetPromocodeHistoryResponseOrders.
        :rtype: float
        """
        return self._bundle_price

    @bundle_price.setter
    def bundle_price(self, bundle_price: float):
        """Sets the bundle_price of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :param bundle_price: The bundle_price of this GetPromocodeHistoryResponseOrders.
        :type bundle_price: float
        """

        self._bundle_price = bundle_price

    @property
    def bundle_retail_price(self) -> float:
        """Gets the bundle_retail_price of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :return: The bundle_retail_price of this GetPromocodeHistoryResponseOrders.
        :rtype: float
        """
        return self._bundle_retail_price

    @bundle_retail_price.setter
    def bundle_retail_price(self, bundle_retail_price: float):
        """Sets the bundle_retail_price of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :param bundle_retail_price: The bundle_retail_price of this GetPromocodeHistoryResponseOrders.
        :type bundle_retail_price: float
        """

        self._bundle_retail_price = bundle_retail_price

    @property
    def matching_id(self) -> str:
        """Gets the matching_id of this GetPromocodeHistoryResponseOrders.


        :return: The matching_id of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._matching_id

    @matching_id.setter
    def matching_id(self, matching_id: str):
        """Sets the matching_id of this GetPromocodeHistoryResponseOrders.


        :param matching_id: The matching_id of this GetPromocodeHistoryResponseOrders.
        :type matching_id: str
        """

        self._matching_id = matching_id

    @property
    def smdp_address(self) -> str:
        """Gets the smdp_address of this GetPromocodeHistoryResponseOrders.


        :return: The smdp_address of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._smdp_address

    @smdp_address.setter
    def smdp_address(self, smdp_address: str):
        """Sets the smdp_address of this GetPromocodeHistoryResponseOrders.


        :param smdp_address: The smdp_address of this GetPromocodeHistoryResponseOrders.
        :type smdp_address: str
        """

        self._smdp_address = smdp_address

    @property
    def activation_code(self) -> str:
        """Gets the activation_code of this GetPromocodeHistoryResponseOrders.


        :return: The activation_code of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._activation_code

    @activation_code.setter
    def activation_code(self, activation_code: str):
        """Sets the activation_code of this GetPromocodeHistoryResponseOrders.


        :param activation_code: The activation_code of this GetPromocodeHistoryResponseOrders.
        :type activation_code: str
        """

        self._activation_code = activation_code

    @property
    def client_name(self) -> str:
        """Gets the client_name of this GetPromocodeHistoryResponseOrders.


        :return: The client_name of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._client_name

    @client_name.setter
    def client_name(self, client_name: str):
        """Sets the client_name of this GetPromocodeHistoryResponseOrders.


        :param client_name: The client_name of this GetPromocodeHistoryResponseOrders.
        :type client_name: str
        """

        self._client_name = client_name

    @property
    def client_email(self) -> str:
        """Gets the client_email of this GetPromocodeHistoryResponseOrders.


        :return: The client_email of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._client_email

    @client_email.setter
    def client_email(self, client_email: str):
        """Sets the client_email of this GetPromocodeHistoryResponseOrders.


        :param client_email: The client_email of this GetPromocodeHistoryResponseOrders.
        :type client_email: str
        """

        self._client_email = client_email

    @property
    def remaining_wallet_balance(self) -> float:
        """Gets the remaining_wallet_balance of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :return: The remaining_wallet_balance of this GetPromocodeHistoryResponseOrders.
        :rtype: float
        """
        return self._remaining_wallet_balance

    @remaining_wallet_balance.setter
    def remaining_wallet_balance(self, remaining_wallet_balance: float):
        """Sets the remaining_wallet_balance of this GetPromocodeHistoryResponseOrders.

        in dollars  # noqa: E501

        :param remaining_wallet_balance: The remaining_wallet_balance of this GetPromocodeHistoryResponseOrders.
        :type remaining_wallet_balance: float
        """

        self._remaining_wallet_balance = remaining_wallet_balance

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetPromocodeHistoryResponseOrders.


        :return: The date_created of this GetPromocodeHistoryResponseOrders.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetPromocodeHistoryResponseOrders.


        :param date_created: The date_created of this GetPromocodeHistoryResponseOrders.
        :type date_created: datetime
        """

        self._date_created = date_created

    @property
    def refund_reason(self) -> str:
        """Gets the refund_reason of this GetPromocodeHistoryResponseOrders.


        :return: The refund_reason of this GetPromocodeHistoryResponseOrders.
        :rtype: str
        """
        return self._refund_reason

    @refund_reason.setter
    def refund_reason(self, refund_reason: str):
        """Sets the refund_reason of this GetPromocodeHistoryResponseOrders.


        :param refund_reason: The refund_reason of this GetPromocodeHistoryResponseOrders.
        :type refund_reason: str
        """

        self._refund_reason = refund_reason

    @property
    def commission_rate(self) -> float:
        """Gets the commission_rate of this GetPromocodeHistoryResponseOrders.


        :return: The commission_rate of this GetPromocodeHistoryResponseOrders.
        :rtype: float
        """
        return self._commission_rate

    @commission_rate.setter
    def commission_rate(self, commission_rate: float):
        """Sets the commission_rate of this GetPromocodeHistoryResponseOrders.


        :param commission_rate: The commission_rate of this GetPromocodeHistoryResponseOrders.
        :type commission_rate: float
        """

        self._commission_rate = commission_rate

    @property
    def commision_amount(self) -> float:
        """Gets the commision_amount of this GetPromocodeHistoryResponseOrders.


        :return: The commision_amount of this GetPromocodeHistoryResponseOrders.
        :rtype: float
        """
        return self._commision_amount

    @commision_amount.setter
    def commision_amount(self, commision_amount: float):
        """Sets the commision_amount of this GetPromocodeHistoryResponseOrders.


        :param commision_amount: The commision_amount of this GetPromocodeHistoryResponseOrders.
        :type commision_amount: float
        """

        self._commision_amount = commision_amount

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.reseller_scoped_bundle import ResellerScopedBundle  # noqa: F401,E501
from swagger_server import util


class RegionGroupedBundlesResponseRegions(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, region: str=None, region_name: str=None, bundles: List[ResellerScopedBundle]=None):  # noqa: E501
        """RegionGroupedBundlesResponseRegions - a model defined in Swagger

        :param region: The region of this RegionGroupedBundlesResponseRegions.  # noqa: E501
        :type region: str
        :param region_name: The region_name of this RegionGroupedBundlesResponseRegions.  # noqa: E501
        :type region_name: str
        :param bundles: The bundles of this RegionGroupedBundlesResponseRegions.  # noqa: E501
        :type bundles: List[ResellerScopedBundle]
        """
        self.swagger_types = {
            'region': str,
            'region_name': str,
            'bundles': List[ResellerScopedBundle]
        }

        self.attribute_map = {
            'region': 'region',
            'region_name': 'region_name',
            'bundles': 'bundles'
        }
        self._region = region
        self._region_name = region_name
        self._bundles = bundles

    @classmethod
    def from_dict(cls, dikt) -> 'RegionGroupedBundlesResponseRegions':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The RegionGroupedBundlesResponse_regions of this RegionGroupedBundlesResponseRegions.  # noqa: E501
        :rtype: RegionGroupedBundlesResponseRegions
        """
        return util.deserialize_model(dikt, cls)

    @property
    def region(self) -> str:
        """Gets the region of this RegionGroupedBundlesResponseRegions.


        :return: The region of this RegionGroupedBundlesResponseRegions.
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region: str):
        """Sets the region of this RegionGroupedBundlesResponseRegions.


        :param region: The region of this RegionGroupedBundlesResponseRegions.
        :type region: str
        """

        self._region = region

    @property
    def region_name(self) -> str:
        """Gets the region_name of this RegionGroupedBundlesResponseRegions.


        :return: The region_name of this RegionGroupedBundlesResponseRegions.
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name: str):
        """Sets the region_name of this RegionGroupedBundlesResponseRegions.


        :param region_name: The region_name of this RegionGroupedBundlesResponseRegions.
        :type region_name: str
        """

        self._region_name = region_name

    @property
    def bundles(self) -> List[ResellerScopedBundle]:
        """Gets the bundles of this RegionGroupedBundlesResponseRegions.


        :return: The bundles of this RegionGroupedBundlesResponseRegions.
        :rtype: List[ResellerScopedBundle]
        """
        return self._bundles

    @bundles.setter
    def bundles(self, bundles: List[ResellerScopedBundle]):
        """Sets the bundles of this RegionGroupedBundlesResponseRegions.


        :param bundles: The bundles of this RegionGroupedBundlesResponseRegions.
        :type bundles: List[ResellerScopedBundle]
        """

        self._bundles = bundles

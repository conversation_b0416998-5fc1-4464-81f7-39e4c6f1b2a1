# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class NetworkListByRegionsNetworks(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, network_id: str=None, network_name: List[object]=None):  # noqa: E501
        """NetworkListByRegionsNetworks - a model defined in Swagger

        :param network_id: The network_id of this NetworkListByRegionsNetworks.  # noqa: E501
        :type network_id: str
        :param network_name: The network_name of this NetworkListByRegionsNetworks.  # noqa: E501
        :type network_name: List[object]
        """
        self.swagger_types = {
            'network_id': str,
            'network_name': List[object]
        }

        self.attribute_map = {
            'network_id': 'network_id',
            'network_name': 'network_name'
        }
        self._network_id = network_id
        self._network_name = network_name

    @classmethod
    def from_dict(cls, dikt) -> 'NetworkListByRegionsNetworks':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The NetworkListByRegions_networks of this NetworkListByRegionsNetworks.  # noqa: E501
        :rtype: NetworkListByRegionsNetworks
        """
        return util.deserialize_model(dikt, cls)

    @property
    def network_id(self) -> str:
        """Gets the network_id of this NetworkListByRegionsNetworks.

        Unique identifier for the network.  # noqa: E501

        :return: The network_id of this NetworkListByRegionsNetworks.
        :rtype: str
        """
        return self._network_id

    @network_id.setter
    def network_id(self, network_id: str):
        """Sets the network_id of this NetworkListByRegionsNetworks.

        Unique identifier for the network.  # noqa: E501

        :param network_id: The network_id of this NetworkListByRegionsNetworks.
        :type network_id: str
        """

        self._network_id = network_id

    @property
    def network_name(self) -> List[object]:
        """Gets the network_name of this NetworkListByRegionsNetworks.


        :return: The network_name of this NetworkListByRegionsNetworks.
        :rtype: List[object]
        """
        return self._network_name

    @network_name.setter
    def network_name(self, network_name: List[object]):
        """Sets the network_name of this NetworkListByRegionsNetworks.


        :param network_name: The network_name of this NetworkListByRegionsNetworks.
        :type network_name: List[object]
        """

        self._network_name = network_name

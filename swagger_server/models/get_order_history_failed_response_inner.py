# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetOrderHistoryFailedResponseInner(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None, reseller_id: str=None, order_status: str=None, bundle_code: str=None, bundle_marketing_name: str=None, country_code: str=None, country_name: str=None, bundle_price: float=None, client_name: str=None, client_email: str=None, remaining_wallet_balance: float=None, date_created: datetime=None):  # noqa: E501
        """GetOrderHistoryFailedResponseInner - a model defined in Swagger

        :param order_id: The order_id of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type order_id: str
        :param reseller_id: The reseller_id of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type reseller_id: str
        :param order_status: The order_status of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type order_status: str
        :param bundle_code: The bundle_code of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type bundle_code: str
        :param bundle_marketing_name: The bundle_marketing_name of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type bundle_marketing_name: str
        :param country_code: The country_code of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type country_code: str
        :param country_name: The country_name of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type country_name: str
        :param bundle_price: The bundle_price of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type bundle_price: float
        :param client_name: The client_name of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type client_name: str
        :param client_email: The client_email of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type client_email: str
        :param remaining_wallet_balance: The remaining_wallet_balance of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type remaining_wallet_balance: float
        :param date_created: The date_created of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :type date_created: datetime
        """
        self.swagger_types = {
            'order_id': str,
            'reseller_id': str,
            'order_status': str,
            'bundle_code': str,
            'bundle_marketing_name': str,
            'country_code': str,
            'country_name': str,
            'bundle_price': float,
            'client_name': str,
            'client_email': str,
            'remaining_wallet_balance': float,
            'date_created': datetime
        }

        self.attribute_map = {
            'order_id': 'order_id',
            'reseller_id': 'reseller_id',
            'order_status': 'order_status',
            'bundle_code': 'bundle_code',
            'bundle_marketing_name': 'bundle_marketing_name',
            'country_code': 'country_code',
            'country_name': 'country_name',
            'bundle_price': 'bundle_price',
            'client_name': 'client_name',
            'client_email': 'client_email',
            'remaining_wallet_balance': 'remaining_wallet_balance',
            'date_created': 'date_created'
        }
        self._order_id = order_id
        self._reseller_id = reseller_id
        self._order_status = order_status
        self._bundle_code = bundle_code
        self._bundle_marketing_name = bundle_marketing_name
        self._country_code = country_code
        self._country_name = country_name
        self._bundle_price = bundle_price
        self._client_name = client_name
        self._client_email = client_email
        self._remaining_wallet_balance = remaining_wallet_balance
        self._date_created = date_created

    @classmethod
    def from_dict(cls, dikt) -> 'GetOrderHistoryFailedResponseInner':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetOrderHistoryFailedResponse_inner of this GetOrderHistoryFailedResponseInner.  # noqa: E501
        :rtype: GetOrderHistoryFailedResponseInner
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this GetOrderHistoryFailedResponseInner.


        :return: The order_id of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this GetOrderHistoryFailedResponseInner.


        :param order_id: The order_id of this GetOrderHistoryFailedResponseInner.
        :type order_id: str
        """

        self._order_id = order_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetOrderHistoryFailedResponseInner.


        :return: The reseller_id of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetOrderHistoryFailedResponseInner.


        :param reseller_id: The reseller_id of this GetOrderHistoryFailedResponseInner.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def order_status(self) -> str:
        """Gets the order_status of this GetOrderHistoryFailedResponseInner.


        :return: The order_status of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._order_status

    @order_status.setter
    def order_status(self, order_status: str):
        """Sets the order_status of this GetOrderHistoryFailedResponseInner.


        :param order_status: The order_status of this GetOrderHistoryFailedResponseInner.
        :type order_status: str
        """
        allowed_values = ["Failed"]  # noqa: E501
        if order_status not in allowed_values:
            raise ValueError(
                "Invalid value for `order_status` ({0}), must be one of {1}"
                .format(order_status, allowed_values)
            )

        self._order_status = order_status

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this GetOrderHistoryFailedResponseInner.


        :return: The bundle_code of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this GetOrderHistoryFailedResponseInner.


        :param bundle_code: The bundle_code of this GetOrderHistoryFailedResponseInner.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def bundle_marketing_name(self) -> str:
        """Gets the bundle_marketing_name of this GetOrderHistoryFailedResponseInner.


        :return: The bundle_marketing_name of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._bundle_marketing_name

    @bundle_marketing_name.setter
    def bundle_marketing_name(self, bundle_marketing_name: str):
        """Sets the bundle_marketing_name of this GetOrderHistoryFailedResponseInner.


        :param bundle_marketing_name: The bundle_marketing_name of this GetOrderHistoryFailedResponseInner.
        :type bundle_marketing_name: str
        """

        self._bundle_marketing_name = bundle_marketing_name

    @property
    def country_code(self) -> str:
        """Gets the country_code of this GetOrderHistoryFailedResponseInner.


        :return: The country_code of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this GetOrderHistoryFailedResponseInner.


        :param country_code: The country_code of this GetOrderHistoryFailedResponseInner.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def country_name(self) -> str:
        """Gets the country_name of this GetOrderHistoryFailedResponseInner.


        :return: The country_name of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: str):
        """Sets the country_name of this GetOrderHistoryFailedResponseInner.


        :param country_name: The country_name of this GetOrderHistoryFailedResponseInner.
        :type country_name: str
        """

        self._country_name = country_name

    @property
    def bundle_price(self) -> float:
        """Gets the bundle_price of this GetOrderHistoryFailedResponseInner.

        in dollars  # noqa: E501

        :return: The bundle_price of this GetOrderHistoryFailedResponseInner.
        :rtype: float
        """
        return self._bundle_price

    @bundle_price.setter
    def bundle_price(self, bundle_price: float):
        """Sets the bundle_price of this GetOrderHistoryFailedResponseInner.

        in dollars  # noqa: E501

        :param bundle_price: The bundle_price of this GetOrderHistoryFailedResponseInner.
        :type bundle_price: float
        """

        self._bundle_price = bundle_price

    @property
    def client_name(self) -> str:
        """Gets the client_name of this GetOrderHistoryFailedResponseInner.


        :return: The client_name of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._client_name

    @client_name.setter
    def client_name(self, client_name: str):
        """Sets the client_name of this GetOrderHistoryFailedResponseInner.


        :param client_name: The client_name of this GetOrderHistoryFailedResponseInner.
        :type client_name: str
        """

        self._client_name = client_name

    @property
    def client_email(self) -> str:
        """Gets the client_email of this GetOrderHistoryFailedResponseInner.


        :return: The client_email of this GetOrderHistoryFailedResponseInner.
        :rtype: str
        """
        return self._client_email

    @client_email.setter
    def client_email(self, client_email: str):
        """Sets the client_email of this GetOrderHistoryFailedResponseInner.


        :param client_email: The client_email of this GetOrderHistoryFailedResponseInner.
        :type client_email: str
        """

        self._client_email = client_email

    @property
    def remaining_wallet_balance(self) -> float:
        """Gets the remaining_wallet_balance of this GetOrderHistoryFailedResponseInner.

        in dollars  # noqa: E501

        :return: The remaining_wallet_balance of this GetOrderHistoryFailedResponseInner.
        :rtype: float
        """
        return self._remaining_wallet_balance

    @remaining_wallet_balance.setter
    def remaining_wallet_balance(self, remaining_wallet_balance: float):
        """Sets the remaining_wallet_balance of this GetOrderHistoryFailedResponseInner.

        in dollars  # noqa: E501

        :param remaining_wallet_balance: The remaining_wallet_balance of this GetOrderHistoryFailedResponseInner.
        :type remaining_wallet_balance: float
        """

        self._remaining_wallet_balance = remaining_wallet_balance

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetOrderHistoryFailedResponseInner.


        :return: The date_created of this GetOrderHistoryFailedResponseInner.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetOrderHistoryFailedResponseInner.


        :param date_created: The date_created of this GetOrderHistoryFailedResponseInner.
        :type date_created: datetime
        """

        self._date_created = date_created

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.create_role_request_permissions import CreateRoleRequestPermissions  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetRoleResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, role_id: str=None, name: str=None, permission_level: int=None, access_level: str=None, description: str=None, permissions: List[CreateRoleRequestPermissions]=None):  # noqa: E501
        """GetRoleResponse - a model defined in Swagger

        :param role_id: The role_id of this GetRoleResponse.  # noqa: E501
        :type role_id: str
        :param name: The name of this GetRoleResponse.  # noqa: E501
        :type name: str
        :param permission_level: The permission_level of this GetRoleResponse.  # noqa: E501
        :type permission_level: int
        :param access_level: The access_level of this GetRoleResponse.  # noqa: E501
        :type access_level: str
        :param description: The description of this GetRoleResponse.  # noqa: E501
        :type description: str
        :param permissions: The permissions of this GetRoleResponse.  # noqa: E501
        :type permissions: List[CreateRoleRequestPermissions]
        """
        self.swagger_types = {
            'role_id': str,
            'name': str,
            'permission_level': int,
            'access_level': str,
            'description': str,
            'permissions': List[CreateRoleRequestPermissions]
        }

        self.attribute_map = {
            'role_id': 'role_id',
            'name': 'name',
            'permission_level': 'permission_level',
            'access_level': 'access_level',
            'description': 'description',
            'permissions': 'permissions'
        }
        self._role_id = role_id
        self._name = name
        self._permission_level = permission_level
        self._access_level = access_level
        self._description = description
        self._permissions = permissions

    @classmethod
    def from_dict(cls, dikt) -> 'GetRoleResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetRoleResponse of this GetRoleResponse.  # noqa: E501
        :rtype: GetRoleResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def role_id(self) -> str:
        """Gets the role_id of this GetRoleResponse.


        :return: The role_id of this GetRoleResponse.
        :rtype: str
        """
        return self._role_id

    @role_id.setter
    def role_id(self, role_id: str):
        """Sets the role_id of this GetRoleResponse.


        :param role_id: The role_id of this GetRoleResponse.
        :type role_id: str
        """
        if role_id is None:
            raise ValueError("Invalid value for `role_id`, must not be `None`")  # noqa: E501

        self._role_id = role_id

    @property
    def name(self) -> str:
        """Gets the name of this GetRoleResponse.


        :return: The name of this GetRoleResponse.
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name: str):
        """Sets the name of this GetRoleResponse.


        :param name: The name of this GetRoleResponse.
        :type name: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def permission_level(self) -> int:
        """Gets the permission_level of this GetRoleResponse.


        :return: The permission_level of this GetRoleResponse.
        :rtype: int
        """
        return self._permission_level

    @permission_level.setter
    def permission_level(self, permission_level: int):
        """Sets the permission_level of this GetRoleResponse.


        :param permission_level: The permission_level of this GetRoleResponse.
        :type permission_level: int
        """

        self._permission_level = permission_level

    @property
    def access_level(self) -> str:
        """Gets the access_level of this GetRoleResponse.


        :return: The access_level of this GetRoleResponse.
        :rtype: str
        """
        return self._access_level

    @access_level.setter
    def access_level(self, access_level: str):
        """Sets the access_level of this GetRoleResponse.


        :param access_level: The access_level of this GetRoleResponse.
        :type access_level: str
        """
        allowed_values = ["basic", "medium", "sensitive"]  # noqa: E501
        if access_level not in allowed_values:
            raise ValueError(
                "Invalid value for `access_level` ({0}), must be one of {1}"
                .format(access_level, allowed_values)
            )

        self._access_level = access_level

    @property
    def description(self) -> str:
        """Gets the description of this GetRoleResponse.


        :return: The description of this GetRoleResponse.
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description: str):
        """Sets the description of this GetRoleResponse.


        :param description: The description of this GetRoleResponse.
        :type description: str
        """

        self._description = description

    @property
    def permissions(self) -> List[CreateRoleRequestPermissions]:
        """Gets the permissions of this GetRoleResponse.


        :return: The permissions of this GetRoleResponse.
        :rtype: List[CreateRoleRequestPermissions]
        """
        return self._permissions

    @permissions.setter
    def permissions(self, permissions: List[CreateRoleRequestPermissions]):
        """Sets the permissions of this GetRoleResponse.


        :param permissions: The permissions of this GetRoleResponse.
        :type permissions: List[CreateRoleRequestPermissions]
        """
        if permissions is None:
            raise ValueError("Invalid value for `permissions`, must not be `None`")  # noqa: E501

        self._permissions = permissions

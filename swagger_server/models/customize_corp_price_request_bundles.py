# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class CustomizeCorpPriceRequestBundles(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, bundle_code: str=None, custom_price: float=None, is_active: bool=None):  # noqa: E501
        """CustomizeCorpPriceRequestBundles - a model defined in Swagger

        :param bundle_code: The bundle_code of this CustomizeCorpPriceRequestBundles.  # noqa: E501
        :type bundle_code: str
        :param custom_price: The custom_price of this CustomizeCorpPriceRequestBundles.  # noqa: E501
        :type custom_price: float
        :param is_active: The is_active of this CustomizeCorpPriceRequestBundles.  # noqa: E501
        :type is_active: bool
        """
        self.swagger_types = {
            'bundle_code': str,
            'custom_price': float,
            'is_active': bool
        }

        self.attribute_map = {
            'bundle_code': 'bundle_code',
            'custom_price': 'custom_price',
            'is_active': 'is_active'
        }
        self._bundle_code = bundle_code
        self._custom_price = custom_price
        self._is_active = is_active

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizeCorpPriceRequestBundles':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizeCorpPriceRequest_bundles of this CustomizeCorpPriceRequestBundles.  # noqa: E501
        :rtype: CustomizeCorpPriceRequestBundles
        """
        return util.deserialize_model(dikt, cls)

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this CustomizeCorpPriceRequestBundles.


        :return: The bundle_code of this CustomizeCorpPriceRequestBundles.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this CustomizeCorpPriceRequestBundles.


        :param bundle_code: The bundle_code of this CustomizeCorpPriceRequestBundles.
        :type bundle_code: str
        """
        if bundle_code is None:
            raise ValueError("Invalid value for `bundle_code`, must not be `None`")  # noqa: E501

        self._bundle_code = bundle_code

    @property
    def custom_price(self) -> float:
        """Gets the custom_price of this CustomizeCorpPriceRequestBundles.

        in dollars  # noqa: E501

        :return: The custom_price of this CustomizeCorpPriceRequestBundles.
        :rtype: float
        """
        return self._custom_price

    @custom_price.setter
    def custom_price(self, custom_price: float):
        """Sets the custom_price of this CustomizeCorpPriceRequestBundles.

        in dollars  # noqa: E501

        :param custom_price: The custom_price of this CustomizeCorpPriceRequestBundles.
        :type custom_price: float
        """

        self._custom_price = custom_price

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this CustomizeCorpPriceRequestBundles.


        :return: The is_active of this CustomizeCorpPriceRequestBundles.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this CustomizeCorpPriceRequestBundles.


        :param is_active: The is_active of this CustomizeCorpPriceRequestBundles.
        :type is_active: bool
        """

        self._is_active = is_active

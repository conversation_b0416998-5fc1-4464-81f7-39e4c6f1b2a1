# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class AllNetworkListNetworks(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, network_id: str=None, vendor_code: str=None, country_code: str=None, operator_list: List[str]=None, is_shown: bool=True):  # noqa: E501
        """AllNetworkListNetworks - a model defined in Swagger

        :param network_id: The network_id of this AllNetworkListNetworks.  # noqa: E501
        :type network_id: str
        :param vendor_code: The vendor_code of this AllNetworkListNetworks.  # noqa: E501
        :type vendor_code: str
        :param country_code: The country_code of this AllNetworkListNetworks.  # noqa: E501
        :type country_code: str
        :param operator_list: The operator_list of this AllNetworkListNetworks.  # noqa: E501
        :type operator_list: List[str]
        :param is_shown: The is_shown of this AllNetworkListNetworks.  # noqa: E501
        :type is_shown: bool
        """
        self.swagger_types = {
            'network_id': str,
            'vendor_code': str,
            'country_code': str,
            'operator_list': List[str],
            'is_shown': bool
        }

        self.attribute_map = {
            'network_id': 'network_id',
            'vendor_code': 'vendor_code',
            'country_code': 'country_code',
            'operator_list': 'operator_list',
            'is_shown': 'is_shown'
        }
        self._network_id = network_id
        self._vendor_code = vendor_code
        self._country_code = country_code
        self._operator_list = operator_list
        self._is_shown = is_shown

    @classmethod
    def from_dict(cls, dikt) -> 'AllNetworkListNetworks':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AllNetworkList_networks of this AllNetworkListNetworks.  # noqa: E501
        :rtype: AllNetworkListNetworks
        """
        return util.deserialize_model(dikt, cls)

    @property
    def network_id(self) -> str:
        """Gets the network_id of this AllNetworkListNetworks.


        :return: The network_id of this AllNetworkListNetworks.
        :rtype: str
        """
        return self._network_id

    @network_id.setter
    def network_id(self, network_id: str):
        """Sets the network_id of this AllNetworkListNetworks.


        :param network_id: The network_id of this AllNetworkListNetworks.
        :type network_id: str
        """

        self._network_id = network_id

    @property
    def vendor_code(self) -> str:
        """Gets the vendor_code of this AllNetworkListNetworks.


        :return: The vendor_code of this AllNetworkListNetworks.
        :rtype: str
        """
        return self._vendor_code

    @vendor_code.setter
    def vendor_code(self, vendor_code: str):
        """Sets the vendor_code of this AllNetworkListNetworks.


        :param vendor_code: The vendor_code of this AllNetworkListNetworks.
        :type vendor_code: str
        """

        self._vendor_code = vendor_code

    @property
    def country_code(self) -> str:
        """Gets the country_code of this AllNetworkListNetworks.


        :return: The country_code of this AllNetworkListNetworks.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this AllNetworkListNetworks.


        :param country_code: The country_code of this AllNetworkListNetworks.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def operator_list(self) -> List[str]:
        """Gets the operator_list of this AllNetworkListNetworks.


        :return: The operator_list of this AllNetworkListNetworks.
        :rtype: List[str]
        """
        return self._operator_list

    @operator_list.setter
    def operator_list(self, operator_list: List[str]):
        """Sets the operator_list of this AllNetworkListNetworks.


        :param operator_list: The operator_list of this AllNetworkListNetworks.
        :type operator_list: List[str]
        """

        self._operator_list = operator_list

    @property
    def is_shown(self) -> bool:
        """Gets the is_shown of this AllNetworkListNetworks.


        :return: The is_shown of this AllNetworkListNetworks.
        :rtype: bool
        """
        return self._is_shown

    @is_shown.setter
    def is_shown(self, is_shown: bool):
        """Sets the is_shown of this AllNetworkListNetworks.


        :param is_shown: The is_shown of this AllNetworkListNetworks.
        :type is_shown: bool
        """

        self._is_shown = is_shown

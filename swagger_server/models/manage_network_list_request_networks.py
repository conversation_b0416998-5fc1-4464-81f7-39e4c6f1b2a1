# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class ManageNetworkListRequestNetworks(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, vendor_name: str=None, country_code: str=None, operator_list: List[str]=None, is_shown: bool=True):  # noqa: E501
        """ManageNetworkListRequestNetworks - a model defined in Swagger

        :param vendor_name: The vendor_name of this ManageNetworkListRequestNetworks.  # noqa: E501
        :type vendor_name: str
        :param country_code: The country_code of this ManageNetworkListRequestNetworks.  # noqa: E501
        :type country_code: str
        :param operator_list: The operator_list of this ManageNetworkListRequestNetworks.  # noqa: E501
        :type operator_list: List[str]
        :param is_shown: The is_shown of this ManageNetworkListRequestNetworks.  # noqa: E501
        :type is_shown: bool
        """
        self.swagger_types = {
            'vendor_name': str,
            'country_code': str,
            'operator_list': List[str],
            'is_shown': bool
        }

        self.attribute_map = {
            'vendor_name': 'vendor_name',
            'country_code': 'country_code',
            'operator_list': 'operator_list',
            'is_shown': 'is_shown'
        }
        self._vendor_name = vendor_name
        self._country_code = country_code
        self._operator_list = operator_list
        self._is_shown = is_shown

    @classmethod
    def from_dict(cls, dikt) -> 'ManageNetworkListRequestNetworks':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ManageNetworkListRequest_networks of this ManageNetworkListRequestNetworks.  # noqa: E501
        :rtype: ManageNetworkListRequestNetworks
        """
        return util.deserialize_model(dikt, cls)

    @property
    def vendor_name(self) -> str:
        """Gets the vendor_name of this ManageNetworkListRequestNetworks.


        :return: The vendor_name of this ManageNetworkListRequestNetworks.
        :rtype: str
        """
        return self._vendor_name

    @vendor_name.setter
    def vendor_name(self, vendor_name: str):
        """Sets the vendor_name of this ManageNetworkListRequestNetworks.


        :param vendor_name: The vendor_name of this ManageNetworkListRequestNetworks.
        :type vendor_name: str
        """
        if vendor_name is None:
            raise ValueError("Invalid value for `vendor_name`, must not be `None`")  # noqa: E501

        self._vendor_name = vendor_name

    @property
    def country_code(self) -> str:
        """Gets the country_code of this ManageNetworkListRequestNetworks.


        :return: The country_code of this ManageNetworkListRequestNetworks.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this ManageNetworkListRequestNetworks.


        :param country_code: The country_code of this ManageNetworkListRequestNetworks.
        :type country_code: str
        """
        if country_code is None:
            raise ValueError("Invalid value for `country_code`, must not be `None`")  # noqa: E501

        self._country_code = country_code

    @property
    def operator_list(self) -> List[str]:
        """Gets the operator_list of this ManageNetworkListRequestNetworks.


        :return: The operator_list of this ManageNetworkListRequestNetworks.
        :rtype: List[str]
        """
        return self._operator_list

    @operator_list.setter
    def operator_list(self, operator_list: List[str]):
        """Sets the operator_list of this ManageNetworkListRequestNetworks.


        :param operator_list: The operator_list of this ManageNetworkListRequestNetworks.
        :type operator_list: List[str]
        """
        if operator_list is None:
            raise ValueError("Invalid value for `operator_list`, must not be `None`")  # noqa: E501

        self._operator_list = operator_list

    @property
    def is_shown(self) -> bool:
        """Gets the is_shown of this ManageNetworkListRequestNetworks.


        :return: The is_shown of this ManageNetworkListRequestNetworks.
        :rtype: bool
        """
        return self._is_shown

    @is_shown.setter
    def is_shown(self, is_shown: bool):
        """Sets the is_shown of this ManageNetworkListRequestNetworks.


        :param is_shown: The is_shown of this ManageNetworkListRequestNetworks.
        :type is_shown: bool
        """

        self._is_shown = is_shown

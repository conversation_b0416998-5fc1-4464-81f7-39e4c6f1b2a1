# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_promocode_history_response_orders import GetPromocodeHistoryResponseOrders  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetPromocodeHistoryResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, total_orders_count: int=None, orders: List[GetPromocodeHistoryResponseOrders]=None):  # noqa: E501
        """GetPromocodeHistoryResponse - a model defined in Swagger

        :param response_code: The response_code of this GetPromocodeHistoryResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetPromocodeHistoryResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetPromocodeHistoryResponse.  # noqa: E501
        :type title: str
        :param total_orders_count: The total_orders_count of this GetPromocodeHistoryResponse.  # noqa: E501
        :type total_orders_count: int
        :param orders: The orders of this GetPromocodeHistoryResponse.  # noqa: E501
        :type orders: List[GetPromocodeHistoryResponseOrders]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'total_orders_count': int,
            'orders': List[GetPromocodeHistoryResponseOrders]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'total_orders_count': 'total_orders_count',
            'orders': 'orders'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._total_orders_count = total_orders_count
        self._orders = orders

    @classmethod
    def from_dict(cls, dikt) -> 'GetPromocodeHistoryResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetPromocodeHistoryResponse of this GetPromocodeHistoryResponse.  # noqa: E501
        :rtype: GetPromocodeHistoryResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetPromocodeHistoryResponse.


        :return: The response_code of this GetPromocodeHistoryResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetPromocodeHistoryResponse.


        :param response_code: The response_code of this GetPromocodeHistoryResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetPromocodeHistoryResponse.


        :return: The developer_message of this GetPromocodeHistoryResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetPromocodeHistoryResponse.


        :param developer_message: The developer_message of this GetPromocodeHistoryResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetPromocodeHistoryResponse.


        :return: The title of this GetPromocodeHistoryResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetPromocodeHistoryResponse.


        :param title: The title of this GetPromocodeHistoryResponse.
        :type title: str
        """

        self._title = title

    @property
    def total_orders_count(self) -> int:
        """Gets the total_orders_count of this GetPromocodeHistoryResponse.


        :return: The total_orders_count of this GetPromocodeHistoryResponse.
        :rtype: int
        """
        return self._total_orders_count

    @total_orders_count.setter
    def total_orders_count(self, total_orders_count: int):
        """Sets the total_orders_count of this GetPromocodeHistoryResponse.


        :param total_orders_count: The total_orders_count of this GetPromocodeHistoryResponse.
        :type total_orders_count: int
        """

        self._total_orders_count = total_orders_count

    @property
    def orders(self) -> List[GetPromocodeHistoryResponseOrders]:
        """Gets the orders of this GetPromocodeHistoryResponse.


        :return: The orders of this GetPromocodeHistoryResponse.
        :rtype: List[GetPromocodeHistoryResponseOrders]
        """
        return self._orders

    @orders.setter
    def orders(self, orders: List[GetPromocodeHistoryResponseOrders]):
        """Sets the orders of this GetPromocodeHistoryResponse.


        :param orders: The orders of this GetPromocodeHistoryResponse.
        :type orders: List[GetPromocodeHistoryResponseOrders]
        """

        self._orders = orders

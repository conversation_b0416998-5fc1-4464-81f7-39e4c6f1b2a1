# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class GenerateVoucherRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, voucher_name: str=None, amount: float=None, quantity: int=None, currency_code: str=None, is_active: bool=None, expiry_datetime: datetime=None, generate_csv_file: bool=None, reason: str=None, voucher_type: str=None):  # noqa: E501
        """GenerateVoucherRequest - a model defined in Swagger

        :param voucher_name: The voucher_name of this GenerateVoucherRequest.  # noqa: E501
        :type voucher_name: str
        :param amount: The amount of this GenerateVoucherRequest.  # noqa: E501
        :type amount: float
        :param quantity: The quantity of this GenerateVoucherRequest.  # noqa: E501
        :type quantity: int
        :param currency_code: The currency_code of this GenerateVoucherRequest.  # noqa: E501
        :type currency_code: str
        :param is_active: The is_active of this GenerateVoucherRequest.  # noqa: E501
        :type is_active: bool
        :param expiry_datetime: The expiry_datetime of this GenerateVoucherRequest.  # noqa: E501
        :type expiry_datetime: datetime
        :param generate_csv_file: The generate_csv_file of this GenerateVoucherRequest.  # noqa: E501
        :type generate_csv_file: bool
        :param reason: The reason of this GenerateVoucherRequest.  # noqa: E501
        :type reason: str
        :param voucher_type: The voucher_type of this GenerateVoucherRequest.  # noqa: E501
        :type voucher_type: str
        """
        self.swagger_types = {
            'voucher_name': str,
            'amount': float,
            'quantity': int,
            'currency_code': str,
            'is_active': bool,
            'expiry_datetime': datetime,
            'generate_csv_file': bool,
            'reason': str,
            'voucher_type': str
        }

        self.attribute_map = {
            'voucher_name': 'voucher_name',
            'amount': 'amount',
            'quantity': 'quantity',
            'currency_code': 'currency_code',
            'is_active': 'is_active',
            'expiry_datetime': 'expiry_datetime',
            'generate_csv_file': 'generate_csv_file',
            'reason': 'reason',
            'voucher_type': 'voucher_type'
        }
        self._voucher_name = voucher_name
        self._amount = amount
        self._quantity = quantity
        self._currency_code = currency_code
        self._is_active = is_active
        self._expiry_datetime = expiry_datetime
        self._generate_csv_file = generate_csv_file
        self._reason = reason
        self._voucher_type = voucher_type

    @classmethod
    def from_dict(cls, dikt) -> 'GenerateVoucherRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GenerateVoucherRequest of this GenerateVoucherRequest.  # noqa: E501
        :rtype: GenerateVoucherRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def voucher_name(self) -> str:
        """Gets the voucher_name of this GenerateVoucherRequest.


        :return: The voucher_name of this GenerateVoucherRequest.
        :rtype: str
        """
        return self._voucher_name

    @voucher_name.setter
    def voucher_name(self, voucher_name: str):
        """Sets the voucher_name of this GenerateVoucherRequest.


        :param voucher_name: The voucher_name of this GenerateVoucherRequest.
        :type voucher_name: str
        """
        if voucher_name is None:
            raise ValueError("Invalid value for `voucher_name`, must not be `None`")  # noqa: E501

        self._voucher_name = voucher_name

    @property
    def amount(self) -> float:
        """Gets the amount of this GenerateVoucherRequest.


        :return: The amount of this GenerateVoucherRequest.
        :rtype: float
        """
        return self._amount

    @amount.setter
    def amount(self, amount: float):
        """Sets the amount of this GenerateVoucherRequest.


        :param amount: The amount of this GenerateVoucherRequest.
        :type amount: float
        """
        if amount is None:
            raise ValueError("Invalid value for `amount`, must not be `None`")  # noqa: E501

        self._amount = amount

    @property
    def quantity(self) -> int:
        """Gets the quantity of this GenerateVoucherRequest.


        :return: The quantity of this GenerateVoucherRequest.
        :rtype: int
        """
        return self._quantity

    @quantity.setter
    def quantity(self, quantity: int):
        """Sets the quantity of this GenerateVoucherRequest.


        :param quantity: The quantity of this GenerateVoucherRequest.
        :type quantity: int
        """
        if quantity is None:
            raise ValueError("Invalid value for `quantity`, must not be `None`")  # noqa: E501

        self._quantity = quantity

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this GenerateVoucherRequest.


        :return: The currency_code of this GenerateVoucherRequest.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this GenerateVoucherRequest.


        :param currency_code: The currency_code of this GenerateVoucherRequest.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this GenerateVoucherRequest.


        :return: The is_active of this GenerateVoucherRequest.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this GenerateVoucherRequest.


        :param is_active: The is_active of this GenerateVoucherRequest.
        :type is_active: bool
        """

        self._is_active = is_active

    @property
    def expiry_datetime(self) -> datetime:
        """Gets the expiry_datetime of this GenerateVoucherRequest.


        :return: The expiry_datetime of this GenerateVoucherRequest.
        :rtype: datetime
        """
        return self._expiry_datetime

    @expiry_datetime.setter
    def expiry_datetime(self, expiry_datetime: datetime):
        """Sets the expiry_datetime of this GenerateVoucherRequest.


        :param expiry_datetime: The expiry_datetime of this GenerateVoucherRequest.
        :type expiry_datetime: datetime
        """
        if expiry_datetime is None:
            raise ValueError("Invalid value for `expiry_datetime`, must not be `None`")  # noqa: E501

        self._expiry_datetime = expiry_datetime

    @property
    def generate_csv_file(self) -> bool:
        """Gets the generate_csv_file of this GenerateVoucherRequest.


        :return: The generate_csv_file of this GenerateVoucherRequest.
        :rtype: bool
        """
        return self._generate_csv_file

    @generate_csv_file.setter
    def generate_csv_file(self, generate_csv_file: bool):
        """Sets the generate_csv_file of this GenerateVoucherRequest.


        :param generate_csv_file: The generate_csv_file of this GenerateVoucherRequest.
        :type generate_csv_file: bool
        """
        if generate_csv_file is None:
            raise ValueError("Invalid value for `generate_csv_file`, must not be `None`")  # noqa: E501

        self._generate_csv_file = generate_csv_file

    @property
    def reason(self) -> str:
        """Gets the reason of this GenerateVoucherRequest.


        :return: The reason of this GenerateVoucherRequest.
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason: str):
        """Sets the reason of this GenerateVoucherRequest.


        :param reason: The reason of this GenerateVoucherRequest.
        :type reason: str
        """

        self._reason = reason

    @property
    def voucher_type(self) -> str:
        """Gets the voucher_type of this GenerateVoucherRequest.


        :return: The voucher_type of this GenerateVoucherRequest.
        :rtype: str
        """
        return self._voucher_type

    @voucher_type.setter
    def voucher_type(self, voucher_type: str):
        """Sets the voucher_type of this GenerateVoucherRequest.


        :param voucher_type: The voucher_type of this GenerateVoucherRequest.
        :type voucher_type: str
        """
        allowed_values = ["default", "triggerable"]  # noqa: E501
        if voucher_type not in allowed_values:
            raise ValueError(
                "Invalid value for `voucher_type` ({0}), must be one of {1}"
                .format(voucher_type, allowed_values)
            )

        self._voucher_type = voucher_type

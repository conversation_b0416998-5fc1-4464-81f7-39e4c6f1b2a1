# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class BundleNetworkListNetworks(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, country_code: str=None, operator_list: List[str]=None):  # noqa: E501
        """BundleNetworkListNetworks - a model defined in Swagger

        :param country_code: The country_code of this BundleNetworkListNetworks.  # noqa: E501
        :type country_code: str
        :param operator_list: The operator_list of this BundleNetworkListNetworks.  # noqa: E501
        :type operator_list: List[str]
        """
        self.swagger_types = {
            'country_code': str,
            'operator_list': List[str]
        }

        self.attribute_map = {
            'country_code': 'country_code',
            'operator_list': 'operator_list'
        }
        self._country_code = country_code
        self._operator_list = operator_list

    @classmethod
    def from_dict(cls, dikt) -> 'BundleNetworkListNetworks':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The BundleNetworkList_networks of this BundleNetworkListNetworks.  # noqa: E501
        :rtype: BundleNetworkListNetworks
        """
        return util.deserialize_model(dikt, cls)

    @property
    def country_code(self) -> str:
        """Gets the country_code of this BundleNetworkListNetworks.


        :return: The country_code of this BundleNetworkListNetworks.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this BundleNetworkListNetworks.


        :param country_code: The country_code of this BundleNetworkListNetworks.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def operator_list(self) -> List[str]:
        """Gets the operator_list of this BundleNetworkListNetworks.


        :return: The operator_list of this BundleNetworkListNetworks.
        :rtype: List[str]
        """
        return self._operator_list

    @operator_list.setter
    def operator_list(self, operator_list: List[str]):
        """Sets the operator_list of this BundleNetworkListNetworks.


        :param operator_list: The operator_list of this BundleNetworkListNetworks.
        :type operator_list: List[str]
        """

        self._operator_list = operator_list

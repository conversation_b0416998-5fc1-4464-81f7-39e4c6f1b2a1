# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class TopupBalanceResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, message: str=None, wallet_balance: float=None):  # noqa: E501
        """TopupBalanceResponse - a model defined in Swagger

        :param response_code: The response_code of this TopupBalanceResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this TopupBalanceResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this TopupBalanceResponse.  # noqa: E501
        :type title: str
        :param message: The message of this TopupBalanceResponse.  # noqa: E501
        :type message: str
        :param wallet_balance: The wallet_balance of this TopupBalanceResponse.  # noqa: E501
        :type wallet_balance: float
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'message': str,
            'wallet_balance': float
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'message': 'message',
            'wallet_balance': 'wallet_balance'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._message = message
        self._wallet_balance = wallet_balance

    @classmethod
    def from_dict(cls, dikt) -> 'TopupBalanceResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The TopupBalanceResponse of this TopupBalanceResponse.  # noqa: E501
        :rtype: TopupBalanceResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this TopupBalanceResponse.


        :return: The response_code of this TopupBalanceResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this TopupBalanceResponse.


        :param response_code: The response_code of this TopupBalanceResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this TopupBalanceResponse.


        :return: The developer_message of this TopupBalanceResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this TopupBalanceResponse.


        :param developer_message: The developer_message of this TopupBalanceResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this TopupBalanceResponse.


        :return: The title of this TopupBalanceResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this TopupBalanceResponse.


        :param title: The title of this TopupBalanceResponse.
        :type title: str
        """

        self._title = title

    @property
    def message(self) -> str:
        """Gets the message of this TopupBalanceResponse.


        :return: The message of this TopupBalanceResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this TopupBalanceResponse.


        :param message: The message of this TopupBalanceResponse.
        :type message: str
        """

        self._message = message

    @property
    def wallet_balance(self) -> float:
        """Gets the wallet_balance of this TopupBalanceResponse.

        in dollars  # noqa: E501

        :return: The wallet_balance of this TopupBalanceResponse.
        :rtype: float
        """
        return self._wallet_balance

    @wallet_balance.setter
    def wallet_balance(self, wallet_balance: float):
        """Sets the wallet_balance of this TopupBalanceResponse.

        in dollars  # noqa: E501

        :param wallet_balance: The wallet_balance of this TopupBalanceResponse.
        :type wallet_balance: float
        """

        self._wallet_balance = wallet_balance

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class GetCountriesResponseCountries(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, iso3_code: str=None, iso2_code: str=None, country_name: str=None):  # noqa: E501
        """GetCountriesResponseCountries - a model defined in Swagger

        :param iso3_code: The iso3_code of this GetCountriesResponseCountries.  # noqa: E501
        :type iso3_code: str
        :param iso2_code: The iso2_code of this GetCountriesResponseCountries.  # noqa: E501
        :type iso2_code: str
        :param country_name: The country_name of this GetCountriesResponseCountries.  # noqa: E501
        :type country_name: str
        """
        self.swagger_types = {
            'iso3_code': str,
            'iso2_code': str,
            'country_name': str
        }

        self.attribute_map = {
            'iso3_code': 'iso3_code',
            'iso2_code': 'iso2_code',
            'country_name': 'country_name'
        }
        self._iso3_code = iso3_code
        self._iso2_code = iso2_code
        self._country_name = country_name

    @classmethod
    def from_dict(cls, dikt) -> 'GetCountriesResponseCountries':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetCountriesResponse_countries of this GetCountriesResponseCountries.  # noqa: E501
        :rtype: GetCountriesResponseCountries
        """
        return util.deserialize_model(dikt, cls)

    @property
    def iso3_code(self) -> str:
        """Gets the iso3_code of this GetCountriesResponseCountries.


        :return: The iso3_code of this GetCountriesResponseCountries.
        :rtype: str
        """
        return self._iso3_code

    @iso3_code.setter
    def iso3_code(self, iso3_code: str):
        """Sets the iso3_code of this GetCountriesResponseCountries.


        :param iso3_code: The iso3_code of this GetCountriesResponseCountries.
        :type iso3_code: str
        """

        self._iso3_code = iso3_code

    @property
    def iso2_code(self) -> str:
        """Gets the iso2_code of this GetCountriesResponseCountries.


        :return: The iso2_code of this GetCountriesResponseCountries.
        :rtype: str
        """
        return self._iso2_code

    @iso2_code.setter
    def iso2_code(self, iso2_code: str):
        """Sets the iso2_code of this GetCountriesResponseCountries.


        :param iso2_code: The iso2_code of this GetCountriesResponseCountries.
        :type iso2_code: str
        """

        self._iso2_code = iso2_code

    @property
    def country_name(self) -> str:
        """Gets the country_name of this GetCountriesResponseCountries.


        :return: The country_name of this GetCountriesResponseCountries.
        :rtype: str
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: str):
        """Sets the country_name of this GetCountriesResponseCountries.


        :param country_name: The country_name of this GetCountriesResponseCountries.
        :type country_name: str
        """

        self._country_name = country_name

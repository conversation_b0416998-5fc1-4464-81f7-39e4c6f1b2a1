# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class ManageEmailTemplatesRequestEmailTemplateDataConsumption(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, greetings: str=None, body_1: str=None, body_2: str=None, whatsapp_number: str=None, instagram_link: str=None, facebook_link: str=None, website_link: str=None, email_image_type: str=None, email_image_consumption: str=None, email_logo_type: str=None, email_logo: str=None, company_name_team: str=None, subject_consumption: str=None, footer: str=None):  # noqa: E501
        """ManageEmailTemplatesRequestEmailTemplateDataConsumption - a model defined in Swagger

        :param greetings: The greetings of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type greetings: str
        :param body_1: The body_1 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type body_1: str
        :param body_2: The body_2 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type body_2: str
        :param whatsapp_number: The whatsapp_number of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type whatsapp_number: str
        :param instagram_link: The instagram_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type instagram_link: str
        :param facebook_link: The facebook_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type facebook_link: str
        :param website_link: The website_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type website_link: str
        :param email_image_type: The email_image_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type email_image_type: str
        :param email_image_consumption: The email_image_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type email_image_consumption: str
        :param email_logo_type: The email_logo_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type email_logo_type: str
        :param email_logo: The email_logo of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type email_logo: str
        :param company_name_team: The company_name_team of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type company_name_team: str
        :param subject_consumption: The subject_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type subject_consumption: str
        :param footer: The footer of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :type footer: str
        """
        self.swagger_types = {
            'greetings': str,
            'body_1': str,
            'body_2': str,
            'whatsapp_number': str,
            'instagram_link': str,
            'facebook_link': str,
            'website_link': str,
            'email_image_type': str,
            'email_image_consumption': str,
            'email_logo_type': str,
            'email_logo': str,
            'company_name_team': str,
            'subject_consumption': str,
            'footer': str
        }

        self.attribute_map = {
            'greetings': 'greetings',
            'body_1': 'body_1',
            'body_2': 'body_2',
            'whatsapp_number': 'whatsapp_number',
            'instagram_link': 'instagram_link',
            'facebook_link': 'facebook_link',
            'website_link': 'website_link',
            'email_image_type': 'email-image_type',
            'email_image_consumption': 'email_image_consumption',
            'email_logo_type': 'email-logo_type',
            'email_logo': 'email_logo',
            'company_name_team': 'company_name_team',
            'subject_consumption': 'subject_consumption',
            'footer': 'footer'
        }
        self._greetings = greetings
        self._body_1 = body_1
        self._body_2 = body_2
        self._whatsapp_number = whatsapp_number
        self._instagram_link = instagram_link
        self._facebook_link = facebook_link
        self._website_link = website_link
        self._email_image_type = email_image_type
        self._email_image_consumption = email_image_consumption
        self._email_logo_type = email_logo_type
        self._email_logo = email_logo
        self._company_name_team = company_name_team
        self._subject_consumption = subject_consumption
        self._footer = footer

    @classmethod
    def from_dict(cls, dikt) -> 'ManageEmailTemplatesRequestEmailTemplateDataConsumption':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ManageEmailTemplatesRequest_email_template_data_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.  # noqa: E501
        :rtype: ManageEmailTemplatesRequestEmailTemplateDataConsumption
        """
        return util.deserialize_model(dikt, cls)

    @property
    def greetings(self) -> str:
        """Gets the greetings of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The greetings of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._greetings

    @greetings.setter
    def greetings(self, greetings: str):
        """Sets the greetings of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param greetings: The greetings of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type greetings: str
        """

        self._greetings = greetings

    @property
    def body_1(self) -> str:
        """Gets the body_1 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The body_1 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._body_1

    @body_1.setter
    def body_1(self, body_1: str):
        """Sets the body_1 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param body_1: The body_1 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type body_1: str
        """

        self._body_1 = body_1

    @property
    def body_2(self) -> str:
        """Gets the body_2 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The body_2 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._body_2

    @body_2.setter
    def body_2(self, body_2: str):
        """Sets the body_2 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param body_2: The body_2 of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type body_2: str
        """

        self._body_2 = body_2

    @property
    def whatsapp_number(self) -> str:
        """Gets the whatsapp_number of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The whatsapp_number of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._whatsapp_number

    @whatsapp_number.setter
    def whatsapp_number(self, whatsapp_number: str):
        """Sets the whatsapp_number of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param whatsapp_number: The whatsapp_number of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type whatsapp_number: str
        """

        self._whatsapp_number = whatsapp_number

    @property
    def instagram_link(self) -> str:
        """Gets the instagram_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The instagram_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._instagram_link

    @instagram_link.setter
    def instagram_link(self, instagram_link: str):
        """Sets the instagram_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param instagram_link: The instagram_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type instagram_link: str
        """

        self._instagram_link = instagram_link

    @property
    def facebook_link(self) -> str:
        """Gets the facebook_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The facebook_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._facebook_link

    @facebook_link.setter
    def facebook_link(self, facebook_link: str):
        """Sets the facebook_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param facebook_link: The facebook_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type facebook_link: str
        """

        self._facebook_link = facebook_link

    @property
    def website_link(self) -> str:
        """Gets the website_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The website_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._website_link

    @website_link.setter
    def website_link(self, website_link: str):
        """Sets the website_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param website_link: The website_link of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type website_link: str
        """

        self._website_link = website_link

    @property
    def email_image_type(self) -> str:
        """Gets the email_image_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The email_image_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._email_image_type

    @email_image_type.setter
    def email_image_type(self, email_image_type: str):
        """Sets the email_image_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param email_image_type: The email_image_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type email_image_type: str
        """

        self._email_image_type = email_image_type

    @property
    def email_image_consumption(self) -> str:
        """Gets the email_image_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The email_image_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._email_image_consumption

    @email_image_consumption.setter
    def email_image_consumption(self, email_image_consumption: str):
        """Sets the email_image_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param email_image_consumption: The email_image_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type email_image_consumption: str
        """

        self._email_image_consumption = email_image_consumption

    @property
    def email_logo_type(self) -> str:
        """Gets the email_logo_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The email_logo_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._email_logo_type

    @email_logo_type.setter
    def email_logo_type(self, email_logo_type: str):
        """Sets the email_logo_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param email_logo_type: The email_logo_type of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type email_logo_type: str
        """

        self._email_logo_type = email_logo_type

    @property
    def email_logo(self) -> str:
        """Gets the email_logo of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The email_logo of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._email_logo

    @email_logo.setter
    def email_logo(self, email_logo: str):
        """Sets the email_logo of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param email_logo: The email_logo of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type email_logo: str
        """

        self._email_logo = email_logo

    @property
    def company_name_team(self) -> str:
        """Gets the company_name_team of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The company_name_team of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._company_name_team

    @company_name_team.setter
    def company_name_team(self, company_name_team: str):
        """Sets the company_name_team of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param company_name_team: The company_name_team of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type company_name_team: str
        """

        self._company_name_team = company_name_team

    @property
    def subject_consumption(self) -> str:
        """Gets the subject_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The subject_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._subject_consumption

    @subject_consumption.setter
    def subject_consumption(self, subject_consumption: str):
        """Sets the subject_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param subject_consumption: The subject_consumption of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type subject_consumption: str
        """

        self._subject_consumption = subject_consumption

    @property
    def footer(self) -> str:
        """Gets the footer of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :return: The footer of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :rtype: str
        """
        return self._footer

    @footer.setter
    def footer(self, footer: str):
        """Sets the footer of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.


        :param footer: The footer of this ManageEmailTemplatesRequestEmailTemplateDataConsumption.
        :type footer: str
        """

        self._footer = footer

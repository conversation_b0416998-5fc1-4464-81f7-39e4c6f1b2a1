# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetMyBundleConsumptionResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, status: bool=None, bundle_name: str=None, data_allocated: float=None, iccid: str=None, logo_uri: str=None, data_used: float=None, data_remaining: float=None, data_unit: str=None, policy_status: str=None, plan_status: str=None, profile_expiry_date: str=None, bundle_expiry_date: str=None, profile_status: str=None, unlimited: bool=False, cached_at: str=None):  # noqa: E501
        """GetMyBundleConsumptionResponse - a model defined in Swagger

        :param response_code: The response_code of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type title: str
        :param status: The status of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type status: bool
        :param bundle_name: The bundle_name of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type bundle_name: str
        :param data_allocated: The data_allocated of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type data_allocated: float
        :param iccid: The iccid of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type iccid: str
        :param logo_uri: The logo_uri of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type logo_uri: str
        :param data_used: The data_used of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type data_used: float
        :param data_remaining: The data_remaining of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type data_remaining: float
        :param data_unit: The data_unit of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type data_unit: str
        :param policy_status: The policy_status of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type policy_status: str
        :param plan_status: The plan_status of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type plan_status: str
        :param profile_expiry_date: The profile_expiry_date of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type profile_expiry_date: str
        :param bundle_expiry_date: The bundle_expiry_date of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type bundle_expiry_date: str
        :param profile_status: The profile_status of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type profile_status: str
        :param unlimited: The unlimited of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type unlimited: bool
        :param cached_at: The cached_at of this GetMyBundleConsumptionResponse.  # noqa: E501
        :type cached_at: str
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'status': bool,
            'bundle_name': str,
            'data_allocated': float,
            'iccid': str,
            'logo_uri': str,
            'data_used': float,
            'data_remaining': float,
            'data_unit': str,
            'policy_status': str,
            'plan_status': str,
            'profile_expiry_date': str,
            'bundle_expiry_date': str,
            'profile_status': str,
            'unlimited': bool,
            'cached_at': str
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'status': 'status',
            'bundle_name': 'bundle_name',
            'data_allocated': 'data_allocated',
            'iccid': 'iccid',
            'logo_uri': 'logo_uri',
            'data_used': 'data_used',
            'data_remaining': 'data_remaining',
            'data_unit': 'data_unit',
            'policy_status': 'policy_status',
            'plan_status': 'plan_status',
            'profile_expiry_date': 'profile_expiry_date',
            'bundle_expiry_date': 'bundle_expiry_date',
            'profile_status': 'profile_status',
            'unlimited': 'unlimited',
            'cached_at': 'cached_at'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._status = status
        self._bundle_name = bundle_name
        self._data_allocated = data_allocated
        self._iccid = iccid
        self._logo_uri = logo_uri
        self._data_used = data_used
        self._data_remaining = data_remaining
        self._data_unit = data_unit
        self._policy_status = policy_status
        self._plan_status = plan_status
        self._profile_expiry_date = profile_expiry_date
        self._bundle_expiry_date = bundle_expiry_date
        self._profile_status = profile_status
        self._unlimited = unlimited
        self._cached_at = cached_at

    @classmethod
    def from_dict(cls, dikt) -> 'GetMyBundleConsumptionResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetMyBundleConsumptionResponse of this GetMyBundleConsumptionResponse.  # noqa: E501
        :rtype: GetMyBundleConsumptionResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetMyBundleConsumptionResponse.


        :return: The response_code of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetMyBundleConsumptionResponse.


        :param response_code: The response_code of this GetMyBundleConsumptionResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetMyBundleConsumptionResponse.


        :return: The developer_message of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetMyBundleConsumptionResponse.


        :param developer_message: The developer_message of this GetMyBundleConsumptionResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetMyBundleConsumptionResponse.


        :return: The title of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetMyBundleConsumptionResponse.


        :param title: The title of this GetMyBundleConsumptionResponse.
        :type title: str
        """

        self._title = title

    @property
    def status(self) -> bool:
        """Gets the status of this GetMyBundleConsumptionResponse.


        :return: The status of this GetMyBundleConsumptionResponse.
        :rtype: bool
        """
        return self._status

    @status.setter
    def status(self, status: bool):
        """Sets the status of this GetMyBundleConsumptionResponse.


        :param status: The status of this GetMyBundleConsumptionResponse.
        :type status: bool
        """

        self._status = status

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this GetMyBundleConsumptionResponse.


        :return: The bundle_name of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this GetMyBundleConsumptionResponse.


        :param bundle_name: The bundle_name of this GetMyBundleConsumptionResponse.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def data_allocated(self) -> float:
        """Gets the data_allocated of this GetMyBundleConsumptionResponse.


        :return: The data_allocated of this GetMyBundleConsumptionResponse.
        :rtype: float
        """
        return self._data_allocated

    @data_allocated.setter
    def data_allocated(self, data_allocated: float):
        """Sets the data_allocated of this GetMyBundleConsumptionResponse.


        :param data_allocated: The data_allocated of this GetMyBundleConsumptionResponse.
        :type data_allocated: float
        """

        self._data_allocated = data_allocated

    @property
    def iccid(self) -> str:
        """Gets the iccid of this GetMyBundleConsumptionResponse.


        :return: The iccid of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._iccid

    @iccid.setter
    def iccid(self, iccid: str):
        """Sets the iccid of this GetMyBundleConsumptionResponse.


        :param iccid: The iccid of this GetMyBundleConsumptionResponse.
        :type iccid: str
        """

        self._iccid = iccid

    @property
    def logo_uri(self) -> str:
        """Gets the logo_uri of this GetMyBundleConsumptionResponse.


        :return: The logo_uri of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._logo_uri

    @logo_uri.setter
    def logo_uri(self, logo_uri: str):
        """Sets the logo_uri of this GetMyBundleConsumptionResponse.


        :param logo_uri: The logo_uri of this GetMyBundleConsumptionResponse.
        :type logo_uri: str
        """

        self._logo_uri = logo_uri

    @property
    def data_used(self) -> float:
        """Gets the data_used of this GetMyBundleConsumptionResponse.


        :return: The data_used of this GetMyBundleConsumptionResponse.
        :rtype: float
        """
        return self._data_used

    @data_used.setter
    def data_used(self, data_used: float):
        """Sets the data_used of this GetMyBundleConsumptionResponse.


        :param data_used: The data_used of this GetMyBundleConsumptionResponse.
        :type data_used: float
        """

        self._data_used = data_used

    @property
    def data_remaining(self) -> float:
        """Gets the data_remaining of this GetMyBundleConsumptionResponse.


        :return: The data_remaining of this GetMyBundleConsumptionResponse.
        :rtype: float
        """
        return self._data_remaining

    @data_remaining.setter
    def data_remaining(self, data_remaining: float):
        """Sets the data_remaining of this GetMyBundleConsumptionResponse.


        :param data_remaining: The data_remaining of this GetMyBundleConsumptionResponse.
        :type data_remaining: float
        """

        self._data_remaining = data_remaining

    @property
    def data_unit(self) -> str:
        """Gets the data_unit of this GetMyBundleConsumptionResponse.


        :return: The data_unit of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._data_unit

    @data_unit.setter
    def data_unit(self, data_unit: str):
        """Sets the data_unit of this GetMyBundleConsumptionResponse.


        :param data_unit: The data_unit of this GetMyBundleConsumptionResponse.
        :type data_unit: str
        """
        allowed_values = ["MB", "GB", "TB"]  # noqa: E501
        if data_unit not in allowed_values:
            raise ValueError(
                "Invalid value for `data_unit` ({0}), must be one of {1}"
                .format(data_unit, allowed_values)
            )

        self._data_unit = data_unit

    @property
    def policy_status(self) -> str:
        """Gets the policy_status of this GetMyBundleConsumptionResponse.


        :return: The policy_status of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._policy_status

    @policy_status.setter
    def policy_status(self, policy_status: str):
        """Sets the policy_status of this GetMyBundleConsumptionResponse.


        :param policy_status: The policy_status of this GetMyBundleConsumptionResponse.
        :type policy_status: str
        """

        self._policy_status = policy_status

    @property
    def plan_status(self) -> str:
        """Gets the plan_status of this GetMyBundleConsumptionResponse.


        :return: The plan_status of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._plan_status

    @plan_status.setter
    def plan_status(self, plan_status: str):
        """Sets the plan_status of this GetMyBundleConsumptionResponse.


        :param plan_status: The plan_status of this GetMyBundleConsumptionResponse.
        :type plan_status: str
        """

        self._plan_status = plan_status

    @property
    def profile_expiry_date(self) -> str:
        """Gets the profile_expiry_date of this GetMyBundleConsumptionResponse.


        :return: The profile_expiry_date of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._profile_expiry_date

    @profile_expiry_date.setter
    def profile_expiry_date(self, profile_expiry_date: str):
        """Sets the profile_expiry_date of this GetMyBundleConsumptionResponse.


        :param profile_expiry_date: The profile_expiry_date of this GetMyBundleConsumptionResponse.
        :type profile_expiry_date: str
        """

        self._profile_expiry_date = profile_expiry_date

    @property
    def bundle_expiry_date(self) -> str:
        """Gets the bundle_expiry_date of this GetMyBundleConsumptionResponse.


        :return: The bundle_expiry_date of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._bundle_expiry_date

    @bundle_expiry_date.setter
    def bundle_expiry_date(self, bundle_expiry_date: str):
        """Sets the bundle_expiry_date of this GetMyBundleConsumptionResponse.


        :param bundle_expiry_date: The bundle_expiry_date of this GetMyBundleConsumptionResponse.
        :type bundle_expiry_date: str
        """

        self._bundle_expiry_date = bundle_expiry_date

    @property
    def profile_status(self) -> str:
        """Gets the profile_status of this GetMyBundleConsumptionResponse.


        :return: The profile_status of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._profile_status

    @profile_status.setter
    def profile_status(self, profile_status: str):
        """Sets the profile_status of this GetMyBundleConsumptionResponse.


        :param profile_status: The profile_status of this GetMyBundleConsumptionResponse.
        :type profile_status: str
        """

        self._profile_status = profile_status

    @property
    def unlimited(self) -> bool:
        """Gets the unlimited of this GetMyBundleConsumptionResponse.


        :return: The unlimited of this GetMyBundleConsumptionResponse.
        :rtype: bool
        """
        return self._unlimited

    @unlimited.setter
    def unlimited(self, unlimited: bool):
        """Sets the unlimited of this GetMyBundleConsumptionResponse.


        :param unlimited: The unlimited of this GetMyBundleConsumptionResponse.
        :type unlimited: bool
        """

        self._unlimited = unlimited

    @property
    def cached_at(self) -> str:
        """Gets the cached_at of this GetMyBundleConsumptionResponse.


        :return: The cached_at of this GetMyBundleConsumptionResponse.
        :rtype: str
        """
        return self._cached_at

    @cached_at.setter
    def cached_at(self, cached_at: str):
        """Sets the cached_at of this GetMyBundleConsumptionResponse.


        :param cached_at: The cached_at of this GetMyBundleConsumptionResponse.
        :type cached_at: str
        """

        self._cached_at = cached_at

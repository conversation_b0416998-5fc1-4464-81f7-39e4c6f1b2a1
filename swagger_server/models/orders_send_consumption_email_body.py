# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class OrdersSendConsumptionEmailBody(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None):  # noqa: E501
        """OrdersSendConsumptionEmailBody - a model defined in Swagger

        :param order_id: The order_id of this OrdersSendConsumptionEmailBody.  # noqa: E501
        :type order_id: str
        """
        self.swagger_types = {
            'order_id': str
        }

        self.attribute_map = {
            'order_id': 'order_id'
        }
        self._order_id = order_id

    @classmethod
    def from_dict(cls, dikt) -> 'OrdersSendConsumptionEmailBody':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Orders_SendConsumptionEmail_body of this OrdersSendConsumptionEmailBody.  # noqa: E501
        :rtype: OrdersSendConsumptionEmailBody
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this OrdersSendConsumptionEmailBody.


        :return: The order_id of this OrdersSendConsumptionEmailBody.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this OrdersSendConsumptionEmailBody.


        :param order_id: The order_id of this OrdersSendConsumptionEmailBody.
        :type order_id: str
        """
        if order_id is None:
            raise ValueError("Invalid value for `order_id`, must not be `None`")  # noqa: E501

        self._order_id = order_id

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.region_grouped_bundles_response_regions import RegionGroupedBundlesResponseRegions  # noqa: F401,E501
from swagger_server import util


class RegionGroupedBundlesResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, page: int=None, limit: int=None, total_bundles: int=None, total_regions: int=None, regions: List[RegionGroupedBundlesResponseRegions]=None):  # noqa: E501
        """RegionGroupedBundlesResponse - a model defined in Swagger

        :param page: The page of this RegionGroupedBundlesResponse.  # noqa: E501
        :type page: int
        :param limit: The limit of this RegionGroupedBundlesResponse.  # noqa: E501
        :type limit: int
        :param total_bundles: The total_bundles of this RegionGroupedBundlesResponse.  # noqa: E501
        :type total_bundles: int
        :param total_regions: The total_regions of this RegionGroupedBundlesResponse.  # noqa: E501
        :type total_regions: int
        :param regions: The regions of this RegionGroupedBundlesResponse.  # noqa: E501
        :type regions: List[RegionGroupedBundlesResponseRegions]
        """
        self.swagger_types = {
            'page': int,
            'limit': int,
            'total_bundles': int,
            'total_regions': int,
            'regions': List[RegionGroupedBundlesResponseRegions]
        }

        self.attribute_map = {
            'page': 'page',
            'limit': 'limit',
            'total_bundles': 'total_bundles',
            'total_regions': 'total_regions',
            'regions': 'regions'
        }
        self._page = page
        self._limit = limit
        self._total_bundles = total_bundles
        self._total_regions = total_regions
        self._regions = regions

    @classmethod
    def from_dict(cls, dikt) -> 'RegionGroupedBundlesResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The RegionGroupedBundlesResponse of this RegionGroupedBundlesResponse.  # noqa: E501
        :rtype: RegionGroupedBundlesResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def page(self) -> int:
        """Gets the page of this RegionGroupedBundlesResponse.


        :return: The page of this RegionGroupedBundlesResponse.
        :rtype: int
        """
        return self._page

    @page.setter
    def page(self, page: int):
        """Sets the page of this RegionGroupedBundlesResponse.


        :param page: The page of this RegionGroupedBundlesResponse.
        :type page: int
        """

        self._page = page

    @property
    def limit(self) -> int:
        """Gets the limit of this RegionGroupedBundlesResponse.


        :return: The limit of this RegionGroupedBundlesResponse.
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit: int):
        """Sets the limit of this RegionGroupedBundlesResponse.


        :param limit: The limit of this RegionGroupedBundlesResponse.
        :type limit: int
        """

        self._limit = limit

    @property
    def total_bundles(self) -> int:
        """Gets the total_bundles of this RegionGroupedBundlesResponse.


        :return: The total_bundles of this RegionGroupedBundlesResponse.
        :rtype: int
        """
        return self._total_bundles

    @total_bundles.setter
    def total_bundles(self, total_bundles: int):
        """Sets the total_bundles of this RegionGroupedBundlesResponse.


        :param total_bundles: The total_bundles of this RegionGroupedBundlesResponse.
        :type total_bundles: int
        """

        self._total_bundles = total_bundles

    @property
    def total_regions(self) -> int:
        """Gets the total_regions of this RegionGroupedBundlesResponse.


        :return: The total_regions of this RegionGroupedBundlesResponse.
        :rtype: int
        """
        return self._total_regions

    @total_regions.setter
    def total_regions(self, total_regions: int):
        """Sets the total_regions of this RegionGroupedBundlesResponse.


        :param total_regions: The total_regions of this RegionGroupedBundlesResponse.
        :type total_regions: int
        """

        self._total_regions = total_regions

    @property
    def regions(self) -> List[RegionGroupedBundlesResponseRegions]:
        """Gets the regions of this RegionGroupedBundlesResponse.


        :return: The regions of this RegionGroupedBundlesResponse.
        :rtype: List[RegionGroupedBundlesResponseRegions]
        """
        return self._regions

    @regions.setter
    def regions(self, regions: List[RegionGroupedBundlesResponseRegions]):
        """Sets the regions of this RegionGroupedBundlesResponse.


        :param regions: The regions of this RegionGroupedBundlesResponse.
        :type regions: List[RegionGroupedBundlesResponseRegions]
        """

        self._regions = regions

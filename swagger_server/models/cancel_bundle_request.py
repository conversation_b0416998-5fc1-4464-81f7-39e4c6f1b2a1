# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class CancelBundleRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_reference: str=None):  # noqa: E501
        """CancelBundleRequest - a model defined in Swagger

        :param order_reference: The order_reference of this CancelBundleRequest.  # noqa: E501
        :type order_reference: str
        """
        self.swagger_types = {
            'order_reference': str
        }

        self.attribute_map = {
            'order_reference': 'order_reference'
        }
        self._order_reference = order_reference

    @classmethod
    def from_dict(cls, dikt) -> 'CancelBundleRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CancelBundleRequest of this CancelBundleRequest.  # noqa: E501
        :rtype: CancelBundleRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_reference(self) -> str:
        """Gets the order_reference of this CancelBundleRequest.


        :return: The order_reference of this CancelBundleRequest.
        :rtype: str
        """
        return self._order_reference

    @order_reference.setter
    def order_reference(self, order_reference: str):
        """Sets the order_reference of this CancelBundleRequest.


        :param order_reference: The order_reference of this CancelBundleRequest.
        :type order_reference: str
        """

        self._order_reference = order_reference

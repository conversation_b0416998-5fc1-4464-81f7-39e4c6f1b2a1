# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class EditIssueReportRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, message: str=None, attachments: List[str]=None):  # noqa: E501
        """EditIssueReportRequest - a model defined in Swagger

        :param message: The message of this EditIssueReportRequest.  # noqa: E501
        :type message: str
        :param attachments: The attachments of this EditIssueReportRequest.  # noqa: E501
        :type attachments: List[str]
        """
        self.swagger_types = {
            'message': str,
            'attachments': List[str]
        }

        self.attribute_map = {
            'message': 'message',
            'attachments': 'attachments'
        }
        self._message = message
        self._attachments = attachments

    @classmethod
    def from_dict(cls, dikt) -> 'EditIssueReportRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The EditIssueReportRequest of this EditIssueReportRequest.  # noqa: E501
        :rtype: EditIssueReportRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def message(self) -> str:
        """Gets the message of this EditIssueReportRequest.

        Detailed description of the issue or concern.  # noqa: E501

        :return: The message of this EditIssueReportRequest.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this EditIssueReportRequest.

        Detailed description of the issue or concern.  # noqa: E501

        :param message: The message of this EditIssueReportRequest.
        :type message: str
        """

        self._message = message

    @property
    def attachments(self) -> List[str]:
        """Gets the attachments of this EditIssueReportRequest.

        List of attached files.  # noqa: E501

        :return: The attachments of this EditIssueReportRequest.
        :rtype: List[str]
        """
        return self._attachments

    @attachments.setter
    def attachments(self, attachments: List[str]):
        """Sets the attachments of this EditIssueReportRequest.

        List of attached files.  # noqa: E501

        :param attachments: The attachments of this EditIssueReportRequest.
        :type attachments: List[str]
        """

        self._attachments = attachments

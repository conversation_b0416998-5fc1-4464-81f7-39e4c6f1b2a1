# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class ResellerEmailSettings(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, username: str=None, password: str=None, smtp_server: str=None, smtp_port: str=None):  # noqa: E501
        """ResellerEmailSettings - a model defined in Swagger

        :param username: The username of this ResellerEmailSettings.  # noqa: E501
        :type username: str
        :param password: The password of this ResellerEmailSettings.  # noqa: E501
        :type password: str
        :param smtp_server: The smtp_server of this ResellerEmailSettings.  # noqa: E501
        :type smtp_server: str
        :param smtp_port: The smtp_port of this ResellerEmailSettings.  # noqa: E501
        :type smtp_port: str
        """
        self.swagger_types = {
            'username': str,
            'password': str,
            'smtp_server': str,
            'smtp_port': str
        }

        self.attribute_map = {
            'username': 'username',
            'password': 'password',
            'smtp_server': 'smtp_server',
            'smtp_port': 'smtp_port'
        }
        self._username = username
        self._password = password
        self._smtp_server = smtp_server
        self._smtp_port = smtp_port

    @classmethod
    def from_dict(cls, dikt) -> 'ResellerEmailSettings':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Reseller_email_settings of this ResellerEmailSettings.  # noqa: E501
        :rtype: ResellerEmailSettings
        """
        return util.deserialize_model(dikt, cls)

    @property
    def username(self) -> str:
        """Gets the username of this ResellerEmailSettings.


        :return: The username of this ResellerEmailSettings.
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username: str):
        """Sets the username of this ResellerEmailSettings.


        :param username: The username of this ResellerEmailSettings.
        :type username: str
        """
        if username is None:
            raise ValueError("Invalid value for `username`, must not be `None`")  # noqa: E501

        self._username = username

    @property
    def password(self) -> str:
        """Gets the password of this ResellerEmailSettings.


        :return: The password of this ResellerEmailSettings.
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password: str):
        """Sets the password of this ResellerEmailSettings.


        :param password: The password of this ResellerEmailSettings.
        :type password: str
        """
        if password is None:
            raise ValueError("Invalid value for `password`, must not be `None`")  # noqa: E501

        self._password = password

    @property
    def smtp_server(self) -> str:
        """Gets the smtp_server of this ResellerEmailSettings.


        :return: The smtp_server of this ResellerEmailSettings.
        :rtype: str
        """
        return self._smtp_server

    @smtp_server.setter
    def smtp_server(self, smtp_server: str):
        """Sets the smtp_server of this ResellerEmailSettings.


        :param smtp_server: The smtp_server of this ResellerEmailSettings.
        :type smtp_server: str
        """
        if smtp_server is None:
            raise ValueError("Invalid value for `smtp_server`, must not be `None`")  # noqa: E501

        self._smtp_server = smtp_server

    @property
    def smtp_port(self) -> str:
        """Gets the smtp_port of this ResellerEmailSettings.


        :return: The smtp_port of this ResellerEmailSettings.
        :rtype: str
        """
        return self._smtp_port

    @smtp_port.setter
    def smtp_port(self, smtp_port: str):
        """Sets the smtp_port of this ResellerEmailSettings.


        :param smtp_port: The smtp_port of this ResellerEmailSettings.
        :type smtp_port: str
        """
        if smtp_port is None:
            raise ValueError("Invalid value for `smtp_port`, must not be `None`")  # noqa: E501

        self._smtp_port = smtp_port

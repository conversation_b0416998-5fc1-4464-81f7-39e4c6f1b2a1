# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_currencies_response_currencies import GetCurrenciesResponseCurrencies  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetCurrenciesResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, currencies: List[GetCurrenciesResponseCurrencies]=None, total_currencies_count: int=None):  # noqa: E501
        """GetCurrenciesResponse - a model defined in Swagger

        :param response_code: The response_code of this GetCurrenciesResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetCurrenciesResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetCurrenciesResponse.  # noqa: E501
        :type title: str
        :param currencies: The currencies of this GetCurrenciesResponse.  # noqa: E501
        :type currencies: List[GetCurrenciesResponseCurrencies]
        :param total_currencies_count: The total_currencies_count of this GetCurrenciesResponse.  # noqa: E501
        :type total_currencies_count: int
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'currencies': List[GetCurrenciesResponseCurrencies],
            'total_currencies_count': int
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'currencies': 'currencies',
            'total_currencies_count': 'total_currencies_count'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._currencies = currencies
        self._total_currencies_count = total_currencies_count

    @classmethod
    def from_dict(cls, dikt) -> 'GetCurrenciesResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetCurrenciesResponse of this GetCurrenciesResponse.  # noqa: E501
        :rtype: GetCurrenciesResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetCurrenciesResponse.


        :return: The response_code of this GetCurrenciesResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetCurrenciesResponse.


        :param response_code: The response_code of this GetCurrenciesResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetCurrenciesResponse.


        :return: The developer_message of this GetCurrenciesResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetCurrenciesResponse.


        :param developer_message: The developer_message of this GetCurrenciesResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetCurrenciesResponse.


        :return: The title of this GetCurrenciesResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetCurrenciesResponse.


        :param title: The title of this GetCurrenciesResponse.
        :type title: str
        """

        self._title = title

    @property
    def currencies(self) -> List[GetCurrenciesResponseCurrencies]:
        """Gets the currencies of this GetCurrenciesResponse.


        :return: The currencies of this GetCurrenciesResponse.
        :rtype: List[GetCurrenciesResponseCurrencies]
        """
        return self._currencies

    @currencies.setter
    def currencies(self, currencies: List[GetCurrenciesResponseCurrencies]):
        """Sets the currencies of this GetCurrenciesResponse.


        :param currencies: The currencies of this GetCurrenciesResponse.
        :type currencies: List[GetCurrenciesResponseCurrencies]
        """

        self._currencies = currencies

    @property
    def total_currencies_count(self) -> int:
        """Gets the total_currencies_count of this GetCurrenciesResponse.


        :return: The total_currencies_count of this GetCurrenciesResponse.
        :rtype: int
        """
        return self._total_currencies_count

    @total_currencies_count.setter
    def total_currencies_count(self, total_currencies_count: int):
        """Sets the total_currencies_count of this GetCurrenciesResponse.


        :param total_currencies_count: The total_currencies_count of this GetCurrenciesResponse.
        :type total_currencies_count: int
        """

        self._total_currencies_count = total_currencies_count

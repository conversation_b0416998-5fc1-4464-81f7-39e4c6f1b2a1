# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetOrderHistoryResponseInner(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None, reseller_id: str=None, iccid: str=None, price: float=None, _date: datetime=None):  # noqa: E501
        """GetOrderHistoryResponseInner - a model defined in Swagger

        :param order_id: The order_id of this GetOrderHistoryResponseInner.  # noqa: E501
        :type order_id: str
        :param reseller_id: The reseller_id of this GetOrderHistoryResponseInner.  # noqa: E501
        :type reseller_id: str
        :param iccid: The iccid of this GetOrderHistoryResponseInner.  # noqa: E501
        :type iccid: str
        :param price: The price of this GetOrderHistoryResponseInner.  # noqa: E501
        :type price: float
        :param _date: The _date of this GetOrderHistoryResponseInner.  # noqa: E501
        :type _date: datetime
        """
        self.swagger_types = {
            'order_id': str,
            'reseller_id': str,
            'iccid': str,
            'price': float,
            '_date': datetime
        }

        self.attribute_map = {
            'order_id': 'order_id',
            'reseller_id': 'reseller_id',
            'iccid': 'iccid',
            'price': 'price',
            '_date': 'date'
        }
        self._order_id = order_id
        self._reseller_id = reseller_id
        self._iccid = iccid
        self._price = price
        self.__date = _date

    @classmethod
    def from_dict(cls, dikt) -> 'GetOrderHistoryResponseInner':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetOrderHistoryResponse_inner of this GetOrderHistoryResponseInner.  # noqa: E501
        :rtype: GetOrderHistoryResponseInner
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this GetOrderHistoryResponseInner.


        :return: The order_id of this GetOrderHistoryResponseInner.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this GetOrderHistoryResponseInner.


        :param order_id: The order_id of this GetOrderHistoryResponseInner.
        :type order_id: str
        """

        self._order_id = order_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetOrderHistoryResponseInner.


        :return: The reseller_id of this GetOrderHistoryResponseInner.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetOrderHistoryResponseInner.


        :param reseller_id: The reseller_id of this GetOrderHistoryResponseInner.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def iccid(self) -> str:
        """Gets the iccid of this GetOrderHistoryResponseInner.


        :return: The iccid of this GetOrderHistoryResponseInner.
        :rtype: str
        """
        return self._iccid

    @iccid.setter
    def iccid(self, iccid: str):
        """Sets the iccid of this GetOrderHistoryResponseInner.


        :param iccid: The iccid of this GetOrderHistoryResponseInner.
        :type iccid: str
        """

        self._iccid = iccid

    @property
    def price(self) -> float:
        """Gets the price of this GetOrderHistoryResponseInner.

        in dollars  # noqa: E501

        :return: The price of this GetOrderHistoryResponseInner.
        :rtype: float
        """
        return self._price

    @price.setter
    def price(self, price: float):
        """Sets the price of this GetOrderHistoryResponseInner.

        in dollars  # noqa: E501

        :param price: The price of this GetOrderHistoryResponseInner.
        :type price: float
        """

        self._price = price

    @property
    def _date(self) -> datetime:
        """Gets the _date of this GetOrderHistoryResponseInner.


        :return: The _date of this GetOrderHistoryResponseInner.
        :rtype: datetime
        """
        return self.__date

    @_date.setter
    def _date(self, _date: datetime):
        """Sets the _date of this GetOrderHistoryResponseInner.


        :param _date: The _date of this GetOrderHistoryResponseInner.
        :type _date: datetime
        """

        self.__date = _date

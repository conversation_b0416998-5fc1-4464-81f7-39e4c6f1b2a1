# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_voucher_use_history_response_data import GetVoucherUseHistoryResponseData  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetVoucherUseHistoryResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, total_uses_count: int=None, data: List[GetVoucherUseHistoryResponseData]=None):  # noqa: E501
        """GetVoucherUseHistoryResponse - a model defined in Swagger

        :param response_code: The response_code of this GetVoucherUseHistoryResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetVoucherUseHistoryResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetVoucherUseHistoryResponse.  # noqa: E501
        :type title: str
        :param total_uses_count: The total_uses_count of this GetVoucherUseHistoryResponse.  # noqa: E501
        :type total_uses_count: int
        :param data: The data of this GetVoucherUseHistoryResponse.  # noqa: E501
        :type data: List[GetVoucherUseHistoryResponseData]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'total_uses_count': int,
            'data': List[GetVoucherUseHistoryResponseData]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'total_uses_count': 'total_uses_count',
            'data': 'data'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._total_uses_count = total_uses_count
        self._data = data

    @classmethod
    def from_dict(cls, dikt) -> 'GetVoucherUseHistoryResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetVoucherUseHistoryResponse of this GetVoucherUseHistoryResponse.  # noqa: E501
        :rtype: GetVoucherUseHistoryResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetVoucherUseHistoryResponse.


        :return: The response_code of this GetVoucherUseHistoryResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetVoucherUseHistoryResponse.


        :param response_code: The response_code of this GetVoucherUseHistoryResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetVoucherUseHistoryResponse.


        :return: The developer_message of this GetVoucherUseHistoryResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetVoucherUseHistoryResponse.


        :param developer_message: The developer_message of this GetVoucherUseHistoryResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetVoucherUseHistoryResponse.


        :return: The title of this GetVoucherUseHistoryResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetVoucherUseHistoryResponse.


        :param title: The title of this GetVoucherUseHistoryResponse.
        :type title: str
        """

        self._title = title

    @property
    def total_uses_count(self) -> int:
        """Gets the total_uses_count of this GetVoucherUseHistoryResponse.


        :return: The total_uses_count of this GetVoucherUseHistoryResponse.
        :rtype: int
        """
        return self._total_uses_count

    @total_uses_count.setter
    def total_uses_count(self, total_uses_count: int):
        """Sets the total_uses_count of this GetVoucherUseHistoryResponse.


        :param total_uses_count: The total_uses_count of this GetVoucherUseHistoryResponse.
        :type total_uses_count: int
        """

        self._total_uses_count = total_uses_count

    @property
    def data(self) -> List[GetVoucherUseHistoryResponseData]:
        """Gets the data of this GetVoucherUseHistoryResponse.


        :return: The data of this GetVoucherUseHistoryResponse.
        :rtype: List[GetVoucherUseHistoryResponseData]
        """
        return self._data

    @data.setter
    def data(self, data: List[GetVoucherUseHistoryResponseData]):
        """Sets the data of this GetVoucherUseHistoryResponse.


        :param data: The data of this GetVoucherUseHistoryResponse.
        :type data: List[GetVoucherUseHistoryResponseData]
        """

        self._data = data

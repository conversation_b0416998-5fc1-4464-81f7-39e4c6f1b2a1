# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.manage_network_list_by_regions_request_countries import ManageNetworkListByRegionsRequestCountries  # noqa: F401,E501
from swagger_server import util


class ManageNetworkListByRegionsRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, region_id: str=None, region_name: str=None, countries: List[ManageNetworkListByRegionsRequestCountries]=None):  # noqa: E501
        """ManageNetworkListByRegionsRequest - a model defined in Swagger

        :param region_id: The region_id of this ManageNetworkListByRegionsRequest.  # noqa: E501
        :type region_id: str
        :param region_name: The region_name of this ManageNetworkListByRegionsRequest.  # noqa: E501
        :type region_name: str
        :param countries: The countries of this ManageNetworkListByRegionsRequest.  # noqa: E501
        :type countries: List[ManageNetworkListByRegionsRequestCountries]
        """
        self.swagger_types = {
            'region_id': str,
            'region_name': str,
            'countries': List[ManageNetworkListByRegionsRequestCountries]
        }

        self.attribute_map = {
            'region_id': 'regionId',
            'region_name': 'regionName',
            'countries': 'countries'
        }
        self._region_id = region_id
        self._region_name = region_name
        self._countries = countries

    @classmethod
    def from_dict(cls, dikt) -> 'ManageNetworkListByRegionsRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ManageNetworkListByRegionsRequest of this ManageNetworkListByRegionsRequest.  # noqa: E501
        :rtype: ManageNetworkListByRegionsRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def region_id(self) -> str:
        """Gets the region_id of this ManageNetworkListByRegionsRequest.

        Unique identifier for the region.  # noqa: E501

        :return: The region_id of this ManageNetworkListByRegionsRequest.
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id: str):
        """Sets the region_id of this ManageNetworkListByRegionsRequest.

        Unique identifier for the region.  # noqa: E501

        :param region_id: The region_id of this ManageNetworkListByRegionsRequest.
        :type region_id: str
        """

        self._region_id = region_id

    @property
    def region_name(self) -> str:
        """Gets the region_name of this ManageNetworkListByRegionsRequest.

        Name of the region.  # noqa: E501

        :return: The region_name of this ManageNetworkListByRegionsRequest.
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name: str):
        """Sets the region_name of this ManageNetworkListByRegionsRequest.

        Name of the region.  # noqa: E501

        :param region_name: The region_name of this ManageNetworkListByRegionsRequest.
        :type region_name: str
        """

        self._region_name = region_name

    @property
    def countries(self) -> List[ManageNetworkListByRegionsRequestCountries]:
        """Gets the countries of this ManageNetworkListByRegionsRequest.

        List of countries in the region.  # noqa: E501

        :return: The countries of this ManageNetworkListByRegionsRequest.
        :rtype: List[ManageNetworkListByRegionsRequestCountries]
        """
        return self._countries

    @countries.setter
    def countries(self, countries: List[ManageNetworkListByRegionsRequestCountries]):
        """Sets the countries of this ManageNetworkListByRegionsRequest.

        List of countries in the region.  # noqa: E501

        :param countries: The countries of this ManageNetworkListByRegionsRequest.
        :type countries: List[ManageNetworkListByRegionsRequestCountries]
        """

        self._countries = countries

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class AddBranchResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, message: str=None, branch_id: str=None, agent_id: str=None):  # noqa: E501
        """AddBranchResponse - a model defined in Swagger

        :param response_code: The response_code of this AddBranchResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this AddBranchResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this AddBranchResponse.  # noqa: E501
        :type title: str
        :param message: The message of this AddBranchResponse.  # noqa: E501
        :type message: str
        :param branch_id: The branch_id of this AddBranchResponse.  # noqa: E501
        :type branch_id: str
        :param agent_id: The agent_id of this AddBranchResponse.  # noqa: E501
        :type agent_id: str
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'message': str,
            'branch_id': str,
            'agent_id': str
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'message': 'message',
            'branch_id': 'branch_id',
            'agent_id': 'agent_id'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._message = message
        self._branch_id = branch_id
        self._agent_id = agent_id

    @classmethod
    def from_dict(cls, dikt) -> 'AddBranchResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AddBranchResponse of this AddBranchResponse.  # noqa: E501
        :rtype: AddBranchResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this AddBranchResponse.


        :return: The response_code of this AddBranchResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this AddBranchResponse.


        :param response_code: The response_code of this AddBranchResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this AddBranchResponse.


        :return: The developer_message of this AddBranchResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this AddBranchResponse.


        :param developer_message: The developer_message of this AddBranchResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this AddBranchResponse.


        :return: The title of this AddBranchResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this AddBranchResponse.


        :param title: The title of this AddBranchResponse.
        :type title: str
        """

        self._title = title

    @property
    def message(self) -> str:
        """Gets the message of this AddBranchResponse.


        :return: The message of this AddBranchResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this AddBranchResponse.


        :param message: The message of this AddBranchResponse.
        :type message: str
        """

        self._message = message

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this AddBranchResponse.


        :return: The branch_id of this AddBranchResponse.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this AddBranchResponse.


        :param branch_id: The branch_id of this AddBranchResponse.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def agent_id(self) -> str:
        """Gets the agent_id of this AddBranchResponse.


        :return: The agent_id of this AddBranchResponse.
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id: str):
        """Sets the agent_id of this AddBranchResponse.


        :param agent_id: The agent_id of this AddBranchResponse.
        :type agent_id: str
        """

        self._agent_id = agent_id

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class CustomizePriceResponseInvalidBundlePrices(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, bundle_code: str=None, custom_price: float=None, unit_price: float=None):  # noqa: E501
        """CustomizePriceResponseInvalidBundlePrices - a model defined in Swagger

        :param bundle_code: The bundle_code of this CustomizePriceResponseInvalidBundlePrices.  # noqa: E501
        :type bundle_code: str
        :param custom_price: The custom_price of this CustomizePriceResponseInvalidBundlePrices.  # noqa: E501
        :type custom_price: float
        :param unit_price: The unit_price of this CustomizePriceResponseInvalidBundlePrices.  # noqa: E501
        :type unit_price: float
        """
        self.swagger_types = {
            'bundle_code': str,
            'custom_price': float,
            'unit_price': float
        }

        self.attribute_map = {
            'bundle_code': 'bundle_code',
            'custom_price': 'custom_price',
            'unit_price': 'unit_price'
        }
        self._bundle_code = bundle_code
        self._custom_price = custom_price
        self._unit_price = unit_price

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizePriceResponseInvalidBundlePrices':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizePriceResponse_invalid_bundle_prices of this CustomizePriceResponseInvalidBundlePrices.  # noqa: E501
        :rtype: CustomizePriceResponseInvalidBundlePrices
        """
        return util.deserialize_model(dikt, cls)

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this CustomizePriceResponseInvalidBundlePrices.


        :return: The bundle_code of this CustomizePriceResponseInvalidBundlePrices.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this CustomizePriceResponseInvalidBundlePrices.


        :param bundle_code: The bundle_code of this CustomizePriceResponseInvalidBundlePrices.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def custom_price(self) -> float:
        """Gets the custom_price of this CustomizePriceResponseInvalidBundlePrices.

        in dollars  # noqa: E501

        :return: The custom_price of this CustomizePriceResponseInvalidBundlePrices.
        :rtype: float
        """
        return self._custom_price

    @custom_price.setter
    def custom_price(self, custom_price: float):
        """Sets the custom_price of this CustomizePriceResponseInvalidBundlePrices.

        in dollars  # noqa: E501

        :param custom_price: The custom_price of this CustomizePriceResponseInvalidBundlePrices.
        :type custom_price: float
        """

        self._custom_price = custom_price

    @property
    def unit_price(self) -> float:
        """Gets the unit_price of this CustomizePriceResponseInvalidBundlePrices.

        in dollars  # noqa: E501

        :return: The unit_price of this CustomizePriceResponseInvalidBundlePrices.
        :rtype: float
        """
        return self._unit_price

    @unit_price.setter
    def unit_price(self, unit_price: float):
        """Sets the unit_price of this CustomizePriceResponseInvalidBundlePrices.

        in dollars  # noqa: E501

        :param unit_price: The unit_price of this CustomizePriceResponseInvalidBundlePrices.
        :type unit_price: float
        """

        self._unit_price = unit_price

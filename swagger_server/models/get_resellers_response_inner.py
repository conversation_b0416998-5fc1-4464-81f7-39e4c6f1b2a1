# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetResellersResponseInner(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, reseller_id: str=None, reseller_name: str=None, reseller_type: str='prepaid', support_topup: bool=False, is_active: bool=False, date_created: date=None, currency_code: str='USD', balance: float=None, rate_revenue: float=None, corp_rate_revenue: float=None, contact: object=None, email_settings: object=None, image: str=None):  # noqa: E501
        """GetResellersResponseInner - a model defined in Swagger

        :param response_code: The response_code of this GetResellersResponseInner.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetResellersResponseInner.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetResellersResponseInner.  # noqa: E501
        :type title: str
        :param reseller_id: The reseller_id of this GetResellersResponseInner.  # noqa: E501
        :type reseller_id: str
        :param reseller_name: The reseller_name of this GetResellersResponseInner.  # noqa: E501
        :type reseller_name: str
        :param reseller_type: The reseller_type of this GetResellersResponseInner.  # noqa: E501
        :type reseller_type: str
        :param support_topup: The support_topup of this GetResellersResponseInner.  # noqa: E501
        :type support_topup: bool
        :param is_active: The is_active of this GetResellersResponseInner.  # noqa: E501
        :type is_active: bool
        :param date_created: The date_created of this GetResellersResponseInner.  # noqa: E501
        :type date_created: date
        :param currency_code: The currency_code of this GetResellersResponseInner.  # noqa: E501
        :type currency_code: str
        :param balance: The balance of this GetResellersResponseInner.  # noqa: E501
        :type balance: float
        :param rate_revenue: The rate_revenue of this GetResellersResponseInner.  # noqa: E501
        :type rate_revenue: float
        :param corp_rate_revenue: The corp_rate_revenue of this GetResellersResponseInner.  # noqa: E501
        :type corp_rate_revenue: float
        :param contact: The contact of this GetResellersResponseInner.  # noqa: E501
        :type contact: object
        :param email_settings: The email_settings of this GetResellersResponseInner.  # noqa: E501
        :type email_settings: object
        :param image: The image of this GetResellersResponseInner.  # noqa: E501
        :type image: str
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'reseller_id': str,
            'reseller_name': str,
            'reseller_type': str,
            'support_topup': bool,
            'is_active': bool,
            'date_created': date,
            'currency_code': str,
            'balance': float,
            'rate_revenue': float,
            'corp_rate_revenue': float,
            'contact': object,
            'email_settings': object,
            'image': str
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'reseller_id': 'reseller_id',
            'reseller_name': 'reseller_name',
            'reseller_type': 'reseller_type',
            'support_topup': 'support_topup',
            'is_active': 'is_active',
            'date_created': 'date_created',
            'currency_code': 'currency_code',
            'balance': 'balance',
            'rate_revenue': 'rate_revenue',
            'corp_rate_revenue': 'corp_rate_revenue',
            'contact': 'contact',
            'email_settings': 'email_settings',
            'image': 'image'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._reseller_id = reseller_id
        self._reseller_name = reseller_name
        self._reseller_type = reseller_type
        self._support_topup = support_topup
        self._is_active = is_active
        self._date_created = date_created
        self._currency_code = currency_code
        self._balance = balance
        self._rate_revenue = rate_revenue
        self._corp_rate_revenue = corp_rate_revenue
        self._contact = contact
        self._email_settings = email_settings
        self._image = image

    @classmethod
    def from_dict(cls, dikt) -> 'GetResellersResponseInner':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetResellersResponse_inner of this GetResellersResponseInner.  # noqa: E501
        :rtype: GetResellersResponseInner
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetResellersResponseInner.


        :return: The response_code of this GetResellersResponseInner.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetResellersResponseInner.


        :param response_code: The response_code of this GetResellersResponseInner.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetResellersResponseInner.


        :return: The developer_message of this GetResellersResponseInner.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetResellersResponseInner.


        :param developer_message: The developer_message of this GetResellersResponseInner.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetResellersResponseInner.


        :return: The title of this GetResellersResponseInner.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetResellersResponseInner.


        :param title: The title of this GetResellersResponseInner.
        :type title: str
        """

        self._title = title

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetResellersResponseInner.


        :return: The reseller_id of this GetResellersResponseInner.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetResellersResponseInner.


        :param reseller_id: The reseller_id of this GetResellersResponseInner.
        :type reseller_id: str
        """
        if reseller_id is None:
            raise ValueError("Invalid value for `reseller_id`, must not be `None`")  # noqa: E501

        self._reseller_id = reseller_id

    @property
    def reseller_name(self) -> str:
        """Gets the reseller_name of this GetResellersResponseInner.


        :return: The reseller_name of this GetResellersResponseInner.
        :rtype: str
        """
        return self._reseller_name

    @reseller_name.setter
    def reseller_name(self, reseller_name: str):
        """Sets the reseller_name of this GetResellersResponseInner.


        :param reseller_name: The reseller_name of this GetResellersResponseInner.
        :type reseller_name: str
        """
        if reseller_name is None:
            raise ValueError("Invalid value for `reseller_name`, must not be `None`")  # noqa: E501

        self._reseller_name = reseller_name

    @property
    def reseller_type(self) -> str:
        """Gets the reseller_type of this GetResellersResponseInner.


        :return: The reseller_type of this GetResellersResponseInner.
        :rtype: str
        """
        return self._reseller_type

    @reseller_type.setter
    def reseller_type(self, reseller_type: str):
        """Sets the reseller_type of this GetResellersResponseInner.


        :param reseller_type: The reseller_type of this GetResellersResponseInner.
        :type reseller_type: str
        """
        allowed_values = ["prepaid"]  # noqa: E501
        if reseller_type not in allowed_values:
            raise ValueError(
                "Invalid value for `reseller_type` ({0}), must be one of {1}"
                .format(reseller_type, allowed_values)
            )

        self._reseller_type = reseller_type

    @property
    def support_topup(self) -> bool:
        """Gets the support_topup of this GetResellersResponseInner.


        :return: The support_topup of this GetResellersResponseInner.
        :rtype: bool
        """
        return self._support_topup

    @support_topup.setter
    def support_topup(self, support_topup: bool):
        """Sets the support_topup of this GetResellersResponseInner.


        :param support_topup: The support_topup of this GetResellersResponseInner.
        :type support_topup: bool
        """
        if support_topup is None:
            raise ValueError("Invalid value for `support_topup`, must not be `None`")  # noqa: E501

        self._support_topup = support_topup

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this GetResellersResponseInner.


        :return: The is_active of this GetResellersResponseInner.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this GetResellersResponseInner.


        :param is_active: The is_active of this GetResellersResponseInner.
        :type is_active: bool
        """
        if is_active is None:
            raise ValueError("Invalid value for `is_active`, must not be `None`")  # noqa: E501

        self._is_active = is_active

    @property
    def date_created(self) -> date:
        """Gets the date_created of this GetResellersResponseInner.


        :return: The date_created of this GetResellersResponseInner.
        :rtype: date
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: date):
        """Sets the date_created of this GetResellersResponseInner.


        :param date_created: The date_created of this GetResellersResponseInner.
        :type date_created: date
        """
        if date_created is None:
            raise ValueError("Invalid value for `date_created`, must not be `None`")  # noqa: E501

        self._date_created = date_created

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this GetResellersResponseInner.


        :return: The currency_code of this GetResellersResponseInner.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this GetResellersResponseInner.


        :param currency_code: The currency_code of this GetResellersResponseInner.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

    @property
    def balance(self) -> float:
        """Gets the balance of this GetResellersResponseInner.


        :return: The balance of this GetResellersResponseInner.
        :rtype: float
        """
        return self._balance

    @balance.setter
    def balance(self, balance: float):
        """Sets the balance of this GetResellersResponseInner.


        :param balance: The balance of this GetResellersResponseInner.
        :type balance: float
        """
        if balance is None:
            raise ValueError("Invalid value for `balance`, must not be `None`")  # noqa: E501

        self._balance = balance

    @property
    def rate_revenue(self) -> float:
        """Gets the rate_revenue of this GetResellersResponseInner.

        rate revenue in percent  # noqa: E501

        :return: The rate_revenue of this GetResellersResponseInner.
        :rtype: float
        """
        return self._rate_revenue

    @rate_revenue.setter
    def rate_revenue(self, rate_revenue: float):
        """Sets the rate_revenue of this GetResellersResponseInner.

        rate revenue in percent  # noqa: E501

        :param rate_revenue: The rate_revenue of this GetResellersResponseInner.
        :type rate_revenue: float
        """
        if rate_revenue is None:
            raise ValueError("Invalid value for `rate_revenue`, must not be `None`")  # noqa: E501

        self._rate_revenue = rate_revenue

    @property
    def corp_rate_revenue(self) -> float:
        """Gets the corp_rate_revenue of this GetResellersResponseInner.

        corporate revenue in percent  # noqa: E501

        :return: The corp_rate_revenue of this GetResellersResponseInner.
        :rtype: float
        """
        return self._corp_rate_revenue

    @corp_rate_revenue.setter
    def corp_rate_revenue(self, corp_rate_revenue: float):
        """Sets the corp_rate_revenue of this GetResellersResponseInner.

        corporate revenue in percent  # noqa: E501

        :param corp_rate_revenue: The corp_rate_revenue of this GetResellersResponseInner.
        :type corp_rate_revenue: float
        """

        self._corp_rate_revenue = corp_rate_revenue

    @property
    def contact(self) -> object:
        """Gets the contact of this GetResellersResponseInner.


        :return: The contact of this GetResellersResponseInner.
        :rtype: object
        """
        return self._contact

    @contact.setter
    def contact(self, contact: object):
        """Sets the contact of this GetResellersResponseInner.


        :param contact: The contact of this GetResellersResponseInner.
        :type contact: object
        """
        if contact is None:
            raise ValueError("Invalid value for `contact`, must not be `None`")  # noqa: E501

        self._contact = contact

    @property
    def email_settings(self) -> object:
        """Gets the email_settings of this GetResellersResponseInner.


        :return: The email_settings of this GetResellersResponseInner.
        :rtype: object
        """
        return self._email_settings

    @email_settings.setter
    def email_settings(self, email_settings: object):
        """Sets the email_settings of this GetResellersResponseInner.


        :param email_settings: The email_settings of this GetResellersResponseInner.
        :type email_settings: object
        """

        self._email_settings = email_settings

    @property
    def image(self) -> str:
        """Gets the image of this GetResellersResponseInner.

        The image file of the user  # noqa: E501

        :return: The image of this GetResellersResponseInner.
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image: str):
        """Sets the image of this GetResellersResponseInner.

        The image file of the user  # noqa: E501

        :param image: The image of this GetResellersResponseInner.
        :type image: str
        """

        self._image = image

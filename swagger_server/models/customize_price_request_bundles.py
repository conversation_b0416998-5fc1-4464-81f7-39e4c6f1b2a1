# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class CustomizePriceRequestBundles(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, bundle_code: str=None, custom_price: float=None, custom_bundle_name: str=None, bundle_tag: List[str]=None, is_active: bool=None):  # noqa: E501
        """CustomizePriceRequestBundles - a model defined in Swagger

        :param bundle_code: The bundle_code of this CustomizePriceRequestBundles.  # noqa: E501
        :type bundle_code: str
        :param custom_price: The custom_price of this CustomizePriceRequestBundles.  # noqa: E501
        :type custom_price: float
        :param custom_bundle_name: The custom_bundle_name of this CustomizePriceRequestBundles.  # noqa: E501
        :type custom_bundle_name: str
        :param bundle_tag: The bundle_tag of this CustomizePriceRequestBundles.  # noqa: E501
        :type bundle_tag: List[str]
        :param is_active: The is_active of this CustomizePriceRequestBundles.  # noqa: E501
        :type is_active: bool
        """
        self.swagger_types = {
            'bundle_code': str,
            'custom_price': float,
            'custom_bundle_name': str,
            'bundle_tag': List[str],
            'is_active': bool
        }

        self.attribute_map = {
            'bundle_code': 'bundle_code',
            'custom_price': 'custom_price',
            'custom_bundle_name': 'custom_bundle_name',
            'bundle_tag': 'bundle_tag',
            'is_active': 'is_active'
        }
        self._bundle_code = bundle_code
        self._custom_price = custom_price
        self._custom_bundle_name = custom_bundle_name
        self._bundle_tag = bundle_tag
        self._is_active = is_active

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizePriceRequestBundles':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizePriceRequest_bundles of this CustomizePriceRequestBundles.  # noqa: E501
        :rtype: CustomizePriceRequestBundles
        """
        return util.deserialize_model(dikt, cls)

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this CustomizePriceRequestBundles.


        :return: The bundle_code of this CustomizePriceRequestBundles.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this CustomizePriceRequestBundles.


        :param bundle_code: The bundle_code of this CustomizePriceRequestBundles.
        :type bundle_code: str
        """
        if bundle_code is None:
            raise ValueError("Invalid value for `bundle_code`, must not be `None`")  # noqa: E501

        self._bundle_code = bundle_code

    @property
    def custom_price(self) -> float:
        """Gets the custom_price of this CustomizePriceRequestBundles.

        in dollars  # noqa: E501

        :return: The custom_price of this CustomizePriceRequestBundles.
        :rtype: float
        """
        return self._custom_price

    @custom_price.setter
    def custom_price(self, custom_price: float):
        """Sets the custom_price of this CustomizePriceRequestBundles.

        in dollars  # noqa: E501

        :param custom_price: The custom_price of this CustomizePriceRequestBundles.
        :type custom_price: float
        """

        self._custom_price = custom_price

    @property
    def custom_bundle_name(self) -> str:
        """Gets the custom_bundle_name of this CustomizePriceRequestBundles.


        :return: The custom_bundle_name of this CustomizePriceRequestBundles.
        :rtype: str
        """
        return self._custom_bundle_name

    @custom_bundle_name.setter
    def custom_bundle_name(self, custom_bundle_name: str):
        """Sets the custom_bundle_name of this CustomizePriceRequestBundles.


        :param custom_bundle_name: The custom_bundle_name of this CustomizePriceRequestBundles.
        :type custom_bundle_name: str
        """

        self._custom_bundle_name = custom_bundle_name

    @property
    def bundle_tag(self) -> List[str]:
        """Gets the bundle_tag of this CustomizePriceRequestBundles.


        :return: The bundle_tag of this CustomizePriceRequestBundles.
        :rtype: List[str]
        """
        return self._bundle_tag

    @bundle_tag.setter
    def bundle_tag(self, bundle_tag: List[str]):
        """Sets the bundle_tag of this CustomizePriceRequestBundles.


        :param bundle_tag: The bundle_tag of this CustomizePriceRequestBundles.
        :type bundle_tag: List[str]
        """

        self._bundle_tag = bundle_tag

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this CustomizePriceRequestBundles.


        :return: The is_active of this CustomizePriceRequestBundles.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this CustomizePriceRequestBundles.


        :param is_active: The is_active of this CustomizePriceRequestBundles.
        :type is_active: bool
        """

        self._is_active = is_active

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.network_list_by_regions_response_networks import NetworkListByRegionsResponseNetworks  # noqa: F401,E501
from swagger_server import util


class NetworkListByRegionsResponseCountries(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, country_name: str=None, country_code: str=None, networks: List[NetworkListByRegionsResponseNetworks]=None):  # noqa: E501
        """NetworkListByRegionsResponseCountries - a model defined in Swagger

        :param country_name: The country_name of this NetworkListByRegionsResponseCountries.  # noqa: E501
        :type country_name: str
        :param country_code: The country_code of this NetworkListByRegionsResponseCountries.  # noqa: E501
        :type country_code: str
        :param networks: The networks of this NetworkListByRegionsResponseCountries.  # noqa: E501
        :type networks: List[NetworkListByRegionsResponseNetworks]
        """
        self.swagger_types = {
            'country_name': str,
            'country_code': str,
            'networks': List[NetworkListByRegionsResponseNetworks]
        }

        self.attribute_map = {
            'country_name': 'country_name',
            'country_code': 'country_code',
            'networks': 'networks'
        }
        self._country_name = country_name
        self._country_code = country_code
        self._networks = networks

    @classmethod
    def from_dict(cls, dikt) -> 'NetworkListByRegionsResponseCountries':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The NetworkListByRegionsResponse_countries of this NetworkListByRegionsResponseCountries.  # noqa: E501
        :rtype: NetworkListByRegionsResponseCountries
        """
        return util.deserialize_model(dikt, cls)

    @property
    def country_name(self) -> str:
        """Gets the country_name of this NetworkListByRegionsResponseCountries.

        Name of the country.  # noqa: E501

        :return: The country_name of this NetworkListByRegionsResponseCountries.
        :rtype: str
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: str):
        """Sets the country_name of this NetworkListByRegionsResponseCountries.

        Name of the country.  # noqa: E501

        :param country_name: The country_name of this NetworkListByRegionsResponseCountries.
        :type country_name: str
        """

        self._country_name = country_name

    @property
    def country_code(self) -> str:
        """Gets the country_code of this NetworkListByRegionsResponseCountries.

        ISO country code.  # noqa: E501

        :return: The country_code of this NetworkListByRegionsResponseCountries.
        :rtype: str
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: str):
        """Sets the country_code of this NetworkListByRegionsResponseCountries.

        ISO country code.  # noqa: E501

        :param country_code: The country_code of this NetworkListByRegionsResponseCountries.
        :type country_code: str
        """

        self._country_code = country_code

    @property
    def networks(self) -> List[NetworkListByRegionsResponseNetworks]:
        """Gets the networks of this NetworkListByRegionsResponseCountries.

        List of networks in the country.  # noqa: E501

        :return: The networks of this NetworkListByRegionsResponseCountries.
        :rtype: List[NetworkListByRegionsResponseNetworks]
        """
        return self._networks

    @networks.setter
    def networks(self, networks: List[NetworkListByRegionsResponseNetworks]):
        """Sets the networks of this NetworkListByRegionsResponseCountries.

        List of networks in the country.  # noqa: E501

        :param networks: The networks of this NetworkListByRegionsResponseCountries.
        :type networks: List[NetworkListByRegionsResponseNetworks]
        """

        self._networks = networks

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetVoucherUseHistoryResponseData(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, voucher_use_id: str=None, reseller_id: str=None, branch_id: str=None, voucher_code: str=None, voucher_name: str=None, username: str=None, amount: float=None, date_created: datetime=None, currency_code: str=None):  # noqa: E501
        """GetVoucherUseHistoryResponseData - a model defined in Swagger

        :param voucher_use_id: The voucher_use_id of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type voucher_use_id: str
        :param reseller_id: The reseller_id of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type reseller_id: str
        :param branch_id: The branch_id of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type branch_id: str
        :param voucher_code: The voucher_code of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type voucher_code: str
        :param voucher_name: The voucher_name of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type voucher_name: str
        :param username: The username of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type username: str
        :param amount: The amount of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type amount: float
        :param date_created: The date_created of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type date_created: datetime
        :param currency_code: The currency_code of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :type currency_code: str
        """
        self.swagger_types = {
            'voucher_use_id': str,
            'reseller_id': str,
            'branch_id': str,
            'voucher_code': str,
            'voucher_name': str,
            'username': str,
            'amount': float,
            'date_created': datetime,
            'currency_code': str
        }

        self.attribute_map = {
            'voucher_use_id': 'voucher_use_id',
            'reseller_id': 'reseller_id',
            'branch_id': 'branch_id',
            'voucher_code': 'voucher_code',
            'voucher_name': 'voucher_name',
            'username': 'username',
            'amount': 'amount',
            'date_created': 'date_created',
            'currency_code': 'currency_code'
        }
        self._voucher_use_id = voucher_use_id
        self._reseller_id = reseller_id
        self._branch_id = branch_id
        self._voucher_code = voucher_code
        self._voucher_name = voucher_name
        self._username = username
        self._amount = amount
        self._date_created = date_created
        self._currency_code = currency_code

    @classmethod
    def from_dict(cls, dikt) -> 'GetVoucherUseHistoryResponseData':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetVoucherUseHistoryResponse_data of this GetVoucherUseHistoryResponseData.  # noqa: E501
        :rtype: GetVoucherUseHistoryResponseData
        """
        return util.deserialize_model(dikt, cls)

    @property
    def voucher_use_id(self) -> str:
        """Gets the voucher_use_id of this GetVoucherUseHistoryResponseData.


        :return: The voucher_use_id of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._voucher_use_id

    @voucher_use_id.setter
    def voucher_use_id(self, voucher_use_id: str):
        """Sets the voucher_use_id of this GetVoucherUseHistoryResponseData.


        :param voucher_use_id: The voucher_use_id of this GetVoucherUseHistoryResponseData.
        :type voucher_use_id: str
        """

        self._voucher_use_id = voucher_use_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetVoucherUseHistoryResponseData.


        :return: The reseller_id of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetVoucherUseHistoryResponseData.


        :param reseller_id: The reseller_id of this GetVoucherUseHistoryResponseData.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this GetVoucherUseHistoryResponseData.


        :return: The branch_id of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this GetVoucherUseHistoryResponseData.


        :param branch_id: The branch_id of this GetVoucherUseHistoryResponseData.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def voucher_code(self) -> str:
        """Gets the voucher_code of this GetVoucherUseHistoryResponseData.


        :return: The voucher_code of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._voucher_code

    @voucher_code.setter
    def voucher_code(self, voucher_code: str):
        """Sets the voucher_code of this GetVoucherUseHistoryResponseData.


        :param voucher_code: The voucher_code of this GetVoucherUseHistoryResponseData.
        :type voucher_code: str
        """

        self._voucher_code = voucher_code

    @property
    def voucher_name(self) -> str:
        """Gets the voucher_name of this GetVoucherUseHistoryResponseData.


        :return: The voucher_name of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._voucher_name

    @voucher_name.setter
    def voucher_name(self, voucher_name: str):
        """Sets the voucher_name of this GetVoucherUseHistoryResponseData.


        :param voucher_name: The voucher_name of this GetVoucherUseHistoryResponseData.
        :type voucher_name: str
        """

        self._voucher_name = voucher_name

    @property
    def username(self) -> str:
        """Gets the username of this GetVoucherUseHistoryResponseData.


        :return: The username of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username: str):
        """Sets the username of this GetVoucherUseHistoryResponseData.


        :param username: The username of this GetVoucherUseHistoryResponseData.
        :type username: str
        """

        self._username = username

    @property
    def amount(self) -> float:
        """Gets the amount of this GetVoucherUseHistoryResponseData.


        :return: The amount of this GetVoucherUseHistoryResponseData.
        :rtype: float
        """
        return self._amount

    @amount.setter
    def amount(self, amount: float):
        """Sets the amount of this GetVoucherUseHistoryResponseData.


        :param amount: The amount of this GetVoucherUseHistoryResponseData.
        :type amount: float
        """

        self._amount = amount

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetVoucherUseHistoryResponseData.


        :return: The date_created of this GetVoucherUseHistoryResponseData.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetVoucherUseHistoryResponseData.


        :param date_created: The date_created of this GetVoucherUseHistoryResponseData.
        :type date_created: datetime
        """

        self._date_created = date_created

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this GetVoucherUseHistoryResponseData.


        :return: The currency_code of this GetVoucherUseHistoryResponseData.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this GetVoucherUseHistoryResponseData.


        :param currency_code: The currency_code of this GetVoucherUseHistoryResponseData.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

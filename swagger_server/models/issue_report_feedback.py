# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class IssueReportFeedback(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, satisfaction: int=None, comments: str=None):  # noqa: E501
        """IssueReportFeedback - a model defined in Swagger

        :param satisfaction: The satisfaction of this IssueReportFeedback.  # noqa: E501
        :type satisfaction: int
        :param comments: The comments of this IssueReportFeedback.  # noqa: E501
        :type comments: str
        """
        self.swagger_types = {
            'satisfaction': int,
            'comments': str
        }

        self.attribute_map = {
            'satisfaction': 'satisfaction',
            'comments': 'comments'
        }
        self._satisfaction = satisfaction
        self._comments = comments

    @classmethod
    def from_dict(cls, dikt) -> 'IssueReportFeedback':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The IssueReport_feedback of this IssueReportFeedback.  # noqa: E501
        :rtype: IssueReportFeedback
        """
        return util.deserialize_model(dikt, cls)

    @property
    def satisfaction(self) -> int:
        """Gets the satisfaction of this IssueReportFeedback.

        User satisfaction rating (e.g., 1-5, with 5 being the highest).  # noqa: E501

        :return: The satisfaction of this IssueReportFeedback.
        :rtype: int
        """
        return self._satisfaction

    @satisfaction.setter
    def satisfaction(self, satisfaction: int):
        """Sets the satisfaction of this IssueReportFeedback.

        User satisfaction rating (e.g., 1-5, with 5 being the highest).  # noqa: E501

        :param satisfaction: The satisfaction of this IssueReportFeedback.
        :type satisfaction: int
        """
        if satisfaction is None:
            raise ValueError("Invalid value for `satisfaction`, must not be `None`")  # noqa: E501

        self._satisfaction = satisfaction

    @property
    def comments(self) -> str:
        """Gets the comments of this IssueReportFeedback.

        Additional comments on the resolution process.  # noqa: E501

        :return: The comments of this IssueReportFeedback.
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments: str):
        """Sets the comments of this IssueReportFeedback.

        Additional comments on the resolution process.  # noqa: E501

        :param comments: The comments of this IssueReportFeedback.
        :type comments: str
        """

        self._comments = comments

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetAgentsResponseAgents(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reseller_id: str=None, branch_id: str=None, agent_id: str=None, email: str=None, username: str=None, name: str=None, is_active: bool=False, date_created: datetime=None, role_name: str=None):  # noqa: E501
        """GetAgentsResponseAgents - a model defined in Swagger

        :param reseller_id: The reseller_id of this GetAgentsResponseAgents.  # noqa: E501
        :type reseller_id: str
        :param branch_id: The branch_id of this GetAgentsResponseAgents.  # noqa: E501
        :type branch_id: str
        :param agent_id: The agent_id of this GetAgentsResponseAgents.  # noqa: E501
        :type agent_id: str
        :param email: The email of this GetAgentsResponseAgents.  # noqa: E501
        :type email: str
        :param username: The username of this GetAgentsResponseAgents.  # noqa: E501
        :type username: str
        :param name: The name of this GetAgentsResponseAgents.  # noqa: E501
        :type name: str
        :param is_active: The is_active of this GetAgentsResponseAgents.  # noqa: E501
        :type is_active: bool
        :param date_created: The date_created of this GetAgentsResponseAgents.  # noqa: E501
        :type date_created: datetime
        :param role_name: The role_name of this GetAgentsResponseAgents.  # noqa: E501
        :type role_name: str
        """
        self.swagger_types = {
            'reseller_id': str,
            'branch_id': str,
            'agent_id': str,
            'email': str,
            'username': str,
            'name': str,
            'is_active': bool,
            'date_created': datetime,
            'role_name': str
        }

        self.attribute_map = {
            'reseller_id': 'reseller_id',
            'branch_id': 'branch_id',
            'agent_id': 'agent_id',
            'email': 'email',
            'username': 'username',
            'name': 'name',
            'is_active': 'is_active',
            'date_created': 'date_created',
            'role_name': 'role_name'
        }
        self._reseller_id = reseller_id
        self._branch_id = branch_id
        self._agent_id = agent_id
        self._email = email
        self._username = username
        self._name = name
        self._is_active = is_active
        self._date_created = date_created
        self._role_name = role_name

    @classmethod
    def from_dict(cls, dikt) -> 'GetAgentsResponseAgents':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetAgentsResponse_agents of this GetAgentsResponseAgents.  # noqa: E501
        :rtype: GetAgentsResponseAgents
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetAgentsResponseAgents.


        :return: The reseller_id of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetAgentsResponseAgents.


        :param reseller_id: The reseller_id of this GetAgentsResponseAgents.
        :type reseller_id: str
        """
        if reseller_id is None:
            raise ValueError("Invalid value for `reseller_id`, must not be `None`")  # noqa: E501

        self._reseller_id = reseller_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this GetAgentsResponseAgents.


        :return: The branch_id of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this GetAgentsResponseAgents.


        :param branch_id: The branch_id of this GetAgentsResponseAgents.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def agent_id(self) -> str:
        """Gets the agent_id of this GetAgentsResponseAgents.


        :return: The agent_id of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id: str):
        """Sets the agent_id of this GetAgentsResponseAgents.


        :param agent_id: The agent_id of this GetAgentsResponseAgents.
        :type agent_id: str
        """
        if agent_id is None:
            raise ValueError("Invalid value for `agent_id`, must not be `None`")  # noqa: E501

        self._agent_id = agent_id

    @property
    def email(self) -> str:
        """Gets the email of this GetAgentsResponseAgents.


        :return: The email of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email: str):
        """Sets the email of this GetAgentsResponseAgents.


        :param email: The email of this GetAgentsResponseAgents.
        :type email: str
        """
        if email is None:
            raise ValueError("Invalid value for `email`, must not be `None`")  # noqa: E501

        self._email = email

    @property
    def username(self) -> str:
        """Gets the username of this GetAgentsResponseAgents.


        :return: The username of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username: str):
        """Sets the username of this GetAgentsResponseAgents.


        :param username: The username of this GetAgentsResponseAgents.
        :type username: str
        """
        if username is None:
            raise ValueError("Invalid value for `username`, must not be `None`")  # noqa: E501

        self._username = username

    @property
    def name(self) -> str:
        """Gets the name of this GetAgentsResponseAgents.


        :return: The name of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name: str):
        """Sets the name of this GetAgentsResponseAgents.


        :param name: The name of this GetAgentsResponseAgents.
        :type name: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this GetAgentsResponseAgents.


        :return: The is_active of this GetAgentsResponseAgents.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this GetAgentsResponseAgents.


        :param is_active: The is_active of this GetAgentsResponseAgents.
        :type is_active: bool
        """
        if is_active is None:
            raise ValueError("Invalid value for `is_active`, must not be `None`")  # noqa: E501

        self._is_active = is_active

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetAgentsResponseAgents.


        :return: The date_created of this GetAgentsResponseAgents.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetAgentsResponseAgents.


        :param date_created: The date_created of this GetAgentsResponseAgents.
        :type date_created: datetime
        """
        if date_created is None:
            raise ValueError("Invalid value for `date_created`, must not be `None`")  # noqa: E501

        self._date_created = date_created

    @property
    def role_name(self) -> str:
        """Gets the role_name of this GetAgentsResponseAgents.


        :return: The role_name of this GetAgentsResponseAgents.
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name: str):
        """Sets the role_name of this GetAgentsResponseAgents.


        :param role_name: The role_name of this GetAgentsResponseAgents.
        :type role_name: str
        """
        if role_name is None:
            raise ValueError("Invalid value for `role_name`, must not be `None`")  # noqa: E501

        self._role_name = role_name

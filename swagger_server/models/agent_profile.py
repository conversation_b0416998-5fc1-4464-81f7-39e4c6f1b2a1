# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_resellers_response_resellers import GetResellersResponseResellers  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class AgentProfile(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reseller: GetResellersResponseResellers=None, agent_id: str=None, branch_id: str=None, email: str=None, username: str=None, name: str=None, is_active: bool=False, date_created: datetime=None, role_id: str=None):  # noqa: E501
        """AgentProfile - a model defined in Swagger

        :param reseller: The reseller of this AgentProfile.  # noqa: E501
        :type reseller: GetResellersResponseResellers
        :param agent_id: The agent_id of this AgentProfile.  # noqa: E501
        :type agent_id: str
        :param branch_id: The branch_id of this AgentProfile.  # noqa: E501
        :type branch_id: str
        :param email: The email of this AgentProfile.  # noqa: E501
        :type email: str
        :param username: The username of this AgentProfile.  # noqa: E501
        :type username: str
        :param name: The name of this AgentProfile.  # noqa: E501
        :type name: str
        :param is_active: The is_active of this AgentProfile.  # noqa: E501
        :type is_active: bool
        :param date_created: The date_created of this AgentProfile.  # noqa: E501
        :type date_created: datetime
        :param role_id: The role_id of this AgentProfile.  # noqa: E501
        :type role_id: str
        """
        self.swagger_types = {
            'reseller': GetResellersResponseResellers,
            'agent_id': str,
            'branch_id': str,
            'email': str,
            'username': str,
            'name': str,
            'is_active': bool,
            'date_created': datetime,
            'role_id': str
        }

        self.attribute_map = {
            'reseller': 'reseller',
            'agent_id': 'agent_id',
            'branch_id': 'branch_id',
            'email': 'email',
            'username': 'username',
            'name': 'name',
            'is_active': 'is_active',
            'date_created': 'date_created',
            'role_id': 'role_id'
        }
        self._reseller = reseller
        self._agent_id = agent_id
        self._branch_id = branch_id
        self._email = email
        self._username = username
        self._name = name
        self._is_active = is_active
        self._date_created = date_created
        self._role_id = role_id

    @classmethod
    def from_dict(cls, dikt) -> 'AgentProfile':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The AgentProfile of this AgentProfile.  # noqa: E501
        :rtype: AgentProfile
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reseller(self) -> GetResellersResponseResellers:
        """Gets the reseller of this AgentProfile.


        :return: The reseller of this AgentProfile.
        :rtype: GetResellersResponseResellers
        """
        return self._reseller

    @reseller.setter
    def reseller(self, reseller: GetResellersResponseResellers):
        """Sets the reseller of this AgentProfile.


        :param reseller: The reseller of this AgentProfile.
        :type reseller: GetResellersResponseResellers
        """

        self._reseller = reseller

    @property
    def agent_id(self) -> str:
        """Gets the agent_id of this AgentProfile.


        :return: The agent_id of this AgentProfile.
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id: str):
        """Sets the agent_id of this AgentProfile.


        :param agent_id: The agent_id of this AgentProfile.
        :type agent_id: str
        """
        if agent_id is None:
            raise ValueError("Invalid value for `agent_id`, must not be `None`")  # noqa: E501

        self._agent_id = agent_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this AgentProfile.


        :return: The branch_id of this AgentProfile.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this AgentProfile.


        :param branch_id: The branch_id of this AgentProfile.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def email(self) -> str:
        """Gets the email of this AgentProfile.


        :return: The email of this AgentProfile.
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email: str):
        """Sets the email of this AgentProfile.


        :param email: The email of this AgentProfile.
        :type email: str
        """
        if email is None:
            raise ValueError("Invalid value for `email`, must not be `None`")  # noqa: E501

        self._email = email

    @property
    def username(self) -> str:
        """Gets the username of this AgentProfile.


        :return: The username of this AgentProfile.
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username: str):
        """Sets the username of this AgentProfile.


        :param username: The username of this AgentProfile.
        :type username: str
        """
        if username is None:
            raise ValueError("Invalid value for `username`, must not be `None`")  # noqa: E501

        self._username = username

    @property
    def name(self) -> str:
        """Gets the name of this AgentProfile.


        :return: The name of this AgentProfile.
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name: str):
        """Sets the name of this AgentProfile.


        :param name: The name of this AgentProfile.
        :type name: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this AgentProfile.


        :return: The is_active of this AgentProfile.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this AgentProfile.


        :param is_active: The is_active of this AgentProfile.
        :type is_active: bool
        """
        if is_active is None:
            raise ValueError("Invalid value for `is_active`, must not be `None`")  # noqa: E501

        self._is_active = is_active

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this AgentProfile.


        :return: The date_created of this AgentProfile.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this AgentProfile.


        :param date_created: The date_created of this AgentProfile.
        :type date_created: datetime
        """
        if date_created is None:
            raise ValueError("Invalid value for `date_created`, must not be `None`")  # noqa: E501

        self._date_created = date_created

    @property
    def role_id(self) -> str:
        """Gets the role_id of this AgentProfile.


        :return: The role_id of this AgentProfile.
        :rtype: str
        """
        return self._role_id

    @role_id.setter
    def role_id(self, role_id: str):
        """Sets the role_id of this AgentProfile.


        :param role_id: The role_id of this AgentProfile.
        :type role_id: str
        """
        if role_id is None:
            raise ValueError("Invalid value for `role_id`, must not be `None`")  # noqa: E501

        self._role_id = role_id

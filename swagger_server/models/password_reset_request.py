# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class PasswordResetRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, password_reset_identifier: str=None, password: str=None, password_confirmation: str=None):  # noqa: E501
        """PasswordResetRequest - a model defined in Swagger

        :param password_reset_identifier: The password_reset_identifier of this PasswordResetRequest.  # noqa: E501
        :type password_reset_identifier: str
        :param password: The password of this PasswordResetRequest.  # noqa: E501
        :type password: str
        :param password_confirmation: The password_confirmation of this PasswordResetRequest.  # noqa: E501
        :type password_confirmation: str
        """
        self.swagger_types = {
            'password_reset_identifier': str,
            'password': str,
            'password_confirmation': str
        }

        self.attribute_map = {
            'password_reset_identifier': 'password_reset_identifier',
            'password': 'password',
            'password_confirmation': 'password_confirmation'
        }
        self._password_reset_identifier = password_reset_identifier
        self._password = password
        self._password_confirmation = password_confirmation

    @classmethod
    def from_dict(cls, dikt) -> 'PasswordResetRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The PasswordResetRequest of this PasswordResetRequest.  # noqa: E501
        :rtype: PasswordResetRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def password_reset_identifier(self) -> str:
        """Gets the password_reset_identifier of this PasswordResetRequest.


        :return: The password_reset_identifier of this PasswordResetRequest.
        :rtype: str
        """
        return self._password_reset_identifier

    @password_reset_identifier.setter
    def password_reset_identifier(self, password_reset_identifier: str):
        """Sets the password_reset_identifier of this PasswordResetRequest.


        :param password_reset_identifier: The password_reset_identifier of this PasswordResetRequest.
        :type password_reset_identifier: str
        """
        if password_reset_identifier is None:
            raise ValueError("Invalid value for `password_reset_identifier`, must not be `None`")  # noqa: E501

        self._password_reset_identifier = password_reset_identifier

    @property
    def password(self) -> str:
        """Gets the password of this PasswordResetRequest.


        :return: The password of this PasswordResetRequest.
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password: str):
        """Sets the password of this PasswordResetRequest.


        :param password: The password of this PasswordResetRequest.
        :type password: str
        """
        if password is None:
            raise ValueError("Invalid value for `password`, must not be `None`")  # noqa: E501

        self._password = password

    @property
    def password_confirmation(self) -> str:
        """Gets the password_confirmation of this PasswordResetRequest.


        :return: The password_confirmation of this PasswordResetRequest.
        :rtype: str
        """
        return self._password_confirmation

    @password_confirmation.setter
    def password_confirmation(self, password_confirmation: str):
        """Sets the password_confirmation of this PasswordResetRequest.


        :param password_confirmation: The password_confirmation of this PasswordResetRequest.
        :type password_confirmation: str
        """

        self._password_confirmation = password_confirmation

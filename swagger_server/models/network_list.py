# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.network_list_networks import NetworkListNetworks  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class NetworkList(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, networks: List[NetworkListNetworks]=None, networks_count: int=None):  # noqa: E501
        """NetworkList - a model defined in Swagger

        :param response_code: The response_code of this NetworkList.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this NetworkList.  # noqa: E501
        :type developer_message: str
        :param title: The title of this NetworkList.  # noqa: E501
        :type title: str
        :param networks: The networks of this NetworkList.  # noqa: E501
        :type networks: List[NetworkListNetworks]
        :param networks_count: The networks_count of this NetworkList.  # noqa: E501
        :type networks_count: int
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'networks': List[NetworkListNetworks],
            'networks_count': int
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'networks': 'networks',
            'networks_count': 'networks_count'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._networks = networks
        self._networks_count = networks_count

    @classmethod
    def from_dict(cls, dikt) -> 'NetworkList':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The NetworkList of this NetworkList.  # noqa: E501
        :rtype: NetworkList
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this NetworkList.


        :return: The response_code of this NetworkList.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this NetworkList.


        :param response_code: The response_code of this NetworkList.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this NetworkList.


        :return: The developer_message of this NetworkList.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this NetworkList.


        :param developer_message: The developer_message of this NetworkList.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this NetworkList.


        :return: The title of this NetworkList.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this NetworkList.


        :param title: The title of this NetworkList.
        :type title: str
        """

        self._title = title

    @property
    def networks(self) -> List[NetworkListNetworks]:
        """Gets the networks of this NetworkList.


        :return: The networks of this NetworkList.
        :rtype: List[NetworkListNetworks]
        """
        return self._networks

    @networks.setter
    def networks(self, networks: List[NetworkListNetworks]):
        """Sets the networks of this NetworkList.


        :param networks: The networks of this NetworkList.
        :type networks: List[NetworkListNetworks]
        """
        if networks is None:
            raise ValueError("Invalid value for `networks`, must not be `None`")  # noqa: E501

        self._networks = networks

    @property
    def networks_count(self) -> int:
        """Gets the networks_count of this NetworkList.


        :return: The networks_count of this NetworkList.
        :rtype: int
        """
        return self._networks_count

    @networks_count.setter
    def networks_count(self, networks_count: int):
        """Sets the networks_count of this NetworkList.


        :param networks_count: The networks_count of this NetworkList.
        :type networks_count: int
        """

        self._networks_count = networks_count

# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.customize_price_request_bundles import CustomizePriceRequestBundles  # noqa: F401,E501
from swagger_server import util


class CustomizePriceRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reset_customizations: bool=None, set_selling_revenue: float=None, bundles: List[CustomizePriceRequestBundles]=None):  # noqa: E501
        """CustomizePriceRequest - a model defined in Swagger

        :param reset_customizations: The reset_customizations of this CustomizePriceRequest.  # noqa: E501
        :type reset_customizations: bool
        :param set_selling_revenue: The set_selling_revenue of this CustomizePriceRequest.  # noqa: E501
        :type set_selling_revenue: float
        :param bundles: The bundles of this CustomizePriceRequest.  # noqa: E501
        :type bundles: List[CustomizePriceRequestBundles]
        """
        self.swagger_types = {
            'reset_customizations': bool,
            'set_selling_revenue': float,
            'bundles': List[CustomizePriceRequestBundles]
        }

        self.attribute_map = {
            'reset_customizations': 'reset_customizations',
            'set_selling_revenue': 'set_selling_revenue',
            'bundles': 'bundles'
        }
        self._reset_customizations = reset_customizations
        self._set_selling_revenue = set_selling_revenue
        self._bundles = bundles

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizePriceRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizePriceRequest of this CustomizePriceRequest.  # noqa: E501
        :rtype: CustomizePriceRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reset_customizations(self) -> bool:
        """Gets the reset_customizations of this CustomizePriceRequest.

        if true, all custom prices will be reset and the rate revenue will be activated.  # noqa: E501

        :return: The reset_customizations of this CustomizePriceRequest.
        :rtype: bool
        """
        return self._reset_customizations

    @reset_customizations.setter
    def reset_customizations(self, reset_customizations: bool):
        """Sets the reset_customizations of this CustomizePriceRequest.

        if true, all custom prices will be reset and the rate revenue will be activated.  # noqa: E501

        :param reset_customizations: The reset_customizations of this CustomizePriceRequest.
        :type reset_customizations: bool
        """

        self._reset_customizations = reset_customizations

    @property
    def set_selling_revenue(self) -> float:
        """Gets the set_selling_revenue of this CustomizePriceRequest.

        percentage  # noqa: E501

        :return: The set_selling_revenue of this CustomizePriceRequest.
        :rtype: float
        """
        return self._set_selling_revenue

    @set_selling_revenue.setter
    def set_selling_revenue(self, set_selling_revenue: float):
        """Sets the set_selling_revenue of this CustomizePriceRequest.

        percentage  # noqa: E501

        :param set_selling_revenue: The set_selling_revenue of this CustomizePriceRequest.
        :type set_selling_revenue: float
        """

        self._set_selling_revenue = set_selling_revenue

    @property
    def bundles(self) -> List[CustomizePriceRequestBundles]:
        """Gets the bundles of this CustomizePriceRequest.


        :return: The bundles of this CustomizePriceRequest.
        :rtype: List[CustomizePriceRequestBundles]
        """
        return self._bundles

    @bundles.setter
    def bundles(self, bundles: List[CustomizePriceRequestBundles]):
        """Sets the bundles of this CustomizePriceRequest.


        :param bundles: The bundles of this CustomizePriceRequest.
        :type bundles: List[CustomizePriceRequestBundles]
        """

        self._bundles = bundles

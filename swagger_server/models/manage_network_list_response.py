# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.manage_network_list_response_invalid_networks import ManageNetworkListResponseInvalidNetworks  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class ManageNetworkListResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, message: str=None, detail: str=None, invalid_networks: List[ManageNetworkListResponseInvalidNetworks]=None):  # noqa: E501
        """ManageNetworkListResponse - a model defined in Swagger

        :param response_code: The response_code of this ManageNetworkListResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this ManageNetworkListResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this ManageNetworkListResponse.  # noqa: E501
        :type title: str
        :param message: The message of this ManageNetworkListResponse.  # noqa: E501
        :type message: str
        :param detail: The detail of this ManageNetworkListResponse.  # noqa: E501
        :type detail: str
        :param invalid_networks: The invalid_networks of this ManageNetworkListResponse.  # noqa: E501
        :type invalid_networks: List[ManageNetworkListResponseInvalidNetworks]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'message': str,
            'detail': str,
            'invalid_networks': List[ManageNetworkListResponseInvalidNetworks]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'message': 'message',
            'detail': 'detail',
            'invalid_networks': 'invalid_networks'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._message = message
        self._detail = detail
        self._invalid_networks = invalid_networks

    @classmethod
    def from_dict(cls, dikt) -> 'ManageNetworkListResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ManageNetworkListResponse of this ManageNetworkListResponse.  # noqa: E501
        :rtype: ManageNetworkListResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this ManageNetworkListResponse.


        :return: The response_code of this ManageNetworkListResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this ManageNetworkListResponse.


        :param response_code: The response_code of this ManageNetworkListResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this ManageNetworkListResponse.


        :return: The developer_message of this ManageNetworkListResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this ManageNetworkListResponse.


        :param developer_message: The developer_message of this ManageNetworkListResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this ManageNetworkListResponse.


        :return: The title of this ManageNetworkListResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this ManageNetworkListResponse.


        :param title: The title of this ManageNetworkListResponse.
        :type title: str
        """

        self._title = title

    @property
    def message(self) -> str:
        """Gets the message of this ManageNetworkListResponse.


        :return: The message of this ManageNetworkListResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this ManageNetworkListResponse.


        :param message: The message of this ManageNetworkListResponse.
        :type message: str
        """

        self._message = message

    @property
    def detail(self) -> str:
        """Gets the detail of this ManageNetworkListResponse.


        :return: The detail of this ManageNetworkListResponse.
        :rtype: str
        """
        return self._detail

    @detail.setter
    def detail(self, detail: str):
        """Sets the detail of this ManageNetworkListResponse.


        :param detail: The detail of this ManageNetworkListResponse.
        :type detail: str
        """

        self._detail = detail

    @property
    def invalid_networks(self) -> List[ManageNetworkListResponseInvalidNetworks]:
        """Gets the invalid_networks of this ManageNetworkListResponse.


        :return: The invalid_networks of this ManageNetworkListResponse.
        :rtype: List[ManageNetworkListResponseInvalidNetworks]
        """
        return self._invalid_networks

    @invalid_networks.setter
    def invalid_networks(self, invalid_networks: List[ManageNetworkListResponseInvalidNetworks]):
        """Sets the invalid_networks of this ManageNetworkListResponse.


        :param invalid_networks: The invalid_networks of this ManageNetworkListResponse.
        :type invalid_networks: List[ManageNetworkListResponseInvalidNetworks]
        """

        self._invalid_networks = invalid_networks

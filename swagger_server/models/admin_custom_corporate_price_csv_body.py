# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class AdminCustomCorporatePriceCsvBody(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, file: str=None):  # noqa: E501
        """AdminCustomCorporatePriceCsvBody - a model defined in Swagger

        :param file: The file of this AdminCustomCorporatePriceCsvBody.  # noqa: E501
        :type file: str
        """
        self.swagger_types = {
            'file': str
        }

        self.attribute_map = {
            'file': 'file'
        }
        self._file = file

    @classmethod
    def from_dict(cls, dikt) -> 'AdminCustomCorporatePriceCsvBody':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The Admin_custom_corporate_price_csv_body of this AdminCustomCorporatePriceCsvBody.  # noqa: E501
        :rtype: AdminCustomCorporatePriceCsvBody
        """
        return util.deserialize_model(dikt, cls)

    @property
    def file(self) -> str:
        """Gets the file of this AdminCustomCorporatePriceCsvBody.


        :return: The file of this AdminCustomCorporatePriceCsvBody.
        :rtype: str
        """
        return self._file

    @file.setter
    def file(self, file: str):
        """Sets the file of this AdminCustomCorporatePriceCsvBody.


        :param file: The file of this AdminCustomCorporatePriceCsvBody.
        :type file: str
        """
        if file is None:
            raise ValueError("Invalid value for `file`, must not be `None`")  # noqa: E501

        self._file = file

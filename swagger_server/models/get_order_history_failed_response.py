# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetOrderHistoryFailedResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None, reseller_id: str=None, branch_id: str=None, order_status: str=None, bundle_code: str=None, bundle_marketing_name: str=None, bundle_name: str=None, bundle_category: str=None, country_code: List[str]=None, country_name: List[str]=None, bundle_price: float=None, bundle_price_in_additional_currency: float=None, bundle_retail_price: float=None, bundle_retail_price_in_additional_currency: float=None, currency_code: str=None, additional_currency_code: str=None, client_name: str=None, client_email: str=None, remaining_wallet_balance: float=None, remaining_wallet_balance_in_additional_currency: float=None, date_created: datetime=None, order_reference: str=None, otp: str=None, order_type: str=None, whatsapp_number: str=None, topup_an_expired_plan: bool=None, has_related_active_topups: bool=None):  # noqa: E501
        """GetOrderHistoryFailedResponse - a model defined in Swagger

        :param order_id: The order_id of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type order_id: str
        :param reseller_id: The reseller_id of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type reseller_id: str
        :param branch_id: The branch_id of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type branch_id: str
        :param order_status: The order_status of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type order_status: str
        :param bundle_code: The bundle_code of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_code: str
        :param bundle_marketing_name: The bundle_marketing_name of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_marketing_name: str
        :param bundle_name: The bundle_name of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_name: str
        :param bundle_category: The bundle_category of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_category: str
        :param country_code: The country_code of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type country_code: List[str]
        :param country_name: The country_name of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type country_name: List[str]
        :param bundle_price: The bundle_price of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_price: float
        :param bundle_price_in_additional_currency: The bundle_price_in_additional_currency of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_price_in_additional_currency: float
        :param bundle_retail_price: The bundle_retail_price of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_retail_price: float
        :param bundle_retail_price_in_additional_currency: The bundle_retail_price_in_additional_currency of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type bundle_retail_price_in_additional_currency: float
        :param currency_code: The currency_code of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type currency_code: str
        :param additional_currency_code: The additional_currency_code of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type additional_currency_code: str
        :param client_name: The client_name of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type client_name: str
        :param client_email: The client_email of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type client_email: str
        :param remaining_wallet_balance: The remaining_wallet_balance of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type remaining_wallet_balance: float
        :param remaining_wallet_balance_in_additional_currency: The remaining_wallet_balance_in_additional_currency of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type remaining_wallet_balance_in_additional_currency: float
        :param date_created: The date_created of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type date_created: datetime
        :param order_reference: The order_reference of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type order_reference: str
        :param otp: The otp of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type otp: str
        :param order_type: The order_type of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type order_type: str
        :param whatsapp_number: The whatsapp_number of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type whatsapp_number: str
        :param topup_an_expired_plan: The topup_an_expired_plan of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type topup_an_expired_plan: bool
        :param has_related_active_topups: The has_related_active_topups of this GetOrderHistoryFailedResponse.  # noqa: E501
        :type has_related_active_topups: bool
        """
        self.swagger_types = {
            'order_id': str,
            'reseller_id': str,
            'branch_id': str,
            'order_status': str,
            'bundle_code': str,
            'bundle_marketing_name': str,
            'bundle_name': str,
            'bundle_category': str,
            'country_code': List[str],
            'country_name': List[str],
            'bundle_price': float,
            'bundle_price_in_additional_currency': float,
            'bundle_retail_price': float,
            'bundle_retail_price_in_additional_currency': float,
            'currency_code': str,
            'additional_currency_code': str,
            'client_name': str,
            'client_email': str,
            'remaining_wallet_balance': float,
            'remaining_wallet_balance_in_additional_currency': float,
            'date_created': datetime,
            'order_reference': str,
            'otp': str,
            'order_type': str,
            'whatsapp_number': str,
            'topup_an_expired_plan': bool,
            'has_related_active_topups': bool
        }

        self.attribute_map = {
            'order_id': 'order_id',
            'reseller_id': 'reseller_id',
            'branch_id': 'branch_id',
            'order_status': 'order_status',
            'bundle_code': 'bundle_code',
            'bundle_marketing_name': 'bundle_marketing_name',
            'bundle_name': 'bundle_name',
            'bundle_category': 'bundle_category',
            'country_code': 'country_code',
            'country_name': 'country_name',
            'bundle_price': 'bundle_price',
            'bundle_price_in_additional_currency': 'bundle_price_in_additional_currency',
            'bundle_retail_price': 'bundle_retail_price',
            'bundle_retail_price_in_additional_currency': 'bundle_retail_price_in_additional_currency',
            'currency_code': 'currency_code',
            'additional_currency_code': 'additional_currency_code',
            'client_name': 'client_name',
            'client_email': 'client_email',
            'remaining_wallet_balance': 'remaining_wallet_balance',
            'remaining_wallet_balance_in_additional_currency': 'remaining_wallet_balance_in_additional_currency',
            'date_created': 'date_created',
            'order_reference': 'order_reference',
            'otp': 'otp',
            'order_type': 'order_type',
            'whatsapp_number': 'whatsapp_number',
            'topup_an_expired_plan': 'topup_an_expired_plan',
            'has_related_active_topups': 'has_related_active_topups'
        }
        self._order_id = order_id
        self._reseller_id = reseller_id
        self._branch_id = branch_id
        self._order_status = order_status
        self._bundle_code = bundle_code
        self._bundle_marketing_name = bundle_marketing_name
        self._bundle_name = bundle_name
        self._bundle_category = bundle_category
        self._country_code = country_code
        self._country_name = country_name
        self._bundle_price = bundle_price
        self._bundle_price_in_additional_currency = bundle_price_in_additional_currency
        self._bundle_retail_price = bundle_retail_price
        self._bundle_retail_price_in_additional_currency = bundle_retail_price_in_additional_currency
        self._currency_code = currency_code
        self._additional_currency_code = additional_currency_code
        self._client_name = client_name
        self._client_email = client_email
        self._remaining_wallet_balance = remaining_wallet_balance
        self._remaining_wallet_balance_in_additional_currency = remaining_wallet_balance_in_additional_currency
        self._date_created = date_created
        self._order_reference = order_reference
        self._otp = otp
        self._order_type = order_type
        self._whatsapp_number = whatsapp_number
        self._topup_an_expired_plan = topup_an_expired_plan
        self._has_related_active_topups = has_related_active_topups

    @classmethod
    def from_dict(cls, dikt) -> 'GetOrderHistoryFailedResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetOrderHistoryFailedResponse of this GetOrderHistoryFailedResponse.  # noqa: E501
        :rtype: GetOrderHistoryFailedResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this GetOrderHistoryFailedResponse.


        :return: The order_id of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this GetOrderHistoryFailedResponse.


        :param order_id: The order_id of this GetOrderHistoryFailedResponse.
        :type order_id: str
        """

        self._order_id = order_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetOrderHistoryFailedResponse.


        :return: The reseller_id of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetOrderHistoryFailedResponse.


        :param reseller_id: The reseller_id of this GetOrderHistoryFailedResponse.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this GetOrderHistoryFailedResponse.


        :return: The branch_id of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this GetOrderHistoryFailedResponse.


        :param branch_id: The branch_id of this GetOrderHistoryFailedResponse.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def order_status(self) -> str:
        """Gets the order_status of this GetOrderHistoryFailedResponse.


        :return: The order_status of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._order_status

    @order_status.setter
    def order_status(self, order_status: str):
        """Sets the order_status of this GetOrderHistoryFailedResponse.


        :param order_status: The order_status of this GetOrderHistoryFailedResponse.
        :type order_status: str
        """

        self._order_status = order_status

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this GetOrderHistoryFailedResponse.


        :return: The bundle_code of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this GetOrderHistoryFailedResponse.


        :param bundle_code: The bundle_code of this GetOrderHistoryFailedResponse.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def bundle_marketing_name(self) -> str:
        """Gets the bundle_marketing_name of this GetOrderHistoryFailedResponse.


        :return: The bundle_marketing_name of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._bundle_marketing_name

    @bundle_marketing_name.setter
    def bundle_marketing_name(self, bundle_marketing_name: str):
        """Sets the bundle_marketing_name of this GetOrderHistoryFailedResponse.


        :param bundle_marketing_name: The bundle_marketing_name of this GetOrderHistoryFailedResponse.
        :type bundle_marketing_name: str
        """

        self._bundle_marketing_name = bundle_marketing_name

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this GetOrderHistoryFailedResponse.


        :return: The bundle_name of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this GetOrderHistoryFailedResponse.


        :param bundle_name: The bundle_name of this GetOrderHistoryFailedResponse.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def bundle_category(self) -> str:
        """Gets the bundle_category of this GetOrderHistoryFailedResponse.


        :return: The bundle_category of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._bundle_category

    @bundle_category.setter
    def bundle_category(self, bundle_category: str):
        """Sets the bundle_category of this GetOrderHistoryFailedResponse.


        :param bundle_category: The bundle_category of this GetOrderHistoryFailedResponse.
        :type bundle_category: str
        """
        allowed_values = ["country", "global", "region", "cruise"]  # noqa: E501
        if bundle_category not in allowed_values:
            raise ValueError(
                "Invalid value for `bundle_category` ({0}), must be one of {1}"
                .format(bundle_category, allowed_values)
            )

        self._bundle_category = bundle_category

    @property
    def country_code(self) -> List[str]:
        """Gets the country_code of this GetOrderHistoryFailedResponse.


        :return: The country_code of this GetOrderHistoryFailedResponse.
        :rtype: List[str]
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: List[str]):
        """Sets the country_code of this GetOrderHistoryFailedResponse.


        :param country_code: The country_code of this GetOrderHistoryFailedResponse.
        :type country_code: List[str]
        """

        self._country_code = country_code

    @property
    def country_name(self) -> List[str]:
        """Gets the country_name of this GetOrderHistoryFailedResponse.


        :return: The country_name of this GetOrderHistoryFailedResponse.
        :rtype: List[str]
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: List[str]):
        """Sets the country_name of this GetOrderHistoryFailedResponse.


        :param country_name: The country_name of this GetOrderHistoryFailedResponse.
        :type country_name: List[str]
        """

        self._country_name = country_name

    @property
    def bundle_price(self) -> float:
        """Gets the bundle_price of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The bundle_price of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._bundle_price

    @bundle_price.setter
    def bundle_price(self, bundle_price: float):
        """Sets the bundle_price of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param bundle_price: The bundle_price of this GetOrderHistoryFailedResponse.
        :type bundle_price: float
        """

        self._bundle_price = bundle_price

    @property
    def bundle_price_in_additional_currency(self) -> float:
        """Gets the bundle_price_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The bundle_price_in_additional_currency of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._bundle_price_in_additional_currency

    @bundle_price_in_additional_currency.setter
    def bundle_price_in_additional_currency(self, bundle_price_in_additional_currency: float):
        """Sets the bundle_price_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param bundle_price_in_additional_currency: The bundle_price_in_additional_currency of this GetOrderHistoryFailedResponse.
        :type bundle_price_in_additional_currency: float
        """

        self._bundle_price_in_additional_currency = bundle_price_in_additional_currency

    @property
    def bundle_retail_price(self) -> float:
        """Gets the bundle_retail_price of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The bundle_retail_price of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._bundle_retail_price

    @bundle_retail_price.setter
    def bundle_retail_price(self, bundle_retail_price: float):
        """Sets the bundle_retail_price of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param bundle_retail_price: The bundle_retail_price of this GetOrderHistoryFailedResponse.
        :type bundle_retail_price: float
        """

        self._bundle_retail_price = bundle_retail_price

    @property
    def bundle_retail_price_in_additional_currency(self) -> float:
        """Gets the bundle_retail_price_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The bundle_retail_price_in_additional_currency of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._bundle_retail_price_in_additional_currency

    @bundle_retail_price_in_additional_currency.setter
    def bundle_retail_price_in_additional_currency(self, bundle_retail_price_in_additional_currency: float):
        """Sets the bundle_retail_price_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param bundle_retail_price_in_additional_currency: The bundle_retail_price_in_additional_currency of this GetOrderHistoryFailedResponse.
        :type bundle_retail_price_in_additional_currency: float
        """

        self._bundle_retail_price_in_additional_currency = bundle_retail_price_in_additional_currency

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this GetOrderHistoryFailedResponse.


        :return: The currency_code of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this GetOrderHistoryFailedResponse.


        :param currency_code: The currency_code of this GetOrderHistoryFailedResponse.
        :type currency_code: str
        """

        self._currency_code = currency_code

    @property
    def additional_currency_code(self) -> str:
        """Gets the additional_currency_code of this GetOrderHistoryFailedResponse.


        :return: The additional_currency_code of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._additional_currency_code

    @additional_currency_code.setter
    def additional_currency_code(self, additional_currency_code: str):
        """Sets the additional_currency_code of this GetOrderHistoryFailedResponse.


        :param additional_currency_code: The additional_currency_code of this GetOrderHistoryFailedResponse.
        :type additional_currency_code: str
        """

        self._additional_currency_code = additional_currency_code

    @property
    def client_name(self) -> str:
        """Gets the client_name of this GetOrderHistoryFailedResponse.


        :return: The client_name of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._client_name

    @client_name.setter
    def client_name(self, client_name: str):
        """Sets the client_name of this GetOrderHistoryFailedResponse.


        :param client_name: The client_name of this GetOrderHistoryFailedResponse.
        :type client_name: str
        """

        self._client_name = client_name

    @property
    def client_email(self) -> str:
        """Gets the client_email of this GetOrderHistoryFailedResponse.


        :return: The client_email of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._client_email

    @client_email.setter
    def client_email(self, client_email: str):
        """Sets the client_email of this GetOrderHistoryFailedResponse.


        :param client_email: The client_email of this GetOrderHistoryFailedResponse.
        :type client_email: str
        """

        self._client_email = client_email

    @property
    def remaining_wallet_balance(self) -> float:
        """Gets the remaining_wallet_balance of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The remaining_wallet_balance of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._remaining_wallet_balance

    @remaining_wallet_balance.setter
    def remaining_wallet_balance(self, remaining_wallet_balance: float):
        """Sets the remaining_wallet_balance of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param remaining_wallet_balance: The remaining_wallet_balance of this GetOrderHistoryFailedResponse.
        :type remaining_wallet_balance: float
        """

        self._remaining_wallet_balance = remaining_wallet_balance

    @property
    def remaining_wallet_balance_in_additional_currency(self) -> float:
        """Gets the remaining_wallet_balance_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :return: The remaining_wallet_balance_in_additional_currency of this GetOrderHistoryFailedResponse.
        :rtype: float
        """
        return self._remaining_wallet_balance_in_additional_currency

    @remaining_wallet_balance_in_additional_currency.setter
    def remaining_wallet_balance_in_additional_currency(self, remaining_wallet_balance_in_additional_currency: float):
        """Sets the remaining_wallet_balance_in_additional_currency of this GetOrderHistoryFailedResponse.

        in dollars  # noqa: E501

        :param remaining_wallet_balance_in_additional_currency: The remaining_wallet_balance_in_additional_currency of this GetOrderHistoryFailedResponse.
        :type remaining_wallet_balance_in_additional_currency: float
        """

        self._remaining_wallet_balance_in_additional_currency = remaining_wallet_balance_in_additional_currency

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetOrderHistoryFailedResponse.


        :return: The date_created of this GetOrderHistoryFailedResponse.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetOrderHistoryFailedResponse.


        :param date_created: The date_created of this GetOrderHistoryFailedResponse.
        :type date_created: datetime
        """

        self._date_created = date_created

    @property
    def order_reference(self) -> str:
        """Gets the order_reference of this GetOrderHistoryFailedResponse.


        :return: The order_reference of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._order_reference

    @order_reference.setter
    def order_reference(self, order_reference: str):
        """Sets the order_reference of this GetOrderHistoryFailedResponse.


        :param order_reference: The order_reference of this GetOrderHistoryFailedResponse.
        :type order_reference: str
        """

        self._order_reference = order_reference

    @property
    def otp(self) -> str:
        """Gets the otp of this GetOrderHistoryFailedResponse.


        :return: The otp of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._otp

    @otp.setter
    def otp(self, otp: str):
        """Sets the otp of this GetOrderHistoryFailedResponse.


        :param otp: The otp of this GetOrderHistoryFailedResponse.
        :type otp: str
        """

        self._otp = otp

    @property
    def order_type(self) -> str:
        """Gets the order_type of this GetOrderHistoryFailedResponse.


        :return: The order_type of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._order_type

    @order_type.setter
    def order_type(self, order_type: str):
        """Sets the order_type of this GetOrderHistoryFailedResponse.


        :param order_type: The order_type of this GetOrderHistoryFailedResponse.
        :type order_type: str
        """

        self._order_type = order_type

    @property
    def whatsapp_number(self) -> str:
        """Gets the whatsapp_number of this GetOrderHistoryFailedResponse.


        :return: The whatsapp_number of this GetOrderHistoryFailedResponse.
        :rtype: str
        """
        return self._whatsapp_number

    @whatsapp_number.setter
    def whatsapp_number(self, whatsapp_number: str):
        """Sets the whatsapp_number of this GetOrderHistoryFailedResponse.


        :param whatsapp_number: The whatsapp_number of this GetOrderHistoryFailedResponse.
        :type whatsapp_number: str
        """

        self._whatsapp_number = whatsapp_number

    @property
    def topup_an_expired_plan(self) -> bool:
        """Gets the topup_an_expired_plan of this GetOrderHistoryFailedResponse.


        :return: The topup_an_expired_plan of this GetOrderHistoryFailedResponse.
        :rtype: bool
        """
        return self._topup_an_expired_plan

    @topup_an_expired_plan.setter
    def topup_an_expired_plan(self, topup_an_expired_plan: bool):
        """Sets the topup_an_expired_plan of this GetOrderHistoryFailedResponse.


        :param topup_an_expired_plan: The topup_an_expired_plan of this GetOrderHistoryFailedResponse.
        :type topup_an_expired_plan: bool
        """

        self._topup_an_expired_plan = topup_an_expired_plan

    @property
    def has_related_active_topups(self) -> bool:
        """Gets the has_related_active_topups of this GetOrderHistoryFailedResponse.


        :return: The has_related_active_topups of this GetOrderHistoryFailedResponse.
        :rtype: bool
        """
        return self._has_related_active_topups

    @has_related_active_topups.setter
    def has_related_active_topups(self, has_related_active_topups: bool):
        """Sets the has_related_active_topups of this GetOrderHistoryFailedResponse.


        :param has_related_active_topups: The has_related_active_topups of this GetOrderHistoryFailedResponse.
        :type has_related_active_topups: bool
        """

        self._has_related_active_topups = has_related_active_topups

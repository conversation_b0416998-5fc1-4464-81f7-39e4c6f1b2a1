# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.network_list_by_regions_countries import NetworkListByRegionsCountries  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class NetworkListByRegions(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, region_code: str=None, region_name: str=None, countries: List[NetworkListByRegionsCountries]=None, networks_count: int=None):  # noqa: E501
        """NetworkListByRegions - a model defined in Swagger

        :param response_code: The response_code of this NetworkListByRegions.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this NetworkListByRegions.  # noqa: E501
        :type developer_message: str
        :param title: The title of this NetworkListByRegions.  # noqa: E501
        :type title: str
        :param region_code: The region_code of this NetworkListByRegions.  # noqa: E501
        :type region_code: str
        :param region_name: The region_name of this NetworkListByRegions.  # noqa: E501
        :type region_name: str
        :param countries: The countries of this NetworkListByRegions.  # noqa: E501
        :type countries: List[NetworkListByRegionsCountries]
        :param networks_count: The networks_count of this NetworkListByRegions.  # noqa: E501
        :type networks_count: int
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'region_code': str,
            'region_name': str,
            'countries': List[NetworkListByRegionsCountries],
            'networks_count': int
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'region_code': 'region_code',
            'region_name': 'region_name',
            'countries': 'countries',
            'networks_count': 'networks_count'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._region_code = region_code
        self._region_name = region_name
        self._countries = countries
        self._networks_count = networks_count

    @classmethod
    def from_dict(cls, dikt) -> 'NetworkListByRegions':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The NetworkListByRegions of this NetworkListByRegions.  # noqa: E501
        :rtype: NetworkListByRegions
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this NetworkListByRegions.


        :return: The response_code of this NetworkListByRegions.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this NetworkListByRegions.


        :param response_code: The response_code of this NetworkListByRegions.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this NetworkListByRegions.


        :return: The developer_message of this NetworkListByRegions.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this NetworkListByRegions.


        :param developer_message: The developer_message of this NetworkListByRegions.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this NetworkListByRegions.


        :return: The title of this NetworkListByRegions.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this NetworkListByRegions.


        :param title: The title of this NetworkListByRegions.
        :type title: str
        """

        self._title = title

    @property
    def region_code(self) -> str:
        """Gets the region_code of this NetworkListByRegions.

        Unique identifier for the region.  # noqa: E501

        :return: The region_code of this NetworkListByRegions.
        :rtype: str
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code: str):
        """Sets the region_code of this NetworkListByRegions.

        Unique identifier for the region.  # noqa: E501

        :param region_code: The region_code of this NetworkListByRegions.
        :type region_code: str
        """

        self._region_code = region_code

    @property
    def region_name(self) -> str:
        """Gets the region_name of this NetworkListByRegions.

        Name of the region.  # noqa: E501

        :return: The region_name of this NetworkListByRegions.
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name: str):
        """Sets the region_name of this NetworkListByRegions.

        Name of the region.  # noqa: E501

        :param region_name: The region_name of this NetworkListByRegions.
        :type region_name: str
        """

        self._region_name = region_name

    @property
    def countries(self) -> List[NetworkListByRegionsCountries]:
        """Gets the countries of this NetworkListByRegions.

        List of countries in the region.  # noqa: E501

        :return: The countries of this NetworkListByRegions.
        :rtype: List[NetworkListByRegionsCountries]
        """
        return self._countries

    @countries.setter
    def countries(self, countries: List[NetworkListByRegionsCountries]):
        """Sets the countries of this NetworkListByRegions.

        List of countries in the region.  # noqa: E501

        :param countries: The countries of this NetworkListByRegions.
        :type countries: List[NetworkListByRegionsCountries]
        """

        self._countries = countries

    @property
    def networks_count(self) -> int:
        """Gets the networks_count of this NetworkListByRegions.


        :return: The networks_count of this NetworkListByRegions.
        :rtype: int
        """
        return self._networks_count

    @networks_count.setter
    def networks_count(self, networks_count: int):
        """Sets the networks_count of this NetworkListByRegions.


        :param networks_count: The networks_count of this NetworkListByRegions.
        :type networks_count: int
        """

        self._networks_count = networks_count

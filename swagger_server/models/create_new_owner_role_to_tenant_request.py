# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class CreateNewOwnerRoleToTenantRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, tenant_id: str=None):  # noqa: E501
        """CreateNewOwnerRoleToTenantRequest - a model defined in Swagger

        :param tenant_id: The tenant_id of this CreateNewOwnerRoleToTenantRequest.  # noqa: E501
        :type tenant_id: str
        """
        self.swagger_types = {
            'tenant_id': str
        }

        self.attribute_map = {
            'tenant_id': 'TenantID'
        }
        self._tenant_id = tenant_id

    @classmethod
    def from_dict(cls, dikt) -> 'CreateNewOwnerRoleToTenantRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CreateNewOwnerRoleToTenantRequest of this CreateNewOwnerRoleToTenantRequest.  # noqa: E501
        :rtype: CreateNewOwnerRoleToTenantRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def tenant_id(self) -> str:
        """Gets the tenant_id of this CreateNewOwnerRoleToTenantRequest.


        :return: The tenant_id of this CreateNewOwnerRoleToTenantRequest.
        :rtype: str
        """
        return self._tenant_id

    @tenant_id.setter
    def tenant_id(self, tenant_id: str):
        """Sets the tenant_id of this CreateNewOwnerRoleToTenantRequest.


        :param tenant_id: The tenant_id of this CreateNewOwnerRoleToTenantRequest.
        :type tenant_id: str
        """

        self._tenant_id = tenant_id

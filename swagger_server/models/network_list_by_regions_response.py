# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.network_list_by_regions_response_networks1 import NetworkListByRegionsResponseNetworks1  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class NetworkListByRegionsResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, networks_count: int=None, networks: List[NetworkListByRegionsResponseNetworks1]=None):  # noqa: E501
        """NetworkListByRegionsResponse - a model defined in Swagger

        :param response_code: The response_code of this NetworkListByRegionsResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this NetworkListByRegionsResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this NetworkListByRegionsResponse.  # noqa: E501
        :type title: str
        :param networks_count: The networks_count of this NetworkListByRegionsResponse.  # noqa: E501
        :type networks_count: int
        :param networks: The networks of this NetworkListByRegionsResponse.  # noqa: E501
        :type networks: List[NetworkListByRegionsResponseNetworks1]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'networks_count': int,
            'networks': List[NetworkListByRegionsResponseNetworks1]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'networks_count': 'networks_count',
            'networks': 'networks'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._networks_count = networks_count
        self._networks = networks

    @classmethod
    def from_dict(cls, dikt) -> 'NetworkListByRegionsResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The NetworkListByRegionsResponse of this NetworkListByRegionsResponse.  # noqa: E501
        :rtype: NetworkListByRegionsResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this NetworkListByRegionsResponse.


        :return: The response_code of this NetworkListByRegionsResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this NetworkListByRegionsResponse.


        :param response_code: The response_code of this NetworkListByRegionsResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this NetworkListByRegionsResponse.


        :return: The developer_message of this NetworkListByRegionsResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this NetworkListByRegionsResponse.


        :param developer_message: The developer_message of this NetworkListByRegionsResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this NetworkListByRegionsResponse.


        :return: The title of this NetworkListByRegionsResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this NetworkListByRegionsResponse.


        :param title: The title of this NetworkListByRegionsResponse.
        :type title: str
        """

        self._title = title

    @property
    def networks_count(self) -> int:
        """Gets the networks_count of this NetworkListByRegionsResponse.


        :return: The networks_count of this NetworkListByRegionsResponse.
        :rtype: int
        """
        return self._networks_count

    @networks_count.setter
    def networks_count(self, networks_count: int):
        """Sets the networks_count of this NetworkListByRegionsResponse.


        :param networks_count: The networks_count of this NetworkListByRegionsResponse.
        :type networks_count: int
        """

        self._networks_count = networks_count

    @property
    def networks(self) -> List[NetworkListByRegionsResponseNetworks1]:
        """Gets the networks of this NetworkListByRegionsResponse.


        :return: The networks of this NetworkListByRegionsResponse.
        :rtype: List[NetworkListByRegionsResponseNetworks1]
        """
        return self._networks

    @networks.setter
    def networks(self, networks: List[NetworkListByRegionsResponseNetworks1]):
        """Sets the networks of this NetworkListByRegionsResponse.


        :param networks: The networks of this NetworkListByRegionsResponse.
        :type networks: List[NetworkListByRegionsResponseNetworks1]
        """

        self._networks = networks

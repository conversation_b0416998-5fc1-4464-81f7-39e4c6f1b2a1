# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class ResellerScopedBundle(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, bundle_id: str=None, bundle_name: str=None, supported_countries: List[str]=None, region: str=None, data_size: str=None, data_unit: str=None, validity_days: int=None, price: float=None):  # noqa: E501
        """ResellerScopedBundle - a model defined in Swagger

        :param bundle_id: The bundle_id of this ResellerScopedBundle.  # noqa: E501
        :type bundle_id: str
        :param bundle_name: The bundle_name of this ResellerScopedBundle.  # noqa: E501
        :type bundle_name: str
        :param supported_countries: The supported_countries of this ResellerScopedBundle.  # noqa: E501
        :type supported_countries: List[str]
        :param region: The region of this ResellerScopedBundle.  # noqa: E501
        :type region: str
        :param data_size: The data_size of this ResellerScopedBundle.  # noqa: E501
        :type data_size: str
        :param data_unit: The data_unit of this ResellerScopedBundle.  # noqa: E501
        :type data_unit: str
        :param validity_days: The validity_days of this ResellerScopedBundle.  # noqa: E501
        :type validity_days: int
        :param price: The price of this ResellerScopedBundle.  # noqa: E501
        :type price: float
        """
        self.swagger_types = {
            'bundle_id': str,
            'bundle_name': str,
            'supported_countries': List[str],
            'region': str,
            'data_size': str,
            'data_unit': str,
            'validity_days': int,
            'price': float
        }

        self.attribute_map = {
            'bundle_id': 'bundle_id',
            'bundle_name': 'bundle_name',
            'supported_countries': 'supported_countries',
            'region': 'region',
            'data_size': 'data_size',
            'data_unit': 'data_unit',
            'validity_days': 'validity_days',
            'price': 'price'
        }
        self._bundle_id = bundle_id
        self._bundle_name = bundle_name
        self._supported_countries = supported_countries
        self._region = region
        self._data_size = data_size
        self._data_unit = data_unit
        self._validity_days = validity_days
        self._price = price

    @classmethod
    def from_dict(cls, dikt) -> 'ResellerScopedBundle':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The ResellerScopedBundle of this ResellerScopedBundle.  # noqa: E501
        :rtype: ResellerScopedBundle
        """
        return util.deserialize_model(dikt, cls)

    @property
    def bundle_id(self) -> str:
        """Gets the bundle_id of this ResellerScopedBundle.


        :return: The bundle_id of this ResellerScopedBundle.
        :rtype: str
        """
        return self._bundle_id

    @bundle_id.setter
    def bundle_id(self, bundle_id: str):
        """Sets the bundle_id of this ResellerScopedBundle.


        :param bundle_id: The bundle_id of this ResellerScopedBundle.
        :type bundle_id: str
        """

        self._bundle_id = bundle_id

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this ResellerScopedBundle.


        :return: The bundle_name of this ResellerScopedBundle.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this ResellerScopedBundle.


        :param bundle_name: The bundle_name of this ResellerScopedBundle.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def supported_countries(self) -> List[str]:
        """Gets the supported_countries of this ResellerScopedBundle.


        :return: The supported_countries of this ResellerScopedBundle.
        :rtype: List[str]
        """
        return self._supported_countries

    @supported_countries.setter
    def supported_countries(self, supported_countries: List[str]):
        """Sets the supported_countries of this ResellerScopedBundle.


        :param supported_countries: The supported_countries of this ResellerScopedBundle.
        :type supported_countries: List[str]
        """

        self._supported_countries = supported_countries

    @property
    def region(self) -> str:
        """Gets the region of this ResellerScopedBundle.


        :return: The region of this ResellerScopedBundle.
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region: str):
        """Sets the region of this ResellerScopedBundle.


        :param region: The region of this ResellerScopedBundle.
        :type region: str
        """

        self._region = region

    @property
    def data_size(self) -> str:
        """Gets the data_size of this ResellerScopedBundle.


        :return: The data_size of this ResellerScopedBundle.
        :rtype: str
        """
        return self._data_size

    @data_size.setter
    def data_size(self, data_size: str):
        """Sets the data_size of this ResellerScopedBundle.


        :param data_size: The data_size of this ResellerScopedBundle.
        :type data_size: str
        """

        self._data_size = data_size

    @property
    def data_unit(self) -> str:
        """Gets the data_unit of this ResellerScopedBundle.


        :return: The data_unit of this ResellerScopedBundle.
        :rtype: str
        """
        return self._data_unit

    @data_unit.setter
    def data_unit(self, data_unit: str):
        """Sets the data_unit of this ResellerScopedBundle.


        :param data_unit: The data_unit of this ResellerScopedBundle.
        :type data_unit: str
        """

        self._data_unit = data_unit

    @property
    def validity_days(self) -> int:
        """Gets the validity_days of this ResellerScopedBundle.


        :return: The validity_days of this ResellerScopedBundle.
        :rtype: int
        """
        return self._validity_days

    @validity_days.setter
    def validity_days(self, validity_days: int):
        """Sets the validity_days of this ResellerScopedBundle.


        :param validity_days: The validity_days of this ResellerScopedBundle.
        :type validity_days: int
        """

        self._validity_days = validity_days

    @property
    def price(self) -> float:
        """Gets the price of this ResellerScopedBundle.


        :return: The price of this ResellerScopedBundle.
        :rtype: float
        """
        return self._price

    @price.setter
    def price(self, price: float):
        """Sets the price of this ResellerScopedBundle.


        :param price: The price of this ResellerScopedBundle.
        :type price: float
        """

        self._price = price

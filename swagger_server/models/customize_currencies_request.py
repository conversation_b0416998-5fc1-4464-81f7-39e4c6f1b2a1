# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.customize_currencies_request_currencies import CustomizeCurrenciesRequestCurrencies  # noqa: F401,E501
from swagger_server import util


class CustomizeCurrenciesRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, currencies: List[CustomizeCurrenciesRequestCurrencies]=None):  # noqa: E501
        """CustomizeCurrenciesRequest - a model defined in Swagger

        :param currencies: The currencies of this CustomizeCurrenciesRequest.  # noqa: E501
        :type currencies: List[CustomizeCurrenciesRequestCurrencies]
        """
        self.swagger_types = {
            'currencies': List[CustomizeCurrenciesRequestCurrencies]
        }

        self.attribute_map = {
            'currencies': 'currencies'
        }
        self._currencies = currencies

    @classmethod
    def from_dict(cls, dikt) -> 'CustomizeCurrenciesRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CustomizeCurrenciesRequest of this CustomizeCurrenciesRequest.  # noqa: E501
        :rtype: CustomizeCurrenciesRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def currencies(self) -> List[CustomizeCurrenciesRequestCurrencies]:
        """Gets the currencies of this CustomizeCurrenciesRequest.


        :return: The currencies of this CustomizeCurrenciesRequest.
        :rtype: List[CustomizeCurrenciesRequestCurrencies]
        """
        return self._currencies

    @currencies.setter
    def currencies(self, currencies: List[CustomizeCurrenciesRequestCurrencies]):
        """Sets the currencies of this CustomizeCurrenciesRequest.


        :param currencies: The currencies of this CustomizeCurrenciesRequest.
        :type currencies: List[CustomizeCurrenciesRequestCurrencies]
        """

        self._currencies = currencies

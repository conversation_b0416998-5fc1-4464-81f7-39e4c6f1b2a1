# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.edit_reseller_request_email_settings import EditResellerRequestEmailSettings  # noqa: F401,E501
from swagger_server.models.reseller_contact import ResellerContact  # noqa: F401,E501
from swagger_server.models.reseller_data_consumption_email import ResellerDataConsumptionEmail  # noqa: F401,E501
from swagger_server.models.reseller_data_expired_email import ResellerDataExpiredEmail  # noqa: F401,E501
from swagger_server.models.reseller_qr_code_email import ResellerQrCodeEmail  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class EditResellerRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, reseller_name: str=None, balance_warning_limit: float=None, reseller_type: str='prepaid', callback_url: str=None, consumption_url: str=None, default_currency_code: str='USD', support_topup: bool=False, supports_promo: bool=None, supports_vouchers: bool=None, is_active: bool=False, currency_code: str='USD', voucher_rate: float=None, contact: ResellerContact=None, email_settings: EditResellerRequestEmailSettings=None, custom_email_template_qr: str=None, custom_email_template_data: str=None, custom_email_template_expired: str=None, image: str=None, is_whitelabel: bool=False, tenant_name: str=None, reseller_category: str=None, active_vendors_list: List[str]=None, vendors_for_balance_deduction_list: List[str]=None, request_custom_email: bool=True, data_consumption_email: ResellerDataConsumptionEmail=None, data_expired_email: ResellerDataExpiredEmail=None, qr_code_email: ResellerQrCodeEmail=None, notification_type: str='webhook', retry_on_failed_after: int=1):  # noqa: E501
        """EditResellerRequest - a model defined in Swagger

        :param reseller_name: The reseller_name of this EditResellerRequest.  # noqa: E501
        :type reseller_name: str
        :param balance_warning_limit: The balance_warning_limit of this EditResellerRequest.  # noqa: E501
        :type balance_warning_limit: float
        :param reseller_type: The reseller_type of this EditResellerRequest.  # noqa: E501
        :type reseller_type: str
        :param callback_url: The callback_url of this EditResellerRequest.  # noqa: E501
        :type callback_url: str
        :param consumption_url: The consumption_url of this EditResellerRequest.  # noqa: E501
        :type consumption_url: str
        :param default_currency_code: The default_currency_code of this EditResellerRequest.  # noqa: E501
        :type default_currency_code: str
        :param support_topup: The support_topup of this EditResellerRequest.  # noqa: E501
        :type support_topup: bool
        :param supports_promo: The supports_promo of this EditResellerRequest.  # noqa: E501
        :type supports_promo: bool
        :param supports_vouchers: The supports_vouchers of this EditResellerRequest.  # noqa: E501
        :type supports_vouchers: bool
        :param is_active: The is_active of this EditResellerRequest.  # noqa: E501
        :type is_active: bool
        :param currency_code: The currency_code of this EditResellerRequest.  # noqa: E501
        :type currency_code: str
        :param voucher_rate: The voucher_rate of this EditResellerRequest.  # noqa: E501
        :type voucher_rate: float
        :param contact: The contact of this EditResellerRequest.  # noqa: E501
        :type contact: ResellerContact
        :param email_settings: The email_settings of this EditResellerRequest.  # noqa: E501
        :type email_settings: EditResellerRequestEmailSettings
        :param custom_email_template_qr: The custom_email_template_qr of this EditResellerRequest.  # noqa: E501
        :type custom_email_template_qr: str
        :param custom_email_template_data: The custom_email_template_data of this EditResellerRequest.  # noqa: E501
        :type custom_email_template_data: str
        :param custom_email_template_expired: The custom_email_template_expired of this EditResellerRequest.  # noqa: E501
        :type custom_email_template_expired: str
        :param image: The image of this EditResellerRequest.  # noqa: E501
        :type image: str
        :param is_whitelabel: The is_whitelabel of this EditResellerRequest.  # noqa: E501
        :type is_whitelabel: bool
        :param tenant_name: The tenant_name of this EditResellerRequest.  # noqa: E501
        :type tenant_name: str
        :param reseller_category: The reseller_category of this EditResellerRequest.  # noqa: E501
        :type reseller_category: str
        :param active_vendors_list: The active_vendors_list of this EditResellerRequest.  # noqa: E501
        :type active_vendors_list: List[str]
        :param vendors_for_balance_deduction_list: The vendors_for_balance_deduction_list of this EditResellerRequest.  # noqa: E501
        :type vendors_for_balance_deduction_list: List[str]
        :param request_custom_email: The request_custom_email of this EditResellerRequest.  # noqa: E501
        :type request_custom_email: bool
        :param data_consumption_email: The data_consumption_email of this EditResellerRequest.  # noqa: E501
        :type data_consumption_email: ResellerDataConsumptionEmail
        :param data_expired_email: The data_expired_email of this EditResellerRequest.  # noqa: E501
        :type data_expired_email: ResellerDataExpiredEmail
        :param qr_code_email: The qr_code_email of this EditResellerRequest.  # noqa: E501
        :type qr_code_email: ResellerQrCodeEmail
        :param notification_type: The notification_type of this EditResellerRequest.  # noqa: E501
        :type notification_type: str
        :param retry_on_failed_after: The retry_on_failed_after of this EditResellerRequest.  # noqa: E501
        :type retry_on_failed_after: int
        """
        self.swagger_types = {
            'reseller_name': str,
            'balance_warning_limit': float,
            'reseller_type': str,
            'callback_url': str,
            'consumption_url': str,
            'default_currency_code': str,
            'support_topup': bool,
            'supports_promo': bool,
            'supports_vouchers': bool,
            'is_active': bool,
            'currency_code': str,
            'voucher_rate': float,
            'contact': ResellerContact,
            'email_settings': EditResellerRequestEmailSettings,
            'custom_email_template_qr': str,
            'custom_email_template_data': str,
            'custom_email_template_expired': str,
            'image': str,
            'is_whitelabel': bool,
            'tenant_name': str,
            'reseller_category': str,
            'active_vendors_list': List[str],
            'vendors_for_balance_deduction_list': List[str],
            'request_custom_email': bool,
            'data_consumption_email': ResellerDataConsumptionEmail,
            'data_expired_email': ResellerDataExpiredEmail,
            'qr_code_email': ResellerQrCodeEmail,
            'notification_type': str,
            'retry_on_failed_after': int
        }

        self.attribute_map = {
            'reseller_name': 'reseller_name',
            'balance_warning_limit': 'balance_warning_limit',
            'reseller_type': 'reseller_type',
            'callback_url': 'callback_url',
            'consumption_url': 'consumption_url',
            'default_currency_code': 'default_currency_code',
            'support_topup': 'support_topup',
            'supports_promo': 'supports_promo',
            'supports_vouchers': 'supports_vouchers',
            'is_active': 'is_active',
            'currency_code': 'currency_code',
            'voucher_rate': 'voucher_rate',
            'contact': 'contact',
            'email_settings': 'email_settings',
            'custom_email_template_qr': 'custom_email_template_qr',
            'custom_email_template_data': 'custom_email_template_data',
            'custom_email_template_expired': 'custom_email_template_expired',
            'image': 'image',
            'is_whitelabel': 'is_whitelabel',
            'tenant_name': 'tenant_name',
            'reseller_category': 'reseller_category',
            'active_vendors_list': 'active_vendors_list',
            'vendors_for_balance_deduction_list': 'vendors_for_balance_deduction_list',
            'request_custom_email': 'request_custom_email',
            'data_consumption_email': 'data_consumption_email',
            'data_expired_email': 'data_expired_email',
            'qr_code_email': 'qr_code_email',
            'notification_type': 'notification_type',
            'retry_on_failed_after': 'retry_on_failed_after'
        }
        self._reseller_name = reseller_name
        self._balance_warning_limit = balance_warning_limit
        self._reseller_type = reseller_type
        self._callback_url = callback_url
        self._consumption_url = consumption_url
        self._default_currency_code = default_currency_code
        self._support_topup = support_topup
        self._supports_promo = supports_promo
        self._supports_vouchers = supports_vouchers
        self._is_active = is_active
        self._currency_code = currency_code
        self._voucher_rate = voucher_rate
        self._contact = contact
        self._email_settings = email_settings
        self._custom_email_template_qr = custom_email_template_qr
        self._custom_email_template_data = custom_email_template_data
        self._custom_email_template_expired = custom_email_template_expired
        self._image = image
        self._is_whitelabel = is_whitelabel
        self._tenant_name = tenant_name
        self._reseller_category = reseller_category
        self._active_vendors_list = active_vendors_list
        self._vendors_for_balance_deduction_list = vendors_for_balance_deduction_list
        self._request_custom_email = request_custom_email
        self._data_consumption_email = data_consumption_email
        self._data_expired_email = data_expired_email
        self._qr_code_email = qr_code_email
        self._notification_type = notification_type
        self._retry_on_failed_after = retry_on_failed_after

    @classmethod
    def from_dict(cls, dikt) -> 'EditResellerRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The EditResellerRequest of this EditResellerRequest.  # noqa: E501
        :rtype: EditResellerRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def reseller_name(self) -> str:
        """Gets the reseller_name of this EditResellerRequest.


        :return: The reseller_name of this EditResellerRequest.
        :rtype: str
        """
        return self._reseller_name

    @reseller_name.setter
    def reseller_name(self, reseller_name: str):
        """Sets the reseller_name of this EditResellerRequest.


        :param reseller_name: The reseller_name of this EditResellerRequest.
        :type reseller_name: str
        """

        self._reseller_name = reseller_name

    @property
    def balance_warning_limit(self) -> float:
        """Gets the balance_warning_limit of this EditResellerRequest.

        in dollars  # noqa: E501

        :return: The balance_warning_limit of this EditResellerRequest.
        :rtype: float
        """
        return self._balance_warning_limit

    @balance_warning_limit.setter
    def balance_warning_limit(self, balance_warning_limit: float):
        """Sets the balance_warning_limit of this EditResellerRequest.

        in dollars  # noqa: E501

        :param balance_warning_limit: The balance_warning_limit of this EditResellerRequest.
        :type balance_warning_limit: float
        """

        self._balance_warning_limit = balance_warning_limit

    @property
    def reseller_type(self) -> str:
        """Gets the reseller_type of this EditResellerRequest.


        :return: The reseller_type of this EditResellerRequest.
        :rtype: str
        """
        return self._reseller_type

    @reseller_type.setter
    def reseller_type(self, reseller_type: str):
        """Sets the reseller_type of this EditResellerRequest.


        :param reseller_type: The reseller_type of this EditResellerRequest.
        :type reseller_type: str
        """
        allowed_values = ["prepaid", "postpaid"]  # noqa: E501
        if reseller_type not in allowed_values:
            raise ValueError(
                "Invalid value for `reseller_type` ({0}), must be one of {1}"
                .format(reseller_type, allowed_values)
            )

        self._reseller_type = reseller_type

    @property
    def callback_url(self) -> str:
        """Gets the callback_url of this EditResellerRequest.


        :return: The callback_url of this EditResellerRequest.
        :rtype: str
        """
        return self._callback_url

    @callback_url.setter
    def callback_url(self, callback_url: str):
        """Sets the callback_url of this EditResellerRequest.


        :param callback_url: The callback_url of this EditResellerRequest.
        :type callback_url: str
        """

        self._callback_url = callback_url

    @property
    def consumption_url(self) -> str:
        """Gets the consumption_url of this EditResellerRequest.


        :return: The consumption_url of this EditResellerRequest.
        :rtype: str
        """
        return self._consumption_url

    @consumption_url.setter
    def consumption_url(self, consumption_url: str):
        """Sets the consumption_url of this EditResellerRequest.


        :param consumption_url: The consumption_url of this EditResellerRequest.
        :type consumption_url: str
        """

        self._consumption_url = consumption_url

    @property
    def default_currency_code(self) -> str:
        """Gets the default_currency_code of this EditResellerRequest.


        :return: The default_currency_code of this EditResellerRequest.
        :rtype: str
        """
        return self._default_currency_code

    @default_currency_code.setter
    def default_currency_code(self, default_currency_code: str):
        """Sets the default_currency_code of this EditResellerRequest.


        :param default_currency_code: The default_currency_code of this EditResellerRequest.
        :type default_currency_code: str
        """

        self._default_currency_code = default_currency_code

    @property
    def support_topup(self) -> bool:
        """Gets the support_topup of this EditResellerRequest.


        :return: The support_topup of this EditResellerRequest.
        :rtype: bool
        """
        return self._support_topup

    @support_topup.setter
    def support_topup(self, support_topup: bool):
        """Sets the support_topup of this EditResellerRequest.


        :param support_topup: The support_topup of this EditResellerRequest.
        :type support_topup: bool
        """

        self._support_topup = support_topup

    @property
    def supports_promo(self) -> bool:
        """Gets the supports_promo of this EditResellerRequest.


        :return: The supports_promo of this EditResellerRequest.
        :rtype: bool
        """
        return self._supports_promo

    @supports_promo.setter
    def supports_promo(self, supports_promo: bool):
        """Sets the supports_promo of this EditResellerRequest.


        :param supports_promo: The supports_promo of this EditResellerRequest.
        :type supports_promo: bool
        """

        self._supports_promo = supports_promo

    @property
    def supports_vouchers(self) -> bool:
        """Gets the supports_vouchers of this EditResellerRequest.


        :return: The supports_vouchers of this EditResellerRequest.
        :rtype: bool
        """
        return self._supports_vouchers

    @supports_vouchers.setter
    def supports_vouchers(self, supports_vouchers: bool):
        """Sets the supports_vouchers of this EditResellerRequest.


        :param supports_vouchers: The supports_vouchers of this EditResellerRequest.
        :type supports_vouchers: bool
        """

        self._supports_vouchers = supports_vouchers

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this EditResellerRequest.


        :return: The is_active of this EditResellerRequest.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this EditResellerRequest.


        :param is_active: The is_active of this EditResellerRequest.
        :type is_active: bool
        """

        self._is_active = is_active

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this EditResellerRequest.


        :return: The currency_code of this EditResellerRequest.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this EditResellerRequest.


        :param currency_code: The currency_code of this EditResellerRequest.
        :type currency_code: str
        """
        allowed_values = ["USD"]  # noqa: E501
        if currency_code not in allowed_values:
            raise ValueError(
                "Invalid value for `currency_code` ({0}), must be one of {1}"
                .format(currency_code, allowed_values)
            )

        self._currency_code = currency_code

    @property
    def voucher_rate(self) -> float:
        """Gets the voucher_rate of this EditResellerRequest.


        :return: The voucher_rate of this EditResellerRequest.
        :rtype: float
        """
        return self._voucher_rate

    @voucher_rate.setter
    def voucher_rate(self, voucher_rate: float):
        """Sets the voucher_rate of this EditResellerRequest.


        :param voucher_rate: The voucher_rate of this EditResellerRequest.
        :type voucher_rate: float
        """

        self._voucher_rate = voucher_rate

    @property
    def contact(self) -> ResellerContact:
        """Gets the contact of this EditResellerRequest.


        :return: The contact of this EditResellerRequest.
        :rtype: ResellerContact
        """
        return self._contact

    @contact.setter
    def contact(self, contact: ResellerContact):
        """Sets the contact of this EditResellerRequest.


        :param contact: The contact of this EditResellerRequest.
        :type contact: ResellerContact
        """

        self._contact = contact

    @property
    def email_settings(self) -> EditResellerRequestEmailSettings:
        """Gets the email_settings of this EditResellerRequest.


        :return: The email_settings of this EditResellerRequest.
        :rtype: EditResellerRequestEmailSettings
        """
        return self._email_settings

    @email_settings.setter
    def email_settings(self, email_settings: EditResellerRequestEmailSettings):
        """Sets the email_settings of this EditResellerRequest.


        :param email_settings: The email_settings of this EditResellerRequest.
        :type email_settings: EditResellerRequestEmailSettings
        """

        self._email_settings = email_settings

    @property
    def custom_email_template_qr(self) -> str:
        """Gets the custom_email_template_qr of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_qr of this EditResellerRequest.
        :rtype: str
        """
        return self._custom_email_template_qr

    @custom_email_template_qr.setter
    def custom_email_template_qr(self, custom_email_template_qr: str):
        """Sets the custom_email_template_qr of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_qr: The custom_email_template_qr of this EditResellerRequest.
        :type custom_email_template_qr: str
        """

        self._custom_email_template_qr = custom_email_template_qr

    @property
    def custom_email_template_data(self) -> str:
        """Gets the custom_email_template_data of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_data of this EditResellerRequest.
        :rtype: str
        """
        return self._custom_email_template_data

    @custom_email_template_data.setter
    def custom_email_template_data(self, custom_email_template_data: str):
        """Sets the custom_email_template_data of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_data: The custom_email_template_data of this EditResellerRequest.
        :type custom_email_template_data: str
        """

        self._custom_email_template_data = custom_email_template_data

    @property
    def custom_email_template_expired(self) -> str:
        """Gets the custom_email_template_expired of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :return: The custom_email_template_expired of this EditResellerRequest.
        :rtype: str
        """
        return self._custom_email_template_expired

    @custom_email_template_expired.setter
    def custom_email_template_expired(self, custom_email_template_expired: str):
        """Sets the custom_email_template_expired of this EditResellerRequest.

        Preferably HTML FILE  # noqa: E501

        :param custom_email_template_expired: The custom_email_template_expired of this EditResellerRequest.
        :type custom_email_template_expired: str
        """

        self._custom_email_template_expired = custom_email_template_expired

    @property
    def image(self) -> str:
        """Gets the image of this EditResellerRequest.

        The image file of the user  # noqa: E501

        :return: The image of this EditResellerRequest.
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image: str):
        """Sets the image of this EditResellerRequest.

        The image file of the user  # noqa: E501

        :param image: The image of this EditResellerRequest.
        :type image: str
        """

        self._image = image

    @property
    def is_whitelabel(self) -> bool:
        """Gets the is_whitelabel of this EditResellerRequest.


        :return: The is_whitelabel of this EditResellerRequest.
        :rtype: bool
        """
        return self._is_whitelabel

    @is_whitelabel.setter
    def is_whitelabel(self, is_whitelabel: bool):
        """Sets the is_whitelabel of this EditResellerRequest.


        :param is_whitelabel: The is_whitelabel of this EditResellerRequest.
        :type is_whitelabel: bool
        """

        self._is_whitelabel = is_whitelabel

    @property
    def tenant_name(self) -> str:
        """Gets the tenant_name of this EditResellerRequest.


        :return: The tenant_name of this EditResellerRequest.
        :rtype: str
        """
        return self._tenant_name

    @tenant_name.setter
    def tenant_name(self, tenant_name: str):
        """Sets the tenant_name of this EditResellerRequest.


        :param tenant_name: The tenant_name of this EditResellerRequest.
        :type tenant_name: str
        """

        self._tenant_name = tenant_name

    @property
    def reseller_category(self) -> str:
        """Gets the reseller_category of this EditResellerRequest.


        :return: The reseller_category of this EditResellerRequest.
        :rtype: str
        """
        return self._reseller_category

    @reseller_category.setter
    def reseller_category(self, reseller_category: str):
        """Sets the reseller_category of this EditResellerRequest.


        :param reseller_category: The reseller_category of this EditResellerRequest.
        :type reseller_category: str
        """

        self._reseller_category = reseller_category

    @property
    def active_vendors_list(self) -> List[str]:
        """Gets the active_vendors_list of this EditResellerRequest.


        :return: The active_vendors_list of this EditResellerRequest.
        :rtype: List[str]
        """
        return self._active_vendors_list

    @active_vendors_list.setter
    def active_vendors_list(self, active_vendors_list: List[str]):
        """Sets the active_vendors_list of this EditResellerRequest.


        :param active_vendors_list: The active_vendors_list of this EditResellerRequest.
        :type active_vendors_list: List[str]
        """

        self._active_vendors_list = active_vendors_list

    @property
    def vendors_for_balance_deduction_list(self) -> List[str]:
        """Gets the vendors_for_balance_deduction_list of this EditResellerRequest.


        :return: The vendors_for_balance_deduction_list of this EditResellerRequest.
        :rtype: List[str]
        """
        return self._vendors_for_balance_deduction_list

    @vendors_for_balance_deduction_list.setter
    def vendors_for_balance_deduction_list(self, vendors_for_balance_deduction_list: List[str]):
        """Sets the vendors_for_balance_deduction_list of this EditResellerRequest.


        :param vendors_for_balance_deduction_list: The vendors_for_balance_deduction_list of this EditResellerRequest.
        :type vendors_for_balance_deduction_list: List[str]
        """

        self._vendors_for_balance_deduction_list = vendors_for_balance_deduction_list

    @property
    def request_custom_email(self) -> bool:
        """Gets the request_custom_email of this EditResellerRequest.


        :return: The request_custom_email of this EditResellerRequest.
        :rtype: bool
        """
        return self._request_custom_email

    @request_custom_email.setter
    def request_custom_email(self, request_custom_email: bool):
        """Sets the request_custom_email of this EditResellerRequest.


        :param request_custom_email: The request_custom_email of this EditResellerRequest.
        :type request_custom_email: bool
        """

        self._request_custom_email = request_custom_email

    @property
    def data_consumption_email(self) -> ResellerDataConsumptionEmail:
        """Gets the data_consumption_email of this EditResellerRequest.


        :return: The data_consumption_email of this EditResellerRequest.
        :rtype: ResellerDataConsumptionEmail
        """
        return self._data_consumption_email

    @data_consumption_email.setter
    def data_consumption_email(self, data_consumption_email: ResellerDataConsumptionEmail):
        """Sets the data_consumption_email of this EditResellerRequest.


        :param data_consumption_email: The data_consumption_email of this EditResellerRequest.
        :type data_consumption_email: ResellerDataConsumptionEmail
        """

        self._data_consumption_email = data_consumption_email

    @property
    def data_expired_email(self) -> ResellerDataExpiredEmail:
        """Gets the data_expired_email of this EditResellerRequest.


        :return: The data_expired_email of this EditResellerRequest.
        :rtype: ResellerDataExpiredEmail
        """
        return self._data_expired_email

    @data_expired_email.setter
    def data_expired_email(self, data_expired_email: ResellerDataExpiredEmail):
        """Sets the data_expired_email of this EditResellerRequest.


        :param data_expired_email: The data_expired_email of this EditResellerRequest.
        :type data_expired_email: ResellerDataExpiredEmail
        """

        self._data_expired_email = data_expired_email

    @property
    def qr_code_email(self) -> ResellerQrCodeEmail:
        """Gets the qr_code_email of this EditResellerRequest.


        :return: The qr_code_email of this EditResellerRequest.
        :rtype: ResellerQrCodeEmail
        """
        return self._qr_code_email

    @qr_code_email.setter
    def qr_code_email(self, qr_code_email: ResellerQrCodeEmail):
        """Sets the qr_code_email of this EditResellerRequest.


        :param qr_code_email: The qr_code_email of this EditResellerRequest.
        :type qr_code_email: ResellerQrCodeEmail
        """

        self._qr_code_email = qr_code_email

    @property
    def notification_type(self) -> str:
        """Gets the notification_type of this EditResellerRequest.


        :return: The notification_type of this EditResellerRequest.
        :rtype: str
        """
        return self._notification_type

    @notification_type.setter
    def notification_type(self, notification_type: str):
        """Sets the notification_type of this EditResellerRequest.


        :param notification_type: The notification_type of this EditResellerRequest.
        :type notification_type: str
        """
        allowed_values = ["webhook"]  # noqa: E501
        if notification_type not in allowed_values:
            raise ValueError(
                "Invalid value for `notification_type` ({0}), must be one of {1}"
                .format(notification_type, allowed_values)
            )

        self._notification_type = notification_type

    @property
    def retry_on_failed_after(self) -> int:
        """Gets the retry_on_failed_after of this EditResellerRequest.


        :return: The retry_on_failed_after of this EditResellerRequest.
        :rtype: int
        """
        return self._retry_on_failed_after

    @retry_on_failed_after.setter
    def retry_on_failed_after(self, retry_on_failed_after: int):
        """Sets the retry_on_failed_after of this EditResellerRequest.


        :param retry_on_failed_after: The retry_on_failed_after of this EditResellerRequest.
        :type retry_on_failed_after: int
        """

        self._retry_on_failed_after = retry_on_failed_after

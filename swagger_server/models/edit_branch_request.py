# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.reseller_contact import ResellerContact  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class EditBranchRequest(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, branch_name: str=None, limit: float=None, limit_consumption: float=None, contact: ResellerContact=None, is_active: bool=True):  # noqa: E501
        """EditBranchRequest - a model defined in Swagger

        :param branch_name: The branch_name of this EditBranchRequest.  # noqa: E501
        :type branch_name: str
        :param limit: The limit of this EditBranchRequest.  # noqa: E501
        :type limit: float
        :param limit_consumption: The limit_consumption of this EditBranchRequest.  # noqa: E501
        :type limit_consumption: float
        :param contact: The contact of this EditBranchRequest.  # noqa: E501
        :type contact: ResellerContact
        :param is_active: The is_active of this EditBranchRequest.  # noqa: E501
        :type is_active: bool
        """
        self.swagger_types = {
            'branch_name': str,
            'limit': float,
            'limit_consumption': float,
            'contact': ResellerContact,
            'is_active': bool
        }

        self.attribute_map = {
            'branch_name': 'branch_name',
            'limit': 'limit',
            'limit_consumption': 'limit_consumption',
            'contact': 'contact',
            'is_active': 'is_active'
        }
        self._branch_name = branch_name
        self._limit = limit
        self._limit_consumption = limit_consumption
        self._contact = contact
        self._is_active = is_active

    @classmethod
    def from_dict(cls, dikt) -> 'EditBranchRequest':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The EditBranchRequest of this EditBranchRequest.  # noqa: E501
        :rtype: EditBranchRequest
        """
        return util.deserialize_model(dikt, cls)

    @property
    def branch_name(self) -> str:
        """Gets the branch_name of this EditBranchRequest.


        :return: The branch_name of this EditBranchRequest.
        :rtype: str
        """
        return self._branch_name

    @branch_name.setter
    def branch_name(self, branch_name: str):
        """Sets the branch_name of this EditBranchRequest.


        :param branch_name: The branch_name of this EditBranchRequest.
        :type branch_name: str
        """

        self._branch_name = branch_name

    @property
    def limit(self) -> float:
        """Gets the limit of this EditBranchRequest.


        :return: The limit of this EditBranchRequest.
        :rtype: float
        """
        return self._limit

    @limit.setter
    def limit(self, limit: float):
        """Sets the limit of this EditBranchRequest.


        :param limit: The limit of this EditBranchRequest.
        :type limit: float
        """

        self._limit = limit

    @property
    def limit_consumption(self) -> float:
        """Gets the limit_consumption of this EditBranchRequest.


        :return: The limit_consumption of this EditBranchRequest.
        :rtype: float
        """
        return self._limit_consumption

    @limit_consumption.setter
    def limit_consumption(self, limit_consumption: float):
        """Sets the limit_consumption of this EditBranchRequest.


        :param limit_consumption: The limit_consumption of this EditBranchRequest.
        :type limit_consumption: float
        """

        self._limit_consumption = limit_consumption

    @property
    def contact(self) -> ResellerContact:
        """Gets the contact of this EditBranchRequest.


        :return: The contact of this EditBranchRequest.
        :rtype: ResellerContact
        """
        return self._contact

    @contact.setter
    def contact(self, contact: ResellerContact):
        """Sets the contact of this EditBranchRequest.


        :param contact: The contact of this EditBranchRequest.
        :type contact: ResellerContact
        """

        self._contact = contact

    @property
    def is_active(self) -> bool:
        """Gets the is_active of this EditBranchRequest.


        :return: The is_active of this EditBranchRequest.
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active: bool):
        """Sets the is_active of this EditBranchRequest.


        :param is_active: The is_active of this EditBranchRequest.
        :type is_active: bool
        """

        self._is_active = is_active

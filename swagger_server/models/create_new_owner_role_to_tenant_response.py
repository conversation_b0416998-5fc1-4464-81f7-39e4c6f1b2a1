# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class CreateNewOwnerRoleToTenantResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, message: str=None, role_id: str=None):  # noqa: E501
        """CreateNewOwnerRoleToTenantResponse - a model defined in Swagger

        :param message: The message of this CreateNewOwnerRoleToTenantResponse.  # noqa: E501
        :type message: str
        :param role_id: The role_id of this CreateNewOwnerRoleToTenantResponse.  # noqa: E501
        :type role_id: str
        """
        self.swagger_types = {
            'message': str,
            'role_id': str
        }

        self.attribute_map = {
            'message': 'Message',
            'role_id': 'RoleID'
        }
        self._message = message
        self._role_id = role_id

    @classmethod
    def from_dict(cls, dikt) -> 'CreateNewOwnerRoleToTenantResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The CreateNewOwnerRoleToTenantResponse of this CreateNewOwnerRoleToTenantResponse.  # noqa: E501
        :rtype: CreateNewOwnerRoleToTenantResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def message(self) -> str:
        """Gets the message of this CreateNewOwnerRoleToTenantResponse.


        :return: The message of this CreateNewOwnerRoleToTenantResponse.
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message: str):
        """Sets the message of this CreateNewOwnerRoleToTenantResponse.


        :param message: The message of this CreateNewOwnerRoleToTenantResponse.
        :type message: str
        """

        self._message = message

    @property
    def role_id(self) -> str:
        """Gets the role_id of this CreateNewOwnerRoleToTenantResponse.


        :return: The role_id of this CreateNewOwnerRoleToTenantResponse.
        :rtype: str
        """
        return self._role_id

    @role_id.setter
    def role_id(self, role_id: str):
        """Sets the role_id of this CreateNewOwnerRoleToTenantResponse.


        :param role_id: The role_id of this CreateNewOwnerRoleToTenantResponse.
        :type role_id: str
        """

        self._role_id = role_id

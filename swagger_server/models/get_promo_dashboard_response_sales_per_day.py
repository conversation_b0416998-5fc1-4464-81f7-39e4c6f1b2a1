# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server import util


class GetPromoDashboardResponseSalesPerDay(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, commission_amount: float=None, bundles_sold: int=None, total_sales_volume: float=None, _date: str=None):  # noqa: E501
        """GetPromoDashboardResponseSalesPerDay - a model defined in Swagger

        :param commission_amount: The commission_amount of this GetPromoDashboardResponseSalesPerDay.  # noqa: E501
        :type commission_amount: float
        :param bundles_sold: The bundles_sold of this GetPromoDashboardResponseSalesPerDay.  # noqa: E501
        :type bundles_sold: int
        :param total_sales_volume: The total_sales_volume of this GetPromoDashboardResponseSalesPerDay.  # noqa: E501
        :type total_sales_volume: float
        :param _date: The _date of this GetPromoDashboardResponseSalesPerDay.  # noqa: E501
        :type _date: str
        """
        self.swagger_types = {
            'commission_amount': float,
            'bundles_sold': int,
            'total_sales_volume': float,
            '_date': str
        }

        self.attribute_map = {
            'commission_amount': 'commission_amount',
            'bundles_sold': 'bundles_sold',
            'total_sales_volume': 'total_sales_volume',
            '_date': 'date'
        }
        self._commission_amount = commission_amount
        self._bundles_sold = bundles_sold
        self._total_sales_volume = total_sales_volume
        self.__date = _date

    @classmethod
    def from_dict(cls, dikt) -> 'GetPromoDashboardResponseSalesPerDay':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetPromoDashboardResponse_sales_per_day of this GetPromoDashboardResponseSalesPerDay.  # noqa: E501
        :rtype: GetPromoDashboardResponseSalesPerDay
        """
        return util.deserialize_model(dikt, cls)

    @property
    def commission_amount(self) -> float:
        """Gets the commission_amount of this GetPromoDashboardResponseSalesPerDay.


        :return: The commission_amount of this GetPromoDashboardResponseSalesPerDay.
        :rtype: float
        """
        return self._commission_amount

    @commission_amount.setter
    def commission_amount(self, commission_amount: float):
        """Sets the commission_amount of this GetPromoDashboardResponseSalesPerDay.


        :param commission_amount: The commission_amount of this GetPromoDashboardResponseSalesPerDay.
        :type commission_amount: float
        """

        self._commission_amount = commission_amount

    @property
    def bundles_sold(self) -> int:
        """Gets the bundles_sold of this GetPromoDashboardResponseSalesPerDay.


        :return: The bundles_sold of this GetPromoDashboardResponseSalesPerDay.
        :rtype: int
        """
        return self._bundles_sold

    @bundles_sold.setter
    def bundles_sold(self, bundles_sold: int):
        """Sets the bundles_sold of this GetPromoDashboardResponseSalesPerDay.


        :param bundles_sold: The bundles_sold of this GetPromoDashboardResponseSalesPerDay.
        :type bundles_sold: int
        """

        self._bundles_sold = bundles_sold

    @property
    def total_sales_volume(self) -> float:
        """Gets the total_sales_volume of this GetPromoDashboardResponseSalesPerDay.


        :return: The total_sales_volume of this GetPromoDashboardResponseSalesPerDay.
        :rtype: float
        """
        return self._total_sales_volume

    @total_sales_volume.setter
    def total_sales_volume(self, total_sales_volume: float):
        """Sets the total_sales_volume of this GetPromoDashboardResponseSalesPerDay.


        :param total_sales_volume: The total_sales_volume of this GetPromoDashboardResponseSalesPerDay.
        :type total_sales_volume: float
        """

        self._total_sales_volume = total_sales_volume

    @property
    def _date(self) -> str:
        """Gets the _date of this GetPromoDashboardResponseSalesPerDay.


        :return: The _date of this GetPromoDashboardResponseSalesPerDay.
        :rtype: str
        """
        return self.__date

    @_date.setter
    def _date(self, _date: str):
        """Sets the _date of this GetPromoDashboardResponseSalesPerDay.


        :param _date: The _date of this GetPromoDashboardResponseSalesPerDay.
        :type _date: str
        """

        self.__date = _date

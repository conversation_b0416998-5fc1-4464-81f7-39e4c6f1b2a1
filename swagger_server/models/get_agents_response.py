# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
from swagger_server.models.get_agents_response_agents import GetAgentsResponseAgents  # noqa: F401,E501
import re  # noqa: F401,E501
from swagger_server import util


class GetAgentsResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, response_code: str=None, developer_message: str=None, title: str=None, total_agents_count: int=None, agents: List[GetAgentsResponseAgents]=None):  # noqa: E501
        """GetAgentsResponse - a model defined in Swagger

        :param response_code: The response_code of this GetAgentsResponse.  # noqa: E501
        :type response_code: str
        :param developer_message: The developer_message of this GetAgentsResponse.  # noqa: E501
        :type developer_message: str
        :param title: The title of this GetAgentsResponse.  # noqa: E501
        :type title: str
        :param total_agents_count: The total_agents_count of this GetAgentsResponse.  # noqa: E501
        :type total_agents_count: int
        :param agents: The agents of this GetAgentsResponse.  # noqa: E501
        :type agents: List[GetAgentsResponseAgents]
        """
        self.swagger_types = {
            'response_code': str,
            'developer_message': str,
            'title': str,
            'total_agents_count': int,
            'agents': List[GetAgentsResponseAgents]
        }

        self.attribute_map = {
            'response_code': 'response_code',
            'developer_message': 'developer_message',
            'title': 'title',
            'total_agents_count': 'total_agents_count',
            'agents': 'agents'
        }
        self._response_code = response_code
        self._developer_message = developer_message
        self._title = title
        self._total_agents_count = total_agents_count
        self._agents = agents

    @classmethod
    def from_dict(cls, dikt) -> 'GetAgentsResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetAgentsResponse of this GetAgentsResponse.  # noqa: E501
        :rtype: GetAgentsResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def response_code(self) -> str:
        """Gets the response_code of this GetAgentsResponse.


        :return: The response_code of this GetAgentsResponse.
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code: str):
        """Sets the response_code of this GetAgentsResponse.


        :param response_code: The response_code of this GetAgentsResponse.
        :type response_code: str
        """

        self._response_code = response_code

    @property
    def developer_message(self) -> str:
        """Gets the developer_message of this GetAgentsResponse.


        :return: The developer_message of this GetAgentsResponse.
        :rtype: str
        """
        return self._developer_message

    @developer_message.setter
    def developer_message(self, developer_message: str):
        """Sets the developer_message of this GetAgentsResponse.


        :param developer_message: The developer_message of this GetAgentsResponse.
        :type developer_message: str
        """

        self._developer_message = developer_message

    @property
    def title(self) -> str:
        """Gets the title of this GetAgentsResponse.


        :return: The title of this GetAgentsResponse.
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title: str):
        """Sets the title of this GetAgentsResponse.


        :param title: The title of this GetAgentsResponse.
        :type title: str
        """

        self._title = title

    @property
    def total_agents_count(self) -> int:
        """Gets the total_agents_count of this GetAgentsResponse.


        :return: The total_agents_count of this GetAgentsResponse.
        :rtype: int
        """
        return self._total_agents_count

    @total_agents_count.setter
    def total_agents_count(self, total_agents_count: int):
        """Sets the total_agents_count of this GetAgentsResponse.


        :param total_agents_count: The total_agents_count of this GetAgentsResponse.
        :type total_agents_count: int
        """

        self._total_agents_count = total_agents_count

    @property
    def agents(self) -> List[GetAgentsResponseAgents]:
        """Gets the agents of this GetAgentsResponse.


        :return: The agents of this GetAgentsResponse.
        :rtype: List[GetAgentsResponseAgents]
        """
        return self._agents

    @agents.setter
    def agents(self, agents: List[GetAgentsResponseAgents]):
        """Sets the agents of this GetAgentsResponse.


        :param agents: The agents of this GetAgentsResponse.
        :type agents: List[GetAgentsResponseAgents]
        """

        self._agents = agents

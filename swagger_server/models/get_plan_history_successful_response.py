# coding: utf-8

from __future__ import absolute_import
from datetime import date, datetime  # noqa: F401

from typing import List, Dict  # noqa: F401

from swagger_server.models.base_model_ import Model
import re  # noqa: F401,E501
from swagger_server import util


class GetPlanHistorySuccessfulResponse(Model):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """
    def __init__(self, order_id: str=None, reseller_id: str=None, branch_id: str=None, order_status: str=None, plan_uid: str=None, plan_status: str=None, plan_started: bool=None, expiry_date: datetime=None, bundle_code: str=None, bundle_marketing_name: str=None, bundle_name: str=None, bundle_category: str=None, data_unit: str=None, retail_price: float=None, data_amount: float=None, validity_amount: str=None, region_name: str=None, region_code: str=None, bundle_duration: int=None, country_code: List[str]=None, country_name: List[str]=None, iccid: str=None, bundle_price: float=None, bundle_price_in_additional_currency: float=None, bundle_retail_price: float=None, bundle_retail_price_in_additional_currency: float=None, currency_code: str=None, additional_currency_code: str=None, matching_id: str=None, smdp_address: str=None, activation_code: str=None, client_name: str=None, client_email: str=None, remaining_wallet_balance: float=None, remaining_wallet_balance_in_additional_currency: float=None, date_created: datetime=None, refund_reason: str=None, order_reference: str=None, otp: str=None, profile_expiry_date: str=None, bundle_expiry_date: str=None, order_type: str=None, whatsapp_number: str=None, topup_an_expired_plan: bool=None, has_related_order: bool=None, has_related_active_topups: bool=None):  # noqa: E501
        """GetPlanHistorySuccessfulResponse - a model defined in Swagger

        :param order_id: The order_id of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type order_id: str
        :param reseller_id: The reseller_id of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type reseller_id: str
        :param branch_id: The branch_id of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type branch_id: str
        :param order_status: The order_status of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type order_status: str
        :param plan_uid: The plan_uid of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type plan_uid: str
        :param plan_status: The plan_status of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type plan_status: str
        :param plan_started: The plan_started of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type plan_started: bool
        :param expiry_date: The expiry_date of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type expiry_date: datetime
        :param bundle_code: The bundle_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_code: str
        :param bundle_marketing_name: The bundle_marketing_name of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_marketing_name: str
        :param bundle_name: The bundle_name of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_name: str
        :param bundle_category: The bundle_category of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_category: str
        :param data_unit: The data_unit of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type data_unit: str
        :param retail_price: The retail_price of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type retail_price: float
        :param data_amount: The data_amount of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type data_amount: float
        :param validity_amount: The validity_amount of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type validity_amount: str
        :param region_name: The region_name of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type region_name: str
        :param region_code: The region_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type region_code: str
        :param bundle_duration: The bundle_duration of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_duration: int
        :param country_code: The country_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type country_code: List[str]
        :param country_name: The country_name of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type country_name: List[str]
        :param iccid: The iccid of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type iccid: str
        :param bundle_price: The bundle_price of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_price: float
        :param bundle_price_in_additional_currency: The bundle_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_price_in_additional_currency: float
        :param bundle_retail_price: The bundle_retail_price of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_retail_price: float
        :param bundle_retail_price_in_additional_currency: The bundle_retail_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_retail_price_in_additional_currency: float
        :param currency_code: The currency_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type currency_code: str
        :param additional_currency_code: The additional_currency_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type additional_currency_code: str
        :param matching_id: The matching_id of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type matching_id: str
        :param smdp_address: The smdp_address of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type smdp_address: str
        :param activation_code: The activation_code of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type activation_code: str
        :param client_name: The client_name of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type client_name: str
        :param client_email: The client_email of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type client_email: str
        :param remaining_wallet_balance: The remaining_wallet_balance of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type remaining_wallet_balance: float
        :param remaining_wallet_balance_in_additional_currency: The remaining_wallet_balance_in_additional_currency of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type remaining_wallet_balance_in_additional_currency: float
        :param date_created: The date_created of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type date_created: datetime
        :param refund_reason: The refund_reason of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type refund_reason: str
        :param order_reference: The order_reference of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type order_reference: str
        :param otp: The otp of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type otp: str
        :param profile_expiry_date: The profile_expiry_date of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type profile_expiry_date: str
        :param bundle_expiry_date: The bundle_expiry_date of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type bundle_expiry_date: str
        :param order_type: The order_type of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type order_type: str
        :param whatsapp_number: The whatsapp_number of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type whatsapp_number: str
        :param topup_an_expired_plan: The topup_an_expired_plan of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type topup_an_expired_plan: bool
        :param has_related_order: The has_related_order of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type has_related_order: bool
        :param has_related_active_topups: The has_related_active_topups of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :type has_related_active_topups: bool
        """
        self.swagger_types = {
            'order_id': str,
            'reseller_id': str,
            'branch_id': str,
            'order_status': str,
            'plan_uid': str,
            'plan_status': str,
            'plan_started': bool,
            'expiry_date': datetime,
            'bundle_code': str,
            'bundle_marketing_name': str,
            'bundle_name': str,
            'bundle_category': str,
            'data_unit': str,
            'retail_price': float,
            'data_amount': float,
            'validity_amount': str,
            'region_name': str,
            'region_code': str,
            'bundle_duration': int,
            'country_code': List[str],
            'country_name': List[str],
            'iccid': str,
            'bundle_price': float,
            'bundle_price_in_additional_currency': float,
            'bundle_retail_price': float,
            'bundle_retail_price_in_additional_currency': float,
            'currency_code': str,
            'additional_currency_code': str,
            'matching_id': str,
            'smdp_address': str,
            'activation_code': str,
            'client_name': str,
            'client_email': str,
            'remaining_wallet_balance': float,
            'remaining_wallet_balance_in_additional_currency': float,
            'date_created': datetime,
            'refund_reason': str,
            'order_reference': str,
            'otp': str,
            'profile_expiry_date': str,
            'bundle_expiry_date': str,
            'order_type': str,
            'whatsapp_number': str,
            'topup_an_expired_plan': bool,
            'has_related_order': bool,
            'has_related_active_topups': bool
        }

        self.attribute_map = {
            'order_id': 'order_id',
            'reseller_id': 'reseller_id',
            'branch_id': 'branch_id',
            'order_status': 'order_status',
            'plan_uid': 'plan_uid',
            'plan_status': 'plan_status',
            'plan_started': 'plan_started',
            'expiry_date': 'expiry_date',
            'bundle_code': 'bundle_code',
            'bundle_marketing_name': 'bundle_marketing_name',
            'bundle_name': 'bundle_name',
            'bundle_category': 'bundle_category',
            'data_unit': 'data_unit',
            'retail_price': 'retail_price',
            'data_amount': 'data_amount',
            'validity_amount': 'validity_amount',
            'region_name': 'region_name',
            'region_code': 'region_code',
            'bundle_duration': 'bundle_duration',
            'country_code': 'country_code',
            'country_name': 'country_name',
            'iccid': 'iccid',
            'bundle_price': 'bundle_price',
            'bundle_price_in_additional_currency': 'bundle_price_in_additional_currency',
            'bundle_retail_price': 'bundle_retail_price',
            'bundle_retail_price_in_additional_currency': 'bundle_retail_price_in_additional_currency',
            'currency_code': 'currency_code',
            'additional_currency_code': 'additional_currency_code',
            'matching_id': 'matching_id',
            'smdp_address': 'smdp_address',
            'activation_code': 'activation_code',
            'client_name': 'client_name',
            'client_email': 'client_email',
            'remaining_wallet_balance': 'remaining_wallet_balance',
            'remaining_wallet_balance_in_additional_currency': 'remaining_wallet_balance_in_additional_currency',
            'date_created': 'date_created',
            'refund_reason': 'refund_reason',
            'order_reference': 'order_reference',
            'otp': 'otp',
            'profile_expiry_date': 'profile_expiry_date',
            'bundle_expiry_date': 'bundle_expiry_date',
            'order_type': 'order_type',
            'whatsapp_number': 'whatsapp_number',
            'topup_an_expired_plan': 'topup_an_expired_plan',
            'has_related_order': 'has_related_order',
            'has_related_active_topups': 'has_related_active_topups'
        }
        self._order_id = order_id
        self._reseller_id = reseller_id
        self._branch_id = branch_id
        self._order_status = order_status
        self._plan_uid = plan_uid
        self._plan_status = plan_status
        self._plan_started = plan_started
        self._expiry_date = expiry_date
        self._bundle_code = bundle_code
        self._bundle_marketing_name = bundle_marketing_name
        self._bundle_name = bundle_name
        self._bundle_category = bundle_category
        self._data_unit = data_unit
        self._retail_price = retail_price
        self._data_amount = data_amount
        self._validity_amount = validity_amount
        self._region_name = region_name
        self._region_code = region_code
        self._bundle_duration = bundle_duration
        self._country_code = country_code
        self._country_name = country_name
        self._iccid = iccid
        self._bundle_price = bundle_price
        self._bundle_price_in_additional_currency = bundle_price_in_additional_currency
        self._bundle_retail_price = bundle_retail_price
        self._bundle_retail_price_in_additional_currency = bundle_retail_price_in_additional_currency
        self._currency_code = currency_code
        self._additional_currency_code = additional_currency_code
        self._matching_id = matching_id
        self._smdp_address = smdp_address
        self._activation_code = activation_code
        self._client_name = client_name
        self._client_email = client_email
        self._remaining_wallet_balance = remaining_wallet_balance
        self._remaining_wallet_balance_in_additional_currency = remaining_wallet_balance_in_additional_currency
        self._date_created = date_created
        self._refund_reason = refund_reason
        self._order_reference = order_reference
        self._otp = otp
        self._profile_expiry_date = profile_expiry_date
        self._bundle_expiry_date = bundle_expiry_date
        self._order_type = order_type
        self._whatsapp_number = whatsapp_number
        self._topup_an_expired_plan = topup_an_expired_plan
        self._has_related_order = has_related_order
        self._has_related_active_topups = has_related_active_topups

    @classmethod
    def from_dict(cls, dikt) -> 'GetPlanHistorySuccessfulResponse':
        """Returns the dict as a model

        :param dikt: A dict.
        :type: dict
        :return: The GetPlanHistorySuccessfulResponse of this GetPlanHistorySuccessfulResponse.  # noqa: E501
        :rtype: GetPlanHistorySuccessfulResponse
        """
        return util.deserialize_model(dikt, cls)

    @property
    def order_id(self) -> str:
        """Gets the order_id of this GetPlanHistorySuccessfulResponse.


        :return: The order_id of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id: str):
        """Sets the order_id of this GetPlanHistorySuccessfulResponse.


        :param order_id: The order_id of this GetPlanHistorySuccessfulResponse.
        :type order_id: str
        """

        self._order_id = order_id

    @property
    def reseller_id(self) -> str:
        """Gets the reseller_id of this GetPlanHistorySuccessfulResponse.


        :return: The reseller_id of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._reseller_id

    @reseller_id.setter
    def reseller_id(self, reseller_id: str):
        """Sets the reseller_id of this GetPlanHistorySuccessfulResponse.


        :param reseller_id: The reseller_id of this GetPlanHistorySuccessfulResponse.
        :type reseller_id: str
        """

        self._reseller_id = reseller_id

    @property
    def branch_id(self) -> str:
        """Gets the branch_id of this GetPlanHistorySuccessfulResponse.


        :return: The branch_id of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._branch_id

    @branch_id.setter
    def branch_id(self, branch_id: str):
        """Sets the branch_id of this GetPlanHistorySuccessfulResponse.


        :param branch_id: The branch_id of this GetPlanHistorySuccessfulResponse.
        :type branch_id: str
        """

        self._branch_id = branch_id

    @property
    def order_status(self) -> str:
        """Gets the order_status of this GetPlanHistorySuccessfulResponse.


        :return: The order_status of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._order_status

    @order_status.setter
    def order_status(self, order_status: str):
        """Sets the order_status of this GetPlanHistorySuccessfulResponse.


        :param order_status: The order_status of this GetPlanHistorySuccessfulResponse.
        :type order_status: str
        """
        allowed_values = ["Successful", "Refunded"]  # noqa: E501
        if order_status not in allowed_values:
            raise ValueError(
                "Invalid value for `order_status` ({0}), must be one of {1}"
                .format(order_status, allowed_values)
            )

        self._order_status = order_status

    @property
    def plan_uid(self) -> str:
        """Gets the plan_uid of this GetPlanHistorySuccessfulResponse.


        :return: The plan_uid of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._plan_uid

    @plan_uid.setter
    def plan_uid(self, plan_uid: str):
        """Sets the plan_uid of this GetPlanHistorySuccessfulResponse.


        :param plan_uid: The plan_uid of this GetPlanHistorySuccessfulResponse.
        :type plan_uid: str
        """

        self._plan_uid = plan_uid

    @property
    def plan_status(self) -> str:
        """Gets the plan_status of this GetPlanHistorySuccessfulResponse.


        :return: The plan_status of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._plan_status

    @plan_status.setter
    def plan_status(self, plan_status: str):
        """Sets the plan_status of this GetPlanHistorySuccessfulResponse.


        :param plan_status: The plan_status of this GetPlanHistorySuccessfulResponse.
        :type plan_status: str
        """

        self._plan_status = plan_status

    @property
    def plan_started(self) -> bool:
        """Gets the plan_started of this GetPlanHistorySuccessfulResponse.


        :return: The plan_started of this GetPlanHistorySuccessfulResponse.
        :rtype: bool
        """
        return self._plan_started

    @plan_started.setter
    def plan_started(self, plan_started: bool):
        """Sets the plan_started of this GetPlanHistorySuccessfulResponse.


        :param plan_started: The plan_started of this GetPlanHistorySuccessfulResponse.
        :type plan_started: bool
        """

        self._plan_started = plan_started

    @property
    def expiry_date(self) -> datetime:
        """Gets the expiry_date of this GetPlanHistorySuccessfulResponse.


        :return: The expiry_date of this GetPlanHistorySuccessfulResponse.
        :rtype: datetime
        """
        return self._expiry_date

    @expiry_date.setter
    def expiry_date(self, expiry_date: datetime):
        """Sets the expiry_date of this GetPlanHistorySuccessfulResponse.


        :param expiry_date: The expiry_date of this GetPlanHistorySuccessfulResponse.
        :type expiry_date: datetime
        """

        self._expiry_date = expiry_date

    @property
    def bundle_code(self) -> str:
        """Gets the bundle_code of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_code of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._bundle_code

    @bundle_code.setter
    def bundle_code(self, bundle_code: str):
        """Sets the bundle_code of this GetPlanHistorySuccessfulResponse.


        :param bundle_code: The bundle_code of this GetPlanHistorySuccessfulResponse.
        :type bundle_code: str
        """

        self._bundle_code = bundle_code

    @property
    def bundle_marketing_name(self) -> str:
        """Gets the bundle_marketing_name of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_marketing_name of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._bundle_marketing_name

    @bundle_marketing_name.setter
    def bundle_marketing_name(self, bundle_marketing_name: str):
        """Sets the bundle_marketing_name of this GetPlanHistorySuccessfulResponse.


        :param bundle_marketing_name: The bundle_marketing_name of this GetPlanHistorySuccessfulResponse.
        :type bundle_marketing_name: str
        """

        self._bundle_marketing_name = bundle_marketing_name

    @property
    def bundle_name(self) -> str:
        """Gets the bundle_name of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_name of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._bundle_name

    @bundle_name.setter
    def bundle_name(self, bundle_name: str):
        """Sets the bundle_name of this GetPlanHistorySuccessfulResponse.


        :param bundle_name: The bundle_name of this GetPlanHistorySuccessfulResponse.
        :type bundle_name: str
        """

        self._bundle_name = bundle_name

    @property
    def bundle_category(self) -> str:
        """Gets the bundle_category of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_category of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._bundle_category

    @bundle_category.setter
    def bundle_category(self, bundle_category: str):
        """Sets the bundle_category of this GetPlanHistorySuccessfulResponse.


        :param bundle_category: The bundle_category of this GetPlanHistorySuccessfulResponse.
        :type bundle_category: str
        """
        allowed_values = ["country", "global", "region", "cruise"]  # noqa: E501
        if bundle_category not in allowed_values:
            raise ValueError(
                "Invalid value for `bundle_category` ({0}), must be one of {1}"
                .format(bundle_category, allowed_values)
            )

        self._bundle_category = bundle_category

    @property
    def data_unit(self) -> str:
        """Gets the data_unit of this GetPlanHistorySuccessfulResponse.


        :return: The data_unit of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._data_unit

    @data_unit.setter
    def data_unit(self, data_unit: str):
        """Sets the data_unit of this GetPlanHistorySuccessfulResponse.


        :param data_unit: The data_unit of this GetPlanHistorySuccessfulResponse.
        :type data_unit: str
        """

        self._data_unit = data_unit

    @property
    def retail_price(self) -> float:
        """Gets the retail_price of this GetPlanHistorySuccessfulResponse.


        :return: The retail_price of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._retail_price

    @retail_price.setter
    def retail_price(self, retail_price: float):
        """Sets the retail_price of this GetPlanHistorySuccessfulResponse.


        :param retail_price: The retail_price of this GetPlanHistorySuccessfulResponse.
        :type retail_price: float
        """

        self._retail_price = retail_price

    @property
    def data_amount(self) -> float:
        """Gets the data_amount of this GetPlanHistorySuccessfulResponse.


        :return: The data_amount of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._data_amount

    @data_amount.setter
    def data_amount(self, data_amount: float):
        """Sets the data_amount of this GetPlanHistorySuccessfulResponse.


        :param data_amount: The data_amount of this GetPlanHistorySuccessfulResponse.
        :type data_amount: float
        """

        self._data_amount = data_amount

    @property
    def validity_amount(self) -> str:
        """Gets the validity_amount of this GetPlanHistorySuccessfulResponse.


        :return: The validity_amount of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._validity_amount

    @validity_amount.setter
    def validity_amount(self, validity_amount: str):
        """Sets the validity_amount of this GetPlanHistorySuccessfulResponse.


        :param validity_amount: The validity_amount of this GetPlanHistorySuccessfulResponse.
        :type validity_amount: str
        """

        self._validity_amount = validity_amount

    @property
    def region_name(self) -> str:
        """Gets the region_name of this GetPlanHistorySuccessfulResponse.


        :return: The region_name of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name: str):
        """Sets the region_name of this GetPlanHistorySuccessfulResponse.


        :param region_name: The region_name of this GetPlanHistorySuccessfulResponse.
        :type region_name: str
        """

        self._region_name = region_name

    @property
    def region_code(self) -> str:
        """Gets the region_code of this GetPlanHistorySuccessfulResponse.


        :return: The region_code of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code: str):
        """Sets the region_code of this GetPlanHistorySuccessfulResponse.


        :param region_code: The region_code of this GetPlanHistorySuccessfulResponse.
        :type region_code: str
        """

        self._region_code = region_code

    @property
    def bundle_duration(self) -> int:
        """Gets the bundle_duration of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_duration of this GetPlanHistorySuccessfulResponse.
        :rtype: int
        """
        return self._bundle_duration

    @bundle_duration.setter
    def bundle_duration(self, bundle_duration: int):
        """Sets the bundle_duration of this GetPlanHistorySuccessfulResponse.


        :param bundle_duration: The bundle_duration of this GetPlanHistorySuccessfulResponse.
        :type bundle_duration: int
        """

        self._bundle_duration = bundle_duration

    @property
    def country_code(self) -> List[str]:
        """Gets the country_code of this GetPlanHistorySuccessfulResponse.


        :return: The country_code of this GetPlanHistorySuccessfulResponse.
        :rtype: List[str]
        """
        return self._country_code

    @country_code.setter
    def country_code(self, country_code: List[str]):
        """Sets the country_code of this GetPlanHistorySuccessfulResponse.


        :param country_code: The country_code of this GetPlanHistorySuccessfulResponse.
        :type country_code: List[str]
        """

        self._country_code = country_code

    @property
    def country_name(self) -> List[str]:
        """Gets the country_name of this GetPlanHistorySuccessfulResponse.


        :return: The country_name of this GetPlanHistorySuccessfulResponse.
        :rtype: List[str]
        """
        return self._country_name

    @country_name.setter
    def country_name(self, country_name: List[str]):
        """Sets the country_name of this GetPlanHistorySuccessfulResponse.


        :param country_name: The country_name of this GetPlanHistorySuccessfulResponse.
        :type country_name: List[str]
        """

        self._country_name = country_name

    @property
    def iccid(self) -> str:
        """Gets the iccid of this GetPlanHistorySuccessfulResponse.


        :return: The iccid of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._iccid

    @iccid.setter
    def iccid(self, iccid: str):
        """Sets the iccid of this GetPlanHistorySuccessfulResponse.


        :param iccid: The iccid of this GetPlanHistorySuccessfulResponse.
        :type iccid: str
        """

        self._iccid = iccid

    @property
    def bundle_price(self) -> float:
        """Gets the bundle_price of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :return: The bundle_price of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._bundle_price

    @bundle_price.setter
    def bundle_price(self, bundle_price: float):
        """Sets the bundle_price of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :param bundle_price: The bundle_price of this GetPlanHistorySuccessfulResponse.
        :type bundle_price: float
        """

        self._bundle_price = bundle_price

    @property
    def bundle_price_in_additional_currency(self) -> float:
        """Gets the bundle_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :return: The bundle_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._bundle_price_in_additional_currency

    @bundle_price_in_additional_currency.setter
    def bundle_price_in_additional_currency(self, bundle_price_in_additional_currency: float):
        """Sets the bundle_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :param bundle_price_in_additional_currency: The bundle_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :type bundle_price_in_additional_currency: float
        """

        self._bundle_price_in_additional_currency = bundle_price_in_additional_currency

    @property
    def bundle_retail_price(self) -> float:
        """Gets the bundle_retail_price of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :return: The bundle_retail_price of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._bundle_retail_price

    @bundle_retail_price.setter
    def bundle_retail_price(self, bundle_retail_price: float):
        """Sets the bundle_retail_price of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :param bundle_retail_price: The bundle_retail_price of this GetPlanHistorySuccessfulResponse.
        :type bundle_retail_price: float
        """

        self._bundle_retail_price = bundle_retail_price

    @property
    def bundle_retail_price_in_additional_currency(self) -> float:
        """Gets the bundle_retail_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :return: The bundle_retail_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._bundle_retail_price_in_additional_currency

    @bundle_retail_price_in_additional_currency.setter
    def bundle_retail_price_in_additional_currency(self, bundle_retail_price_in_additional_currency: float):
        """Sets the bundle_retail_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :param bundle_retail_price_in_additional_currency: The bundle_retail_price_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :type bundle_retail_price_in_additional_currency: float
        """

        self._bundle_retail_price_in_additional_currency = bundle_retail_price_in_additional_currency

    @property
    def currency_code(self) -> str:
        """Gets the currency_code of this GetPlanHistorySuccessfulResponse.


        :return: The currency_code of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code: str):
        """Sets the currency_code of this GetPlanHistorySuccessfulResponse.


        :param currency_code: The currency_code of this GetPlanHistorySuccessfulResponse.
        :type currency_code: str
        """

        self._currency_code = currency_code

    @property
    def additional_currency_code(self) -> str:
        """Gets the additional_currency_code of this GetPlanHistorySuccessfulResponse.


        :return: The additional_currency_code of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._additional_currency_code

    @additional_currency_code.setter
    def additional_currency_code(self, additional_currency_code: str):
        """Sets the additional_currency_code of this GetPlanHistorySuccessfulResponse.


        :param additional_currency_code: The additional_currency_code of this GetPlanHistorySuccessfulResponse.
        :type additional_currency_code: str
        """

        self._additional_currency_code = additional_currency_code

    @property
    def matching_id(self) -> str:
        """Gets the matching_id of this GetPlanHistorySuccessfulResponse.


        :return: The matching_id of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._matching_id

    @matching_id.setter
    def matching_id(self, matching_id: str):
        """Sets the matching_id of this GetPlanHistorySuccessfulResponse.


        :param matching_id: The matching_id of this GetPlanHistorySuccessfulResponse.
        :type matching_id: str
        """

        self._matching_id = matching_id

    @property
    def smdp_address(self) -> str:
        """Gets the smdp_address of this GetPlanHistorySuccessfulResponse.


        :return: The smdp_address of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._smdp_address

    @smdp_address.setter
    def smdp_address(self, smdp_address: str):
        """Sets the smdp_address of this GetPlanHistorySuccessfulResponse.


        :param smdp_address: The smdp_address of this GetPlanHistorySuccessfulResponse.
        :type smdp_address: str
        """

        self._smdp_address = smdp_address

    @property
    def activation_code(self) -> str:
        """Gets the activation_code of this GetPlanHistorySuccessfulResponse.


        :return: The activation_code of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._activation_code

    @activation_code.setter
    def activation_code(self, activation_code: str):
        """Sets the activation_code of this GetPlanHistorySuccessfulResponse.


        :param activation_code: The activation_code of this GetPlanHistorySuccessfulResponse.
        :type activation_code: str
        """

        self._activation_code = activation_code

    @property
    def client_name(self) -> str:
        """Gets the client_name of this GetPlanHistorySuccessfulResponse.


        :return: The client_name of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._client_name

    @client_name.setter
    def client_name(self, client_name: str):
        """Sets the client_name of this GetPlanHistorySuccessfulResponse.


        :param client_name: The client_name of this GetPlanHistorySuccessfulResponse.
        :type client_name: str
        """

        self._client_name = client_name

    @property
    def client_email(self) -> str:
        """Gets the client_email of this GetPlanHistorySuccessfulResponse.


        :return: The client_email of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._client_email

    @client_email.setter
    def client_email(self, client_email: str):
        """Sets the client_email of this GetPlanHistorySuccessfulResponse.


        :param client_email: The client_email of this GetPlanHistorySuccessfulResponse.
        :type client_email: str
        """

        self._client_email = client_email

    @property
    def remaining_wallet_balance(self) -> float:
        """Gets the remaining_wallet_balance of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :return: The remaining_wallet_balance of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._remaining_wallet_balance

    @remaining_wallet_balance.setter
    def remaining_wallet_balance(self, remaining_wallet_balance: float):
        """Sets the remaining_wallet_balance of this GetPlanHistorySuccessfulResponse.

        in dollars  # noqa: E501

        :param remaining_wallet_balance: The remaining_wallet_balance of this GetPlanHistorySuccessfulResponse.
        :type remaining_wallet_balance: float
        """

        self._remaining_wallet_balance = remaining_wallet_balance

    @property
    def remaining_wallet_balance_in_additional_currency(self) -> float:
        """Gets the remaining_wallet_balance_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in additional  # noqa: E501

        :return: The remaining_wallet_balance_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :rtype: float
        """
        return self._remaining_wallet_balance_in_additional_currency

    @remaining_wallet_balance_in_additional_currency.setter
    def remaining_wallet_balance_in_additional_currency(self, remaining_wallet_balance_in_additional_currency: float):
        """Sets the remaining_wallet_balance_in_additional_currency of this GetPlanHistorySuccessfulResponse.

        in additional  # noqa: E501

        :param remaining_wallet_balance_in_additional_currency: The remaining_wallet_balance_in_additional_currency of this GetPlanHistorySuccessfulResponse.
        :type remaining_wallet_balance_in_additional_currency: float
        """

        self._remaining_wallet_balance_in_additional_currency = remaining_wallet_balance_in_additional_currency

    @property
    def date_created(self) -> datetime:
        """Gets the date_created of this GetPlanHistorySuccessfulResponse.


        :return: The date_created of this GetPlanHistorySuccessfulResponse.
        :rtype: datetime
        """
        return self._date_created

    @date_created.setter
    def date_created(self, date_created: datetime):
        """Sets the date_created of this GetPlanHistorySuccessfulResponse.


        :param date_created: The date_created of this GetPlanHistorySuccessfulResponse.
        :type date_created: datetime
        """

        self._date_created = date_created

    @property
    def refund_reason(self) -> str:
        """Gets the refund_reason of this GetPlanHistorySuccessfulResponse.


        :return: The refund_reason of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._refund_reason

    @refund_reason.setter
    def refund_reason(self, refund_reason: str):
        """Sets the refund_reason of this GetPlanHistorySuccessfulResponse.


        :param refund_reason: The refund_reason of this GetPlanHistorySuccessfulResponse.
        :type refund_reason: str
        """

        self._refund_reason = refund_reason

    @property
    def order_reference(self) -> str:
        """Gets the order_reference of this GetPlanHistorySuccessfulResponse.


        :return: The order_reference of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._order_reference

    @order_reference.setter
    def order_reference(self, order_reference: str):
        """Sets the order_reference of this GetPlanHistorySuccessfulResponse.


        :param order_reference: The order_reference of this GetPlanHistorySuccessfulResponse.
        :type order_reference: str
        """

        self._order_reference = order_reference

    @property
    def otp(self) -> str:
        """Gets the otp of this GetPlanHistorySuccessfulResponse.


        :return: The otp of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._otp

    @otp.setter
    def otp(self, otp: str):
        """Sets the otp of this GetPlanHistorySuccessfulResponse.


        :param otp: The otp of this GetPlanHistorySuccessfulResponse.
        :type otp: str
        """

        self._otp = otp

    @property
    def profile_expiry_date(self) -> str:
        """Gets the profile_expiry_date of this GetPlanHistorySuccessfulResponse.


        :return: The profile_expiry_date of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._profile_expiry_date

    @profile_expiry_date.setter
    def profile_expiry_date(self, profile_expiry_date: str):
        """Sets the profile_expiry_date of this GetPlanHistorySuccessfulResponse.


        :param profile_expiry_date: The profile_expiry_date of this GetPlanHistorySuccessfulResponse.
        :type profile_expiry_date: str
        """

        self._profile_expiry_date = profile_expiry_date

    @property
    def bundle_expiry_date(self) -> str:
        """Gets the bundle_expiry_date of this GetPlanHistorySuccessfulResponse.


        :return: The bundle_expiry_date of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._bundle_expiry_date

    @bundle_expiry_date.setter
    def bundle_expiry_date(self, bundle_expiry_date: str):
        """Sets the bundle_expiry_date of this GetPlanHistorySuccessfulResponse.


        :param bundle_expiry_date: The bundle_expiry_date of this GetPlanHistorySuccessfulResponse.
        :type bundle_expiry_date: str
        """

        self._bundle_expiry_date = bundle_expiry_date

    @property
    def order_type(self) -> str:
        """Gets the order_type of this GetPlanHistorySuccessfulResponse.


        :return: The order_type of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._order_type

    @order_type.setter
    def order_type(self, order_type: str):
        """Sets the order_type of this GetPlanHistorySuccessfulResponse.


        :param order_type: The order_type of this GetPlanHistorySuccessfulResponse.
        :type order_type: str
        """

        self._order_type = order_type

    @property
    def whatsapp_number(self) -> str:
        """Gets the whatsapp_number of this GetPlanHistorySuccessfulResponse.


        :return: The whatsapp_number of this GetPlanHistorySuccessfulResponse.
        :rtype: str
        """
        return self._whatsapp_number

    @whatsapp_number.setter
    def whatsapp_number(self, whatsapp_number: str):
        """Sets the whatsapp_number of this GetPlanHistorySuccessfulResponse.


        :param whatsapp_number: The whatsapp_number of this GetPlanHistorySuccessfulResponse.
        :type whatsapp_number: str
        """

        self._whatsapp_number = whatsapp_number

    @property
    def topup_an_expired_plan(self) -> bool:
        """Gets the topup_an_expired_plan of this GetPlanHistorySuccessfulResponse.


        :return: The topup_an_expired_plan of this GetPlanHistorySuccessfulResponse.
        :rtype: bool
        """
        return self._topup_an_expired_plan

    @topup_an_expired_plan.setter
    def topup_an_expired_plan(self, topup_an_expired_plan: bool):
        """Sets the topup_an_expired_plan of this GetPlanHistorySuccessfulResponse.


        :param topup_an_expired_plan: The topup_an_expired_plan of this GetPlanHistorySuccessfulResponse.
        :type topup_an_expired_plan: bool
        """

        self._topup_an_expired_plan = topup_an_expired_plan

    @property
    def has_related_order(self) -> bool:
        """Gets the has_related_order of this GetPlanHistorySuccessfulResponse.


        :return: The has_related_order of this GetPlanHistorySuccessfulResponse.
        :rtype: bool
        """
        return self._has_related_order

    @has_related_order.setter
    def has_related_order(self, has_related_order: bool):
        """Sets the has_related_order of this GetPlanHistorySuccessfulResponse.


        :param has_related_order: The has_related_order of this GetPlanHistorySuccessfulResponse.
        :type has_related_order: bool
        """

        self._has_related_order = has_related_order

    @property
    def has_related_active_topups(self) -> bool:
        """Gets the has_related_active_topups of this GetPlanHistorySuccessfulResponse.


        :return: The has_related_active_topups of this GetPlanHistorySuccessfulResponse.
        :rtype: bool
        """
        return self._has_related_active_topups

    @has_related_active_topups.setter
    def has_related_active_topups(self, has_related_active_topups: bool):
        """Sets the has_related_active_topups of this GetPlanHistorySuccessfulResponse.


        :param has_related_active_topups: The has_related_active_topups of this GetPlanHistorySuccessfulResponse.
        :type has_related_active_topups: bool
        """

        self._has_related_active_topups = has_related_active_topups

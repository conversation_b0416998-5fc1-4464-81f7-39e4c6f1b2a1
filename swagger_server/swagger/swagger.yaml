openapi: 3.0.0
info:
  title: Reseller API
  description: This is an OpenAPI for the Reseller Microservice
  version: "0.0"
servers:
- url: http://0.0.0.0:9005/api/v0
tags:
- name: Reseller
  description: Reseller Related Operations
- name: Branch
  description: Branch Related Operations
- name: Agent
  description: Agent Related Operations
- name: Bundles
  description: Bundles Related Operations
- name: Orders
  description: Orders Related Operations
- name: Role
  description: Role Related Operations
- name: Token
  description: Token Related Operations
- name: IssueReport
  description: IssueReport Related Operations
- name: NetworkList
  description: NetworkList Related Operations
paths:
  /HealthCheck:
    get:
      tags:
      - Welcome
      summary: Check Server
      description: a simple api to test if server is up and running
      operationId: health_check
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: string
                example: Welcome To Reseller Portal! The server is Up and Running.
                x-content-type: application/json
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.welcome_controller
  /Agent/login:
    post:
      tags:
      - Agent
      summary: Login Agent to Platform.
      description: Logs in User to Platform and returns access token.
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LoginRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoginResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Agent/forgot-password:
    post:
      tags:
      - Agent
      summary: Submit a forgot Password request.
      description: Allows the user to reset password via email.
      operationId: forgot_password
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ForgotPasswordRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ForgotPasswordResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Agent/reset-password:
    post:
      tags:
      - Agent
      summary: Resets User Password .
      description: Allows the user to reset password.
      operationId: reset_password
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PasswordResetRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PasswordResetResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Agent/logout:
    post:
      tags:
      - Agent
      summary: Logout Agent from Platform
      description: Logs out Agent from Platform.
      operationId: logout
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenRequest"
      responses:
        "200":
          description: Successful logout
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LogoutResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Agent:
    get:
      tags:
      - Agent
      summary: Gets all  Agents
      description: "Gets all Agents , can be called by PERSONNEL."
      operationId: get_agent
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: branch_id
        in: query
        description: branch id for super admin and reseller admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgentsResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
    post:
      tags:
      - Agent
      summary: Add Agent to Branch.
      description: "Adds a Agent to branch, can be called by PERSONNEL."
      operationId: add_agent
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: branch id for super admin and reseller admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddAgentRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddAgentResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Agent/{AgentID}:
    get:
      tags:
      - Agent
      summary: Gets Agent by ID
      description: "Gets a Agent by ID , can be called by PERSONNEL."
      operationId: get_agent_by_id
      parameters:
      - name: AgentID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AgentProfile"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
    put:
      tags:
      - Agent
      summary: Edit Agent
      description: Allows admins to edit an Agent
      operationId: edit_agent
      parameters:
      - name: AgentID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditAgentRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditAgentResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
    delete:
      tags:
      - Agent
      summary: Delete Agent
      description: Allows PERSONNEL to delete Agent.
      operationId: delete_agent
      parameters:
      - name: AgentID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteAgentResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      deprecated: true
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Token/Refresh:
    post:
      tags:
      - Token
      summary: Token Refresher
      description: Takes a refresh Token and grants the user a new Access token and
        refresh Token.
      operationId: refresh_token
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefreshTokenRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RefreshTokenResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.token_controller
  /Bundles/Cancel:
    post:
      tags:
      - Bundles
      summary: Cancel Order.
      description: Allow to cancel an order.
      operationId: cancel_bundle
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CancelBundleRequest"
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CancelBundleResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles/Reserve:
    post:
      tags:
      - Bundles
      summary: Reserve Bundle
      description: "Checks if the reseller has balance, and if the bundle is available,\
        \ and assigns it to a user."
      operationId: reserve_bundle
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ReserveBundleRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ReserveBundleResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles/Complete:
    post:
      tags:
      - Bundles
      summary: Complete transaction
      description: Complete transaction and send profile information
      operationId: complete_transaction
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompleteTransactionRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteTransactionResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles/CSV:
    get:
      tags:
      - Bundles
      summary: Get all country bundles Available CSV
      description: Can be called by users to view all bundles available
      operationId: get_bundles_of_country_csv
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_category
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - global
          - region
          - country
          - cruise
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                x-content-type: application/octet-stream
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /AvailableCountries:
    get:
      tags:
      - Bundles
      summary: Get all countries Available
      description: Can be called by users to view all counties available
      operationId: get_countries
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCountriesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /AvailableCurrenciesCSV:
    post:
      tags:
      - Bundles
      summary: Manage Currencies.
      description: Allows a Super admin to manage Currencies.
      operationId: manage_currencies_csv
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/AvailableCurrenciesCSV_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizeCurrenciesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /AvailableCurrencies:
    get:
      tags:
      - Bundles
      summary: Get all currencies Available
      description: Can be called by users to view all currencies available
      operationId: get_currencies
      parameters:
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCurrenciesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
    post:
      tags:
      - Bundles
      summary: Manage Currencies.
      description: Allows a Super admin to manage Currencies.
      operationId: manage_currencies
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomizeCurrenciesRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizeCurrenciesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /AvailableRegions:
    get:
      tags:
      - Bundles
      summary: Get all regions Available
      description: Can be called by users to view all regions available
      operationId: get_regions
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRegionsResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles:
    get:
      tags:
      - Bundles
      summary: Get all country bundles Available
      description: Can be called by users to view all bundles available
      operationId: get_bundles_of_country
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_category
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - global
          - region
          - country
          - cruise
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: bundle_tag
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: region_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          type: string
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      - name: bundle_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundlesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
    post:
      tags:
      - Bundles
      summary: Assign Bundle
      description: "Checks if the reseller has balance, and if the bundle is available,\
        \ and assigns it to a user."
      operationId: assign_bundle
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignBundleRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssignBundleResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles/Networks:
    get:
      tags:
      - Bundles
      summary: Gets the bundle network list
      description: "Gets the bundle network list , can be called by users."
      operationId: get_bundle_network_list
      parameters:
      - name: bundle_code
        in: query
        required: true
        style: form
        explode: true
        schema:
          type: string
          example: GBR_1213202219550292
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BundleNetworkList"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Bundles/Topup:
    get:
      tags:
      - Bundles
      summary: Get compatible topup bundles
      description: Allows the user to view all bundles that are compatible with the
        Esim Profile Installed.
      operationId: get_compatible_topup_bundles
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_code
        in: query
        required: true
        style: form
        explode: true
        schema:
          type: string
          example: GBR_1213202219550292
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: bundle_tag
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundlesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      deprecated: true
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
    post:
      tags:
      - Bundles
      summary: Topup Bundle
      description: "Checks if the reseller has balance, and if the bundle is available,\
        \ topups the bundle of the user."
      operationId: topup_bundle
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: branch id for super admin and reseller admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TopupBundleRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TopupBundleResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /v2/Bundles/Topup:
    get:
      tags:
      - Bundles
      summary: Get compatible topup bundles
      description: Allows the user to view all bundles that are compatible with the
        Esim Profile Installed.
      operationId: get_compatible_topup_bundles_v2
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_code
        in: query
        required: true
        style: form
        explode: true
        schema:
          type: string
          example: GBR_1213202219550292
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: bundle_tag
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: order_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: previous_order_reference
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
          description: previous custom order reference to topup bundle
      - name: region_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          type: string
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundlesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Reseller:
    get:
      tags:
      - Reseller
      summary: Gets all  Resellers
      description: "Gets all Resellers , can be called by PERSONNEL."
      operationId: get_reseller
      parameters:
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: dropdown
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: reseller_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: supports_promo
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: supports_voucher
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      - name: is_whitelabel
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: reseller_category
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetResellersResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
    post:
      tags:
      - Reseller
      summary: Adds a Reseller to platform.
      description: "Adds a Reseller to platform, can be called by PERSONNEL."
      operationId: add_reseller
      parameters:
      - name: currency_code
        in: query
        description: currency_code for operation
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddResellerRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddResellerResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/Admin/custom_corporate_price:
    post:
      tags:
      - Reseller
      summary: Customize Bundle Unit Price.
      description: Allows a reseller agent to customize bundle retail price.
      operationId: customize_corporate_price
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: true
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomizeCorpPriceRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizePriceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/Admin/custom_corporate_price_csv:
    post:
      tags:
      - Reseller
      summary: Customize Bundle Unit Price CSV file.
      description: Allows a reseller agent to customize bundle retail price by CSV
        files.
      operationId: customize_corporate_price_csv
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: true
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/Admin_custom_corporate_price_csv_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizePriceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/custom_price:
    post:
      tags:
      - Reseller
      summary: Customize Bundle Price.
      description: Allows a reseller agent to customize bundle retail price.
      operationId: customize_price
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomizePriceRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizePriceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/custom_price_csv:
    post:
      tags:
      - Reseller
      summary: Customize Bundle Price.
      description: Allows a reseller agent to customize bundle retail price via Excel
        CSV File.
      operationId: customize_price_csv
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/Reseller_custom_price_csv_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomizePriceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/Topup/{ResellerID}:
    post:
      tags:
      - Reseller
      summary: Topup Reseller Balance.
      description: Allows a monty agent to topup reseller wallet balance.
      operationId: topup_reseller_balance
      parameters:
      - name: ResellerID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TopupBalanceRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TopupBalanceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Reseller/{ResellerID}:
    get:
      tags:
      - Reseller
      summary: Gets Reseller by ID
      description: "Gets a Reseller by ID , can be called by PERSONNEL."
      operationId: get_reseller_by_id
      parameters:
      - name: ResellerID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetResellerResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
    put:
      tags:
      - Reseller
      summary: Edit Reseller
      description: Allows admins to edit Reseller
      operationId: edit_reseller
      parameters:
      - name: ResellerID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditResellerRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditResellerResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
    delete:
      tags:
      - Reseller
      summary: Delete Reseller
      description: Allows PERSONNEL to delete Reseller.
      operationId: delete_reseller
      parameters:
      - name: ResellerID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteResellerResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Branch:
    get:
      tags:
      - Branch
      summary: Gets all  Branches
      description: "Gets all Branches , can be called by PERSONNEL."
      operationId: get_branch
      parameters:
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: dropdown
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBranchesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.branch_controller
    post:
      tags:
      - Branch
      summary: Adds a Branch to platform.
      description: "Adds a Branch to platform, can be called by Reseller Admin."
      operationId: add_branch
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddBranchRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddBranchResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.branch_controller
  /Branch/{BranchID}:
    get:
      tags:
      - Branch
      summary: Gets Branch by ID
      description: "Gets a Branch by ID , can be called by PERSONNEL."
      operationId: get_branch_by_id
      parameters:
      - name: BranchID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBranchResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.branch_controller
    put:
      tags:
      - Branch
      summary: Edit Branch
      description: Allows admins to edit Branch
      operationId: edit_branch
      parameters:
      - name: BranchID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditBranchRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditBranchResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.branch_controller
    delete:
      tags:
      - Branch
      summary: Delete Branch
      description: Allows PERSONNEL to delete Branch.
      operationId: delete_branch
      parameters:
      - name: BranchID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteBranchResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.branch_controller
  /Transactions:
    get:
      tags:
      - Orders
      summary: Get Transaction History
      description: Can be called to view reseller transaction history.
      operationId: get_transaction_history
      parameters:
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: show_branches
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTransactionHistoryResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Transactions/{TransactionID}:
    put:
      tags:
      - Orders
      summary: Edit Transaction
      description: Allows Super admin to edit a Transaction
      operationId: edit_transaction
      parameters:
      - name: TransactionID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: currency_code
        in: query
        description: currency_code to preview
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TopupBalanceRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditTopupBalanceResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders:
    get:
      tags:
      - Orders
      summary: Get Order History
      description: Can be called to view reseller order history.
      operationId: get_order_history
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
          example: USA
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: order_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: order_reference
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: search
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: fields_to_search
        in: query
        description: |
          JSON array of fields to search within (e.g., ["Order Status", "Order Date"]).
        required: false
        style: form
        explode: true
        schema:
          uniqueItems: true
          type: array
          items:
            type: string
            enum:
            - order_status
            - bundle_name
            - client_email
            - client_name
            - bundle_price
            - bundle_retail_price
            - whatsapp_number
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetOrderHistoryResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/Dashboard:
    get:
      tags:
      - Orders
      summary: Get Dashboard
      description: "Can be called to view reseller bundles sold chard, top 5 bundles,\
        \ and sales volume."
      operationId: get_dashboard
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
          example: USA
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: branch id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDashboardResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Promocode:
    get:
      tags:
      - Orders
      summary: Get Promocode Order History
      description: Can be called to view reseller promocode order history.
      operationId: get_promocode_history
      parameters:
      - name: promocode
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: customer_name
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
      - name: affiliate_program
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPromocodeHistoryResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Affiliate:
    get:
      tags:
      - Affiliate
      summary: Get Affiliate Program
      description: Can be called to view reseller Affiliate Program.
      operationId: get_affiliate_program
      parameters:
      - name: affiliate_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAffiliateProgramResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.affiliate_controller
  /Promocode/Dashboard:
    get:
      tags:
      - Orders
      summary: Get Promocode Dashboard
      description: Can be called to view reseller promocode bundles sold chart
      operationId: get_promocode_dashboard
      parameters:
      - name: promocode
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: branch_id
        in: query
        description: branch id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: affiliate_program
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPromoDashboardResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/Consumption:
    get:
      tags:
      - Orders
      summary: Get Bundle Consumption
      description: Can be called to view user bundle consumption and status.
      operationId: get_bundle_consumption
      parameters:
      - name: order_id
        in: query
        description: order id
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: order_reference
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundleConsumptionResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/MyeSimConsumption:
    get:
      tags:
      - Orders
      summary: Get my Bundle Consumption
      description: Can be called to view user bundle consumption and status.
      operationId: get_my_bundle_consumption
      parameters:
      - name: order_id
        in: query
        description: order id
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: otp
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
          format: uuid
      - name: order_reference
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetMyBundleConsumptionResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/ResendEmail:
    post:
      tags:
      - Orders
      summary: Resend Order Email.
      description: Allows User to Resend an order email to deliver invoice and qr
        code.
      operationId: resend_order_email
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Orders_ResendEmail_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResendOrderEmailResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/SendConsumptionEmail:
    post:
      tags:
      - Orders
      summary: Send Consumption Email.
      description: Allows User to Send Consumption Email to the user upon certain
        thresholds.
      operationId: send_consumption_email
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: notification_type
        in: query
        description: reseller id for super admin
        required: true
        style: form
        explode: true
        schema:
          type: string
          enum:
          - Expired
          - Eighty
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Orders_SendConsumptionEmail_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResendOrderEmailResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Orders/Refund:
    post:
      tags:
      - Orders
      summary: Refund Order.
      description: Allows SuperAdmin to Refund an order.
      operationId: refund_order
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RefundOrderRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RefundOrderResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Role/{RoleID}:
    get:
      tags:
      - Role
      summary: Returns specific role by ID
      description: Returns specific role by ID
      operationId: get_role_by_id
      parameters:
      - name: RoleID
        in: path
        description: id value of role that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Returned Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRoleResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
    put:
      tags:
      - Role
      summary: Edit Role
      description: Allows admins to edit a Role
      operationId: edit_role
      parameters:
      - name: RoleID
        in: path
        description: id value of role that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditRoleRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditRoleResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
    delete:
      tags:
      - Role
      summary: Deletes specific role by ID
      description: Deletes specific role by ID
      operationId: delete_role
      parameters:
      - name: RoleID
        in: path
        description: id value of role that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Deleted Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteRoleResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
  /Role/All:
    get:
      tags:
      - Role
      summary: Returns all roles in platform
      description: "Returns a list of roles on the platform. Gives the user the role's\
        \ name, id and description."
      operationId: get_all_roles
      responses:
        "200":
          description: Returned Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAllRolesResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
  /Role:
    post:
      tags:
      - Role
      summary: Creates a new role.
      description: Takes a role object and inserts it into the DB
      operationId: create_role
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateRoleRequest"
      responses:
        "201":
          description: Created Role Successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateRoleResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
  /Roles/UMAP:
    patch:
      tags:
      - Role
      summary: Updates Monty Admin Permissions.
      description: Updates Monty Admin Role with all Permissions Available.
      operationId: update_monty_admin_permissions
      responses:
        "200":
          description: Added APIs to role's permissions successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateAdminPermissionsResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.role_controller
  /Voucher/Generate:
    post:
      tags:
      - Voucher
      summary: Generate Voucher
      description: "Checks if the reseller has balance, and generates a voucher"
      operationId: generate_voucher
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GenerateVoucherRequest"
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GenerateVoucherResponse"
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.voucher_controller
  /Voucher/History:
    get:
      tags:
      - Voucher
      summary: Get Voucher Use History
      description: Can be called to view reseller Voucher Use history.
      operationId: get_voucher_code_history
      parameters:
      - name: voucher_name
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: username
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetVoucherUseHistoryResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.voucher_controller
  /Voucher/Details:
    get:
      tags:
      - Voucher
      summary: Get Vouchers Generated
      description: Can be called to view reseller Vouchers generated.
      operationId: get_voucher_codes
      parameters:
      - name: voucher_name
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: available
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetVouchersResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.voucher_controller
    patch:
      tags:
      - Voucher
      summary: Updates Voucher Attributes
      description: Updates Voucher Attributes.
      operationId: patch_voucher
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: voucher_id
        in: query
        description: voucher id
        required: true
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateVoucherAttributesRequest"
      responses:
        "200":
          description: Updated Voucher Successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateVoucherAttributesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.voucher_controller
  /Voucher:
    get:
      tags:
      - Voucher
      summary: Get Vouchers Generated bundled in voucher names
      description: Can be called to view reseller Vouchers generated.
      operationId: get_vouchers
      parameters:
      - name: voucher_name
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetVouchersBundledResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.voucher_controller
  /IssueReport:
    get:
      tags:
      - IssueReport
      summary: Gets all  Issues Reported
      description: "Gets all Issues Reported , can be called by PERSONNEL."
      operationId: get_issue_report
      parameters:
      - name: IssueReportID
        in: query
        description: id value that needs to be considered for filter
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: show_branches
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: search
        in: query
        required: false
        style: form
        explode: true
        schema:
          pattern: "^[^\\[\\]\\{\\}]*$"
          type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IssueReport"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
    post:
      tags:
      - IssueReport
      summary: Submit an Issue Report.
      description: Submit a technical issue or pricing concern report.
      operationId: add_issue_report
      parameters:
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddIssueReportRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AddIssueReportResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
  /IssueReport/{ReportID}:
    put:
      tags:
      - IssueReport
      summary: Modify Issue Report
      description: Manage the issue report
      operationId: edit_issue_report
      parameters:
      - name: ReportID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditIssueReportRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditIssueReportResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
    delete:
      tags:
      - IssueReport
      summary: Delete IssueReport
      description: Allows PERSONNEL to delete IssueReport.
      operationId: delete_issue_report
      parameters:
      - name: ReportID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteIssueReportResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
  /ResolveIssue/{ReportID}:
    put:
      tags:
      - IssueReport
      summary: Resolve an Issue Report.
      description: Resolve and finalize an Issue Report.
      operationId: resolve_issue
      parameters:
      - name: ReportID
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          type: string
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ResolveIssueRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResolveIssueResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
  /IssueReport/{ReportID}/Feedback:
    put:
      tags:
      - IssueReport
      summary: Submit Feedback for Issue Resolution
      description: Submit feedback on the satisfaction of issue resolution.
      operationId: submit_feedback
      parameters:
      - name: ReportID
        in: path
        description: ID of the issue report.
        required: true
        style: simple
        explode: false
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitFeedbackRequest"
        required: true
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubmitFeedbackResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.issue_report_controller
  /AllNetworkList:
    get:
      tags:
      - NetworkList
      summary: Gets all Network lists from for all bundles
      description: "Gets all Network lists , can be called by users."
      operationId: get_all_network_list
      parameters:
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AllNetworkList_networksResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
  /NetworkList:
    get:
      tags:
      - NetworkList
      summary: Gets all Network lists
      description: "Gets all Network lists , can be called by users."
      operationId: get_network_list
      parameters:
      - name: network_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetworkList"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
    post:
      tags:
      - NetworkList
      summary: manage network lists to branch.
      description: "Adds a NetworkList to branch, can be called by PERSONNEL."
      operationId: manage_network_list
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ManageNetworkListRequest"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ManageNetworkListResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
  /NetworkList/{network_id}:
    delete:
      tags:
      - NetworkList
      summary: Delete NetworkList
      description: Allows admin to delete a Network.
      operationId: delete_network_list
      parameters:
      - name: network_id
        in: path
        description: id value that needs to be considered for filter
        required: true
        style: simple
        explode: false
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteNetworkListResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
  /NetworkListCSV:
    post:
      tags:
      - NetworkList
      summary: Manage Networks CSV.
      description: Allows a Super admin to manage Networks Via CSV File.
      operationId: manage_networks_csv
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/NetworkListCSV_body"
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ManageNetworkListResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
  /Bundles/AvailableTopup:
    get:
      tags:
      - Bundles
      summary: Get available topup bundles
      description: Allows the user to view all bundles that are available with the
        Esim Profile Installed.
      operationId: get_available_topup_bundles
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_code
        in: query
        required: true
        style: form
        explode: true
        schema:
          type: string
          example: GBR_1213202219550292
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: bundle_tag
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: region_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          type: string
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundlesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Reseller/Bundles/Scope:
    get:
      tags:
      - Bundles
      summary: "Get reseller bundles grouped by scope (region, countries, or global)"
      description: |
        Returns bundles grouped by their availability scope: region, country, or global.
        **Filtering Rules**: - Use the `filter` query parameter to specify grouping scope: `region`, `countries`, or `global`. - Pagination is supported via `page` and `limit`.
        **Region Rules**: - Bundles are grouped under one of the following display regions: Africa, Asia, Europe, Middle East, North America, South America. - **Turkey** is grouped under Europe by default. But if a bundle contains **Turkey and a Middle Eastern country**, it is grouped under Middle East. - **Oceania** countries are grouped under **Asia**. - **Central America** and **Caribbean** countries are grouped under **South America**. - Bundles are **never duplicated across regions**—each belongs to one region only.
        **Global Rules**: - Bundles in the `global` scope must cover **more than 50 countries**.
        **Data size input rules**: - If the value is **less than 1**, it is multiplied by 1000 and treated as megabytes. - If the value is **1 or more**, it is treated as gigabytes.
        **Examples**: - To filter for **500 MB**, pass `0.5` - To filter for **500 GB**, pass `500`
      operationId: get_reseller_bundles_by_scope
      parameters:
      - name: geoscope
        in: query
        description: |
          Determines how bundles are grouped: - `region`: groups bundles under regions like Europe, Asia, etc. - `countries`: returns all bundles scoped to specific countries, regions, or global (flat list). - `global`: returns only global bundles covering over 50 countries.
        required: true
        style: form
        explode: true
        schema:
          type: string
          enum:
          - region
          - countries
          - global
      - name: page
        in: query
        description: Page number for pagination.
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
          default: 1
      - name: limit
        in: query
        description: Maximum number of bundles per page.
        required: false
        style: form
        explode: true
        schema:
          maximum: 100
          minimum: 1
          type: integer
          default: 20
      - name: data_size_min
        in: query
        description: |
          Minimum data size (e.g., "0.5" for 500MB or "1" for 1GB).  If value < 1, it is multiplied by 1000 and treated as megabytes.  If >= 1, it is treated as gigabytes.
        required: false
        style: form
        explode: true
        schema:
          pattern: ^\d+(\.\d+)?$
          type: string
      - name: data_size_max
        in: query
        description: |
          Maximum data size (e.g., "0.5" for 500MB or "1" for 1GB).  If value < 1, it is multiplied by 1000 and treated as megabytes.  If >= 1, it is treated as gigabytes.
        required: false
        style: form
        explode: true
        schema:
          pattern: ^\d+(\.\d+)?$
          type: string
      - name: validity_days_min
        in: query
        description: Minimum validity period in days.
        required: false
        style: form
        explode: true
        schema:
          minimum: 0
          type: integer
      - name: validity_days_max
        in: query
        description: Maximum validity period in days.
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: price_min
        in: query
        description: Minimum bundle price.
        required: false
        style: form
        explode: true
        schema:
          pattern: ^\d+(\.\d+)?$
          type: string
      - name: price_max
        in: query
        description: Maximum bundle price.
        required: false
        style: form
        explode: true
        schema:
          pattern: ^\d+(\.\d+)?$
          type: string
      responses:
        "200":
          description: Successful response with bundles grouped by region or as a
            flat list
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_200"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /Reseller/AvailableResellerProperties:
    get:
      tags:
      - Reseller
      summary: Get available properties of resellers
      description: Allows the user to view all properties that are available for resellers.
      operationId: available_reseller_properties
      parameters:
      - name: category_names_list
        in: query
        description: category names list of resellers
        required: false
        style: form
        explode: true
        schema:
          uniqueItems: true
          type: array
          items:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAvailableResellerPropertiesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.reseller_controller
  /Agent/GetAgentByEmail:
    post:
      tags:
      - Agent
      summary: Gets Agent by Email
      description: "Gets a Agent by Email, can be called by PERSONNEL."
      operationId: get_agent_by_email
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetAgentByEmailRequest"
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAgentResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.agent_controller
  /Orders/PlanHistory:
    get:
      tags:
      - Orders
      summary: Get Plan History
      description: Can be called to view reseller order history.
      operationId: get_plan_history
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
          example: USA
      - name: startDate
        in: query
        description: beginning date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: endDate
        in: query
        description: end date for filtering the order
        required: false
        style: form
        explode: true
        schema:
          maxLength: 50
          minLength: 0
          type: string
          format: date-time
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: branch_id
        in: query
        description: "branch id for super admin, reseller admin"
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: order_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: order_reference
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: search
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
      - name: fields_to_search
        in: query
        description: |
          JSON array of fields to search within (e.g., ["Order Status", "Order Date"]).
        required: false
        style: form
        explode: true
        schema:
          uniqueItems: true
          type: array
          items:
            type: string
            enum:
            - order_status
            - bundle_name
            - client_email
            - client_name
            - bundle_price
            - bundle_retail_price
            - whatsapp_number
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      - name: iccid
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          pattern: "^\\d{18,20}$"
          type: string
          example: "8943108161000050000"
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPlanHistoryResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.orders_controller
  /Bundles/V2:
    get:
      tags:
      - Bundles
      summary: Get all country bundles Available
      description: Can be called by users to view all bundles available
      operationId: get_bundles_of_country_v2
      parameters:
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          minLength: 3
          type: string
      - name: bundle_category
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - global
          - region
          - country
          - cruise
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: reseller_id
        in: query
        description: reseller id for super admin
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: bundle_name
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 100
          type: string
      - name: sort_by
        in: query
        description: sorting parameter
        required: false
        style: form
        explode: true
        schema:
          type: string
          enum:
          - price_asc
          - price_dsc
          - bundle_name
          - data_asc
          - data_dsc
      - name: reseller_admin_view
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      - name: bundle_tag
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: region_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          type: string
      - name: currency_code
        in: query
        description: currency code to preview in
        required: false
        style: form
        explode: true
        schema:
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          type: string
          example: SAR
      - name: bundle_code
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          maxLength: 30
          type: string
      - name: country_code_array
        in: query
        description: filter parameter
        required: false
        style: form
        explode: true
        schema:
          type: array
          items:
            type: string
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBundlesResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.bundles_controller
  /NetworkListByRegions:
    get:
      tags:
      - NetworkList
      summary: Gets all Network lists
      description: "Gets all Network lists , can be called by users."
      operationId: get_network_list_by_regions
      parameters:
      - name: network_id
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      - name: country_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          type: string
      - name: region_code
        in: query
        required: false
        style: form
        explode: true
        schema:
          maxLength: 3
          type: string
      - name: page_size
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100
      - name: page_number
        in: query
        required: false
        style: form
        explode: true
        schema:
          minimum: 1
          type: integer
      - name: export
        in: query
        required: false
        style: form
        explode: true
        schema:
          type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/NetworkListByRegionsResponse"
        "400":
          description: Bad Request
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "403":
          description: Forbidden
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
        "204":
          description: Not Found'
          headers:
            Cache-Control:
              style: simple
              explode: false
              schema:
                type: string
                default: no-store
                enum:
                - no-store
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/inline_response_204"
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      x-openapi-router-controller: swagger_server.controllers.network_list_controller
components:
  schemas:
    ResponseEntity:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
      additionalProperties: false
    ObjectID:
      maxLength: 24
      minLength: 24
      pattern: "^[0-9a-fA-F]{24}$"
      type: string
      format: hex
      example: 507f191e810c19729de860ea
    Role:
      required:
      - name
      - role_id
      type: object
      properties:
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        name:
          maxLength: 20
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
        description:
          maxLength: 2000
          minLength: 0
          pattern: "^[a-zA-Z .,']+$"
          type: string
          example: Has Full Authority Over Reseller
      additionalProperties: false
    DeleteRoleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: This is a message
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: This is a message
    CreateRoleRequest:
      required:
      - access_level
      - description
      - name
      - permission_level
      - permissions
      type: object
      properties:
        name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        access_level:
          type: string
          example: basic
          enum:
          - basic
          - medium
          - sensitive
        description:
          maxLength: 2000
          minLength: 0
          pattern: "^[a-zA-Z .,']+$"
          type: string
          example: Has Full Authority Over Reseller
        permissions:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CreateRoleRequest_permissions"
      additionalProperties: false
    CreateRoleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: This is a message
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        role_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: This is a message
    GetRoleResponse:
      required:
      - access_level
      - name
      - permissions
      - role_id
      type: object
      properties:
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        name:
          maxLength: 20
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        access_level:
          type: string
          example: basic
          enum:
          - basic
          - medium
          - sensitive
        description:
          maxLength: 2000
          minLength: 0
          pattern: "^[a-zA-Z .,']+$"
          type: string
          example: Has Full Authority Over Reseller
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/CreateRoleRequest_permissions"
      additionalProperties: false
      example:
        access_level: basic
        permission_level: 1
        role_id: 507f191e810c19729de860ea
        permissions:
        - permission_type: Self
          api_name: topupBundle
        - permission_type: Self
          api_name: topupBundle
        name: ResellerAdmin
        description: Has Full Authority Over Reseller
    GetAllRolesResponse:
      minItems: 0
      type: array
      items:
        $ref: "#/components/schemas/GetAllRolesResponse_inner"
    EditRoleRequest:
      type: object
      properties:
        name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        access_level:
          type: string
          example: basic
          enum:
          - basic
          - medium
          - sensitive
        description:
          maxLength: 2000
          minLength: 0
          pattern: "^[a-zA-Z .,']+$"
          type: string
          example: Has Full Authority Over Reseller
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/CreateRoleRequest_permissions"
      additionalProperties: false
    EditRoleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Role Edited Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Role Edited Successfully
    UpdateAdminPermissionsResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Monty Admin Role Updated Successfully.
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        role_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: Monty Admin Role Updated Successfully.
    LoginRequest:
      required:
      - password
      - username
      type: object
      properties:
        username:
          type: string
          example: admin
        password:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})"
          type: string
          writeOnly: true
          example: $3343JcS24
      additionalProperties: false
    ForgotPasswordRequest:
      required:
      - email
      type: object
      properties:
        email:
          pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
          type: string
          example: <EMAIL>
        captcha_token:
          type: string
          example: 03Aeyuqwe978qwy7e7wqyeiouy21ey710ewquiey8712
      additionalProperties: false
    ForgotPasswordResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Password Reset Link Sent to email
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Password Reset Link Sent to email
    PasswordResetRequest:
      required:
      - password
      - password_reset_identifier
      type: object
      properties:
        password_reset_identifier:
          pattern: "^\\d{25}$"
          type: string
          example: "1129037324695099832474129"
        password:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
        password_confirmation:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
      additionalProperties: false
    PasswordResetResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: "Password Reset Successfully, please login to your account."
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: "Password Reset Successfully, please login to your account."
    LoginResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: "Credentials Valid, User Logged In"
        access_token:
          type: string
          example: aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw
        refresh_token:
          type: string
          example: eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        token_type:
          type: string
          enum:
          - ApiKey
        expires_in:
          minimum: 300
          type: integer
        refresh_expires_in:
          minimum: 1800
          type: integer
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        role_name:
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        reseller_currency_code:
          maxLength: 15
          type: string
      additionalProperties: false
      example:
        response_code: "1"
        reseller_currency_code: reseller_currency_code
        agent_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: "Credentials Valid, User Logged In"
        token_type: ApiKey
        access_token: aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw
        role_name: ResellerAdmin
        refresh_token: eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog
        permission_level: 1
        refresh_expires_in: 1800
        supports_promo: true
        supports_vouchers: true
        expires_in: 300
        reseller_id: 507f191e810c19729de860ea
    RefreshTokenResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: "Credentials Valid, Access Token Refreshed"
        access_token:
          type: string
          example: aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw
        refresh_token:
          type: string
          example: eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        token_type:
          type: string
          enum:
          - ApiKey
        expires_in:
          minimum: 300
          type: integer
        refresh_expires_in:
          minimum: 1800
          type: integer
        role_name:
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        reseller_currency_code:
          maxLength: 15
          type: string
      additionalProperties: false
      example:
        response_code: "1"
        reseller_currency_code: reseller_currency_code
        developer_message: Operation Succcessful
        title: Success
        message: "Credentials Valid, Access Token Refreshed"
        token_type: ApiKey
        access_token: aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw
        role_name: ResellerAdmin
        refresh_token: eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog
        permission_level: 1
        refresh_expires_in: 1800
        supports_promo: true
        supports_vouchers: true
        expires_in: 300
    LogoutResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Log Out Successful
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Log Out Successful
    RefreshTokenRequest:
      required:
      - refresh_token
      type: object
      properties:
        refresh_token:
          maxLength: 2048
          minLength: 20
          pattern: "^[A-Za-z0-9-_=.]+$"
          type: string
          nullable: false
          example: aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw
      additionalProperties: false
    CheckTokenResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Token Valid
        allowed_microservices:
          type: object
      additionalProperties: false
    GetBundlesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        bundles:
          type: array
          items:
            $ref: "#/components/schemas/GetBundlesResponse_bundles"
        total_bundles_count:
          type: integer
          example: 1000
      additionalProperties: false
      example:
        response_code: "1"
        total_bundles_count: 1000
        developer_message: Operation Succcessful
        bundles:
        - is_active_corp: true
          reseller_retail_price: 20.7
          bundle_tag:
          - bundle_tag
          - bundle_tag
          currency_code_list:
          - USD
          - USD
          subscriber_price_in_additional_currency: 16.7
          is_active: true
          unlimited: true
          gprs_limit: 10
          reseller_retail_price_in_additional_currency: 20.7
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          data_unit: MB
          bundle_category: bundle_category
          country_code:
          - USA
          - USA
          supplier_vendor: supplier_vendor
          bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          subscriber_price: 16.7
          support_topup: true
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          country_name:
          - United States
          - United States
          region_name: Asia
          validity: 30
          additional_currency_code: SAR
          refill_group: refill_group
          region_code: AS
        - is_active_corp: true
          reseller_retail_price: 20.7
          bundle_tag:
          - bundle_tag
          - bundle_tag
          currency_code_list:
          - USD
          - USD
          subscriber_price_in_additional_currency: 16.7
          is_active: true
          unlimited: true
          gprs_limit: 10
          reseller_retail_price_in_additional_currency: 20.7
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          data_unit: MB
          bundle_category: bundle_category
          country_code:
          - USA
          - USA
          supplier_vendor: supplier_vendor
          bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          subscriber_price: 16.7
          support_topup: true
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          country_name:
          - United States
          - United States
          region_name: Asia
          validity: 30
          additional_currency_code: SAR
          refill_group: refill_group
          region_code: AS
        title: Success
    GetCountriesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        countries:
          type: array
          items:
            $ref: "#/components/schemas/GetCountriesResponse_countries"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        countries:
        - iso3_code: FRA
          iso2_code: FR
          country_name: France
        - iso3_code: FRA
          iso2_code: FR
          country_name: France
        title: Success
    GetCurrenciesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        currencies:
          type: array
          items:
            $ref: "#/components/schemas/GetCurrenciesResponse_currencies"
        total_currencies_count:
          minimum: 0
          type: integer
      additionalProperties: false
      example:
        total_currencies_count: 0
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        currencies:
        - currency_rate: 0.08008282
          currency name: Saudi Arabian Rial
          last_modified: 2000-01-23
          currency_code: SAR
          is_available: true
        - currency_rate: 0.08008282
          currency name: Saudi Arabian Rial
          last_modified: 2000-01-23
          currency_code: SAR
          is_available: true
    GetRegionsResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        regions:
          type: array
          items:
            $ref: "#/components/schemas/GetRegionsResponse_regions"
      additionalProperties: false
      example:
        response_code: "1"
        regions:
        - region_name: France
          region_code: FRA
        - region_name: France
          region_code: FRA
        developer_message: Operation Succcessful
        title: Success
    GetBundleConsumptionResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        status:
          type: boolean
        data_allocated:
          type: number
          format: float
          example: 5000
        data_used:
          type: number
          format: float
          example: 5000
        data_remaining:
          type: number
          format: float
          example: 5000
        data_unit:
          type: string
          example: MB
          enum:
          - MB
          - GB
          - TB
        policy_status:
          type: string
        plan_status:
          type: string
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        cached_at:
          type: string
        profile_status:
          type: string
        unlimited:
          type: boolean
          default: false
      additionalProperties: false
      example:
        response_code: "1"
        data_used: 5000
        developer_message: Operation Succcessful
        unlimited: false
        policy_status: policy_status
        data_remaining: 5000
        title: Success
        data_unit: MB
        profile_status: profile_status
        profile_expiry_date: profile_expiry_date
        bundle_expiry_date: bundle_expiry_date
        cached_at: cached_at
        data_allocated: 5000
        status: true
        plan_status: plan_status
    GetMyBundleConsumptionResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        status:
          type: boolean
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        data_allocated:
          type: number
          format: float
          example: 5000
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        logo_uri:
          type: string
        data_used:
          type: number
          format: float
          example: 5000
        data_remaining:
          type: number
          format: float
          example: 5000
        data_unit:
          type: string
          example: MB
          enum:
          - MB
          - GB
          - TB
        policy_status:
          type: string
        plan_status:
          type: string
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        profile_status:
          type: string
        unlimited:
          type: boolean
          default: false
        cached_at:
          type: string
      additionalProperties: false
      example:
        response_code: "1"
        data_used: 5000
        developer_message: Operation Succcessful
        logo_uri: logo_uri
        unlimited: false
        policy_status: policy_status
        data_remaining: 5000
        bundle_name: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        title: Success
        data_unit: MB
        profile_status: profile_status
        iccid: iccid
        profile_expiry_date: profile_expiry_date
        bundle_expiry_date: bundle_expiry_date
        cached_at: cached_at
        data_allocated: 5000
        status: true
        plan_status: plan_status
    AssignBundleRequest:
      type: object
      properties:
        order_reference:
          maxLength: 30
          type: string
        bundle_code:
          type: string
          example: USA3GB_3102938429
        whatsapp_number:
          type: string
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        name:
          type: string
          example: John Wick
      additionalProperties: false
    AssignBundleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Bundle Assigned Successfully
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 30
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional currency
          format: float
          example: 30
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
      additionalProperties: false
      example:
        remaining_wallet_balance: 30
        response_code: "1"
        iccid: iccid
        developer_message: Operation Succcessful
        title: Success
        message: Bundle Assigned Successfully
        remaining_wallet_balance_in_additional_currency: 30
        additional_currency_code: SAR
        order_id: 507f191e810c19729de860ea
        reseller_id: 507f191e810c19729de860ea
    ReserveBundleRequest:
      type: object
      properties:
        order_reference:
          maxLength: 100
          type: string
        bundle_code:
          type: string
          example: USA3GB_3102938429
        whatsapp_number:
          type: string
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: John Wick
      additionalProperties: false
    ReserveBundleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Bundle Reserved Successfully
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 30
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional currency
          format: float
          example: 30
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
      additionalProperties: false
      example:
        remaining_wallet_balance: 30
        response_code: "1"
        iccid: iccid
        developer_message: Operation Succcessful
        title: Success
        message: Bundle Reserved Successfully
        remaining_wallet_balance_in_additional_currency: 30
        additional_currency_code: SAR
        order_id: 507f191e810c19729de860ea
        reseller_id: 507f191e810c19729de860ea
    CancelBundleRequest:
      type: object
      properties:
        order_reference:
          type: string
      additionalProperties: false
    CancelBundleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Order Canceled Successfully
      additionalProperties: false
      example:
        response_code: "1"
        Message: Order Canceled Successfully
        developer_message: Operation Succcessful
        title: Success
    CompleteTransactionRequest:
      type: object
      properties:
        order_reference:
          maxLength: 100
          type: string
      additionalProperties: false
    CompleteTransactionResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Transaction Completed Successfully
        total_orders_count:
          minimum: 0
          type: integer
        orders:
          type: array
          items:
            oneOf:
            - $ref: "#/components/schemas/CompleteTransactionSuccessfulResponse"
            - $ref: "#/components/schemas/CompleteTransactionFailedResponse"
      additionalProperties: false
      example:
        response_code: "1"
        total_orders_count: 0
        developer_message: Operation Succcessful
        orders:
        - ""
        - ""
        title: Success
        message: Transaction Completed Successfully
    CompleteTransactionSuccessfulResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
          type: string
          example: 6380d4efa3fb4
        plan_status:
          type: string
        plan_started:
          type: boolean
        expiry_date:
          type: string
          format: date-time
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        matching_id:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        order_type:
          type: string
          example: BuyBundle
        vendor_expiry_date_profile:
          type: number
        vendor_expiry_days:
          type: number
        vendor_start_bundle:
          type: boolean
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_active_topups:
          type: boolean
      additionalProperties: false
    CompleteTransactionFailedResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Failed
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_active_topups:
          type: boolean
      additionalProperties: false
    GenerateVoucherRequest:
      required:
      - amount
      - currency_code
      - expiry_datetime
      - generate_csv_file
      - quantity
      - voucher_name
      type: object
      properties:
        voucher_name:
          maxLength: 30
          minLength: 1
          type: string
          example: Adventure30
        amount:
          minimum: 0
          exclusiveMinimum: true
          type: number
          format: float
          example: 30
        quantity:
          minimum: 1
          type: integer
          format: int32
          example: 1
        currency_code:
          type: string
          enum:
          - USD
        is_active:
          type: boolean
          readOnly: true
        expiry_datetime:
          type: string
          format: date-time
        generate_csv_file:
          type: boolean
        reason:
          maxLength: 300
          type: string
          example: "When adventure calls, you gotta answer!"
        voucher_type:
          type: string
          enum:
          - default
          - triggerable
      additionalProperties: false
    GetVouchersResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_vouchers_count:
          minimum: 0
          type: integer
        vouchers:
          type: array
          items:
            $ref: "#/components/schemas/GetVouchersResponse_vouchers"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        total_vouchers_count: 0
        title: Success
        vouchers:
        - reason: "When adventure calls, you gotta answer!"
          amount: 30
          is_active: true
          voucher_name: Adventure30
          voucher_code: voucher_code
          reseller_amount: 30
          is_used: true
          datetime: 2000-01-23T04:56:07.000+00:00
          expiry_datetime: 2000-01-23T04:56:07.000+00:00
          voucher_type: voucher_type
          voucher_id: 507f191e810c19729de860ea
          reseller_id: 507f191e810c19729de860ea
          status: status
        - reason: "When adventure calls, you gotta answer!"
          amount: 30
          is_active: true
          voucher_name: Adventure30
          voucher_code: voucher_code
          reseller_amount: 30
          is_used: true
          datetime: 2000-01-23T04:56:07.000+00:00
          expiry_datetime: 2000-01-23T04:56:07.000+00:00
          voucher_type: voucher_type
          voucher_id: 507f191e810c19729de860ea
          reseller_id: 507f191e810c19729de860ea
          status: status
    GetVouchersBundledResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_vouchers_count:
          minimum: 0
          type: integer
        vouchers:
          type: array
          items:
            $ref: "#/components/schemas/GetVouchersBundledResponse_vouchers"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        total_vouchers_count: 0
        title: Success
        vouchers:
        - datetime: 2000-01-23T04:56:07.000+00:00
          quantity: 1
          expiry_datetime: 2000-01-23T04:56:07.000+00:00
          voucher_name: Adventure30
          voucher_type: default
          reseller_id: 507f191e810c19729de860ea
        - datetime: 2000-01-23T04:56:07.000+00:00
          quantity: 1
          expiry_datetime: 2000-01-23T04:56:07.000+00:00
          voucher_name: Adventure30
          voucher_type: default
          reseller_id: 507f191e810c19729de860ea
    GenerateVoucherResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Voucher Generation Successful
        csv_file:
          type: string
          description: The csv file for the user
          format: binary
      additionalProperties: false
      example:
        response_code: "1"
        csv_file: ""
        developer_message: Operation Succcessful
        title: Success
        message: Voucher Generation Successful
    TopupBundleRequest:
      type: object
      properties:
        order_reference:
          maxLength: 100
          type: string
          description: custom order reference
        previous_order_reference:
          maxLength: 100
          type: string
          description: previous custom order reference to topup bundle
        bundle_code:
          type: string
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        whatsapp_number:
          type: string
      additionalProperties: false
    TopupBundleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Bundle Assigned Successfully
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 30
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional currency
          format: float
          example: 30
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
      additionalProperties: false
      example:
        remaining_wallet_balance: 30
        response_code: "1"
        iccid: iccid
        developer_message: Operation Succcessful
        title: Success
        message: Bundle Assigned Successfully
        remaining_wallet_balance_in_additional_currency: 30
        additional_currency_code: SAR
        order_id: 507f191e810c19729de860ea
        reseller_id: 507f191e810c19729de860ea
    ContactObject:
      required:
      - emails
      - phones
      type: object
      properties:
        emails:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
            type: string
            example: <EMAIL>
        phones:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^\\+(?:[0-9] ?){6,14}[0-9]$"
            type: string
        website:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        address:
          maxLength: 300
          type: string
    Reseller:
      required:
      - balance
      - contact
      - currency_code
      - date_created
      - is_active
      - reseller_id
      - reseller_name
      - reseller_type
      - support_topup
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        reseller_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        reseller_type:
          type: string
          default: prepaid
          enum:
          - prepaid
          - postpaid
        callback_url:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        support_topup:
          type: boolean
          default: false
        is_active:
          type: boolean
          default: false
        supports_multibranches:
          type: boolean
          default: false
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        date_created:
          type: string
          format: date
          readOnly: true
        currency_code:
          type: string
          default: USD
          enum:
          - USD
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        default_currency_code:
          maxLength: 15
          type: string
          default: USD
        balance:
          minimum: 0
          type: number
          format: float
        balance_in_additional_currency:
          minimum: 0
          type: number
          format: float
        balance_warning_limit:
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 1000
        credit_limit:
          minimum: 0
          type: number
          description: postpaid limit
          format: float
          default: 0
        credit_warning_limit:
          maximum: 100
          minimum: 0
          type: number
          description: credit warning limit in percentage
          format: float
          default: 0
        credit_limit_in_additional_currency:
          minimum: 0
          type: number
          format: float
        rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: rate revenue in percent
          format: float
          example: 12
          default: 0
        corp_rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: corporate revenue in percent
          format: float
          example: 12
        voucher_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
          example: 12
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        email_settings:
          $ref: "#/components/schemas/Reseller_email_settings"
        custom_email_template_qr:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_data:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_expired:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        image:
          type: string
          description: The image file of the user
          format: binary
        image_type:
          type: string
        is_whitelabel:
          type: boolean
          default: false
        tenant_name:
          type: string
        reseller_category:
          type: string
        active_vendors_list:
          uniqueItems: true
          type: array
          items:
            type: string
        vendors_for_balance_deduction_list:
          uniqueItems: true
          type: array
          items:
            type: string
        request_custom_email:
          type: boolean
          default: true
        data_consumption_email:
          $ref: "#/components/schemas/Reseller_data_consumption_email"
        data_expired_email:
          $ref: "#/components/schemas/Reseller_data_expired_email"
        qr_code_email:
          $ref: "#/components/schemas/Reseller_qr_code_email"
      additionalProperties: false
    AddResellerRequest:
      required:
      - agent
      - balance
      - contact
      - currency_code
      - is_active
      - reseller_name
      - reseller_type
      - support_topup
      - supports_multibranches
      type: object
      properties:
        reseller_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        reseller_type:
          type: string
          default: prepaid
          enum:
          - prepaid
          - postpaid
        callback_url:
          pattern: "(https?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          description: callback url where reseller received the push notifications
          nullable: true
          example: www.xyz.com
        consumption_url:
          pattern: "(https?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          description: consumption url where reseller received the consumption notifications
          nullable: true
          example: www.xyz.com
        support_topup:
          type: boolean
          default: true
        is_active:
          type: boolean
          default: true
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        supports_multibranches:
          type: boolean
          default: false
        currency_code:
          type: string
          default: USD
          enum:
          - USD
        default_currency_code:
          maxLength: 15
          type: string
          example: SAR
        balance:
          minimum: 0
          type: number
          format: float
        credit_limit:
          minimum: 0
          type: number
          description: postpaid limit
          format: float
          default: 0
        credit_warning_limit:
          maximum: 100
          minimum: 0
          type: number
          description: credit warning limit in percentage
          format: float
          default: 0
        balance_warning_limit:
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 1000
        voucher_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
          example: 12
        rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: rate revenue in percent
          format: float
          example: 12
          default: 0
        corp_rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: rate revenue in percent for the corporate
          format: float
          example: 12
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        agent:
          $ref: "#/components/schemas/AddResellerRequest_agent"
        email_settings:
          $ref: "#/components/schemas/Reseller_email_settings"
        custom_email_template_qr:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_data:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_expired:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        image:
          type: string
          description: The image file of the user
          format: binary
        is_whitelabel:
          type: boolean
          default: false
        tenant_name:
          type: string
        reseller_category:
          type: string
        active_vendors_list:
          uniqueItems: true
          type: array
          items:
            type: string
        vendors_for_balance_deduction_list:
          uniqueItems: true
          type: array
          items:
            type: string
        request_custom_email:
          type: boolean
          default: true
        data_consumption_email:
          $ref: "#/components/schemas/Reseller_data_consumption_email"
        data_expired_email:
          $ref: "#/components/schemas/Reseller_data_expired_email"
        qr_code_email:
          $ref: "#/components/schemas/Reseller_qr_code_email"
        notification_type:
          type: string
          default: webhook
          enum:
          - webhook
        retry_on_failed_after:
          type: integer
          default: 1
      additionalProperties: false
    TopupBalanceRequest:
      type: object
      properties:
        topup_amount:
          minimum: 0
          type: number
          format: float
          example: 30
        credit_limit:
          minimum: 0
          type: number
          format: float
          example: 100
      additionalProperties: false
    TopupBalanceResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Wallet Topup Successful
        wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        wallet_balance: 1000
        title: Success
        message: Wallet Topup Successful
    EditTopupBalanceResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Wallet Topup Edited Successfully
        wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        wallet_balance: 1000
        title: Success
        message: Wallet Topup Edited Successfully
    EditResellerRequest:
      type: object
      properties:
        reseller_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        balance_warning_limit:
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 1000
        reseller_type:
          type: string
          default: prepaid
          enum:
          - prepaid
          - postpaid
        callback_url:
          pattern: "(https?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        consumption_url:
          pattern: "(https?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        default_currency_code:
          maxLength: 15
          type: string
          default: USD
        support_topup:
          type: boolean
          default: false
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        is_active:
          type: boolean
          default: false
        currency_code:
          type: string
          default: USD
          enum:
          - USD
        voucher_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
          example: 12
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        email_settings:
          $ref: "#/components/schemas/EditResellerRequest_email_settings"
        custom_email_template_qr:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_data:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_expired:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        image:
          type: string
          description: The image file of the user
          format: binary
        is_whitelabel:
          type: boolean
          default: false
        tenant_name:
          type: string
        reseller_category:
          type: string
        active_vendors_list:
          uniqueItems: true
          type: array
          items:
            type: string
        vendors_for_balance_deduction_list:
          uniqueItems: true
          type: array
          items:
            type: string
        request_custom_email:
          type: boolean
          default: true
        data_consumption_email:
          $ref: "#/components/schemas/Reseller_data_consumption_email"
        data_expired_email:
          $ref: "#/components/schemas/Reseller_data_expired_email"
        qr_code_email:
          $ref: "#/components/schemas/Reseller_qr_code_email"
        notification_type:
          type: string
          default: webhook
          enum:
          - webhook
        retry_on_failed_after:
          type: integer
          default: 1
      additionalProperties: false
    GetResellerResponse:
      required:
      - balance
      - contact
      - currency_code
      - date_created
      - is_active
      - reseller_id
      - reseller_name
      - reseller_type
      - support_topup
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        reseller_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        reseller_type:
          type: string
          default: prepaid
          enum:
          - prepaid
          - postpaid
        callback_url:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        support_topup:
          type: boolean
          default: false
        is_active:
          type: boolean
          default: false
        supports_multibranches:
          type: boolean
          default: false
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        date_created:
          type: string
          format: date
          readOnly: true
        currency_code:
          type: string
          default: USD
          enum:
          - USD
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        default_currency_code:
          maxLength: 15
          type: string
          default: USD
        balance:
          minimum: 0
          type: number
          format: float
        balance_in_additional_currency:
          minimum: 0
          type: number
          format: float
        balance_warning_limit:
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 1000
        credit_limit:
          minimum: 0
          type: number
          description: postpaid limit
          format: float
          default: 0
        credit_warning_limit:
          maximum: 100
          minimum: 0
          type: number
          description: credit warning limit in percentage
          format: float
          default: 0
        credit_limit_in_additional_currency:
          minimum: 0
          type: number
          format: float
        rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: rate revenue in percent
          format: float
          example: 12
          default: 0
        corp_rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: corporate revenue in percent
          format: float
          example: 12
        voucher_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
          example: 12
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        email_settings:
          $ref: "#/components/schemas/Reseller_email_settings"
        custom_email_template_qr:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_data:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_expired:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        image:
          type: string
          description: The image file of the user
          format: binary
        image_type:
          type: string
        is_whitelabel:
          type: boolean
          default: false
        tenant_name:
          type: string
        reseller_category:
          type: string
        active_vendors_list:
          uniqueItems: true
          type: array
          items:
            type: string
        vendors_for_balance_deduction_list:
          uniqueItems: true
          type: array
          items:
            type: string
        request_custom_email:
          type: boolean
          default: true
        data_consumption_email:
          $ref: "#/components/schemas/Reseller_data_consumption_email"
        data_expired_email:
          $ref: "#/components/schemas/Reseller_data_expired_email"
        qr_code_email:
          $ref: "#/components/schemas/Reseller_qr_code_email"
      additionalProperties: false
      example:
        balance_in_additional_currency: 0.6027456
        response_code: "1"
        developer_message: Operation Succcessful
        corp_rate_revenue: 12
        custom_email_template_data: ""
        active_vendors_list:
        - active_vendors_list
        - active_vendors_list
        data_expired_email:
          website_link: website_link
          footer: footer
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          instagram_link: instagram_link
          email_logo: email_logo
          email-logo_type: email-logo_type
          subject_expired: subject_expired
          whatsapp_number: whatsapp_number
          email_image_expired: email_image_expired
        voucher_rate: 12
        title: Success
        vendors_for_balance_deduction_list:
        - vendors_for_balance_deduction_list
        - vendors_for_balance_deduction_list
        currency_code: USD
        data_consumption_email:
          website_link: website_link
          footer: footer
          email_image_consumption: email_image_consumption
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          instagram_link: instagram_link
          email_logo: email_logo
          email-logo_type: email-logo_type
          subject_consumption: subject_consumption
          whatsapp_number: whatsapp_number
        callback_url: www.xyz.com
        balance: 0.08008282
        rate_revenue: 12
        contact:
          emails:
          - <EMAIL>
          - <EMAIL>
          website: www.xyz.com
          address: address
          phones:
          - phones
          - phones
        supports_promo: true
        credit_limit: 0.14658129
        additional_currency_code: SAR
        reseller_id: 507f191e810c19729de860ea
        image_type: image_type
        image: ""
        is_whitelabel: false
        custom_email_template_expired: ""
        tenant_name: tenant_name
        is_active: false
        default_currency_code: USD
        qr_code_email:
          website_link: website_link
          activation_code: activation_code
          bundle_consumption_link: bundle_consumption_link
          topup_sentence: topup_sentence
          footer: footer
          android_users: android_users
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          email_image_qrcode: email_image_qrcode
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          topup_link: topup_link
          instagram_link: instagram_link
          whatsapp_number: whatsapp_number
          matching_id: matching_id
          invoice_details: invoice_details
          instructions_link: instructions_link
          subject_qrcode: subject_qrcode
          data_bundle_details: data_bundle_details
          smdp_address: smdp_address
          ios_users: ios_users
          email_logo: email_logo
          email-logo_type: email-logo_type
          bundle_consumption_sentence: bundle_consumption_sentence
        date_created: 2000-01-23
        balance_warning_limit: 1000
        credit_warning_limit: 59.621338
        email_settings:
          password: password
          smtp_port: smtp_port
          smtp_server: smtp_server
          username: <EMAIL>
        request_custom_email: true
        support_topup: false
        supports_multibranches: false
        reseller_name: Nakhal
        reseller_type: prepaid
        supports_vouchers: true
        reseller_category: reseller_category
        credit_limit_in_additional_currency: 0.5637377
        custom_email_template_qr: ""
    GetResellersResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_resellers_count:
          minimum: 0
          type: integer
        resellers:
          type: array
          items:
            $ref: "#/components/schemas/GetResellersResponse_resellers"
      additionalProperties: false
      example:
        total_resellers_count: 0
        response_code: "1"
        developer_message: Operation Succcessful
        resellers:
        - balance_in_additional_currency: 0.6027456
          response_code: "1"
          developer_message: Operation Succcessful
          corp_rate_revenue: 12
          custom_email_template_data: ""
          active_vendors_list:
          - active_vendors_list
          - active_vendors_list
          data_expired_email:
            website_link: website_link
            footer: footer
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_expired: subject_expired
            whatsapp_number: whatsapp_number
            email_image_expired: email_image_expired
          voucher_rate: 12
          title: Success
          vendors_for_balance_deduction_list:
          - vendors_for_balance_deduction_list
          - vendors_for_balance_deduction_list
          currency_code: USD
          data_consumption_email:
            website_link: website_link
            footer: footer
            email_image_consumption: email_image_consumption
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_consumption: subject_consumption
            whatsapp_number: whatsapp_number
          callback_url: www.xyz.com
          balance: 0.08008282
          rate_revenue: 12
          contact:
            emails:
            - <EMAIL>
            - <EMAIL>
            website: www.xyz.com
            address: address
            phones:
            - phones
            - phones
          supports_promo: true
          credit_limit: 0.14658129
          additional_currency_code: SAR
          reseller_id: 507f191e810c19729de860ea
          image_type: image_type
          image: ""
          is_whitelabel: false
          custom_email_template_expired: ""
          tenant_name: tenant_name
          is_active: false
          default_currency_code: USD
          qr_code_email:
            website_link: website_link
            activation_code: activation_code
            bundle_consumption_link: bundle_consumption_link
            topup_sentence: topup_sentence
            footer: footer
            android_users: android_users
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            email_image_qrcode: email_image_qrcode
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            topup_link: topup_link
            instagram_link: instagram_link
            whatsapp_number: whatsapp_number
            matching_id: matching_id
            invoice_details: invoice_details
            instructions_link: instructions_link
            subject_qrcode: subject_qrcode
            data_bundle_details: data_bundle_details
            smdp_address: smdp_address
            ios_users: ios_users
            email_logo: email_logo
            email-logo_type: email-logo_type
            bundle_consumption_sentence: bundle_consumption_sentence
          date_created: 2000-01-23
          balance_warning_limit: 1000
          credit_warning_limit: 59.621338
          email_settings:
            password: password
            smtp_port: smtp_port
            smtp_server: smtp_server
            username: <EMAIL>
          request_custom_email: true
          support_topup: false
          supports_multibranches: false
          reseller_name: Nakhal
          reseller_type: prepaid
          supports_vouchers: true
          reseller_category: reseller_category
          credit_limit_in_additional_currency: 0.5637377
          custom_email_template_qr: ""
        - balance_in_additional_currency: 0.6027456
          response_code: "1"
          developer_message: Operation Succcessful
          corp_rate_revenue: 12
          custom_email_template_data: ""
          active_vendors_list:
          - active_vendors_list
          - active_vendors_list
          data_expired_email:
            website_link: website_link
            footer: footer
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_expired: subject_expired
            whatsapp_number: whatsapp_number
            email_image_expired: email_image_expired
          voucher_rate: 12
          title: Success
          vendors_for_balance_deduction_list:
          - vendors_for_balance_deduction_list
          - vendors_for_balance_deduction_list
          currency_code: USD
          data_consumption_email:
            website_link: website_link
            footer: footer
            email_image_consumption: email_image_consumption
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_consumption: subject_consumption
            whatsapp_number: whatsapp_number
          callback_url: www.xyz.com
          balance: 0.08008282
          rate_revenue: 12
          contact:
            emails:
            - <EMAIL>
            - <EMAIL>
            website: www.xyz.com
            address: address
            phones:
            - phones
            - phones
          supports_promo: true
          credit_limit: 0.14658129
          additional_currency_code: SAR
          reseller_id: 507f191e810c19729de860ea
          image_type: image_type
          image: ""
          is_whitelabel: false
          custom_email_template_expired: ""
          tenant_name: tenant_name
          is_active: false
          default_currency_code: USD
          qr_code_email:
            website_link: website_link
            activation_code: activation_code
            bundle_consumption_link: bundle_consumption_link
            topup_sentence: topup_sentence
            footer: footer
            android_users: android_users
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            email_image_qrcode: email_image_qrcode
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            topup_link: topup_link
            instagram_link: instagram_link
            whatsapp_number: whatsapp_number
            matching_id: matching_id
            invoice_details: invoice_details
            instructions_link: instructions_link
            subject_qrcode: subject_qrcode
            data_bundle_details: data_bundle_details
            smdp_address: smdp_address
            ios_users: ios_users
            email_logo: email_logo
            email-logo_type: email-logo_type
            bundle_consumption_sentence: bundle_consumption_sentence
          date_created: 2000-01-23
          balance_warning_limit: 1000
          credit_warning_limit: 59.621338
          email_settings:
            password: password
            smtp_port: smtp_port
            smtp_server: smtp_server
            username: <EMAIL>
          request_custom_email: true
          support_topup: false
          supports_multibranches: false
          reseller_name: Nakhal
          reseller_type: prepaid
          supports_vouchers: true
          reseller_category: reseller_category
          credit_limit_in_additional_currency: 0.5637377
          custom_email_template_qr: ""
        title: Success
    AddResellerResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Reseller Added Successfully
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        agent_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: Reseller Added Successfully
        reseller_id: 507f191e810c19729de860ea
    EditResellerResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Reseller Edited Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Reseller Edited Successfully
    DeleteResellerResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Reseller Deleted Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Reseller Deleted Successfully
    CustomizePriceRequest:
      type: object
      properties:
        reset_customizations:
          type: boolean
          description: "if true, all custom prices will be reset and the rate revenue\
            \ will be activated."
        set_selling_revenue:
          maximum: 200
          minimum: -100
          type: number
          description: percentage
          format: float
          example: 12
        bundles:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizePriceRequest_bundles"
      additionalProperties: false
    CustomizeCorpPriceRequest:
      type: object
      properties:
        reset_customizations:
          type: boolean
          description: "if true, all custom prices will be reset and the rate revenue\
            \ will be activated."
        set_selling_revenue:
          maximum: 200
          minimum: -100
          type: number
          description: percentage
          format: float
          example: 12
        bundles:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizeCorpPriceRequest_bundles"
      additionalProperties: false
    CsvFile:
      required:
      - csv_file
      type: object
      properties:
        csv_file:
          type: string
          format: binary
      additionalProperties: false
    CustomizePriceResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Bundle Retail Price Edited Successfully
        detail:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Bundle Does not exist.
        invalid_bundle_prices:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizePriceResponse_invalid_bundle_prices"
        bundles_not_found:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizePriceResponse_bundles_not_found"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        bundles_not_found:
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        detail: Bundle Does not exist.
        title: Success
        message: Bundle Retail Price Edited Successfully
        invalid_bundle_prices:
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          custom_price: 12
          unit_price: 12
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          custom_price: 12
          unit_price: 12
    CustomizeCurrenciesRequest:
      type: object
      properties:
        currencies:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizeCurrenciesRequest_currencies"
      additionalProperties: false
    CustomizeCurrenciesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Currencies have been modified Successfully
        detail:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Currencies have been modified Successfully
        invalid_currency_rates:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizeCurrenciesResponse_invalid_currency_rates"
        currencies_not_found:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/CustomizeCurrenciesResponse_currencies_not_found"
      additionalProperties: false
      example:
        invalid_currency_rates:
        - currency_rate: 12
          currency_code: ZZZ
        - currency_rate: 12
          currency_code: ZZZ
        response_code: "1"
        currencies_not_found:
        - currency_name: Saudii Arabian Rial
          currency_code: SAR
        - currency_name: Saudii Arabian Rial
          currency_code: SAR
        developer_message: Operation Succcessful
        detail: Currencies have been modified Successfully
        title: Success
        message: Currencies have been modified Successfully
    Branch:
      required:
      - branch_id
      - branch_name
      - date_created
      - is_active
      - reseller_id
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        is_active:
          type: boolean
          default: true
        date_created:
          type: string
          format: date
          readOnly: true
        limit:
          maximum: 1000000000000000
          minimum: -1
          exclusiveMinimum: false
          type: number
          format: float
          nullable: true
        limit_consumption:
          maximum: 1000000000000000
          minimum: 0
          type: number
          format: float
          nullable: true
        contact:
          $ref: "#/components/schemas/Branch_contact"
      additionalProperties: false
    AddBranchRequest:
      required:
      - agent
      - branch_name
      - contact
      - is_active
      type: object
      properties:
        branch_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        is_active:
          type: boolean
          default: true
        limit:
          maximum: 1000000000000000
          minimum: -1
          exclusiveMinimum: false
          type: number
          format: float
        limit_consumption:
          maximum: 1000000000000000
          minimum: 0
          type: number
          format: float
          nullable: true
          readOnly: true
        contact:
          $ref: "#/components/schemas/Branch_contact"
        agent:
          $ref: "#/components/schemas/AddResellerRequest_agent"
      additionalProperties: false
    EditBranchRequest:
      type: object
      properties:
        branch_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        limit:
          maximum: 1000000000000000
          minimum: -1
          exclusiveMinimum: false
          type: number
          format: float
          nullable: true
        limit_consumption:
          maximum: 1000000000000000
          minimum: 0
          type: number
          format: float
          nullable: true
          readOnly: true
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        is_active:
          type: boolean
          default: true
      additionalProperties: false
    GetBranchResponse:
      required:
      - branch_id
      - branch_name
      - date_created
      - is_active
      - reseller_id
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        is_active:
          type: boolean
          default: true
        date_created:
          type: string
          format: date
          readOnly: true
        limit:
          maximum: 1000000000000000
          minimum: -1
          exclusiveMinimum: false
          type: number
          format: float
          nullable: true
        limit_consumption:
          maximum: 1000000000000000
          minimum: 0
          type: number
          format: float
          nullable: true
        contact:
          $ref: "#/components/schemas/Branch_contact"
      additionalProperties: false
      example:
        response_code: "1"
        is_active: true
        branch_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        date_created: 2000-01-23
        branch_name: Nakhal
        contact:
          emails:
          - <EMAIL>
          - <EMAIL>
          website: www.xyz.com
          address: address
          phones:
          - phones
          - phones
        limit: 8.0082819E13
        limit_consumption: 6.0274564E14
        title: Success
        reseller_id: 507f191e810c19729de860ea
    GetBranchesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_branches_count:
          minimum: 0
          type: integer
        branches:
          type: array
          items:
            $ref: "#/components/schemas/GetBranchesResponse_branches"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        total_branches_count: 0
        title: Success
        branches:
        - response_code: "1"
          is_active: true
          branch_id: 507f191e810c19729de860ea
          developer_message: Operation Succcessful
          date_created: 2000-01-23
          branch_name: Nakhal
          contact:
            emails:
            - <EMAIL>
            - <EMAIL>
            website: www.xyz.com
            address: address
            phones:
            - phones
            - phones
          limit: 6.0274564E14
          limit_consumption: 1.46581295E14
          title: Success
          reseller_id: 507f191e810c19729de860ea
        - response_code: "1"
          is_active: true
          branch_id: 507f191e810c19729de860ea
          developer_message: Operation Succcessful
          date_created: 2000-01-23
          branch_name: Nakhal
          contact:
            emails:
            - <EMAIL>
            - <EMAIL>
            website: www.xyz.com
            address: address
            phones:
            - phones
            - phones
          limit: 6.0274564E14
          limit_consumption: 1.46581295E14
          title: Success
          reseller_id: 507f191e810c19729de860ea
    AddBranchResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Branch Added Successfully
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        agent_id: 507f191e810c19729de860ea
        branch_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: Branch Added Successfully
    EditBranchResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Branch Edited Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Branch Edited Successfully
    DeleteBranchResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Branch Deleted Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Branch Deleted Successfully
    AgentProfile:
      required:
      - agent_id
      - date_created
      - email
      - is_active
      - name
      - role_id
      - username
      type: object
      properties:
        reseller:
          $ref: "#/components/schemas/GetResellersResponse_resellers"
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        username:
          maximum: 30
          minimum: 1
          type: string
          example: john.doe
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        is_active: false
        agent_id: 507f191e810c19729de860ea
        branch_id: 507f191e810c19729de860ea
        role_id: 507f191e810c19729de860ea
        date_created: 2000-01-23T04:56:07.000+00:00
        reseller:
          balance_in_additional_currency: 0.6027456
          response_code: "1"
          developer_message: Operation Succcessful
          corp_rate_revenue: 12
          custom_email_template_data: ""
          active_vendors_list:
          - active_vendors_list
          - active_vendors_list
          data_expired_email:
            website_link: website_link
            footer: footer
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_expired: subject_expired
            whatsapp_number: whatsapp_number
            email_image_expired: email_image_expired
          voucher_rate: 12
          title: Success
          vendors_for_balance_deduction_list:
          - vendors_for_balance_deduction_list
          - vendors_for_balance_deduction_list
          currency_code: USD
          data_consumption_email:
            website_link: website_link
            footer: footer
            email_image_consumption: email_image_consumption
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            instagram_link: instagram_link
            email_logo: email_logo
            email-logo_type: email-logo_type
            subject_consumption: subject_consumption
            whatsapp_number: whatsapp_number
          callback_url: www.xyz.com
          balance: 0.08008282
          rate_revenue: 12
          contact:
            emails:
            - <EMAIL>
            - <EMAIL>
            website: www.xyz.com
            address: address
            phones:
            - phones
            - phones
          supports_promo: true
          credit_limit: 0.14658129
          additional_currency_code: SAR
          reseller_id: 507f191e810c19729de860ea
          image_type: image_type
          image: ""
          is_whitelabel: false
          custom_email_template_expired: ""
          tenant_name: tenant_name
          is_active: false
          default_currency_code: USD
          qr_code_email:
            website_link: website_link
            activation_code: activation_code
            bundle_consumption_link: bundle_consumption_link
            topup_sentence: topup_sentence
            footer: footer
            android_users: android_users
            facebook_link: facebook_link
            email-image_type: email-image_type
            greetings: greetings
            email_image_qrcode: email_image_qrcode
            company_name_team: company_name_team
            body_1: body_1
            body_2: body_2
            topup_link: topup_link
            instagram_link: instagram_link
            whatsapp_number: whatsapp_number
            matching_id: matching_id
            invoice_details: invoice_details
            instructions_link: instructions_link
            subject_qrcode: subject_qrcode
            data_bundle_details: data_bundle_details
            smdp_address: smdp_address
            ios_users: ios_users
            email_logo: email_logo
            email-logo_type: email-logo_type
            bundle_consumption_sentence: bundle_consumption_sentence
          date_created: 2000-01-23
          balance_warning_limit: 1000
          credit_warning_limit: 59.621338
          email_settings:
            password: password
            smtp_port: smtp_port
            smtp_server: smtp_server
            username: <EMAIL>
          request_custom_email: true
          support_topup: false
          supports_multibranches: false
          reseller_name: Nakhal
          reseller_type: prepaid
          supports_vouchers: true
          reseller_category: reseller_category
          credit_limit_in_additional_currency: 0.5637377
          custom_email_template_qr: ""
        name: john
        email: <EMAIL>
        username: john.doe
    Agent:
      required:
      - agent_id
      - date_created
      - email
      - is_active
      - name
      - reseller_id
      - role_name
      - username
      type: object
      properties:
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        username:
          maximum: 30
          minimum: 1
          type: string
          example: john.doe
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
      additionalProperties: false
    AddAgentRequest:
      required:
      - email
      - name
      - password
      - role_name
      - username
      type: object
      properties:
        email:
          pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
          type: string
          example: <EMAIL>
        username:
          maxLength: 30
          minLength: 1
          pattern: "^[a-zA-Z][a-zA-Z0-9_.-]*$"
          type: string
          example: john.snow
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john.snow
        is_active:
          type: boolean
          readOnly: true
          default: false
        role_name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        password:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
      additionalProperties: false
    EditAgentRequest:
      type: object
      properties:
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
        is_active:
          type: boolean
          default: false
        role_name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        password:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
        password_confirmation:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
      additionalProperties: false
    GetAgentResponse:
      required:
      - agent_id
      - date_created
      - email
      - is_active
      - name
      - reseller_id
      - role_name
      - username
      type: object
      properties:
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        username:
          maximum: 30
          minimum: 1
          type: string
          example: john.doe
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
      additionalProperties: false
      example:
        role_name: ResellerAdmin
        is_active: false
        agent_id: 507f191e810c19729de860ea
        branch_id: 507f191e810c19729de860ea
        date_created: 2000-01-23T04:56:07.000+00:00
        name: john snow
        reseller_id: 507f191e810c19729de860ea
        email: <EMAIL>
        username: john.doe
    GetAgentsResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_agents_count:
          minimum: 0
          type: integer
        agents:
          type: array
          items:
            $ref: "#/components/schemas/GetAgentsResponse_agents"
      additionalProperties: false
      example:
        response_code: "1"
        total_agents_count: 0
        developer_message: Operation Succcessful
        title: Success
        agents:
        - role_name: ResellerAdmin
          is_active: false
          agent_id: 507f191e810c19729de860ea
          branch_id: 507f191e810c19729de860ea
          date_created: 2000-01-23T04:56:07.000+00:00
          name: john snow
          reseller_id: 507f191e810c19729de860ea
          email: <EMAIL>
          username: john.doe
        - role_name: ResellerAdmin
          is_active: false
          agent_id: 507f191e810c19729de860ea
          branch_id: 507f191e810c19729de860ea
          date_created: 2000-01-23T04:56:07.000+00:00
          name: john snow
          reseller_id: 507f191e810c19729de860ea
          email: <EMAIL>
          username: john.doe
    AddAgentResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Agent Added Successfully
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        agent_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        title: Success
        message: Agent Added Successfully
    EditAgentResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Agent Edited Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Agent Edited Successfully
    DeleteAgentResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Agent Deleted Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Agent Deleted Successfully
    GetOrderHistorySuccessfulResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
          type: string
          description: Plan uid related to profiles
          example: 6380d4efa3fb4
        plan_status:
          type: string
          description: "It could be Pending, Active or Expired"
        plan_started:
          type: boolean
          description: Only if plan started
        expiry_date:
          type: string
          description: Expiration date for the bundle
          format: date-time
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        bundle_price:
          type: number
          description: Price in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: Price in currency code updated by reseller
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: Price in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: Price in currency code updated by reseller
          format: float
          example: 12
        currency_code:
          type: string
          description: By default it is dollars
        additional_currency_code:
          maxLength: 14
          type: string
          description: Currency code updated by reseller
          example: SAR
        matching_id:
          type: string
          description: Data for qr code value
        smdp_address:
          type: string
          description: Data for qr code value
        activation_code:
          type: string
          description: Data for qr code value
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: Remaining wallet for reseller in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: Remaining wallet for reseller in updated currency
          format: float
          example: 1000
        date_created:
          type: string
          description: Creation date for the order
          format: date-time
        refund_reason:
          type: string
          description: Refund reason in case refund occured
          example: User Did not have good coverage.
        order_reference:
          maxLength: 30
          type: string
          description: Order reference number
        otp:
          type: string
          description: Order reference number
          format: uuid
        profile_expiry_date:
          type: string
          description: Expiration date for the profile
        bundle_expiry_date:
          type: string
          description: Expiration date for the bundle
        order_type:
          type: string
          description: To specify bundle or topup
          example: BuyBundle
        whatsapp_number:
          type: string
          description: Whatsapp number for the client
        topup_an_expired_plan:
          type: boolean
          description: If topup a plan that have plan_status = Expired
        has_related_active_topups:
          type: boolean
          description: If order type is BuyBundle and the order has related topup(s)
      additionalProperties: false
    GetOrderHistoryFailedResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_active_topups:
          type: boolean
      additionalProperties: false
    RefundOrderRequest:
      type: object
      properties:
        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
    RefundOrderResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        Message:
          type: string
          example: Order Refunded Successfully
      additionalProperties: false
      example:
        response_code: "1"
        Message: Order Refunded Successfully
        developer_message: Operation Succcessful
        title: Success
    GetPromoDashboardResponse:
      type: object
      properties:
        sales_per_day:
          type: array
          items:
            $ref: "#/components/schemas/GetPromoDashboardResponse_sales_per_day"
      additionalProperties: false
      example:
        sales_per_day:
        - date: 2023-04-27
          bundles_sold: 2
          commission_amount: 0.522
          total_sales_volume: 5.22
        - date: 2023-04-27
          bundles_sold: 2
          commission_amount: 0.522
          total_sales_volume: 5.22
    GetDashboardResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        top_five_bundles:
          maxItems: 5
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/GetDashboardResponse_top_five_bundles"
        gross_sales_volume_usd:
          type: number
          format: float
          example: 3000.5
        net_sales_volume_usd:
          type: number
          format: float
          example: 300
        bundles_sold:
          type: array
          items:
            $ref: "#/components/schemas/GetDashboardResponse_bundles_sold"
      additionalProperties: false
      example:
        bundles_sold:
        - date: date
          sales_number: 0
        - date: date
          sales_number: 0
        response_code: "1"
        developer_message: Operation Succcessful
        top_five_bundles:
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          sales_number: 0
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          sales_number: 0
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          sales_number: 0
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          sales_number: 0
        - bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
          sales_number: 0
        gross_sales_volume_usd: 3000.5
        title: Success
        net_sales_volume_usd: 300
    ResendOrderEmailResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Email Sent Successfully
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        message: Email Sent Successfully
    GetTransactionHistoryResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_transactions_count:
          minimum: 0
          type: integer
        transactions:
          type: array
          items:
            $ref: "#/components/schemas/GetTransactionHistoryResponse_transactions"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        total_transactions_count: 0
        title: Success
        transactions:
        - transaction_id: 507f191e810c19729de860ea
          amount: 20
          branch_id: 507f191e810c19729de860ea
          date_created: 2000-01-23T04:56:07.000+00:00
          destination_branch_id: 507f191e810c19729de860ea
          refund_reason: User Did not have good coverage.
          type: type
          additional_currency_code: SAR
          order_id: 507f191e810c19729de860ea
          amount_additional_currency: 20
          reseller_id: 507f191e810c19729de860ea
        - transaction_id: 507f191e810c19729de860ea
          amount: 20
          branch_id: 507f191e810c19729de860ea
          date_created: 2000-01-23T04:56:07.000+00:00
          destination_branch_id: 507f191e810c19729de860ea
          refund_reason: User Did not have good coverage.
          type: type
          additional_currency_code: SAR
          order_id: 507f191e810c19729de860ea
          amount_additional_currency: 20
          reseller_id: 507f191e810c19729de860ea
    GetOrderHistoryResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_orders_count:
          minimum: 0
          type: integer
        orders:
          type: array
          items:
            oneOf:
            - $ref: "#/components/schemas/GetOrderHistorySuccessfulResponse"
            - $ref: "#/components/schemas/GetOrderHistoryFailedResponse"
      additionalProperties: false
      example:
        response_code: "1"
        total_orders_count: 0
        developer_message: Operation Succcessful
        orders:
        - ""
        - ""
        title: Success
    GetPromocodeHistoryResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_orders_count:
          minimum: 0
          type: integer
        orders:
          type: array
          items:
            $ref: "#/components/schemas/GetPromocodeHistoryResponse_orders"
      additionalProperties: false
      example:
        response_code: "1"
        total_orders_count: 0
        developer_message: Operation Succcessful
        orders:
        - affiliate_program: true
          activation_code: activation_code
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
          affiliate_marketing_message: affiliate_marketing_message
          order_status: Successful
          remaining_wallet_balance: 1000
          iccid: iccid
          bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          branch_id: 507f191e810c19729de860ea
          country_name:
          - United States
          - United States
          refund_reason: User Did not have good coverage.
          client_name: client_name
          reseller_id: 507f191e810c19729de860ea
          plan_uid: 6380d4efa3fb4
          matching_id: matching_id
          date_created: 2000-01-23T04:56:07.000+00:00
          client_email: <EMAIL>
          promocode: Monty2023
          bundle_price: 12
          smdp_address: smdp_address
          bundle_category: country
          country_code:
          - USA
          - USA
          commision_amount: 1.4658129
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_retail_price: 12
          commission_rate: 60.274563
          order_id: 507f191e810c19729de860ea
          plan_status: plan_status
        - affiliate_program: true
          activation_code: activation_code
          bundle_name: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
          affiliate_marketing_message: affiliate_marketing_message
          order_status: Successful
          remaining_wallet_balance: 1000
          iccid: iccid
          bundle_code: esim_10gb_30days_unitedstates__0905202216214562
          branch_id: 507f191e810c19729de860ea
          country_name:
          - United States
          - United States
          refund_reason: User Did not have good coverage.
          client_name: client_name
          reseller_id: 507f191e810c19729de860ea
          plan_uid: 6380d4efa3fb4
          matching_id: matching_id
          date_created: 2000-01-23T04:56:07.000+00:00
          client_email: <EMAIL>
          promocode: Monty2023
          bundle_price: 12
          smdp_address: smdp_address
          bundle_category: country
          country_code:
          - USA
          - USA
          commision_amount: 1.4658129
          bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
          bundle_retail_price: 12
          commission_rate: 60.274563
          order_id: 507f191e810c19729de860ea
          plan_status: plan_status
        title: Success
    GetAffiliateProgramResponse:
      type: object
      properties:
        affiliate_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        image_url:
          type: string
        promo_code:
          type: string
        discount_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
        amount:
          minimum: 0
          type: number
          format: float
        draft:
          type: boolean
      additionalProperties: false
      example:
        amount: 0.6027456
        affiliate_id: 507f191e810c19729de860ea
        image_url: image_url
        draft: true
        promo_code: promo_code
        discount_rate: 8.008282
    GetVoucherUseHistoryResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_uses_count:
          minimum: 0
          type: integer
        data:
          type: array
          items:
            $ref: "#/components/schemas/GetVoucherUseHistoryResponse_data"
      additionalProperties: false
      example:
        total_uses_count: 0
        response_code: "1"
        data:
        - amount: 6.0274563
          branch_id: 507f191e810c19729de860ea
          voucher_name: Adventure30
          date_created: 2000-01-23T04:56:07.000+00:00
          voucher_use_id: 507f191e810c19729de860ea
          voucher_code: Adventure30
          reseller_id: 507f191e810c19729de860ea
          currency_code: USD
          username: john snow
        - amount: 6.0274563
          branch_id: 507f191e810c19729de860ea
          voucher_name: Adventure30
          date_created: 2000-01-23T04:56:07.000+00:00
          voucher_use_id: 507f191e810c19729de860ea
          voucher_code: Adventure30
          reseller_id: 507f191e810c19729de860ea
          currency_code: USD
          username: john snow
        developer_message: Operation Succcessful
        title: Success
    IssueReport:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_issues_count:
          minimum: 0
          type: integer
        issues:
          type: array
          items:
            $ref: "#/components/schemas/IssueReport_issues"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        total_issues_count: 0
        title: Success
        issues:
        - requester: John Wick
          issue_description: issue_description
          date_created: 2000-01-23
          issue_type: Technical Issue
          priority: 2
          resolution: resolution
          feedback:
            comments: comments
            satisfaction: 4
          added_by: 507f191e810c19729de860ea
          branch_id: 507f191e810c19729de860ea
          report_id: 507f191e810c19729de860ea
          chat:
          - added_by_type: added_by_type
            added_by: 507f191e810c19729de860ea
            date_created: 2000-01-23
            attachement: attachement
            added_by_name: John Wick
            message: message
            file_size: 16.7
          - added_by_type: added_by_type
            added_by: 507f191e810c19729de860ea
            date_created: 2000-01-23
            attachement: attachement
            added_by_name: John Wick
            message: message
            file_size: 16.7
          issue_subject: issue_subject
          assignee: Monty eSIM Support
          last_modified: 2000-01-23
          reseller_id: 507f191e810c19729de860ea
          status: Open
        - requester: John Wick
          issue_description: issue_description
          date_created: 2000-01-23
          issue_type: Technical Issue
          priority: 2
          resolution: resolution
          feedback:
            comments: comments
            satisfaction: 4
          added_by: 507f191e810c19729de860ea
          branch_id: 507f191e810c19729de860ea
          report_id: 507f191e810c19729de860ea
          chat:
          - added_by_type: added_by_type
            added_by: 507f191e810c19729de860ea
            date_created: 2000-01-23
            attachement: attachement
            added_by_name: John Wick
            message: message
            file_size: 16.7
          - added_by_type: added_by_type
            added_by: 507f191e810c19729de860ea
            date_created: 2000-01-23
            attachement: attachement
            added_by_name: John Wick
            message: message
            file_size: 16.7
          issue_subject: issue_subject
          assignee: Monty eSIM Support
          last_modified: 2000-01-23
          reseller_id: 507f191e810c19729de860ea
          status: Open
    AddIssueReportRequest:
      required:
      - issue_description
      - issue_type
      type: object
      properties:
        issue_type:
          type: string
          description: "Type of the issue (e.g., \"Technical Issue\" or \"Price-related\
            \ Concern\")."
          enum:
          - Technical Issue
          - Price-related Concern
        issue_subject:
          maxLength: 500
          minLength: 3
          type: string
          description: Summary of Issue
        issue_description:
          maxLength: 10000
          minLength: 3
          type: string
          description: Detailed description of the issue or concern.
        priority:
          maximum: 4
          minimum: 1
          type: integer
          description: "Priority level of the issue (\"Critical :1\", \"High:2\",\
            \ \"Medium:3\", or \"Low:4\")."
          default: 3
        attachments:
          type: array
          description: List of attached files.
          items:
            type: string
            format: binary
      additionalProperties: false
    EditIssueReportRequest:
      type: object
      properties:
        message:
          maxLength: 10000
          type: string
          description: Detailed description of the issue or concern.
        attachments:
          type: array
          description: List of attached files.
          items:
            type: string
            format: binary
      additionalProperties: false
    ResolveIssueRequest:
      required:
      - resolution
      type: object
      properties:
        resolution:
          maxLength: 10000
          type: string
          example: Customer has been satisfied.
      additionalProperties: false
    SubmitFeedbackRequest:
      required:
      - satisfaction
      type: object
      properties:
        satisfaction:
          maximum: 5
          minimum: 0
          type: integer
          description: "User satisfaction rating (e.g., 1-5, with 5 being the highest)."
          example: 4
        comments:
          type: string
          description: Additional comments on the resolution process.
      additionalProperties: false
    ResolveIssueResponse:
      type: object
      properties:
        Message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Issue Resolved Successfully
      additionalProperties: false
      example:
        Message: Issue Resolved Successfully
    GetIssueReportsResponse:
      maxItems: 1000
      minItems: 0
      type: array
      items:
        $ref: "#/components/schemas/GetIssueReportsResponse_inner"
    AddIssueReportResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Issue Reported Successfully
        report_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        report_id: 507f191e810c19729de860ea
        title: Success
        message: Issue Reported Successfully
    EditIssueReportResponse:
      type: object
      properties:
        Message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Issue Report Modified Successfully
      additionalProperties: false
      example:
        Message: Issue Report Modified Successfully
    SubmitFeedbackResponse:
      type: object
      properties:
        Message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Feedback Submitted Successfully
      additionalProperties: false
      example:
        Message: Feedback Submitted Successfully
    DeleteIssueReportResponse:
      type: object
      properties:
        Message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Issue Report Deleted Successfully
      additionalProperties: false
      example:
        Message: Issue Report Deleted Successfully
    NetworkList:
      required:
      - networks
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        networks:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/NetworkList_networks"
        networks_count:
          type: integer
          example: 1000
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        networks:
        - country_code: FRA
          network_id: 507f191e810c19729de860ea
          is_shown: true
          vendor_name: Saudi Arabian Rial
          operator_list:
          - Voda
          - Voda
        - country_code: FRA
          network_id: 507f191e810c19729de860ea
          is_shown: true
          vendor_name: Saudi Arabian Rial
          operator_list:
          - Voda
          - Voda
        networks_count: 1000
    BundleNetworkList:
      required:
      - networks
      type: object
      properties:
        networks:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/BundleNetworkList_networks"
        networks_count:
          type: integer
          example: 1000
      additionalProperties: false
      example:
        networks:
        - country_code: FRA
          operator_list:
          - Voda
          - Voda
        - country_code: FRA
          operator_list:
          - Voda
          - Voda
        networks_count: 1000
    ManageNetworkListRequest:
      type: object
      properties:
        networks:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/ManageNetworkListRequest_networks"
      additionalProperties: false
    AllNetworkList_networksResponse:
      type: object
      properties:
        network_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        vendor_code:
          maxLength: 100
          minLength: 2
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Saudi Arabian Rial
        country_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        operator_list:
          uniqueItems: true
          type: array
          items:
            maxLength: 100
            minLength: 2
            pattern: "^[a-zA-Z ']+$"
            type: string
            example: Voda
        is_shown:
          type: boolean
          default: true
      additionalProperties: false
      example:
        country_code: FRA
        vendor_code: TEST
        network_id: 507f191e810c19729de860ea
        is_shown: true
        vendor_name: Saudi Arabian Rial
        operator_list:
        - Voda
        - Voda
    ManageNetworkListResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Networks have been modified Successfully
        detail:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Networks have been modified Successfully
        invalid_networks:
          uniqueItems: true
          type: array
          items:
            $ref: "#/components/schemas/ManageNetworkListResponse_invalid_networks"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        invalid_networks:
        - country_code: ZZZ
          vendor_name: Johnnies net
        - country_code: ZZZ
          vendor_name: Johnnies net
        detail: Networks have been modified Successfully
        title: Success
        message: Networks have been modified Successfully
    DeleteNetworkListResponse:
      type: object
      properties:
        Message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Network Deleted Successfully
      additionalProperties: false
      example:
        Message: Network Deleted Successfully
    GetAvailableResellerPropertiesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        categories:
          uniqueItems: true
          type: array
          items:
            type: string
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        categories:
        - categories
        - categories
        title: Success
    GetAgentByEmailRequest:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        email:
          type: string
      additionalProperties: false
    GetPlanHistorySuccessfulResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
          type: string
          example: 6380d4efa3fb4
        plan_status:
          type: string
        plan_started:
          type: boolean
        expiry_date:
          type: string
          format: date-time
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        data_unit:
          type: string
        retail_price:
          type: number
          format: float
        data_amount:
          type: number
          format: float
        validity_amount:
          type: string
        region_name:
          type: string
        region_code:
          type: string
        bundle_duration:
          type: integer
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        matching_id:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_order:
          type: boolean
        has_related_active_topups:
          type: boolean
      additionalProperties: false
    GetPlanHistoryFailedResponse:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_order:
          type: boolean
        has_related_active_topups:
          type: boolean
      additionalProperties: false
    GetPlanHistoryResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_orders_count:
          minimum: 0
          type: integer
        orders:
          type: array
          items:
            oneOf:
            - $ref: "#/components/schemas/GetPlanHistorySuccessfulResponse"
            - $ref: "#/components/schemas/GetPlanHistoryFailedResponse"
      additionalProperties: false
      example:
        response_code: "1"
        total_orders_count: 0
        developer_message: Operation Succcessful
        orders:
        - ""
        - ""
        title: Success
    NetworkListByRegionsResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        networks_count:
          type: integer
          example: 1000
        networks:
          type: array
          items:
            $ref: "#/components/schemas/NetworkListByRegionsResponse_networks_1"
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        title: Success
        networks:
        - region_name: Asia
          countries:
          - country_code: IN
            country_name: India
            networks:
            - network_id: "1"
              network_name:
              - network_name
              - network_name
            - network_id: "1"
              network_name:
              - network_name
              - network_name
          - country_code: IN
            country_name: India
            networks:
            - network_id: "1"
              network_name:
              - network_name
              - network_name
            - network_id: "1"
              network_name:
              - network_name
              - network_name
          region_code: "1"
        - region_name: Asia
          countries:
          - country_code: IN
            country_name: India
            networks:
            - network_id: "1"
              network_name:
              - network_name
              - network_name
            - network_id: "1"
              network_name:
              - network_name
              - network_name
          - country_code: IN
            country_name: India
            networks:
            - network_id: "1"
              network_name:
              - network_name
              - network_name
            - network_id: "1"
              network_name:
              - network_name
              - network_name
          region_code: "1"
        networks_count: 1000
    UpdateVoucherAttributesResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          maxLength: 100
          minLength: 0
          pattern: "^[\\s\\S]+$"
          type: string
          example: Voucher Updated Successfully.
        voucher_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        response_code: "1"
        developer_message: Operation Succcessful
        voucher_id: 507f191e810c19729de860ea
        title: Success
        message: Voucher Updated Successfully.
    UpdateVoucherAttributesRequest:
      required:
      - status
      type: object
      properties:
        status:
          type: string
          enum:
          - active
          - inactive
      additionalProperties: false
    ResellerScopedBundle:
      type: object
      properties:
        bundle_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        bundle_name:
          type: string
        supported_countries:
          type: array
          items:
            type: string
        region:
          type: string
        data_size:
          type: string
        data_unit:
          type: string
        validity_days:
          type: integer
        price:
          type: number
    ScopedBundlesResponse:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total_bundles:
          type: integer
        bundles:
          type: array
          items:
            $ref: "#/components/schemas/ResellerScopedBundle"
    RegionGroupedBundlesResponse:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total_bundles:
          type: integer
        total_regions:
          type: integer
        regions:
          type: array
          items:
            $ref: "#/components/schemas/RegionGroupedBundlesResponse_regions"
    inline_response_204:
      type: object
      properties:
        status:
          type: string
        title:
          type: string
        detail:
          type: string
      additionalProperties: false
    AvailableCurrenciesCSV_body:
      required:
      - file
      type: object
      properties:
        file:
          type: string
          format: binary
      additionalProperties: false
    Admin_custom_corporate_price_csv_body:
      required:
      - file
      type: object
      properties:
        file:
          type: string
          format: binary
      additionalProperties: false
    Reseller_custom_price_csv_body:
      required:
      - file
      type: object
      properties:
        file:
          type: string
          format: binary
      additionalProperties: false
    Orders_ResendEmail_body:
      required:
      - order_id
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
    Orders_SendConsumptionEmail_body:
      required:
      - order_id
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
    NetworkListCSV_body:
      required:
      - file
      type: object
      properties:
        file:
          type: string
          format: binary
      additionalProperties: false
    inline_response_200:
      oneOf:
      - $ref: "#/components/schemas/ScopedBundlesResponse"
      - $ref: "#/components/schemas/RegionGroupedBundlesResponse"
    CreateRoleRequest_permissions:
      type: object
      properties:
        api_name:
          type: string
          example: topupBundle
        permission_type:
          type: string
          example: Self
      example:
        permission_type: Self
        api_name: topupBundle
    GetAllRolesResponse_inner:
      required:
      - access_level
      - name
      - permissions
      - role_id
      type: object
      properties:
        role_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        name:
          maxLength: 20
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
        permission_level:
          maximum: 3
          minimum: 1
          type: integer
          format: int32
        access_level:
          type: string
          example: basic
          enum:
          - basic
          - medium
          - sensitive
        description:
          maxLength: 2000
          minLength: 0
          pattern: "^[a-zA-Z .,']+$"
          type: string
          example: Has Full Authority Over Reseller
        permissions:
          type: array
          items:
            type: object
            properties:
              api_name:
                type: string
                example: topupBundle
              permission_type:
                type: string
                example: Self
            example:
              permission_type: Self
              api_name: topupBundle
      additionalProperties: false
      example:
        access_level: basic
        permission_level: 1
        role_id: 507f191e810c19729de860ea
        permissions:
        - permission_type: Self
          api_name: topupBundle
        - permission_type: Self
          api_name: topupBundle
        name: ResellerAdmin
        description: Has Full Authority Over Reseller
    GetBundlesResponse_bundles:
      type: object
      properties:
        bundle_tag:
          type: array
          description: will exist only if value inserted by reseller
          items:
            type: string
        is_active:
          type: boolean
          description: Indicates whether the bundle is currently active and available
            for purchase.
        is_active_corp:
          type: boolean
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
        bundle_code:
          type: string
          description: A unique identifier for the bundle
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_category:
          type: string
          description: Classification of the bundle
        country_code:
          uniqueItems: true
          type: array
          items:
            maxLength: 3
            minLength: 3
            type: string
            example: USA
        country_name:
          uniqueItems: true
          type: array
          items:
            type: string
            example: United States
        region_code:
          type: string
          nullable: true
          example: AS
        region_name:
          type: string
          nullable: true
          example: Asia
        currency_code_list:
          uniqueItems: true
          type: array
          items:
            type: string
            enum:
            - USD
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        data_unit:
          type: string
          description: The unit of measurement for data.
          enum:
          - MB
          - GB
          - TB
        gprs_limit:
          type: number
          description: The maximum data usage limit.
          format: float
          example: 10
        subscriber_price:
          type: number
          format: float
          example: 16.7
        subscriber_price_in_additional_currency:
          type: number
          format: float
          example: 16.7
        reseller_retail_price:
          type: number
          description: The retail price set by the reseller in the primary currency.
          format: float
          example: 20.7
        reseller_retail_price_in_additional_currency:
          type: number
          description: The reseller retail price converted into the `additional_currency_code`.
          format: float
          example: 20.7
        validity:
          type: integer
          description: The validity period of the package in days.
          example: 30
        unlimited:
          type: boolean
          description: Indicates whether the package has unlimited data usage.
        refill_group:
          type: string
        support_topup:
          type: boolean
          description: Indicates whether the package supports top-ups (additional
            data purchases).
        supplier_vendor:
          type: string
          description: The name of the vendor or supplier providing the data package.
      additionalProperties: false
      example:
        is_active_corp: true
        reseller_retail_price: 20.7
        bundle_tag:
        - bundle_tag
        - bundle_tag
        currency_code_list:
        - USD
        - USD
        subscriber_price_in_additional_currency: 16.7
        is_active: true
        unlimited: true
        gprs_limit: 10
        reseller_retail_price_in_additional_currency: 20.7
        bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
        data_unit: MB
        bundle_category: bundle_category
        country_code:
        - USA
        - USA
        supplier_vendor: supplier_vendor
        bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        subscriber_price: 16.7
        support_topup: true
        bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        country_name:
        - United States
        - United States
        region_name: Asia
        validity: 30
        additional_currency_code: SAR
        refill_group: refill_group
        region_code: AS
    GetCountriesResponse_countries:
      type: object
      properties:
        iso3_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        iso2_code:
          maxLength: 2
          minLength: 2
          type: string
          example: FR
        country_name:
          type: string
          example: France
      additionalProperties: false
      example:
        iso3_code: FRA
        iso2_code: FR
        country_name: France
    GetCurrenciesResponse_currencies:
      type: object
      properties:
        currency_code:
          maxLength: 15
          type: string
          example: SAR
        currency name:
          type: string
          example: Saudi Arabian Rial
        currency_rate:
          minimum: 0
          type: number
          format: float
        is_available:
          type: boolean
        last_modified:
          type: string
          format: date
          readOnly: true
      additionalProperties: false
      example:
        currency_rate: 0.08008282
        currency name: Saudi Arabian Rial
        last_modified: 2000-01-23
        currency_code: SAR
        is_available: true
    GetRegionsResponse_regions:
      type: object
      properties:
        region_code:
          type: string
          example: FRA
        region_name:
          type: string
          example: France
      additionalProperties: false
      example:
        region_name: France
        region_code: FRA
    GetVouchersResponse_vouchers:
      required:
      - amount
      - expiry_datetime
      - reseller_amount
      - voucher_code
      - voucher_name
      type: object
      properties:
        voucher_name:
          maxLength: 30
          type: string
          example: Adventure30
        voucher_code:
          type: string
        amount:
          minimum: 0
          type: number
          format: float
          example: 30
        reseller_amount:
          minimum: 0
          type: number
          format: float
          example: 30
        is_used:
          type: boolean
        is_active:
          type: boolean
          readOnly: true
        expiry_datetime:
          type: string
          format: date-time
        datetime:
          type: string
          format: date-time
        reason:
          maxLength: 300
          type: string
          example: "When adventure calls, you gotta answer!"
        voucher_type:
          maxLength: 30
          type: string
        status:
          maxLength: 30
          type: string
        voucher_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        reason: "When adventure calls, you gotta answer!"
        amount: 30
        is_active: true
        voucher_name: Adventure30
        voucher_code: voucher_code
        reseller_amount: 30
        is_used: true
        datetime: 2000-01-23T04:56:07.000+00:00
        expiry_datetime: 2000-01-23T04:56:07.000+00:00
        voucher_type: voucher_type
        voucher_id: 507f191e810c19729de860ea
        reseller_id: 507f191e810c19729de860ea
        status: status
    GetVouchersBundledResponse_vouchers:
      required:
      - expiry_datetime
      - quantity
      - voucher_name
      type: object
      properties:
        voucher_name:
          maxLength: 30
          type: string
          example: Adventure30
        quantity:
          minimum: 1
          type: integer
          format: int32
        expiry_datetime:
          type: string
          format: date-time
        datetime:
          type: string
          format: date-time
        voucher_type:
          maxLength: 30
          type: string
          default: default
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
      additionalProperties: false
      example:
        datetime: 2000-01-23T04:56:07.000+00:00
        quantity: 1
        expiry_datetime: 2000-01-23T04:56:07.000+00:00
        voucher_name: Adventure30
        voucher_type: default
        reseller_id: 507f191e810c19729de860ea
    Reseller_contact:
      required:
      - emails
      - phones
      type: object
      properties:
        emails:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
            type: string
            example: <EMAIL>
        phones:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^\\+(?:[0-9] ?){6,14}[0-9]$"
            type: string
        website:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        address:
          maxLength: 300
          type: string
      example:
        emails:
        - <EMAIL>
        - <EMAIL>
        website: www.xyz.com
        address: address
        phones:
        - phones
        - phones
    Reseller_email_settings:
      required:
      - password
      - smtp_port
      - smtp_server
      - username
      type: object
      properties:
        username:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        password:
          maximum: 50
          minimum: 8
          type: string
        smtp_server:
          type: string
        smtp_port:
          maxLength: 5
          pattern: "^[0-9]*$"
          type: string
      additionalProperties: false
      example:
        password: password
        smtp_port: smtp_port
        smtp_server: smtp_server
        username: <EMAIL>
    Reseller_data_consumption_email:
      type: object
      properties:
        greetings:
          type: string
        body_1:
          type: string
        body_2:
          type: string
        whatsapp_number:
          type: string
        instagram_link:
          type: string
        facebook_link:
          type: string
        website_link:
          type: string
        email-image_type:
          type: string
        email_image_consumption:
          type: string
        email-logo_type:
          type: string
        email_logo:
          type: string
        company_name_team:
          type: string
        subject_consumption:
          type: string
        footer:
          type: string
      example:
        website_link: website_link
        footer: footer
        email_image_consumption: email_image_consumption
        facebook_link: facebook_link
        email-image_type: email-image_type
        greetings: greetings
        company_name_team: company_name_team
        body_1: body_1
        body_2: body_2
        instagram_link: instagram_link
        email_logo: email_logo
        email-logo_type: email-logo_type
        subject_consumption: subject_consumption
        whatsapp_number: whatsapp_number
    Reseller_data_expired_email:
      type: object
      properties:
        greetings:
          type: string
        body_1:
          type: string
        body_2:
          type: string
        whatsapp_number:
          type: string
        instagram_link:
          type: string
        facebook_link:
          type: string
        website_link:
          type: string
        email-image_type:
          type: string
        email_image_expired:
          type: string
        email-logo_type:
          type: string
        email_logo:
          type: string
        company_name_team:
          type: string
        subject_expired:
          type: string
        footer:
          type: string
      example:
        website_link: website_link
        footer: footer
        facebook_link: facebook_link
        email-image_type: email-image_type
        greetings: greetings
        company_name_team: company_name_team
        body_1: body_1
        body_2: body_2
        instagram_link: instagram_link
        email_logo: email_logo
        email-logo_type: email-logo_type
        subject_expired: subject_expired
        whatsapp_number: whatsapp_number
        email_image_expired: email_image_expired
    Reseller_qr_code_email:
      type: object
      properties:
        greetings:
          type: string
        body_1:
          type: string
        body_2:
          type: string
        android_users:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string
        ios_users:
          type: string
        matching_id:
          type: string
        invoice_details:
          type: string
        data_bundle_details:
          type: string
        whatsapp_number:
          type: string
        instagram_link:
          type: string
        facebook_link:
          type: string
        website_link:
          type: string
        email-image_type:
          type: string
        email_image_qrcode:
          type: string
        email-logo_type:
          type: string
        email_logo:
          type: string
        company_name_team:
          type: string
        bundle_consumption_sentence:
          type: string
        bundle_consumption_link:
          type: string
        topup_sentence:
          type: string
        topup_link:
          type: string
        instructions_link:
          type: string
        subject_qrcode:
          type: string
        footer:
          type: string
      example:
        website_link: website_link
        activation_code: activation_code
        bundle_consumption_link: bundle_consumption_link
        topup_sentence: topup_sentence
        footer: footer
        android_users: android_users
        facebook_link: facebook_link
        email-image_type: email-image_type
        greetings: greetings
        email_image_qrcode: email_image_qrcode
        company_name_team: company_name_team
        body_1: body_1
        body_2: body_2
        topup_link: topup_link
        instagram_link: instagram_link
        whatsapp_number: whatsapp_number
        matching_id: matching_id
        invoice_details: invoice_details
        instructions_link: instructions_link
        subject_qrcode: subject_qrcode
        data_bundle_details: data_bundle_details
        smdp_address: smdp_address
        ios_users: ios_users
        email_logo: email_logo
        email-logo_type: email-logo_type
        bundle_consumption_sentence: bundle_consumption_sentence
    AddResellerRequest_agent:
      required:
      - email
      - name
      - password
      - username
      type: object
      properties:
        email:
          pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
          type: string
          example: <EMAIL>
        username:
          maxLength: 30
          minLength: 1
          type: string
          example: john.snow
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
        password:
          maxLength: 30
          pattern: "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})"
          type: string
          writeOnly: true
          example: $3343JcS2412345
      additionalProperties: false
    EditResellerRequest_email_settings:
      type: object
      properties:
        username:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        password:
          maximum: 50
          minimum: 8
          type: string
        smtp_server:
          type: string
        smtp_port:
          minimum: 1
          maxLength: 5
          pattern: "^[0-9]*$"
          type: string
      additionalProperties: false
    GetResellersResponse_resellers:
      required:
      - balance
      - contact
      - currency_code
      - date_created
      - is_active
      - reseller_id
      - reseller_name
      - reseller_type
      - support_topup
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        reseller_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        reseller_type:
          type: string
          default: prepaid
          enum:
          - prepaid
          - postpaid
        callback_url:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        support_topup:
          type: boolean
          default: false
        is_active:
          type: boolean
          default: false
        supports_multibranches:
          type: boolean
          default: false
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        date_created:
          type: string
          format: date
          readOnly: true
        currency_code:
          type: string
          default: USD
          enum:
          - USD
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        default_currency_code:
          maxLength: 15
          type: string
          default: USD
        balance:
          minimum: 0
          type: number
          format: float
        balance_in_additional_currency:
          minimum: 0
          type: number
          format: float
        balance_warning_limit:
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 1000
        credit_limit:
          minimum: 0
          type: number
          description: postpaid limit
          format: float
          default: 0
        credit_warning_limit:
          maximum: 100
          minimum: 0
          type: number
          description: credit warning limit in percentage
          format: float
          default: 0
        credit_limit_in_additional_currency:
          minimum: 0
          type: number
          format: float
        rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: rate revenue in percent
          format: float
          example: 12
          default: 0
        corp_rate_revenue:
          maximum: 200
          minimum: 0
          type: number
          description: corporate revenue in percent
          format: float
          example: 12
        voucher_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
          example: 12
        contact:
          $ref: "#/components/schemas/Reseller_contact"
        email_settings:
          $ref: "#/components/schemas/Reseller_email_settings"
        custom_email_template_qr:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_data:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        custom_email_template_expired:
          maxLength: 1048576
          type: string
          description: Preferably HTML FILE
          format: binary
        image:
          type: string
          description: The image file of the user
          format: binary
        image_type:
          type: string
        is_whitelabel:
          type: boolean
          default: false
        tenant_name:
          type: string
        reseller_category:
          type: string
        active_vendors_list:
          uniqueItems: true
          type: array
          items:
            type: string
        vendors_for_balance_deduction_list:
          uniqueItems: true
          type: array
          items:
            type: string
        request_custom_email:
          type: boolean
          default: true
        data_consumption_email:
          $ref: "#/components/schemas/Reseller_data_consumption_email"
        data_expired_email:
          $ref: "#/components/schemas/Reseller_data_expired_email"
        qr_code_email:
          $ref: "#/components/schemas/Reseller_qr_code_email"
      additionalProperties: false
      example:
        balance_in_additional_currency: 0.6027456
        response_code: "1"
        developer_message: Operation Succcessful
        corp_rate_revenue: 12
        custom_email_template_data: ""
        active_vendors_list:
        - active_vendors_list
        - active_vendors_list
        data_expired_email:
          website_link: website_link
          footer: footer
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          instagram_link: instagram_link
          email_logo: email_logo
          email-logo_type: email-logo_type
          subject_expired: subject_expired
          whatsapp_number: whatsapp_number
          email_image_expired: email_image_expired
        voucher_rate: 12
        title: Success
        vendors_for_balance_deduction_list:
        - vendors_for_balance_deduction_list
        - vendors_for_balance_deduction_list
        currency_code: USD
        data_consumption_email:
          website_link: website_link
          footer: footer
          email_image_consumption: email_image_consumption
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          instagram_link: instagram_link
          email_logo: email_logo
          email-logo_type: email-logo_type
          subject_consumption: subject_consumption
          whatsapp_number: whatsapp_number
        callback_url: www.xyz.com
        balance: 0.08008282
        rate_revenue: 12
        contact:
          emails:
          - <EMAIL>
          - <EMAIL>
          website: www.xyz.com
          address: address
          phones:
          - phones
          - phones
        supports_promo: true
        credit_limit: 0.14658129
        additional_currency_code: SAR
        reseller_id: 507f191e810c19729de860ea
        image_type: image_type
        image: ""
        is_whitelabel: false
        custom_email_template_expired: ""
        tenant_name: tenant_name
        is_active: false
        default_currency_code: USD
        qr_code_email:
          website_link: website_link
          activation_code: activation_code
          bundle_consumption_link: bundle_consumption_link
          topup_sentence: topup_sentence
          footer: footer
          android_users: android_users
          facebook_link: facebook_link
          email-image_type: email-image_type
          greetings: greetings
          email_image_qrcode: email_image_qrcode
          company_name_team: company_name_team
          body_1: body_1
          body_2: body_2
          topup_link: topup_link
          instagram_link: instagram_link
          whatsapp_number: whatsapp_number
          matching_id: matching_id
          invoice_details: invoice_details
          instructions_link: instructions_link
          subject_qrcode: subject_qrcode
          data_bundle_details: data_bundle_details
          smdp_address: smdp_address
          ios_users: ios_users
          email_logo: email_logo
          email-logo_type: email-logo_type
          bundle_consumption_sentence: bundle_consumption_sentence
        date_created: 2000-01-23
        balance_warning_limit: 1000
        credit_warning_limit: 59.621338
        email_settings:
          password: password
          smtp_port: smtp_port
          smtp_server: smtp_server
          username: <EMAIL>
        request_custom_email: true
        support_topup: false
        supports_multibranches: false
        reseller_name: Nakhal
        reseller_type: prepaid
        supports_vouchers: true
        reseller_category: reseller_category
        credit_limit_in_additional_currency: 0.5637377
        custom_email_template_qr: ""
    CustomizePriceRequest_bundles:
      required:
      - bundle_code
      type: object
      properties:
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        custom_price:
          maximum: 99999999999999
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 12
        custom_bundle_name:
          maxLength: 100
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_tag:
          type: array
          items:
            maxLength: 30
            type: string
        is_active:
          type: boolean
      additionalProperties: false
    CustomizeCorpPriceRequest_bundles:
      required:
      - bundle_code
      type: object
      properties:
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        custom_price:
          maximum: 99999999999999
          minimum: 0
          type: number
          description: in dollars
          format: float
          example: 12
        is_active:
          type: boolean
      additionalProperties: false
    CustomizePriceResponse_invalid_bundle_prices:
      type: object
      properties:
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        custom_price:
          type: number
          description: in dollars
          format: float
          example: 12
        unit_price:
          type: number
          description: in dollars
          format: float
          example: 12
      additionalProperties: false
      example:
        bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        custom_price: 12
        unit_price: 12
    CustomizePriceResponse_bundles_not_found:
      type: object
      properties:
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
      additionalProperties: false
      example:
        bundle_code: esim_10gb_30days_unitedstates__0905202216214562
    CustomizeCurrenciesRequest_currencies:
      required:
      - currency_code
      type: object
      properties:
        currency_code:
          maxLength: 15
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: SAR
        currency_rate:
          maximum: 99999999999999
          minimum: 0
          type: number
          description: rate for one US dollar
          format: float
          example: 12
        currency_name:
          maxLength: 100
          type: string
          example: Saudi Arabian Rial
        is_available:
          type: boolean
      additionalProperties: false
    CustomizeCurrenciesResponse_invalid_currency_rates:
      type: object
      properties:
        currency_code:
          type: string
          example: ZZZ
        currency_rate:
          type: number
          format: float
          example: 12
      additionalProperties: false
      example:
        currency_rate: 12
        currency_code: ZZZ
    CustomizeCurrenciesResponse_currencies_not_found:
      type: object
      properties:
        currency_code:
          type: string
          example: SAR
        currency_name:
          type: string
          example: Saudii Arabian Rial
      additionalProperties: false
      example:
        currency_name: Saudii Arabian Rial
        currency_code: SAR
    Branch_contact:
      required:
      - emails
      - phones
      type: object
      properties:
        emails:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-]+)(\\.[a-zA-Z]{2,5}){1,2}$"
            type: string
            example: <EMAIL>
        phones:
          minItems: 1
          uniqueItems: true
          type: array
          items:
            pattern: "^\\+(?:[0-9] ?){6,14}[0-9]$"
            type: string
        website:
          pattern: "(http(s)?:\\/\\/.)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\\
            b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)"
          type: string
          nullable: true
          example: www.xyz.com
        address:
          maxLength: 300
          type: string
      nullable: true
      example:
        emails:
        - <EMAIL>
        - <EMAIL>
        website: www.xyz.com
        address: address
        phones:
        - phones
        - phones
    GetBranchesResponse_branches:
      required:
      - branch_id
      - branch_name
      - date_created
      - is_active
      - reseller_id
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        branch_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Nakhal
        is_active:
          type: boolean
          default: true
        date_created:
          type: string
          format: date
          readOnly: true
        limit:
          maximum: 1000000000000000
          minimum: -1
          exclusiveMinimum: false
          type: number
          format: float
          nullable: true
        limit_consumption:
          maximum: 1000000000000000
          minimum: 0
          type: number
          format: float
          nullable: true
        contact:
          $ref: "#/components/schemas/Branch_contact"
      additionalProperties: false
      example:
        response_code: "1"
        is_active: true
        branch_id: 507f191e810c19729de860ea
        developer_message: Operation Succcessful
        date_created: 2000-01-23
        branch_name: Nakhal
        contact:
          emails:
          - <EMAIL>
          - <EMAIL>
          website: www.xyz.com
          address: address
          phones:
          - phones
          - phones
        limit: 6.0274564E14
        limit_consumption: 1.46581295E14
        title: Success
        reseller_id: 507f191e810c19729de860ea
    GetAgentsResponse_agents:
      required:
      - agent_id
      - date_created
      - email
      - is_active
      - name
      - reseller_id
      - role_name
      - username
      type: object
      properties:
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        agent_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          readOnly: true
          example: 507f191e810c19729de860ea
        email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        username:
          maximum: 30
          minimum: 1
          type: string
          example: john.doe
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: john snow
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_name:
          maxLength: 45
          minLength: 0
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: ResellerAdmin
      additionalProperties: false
      example:
        role_name: ResellerAdmin
        is_active: false
        agent_id: 507f191e810c19729de860ea
        branch_id: 507f191e810c19729de860ea
        date_created: 2000-01-23T04:56:07.000+00:00
        name: john snow
        reseller_id: 507f191e810c19729de860ea
        email: <EMAIL>
        username: john.doe
    GetPromoDashboardResponse_sales_per_day:
      type: object
      properties:
        commission_amount:
          type: number
          format: float
          example: 0.522
        bundles_sold:
          minimum: 0
          type: integer
          example: 2
        total_sales_volume:
          type: number
          format: float
          example: 5.22
        date:
          type: string
          example: 2023-04-27
      additionalProperties: false
      example:
        date: 2023-04-27
        bundles_sold: 2
        commission_amount: 0.522
        total_sales_volume: 5.22
    GetDashboardResponse_top_five_bundles:
      type: object
      properties:
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        sales_number:
          minimum: 0
          type: integer
      additionalProperties: false
      example:
        bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
        sales_number: 0
    GetDashboardResponse_bundles_sold:
      type: object
      properties:
        date:
          pattern: "^\\d{4}-\\d{2}$"
          type: string
        sales_number:
          minimum: 0
          type: integer
      additionalProperties: false
      example:
        date: date
        sales_number: 0
    GetTransactionHistoryResponse_transactions:
      type: object
      properties:
        type:
          type: string
        amount:
          type: number
          format: float
          example: 20
        amount_additional_currency:
          type: number
          format: float
          example: 20
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
        date_created:
          type: string
          format: date-time
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        destination_branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        transaction_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        refund_reason:
          type: string
          example: User Did not have good coverage.
      additionalProperties: false
      example:
        transaction_id: 507f191e810c19729de860ea
        amount: 20
        branch_id: 507f191e810c19729de860ea
        date_created: 2000-01-23T04:56:07.000+00:00
        destination_branch_id: 507f191e810c19729de860ea
        refund_reason: User Did not have good coverage.
        type: type
        additional_currency_code: SAR
        order_id: 507f191e810c19729de860ea
        amount_additional_currency: 20
        reseller_id: 507f191e810c19729de860ea
    GetPromocodeHistoryResponse_orders:
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        promocode:
          type: string
          example: Monty2023
        affiliate_program:
          type: boolean
        affiliate_marketing_message:
          maxLength: 1000
          type: string
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
          type: string
          example: 6380d4efa3fb4
        plan_status:
          type: string
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        matching_id:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string
        client_name:
          type: string
        client_email:
          pattern: "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$"
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        refund_reason:
          type: string
          example: User Did not have good coverage.
        commission_rate:
          maximum: 100
          minimum: 0
          type: number
          format: float
        commision_amount:
          type: number
          format: float
      additionalProperties: false
      example:
        affiliate_program: true
        activation_code: activation_code
        bundle_name: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        affiliate_marketing_message: affiliate_marketing_message
        order_status: Successful
        remaining_wallet_balance: 1000
        iccid: iccid
        bundle_code: esim_10gb_30days_unitedstates__0905202216214562
        branch_id: 507f191e810c19729de860ea
        country_name:
        - United States
        - United States
        refund_reason: User Did not have good coverage.
        client_name: client_name
        reseller_id: 507f191e810c19729de860ea
        plan_uid: 6380d4efa3fb4
        matching_id: matching_id
        date_created: 2000-01-23T04:56:07.000+00:00
        client_email: <EMAIL>
        promocode: Monty2023
        bundle_price: 12
        smdp_address: smdp_address
        bundle_category: country
        country_code:
        - USA
        - USA
        commision_amount: 1.4658129
        bundle_marketing_name: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_retail_price: 12
        commission_rate: 60.274563
        order_id: 507f191e810c19729de860ea
        plan_status: plan_status
    GetVoucherUseHistoryResponse_data:
      type: object
      properties:
        voucher_use_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        voucher_code:
          type: string
          example: Adventure30
        voucher_name:
          type: string
          example: Adventure30
        username:
          maxLength: 20
          minLength: 1
          type: string
          example: john snow
        amount:
          type: number
          format: float
        date_created:
          type: string
          format: date-time
        currency_code:
          type: string
          enum:
          - USD
      additionalProperties: false
      example:
        amount: 6.0274563
        branch_id: 507f191e810c19729de860ea
        voucher_name: Adventure30
        date_created: 2000-01-23T04:56:07.000+00:00
        voucher_use_id: 507f191e810c19729de860ea
        voucher_code: Adventure30
        reseller_id: 507f191e810c19729de860ea
        currency_code: USD
        username: john snow
    IssueReport_feedback:
      required:
      - satisfaction
      type: object
      properties:
        satisfaction:
          maximum: 5
          minimum: 0
          type: integer
          description: "User satisfaction rating (e.g., 1-5, with 5 being the highest)."
          example: 4
        comments:
          type: string
          description: Additional comments on the resolution process.
      additionalProperties: false
      example:
        comments: comments
        satisfaction: 4
    IssueReport_chat:
      type: object
      properties:
        message:
          maxLength: 10000
          type: string
          description: Detailed description of the issue or concern.
        attachement:
          type: string
          description: Attachement Url
        date_created:
          type: string
          format: date
          readOnly: true
        added_by:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        added_by_name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: John Wick
        added_by_type:
          type: string
          readOnly: true
        file_size:
          type: number
          description: in kiloBytes
          format: float
          example: 16.7
      additionalProperties: false
      example:
        added_by_type: added_by_type
        added_by: 507f191e810c19729de860ea
        date_created: 2000-01-23
        attachement: attachement
        added_by_name: John Wick
        message: message
        file_size: 16.7
    IssueReport_issues:
      type: object
      properties:
        feedback:
          $ref: "#/components/schemas/IssueReport_feedback"
        report_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        added_by:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        date_created:
          type: string
          format: date
          readOnly: true
        last_modified:
          type: string
          format: date
          readOnly: true
        chat:
          type: array
          items:
            $ref: "#/components/schemas/IssueReport_chat"
        requester:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: John Wick
        assignee:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Monty eSIM Support
        resolution:
          maxLength: 10000
          type: string
          description: Final resolution before closing the issue.
        status:
          type: string
          enum:
          - Open
          - Closed
          - Under Review
        issue_type:
          type: string
          description: "Type of the issue (e.g., \"Technical Issue\" or \"Price-related\
            \ Concern\")."
          enum:
          - Technical Issue
          - Price-related Concern
        issue_subject:
          maxLength: 500
          type: string
          description: Summary of Issue
        issue_description:
          maxLength: 10000
          type: string
          description: Detailed description of the issue or concern.
        priority:
          maximum: 4
          minimum: 1
          type: integer
          description: "Priority level of the issue (\"Critical :1\", \"High:2\",\
            \ \"Medium:3\", or \"Low:4\")."
          default: 3
      additionalProperties: false
      example:
        requester: John Wick
        issue_description: issue_description
        date_created: 2000-01-23
        issue_type: Technical Issue
        priority: 2
        resolution: resolution
        feedback:
          comments: comments
          satisfaction: 4
        added_by: 507f191e810c19729de860ea
        branch_id: 507f191e810c19729de860ea
        report_id: 507f191e810c19729de860ea
        chat:
        - added_by_type: added_by_type
          added_by: 507f191e810c19729de860ea
          date_created: 2000-01-23
          attachement: attachement
          added_by_name: John Wick
          message: message
          file_size: 16.7
        - added_by_type: added_by_type
          added_by: 507f191e810c19729de860ea
          date_created: 2000-01-23
          attachement: attachement
          added_by_name: John Wick
          message: message
          file_size: 16.7
        issue_subject: issue_subject
        assignee: Monty eSIM Support
        last_modified: 2000-01-23
        reseller_id: 507f191e810c19729de860ea
        status: Open
    GetIssueReportsResponse_inner:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        total_issues_count:
          minimum: 0
          type: integer
        issues:
          type: array
          items:
            type: object
            properties:
              feedback:
                required:
                - satisfaction
                type: object
                properties:
                  satisfaction:
                    maximum: 5
                    minimum: 0
                    type: integer
                    description: "User satisfaction rating (e.g., 1-5, with 5 being\
                      \ the highest)."
                    example: 4
                  comments:
                    type: string
                    description: Additional comments on the resolution process.
                additionalProperties: false
              report_id:
                maxLength: 24
                minLength: 24
                pattern: "^[0-9a-fA-F]{24}$"
                type: string
                format: hex
                example: 507f191e810c19729de860ea
              added_by:
                maxLength: 24
                minLength: 24
                pattern: "^[0-9a-fA-F]{24}$"
                type: string
                format: hex
                example: 507f191e810c19729de860ea
              reseller_id:
                maxLength: 24
                minLength: 24
                pattern: "^[0-9a-fA-F]{24}$"
                type: string
                format: hex
                example: 507f191e810c19729de860ea
              branch_id:
                maxLength: 24
                minLength: 24
                pattern: "^[0-9a-fA-F]{24}$"
                type: string
                format: hex
                example: 507f191e810c19729de860ea
              date_created:
                type: string
                format: date
                readOnly: true
              last_modified:
                type: string
                format: date
                readOnly: true
              chat:
                type: array
                items:
                  type: object
                  properties:
                    message:
                      maxLength: 10000
                      type: string
                      description: Detailed description of the issue or concern.
                    attachement:
                      type: string
                      description: Attachement Url
                    date_created:
                      type: string
                      format: date
                      readOnly: true
                    added_by:
                      maxLength: 24
                      minLength: 24
                      pattern: "^[0-9a-fA-F]{24}$"
                      type: string
                      format: hex
                      example: 507f191e810c19729de860ea
                    added_by_name:
                      maxLength: 20
                      minLength: 1
                      pattern: "^[a-zA-Z ']+$"
                      type: string
                      example: John Wick
                    added_by_type:
                      type: string
                      readOnly: true
                    file_size:
                      type: number
                      description: in kiloBytes
                      format: float
                      example: 16.7
                  additionalProperties: false
              requester:
                maxLength: 20
                minLength: 1
                pattern: "^[a-zA-Z ']+$"
                type: string
                example: John Wick
              assignee:
                maxLength: 20
                minLength: 1
                pattern: "^[a-zA-Z ']+$"
                type: string
                example: Monty eSIM Support
              resolution:
                maxLength: 10000
                type: string
                description: Final resolution before closing the issue.
              status:
                type: string
                enum:
                - Open
                - Closed
                - Under Review
              issue_type:
                type: string
                description: "Type of the issue (e.g., \"Technical Issue\" or \"Price-related\
                  \ Concern\")."
                enum:
                - Technical Issue
                - Price-related Concern
              issue_subject:
                maxLength: 500
                type: string
                description: Summary of Issue
              issue_description:
                maxLength: 10000
                type: string
                description: Detailed description of the issue or concern.
              priority:
                maximum: 4
                minimum: 1
                type: integer
                description: "Priority level of the issue (\"Critical :1\", \"High:2\"\
                  , \"Medium:3\", or \"Low:4\")."
                default: 3
            additionalProperties: false
      additionalProperties: false
    NetworkList_networks:
      type: object
      properties:
        network_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        vendor_name:
          maxLength: 100
          minLength: 2
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Saudi Arabian Rial
        country_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        operator_list:
          uniqueItems: true
          type: array
          items:
            maxLength: 100
            minLength: 2
            pattern: "^[a-zA-Z ']+$"
            type: string
            example: Voda
        is_shown:
          type: boolean
          default: true
      additionalProperties: false
      example:
        country_code: FRA
        network_id: 507f191e810c19729de860ea
        is_shown: true
        vendor_name: Saudi Arabian Rial
        operator_list:
        - Voda
        - Voda
    BundleNetworkList_networks:
      type: object
      properties:
        country_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        operator_list:
          uniqueItems: true
          type: array
          items:
            maxLength: 100
            minLength: 2
            pattern: "^[a-zA-Z ']+$"
            type: string
            example: Voda
      additionalProperties: false
      example:
        country_code: FRA
        operator_list:
        - Voda
        - Voda
    ManageNetworkListRequest_networks:
      required:
      - country_code
      - operator_list
      - vendor_name
      type: object
      properties:
        vendor_name:
          maxLength: 100
          minLength: 2
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Venderino
        country_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        operator_list:
          uniqueItems: true
          type: array
          items:
            maxLength: 100
            minLength: 2
            pattern: "^[a-zA-Z ']+$"
            type: string
            example: Voda
        is_shown:
          type: boolean
          default: true
      additionalProperties: false
    ManageNetworkListResponse_invalid_networks:
      type: object
      properties:
        country_code:
          type: string
          example: ZZZ
        vendor_name:
          type: string
          example: Johnnies net
      additionalProperties: false
      example:
        country_code: ZZZ
        vendor_name: Johnnies net
    NetworkListByRegionsResponse_networks:
      type: object
      properties:
        network_id:
          type: string
          description: Unique identifier for the network.
          example: "1"
        network_name:
          type: array
          items:
            type: string
      example:
        network_id: "1"
        network_name:
        - network_name
        - network_name
    NetworkListByRegionsResponse_countries:
      type: object
      properties:
        country_name:
          type: string
          description: Name of the country.
          example: India
        country_code:
          type: string
          description: ISO country code.
          example: IN
        networks:
          type: array
          description: List of networks in the country.
          items:
            $ref: "#/components/schemas/NetworkListByRegionsResponse_networks"
      additionalProperties: false
      example:
        country_code: IN
        country_name: India
        networks:
        - network_id: "1"
          network_name:
          - network_name
          - network_name
        - network_id: "1"
          network_name:
          - network_name
          - network_name
    NetworkListByRegionsResponse_networks_1:
      type: object
      properties:
        region_code:
          type: string
          description: Unique identifier for the region.
          example: "1"
        region_name:
          type: string
          description: Name of the region.
          example: Asia
        countries:
          type: array
          description: List of countries in the region.
          items:
            $ref: "#/components/schemas/NetworkListByRegionsResponse_countries"
      additionalProperties: false
      example:
        region_name: Asia
        countries:
        - country_code: IN
          country_name: India
          networks:
          - network_id: "1"
            network_name:
            - network_name
            - network_name
          - network_id: "1"
            network_name:
            - network_name
            - network_name
        - country_code: IN
          country_name: India
          networks:
          - network_id: "1"
            network_name:
            - network_name
            - network_name
          - network_id: "1"
            network_name:
            - network_name
            - network_name
        region_code: "1"
    RegionGroupedBundlesResponse_regions:
      type: object
      properties:
        region:
          type: string
        region_name:
          type: string
        bundles:
          type: array
          items:
            $ref: "#/components/schemas/ResellerScopedBundle"
  responses:
    "200":
      description: Ok
      headers:
        Cache-Control:
          style: simple
          explode: false
          schema:
            type: string
            default: no-store
            enum:
            - no-store
    "201":
      description: Created
      headers:
        Cache-Control:
          style: simple
          explode: false
          schema:
            type: string
            default: no-store
            enum:
            - no-store
    "400":
      description: Bad Request
      headers:
        Cache-Control:
          style: simple
          explode: false
          schema:
            type: string
            default: no-store
            enum:
            - no-store
    "403":
      description: Forbidden
      headers:
        Cache-Control:
          style: simple
          explode: false
          schema:
            type: string
            default: no-store
            enum:
            - no-store
    "204":
      description: Not Found'
      headers:
        Cache-Control:
          style: simple
          explode: false
          schema:
            type: string
            default: no-store
            enum:
            - no-store
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/inline_response_204"
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      name: Access-Token
      in: header
      x-apikeyInfoFunc: swagger_server.controllers.authorization_controller.check_ApiKeyAuth
    InternalApiKeyAuth:
      type: apiKey
      name: Internal-Token
      in: header
      x-apikeyInfoFunc: swagger_server.controllers.authorization_controller.check_InternalApiKeyAuth


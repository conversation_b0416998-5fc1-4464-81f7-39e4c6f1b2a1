from datetime import datetime, <PERSON>elta

import pymongo
from app_models.consumer_models import (
    Vend<PERSON>,
    Bundles,
    Profiles,
)
from app_models.reseller_models import Reseller
from helpers.constaints import FLEXIROAM_VENDOR, ESIMGO_VENDOR
from app_helpers import mongodb
from app_models.main_models import Settings, EmailSettings

from instance import consumer_config
from swagger_server.models import (
    CreateRoleRequestPermissions,
    CreateRoleRequest,
    AddResellerRequestAgent,
    AddBranchRequest,
    BranchContact,
)
from app_models.reseller_models import Order_history
from swagger_server.__main__ import app
from swagger_server.test.utils.db_utils import wipe_db_data_clean
from flask_testing import TestCase


class BaseTestSetup(TestCase):

    @classmethod
    def setUpClass(cls):
        cls.db = pymongo.MongoClient(consumer_config.new_host_).get_database(consumer_config.decrypted_db_name_alias)
        wipe_db_data_clean()

    def create_app(self):
        return app.app

    def setUp(self):
        self.settings = self._create_settings()
        self.email_setting = self._create_email_settings()
        self._create_bundles()
        self._create_vendors()
        self._create_profiles()
        self.reseller = self._create_reseller()
        self.agent = AddResellerRequestAgent(email="<EMAIL>", username="nozka", name="nuska", password="pass")

        self.role = self.create_role(name="BranchAdmin", description="admin role", permission_level=1,
                                     reseller_id=self.reseller.id)

        branch_contact = BranchContact(emails="<EMAIL>", phones="00159996157", website="we.branch.com",
                                       address="Cairo", )
        self.branch = self.create_branch(branch_name="cairo", is_active=True, contact=branch_contact,
                                         reseller_id=self.reseller.id, )
        self._create_orders()

    def _create_bundles(self):
        self.flexi_bundle = self._create_bundle(FLEXIROAM_VENDOR, "Africa10GB15days", "The World is Yours",
                                                "4G-released", "country", 10, "tete")
        self.esim_go_bundle = self._create_bundle(ESIMGO_VENDOR, "eSIM-go", "eSIM-go", "eSIM-go", "country", 20, "te")

    def _create_profiles(self):
        self.inv_flexi_bundle_profile = self._create_profile(FLEXIROAM_VENDOR, "12345678912345678914", "tete", True,
                                                             "Free", "Test-uid", self.flexi_bundle.bundle_code,
                                                             datetime.utcnow() + timedelta(365))
        self.ordered_profile = self._create_profile(FLEXIROAM_VENDOR, "12345678912345678915", "tete", True,
                                                    "Assigned", "Test-uid", "",
                                                    datetime.utcnow() + timedelta(365))

    def _create_orders(self):
        self.flexi_profile_ordered = self._create_order(self.ordered_profile, self.flexi_bundle, self.reseller.id,
                                                        self.branch.inserted_id, "ref-1234567890", 11.0)

    def _create_vendors(self):
        self.vendor = self._create_vendor(FLEXIROAM_VENDOR, "Fl", "me", 30, 1, "token1", True, True)
        self.esim_go_vendor = self._create_vendor(ESIMGO_VENDOR, "ESIM", "GO", 30, 1, "token2", True, True)

    def _create_bundle(self, vendor_name, bundle_name, marketing_name, bundle_code, category, unit_price,
                       profile_name=""):
        return Bundles.objects(bundle_code=bundle_code).first() or \
               Bundles(**{"vendor_name": vendor_name, "bundle_code": bundle_code, "bundle_name": bundle_name,
                          "bundle_marketing_name": marketing_name, "bundle_category": category,
                          "category_name": "region", "region_code": "af", "region_name": "Africa",
                          "bundle_vendor_code": bundle_code, "bundle_vendor_name": vendor_name,
                          "create_datetime": datetime.utcnow(),
                          "bundle_duration": 15, "unit_price": unit_price, "rate_revenue": 25, "retail_price": 1,
                          "currency_code": "USD",
                          "data_amount": 10, "fullspeed_data_amount": 10, "data_unit": "GB", "validity_amount": "15",
                          "allocated_unit": 10, "reserved_unit": 0, "consumed_unit": 0, "is_region": True,
                          "is_active": True,
                          "deleted": False, "country_list": ["Egypt"], "country_code_list": ["EG"],
                          "profile_names": profile_name,
                          "supplier_vendor": "", "daily_used": 0, "allocate_profiles": True,
                          "preview_for": ["subscriber", "reseller"],
                          "group_id": "1042", "unlimited": False, }).save()

    def _create_vendor(self, vendor_name, prefix, suffix, bundle_count, minimal_balance, token, is_active=True,
                       support_topup=True):
        return Vendors.objects(vendor_name=vendor_name).first() or Vendors(**{
            "vendor_name": vendor_name, "vendor_prefix": prefix, "vendor_suffix": suffix, "bundles_count": bundle_count,
            "minimal_balance": minimal_balance, "temp_token": token, "is_active": is_active,
            "number_of_expiry_days": None,
            "support_topup": support_topup, "vendor_expiry_days": 365}).save()

    def _create_profile(self, vendor_name, iccid, profile_name, status, availability, plan_uid=None, bundle_code=None,
                        expiry=None):
        return Profiles.objects(iccid=iccid).first() or Profiles(**{
            "vendor_name": vendor_name, "sku": "profile-id-unique", "iccid": iccid, "qr_code_value": "qr_code_value",
            "profile_names": profile_name, "smdp_address": "rsp.test.client.com", "matching_id": None,
            "create_datetime": datetime.utcnow(),
            "status": status, "availability": availability, "plan_uid": plan_uid, "bundle_code": bundle_code,
            "expiry_date": expiry, }).save()

    def _create_settings(self):
        return Settings.objects(contact_email="<EMAIL>").first() or Settings \
            (**{"contact_email": "<EMAIL>", "esim_email": "<EMAIL>",
                "merchant_key": "test", "client_keycloack_secret": "test-secret",
                "merchant_password": "1234567890", "fcm_registration": "123456", "whatsapp_misisdn": "0101517485",
                "reward_amount_limit_usd": 10, "percentage_of_reward": 10, "purchase_threshold_for_reward": 3,
                # limited_bundle minimum numbers of bundle that should be existed in inventory
                "limit_bundle": 5, }).save()

    def _create_email_settings(self):
        return EmailSettings.objects(email=consumer_config.email_login_).first() or EmailSettings \
            (**{"email": consumer_config.email_login_, "username": "test", "password": "test",
                "smtp_server": "protocol.com", "smtp_port": 1}).save()

    def _create_reseller(self):
        return Reseller.objects(reseller_name="Vodafone").first() or Reseller(
            **{"reseller_name": "Vodafone", "reseller_type": "prepaid", "support_topup": True, "is_active": True,
               "supports_multibranches": False, "currency_code": "USD", "balance": 500, "rate_revenue": 10,
               "corp_rate_revenue": 10, "reseller_category": "Internal", "contact": {"address": "address",
                "emails": ["<EMAIL>"], "phones": ["+971508157888"], "website": "www.xyz.com"},}).save()

    def _create_order(self, profile, bundle, reseller_id, branch_id, order_reference, bundle_price):
        return Order_history(
            **{"order_status": "Pending", "iccid": profile.iccid, "bundle_price": bundle_price,
               "bundle_code": bundle.bundle_code,
               "bundle_retail_price": 10, "bundle_marketing_name": bundle.bundle_marketing_name,
               "bundle_name": bundle.bundle_name,
               "matching_id": "0088:1144:5566:2233:0000", "smdp_address": "rsp.test.client.com",
               "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
               "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": bundle.bundle_code,
               "remaining_wallet_balance": 490, "bundle_category": bundle.bundle_category,
               "reseller_id": reseller_id, "branch_id": branch_id, "order_reference": order_reference, "reseller_type": "reseller"}).save()

    def create_permission(self, api_name, permission_type="get"):
        return CreateRoleRequestPermissions(api_name=api_name, permission_type=permission_type)

    def create_role(self, name, description, permission_level, reseller_id):
        # Creating permissions
        permissions = [
            self.create_permission("cancelBundle"),
            self.create_permission("reserveBundle"),
            self.create_permission("completeTransaction"),
            self.create_permission("createRole"),
            self.create_permission("deleteRole"),
            self.create_permission("editRole"),
            self.create_permission("getRoleById"),
            self.create_permission("getAllRoles"),
            self.create_permission("updateMontyAdminPermissions")
        ]
        role_model = CreateRoleRequest(name=name, description=description, permissions=permissions,
                                       permission_level=permission_level, access_level="sensitive")
        return mongodb.insert_one("roles", {**role_model.to_dict(), "reseller_id": reseller_id}, apply_Tenancy=0)

    def create_branch(self, branch_name, is_active, contact, reseller_id, limit=None, limit_consumption=None):
        branch_model = AddBranchRequest(branch_name=branch_name, is_active=is_active, limit=limit,
                                        limit_consumption=limit_consumption, contact=contact, agent=self.agent)
        return mongodb.insert_one("branch", {**branch_model.to_dict(), "reseller_id": reseller_id}, apply_Tenancy=0)

    def tearDown(self):
        wipe_db_data_clean()
        self.db.get_collection("branch").delete_many({})

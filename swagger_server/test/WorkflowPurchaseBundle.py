import sys
from swagger_server.test.test_welcome_controller import TestWelcomeController
from swagger_server.test.test_welcome_controller_two import TestWelcomeControllerTwo
import HtmlTestRunner
import unittest

try:
    if sys.version_info < (2, 7):
        import unittest2
    else:
        raise ImportError()
except ImportError:
    import unittest

@unittest.skip("Skip this unittest")
def suite():
    test_suite = unittest.TestSuite()
    test_suite.addTests([
        TestWelcomeController("test_health_check"),
        TestWelcomeController("test_health_check_one"),
        TestWelcomeController("test_health_check_two"),
        TestWelcomeControllerTwo("test_external")

    ])

    return test_suite


if __name__ == '__main__':
    test_runner = HtmlTestRunner.HTMLTestRunner(output='./html_report', combine_reports=True,report_name="Workflow_Report")
    test_runner.run(suite())
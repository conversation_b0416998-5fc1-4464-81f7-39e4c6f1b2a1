from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock
import unittest

import pymongo
from app_models.consumer_models import (
    Vendors,
    Bundles,
    region_names_,
    regions_,
    Profiles,
)
from app_models.reseller_models import Reseller
from helpers.constaints import VODAFONE_VENDOR, FLEXIROAM_VENDOR
from bson import ObjectId
from flask import json
from requests import JSONDecodeError
from urllib3.exceptions import NewConnectionError

from app_helpers import mongodb
from app_models.main_models import Settings, EmailSettings, RunnableScripts

from instance import consumer_config
from swagger_server.models import (
    CreateRoleRequestPermissions,
    CreateRoleRequest,
    AddResellerRequest,
    ResellerContact,
    AddResellerRequestAgent,
    ResellerEmailSettings,
    AddBranchRequest,
    BranchContact,
)
from swagger_server.models.assign_bundle_request import AssignBundleRequest
from swagger_server.models.reserve_bundle_request import ReserveBundleRequest
from swagger_server.models.complete_transaction_request import (
    CompleteTransactionRequest,
)
from swagger_server.models.topup_bundle_request import TopupBundleRequest
from swagger_server.__main__ import app
from flask_testing import TestCase


class TestReserveBundle(TestCase):
    """Reserve Bundle integration test stubs"""

    @classmethod
    def setUpClass(cls):
        print("Setting up the test class")
        contract = ResellerContact(
            emails=["<EMAIL>"],
            phones=["00159996157"],
            website="we.com",
            address="Cairo",
        )
        agent = AddResellerRequestAgent(
            email="<EMAIL>", username="nozka", name="nuska", password="pass"
        )
        cls.reseller_model = AddResellerRequest(
            reseller_name="Vodafone",
            reseller_type="prepaid",
            support_topup=True,
            is_active=True,
            supports_multibranches=False,
            currency_code="USD",
            balance=500,
            rate_revenue=10,
            # contact=contract,
            agent=agent,
            # email_settings=email,
            corp_rate_revenue=10,
            reseller_category="Internal",
        )

        # Ensure reseller is created once
        reseller = Reseller.objects(reseller_name="Vodafone").first()
        if not reseller:
            reseller = Reseller(**cls.reseller_model.to_dict()).save()

        cls.inserted_reseller_id = reseller.id
        print(f"Inserted reseller ID: {cls.inserted_reseller_id}")

    @classmethod
    def tearDownClass(cls):
        print("Tearing down the test class")
        Reseller.objects(reseller_name="Vodafone").delete()

    mongo_client = pymongo.MongoClient(consumer_config.new_host_)
    db = mongo_client.get_database(consumer_config.decrypted_db_name_alias)

    CUSTOMER_EMAIL = "<EMAIL>"
    MOCKED_KEYCLOAK_SERVER_URL = "http://localhost.mocked.keycloak.com"
    MOCKED_KEYCLOAK_CLIENT_ID = "admin-cli"
    MOCKED_KEYCLOAK_REALM_NAME = "master"
    MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = "0yyttrewqfssqddffgnbvcxww0020gfs"

    NEW_CONNECTION_ERROR_MESSAGE = """HTTPSConnectionPool(host='devapi.flexiroam.com', port=443): Max retries exceeded \
    with url: /plan/load/v1 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f2e4c3cca60>
    : Failed to establish a new connection: [Errno 111] Connection refused'))"""

    def create_app(self):
        return app.app

    def setUp(self):
        self.settings = (
            Settings.objects(contact_email="<EMAIL>").first()
            or Settings(
                **{
                    "contact_email": "<EMAIL>",
                    "esim_email": "<EMAIL>",
                    "merchant_key": "test",
                    "merchant_password": "1234567890",
                    "fcm_registration": "123456",
                    "whatsapp_misisdn": "0101517485",
                    "reward_amount_limit_usd": 10,
                    "percentage_of_reward": 10,
                    "purchase_threshold_for_reward": 3,
                    # limited_bundle minimum numbers of bundle that should be existed in inventory
                    "limit_bundle": 5,
                }
            ).save()
        )

        self.email_setting = (
            EmailSettings.objects(email=consumer_config.email_login_).first()
            or EmailSettings(
                **{
                    "email": consumer_config.email_login_,
                    "username": "test",
                    "password": "test",
                    "smtp_server": "protocol.com",
                    "smtp_port": 1,
                }
            ).save()
        )

        self.vendor = (
            Vendors.objects(vendor_name="Flexiroam").first()
            or Vendors(
                **{
                    "vendor_name": "Flexiroam",
                    "vendor_prefix": "Vo",
                    "vendor_suffix": "ne",
                    "bundles_count": 30,
                    "minimal_balance": 1,
                    "temp_token": "temp-access-token",
                    "is_active": True,
                    "number_of_expiry_days": None,
                    "support_topup": True,
                }
            ).save()
        )
        self.bundle = (
            Bundles.objects(bundle_code="4G-released").first()
            or Bundles(
                **{
                    "vendor_name": FLEXIROAM_VENDOR,
                    "bundle_code": "4G-released",
                    "bundle_name": "Africa10GB15days",
                    "bundle_marketing_name": "The World is Yours",
                    "bundle_category": "country",
                    "category_name": "region",
                    "region_code": "af",
                    "region_name": "Africa",
                    "bundle_vendor_code": "4G-released",
                    "bundle_vendor_name": self.vendor.vendor_name,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 15,
                    "unit_price": 1,
                    "rate_revenue": 25,
                    "retail_price": 1,
                    "currency_code": "USD",
                    "data_amount": 10,
                    "fullspeed_data_amount": 10,
                    "data_unit": "GB",
                    "validity_amount": "15",
                    "allocated_unit": 10,
                    "reserved_unit": 0,
                    "consumed_unit": 0,
                    "is_region": True,
                    "is_active": True,
                    "deleted": False,
                    "country_list": ["Egypt"],
                    "country_code_list": ["EG"],
                    "profile_names": "tete",
                    "supplier_vendor": "",
                    "daily_used": 0,
                    "allocate_profiles": True,
                    "preview_for": ["subscriber", "reseller"],
                    "group_id": "1042",
                    "unlimited": False,
                }
            ).save()
        )

        self.vodafone_vendor = (
            Vendors.objects(vendor_name=VODAFONE_VENDOR).first()
            or Vendors(
                **{
                    "vendor_name": VODAFONE_VENDOR,
                    "vendor_prefix": "vodafone",
                    "vendor_suffix": "Vo",
                    "bundles_count": 5,
                    "minimal_balance": 1,
                    "is_active": True,
                    "number_of_expiry_days": 90,
                    "apply_inventory": True,
                }
            ).save()
        )
        self.vodafone_bundle = (
            Bundles.objects(bundle_code="3G-Vo-released").first()
            or Bundles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "bundle_code": "3G-Vo-released",
                    "bundle_name": "3G",
                    "bundle_category": "country",
                    "retail_price": 2,
                    "supplier_vendor": f"{self.vendor}",
                    "bundle_marketing_name": "fastest-3G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "3G-Vo-released",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "is_active": True,
                    "unit_price": 2,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "te",
                    "country_list": ["EG"],
                    "country_code_list": ["EG"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                    "validity_amount": "29",
                    "bundle_vendor_name": self.vendor.vendor_name,
                }
            ).save()
        )

        self.cairo_vodafone_bundle = (
            Bundles.objects(bundle_code="3G-Vo-released-cairo").first()
            or Bundles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "bundle_code": "3G-Vo-released-cairo",
                    "bundle_name": "3G-C",
                    "bundle_category": "country",
                    "retail_price": 4,
                    "supplier_vendor": f"{self.vendor}",
                    "bundle_marketing_name": "-3G-cairo",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "3G-Vo-released-cairo",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "is_active": True,
                    "unit_price": 4,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "te",
                    "country_list": ["CA"],
                    "country_code_list": ["CA"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                    "validity_amount": "29",
                    "bundle_vendor_name": self.vendor.vendor_name,
                }
            ).save()
        )

        self.esim_go_vendor = (
            Vendors.objects(vendor_name="eSIMGo").first()
            or Vendors(
                **{
                    "vendor_name": "eSIMGo",
                    "vendor_prefix": "eSIMGo",
                    "vendor_suffix": "eSIMGo",
                    "bundles_count": 30,
                    "minimal_balance": 1,
                    "temp_token": "temp-access-token",
                    "is_active": True,
                }
            ).save()
        )

        self.alternative_esim_go_bundle_for_cairo_vodafone_bundle = (
            Bundles.objects(bundle_code="5G-released-cairo").first()
            or Bundles(
                **{
                    "vendor_name": self.esim_go_vendor.vendor_name,
                    "bundle_code": "5G-released-cairo",
                    "bundle_name": "5G-Cairo",
                    "bundle_category": "country",
                    "retail_price": 6,
                    "supplier_vendor": f"{self.esim_go_vendor}",
                    "bundle_marketing_name": "fastest-5G-cairo",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "5G-released-cairo",
                    "bundle_vendor_name": self.esim_go_vendor.vendor_name,
                    "validity_amount": "29",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 31,
                    "is_active": True,
                    "unit_price": 6,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "te",
                    "country_list": ["CA"],
                    "country_code_list": ["CA"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                }
            ).save()
        )

        self.esim_go_bundle = (
            Bundles.objects(bundle_code="5G-released").first()
            or Bundles(
                **{
                    "vendor_name": self.esim_go_vendor.vendor_name,
                    "bundle_code": "5G-released",
                    "bundle_name": "5G",
                    "bundle_category": "country",
                    "retail_price": 2,
                    "supplier_vendor": f"{self.esim_go_vendor}",
                    "bundle_marketing_name": "fastest-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "5G-released",
                    "bundle_vendor_name": self.esim_go_vendor.vendor_name,
                    "validity_amount": "29",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "is_active": True,
                    "unit_price": 20,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "te",
                    "country_list": ["FR"],
                    "country_code_list": ["FR"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                }
            ).save()
        )

        self.alternative_flexi_bundle_for_esim_go = (
            Bundles.objects(bundle_code="6G-released").first()
            or Bundles(
                **{
                    "vendor_name": FLEXIROAM_VENDOR,
                    "bundle_code": "6G-released",
                    "bundle_name": "6G",
                    "bundle_category": "country",
                    "retail_price": 7,
                    "supplier_vendor": f"{self.vendor}",
                    "bundle_marketing_name": "fastest-6G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "6G-released",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 35,
                    "is_active": True,
                    "unit_price": 7,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "6g",
                    "country_list": ["FR"],
                    "country_code_list": ["FR"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                    "validity_amount": "29",
                    "bundle_vendor_name": self.vendor.vendor_name,
                }
            ).save()
        )

        self.profile = (
            Profiles.objects(iccid="12345678912345678912").first()
            or Profiles(
                **{
                    "vendor_name": self.vendor.vendor_name,
                    "sku": "profile-id-unique",
                    "iccid": "12345678912345678912",
                    "qr_code_value": "qr_code_value",
                    "profile_names": "te",
                    "smdp_address": "rsp.test.client.com",
                    "matching_id": None,
                    "create_datetime": datetime.utcnow(),
                    "status": True,
                    "availability": "Free",
                }
            ).save()
        )

        self.another_profile = (
            Profiles.objects(iccid="12345678912345678922").first()
            or Profiles(
                **{
                    "vendor_name": self.vendor.vendor_name,
                    "sku": "profile-id-unique",
                    "iccid": "12345678912345678922",
                    "qr_code_value": "qr_code_value",
                    "profile_names": "te",
                    "smdp_address": "rsp.test.client.com",
                    "matching_id": None,
                    "create_datetime": datetime.utcnow(),
                    "status": True,
                    "availability": "Free",
                }
            ).save()
        )

        self.profile_6g = (
            Profiles.objects(iccid="12345678912345678222").first()
            or Profiles(
                **{
                    "vendor_name": self.vendor.vendor_name,
                    "sku": "profile-id-unique",
                    "iccid": "12345678912345678222",
                    "qr_code_value": "qr_code_value",
                    "profile_names": "6g",
                    "smdp_address": "rsp.test.client.com",
                    "matching_id": None,
                    "create_datetime": datetime.utcnow(),
                    "status": True,
                    "availability": "Free",
                }
            ).save()
        )

        self.alternative_profile_6g = (
            Profiles.objects(iccid="12345678912345675222").first()
            or Profiles(
                **{
                    "vendor_name": self.vendor.vendor_name,
                    "sku": "profile-id-unique",
                    "iccid": "12345678912345675222",
                    "qr_code_value": "qr_code_value",
                    "profile_names": "6g",
                    "smdp_address": "rsp.test.client.com",
                    "matching_id": None,
                    "create_datetime": datetime.utcnow(),
                    "status": True,
                    "availability": "Free",
                }
            ).save()
        )

        contract = ResellerContact(
            emails="<EMAIL>", phones="00159996157", website="we.com", address="Cairo"
        )
        agent = AddResellerRequestAgent(
            email="<EMAIL>", username="nozka", name="nuska", password="pass"
        )
        email = ResellerEmailSettings(
            username="us",
            password="Wu7Hlw==",
            smtp_server="smtp.office365.com",
            smtp_port="654",
        )
        self.reseller_model = AddResellerRequest(
            reseller_name="Vodafone",
            reseller_type="prepaid",
            support_topup=True,
            is_active=True,
            supports_multibranches=False,
            currency_code="USD",
            balance=500,
            rate_revenue=10,
            # contact=contract,
            agent=agent,
            # email_settings=email,
            corp_rate_revenue=10,
            reseller_category="Internal",
        )

        reseller = (
            Reseller.objects(reseller_name="Vodafone").first()
            or Reseller(**self.reseller_model.to_dict()).save()
        )
        # reseller = mongodb.find_one("reseller", search_query, apply_Tenancy=0)

        if not reseller:  # if the reseller entry doesn't exist
            # inserting new reseller
            reseller_insertion = mongodb.insert_one(
                "reseller", self.reseller_model.to_dict(), apply_Tenancy=0
            )

            reseller_id = reseller_insertion.inserted_id
            print(f"Inserted new reseller with ID: {reseller_id}")
        else:
            # if reseller already existed, get its '_id'
            reseller_id = reseller["id"]
            print(f"get reseller {reseller_id}")

        # self.inserted_reseller_id = reseller_id
        print("self.inserted_reseller_id ", self.inserted_reseller_id)

        permission = CreateRoleRequestPermissions(
            api_name="assignBundle", permission_type="get"
        )
        reserve_permission = CreateRoleRequestPermissions(
            api_name="reserveBundle", permission_type="get"
        )
        complete_permission = CreateRoleRequestPermissions(
            api_name="completeTransaction", permission_type="get"
        )
        self.role_model = CreateRoleRequest(
            name="BranchAdmin",
            description="admin role",
            permissions=[permission, reserve_permission, complete_permission],
            permission_level=1,
        )
        self.role = mongodb.insert_one(
            "roles",
            {**self.role_model.to_dict(), "reseller_id": self.inserted_reseller_id},
            apply_Tenancy=0,
        )

        branch_contract = BranchContact(
            emails="<EMAIL>",
            phones="00159996157",
            website="we.branch.com",
            address="Cairo",
        )
        self.branch_model = AddBranchRequest(
            branch_name="cairo",
            is_active=True,
            limit=None,
            limit_consumption=None,
            contact=branch_contract,
            agent=agent,
        )
        self.branch = mongodb.insert_one(
            "branch",
            {**self.branch_model.to_dict(), "reseller_id": self.inserted_reseller_id},
            apply_Tenancy=0,
        )

        self.branch_with_limit_model = AddBranchRequest(
            branch_name="cairo_with_limit",
            is_active=True,
            limit=1,
            limit_consumption=1,
            contact=branch_contract,
            agent=agent,
        )
        self.branch_with_limit = mongodb.insert_one(
            "branch",
            {
                **self.branch_with_limit_model.to_dict(),
                "reseller_id": self.inserted_reseller_id,
            },
            apply_Tenancy=0,
        )

        # setup data for testing esimgo bundle from inventory side effects profile expiry date will be None

        self.esimgo_reserved_bundle = (
            Bundles.objects(bundle_code="3G-esim-pre-reserved").first()
            or Bundles(
                **{
                    "vendor_name": self.esim_go_vendor.vendor_name,
                    "bundle_code": "3G-esim-pre-reserved",
                    "bundle_name": "esim-GA",
                    "bundle_category": "country",
                    "retail_price": 2,
                    "supplier_vendor": f"{self.esim_go_vendor}",
                    "bundle_marketing_name": "fastest-esimgo-G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "region_name": regions_[1],
                    "bundle_vendor_code": "3G-esimgo-pre-reserved",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "is_active": True,
                    "unit_price": 2,
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "profile_names": "",
                    "country_list": ["EG"],
                    "country_code_list": ["EG"],
                    "allocated_unit": 10,
                    "consumed_unit": 5,
                    "validity_amount": "29",
                    "bundle_vendor_name": self.esim_go_vendor.vendor_name,
                    "daily_used": 0,
                }
            ).save()
        )

        self.esimgo_reserved_profile = (
            Profiles.objects(iccid="33345678912345678003").first()
            or Profiles(
                **{
                    "vendor_name": self.esim_go_vendor.vendor_name,
                    "sku": "sku-reserve-esimgo",
                    "iccid": "33345678912345678003",
                    "qr_code_value": "qr_code_value",
                    "profile_names": "",
                    "smdp_address": "rsp.test.client.com",
                    "matching_id": "2546:65875:0201:3265",
                    "create_datetime": datetime.utcnow(),
                    "status": True,
                    "availability": "Free",
                    "plan_uid": "QWE:0123:UID",
                    "bundle_code": self.esimgo_reserved_bundle.bundle_code,
                    "expiry_date": datetime.utcnow() + timedelta(90),
                }
            ).save()
        )

    def tearDown(self):
        RunnableScripts.objects().delete()
        self.email_setting.delete()
        self.settings.delete()
        self.vendor.delete()
        self.bundle.delete()
        self.profile.delete()
        Bundles.objects().delete()

        # role = self.db.get_collection("roles")
        # role.delete_many({"name": self.role_model.name})

        reseller = self.db.get_collection("reseller")
        reseller.delete_many({"reseller_name": self.reseller_model.reseller_name})

        branch = self.db.get_collection("branch")
        branch.delete_many({"branch_name": self.branch_model.branch_name})

        order = self.db.get_collection("order_history")

        order.delete_many({})

        transaction_history = self.db.get_collection("transaction_history")
        transaction_history.delete_many({"reseller_id": self.inserted_reseller_id})

        self.esim_go_vendor.delete()
        self.esim_go_bundle.delete()
        self.esimgo_reserved_profile.delete()

        Profiles.objects().delete()

    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    @unittest.skip("Skip this unittest")
    def test_reserve_esimgo_prime_bundle_success_allocation_from_inventory(
        self, mock_authorization, *args
    ):
        mock_authorization.return_value = {
            "exp": 1682638756,
            "iat": 1682602756,
            "jti": "105ee738-993d-447e-8507-ee0cd4aa36c3",
            "iss": "http://localhost:8080/realms/MontyMobile",
            "sub": "1087f5f9-48ed-4509-8a89-9a077a3d7211",
            "typ": "Bearer",
            "azp": "admin-cli",
            "session_state": "0efd540d-bf9d-452f-b647-24245319ce2c",
            "name": "zoro",
            "given_name": "Satoru",
            "family_name": "Gojo",
            "sid": "0efd540d-bf9d-452f-b647-24245319ce2c",
            "preferred_username": "Satoru",
            "email": "<EMAIL>",
            "email_verified": False,
            "acr": "1",
            "scope": "email profile",
            "active": True,
            "role_name": "normal_user",
            "username": "Satoru",
            "permission_level": "2",
            "client_id": "admin-cli",
            "role_id": str(self.role.inserted_id),
            "reseller_id": str(self.inserted_reseller_id),
            "branch_id": str(self.branch.inserted_id),
            "user_id": str(self.inserted_reseller_id),
        }
        body = ReserveBundleRequest(
            bundle_code=self.esimgo_reserved_bundle.bundle_code,
            order_reference="12345567",
        )
        query_string = [
            ("reseller_id", self.inserted_reseller_id),
            ("branch_id", self.branch.inserted_id),
        ]

        reserve_process_response = self.client.open(
            "/api/v0/Bundles/Reserve",
            method="POST",
            data=json.dumps(body),
            content_type="application/json",
            headers={"Content-Type": "application/json", "Access-Token": ""},
            query_string=query_string,
        )

        order = self.db.get_collection("order_history")
        order_object = order.find_one(
            {
                "bundle_code": self.esimgo_reserved_bundle.bundle_code,
                "iccid": self.esimgo_reserved_profile.iccid,
                "order_status": "Pending",
                "bundle_marketing_name": self.esimgo_reserved_bundle.bundle_marketing_name,
                "bundle_name": self.esimgo_reserved_bundle.bundle_name,
                "matching_id": self.esimgo_reserved_profile.matching_id,
                "smdp_address": self.esimgo_reserved_profile.smdp_address,
                "activation_code": self.esimgo_reserved_profile.qr_code_value,
                "plan_uid": self.esimgo_reserved_profile.plan_uid,
                "bundle_category": self.esimgo_reserved_bundle.bundle_category,
            }
        )
        self.assertIsNotNone(order_object)
        self.assertDictEqual(
            reserve_process_response.json,
            {
                "iccid": self.esimgo_reserved_profile.iccid,
                "message": "Bundle Reserved Successfully",
                "order_id": str(order_object["_id"]),
                "remaining_wallet_balance": (
                    self.reseller_model.balance
                    - self.esimgo_reserved_bundle.unit_price
                    * (100 + self.reseller_model.corp_rate_revenue)
                    / 100
                ),
                "reseller_id": str(self.inserted_reseller_id),
            },
        )

        transaction_history = self.db.get_collection("transaction_history")
        transaction_history_object = transaction_history.find_one(
            {
                "reseller_id": self.inserted_reseller_id,
                "order_id": order_object["_id"],
                "type": "Order",
                "amount": (
                    -self.esimgo_reserved_bundle.unit_price
                    * (100 + self.reseller_model.corp_rate_revenue)
                    / 100
                ),
            }
        )
        self.assertIsNotNone(transaction_history_object)

        self.esim_go_vendor.reload()
        self.assertTrue(self.esim_go_vendor.is_active)
        self.assertIsNotNone(
            RunnableScripts.objects(
                bundle_code=self.esimgo_reserved_bundle.bundle_code,
                state="Accepted",
                informer="From API",
                script="AllocateProfiles",
                daily_used=self.settings.limit_bundle,
            ).first()
        )

        self.esimgo_reserved_profile.reload()
        self.assertEqual(
            self.esimgo_reserved_profile.expiry_date, self.esim_go_vendor.expiry_date
        )


if __name__ == "__main__":
    import unittest

    unittest.main()

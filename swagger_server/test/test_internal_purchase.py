from swagger_server.test.base_test_setup import BaseTestSetup
from swagger_server.test.utils.object_factories.object_factory import KeycloakAPIAuthResponseFactory
from app_models.reseller_models import Order_history
from instance import consumer_config
from unittest import mock
from helpers.mocked_requests import MockedRequest
import json
from app_models.consumer_models import (Bundles, Profiles)
import datetime


class TestReserveBundle(BaseTestSetup):
    URL = "/api/v0/Bundles/Reserve"
    HEADERS = {"Content-Type": "application/json", "Access-Token": ""}
    CONTENT_TYPE = "application/json"

    @mock.patch(
        "requests.post",
        side_effect=[
            MockedRequest(method="POST", headers={}, status_code=200, data={},
                          url=f"{consumer_config.esimgo_url}/v2.3/orders ",
                          response={"orderReference": "123e4567-e89b-12d3-a456-426614174000", "order": [{"esims": [{
                              "matchingId": "some-matching-id", "smdpAddress": "rsp.nottherealtruphone.com",
                              "iccid": "1234567890123456"}],
                              "type": "bundle", "item": "esim_UL_3D_RME_V2", "iccids": [], "quantity": 1}], })
        ]
    )
    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_reserve_profile_success_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "ReserveProfileFromInventory::SuccessCase",
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.flexi_bundle.bundle_code},
                "response": {"message": "Bundle Reserved Successfully", "balance": 489.0,
                             "iccid": "12345678912345678914"}
            },
            {
                "name": "ReserveProfileFromVendor::SuccessCase",
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"message": "Bundle Reserved Successfully", "balance": 467.0, "iccid": "1234567890123456"}
            }
        ]

        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()
        for index, tc in enumerate(test_cases):
            with self.subTest(tc["name"]):
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                bundle = Bundles.objects(bundle_code=tc["request_body"]["bundle_code"]).first()
                profile = Profiles.objects(iccid=tc["response"]["iccid"]).first()
                self.assertEqual(response_json["message"], tc["response"]["message"])
                self.assertEqual(response_json["remaining_wallet_balance"], tc["response"]["balance"])

                # Make sure that profile has been updated successfully
                self.assertEqual("Assigned", profile.availability)

                # Check whether the order has been created successfully
                order: Order_history = Order_history.objects(iccid=profile.iccid,
                                                             bundle_code=bundle.bundle_code).first()
                self.assertIsNotNone(order)
                self.assertEqual("Pending", order.order_status)

                # Check reseller balance has deducted successfully
                expected_balance = round(
                    self.reseller.balance - (
                            bundle.unit_price * (100 + self.reseller.corp_rate_revenue)
                    ) / 100, 1)
                self.reseller.reload()
                self.assertEqual(self.reseller.balance, expected_balance)

                # Make sure that transaction history has been created
                transaction_history_object = self.db.get_collection("transaction_history").find_one({
                    "reseller_id": self.reseller.id, "order_id": order.id, "type": "Order",
                    "amount": -bundle.unit_price * (100 + self.reseller.corp_rate_revenue) / 100
                })
                self.assertIsNotNone(transaction_history_object)

    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_reserve_profile_failure_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "ReserveProfile::FailureCase::UnknownReseller",
                "query_string": [("reseller_id", "123456789012345678901234"),
                                 ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Reseller Not Found", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::UnknownBranch",
                "query_string": [("reseller_id", str(self.reseller.id)),
                                 ("branch_id", "123456789012345678901234")],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Branch Not Found", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::InactiveReseller",
                "reseller_update": {"is_active": False},
                "query_string": [("reseller_id", str(self.reseller.id)),
                                 ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Reseller is disabled, Contact administration!", "status": 401}
            },
            {
                "name": "ReserveProfile::FailureCase::InactiveBranch",
                "reseller_update": {"is_active": True},
                "branch_update": {"is_active": False},
                "query_string": [("reseller_id", str(self.reseller.id)),
                                 ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Branch is disabled, Contact administration!", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::DuplicateOrderReference",
                "reseller_update": {"is_active": True},
                "branch_update": {"is_active": True},
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code,
                                 "order_reference": "ref-1234567890"},
                "response": {"detail": "Please choose a unique order reference", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::UnknownBundle",
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": "unknown"},
                "response": {"detail": "Bundle Code does not exist!", "status": 404}
            },
            {
                "name": "ReserveProfile::FailureCase::InsufficientBalance",
                "reseller_update": {"balance": 5},
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Insufficient Balance", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::BranchLimitReached",
                "reseller_update": {"balance": 500},
                "branch_update": {"limit": 100, "limit_consumption": 95},
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Branch Limit Reached", "status": 400}
            },
            {
                "name": "ReserveProfile::FailureCase::NoResponseFromVendor",
                "reseller_update": {"balance": 500},
                "branch_update": {"limit": 100, "limit_consumption": 10},
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Purchase Unsuccessful, contact Administration, failure reason: ICCID has not "
                                       "been assigned - Allocation Failed", "status": 500}
            },
            {
                "name": "ReserveProfile::FailureCase::ConsumedUnit>=AllocateUnit",
                "reseller_update": {"balance": 500},
                "branch_update": {"limit": 100, "limit_consumption": 10},
                "bundle_update": {"allocated_unit": 10, "consumed_unit": 10, "is_active": True},
                "query_string": [("reseller_id", str(self.reseller.id)), ("branch_id", str(self.branch.inserted_id))],
                "request_body": {"bundle_code": self.esim_go_bundle.bundle_code},
                "response": {"detail": "Purchase Unsuccessful, contact Administration, failure reason: Bundle not "
                                       "found - Get bundle failed", "status": 500}
            }
        ]

        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()
        for index, tc in enumerate(test_cases):
            if tc.get("bundle_update", None):
                self.esim_go_bundle.update(**tc.get("bundle_update"))
            if tc.get("reseller_update", None):
                self.reseller.update(**tc.get("reseller_update"))
            if tc.get("branch_update", None):
                self.db.branch.update_one({"_id": self.branch.inserted_id}, {"$set": {**tc.get("branch_update"),
                                                                                      "reseller_id": self.reseller.id,
                                                                                      "apply_Tenancy": 0}})
            with self.subTest(tc["name"]):
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                self.assertEqual(tc["response"]["detail"], response_json["detail"])
                self.assertEqual(tc["response"]["status"], response_json["status"])


class TestCompleteTransaction(BaseTestSetup):
    URL = "/api/v0/Bundles/Complete"
    HEADERS = {"Content-Type": "application/json", "Access-Token": ""}
    CONTENT_TYPE = "application/json"

    @mock.patch(
        "Services.bundles_services.complete_transaction_service.threading",
        return_value=(True, ""),
    )
    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_complete_transaction_success_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "CompleteTransaction::SuccessCase",
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"developer_message": "Success", }
            }
        ]

        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        for index, tc in enumerate(test_cases):
            with self.subTest(tc["name"]):
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                self.assertEqual(tc["response"]["developer_message"], response_json["developer_message"])
                self.assertIsNotNone(response_json["orders"][0]["activation_code"], "activation code should not be "
                                                                                    "blank")
                # Make sure that order_status and payment date has been updated in the order history
                self.flexi_profile_ordered.reload()
                self.assertEqual(self.flexi_profile_ordered.order_status, "Successful")
                self.assertEqual(self.flexi_profile_ordered.payment_date.date(), datetime.datetime.utcnow().date())

                # Make sure that payment date has been updated in the profile
                self.ordered_profile.reload()
                self.assertEqual(self.ordered_profile.payment_date.date(), datetime.datetime.utcnow().date())

                # Make sure that daily used count is increased by 1
                daily_used = self.flexi_bundle.daily_used
                self.flexi_bundle.reload()
                self.assertEqual(daily_used + 1, self.flexi_bundle.daily_used)

    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_complete_transaction_failure_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "CompleteTransaction::FailedCase::UnknownResellerID",
                "query_string": [("reseller_id", "507f191e810c19729de860ea")],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Reseller Not Found", "status": 400}
            },
            {
                "name": "CompleteTransaction::FailedCase::UnknownOrderReference",
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": "507f191e810c19729de860ea"},
                "response": {"developer_message": "No Orders Found", "response_code": '404'}
            },
            {
                "name": "CompleteTransaction::FailedCase::AlreadyCompletedOrder",
                "order_update": {"order_status": "Successful"},
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"developer_message": "Order Provided Is Not Suitable", "response_code": '404'}
            }
        ]

        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        for index, tc in enumerate(test_cases):
            with self.subTest(tc["name"]):
                if tc.get("order_update"):
                    self.flexi_profile_ordered.update(**tc.get("order_update"))
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                self.assertEqual(tc["response"].get("detail"), response_json.get("detail"))
                self.assertEqual(tc["response"].get("status"), response_json.get("status"))
                self.assertEqual(tc["response"].get("developer_message"), response_json.get("developer_message"))
                self.assertEqual(tc["response"].get("response_code"), response_json.get("response_code"))


class TestCancelBundle(BaseTestSetup):
    URL = "/api/v0/Bundles/Cancel"
    HEADERS = {"Content-Type": "application/json", "Access-Token": ""}
    CONTENT_TYPE = "application/json"

    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_cancel_bundle_success_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "CancelBundle::SuccessCase",
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"message": "Order Canceled Successfully.", }
            }
        ]
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        for index, tc in enumerate(test_cases):
            with self.subTest(tc["name"]):
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                self.assertEqual(tc["response"].get("message"), response_json.get("message"))

                # Make sure that order status has been updated successfully
                self.flexi_profile_ordered.reload()
                self.assertEqual(self.flexi_profile_ordered.order_status, "Canceled")

                # Make sure reseller balance has been updated successfully.
                balance = self.reseller.balance
                self.reseller.reload()
                self.assertEqual(self.reseller.balance, balance + self.flexi_profile_ordered.bundle_price)

                # Make sure that transaction history has been created
                transaction_history_object = self.db.get_collection("transaction_history").find_one({
                    "reseller_id": self.reseller.id, "order_id": self.flexi_profile_ordered.id, "type": "Refund",
                    "amount": self.flexi_profile_ordered.bundle_price
                })
                self.assertIsNotNone(transaction_history_object)

                # Make sure that profile has been updated successfully.
                self.ordered_profile.reload()
                self.assertEqual(self.ordered_profile.availability, "Free")
                self.assertIsNone(self.ordered_profile.payment_date)
                self.assertIsNone(self.ordered_profile.reseller_id)
                self.assertFalse(self.ordered_profile.reserved)
                self.assertIsNone(self.ordered_profile.date_of_reservation)
                self.assertEqual(self.ordered_profile.update_availability_date.date(),
                                 datetime.datetime.utcnow().date())

    @mock.patch(
        "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
    )
    def test_cancel_bundle_failure_path(self, mock_validate_token, *args):
        test_cases = [
            {
                "name": "CancelBundle::FailedCase::UnknownResellerID",
                "query_string": [("reseller_id", "507f191e810c19729de860ea")],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Reseller Not Found", "status": 400}
            },
            {
                "name": "CancelBundle::FailedCase::UnknownOrderReference",
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": "507f191e810c19729de860ea"},
                "response": {"detail": "Order Not Found!", "status": 404}
            },
            {
                "name": "CancelBundle::FailedCase::AlreadyCompletedOrder",
                "order_update": {"order_status": "Successful"},
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Cannot Cancel a successful Order!", "status": 400}
            },
            {
                "name": "CancelBundle::FailedCase::AlreadyCancelledOrder",
                "order_update": {"order_status": "Canceled"},
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Order Already canceled!", "status": 400}
            },
            {
                "name": "CancelBundle::FailedCase::FailedOrder",
                "order_update": {"order_status": "Failed"},
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Cannot Cancel a Failed Order!", "status": 400}
            },
            {
                "name": "CancelBundle::FailedCase::ThresholdTimeReached",
                "order_update": {"order_status": "Pending", "date_created": (datetime.datetime.utcnow() -
                                                                             datetime.timedelta(hours=6))},
                "query_string": [("reseller_id", str(self.reseller.id))],
                "request_body": {"order_reference": self.flexi_profile_ordered.order_reference},
                "response": {"detail": "Cannot Refund an order older than 5 hours!", "status": 400}
            },
        ]

        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        for index, tc in enumerate(test_cases):
            with self.subTest(tc["name"]):
                if tc.get("order_update"):
                    self.flexi_profile_ordered.update(**tc.get("order_update"))
                response = self.client.post(self.URL, data=json.dumps(tc["request_body"]),
                                            content_type=self.CONTENT_TYPE,
                                            headers=self.HEADERS, query_string=tc["query_string"])
                response_json = response.json
                self.assertEqual(tc["response"].get("detail"), response_json.get("detail"))
                self.assertEqual(tc["response"].get("status"), response_json.get("status"))

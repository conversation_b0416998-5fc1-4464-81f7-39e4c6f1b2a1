# coding: utf-8

from __future__ import absolute_import

from flask import json
from six import BytesIO
import unittest
from swagger_server.models.add_reseller_request import AddResellerRequest  # noqa: E501
from swagger_server.models.add_reseller_response import AddResellerResponse  # noqa: E501
from swagger_server.models.customize_corp_price_request import CustomizeCorpPriceRequest  # noqa: E501
from swagger_server.models.customize_price_request import CustomizePriceRequest  # noqa: E501
from swagger_server.models.customize_price_response import CustomizePriceResponse  # noqa: E501
from swagger_server.models.delete_reseller_response import DeleteResellerResponse  # noqa: E501
from swagger_server.models.edit_reseller_request import EditResellerRequest  # noqa: E501
from swagger_server.models.edit_reseller_response import EditResellerResponse  # noqa: E501
from swagger_server.models.get_available_reseller_properties_response import GetAvailableResellerPropertiesResponse  # noqa: E501
from swagger_server.models.get_reseller_by_email_request import GetResellerByEmailRequest  # noqa: E501
from swagger_server.models.get_reseller_response import GetResellerResponse  # noqa: E501
from swagger_server.models.get_resellers_response import GetResellersResponse  # noqa: E501
from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.topup_balance_request import TopupBalanceRequest  # noqa: E501
from swagger_server.models.topup_balance_response import TopupBalanceResponse  # noqa: E501
from swagger_server.test import BaseTestCase

@unittest.skip("Skip this unittest")
class TestResellerController(BaseTestCase):
    """ResellerController integration test stubs"""

    def test_add_reseller(self):
        """Test case for add_reseller

        Adds a Reseller to platform.
        """
        body = AddResellerRequest()
        query_string = [('currency_code', 'currency_code_example')]
        response = self.client.open(
            '/api/v0/Reseller',
            method='POST',
            data=json.dumps(body),
            content_type='application/json',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_available_reseller_properties(self):
        """Test case for available_reseller_properties

        Get available properties of resellers
        """
        query_string = [('category_names_list', 'category_names_list_example')]
        response = self.client.open(
            '/api/v0/Reseller/AvailableResellerProperties',
            method='GET',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_customize_corporate_price(self):
        """Test case for customize_corporate_price

        Customize Bundle Unit Price.
        """
        body = CustomizeCorpPriceRequest()
        query_string = [('reseller_id', 'reseller_id_example'),
                        ('currency_code', 'currency_code_example')]
        response = self.client.open(
            '/api/v0/Reseller/Admin/custom_corporate_price',
            method='POST',
            data=json.dumps(body),
            content_type='application/json',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_customize_corporate_price_csv(self):
        """Test case for customize_corporate_price_csv

        Customize Bundle Unit Price CSV file.
        """
        query_string = [('reseller_id', 'reseller_id_example')]
        data = dict(file='file_example')
        response = self.client.open(
            '/api/v0/Reseller/Admin/custom_corporate_price_csv',
            method='POST',
            data=data,
            content_type='multipart/form-data',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_customize_price(self):
        """Test case for customize_price

        Customize Bundle Price.
        """
        body = CustomizePriceRequest()
        query_string = [('reseller_id', 'reseller_id_example'),
                        ('currency_code', 'currency_code_example')]
        response = self.client.open(
            '/api/v0/Reseller/custom_price',
            method='POST',
            data=json.dumps(body),
            content_type='application/json',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_customize_price_csv(self):
        """Test case for customize_price_csv

        Customize Bundle Price.
        """
        query_string = [('reseller_id', 'reseller_id_example')]
        data = dict(file='file_example')
        response = self.client.open(
            '/api/v0/Reseller/custom_price_csv',
            method='POST',
            data=data,
            content_type='multipart/form-data',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_delete_reseller(self):
        """Test case for delete_reseller

        Delete Reseller
        """
        response = self.client.open(
            '/api/v0/Reseller/{ResellerID}'.format(reseller_id='reseller_id_example'),
            method='DELETE')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_edit_reseller(self):
        """Test case for edit_reseller

        Edit Reseller
        """
        body = EditResellerRequest()
        response = self.client.open(
            '/api/v0/Reseller/{ResellerID}'.format(reseller_id='reseller_id_example'),
            method='PUT',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_get_reseller(self):
        """Test case for get_reseller

        Gets all  Resellers
        """
        query_string = [('page_size', 56),
                        ('page_number', 2),
                        ('dropdown', true),
                        ('reseller_name', 'reseller_name_example'),
                        ('supports_promo', true),
                        ('supports_voucher', true),
                        ('currency_code', 'currency_code_example'),
                        ('is_whitelabel', true),
                        ('reseller_category', 'reseller_category_example')]
        response = self.client.open(
            '/api/v0/Reseller',
            method='GET',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_get_reseller_by_email(self):
        """Test case for get_reseller_by_email

        Gets Reseller by Email
        """
        body = GetResellerByEmailRequest()
        response = self.client.open(
            '/api/v0/Reseller/GetResellerByEmail',
            method='POST',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_get_reseller_by_id(self):
        """Test case for get_reseller_by_id

        Gets Reseller by ID
        """
        query_string = [('currency_code', 'currency_code_example')]
        response = self.client.open(
            '/api/v0/Reseller/{ResellerID}'.format(reseller_id='reseller_id_example'),
            method='GET',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_topup_reseller_balance(self):
        """Test case for topup_reseller_balance

        Topup Reseller Balance.
        """
        body = TopupBalanceRequest()
        query_string = [('currency_code', 'currency_code_example')]
        response = self.client.open(
            '/api/v0/Reseller/Topup/{ResellerID}'.format(reseller_id='reseller_id_example'),
            method='POST',
            data=json.dumps(body),
            content_type='application/json',
            query_string=query_string)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))


if __name__ == '__main__':
    import unittest
    unittest.main()

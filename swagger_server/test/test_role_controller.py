# coding: utf-8

from __future__ import absolute_import

import unittest

from flask import json
from six import BytesIO

from swagger_server.models.create_role_request import CreateRoleRequest  # noqa: E501
from swagger_server.models.create_role_response import CreateRoleResponse  # noqa: E501
from swagger_server.models.delete_role_response import DeleteRoleResponse  # noqa: E501
from swagger_server.models.edit_role_request import EditRoleRequest  # noqa: E501
from swagger_server.test.utils.object_factories.object_factory import KeycloakAPIAuthResponseFactory
from swagger_server.test.base_test_setup import BaseTestSetup
from unittest import mock


@mock.patch(
    "Services.authorization_services.check_ApiKeyAuth_service.KeycloakHelper.validate_token",
)
class TestRoleController(BaseTestSetup):
    HEADERS = {"Content-Type": "application/json", "Access-Token": ""}
    """RoleController integration test stubs"""

    def setUp(self):
        super().setUp()
        self.test_role = self.create_role(name="TestRole", description="test role", permission_level=1,
                                          reseller_id=self.reseller.id)

    def test_create_role(self, mock_validate_token):
        """Test case for create_role

        Creates a new role.
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        body = CreateRoleRequest(name="testRole", access_level="basic", permission_level=2,
                                 description="this is a test role", permissions=[self.create_permission("createRole")])
        response = self.client.open(
            '/api/v0/Role',
            method='POST',
            data=json.dumps(body),
            content_type='application/json',
            headers=self.HEADERS)
        self.assertEqual(201, response.status_code, 'Response body is : ' + response.data.decode('utf-8'))

    def test_delete_role(self, mock_validate_token):
        """Test case for delete_role

        Deletes specific role by ID
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        response = self.client.open(
            '/api/v0/Role/{role_id}'.format(role_id=self.test_role.inserted_id),
            method='DELETE',
            headers=self.HEADERS
        )
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_edit_role(self, mock_validate_token):
        """Test case for edit_role

        Edit Role
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        body = EditRoleRequest(name="test role", access_level="basic", permission_level=2,
                               description="this is a test role", permissions=[self.create_permission("createRole")])
        response = self.client.open(
            '/api/v0/Role/{role_id}'.format(role_id=self.test_role.inserted_id),
            method='PUT',
            data=json.dumps(body),
            content_type='application/json',
            headers=self.HEADERS
        )
        self.assertEqual(201, response.status_code,
                         'Response body is : ' + response.data.decode('utf-8'))

    def test_get_all_roles(self, mock_validate_token):
        """Test case for get_all_roles

        Returns all roles in platform
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        response = self.client.open(
            '/api/v0/Role/All',
            method='GET',
            headers=self.HEADERS)
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_get_role_by_id(self, mock_validate_token):
        """Test case for get_role_by_id

        Returns specific role by ID
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        response = self.client.open(
            '/api/v0/Role/{role_id}'.format(role_id=self.test_role.inserted_id),
            method='GET',
            headers=self.HEADERS
        )
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    @unittest.skip
    def test_update_monty_admin_permissions(self, mock_validate_token):
        """Test case for update_monty_admin_permissions

        Updates Monty Admin Permissions.
        """
        mock_validate_token.return_value = KeycloakAPIAuthResponseFactory(
            role_id=str(self.role.inserted_id),
            reseller_id=str(self.reseller.id),
            branch_id=str(self.branch.inserted_id)
        ).build()

        response = self.client.open(
            '/api/v0/Roles/UMAP',
            method='PATCH',
            headers=self.HEADERS
        )
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

# coding: utf-8

from __future__ import absolute_import
import unittest
from flask import json
from six import BytesIO

from swagger_server.models.add_default_owner_missing_role_permissions_request import AddDefaultOwnerMissingRolePermissionsRequest  # noqa: E501
from swagger_server.models.add_default_owner_missing_role_permissions_response import AddDefaultOwnerMissingRolePermissionsResponse  # noqa: E501
from swagger_server.models.add_missing_role_permissions_request import AddMissingRolePermissionsRequest  # noqa: E501
from swagger_server.models.add_missing_role_permissions_response import AddMissingRolePermissionsResponse  # noqa: E501
from swagger_server.models.create_role_permission_request import CreateRolePermissionRequest  # noqa: E501
from swagger_server.models.create_role_permission_response import CreateRolePermissionResponse  # noqa: E501
from swagger_server.test import BaseTestCase

@unittest.skip("Skip this unittest")
class TestRolePermissionsController(BaseTestCase):
    """RolePermissionsController integration test stubs"""

    def test_add_missing_default_owner_role_permissions(self):
        """Test case for add_missing_default_owner_role_permissions

        Adds the missing APIs to role permissions of the default Owner role.
        """
        body = AddDefaultOwnerMissingRolePermissionsRequest()
        response = self.client.open(
            '/api/v0/Permission/Default',
            method='PATCH',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_add_missing_role_permissions(self):
        """Test case for add_missing_role_permissions

        Adds the missing APIs to role permissions of a specific role.
        """
        body = AddMissingRolePermissionsRequest()
        response = self.client.open(
            '/api/v0/Permission',
            method='PATCH',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))

    def test_create_role_permission(self):
        """Test case for create_role_permission

        Creates a new role permission.
        """
        body = CreateRolePermissionRequest()
        response = self.client.open(
            '/api/v0/Permission',
            method='POST',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))


if __name__ == '__main__':
    import unittest
    unittest.main()

# coding: utf-8

from __future__ import absolute_import
import unittest
from flask import json
from six import BytesIO

from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.issue_report_request import IssueReportRequest  # noqa: E501
from swagger_server.models.issue_report_response import IssueReportResponse  # noqa: E501
from swagger_server.test import BaseTestCase

@unittest.skip("Skip this unittest")
class TestSupportController(BaseTestCase):
    """SupportController integration test stubs"""

    def test_issue_report(self):
        """Test case for issue_report

        Submit an Issue Report.
        """
        body = IssueReportRequest()
        response = self.client.open(
            '/api/v0/Support',
            method='POST',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))


if __name__ == '__main__':
    import unittest
    unittest.main()

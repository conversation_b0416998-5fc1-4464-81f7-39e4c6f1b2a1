if [ -z "$GIT_HELPERS_VERSION_TAG" ]; then export GIT_HELPERS_VERSION_TAG=master ; fi

pip install --upgrade --force-reinstall git+https://$GIT_HELPERS_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim_b2c_helpers.git@$GIT_HELPERS_VERSION_TAG
pip install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim-b2c-models-py.git
pip install -r requirements.txt

pip install -r Testing/test-requirements.txt

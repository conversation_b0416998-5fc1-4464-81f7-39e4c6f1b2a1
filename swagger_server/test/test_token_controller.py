# coding: utf-8

from __future__ import absolute_import

from flask import json
from six import BytesIO

from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.refresh_token_request import RefreshTokenRequest  # noqa: E501
from swagger_server.models.refresh_token_response import RefreshTokenResponse  # noqa: E501
from swagger_server.test import BaseTestCase

import unittest
@unittest.skip("Skip this unittest")
class TestTokenController(BaseTestCase):
    """TokenController integration test stubs"""

    def test_refresh_token(self):
        """Test case for refresh_token

        Token Refresher
        """
        body = RefreshTokenRequest()
        response = self.client.open(
            '/api/v0/Token/Refresh',
            method='POST',
            data=json.dumps(body),
            content_type='application/json')
        self.assert200(response,
                       'Response body is : ' + response.data.decode('utf-8'))


if __name__ == '__main__':
    import unittest
    unittest.main()

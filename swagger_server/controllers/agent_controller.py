from Services.agent_services import add_agent_service,delete_agent_service,edit_agent_service,forgot_password_service,get_agent_service,get_agent_by_email_service,get_agent_by_id_service,login_service,logout_service,reset_password_service
import connexion
import six

from swagger_server.models.add_agent_request import AddAgentRequest  # noqa: E501
from swagger_server.models.add_agent_response import AddAgentResponse  # noqa: E501
from swagger_server.models.agent_profile import AgentProfile  # noqa: E501
from swagger_server.models.delete_agent_response import DeleteAgentResponse  # noqa: E501
from swagger_server.models.edit_agent_request import EditAgentRequest  # noqa: E501
from swagger_server.models.edit_agent_response import EditAgentResponse  # noqa: E501
from swagger_server.models.forgot_password_request import ForgotPasswordRequest  # noqa: E501
from swagger_server.models.forgot_password_response import ForgotPasswordResponse  # noqa: E501
from swagger_server.models.get_agent_by_email_request import GetAgentByEmailRequest  # noqa: E501
from swagger_server.models.get_agent_response import GetAgentResponse  # noqa: E501
from swagger_server.models.get_agents_response import GetAgentsResponse  # noqa: E501
from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.login_request import LoginRequest  # noqa: E501
from swagger_server.models.login_response import LoginResponse  # noqa: E501
from swagger_server.models.logout_response import LogoutResponse  # noqa: E501
from swagger_server.models.password_reset_request import PasswordResetRequest  # noqa: E501
from swagger_server.models.password_reset_response import PasswordResetResponse  # noqa: E501
from swagger_server.models.refresh_token_request import RefreshTokenRequest  # noqa: E501
from swagger_server import util


def add_agent(body=None, reseller_id=None, branch_id=None):  # noqa: E501
    """Add Agent to Branch.

    Adds a Agent to branch, can be called by PERSONNEL. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: branch id for super admin and reseller admin
    :type branch_id: str

    :rtype: AddAgentResponse
    """
    if connexion.request.is_json:
        body = AddAgentRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return add_agent_service.add_agent(body, reseller_id, branch_id)
# return 'do some magic!'


def delete_agent(agent_id, reseller_id=None, branch_id=None):  # noqa: E501
    """Delete Agent

    Allows PERSONNEL to delete Agent. # noqa: E501

    :param agent_id: id value that needs to be considered for filter
    :type agent_id: str
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: branch id for super admin, reseller admin
    :type branch_id: str

    :rtype: DeleteAgentResponse
    """
    return delete_agent_service.delete_agent(agent_id, reseller_id, branch_id)
# return 'do some magic!'


def edit_agent(agent_id, body=None, reseller_id=None, branch_id=None):  # noqa: E501
    """Edit Agent

    Allows admins to edit an Agent # noqa: E501

    :param agent_id: id value that needs to be considered for filter
    :type agent_id: str
    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: branch id for super admin, reseller admin
    :type branch_id: str

    :rtype: EditAgentResponse
    """
    if connexion.request.is_json:
        body = EditAgentRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return edit_agent_service.edit_agent(agent_id, body, reseller_id, branch_id)
# return 'do some magic!'


def forgot_password(body=None):  # noqa: E501
    """Submit a forgot Password request.

    Allows the user to reset password via email. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: ForgotPasswordResponse
    """
    if connexion.request.is_json:
        body = ForgotPasswordRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return forgot_password_service.forgot_password(body)
# return 'do some magic!'


def get_agent(reseller_id=None, page_size=None, page_number=None, branch_id=None):  # noqa: E501
    """Gets all  Agents

    Gets all Agents , can be called by PERSONNEL. # noqa: E501

    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param branch_id: branch id for super admin and reseller admin
    :type branch_id: str

    :rtype: GetAgentsResponse
    """
    return get_agent_service.get_agent(reseller_id, page_size, page_number, branch_id)
# return 'do some magic!'


def get_agent_by_email(body=None):  # noqa: E501
    """Gets Agent by Email

    Gets a Agent by Email, can be called by PERSONNEL. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: GetAgentResponse
    """
    if connexion.request.is_json:
        body = GetAgentByEmailRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return get_agent_by_email_service.get_agent_by_email(body)
# return 'do some magic!'


def get_agent_by_id(agent_id, reseller_id=None, branch_id=None):  # noqa: E501
    """Gets Agent by ID

    Gets a Agent by ID , can be called by PERSONNEL. # noqa: E501

    :param agent_id: id value that needs to be considered for filter
    :type agent_id: str
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: branch id for super admin, reseller admin
    :type branch_id: str

    :rtype: AgentProfile
    """
    return get_agent_by_id_service.get_agent_by_id(agent_id, reseller_id, branch_id)
# return 'do some magic!'


def login(body=None):  # noqa: E501
    """Login Agent to Platform.

    Logs in User to Platform and returns access token. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: LoginResponse
    """
    if connexion.request.is_json:
        body = LoginRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return login_service.login(body)
# return 'do some magic!'


def logout(body=None):  # noqa: E501
    """Logout Agent from Platform

    Logs out Agent from Platform. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: LogoutResponse
    """
    if connexion.request.is_json:
        body = RefreshTokenRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return logout_service.logout(body)
# return 'do some magic!'


def reset_password(body=None):  # noqa: E501
    """Resets User Password .

    Allows the user to reset password. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: PasswordResetResponse
    """
    if connexion.request.is_json:
        body = PasswordResetRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return reset_password_service.reset_password(body)
# return 'do some magic!'

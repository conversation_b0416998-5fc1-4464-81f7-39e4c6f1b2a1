from Services.bundles_services import assign_bundle_service,cancel_bundle_service,complete_transaction_service,get_available_topup_bundles_service,get_bundle_network_list_service,get_bundles_of_country_service,get_bundles_of_country_csv_service,get_bundles_of_country_v2_service,get_compatible_topup_bundles_service,get_compatible_topup_bundles_v2_service,get_countries_service,get_currencies_service,get_regions_service,get_reseller_bundles_by_scope_service,manage_currencies_service,manage_currencies_csv_service,reserve_bundle_service,topup_bundle_service
import connexion
import six

from swagger_server.models.assign_bundle_request import AssignBundleRequest  # noqa: E501
from swagger_server.models.assign_bundle_response import AssignBundleResponse  # noqa: E501
from swagger_server.models.bundle_network_list import BundleNetworkList  # noqa: E501
from swagger_server.models.cancel_bundle_request import CancelBundleRequest  # noqa: E501
from swagger_server.models.cancel_bundle_response import CancelBundleResponse  # noqa: E501
from swagger_server.models.complete_transaction_request import CompleteTransactionRequest  # noqa: E501
from swagger_server.models.complete_transaction_response import CompleteTransactionResponse  # noqa: E501
from swagger_server.models.customize_currencies_request import CustomizeCurrenciesRequest  # noqa: E501
from swagger_server.models.customize_currencies_response import CustomizeCurrenciesResponse  # noqa: E501
from swagger_server.models.get_bundles_response import GetBundlesResponse  # noqa: E501
from swagger_server.models.get_countries_response import GetCountriesResponse  # noqa: E501
from swagger_server.models.get_currencies_response import GetCurrenciesResponse  # noqa: E501
from swagger_server.models.get_regions_response import GetRegionsResponse  # noqa: E501
from swagger_server.models.inline_response200 import InlineResponse200  # noqa: E501
from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.reserve_bundle_request import ReserveBundleRequest  # noqa: E501
from swagger_server.models.reserve_bundle_response import ReserveBundleResponse  # noqa: E501
from swagger_server.models.topup_bundle_request import TopupBundleRequest  # noqa: E501
from swagger_server.models.topup_bundle_response import TopupBundleResponse  # noqa: E501
from swagger_server import util


def assign_bundle(body=None, reseller_id=None, branch_id=None, currency_code=None):  # noqa: E501
    """Assign Bundle

    Checks if the reseller has balance, and if the bundle is available, and assigns it to a user. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: reseller id for super admin
    :type branch_id: str
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: AssignBundleResponse
    """
    if connexion.request.is_json:
        body = AssignBundleRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return assign_bundle_service.assign_bundle(body, reseller_id, branch_id, currency_code)
# return 'do some magic!'


def cancel_bundle(body=None, reseller_id=None):  # noqa: E501
    """Cancel Order.

    Allow to cancel an order. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str

    :rtype: CancelBundleResponse
    """
    if connexion.request.is_json:
        body = CancelBundleRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return cancel_bundle_service.cancel_bundle(body, reseller_id)
# return 'do some magic!'


def complete_transaction(body=None, reseller_id=None):  # noqa: E501
    """Complete transaction

    Complete transaction and send profile information # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str

    :rtype: CompleteTransactionResponse
    """
    if connexion.request.is_json:
        body = CompleteTransactionRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return complete_transaction_service.complete_transaction(body, reseller_id)
# return 'do some magic!'


def get_available_topup_bundles(bundle_code, country_code=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, bundle_tag=None, region_code=None, currency_code=None):  # noqa: E501
    """Get available topup bundles

    Allows the user to view all bundles that are available with the Esim Profile Installed. # noqa: E501

    :param bundle_code: 
    :type bundle_code: str
    :param country_code: 
    :type country_code: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param bundle_tag: filter parameter
    :type bundle_tag: str
    :param region_code: filter parameter
    :type region_code: str
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: GetBundlesResponse
    """
    return get_available_topup_bundles_service.get_available_topup_bundles(bundle_code, country_code, page_size, page_number, reseller_id, bundle_name, sort_by, bundle_tag, region_code, currency_code)
# return 'do some magic!'


def get_bundle_network_list(bundle_code, country_code=None):  # noqa: E501
    """Gets the bundle network list

    Gets the bundle network list , can be called by users. # noqa: E501

    :param bundle_code: 
    :type bundle_code: str
    :param country_code: 
    :type country_code: str

    :rtype: BundleNetworkList
    """
    return get_bundle_network_list_service.get_bundle_network_list(bundle_code, country_code)
# return 'do some magic!'


def get_bundles_of_country(country_code=None, bundle_category=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, reseller_admin_view=None, bundle_tag=None, region_code=None, currency_code=None, bundle_code=None):  # noqa: E501
    """Get all country bundles Available

    Can be called by users to view all bundles available # noqa: E501

    :param country_code: 
    :type country_code: str
    :param bundle_category: 
    :type bundle_category: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param reseller_admin_view: 
    :type reseller_admin_view: bool
    :param bundle_tag: filter parameter
    :type bundle_tag: str
    :param region_code: filter parameter
    :type region_code: str
    :param currency_code: currency code to preview in
    :type currency_code: str
    :param bundle_code: filter parameter
    :type bundle_code: str

    :rtype: GetBundlesResponse
    """
    return get_bundles_of_country_service.get_bundles_of_country(country_code, bundle_category, page_size, page_number, reseller_id, bundle_name, sort_by, reseller_admin_view, bundle_tag, region_code, currency_code, bundle_code)
# return 'do some magic!'


def get_bundles_of_country_csv(country_code=None, bundle_category=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, reseller_admin_view=None, export=None, currency_code=None):  # noqa: E501
    """Get all country bundles Available CSV

    Can be called by users to view all bundles available # noqa: E501

    :param country_code: 
    :type country_code: str
    :param bundle_category: 
    :type bundle_category: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param reseller_admin_view: 
    :type reseller_admin_view: bool
    :param export: 
    :type export: bool
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: str
    """
    return get_bundles_of_country_csv_service.get_bundles_of_country_csv(country_code, bundle_category, page_size, page_number, reseller_id, bundle_name, sort_by, reseller_admin_view, export, currency_code)
# return 'do some magic!'


def get_bundles_of_country_v2(country_code=None, bundle_category=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, reseller_admin_view=None, bundle_tag=None, region_code=None, currency_code=None, bundle_code=None, country_code_array=None):  # noqa: E501
    """Get all country bundles Available

    Can be called by users to view all bundles available # noqa: E501

    :param country_code: 
    :type country_code: str
    :param bundle_category: 
    :type bundle_category: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param reseller_admin_view: 
    :type reseller_admin_view: bool
    :param bundle_tag: filter parameter
    :type bundle_tag: str
    :param region_code: filter parameter
    :type region_code: str
    :param currency_code: currency code to preview in
    :type currency_code: str
    :param bundle_code: filter parameter
    :type bundle_code: str
    :param country_code_array: filter parameter
    :type country_code_array: List[str]

    :rtype: GetBundlesResponse
    """
    return get_bundles_of_country_v2_service.get_bundles_of_country_v2(country_code, bundle_category, page_size, page_number, reseller_id, bundle_name, sort_by, reseller_admin_view, bundle_tag, region_code, currency_code, bundle_code, country_code_array)
# return 'do some magic!'


def get_compatible_topup_bundles(bundle_code, country_code=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, bundle_tag=None, currency_code=None):  # noqa: E501
    """Get compatible topup bundles

    Allows the user to view all bundles that are compatible with the Esim Profile Installed. # noqa: E501

    :param bundle_code: 
    :type bundle_code: str
    :param country_code: 
    :type country_code: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param bundle_tag: filter parameter
    :type bundle_tag: str
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: GetBundlesResponse
    """
    return get_compatible_topup_bundles_service.get_compatible_topup_bundles(bundle_code, country_code, page_size, page_number, reseller_id, bundle_name, sort_by, bundle_tag, currency_code)
# return 'do some magic!'


def get_compatible_topup_bundles_v2(bundle_code, country_code=None, page_size=None, page_number=None, reseller_id=None, bundle_name=None, sort_by=None, bundle_tag=None, order_id=None, previous_order_reference=None, region_code=None, currency_code=None):  # noqa: E501
    """Get compatible topup bundles

    Allows the user to view all bundles that are compatible with the Esim Profile Installed. # noqa: E501

    :param bundle_code: 
    :type bundle_code: str
    :param country_code: 
    :type country_code: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param bundle_name: filter parameter
    :type bundle_name: str
    :param sort_by: sorting parameter
    :type sort_by: str
    :param bundle_tag: filter parameter
    :type bundle_tag: str
    :param order_id: 
    :type order_id: str
    :param previous_order_reference: 
    :type previous_order_reference: str
    :param region_code: filter parameter
    :type region_code: str
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: GetBundlesResponse
    """
    return get_compatible_topup_bundles_v2_service.get_compatible_topup_bundles_v2(bundle_code, country_code, page_size, page_number, reseller_id, bundle_name, sort_by, bundle_tag, order_id, previous_order_reference, region_code, currency_code)
# return 'do some magic!'


def get_countries(reseller_id=None, reseller_admin_view=None):  # noqa: E501
    """Get all countries Available

    Can be called by users to view all counties available # noqa: E501

    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param reseller_admin_view: 
    :type reseller_admin_view: bool

    :rtype: GetCountriesResponse
    """
    return get_countries_service.get_countries(reseller_id, reseller_admin_view)
# return 'do some magic!'


def get_currencies(reseller_admin_view=None, page_size=None, page_number=None, export=None):  # noqa: E501
    """Get all currencies Available

    Can be called by users to view all currencies available # noqa: E501

    :param reseller_admin_view: 
    :type reseller_admin_view: bool
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param export: 
    :type export: bool

    :rtype: GetCurrenciesResponse
    """
    return get_currencies_service.get_currencies(reseller_admin_view, page_size, page_number, export)
# return 'do some magic!'


def get_regions(reseller_id=None, reseller_admin_view=None):  # noqa: E501
    """Get all regions Available

    Can be called by users to view all regions available # noqa: E501

    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param reseller_admin_view: 
    :type reseller_admin_view: bool

    :rtype: GetRegionsResponse
    """
    return get_regions_service.get_regions(reseller_id, reseller_admin_view)
# return 'do some magic!'


def get_reseller_bundles_by_scope(geoscope, page=None, limit=None, data_size_min=None, data_size_max=None, validity_days_min=None, validity_days_max=None, price_min=None, price_max=None):  # noqa: E501
    """Get reseller bundles grouped by scope (region, countries, or global)

    Returns bundles grouped by their availability scope: region, country, or global. **Filtering Rules**: - Use the &#x60;filter&#x60; query parameter to specify grouping scope: &#x60;region&#x60;, &#x60;countries&#x60;, or &#x60;global&#x60;. - Pagination is supported via &#x60;page&#x60; and &#x60;limit&#x60;. **Region Rules**: - Bundles are grouped under one of the following display regions: Africa, Asia, Europe, Middle East, North America, South America. - **Turkey** is grouped under Europe by default. But if a bundle contains **Turkey and a Middle Eastern country**, it is grouped under Middle East. - **Oceania** countries are grouped under **Asia**. - **Central America** and **Caribbean** countries are grouped under **South America**. - Bundles are **never duplicated across regions**—each belongs to one region only. **Global Rules**: - Bundles in the &#x60;global&#x60; scope must cover **more than 50 countries**. **Data size input rules**: - If the value is **less than 1**, it is multiplied by 1000 and treated as megabytes. - If the value is **1 or more**, it is treated as gigabytes. **Examples**: - To filter for **500 MB**, pass &#x60;0.5&#x60; - To filter for **500 GB**, pass &#x60;500&#x60;  # noqa: E501

    :param geoscope: Determines how bundles are grouped: - &#x60;region&#x60;: groups bundles under regions like Europe, Asia, etc. - &#x60;countries&#x60;: returns all bundles scoped to specific countries, regions, or global (flat list). - &#x60;global&#x60;: returns only global bundles covering over 50 countries. 
    :type geoscope: str
    :param page: Page number for pagination.
    :type page: int
    :param limit: Maximum number of bundles per page.
    :type limit: int
    :param data_size_min: Minimum data size (e.g., \&quot;0.5\&quot; for 500MB or \&quot;1\&quot; for 1GB).  If value &lt; 1, it is multiplied by 1000 and treated as megabytes.  If &gt;&#x3D; 1, it is treated as gigabytes. 
    :type data_size_min: str
    :param data_size_max: Maximum data size (e.g., \&quot;0.5\&quot; for 500MB or \&quot;1\&quot; for 1GB).  If value &lt; 1, it is multiplied by 1000 and treated as megabytes.  If &gt;&#x3D; 1, it is treated as gigabytes. 
    :type data_size_max: str
    :param validity_days_min: Minimum validity period in days.
    :type validity_days_min: int
    :param validity_days_max: Maximum validity period in days.
    :type validity_days_max: int
    :param price_min: Minimum bundle price.
    :type price_min: str
    :param price_max: Maximum bundle price.
    :type price_max: str

    :rtype: InlineResponse200
    """
    return get_reseller_bundles_by_scope_service.get_reseller_bundles_by_scope(geoscope, page, limit, data_size_min, data_size_max, validity_days_min, validity_days_max, price_min, price_max)
# return 'do some magic!'


def manage_currencies(body=None):  # noqa: E501
    """Manage Currencies.

    Allows a Super admin to manage Currencies. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: CustomizeCurrenciesResponse
    """
    if connexion.request.is_json:
        body = CustomizeCurrenciesRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return manage_currencies_service.manage_currencies(body)
# return 'do some magic!'


def manage_currencies_csv(file=None):  # noqa: E501
    """Manage Currencies.

    Allows a Super admin to manage Currencies. # noqa: E501

    :param file: 
    :type file: strstr

    :rtype: CustomizeCurrenciesResponse
    """
    return manage_currencies_csv_service.manage_currencies_csv(file)
# return 'do some magic!'


def reserve_bundle(body=None, reseller_id=None, branch_id=None, currency_code=None):  # noqa: E501
    """Reserve Bundle

    Checks if the reseller has balance, and if the bundle is available, and assigns it to a user. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: reseller id for super admin
    :type branch_id: str
    :param currency_code: currency code to preview in
    :type currency_code: str

    :rtype: ReserveBundleResponse
    """
    if connexion.request.is_json:
        body = ReserveBundleRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return reserve_bundle_service.reserve_bundle(body, reseller_id, branch_id, currency_code)
# return 'do some magic!'


def topup_bundle(body=None, reseller_id=None, branch_id=None, currency_code=None):  # noqa: E501
    """Topup Bundle

    Checks if the reseller has balance, and if the bundle is available, topups the bundle of the user. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param branch_id: branch id for super admin and reseller admin
    :type branch_id: str
    :param currency_code: 
    :type currency_code: str

    :rtype: TopupBundleResponse
    """
    if connexion.request.is_json:
        body = TopupBundleRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return topup_bundle_service.topup_bundle(body, reseller_id, branch_id, currency_code)
# return 'do some magic!'

from Services.network_list_services import delete_network_list_service,get_all_network_list_service,get_network_list_service,get_network_list_by_regions_service,manage_network_list_service,manage_networks_csv_service
import connexion
import six

from swagger_server.models.all_network_list_networks_response import AllNetworkListNetworksResponse  # noqa: E501
from swagger_server.models.delete_network_list_response import DeleteNetworkListResponse  # noqa: E501
from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.manage_network_list_request import ManageNetworkListRequest  # noqa: E501
from swagger_server.models.manage_network_list_response import ManageNetworkListResponse  # noqa: E501
from swagger_server.models.network_list import NetworkList  # noqa: E501
from swagger_server.models.network_list_by_regions_response import NetworkListByRegionsResponse  # noqa: E501
from swagger_server import util


def delete_network_list(network_id):  # noqa: E501
    """Delete NetworkList

    Allows admin to delete a Network. # noqa: E501

    :param network_id: id value that needs to be considered for filter
    :type network_id: str

    :rtype: DeleteNetworkListResponse
    """
    return delete_network_list_service.delete_network_list(network_id)
# return 'do some magic!'


def get_all_network_list(page_size=None, page_number=None, export=None):  # noqa: E501
    """Gets all Network lists from for all bundles

    Gets all Network lists , can be called by users. # noqa: E501

    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param export: 
    :type export: bool

    :rtype: AllNetworkListNetworksResponse
    """
    return get_all_network_list_service.get_all_network_list(page_size, page_number, export)
# return 'do some magic!'


def get_network_list(network_id=None, country_code=None, page_size=None, page_number=None, export=None):  # noqa: E501
    """Gets all Network lists

    Gets all Network lists , can be called by users. # noqa: E501

    :param network_id: 
    :type network_id: str
    :param country_code: 
    :type country_code: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param export: 
    :type export: bool

    :rtype: NetworkList
    """
    return get_network_list_service.get_network_list(network_id, country_code, page_size, page_number, export)
# return 'do some magic!'


def get_network_list_by_regions(network_id=None, country_code=None, region_code=None, page_size=None, page_number=None, export=None):  # noqa: E501
    """Gets all Network lists

    Gets all Network lists , can be called by users. # noqa: E501

    :param network_id: 
    :type network_id: str
    :param country_code: 
    :type country_code: str
    :param region_code: 
    :type region_code: str
    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param export: 
    :type export: bool

    :rtype: NetworkListByRegionsResponse
    """
    return get_network_list_by_regions_service.get_network_list_by_regions(network_id, country_code, region_code, page_size, page_number, export)
# return 'do some magic!'


def manage_network_list(body=None):  # noqa: E501
    """manage network lists to branch.

    Adds a NetworkList to branch, can be called by PERSONNEL. # noqa: E501

    :param body: 
    :type body: dict | bytes

    :rtype: ManageNetworkListResponse
    """
    if connexion.request.is_json:
        body = ManageNetworkListRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return manage_network_list_service.manage_network_list(body)
# return 'do some magic!'


def manage_networks_csv(file=None):  # noqa: E501
    """Manage Networks CSV.

    Allows a Super admin to manage Networks Via CSV File. # noqa: E501

    :param file: 
    :type file: strstr

    :rtype: ManageNetworkListResponse
    """
    return manage_networks_csv_service.manage_networks_csv(file)
# return 'do some magic!'

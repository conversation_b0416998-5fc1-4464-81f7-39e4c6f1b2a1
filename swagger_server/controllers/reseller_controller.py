from Services.reseller_services import add_reseller_service,available_reseller_properties_service,customize_corporate_price_service,customize_corporate_price_csv_service,customize_price_service,customize_price_csv_service,delete_reseller_service,edit_reseller_service,get_reseller_service,get_reseller_by_id_service,topup_reseller_balance_service
import connexion
import six

from swagger_server.models.add_reseller_request import AddResellerRequest  # noqa: E501
from swagger_server.models.add_reseller_response import AddResellerResponse  # noqa: E501
from swagger_server.models.customize_corp_price_request import CustomizeCorpPriceRequest  # noqa: E501
from swagger_server.models.customize_price_request import CustomizePriceRequest  # noqa: E501
from swagger_server.models.customize_price_response import CustomizePriceResponse  # noqa: E501
from swagger_server.models.delete_reseller_response import DeleteResellerResponse  # noqa: E501
from swagger_server.models.edit_reseller_request import EditResellerRequest  # noqa: E501
from swagger_server.models.edit_reseller_response import EditResellerResponse  # noqa: E501
from swagger_server.models.get_available_reseller_properties_response import GetAvailableResellerPropertiesResponse  # noqa: E501
from swagger_server.models.get_reseller_response import GetResellerResponse  # noqa: E501
from swagger_server.models.get_resellers_response import GetResellersResponse  # noqa: E501
from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.topup_balance_request import TopupBalanceRequest  # noqa: E501
from swagger_server.models.topup_balance_response import TopupBalanceResponse  # noqa: E501
from swagger_server import util


def add_reseller(body=None, currency_code=None):  # noqa: E501
    """Adds a Reseller to platform.

    Adds a Reseller to platform, can be called by PERSONNEL. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param currency_code: currency_code for operation
    :type currency_code: str

    :rtype: AddResellerResponse
    """
    if connexion.request.is_json:
        body = AddResellerRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return add_reseller_service.add_reseller(body, currency_code)
# return 'do some magic!'


def available_reseller_properties(category_names_list=None):  # noqa: E501
    """Get available properties of resellers

    Allows the user to view all properties that are available for resellers. # noqa: E501

    :param category_names_list: category names list of resellers
    :type category_names_list: List[str]

    :rtype: GetAvailableResellerPropertiesResponse
    """
    return available_reseller_properties_service.available_reseller_properties(category_names_list)
# return 'do some magic!'


def customize_corporate_price(reseller_id, body=None, currency_code=None):  # noqa: E501
    """Customize Bundle Unit Price.

    Allows a reseller agent to customize bundle retail price. # noqa: E501

    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param body: 
    :type body: dict | bytes
    :param currency_code: currency_code to preview
    :type currency_code: str

    :rtype: CustomizePriceResponse
    """
    if connexion.request.is_json:
        body = CustomizeCorpPriceRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return customize_corporate_price_service.customize_corporate_price(reseller_id, body, currency_code)
# return 'do some magic!'


def customize_corporate_price_csv(reseller_id, file=None):  # noqa: E501
    """Customize Bundle Unit Price CSV file.

    Allows a reseller agent to customize bundle retail price by CSV files. # noqa: E501

    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param file: 
    :type file: strstr

    :rtype: CustomizePriceResponse
    """
    return customize_corporate_price_csv_service.customize_corporate_price_csv(reseller_id, file)
# return 'do some magic!'


def customize_price(body=None, reseller_id=None, currency_code=None):  # noqa: E501
    """Customize Bundle Price.

    Allows a reseller agent to customize bundle retail price. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str
    :param currency_code: currency_code to preview
    :type currency_code: str

    :rtype: CustomizePriceResponse
    """
    if connexion.request.is_json:
        body = CustomizePriceRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return customize_price_service.customize_price(body, reseller_id, currency_code)
# return 'do some magic!'


def customize_price_csv(file=None, reseller_id=None):  # noqa: E501
    """Customize Bundle Price.

    Allows a reseller agent to customize bundle retail price via Excel CSV File. # noqa: E501

    :param file: 
    :type file: strstr
    :param reseller_id: reseller id for super admin
    :type reseller_id: str

    :rtype: CustomizePriceResponse
    """
    return customize_price_csv_service.customize_price_csv(file, reseller_id)
# return 'do some magic!'


def delete_reseller(reseller_id):  # noqa: E501
    """Delete Reseller

    Allows PERSONNEL to delete Reseller. # noqa: E501

    :param reseller_id: id value that needs to be considered for filter
    :type reseller_id: str

    :rtype: DeleteResellerResponse
    """
    return delete_reseller_service.delete_reseller(reseller_id)
# return 'do some magic!'


def edit_reseller(reseller_id, body=None):  # noqa: E501
    """Edit Reseller

    Allows admins to edit Reseller # noqa: E501

    :param reseller_id: id value that needs to be considered for filter
    :type reseller_id: str
    :param body: 
    :type body: dict | bytes

    :rtype: EditResellerResponse
    """
    if connexion.request.is_json:
        body = EditResellerRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return edit_reseller_service.edit_reseller(reseller_id, body)
# return 'do some magic!'


def get_reseller(page_size=None, page_number=None, dropdown=None, reseller_name=None, supports_promo=None, supports_voucher=None, currency_code=None, is_whitelabel=None, reseller_category=None):  # noqa: E501
    """Gets all  Resellers

    Gets all Resellers , can be called by PERSONNEL. # noqa: E501

    :param page_size: 
    :type page_size: int
    :param page_number: 
    :type page_number: int
    :param dropdown: 
    :type dropdown: bool
    :param reseller_name: filter parameter
    :type reseller_name: str
    :param supports_promo: filter parameter
    :type supports_promo: bool
    :param supports_voucher: filter parameter
    :type supports_voucher: bool
    :param currency_code: currency_code to preview
    :type currency_code: str
    :param is_whitelabel: filter parameter
    :type is_whitelabel: bool
    :param reseller_category: filter parameter
    :type reseller_category: str

    :rtype: GetResellersResponse
    """
    return get_reseller_service.get_reseller(page_size, page_number, dropdown, reseller_name, supports_promo, supports_voucher, currency_code, is_whitelabel, reseller_category)
# return 'do some magic!'


def get_reseller_by_id(reseller_id, currency_code=None):  # noqa: E501
    """Gets Reseller by ID

    Gets a Reseller by ID , can be called by PERSONNEL. # noqa: E501

    :param reseller_id: id value that needs to be considered for filter
    :type reseller_id: str
    :param currency_code: currency_code to preview
    :type currency_code: str

    :rtype: GetResellerResponse
    """
    return get_reseller_by_id_service.get_reseller_by_id(reseller_id, currency_code)
# return 'do some magic!'


def topup_reseller_balance(reseller_id, body=None, currency_code=None):  # noqa: E501
    """Topup Reseller Balance.

    Allows a monty agent to topup reseller wallet balance. # noqa: E501

    :param reseller_id: id value that needs to be considered for filter
    :type reseller_id: str
    :param body: 
    :type body: dict | bytes
    :param currency_code: currency_code to preview
    :type currency_code: str

    :rtype: TopupBalanceResponse
    """
    if connexion.request.is_json:
        body = TopupBalanceRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return topup_reseller_balance_service.topup_reseller_balance(reseller_id, body, currency_code)
# return 'do some magic!'

from Services.authorization_services import check_ApiKeyAuth_service,check_InternalApiKeyAuth_service
from typing import List
"""
controller generated to handled auth operation described at:
https://connexion.readthedocs.io/en/latest/security.html
"""
def check_ApiKeyAuth(api_key, required_scopes):
    return check_ApiKeyAuth_service.check_ApiKeyAuth(api_key, required_scopes)
# return {'test_key': 'test_value'}

def check_InternalApiKeyAuth(api_key, required_scopes):
    return check_InternalApiKeyAuth_service.check_InternalApiKeyAuth(api_key, required_scopes)
# return {'test_key': 'test_value'}



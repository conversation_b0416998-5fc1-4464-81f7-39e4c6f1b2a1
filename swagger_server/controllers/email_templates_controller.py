from Services.email_templates_services import email_templates_service
from Services.email_templates_services import email_templates_service
import connexion
import six

from swagger_server.models.inline_response204 import InlineResponse204  # noqa: E501
from swagger_server.models.manage_email_templates_request import ManageEmailTemplatesRequest  # noqa: E501
from swagger_server.models.manage_email_templates_response import ManageEmailTemplatesResponse  # noqa: E501
from swagger_server import util


def email_templates(body=None, reseller_id=None):  # noqa: E501
    """Manage email templates.

    Manage email templates. # noqa: E501

    :param body: 
    :type body: dict | bytes
    :param reseller_id: reseller id for super admin
    :type reseller_id: str

    :rtype: ManageEmailTemplatesResponse
    """
    if connexion.request.is_json:
        body = ManageEmailTemplatesRequest.from_dict(connexion.request.get_json())  # noqa: E501
    return email_templates_service.email_templates(body, reseller_id)


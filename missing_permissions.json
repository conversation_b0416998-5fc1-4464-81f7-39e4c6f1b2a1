{"Reseller": ["healthCheck", "login", "forgotPassword", "resetPassword", "logout", "addAgent", "getAgent", "editAgent", "deleteAgent", "getAgentById", "refreshToken", "getBundlesOfCountryCsv", "getCountries", "manageCurrenciesCsv", "manageCurrencies", "getCurrencies", "getRegions", "getBundlesOfCountry", "assignBundle", "getBundleNetworkList", "topupBundle", "getCompatibleTopupBundles", "getCompatibleTopupBundlesV2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "customizeCorporatePrice", "customizeCorporatePriceCsv", "customizePrice", "customizePriceCsv", "topupResellerBalance", "<PERSON><PERSON><PERSON><PERSON>", "delete<PERSON>eseller", "getResellerById", "addBranch", "getBranch", "editBranch", "deleteBranch", "getBranchById", "getTransactionHistory", "editTransaction", "getOrderHistory", "getDashboard", "getPromocodeHistory", "getAffiliateProgram", "getPromocodeDashboard", "getBundleConsumption", "getMyBundleConsumption", "resendOrderEmail", "sendConsumptionEmail", "refundOrder", "deleteRole", "editRole", "getRoleById", "getAllRoles", "createRole", "updateMontyAdminPermissions", "generateVoucher", "getVoucherCodeHistory", "getVoucherCodes", "getVouchers", "addIssueReport", "getIssueReport", "editIssueReport", "deleteIssueReport", "resolveIssue", "submitFeedback", "manageNetworkList", "getNetworkList", "deleteNetworkList", "manageNetworksCsv", "getAvailableTopupBundles", "availableResellerProperties", "getAgentByEmail", "reserveBundle", "cancelBundle", "completeTransaction", "getAllNetworkList", "getPlanHistory", "emailTemplates", "getNetworkListByRegions", "getBundlesOfCountryV2", "patchVoucher"]}
from instance import consumer_config as instance_config
from b2c_helpers.db_helper import AllocationBundle, get_vendor_info
from flask_mongoengine import MongoEngine
from flask import Flask
import unittest
import datetime
import requests
import logging
import json


logging.basicConfig(filename="log_assign_bundle.txt", filemode="w", level=logging.DEBUG)
logging.info(datetime.datetime.now())


file = open('test_assign_bundle.json', "r")
file_data = file.read()
data_dictionary = json.loads(file_data)
file.close()


def get_token_flexiroam(username, password):
    try:
        headers = {}
        payload = {'email': username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post('{}/user/login/v1'.format(instance_config.flexiroam_url), headers=headers,
                          data=payload)
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"

def unload_flexi(plan_uid):
    try:
        bundles = False
        exception = ""
        access_token = get_vendor_info("Flexiroam").temp_token

        if access_token:
            headers = {
                'token': access_token,
                'Content-Type': 'application/json'
            }
            payload = {"plan_uid": plan_uid}
            r = requests.post('{}/plan/unload/v1'.format(instance_config.flexiroam_url), headers=headers, json=payload)
            bundles = r.json()

    except Exception as e:
        print("Exception: ", str(e))

    return bundles, exception


def reset_bundle(bundle):
    bundle.is_active = True
    bundle.consumed_unit = int(bundle.consumed_unit) - 1
    bundle.save()


class UnitTestSuite(unittest.TestCase):
    # Set up the Flask app for testing
    def setUp(self):
        # create a Flask app
        app = Flask(__name__)
        app.config['MONGODB_SETTINGS'] = {
            'db': 'rsp_unit_test',
            'host': 'localhost',
            'port': 27017
        }

        # create a MongoEngine instance
        self.db = MongoEngine(app)

    def test_assign_bundle(self):
        function_name = "assign_bundle"
        test_cases = data_dictionary[function_name]
        for case in test_cases:
            test_name = case["name"]
            test_input = case["input"]
            test_expected = case["expected"]

            allocate_bundle = AllocationBundle()
            response_json = allocate_bundle.assign_bundle(bundle_code=test_input.get("bundle_code", ""),
                                                           email=test_input.get("email", 50),
                                                           instance_config=test_input.get("instance_config", 1),
                                                           iccid=test_input.get("iccid", None),
                                                           reseller_id=test_input.get("reseller_id", None),
                                                           category=test_input.get("category", None))
            actual = response_json["status"]
            if actual:
                plan_uid = response_json["data"]["plan_uid"]

                bundle_object = AllocationBundle.get_bundle_object(bundle_code=test_input.get("bundle_code", ""))
                unload_flexi(plan_uid=plan_uid)
                reset_bundle(bundle=bundle_object)

                message = f"SUCCESSFUL test {test_name} in {function_name}"
                logging.log(20, message)
            else:
                message = f"FAILED test {test_name} in {function_name} expected {test_expected['status']}," \
                          f" actual {actual} with response: {response_json}"
                logging.log(50, message)


            self.assertEqual(test_expected['status'], actual, message)


if __name__ == '__main__':
    unittest.main()
FROM python:3.8-slim-buster

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

ARG GIT_HELPERS_USERNAME
ARG GIT_HELPERS_ACCESS_TOKEN
ARG GIT_HELPERS_VERSION_TAG


# Install needed binaries for PyInstaller later.
ARG GIT_USERNAME
ARG GIT_ACCESS_TOKEN

RUN apt-get update
RUN apt-get install -y binutils git

RUN adduser --disabled-password --home /home/<USER>

RUN mkdir /home/<USER>/app/ && chown -R rsp:rsp /home/<USER>/app
WORKDIR /home/<USER>/app

USER rsp

ENV PATH="/home/<USER>/.local/bin:${PATH}"

ADD --chown=rsp:rsp ./requirements.txt requirements.txt



ARG CACHEBUST=1

RUN pip install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim-b2c-models-py.git
RUN pip install --upgrade --force-reinstall git+https://$GIT_HELPERS_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim_b2c_helpers.git@$GIT_HELPERS_VERSION_TAG

RUN pip install -r requirements.txt

ADD --chown=rsp:rsp . .

CMD ["gunicorn","--workers=1","--threads=2 ","--worker-class=gthread","app_main:app", "-b", "0.0.0.0:5007"]

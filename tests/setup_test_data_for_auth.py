from datetime import datetime

from app_models.main_models import OperatorList, KeycloackSettings, EmailSettings, Settings
from app_models.mobiles import AppVersionList, AppList, app_platforms_, AppUserDetails
from app_models.consumer_models import Languages

from instance import consumer_config


def setup():
    """
        only create
    """
    app = (AppList.objects(op_name="Orange").first() or AppList(**{
            "op_name": "Orange", 'app_name': 'B2COrg', 'app_platform': app_platforms_[0],
            "update_date": datetime.utcnow()
        }).save())

    app_version = (AppVersionList.objects(version_token="2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE").first() or AppVersionList(**{
        "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE", "app_id": app.to_dbref(),
        "total_request": 20, "total_verify": 20, "total_download": "20", "update_date": datetime.utcnow(),
        "create_date": datetime.utcnow(),"obsolete_versions":["1.old.0"], "user_agents": ["test_user_agent"],
        "force_update": True
    }).save())

    app_version_without_app_id = (AppVersionList.objects(version_token="ASDASDSAD").first() or AppVersionList(**{
        "version_token": "ASDASDSAD", "app_id": None,
        "total_request": 20, "total_verify": 20, "total_download": "20", "update_date": datetime.utcnow(),
        "create_date": datetime.utcnow(), "obsolete_versions":["1.old.0"], "user_agents": ["test_user_agent"],
        "force_update": True
    }).save())

    app_without_operator = (AppList.objects(op_name="NoOperator").first() or  AppList(**{
        "op_name": "NoOperator", 'app_name': 'B2COrg', 'app_platform': app_platforms_[0],
        "update_date": datetime.utcnow()
    }).save())

    app_version_without_operator = (AppVersionList.objects(version_token="XZCXZC").first() or AppVersionList(**{
        "version_token": "XZCXZC", "app_id": app_without_operator.to_dbref(),
        "total_request": 20, "total_verify": 20, "total_download": "20", "update_date": datetime.utcnow(),
        "create_date": datetime.utcnow(),
    }).save())

    operator = (OperatorList.objects(op_name="Orange").first() or OperatorList(**{
        "op_name": "Orange", "cert_name": "test_file.pem", 'sk_name': "OrangeB2c", 'unverified_context': True,
        'request_identifier': "Orange", "profile_type": "Organization", "from_email": "<EMAIL>",
        'op_country': 'Egypt', 'op_fci': b"000", 'salt': b"01111", 'create_date': datetime.utcnow()
    }).save())



    user = (AppUserDetails.objects(user_email="<EMAIL>").first()  or AppUserDetails(**{
        "user_token": "Token", "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE", "user_name": "<EMAIL>",
        'user_email': "<EMAIL>", "balance": 150,"fcm_token":"token firebase", "device_id": "device_id", "ipv4_address": "*******"
    }).save())

    app_email_verification =  AppUserDetails.objects(user_email=user.user_email).first()
    keycloak_setting = (KeycloackSettings.objects(op_name=consumer_config.operator_name).first()  or  KeycloackSettings(**{
        "op_name": consumer_config.operator_name, "client_id": "admin-cli", 'client_key': "1234555555555555555555550",
        'releam_name': 'orange', 'admin_releam': "master", 'admin_key': '01'
    }).save())

    email_setting =(EmailSettings.objects().first() or  EmailSettings(**{
        "email": "<EMAIL>", 'username': 'test', 'password': "test", 'smtp_server': "protocol.com", 'smtp_port':1
    }).save())

    (Settings.objects(contact_email="<EMAIL>").first() or Settings(**{
        "contact_email": "<EMAIL>", "esim_email": "<EMAIL>", "merchant_key": "test",
        "merchant_password": "1234567890", "fcm_registration": "123456", "whatsapp_misisdn": "0101517485",
        "reward_amount_limit_usd": 10, "percentage_of_reward": 10, "purchase_threshold_for_reward": 3
    }).save())

    (Languages.objects(language_code="en").first() or Languages(**{"language_code": "en", "language_name": "English", "default_lang": True, "is_ltr": True}).save())

    return (
        app, app_version, app_version_without_app_id, app_without_operator, app_version_without_operator, operator,
        app_email_verification, user, keycloak_setting, email_setting
    )

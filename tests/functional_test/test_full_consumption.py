import unittest
from datetime import datetime, timedelta
from hashlib import md5

from app_models.consumer_models import UserBundleLog, Bundles, Vendors, regions_, region_names_, Profiles, UserIccid
from app_models.reseller_models import Order_history
from app_models.mobiles import AppList, app_platforms_, AppVersionList
from unittest import mock

from app_extra_dev import app
from app_helpers.script_helper import get_full_consumption
from tests.functional_test.mocked_requests import MockedRequest
from tests.instance import consumer_config


class TestFullConsumption(unittest.TestCase):
    flexiroam_url = consumer_config.flexiroam_url
    CUSTOMER_EMAIL = '<EMAIL>'

    def setUp(self):
        app.testing = True

        self.vodafone_vendor = Vendors(**{
            "vendor_name": "Vodafone", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "is_active": True
        }).save()
        self.indosat_vendor = Vendors(**{
            "vendor_name": "Indosa", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "is_active": True
        }).save()
        self.montymobile_vendor = Vendors(**{
            "vendor_name": "Monty Mobile", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "is_active": True
        }).save()
        self.esimgo_vendor = Vendors(**{
            "vendor_name": "eSIMGo", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "is_active": True
        }).save()
        self.flexiroam_vendor = Vendors(**{
            "vendor_name": "Flexiroam", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "is_active": True, "temp_token": "eytyyyyyyyyy"
        }).save()

        self.vodafone_bundle = Bundles(**{
            "vendor_name": self.vodafone_vendor.vendor_name, "bundle_code": "5G-Vodafone", "bundle_name": "5G",
            "bundle_marketing_name": "fastest-4G", "category_name": "1", "region_code": region_names_[1],
            "retail_price": 1, "bundle_vendor_name": self.vodafone_vendor.vendor_name,
            "region_name": regions_[1], "bundle_vendor_code": "5G-Vodafone",
            "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30, "allocated_unit": 30,
            "supplier_vendor": f"{self.vodafone_vendor.id}", "bundle_category": "country", "country_list": ["FR"],
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
            "validity_amount": "29", "profile_names": "", "is_active": True, "country_code_list": ["FR"]
        }).save()

        self.order_history = Order_history(**{
            "client_email": self.CUSTOMER_EMAIL, "bundle_code": self.vodafone_bundle.bundle_code, "country_code": "EG",
            "amount": self.vodafone_bundle.retail_price, "paid_amount_credit_card": self.vodafone_bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": '123456789123', "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": '123456789125', "cancel_otp": '123456789120',
            "validy_date": datetime.today() + timedelta(self.vodafone_bundle.bundle_duration),
            "order_number": "12345678", "payment_category": 1,
        }).save()
        self.profile_vodafone = Profiles(**{
            "vendor_name": self.vodafone_vendor.vendor_name, "sku": "profile-id-unique-new-one",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "21305670901234567890",
        }).save()

        self.profile_flexiroam = Profiles(**{
            "vendor_name": self.flexiroam_vendor.vendor_name, "sku": "profile-id-unique-new-one",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "22305670901234567890",
        }).save()

        self.profile_flexiroam_2 = Profiles(**{
            "vendor_name": self.flexiroam_vendor.vendor_name, "sku": "8910300000003657004",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "8910300000003657004",
        }).save()

        self.profile_esimgo = Profiles(**{
            "vendor_name": self.esimgo_vendor.vendor_name, "sku": "profile-id-unique-new-one",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "23305670901234567890",
        }).save()

        self.profile_montymobile = Profiles(**{
            "vendor_name": self.montymobile_vendor.vendor_name, "sku": "profile-id-unique-new-one",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "24305670901234567890",
        }).save()

        self.profile_indosat = Profiles(**{
            "vendor_name": self.indosat_vendor.vendor_name, "sku": "profile-id-unique-new-one",
            "qr_code_value": None, "profile_names": "", "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code, "plan_uid": "5g-KLOW-20",
            "create_datetime": datetime.utcnow(), "availability": "Assigned", "status": True,
            'expiry_date': datetime.utcnow() + timedelta(90), "iccid": "25305670901234567890",
        }).save()

    def tearDown(self):
        self.vodafone_vendor.delete()
        self.montymobile_vendor.delete()
        self.flexiroam_vendor.delete()
        self.esimgo_vendor.delete()
        self.indosat_vendor.delete()

        self.vodafone_bundle.delete()
        self.order_history.delete()

        self.profile_indosat.delete()
        self.profile_vodafone.delete()
        self.profile_montymobile.delete()
        self.profile_flexiroam.delete()
        self.profile_esimgo.delete()
        self.profile_flexiroam_2.delete()

    @mock.patch(
        "requests.post",
        side_effect=[
            MockedRequest(
                method="POST",
                url=f"{consumer_config.flexiroam_url}/plan/simplan/v1", headers={"token": ""},
                status_code=200, data={},
                json={
                    "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
                }, response={"data": None, "success": False}
            )
        ]
    )
    def test_full_consumption(self,  *args):
        # Testing Flexiroam
        vendor = self.flexiroam_vendor
        vendor.vendor_name = "Flexiroam"
        profile = self.profile_flexiroam
        bundles, _, _ = get_full_consumption(profile)
        self.assertFalse(bundles['status'])
        self.assertEqual(bundles['message'], "No Data to view")

        profile_2 = self.profile_flexiroam_2
        bundles, profiles, esim = get_full_consumption(profile_2)
        self.assertTrue(bundles['status'])
        self.assertNotEqual(bundles['message'], "No Data to view")

        # Testing eSIMGo
        # sku =self.esimgo_vendor
        # sku.vendor_name = "eSIMGo"
        # bundles, profiles, esim = get_full_consumption(sku)
        # # Write appropriate assertions based on expected responses
        #
        # # Testing Vodafone
        # sku =self.vodafone_vendor
        # sku.vendor_name = "Vodafone"
        # bundles, profiles, esim = get_full_consumption(sku)
        # # Write appropriate assertions based on expected responses
        #
        # # Testing Monty Mobile
        # sku =self.montymobile_vendor
        # sku.vendor_name = "Monty Mobile"
        # bundles, profiles, esim = get_full_consumption(sku)
        # # Write appropriate assertions based on expected responses
        #
        # # Testing Indosat
        # sku =self.indosat_vendor
        # sku.vendor_name = "Indosat"
        # bundles, profiles, esim = get_full_consumption(sku)
        # # Write appropriate assertions based on expected responses

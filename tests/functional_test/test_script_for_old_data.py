from app_models.consumer_models import  Bundles, UserIccid, UserBundleLog
from datetime import datetime, timedelta
import re_save_old_data
from app_main import app
import pymongo
from unittest import TestCase
from instance import consumer_config
import secrets
import string
from app_models.reseller_models import Order_history
from app_models.main_models import HistoryLogs


def generate_temp_otp(n=25):
    new_rand = ''.join(secrets.choice(string.digits) for i in range(0, n))
    return new_rand


class MigrateData(TestCase):
    @classmethod
    def setUpClass(cls):
        cls.client = app.test_client()

        mongo_client = pymongo.MongoClient(consumer_config.new_host_)
        cls.db = mongo_client.get_database(consumer_config.decrypted_db_name_alias)

    def setUp(self):
        app.testing = True

        self.bundle = (Bundles.objects(bundle_code="5G-Flexi").first() or Bundles(**{
            "vendor_name": "Flexiroam", "bundle_code": "5G-Flexi", "bundle_name": "5G-flexi",
            "bundle_marketing_name": "fastest-flexi-4G", "category_name": "1", "region_code": "an",
            "retail_price": 1, "bundle_vendor_name": "Flexiroam",
            "bundle_vendor_code": "5G-Flexi",
            "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
            "allocated_unit": 30,
            "consumed_unit": 25,
            "bundle_category": "country", "country_list": ["EG"],
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
            "validity_amount": "29", "daily_used": 0,
            "profile_names": "te", "is_active": True, "country_code_list": ["EG"]
        }).save())

        self.user_iccid = UserIccid(**{
            "email": "<EMAIL>", "status": "active", "payment_otp": "123456-010",
            "datetime": datetime.utcnow() - timedelta(days=15), "iccid": "6789"
        }).save()

        self.user_iccid_2 = UserIccid(**{
            "email": "<EMAIL>", "status": "active", "payment_otp": "123456-011",
            "datetime": datetime.utcnow() - timedelta(days=15), "iccid": "6789"
        }).save()

        self.user_bundle_log = UserBundleLog(**{
            "email": "<EMAIL>", "bundle_code": self.bundle.bundle_code, "country_code": "EG",
            "amount": self.bundle.retail_price, "paid_amount_credit_card": self.bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": "123456-010", "cancel_otp": generate_temp_otp(12),
            "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
            "order_number": "whsec_sP8iSRHHQzAVJW5O6USVdRBz9UjhwwU3-FLEXI",
            "history_log": 'hist_124', "payment_status": True,
            "payment_id": "1234", "promo_code": "", "currency_code": self.bundle.currency_code,
            "validity_days": self.bundle.bundle_duration, "version_token": "",
            "payment_category": 1, 'stripe_client_secret': "*********",
        }).save()

        self.user_bundle_log_2 = UserBundleLog(**{
            "email": "<EMAIL>", "bundle_code": self.bundle.bundle_code, "country_code": "EG",
            "amount": self.bundle.retail_price, "paid_amount_credit_card": self.bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": "123456-011", "cancel_otp": generate_temp_otp(12),
            "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
            "order_number": "whsec_sP8iSRHHQzAVJW5O6USVdRBz9UjhwwU3-FLEXI",
            "history_log": 'xxxxxx', "payment_status": True,
            "payment_id": "1234", "promo_code": "", "currency_code": self.bundle.currency_code,
            "validity_days": self.bundle.bundle_duration, "version_token": "",
            "payment_category": 1, 'stripe_client_secret': "*********",
        }).save()

        self.history_log = HistoryLogs(**{
            "history_log_id": 'hist_124',
            "datetime": self.user_bundle_log.payment_date,
            "email": self.user_bundle_log.email,
            "iccid": "6789",
            "bundle_name": self.bundle.bundle_name,
            "bundle_marketing_name": self.bundle.bundle_marketing_name,
            "coverage": "AUSTRALIA",
            "price": self.user_bundle_log.amount,
            "currency_code": "USD",
            "bundle_code": self.bundle.bundle_code,
            "qr_code_link": "link",
            "sent_using": "Email",
            "transaction": 'BuyBundle'
        }).save()

    def test_save_old_data(self):
        re_save_old_data
        order_history_1 = Order_history.objects(client_email="<EMAIL>").first()
        if order_history_1:
            self.assertEqual(order_history_1.client_email, self.user_bundle_log.email)
            self.assertEqual(order_history_1.iccid, self.user_iccid.iccid)
            self.assertEqual(order_history_1.qr_code_link, self.history_log.qr_code_link)

        order_history_2 =Order_history.objects(client_email="<EMAIL>").first()
        if order_history_2:
            self.assertEqual(order_history_2.iccid, self.user_iccid_2.iccid)
            self.assertEqual(order_history_2.qr_code_link, self.history_log.qr_code_link)


    def tearDown(self):
        self.user_iccid.delete()
        self.history_log.delete()
        self.user_bundle_log.delete()
        self.user_iccid_2.delete()
        self.user_bundle_log_2.delete()


import unittest
from dataclasses import dataclass
from datetime import datetime, timedelta
from hashlib import md5
from unittest import mock

import mongoengine
from app_models.consumer_models import UserBundleLog, UserIccid, TransactionLogs, Profiles, CashbackHistory
from app_models.main_models import Settings, NotificationLogs, HistoryLogs
from app_models.mobiles import PromoRedeemLogs, AppUserDetails
from b2c_helpers.mock_send_email import DummySendEmail
from flask import json

from src import app

from src.global_helpers.email_helpers import  send_email_invoice
from b2c_helpers.db_helper import send_fcm_notification
from src.global_helpers.utils import random_url_token, generate_temp_otp
from instance import consumer_config
from tests.setup_test_data_for_assign_bundle import setup
from tests.func_test.mock_thread import MockThread
from tests.func_test.mocked_requests import MockedRequest


# @dataclass()
# class StripeCallbackRequest:
#     stripe_request_id: str
#     event: str
#     email: str
#     livemode: str
#     name: str
#     balance: int
#     amount_received: float
#     outcome: dict
#     paid: bool
#     payment_intent: str
#     failure_balance_transaction: int
#     failure_code: str
#     failure_message: str
#     status: str
#     receipt_url: str
#     payment_method_types: str
#     customer: str
#     client_secret: str
#     created: str
#
#     response: dict
#
#     assert_list: list
#
#     @property
#     def data(self):
#         return json.dumps({
#             "stripe_request_id": self.stripe_request_id, "event": self.event, "customer": self.customer,
#             "email": self.email, "livemode": self.livemode, "name": self.name, "balance": self.balance,
#             "amount_received": self.amount_received, "outcome": self.outcome, "paid": self.paid,
#             "payment_intent": self.payment_intent, "failure_balance_transaction": self.failure_balance_transaction,
#             "failure_code": self.failure_code, "failure_message": self.failure_message, "status": self.status,
#             "receipt_url": self.receipt_url, "payment_method_types": self.payment_method_types,
#             "client_secret": self.client_secret, "created": self.created
#         })
#
# @unittest.skip("old extremely outdated stripe callback api test")
# class StripeCallbackTestCases(unittest.TestCase):
#     STRIPE_API_ACCESS_TOKEN = "Stripe Api Access Token <Keycloak>"
#     CUSTOMER_EMAIL = "<EMAIL>"
#     PROMO_CODE = 'EID'
#
#     def setUp(self):
#         app.testing = True
#         self.client = app.test_client()
#         self.stripe_callback_url = "/stripe-callback"
#
#         try:
#             self.app, self.app_version, self.app_version_without_app_id, self.app_without_operator, \
#             self.app_version_without_operator, self.operator, self.app_email_verification, self.user, \
#             self.keycloak_setting, self.email_setting, self.vendor, self.bundle, self.esim_go_mock_bundle, \
#             self.profile, self.unused_user_iccid, self.esim_go_bundle, self.not_consumed_bundle, \
#             self.flexi_bundle_without_profile, self.user_bundle_log, self.esim_go_mock_vendor, self.esim_go_vendor, \
#             self.reward, self.from_user, self.vodafone_vendor, self.vodafone_bundle, \
#             self.unused_user_iccid_for_unused_profile, self.un_used_profile, self.esim_go_bundle_usa, \
#             self.alternative_flexi_bundle_for_vodafone, self.dark_continental_vodafone_bundle, self.free_profile = setup()
#             self.esim_go_profile = Profiles(**{
#                 "vendor_name": self.esim_go_vendor.vendor_name, "sku": "profile-esim-go-id-unique",
#                 "iccid": "12245678901234567890",
#                 "qr_code_value": "qr_code_value", "profile_names": "te", "smdp_address": "rsp.test.client.com",
#                 'matching_id': None,
#                 "create_datetime": datetime.utcnow()
#             }).save()
#         except mongoengine.errors.NotUniqueError as e:
#             self.tearDown()
#             raise e
#
#     def tearDown(self):
#         self.app.delete()
#         self.app_version.delete()
#         self.app_version_without_app_id.delete()
#         self.app_without_operator.delete()
#         self.app_version_without_operator.delete()
#         self.operator.delete()
#         self.app_email_verification.delete()
#         self.user.delete()
#         self.keycloak_setting.delete()
#         self.email_setting.delete()
#         self.vendor.delete()
#         self.bundle.delete()
#         self.esim_go_mock_bundle.delete()
#         self.profile.delete()
#         self.unused_user_iccid.delete()
#         self.esim_go_bundle.delete()
#         self.not_consumed_bundle.delete()
#         self.flexi_bundle_without_profile.delete()
#         self.user_bundle_log.delete()
#         self.esim_go_mock_vendor.delete()
#         self.esim_go_vendor.delete()
#         Settings.objects(contact_email="<EMAIL>").delete()
#         self.reward.delete()
#         self.from_user.delete()
#         self.vodafone_bundle.delete()
#         self.vodafone_vendor.delete()
#         UserBundleLog.objects(email=self.CUSTOMER_EMAIL).delete()
#         UserIccid.objects(email=self.CUSTOMER_EMAIL).delete()
#         NotificationLogs.objects(email=self.CUSTOMER_EMAIL).delete()
#         self.un_used_profile.delete()
#         self.unused_user_iccid_for_unused_profile.delete()
#         Profiles.objects(
#             iccid__in=[
#                 "22345678901234567890",
#                 "12345678901254567899", "22245678901234567890", "22214678901234567890", "22224678901234567890"]
#         ).delete()
#         CashbackHistory.objects(user_email=self.CUSTOMER_EMAIL).delete()
#         self.esim_go_profile.delete()
#         self.esim_go_bundle_usa.delete()
#         self.alternative_flexi_bundle_for_vodafone.delete()
#         self.dark_continental_vodafone_bundle.delete()
#         self.free_profile.delete()
#         UserIccid.objects(email="<EMAIL>").delete()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[MockedRequest(
#             method="POST", url=consumer_config.stripe_login_url,
#             headers={}, status_code=200, data={}, json={
#                 "username": consumer_config.stripe_superadmin_username,
#                 "password": consumer_config.stripe_superadmin_password,
#             }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#         )]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 #url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZvKYlo2CYrlRYoUz",
#                 url=None,
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#         ]
#     )
#     def case_where_incoming_stripe_webhook_but_not_attached_to_user_bundle_log(self, *args):
#
#         self.skipTest("old extremely outdated stripe callback api test")
#
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": "Bearer mocked_token"
#         }
#
#         @dataclass()
#         class StripeCallbackFailureRequest(StripeCallbackRequest):
#             pass
#
#         test_case_where_no_user_bundle_log_for_incoming_webhook = StripeCallbackFailureRequest(
#             stripe_request_id="we_1N7wlo2eZvKYlo2CYrlRYoUz", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeed",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret="whsec_sP8iSRHHQzAVJW5O6USVdRBz9UjhwwU3", created=datetime.utcnow(),
#             response={"status": False}, assert_list=[]
#         )
#
#         test_cases = [
#             test_case_where_no_user_bundle_log_for_incoming_webhook,
#         ]
#
#         for test_case in test_cases:
#             response = self.client.post(self.stripe_callback_url, headers=headers, data=test_case.data)
#             self.assertEqual(response.json, test_case.response)
#
#     @mock.patch(
#         "app_helpers.main_helper.send_email"
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             # test_case_where_trying_to_buy_flexi_bundle
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             # mock stripe login while doing test buy esim go bundle
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             # mock allocate_esimgo_bundle while buy esim go bundle
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response={"orderReference": "0125102154444"}
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             # mock allocate_esimgo_bundle while buy esim go bundle
#             MockedRequest(
#                 method="GET",
#                 url=f"{consumer_config.esimgo_url}/v2.2/esims/assignments/we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 content=f'ICCID,Matching ID,RSP URL,Bundle,Reference\n12345678901254567899,SS:00:W0:R0:Q0:T1,rsp.test.client.com,2G-released,0125102154444',
#                 # another iccid value after successful buy esim go bundle we are create profile with below iccid
#                 text=f"12345678901254567899,rsp.test.client.com,SS:00:W0:R0:Q0:T1",
#                 response={"orderReference": "0125102154444"}
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": False, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response={}
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/esims/apply",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 response={"applyReference": "order_number"}, json={
#                     "iccid": "12345678901254567890", "name": "2G-released", "repeat": 1
#                 }
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             # test_case_where_trying_to_buy_flexi_bundle
#             MockedRequest(
#                 #url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz",
#                 url=None,
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             # mock status of trx in stripe while doing test buy esim go bundle
#             MockedRequest(
#                 #url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 url=None,
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockedRequest(
#                 #url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 url=None,
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             # test_case_where_trying_to_buy_flexi_bundle
#             # mock allocate flexi bundle
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response={"data": {"plan_uid": "plan_uid"}, "success": True}
#             ),
#             # test_case_where_trying_to_buy_flexi_bundle
#             # mock user consumption
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f'{consumer_config.flexiroam_url}/plan/simplan/v1', headers={"token": "vendor_tem_token"},
#                 status_code=200, json={}, data={"sku": "[profile-id-unique]"}, response={"data": [
#                     {"end_date": datetime.utcnow() + timedelta(days=10)}
#                 ]}
#             )
#         ]
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             # test_case_where_trying_to_buy_flexi_bundle
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             # test_case_where_trying_to_buy_flexi_bundle
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-esim-go-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             # test_case_where_trying_to_buy_esim_go_bundle
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True),
#                 name=f"Thread-to-esim-go-send-email-invoice-{datetime.utcnow()}"
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-esim-go-top-up-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             # test_case_where_trying_to_buy_esim_go_top_up_bundle
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True),
#                 name=f"Thread-to-esim-go-top-up-send-email-invoice-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     def test_success_flow(self, *args):
#         self.skipTest("old extremely outdated stripe callback api test")
#         dummy_sendemail = DummySendEmail()
#
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": "Bearer mocked_token"
#         }
#
#         @dataclass()
#         class StripeCallbackSuccessRequest(StripeCallbackRequest):
#             pass
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3"
#         '''user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.bundle.bundle_code, "country_code": "EG",
#             "amount": self.bundle.retail_price, "paid_amount_credit_card": self.bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": self.PROMO_CODE, "currency_code": self.bundle.currency_code,
#             "validity_days": self.bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": 1
#         })'''
#         #was user iccid added
#         test_case_where_trying_to_buy_flexi_bundle = StripeCallbackSuccessRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=stripe_client_secret, created=datetime.utcnow(),
#             response={
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                 'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0
#             }, assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=test_case_where_trying_to_buy_flexi_bundle.client_secret,
#                         transaction_status=True
#                     ).first(),
#                     "we are saving transaction bundle log before we go with success flow"
#                 ),
#                 lambda: self.assertTrue(
#                     PromoRedeemLogs.objects(
#                         promo_code=self.PROMO_CODE, username=self.CUSTOMER_EMAIL).first().consumed,
#                     "we mark promo code as consumed to make it use for one time"
#                 ),
#                 lambda: self.assertTrue(
#                     AppUserDetails.objects(user_email=self.CUSTOMER_EMAIL).first().temporary_balance < 0.0,
#                     "this should be fix balance cant be less than ZERO"
#                 ),
#                 lambda: self.assertEqual(
#                     UserIccid.objects(iccid=self.profile.iccid).order_by("-_id").first().plan_uid, "plan_uid",
#                     "we are saving plan_uid to user iccid if allocation done successfully"
#                 ),
#                 lambda: self.assertEqual(
#                     UserIccid.objects(iccid=self.profile.iccid).order_by("-_id").first().status, "used",
#                     "we are marking this iccid is used "
#                 ),
#                 lambda: self.assertIsNotNone(
#                     UserIccid.objects(iccid=self.profile.iccid).order_by("-_id").first().qr_code_path,
#                     "if allocation is done successfully we are adding qr code image to user iccid object"
#                 ),
#                 lambda: self.assertTrue(
#                     UserBundleLog.objects(
#                         order_number=user_bundle_log.order_number).order_by("-_id").first().payment_status,
#                     "after successful payment we mark user bundle log as payment_status true"
#                 ),
#                 lambda: self.assertTrue(
#                     TransactionLogs.objects(
#                         order_number=test_case_where_trying_to_buy_flexi_bundle.client_secret,
#                         transaction_status=True
#                     ).first().bundle_status,
#                     "after successful payment we mark transaction log as successful bundle_status to true"
#                 ),
#                 lambda: self.assertTrue(
#                     NotificationLogs.objects(
#                         email=self.app_email_verification.user_email, transaction="BuyBundle",
#                         transaction_message=f"Kindly note that you have bought {self.bundle.bundle_marketing_name} "
#                     ).order_by("-_id").first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         email=self.CUSTOMER_EMAIL, iccid=self.profile.iccid, bundle_name=self.bundle.bundle_name,
#                         bundle_marketing_name=self.bundle.bundle_marketing_name, coverage=self.bundle.country_list[0],
#                         price=self.bundle.retail_price, currency_code=self.bundle.currency_code,
#                         bundle_code=self.bundle.bundle_code, data_amount=self.bundle.data_amount
#                     ).first()
#                 ),
#                 lambda: self.assertTrue(
#                     CashbackHistory.objects(user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number).first().rewarded_amount > 0,
#                     "user wasn't rewarded via cashback"
#                 ),
#                 lambda: self.assertTrue(
#                     CashbackHistory.objects(user_email=self.CUSTOMER_EMAIL).count() >= Settings.objects(contact_email="<EMAIL>").first().purchase_threshold_for_reward,
#                     "user was rewarded before reward threshold is triggered"
#                 ),
#             ]
#         )
#
#         esimgo_stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go"
#         '''user_esim_go_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "EG",
#             "amount": self.esim_go_bundle.retail_price, "paid_amount_credit_card": self.esim_go_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "order_number": esimgo_stripe_client_secret,
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": 1, "stripe_client_secret": md5(esimgo_stripe_client_secret.encode()).hexdigest(),
#         })'''
#         '''add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": self.esim_go_bundle.bundle_code,
#             "country_code": "EG",
#             "iccid": self.esim_go_profile.iccid,
#             "activation_code": self.esim_go_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_esim_go_bundle_log.otp,
#             "cancel_otp": user_esim_go_bundle_log.cancel_otp,
#         })'''
#         test_case_where_trying_to_buy_esim_go_bundle = StripeCallbackSuccessRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret="whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go", created=datetime.utcnow(),
#             response={
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                 'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0
#             },
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=test_case_where_trying_to_buy_esim_go_bundle.client_secret,
#                         transaction_status=True
#                     ).first(),
#                     "we are saving transaction bundle log before we go with success flow"
#                 ),
#                 lambda: self.assertTrue(
#                     # cumulative temporary balance because we call the api twice
#                     AppUserDetails.objects(user_email=self.CUSTOMER_EMAIL).first().temporary_balance < 0.0,
#                     "this should be fix balance cant be less than ZERO"
#                 ),
#                 lambda: self.assertTrue(
#                     TransactionLogs.objects(
#                         order_number=test_case_where_trying_to_buy_esim_go_bundle.client_secret,
#                         transaction_status=True
#                     ).first().bundle_status,
#                     "after successful payment we mark transaction log as successful bundle_status to true"
#                 ),
#                 lambda: self.assertEqual(
#                     UserIccid.objects(iccid="12345678901254567899").order_by("-_id").first().plan_uid, "0125102154444",
#                     "we are saving plan_uid to user iccid if allocation done successfully"
#                 ),
#                 lambda: self.assertEqual(
#                     UserIccid.objects(iccid="12345678901254567899").order_by("-_id").first().status, "used",
#                     "we are saving used to user iccid if allocation done successfully to used"
#                 ),
#                 lambda: self.assertTrue(
#                     UserBundleLog.objects(
#                         order_number=user_esim_go_bundle_log.order_number).order_by("-_id").first().payment_status,
#                     "after successful payment we mark user bundle log as payment_status true"
#                 ),
#                 lambda: self.assertTrue(
#                     TransactionLogs.objects(
#                         order_number=test_case_where_trying_to_buy_esim_go_bundle.client_secret,
#                         transaction_status=True
#                     ).first().bundle_status,
#                     "after successful payment we mark transaction log as successful bundle_status to true"
#                 ),
#                 lambda: self.assertTrue(
#                     NotificationLogs.objects(
#                         email=self.app_email_verification.user_email, transaction="BuyBundle",
#                         transaction_message=f"Kindly note that you have bought {self.esim_go_bundle.bundle_marketing_name} "
#                     ).order_by("-_id").first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         email=self.CUSTOMER_EMAIL, iccid="12345678901254567899",
#                         bundle_name=self.esim_go_bundle.bundle_name,
#                         bundle_marketing_name=self.esim_go_bundle.bundle_marketing_name,
#                         coverage=self.esim_go_bundle.country_list[0],
#                         price=self.esim_go_bundle.retail_price, currency_code=self.esim_go_bundle.currency_code,
#                         bundle_code=self.esim_go_bundle.bundle_code, data_amount=self.esim_go_bundle.data_amount
#                     ).first()
#                 )
#             ]
#         )
#
#         '''user_esim_go_top_up_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "EG",
#             "amount": self.esim_go_bundle.retail_price, "paid_amount_credit_card": self.esim_go_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12),
#             "topup_code": self.esim_go_bundle.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go_top_up",
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": 1
#         })'''
#         '''add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": self.esim_go_bundle.bundle_code,
#             "country_code": "EG",
#             "iccid": self.esim_go_profile.iccid,
#             "activation_code": self.esim_go_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_esim_go_top_up_bundle_log.otp,
#             "cancel_otp": user_esim_go_top_up_bundle_log.cancel_otp,
#         })'''
#         test_case_where_trying_to_buy_esim_go_top_up_bundle = StripeCallbackSuccessRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_top_up", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret="whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go_top_up", created=datetime.utcnow(),
#             response={
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Topup added', 'responseCode': 1,
#                 'status': True, 'title': 'Success', 'total_count': 0},
#             assert_list=[]
#         )
#
#         test_cases = [
#             test_case_where_trying_to_buy_flexi_bundle,
#             test_case_where_trying_to_buy_esim_go_bundle,
#         ]
#
#         for test_case in test_cases:
#             response = self.client.post(self.stripe_callback_url, headers=headers, data=test_case.data)
#             self.assertEqual(response.json, test_case.response)
#             for assert_case in test_case.assert_list:
#                 assert_case()

import copy
from abc import ABC, abstractmethod


class BuildStrategy(ABC):
    @abstractmethod
    def build(self, base_request, modifications, deletions):
        pass


class DictBuildStrategy(BuildStrategy):
    def build(self, base_request, modifications, deletions):
        new_instance = copy.deepcopy(base_request)
        new_instance.update(modifications)
        for field in deletions:
            new_instance.remove(field)
        return new_instance


class MongoBuildStrategy(BuildStrategy):
    def __init__(self, document_class):
        self._document_class = document_class

    def build(self, base_request, modifications, deletions):
        document_fields = copy.deepcopy(base_request)
        document_fields.update(modifications)
        for field in deletions:
            if field in document_fields:
                del document_fields[field]
        mongo_doc = self._document_class(**document_fields)
        mongo_doc.save()
        return mongo_doc


class StrictDict(dict):
    def __init__(self, *args, **kwargs):
        self._allowed_keys = set(kwargs.keys())
        super().__init__(*args, **kwargs)
        for key in self.keys():
            self._validate_key(key)

    def __call__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for key in self.keys():
            self._validate_key(key)

    def __setitem__(self, key, value):
        self._validate_key(key)
        super().__setitem__(key, value)

    def __getitem__(self, item):
        self._validate_key(item)
        return super().__getitem__(item)

    def __delitem__(self, key):
        self._validate_key(key)
        self._allowed_keys.remove(key)
        super().__delitem__(key)

    def pop(self, key, *args):
        self._validate_key(key)
        self._allowed_keys.remove(key)
        return super().pop(key, *args)

    def update(self, *args, **kwargs):
        for key in dict(*args, **kwargs).keys():
            self._validate_key(key)
        super().update(*args, **kwargs)

    def remove(self, key):
        self._validate_key(key)
        self._allowed_keys.remove(key)
        super().pop(key, None)

    def _validate_key(self, key):
        if key not in self._allowed_keys:
            raise KeyError(f"Key '{key}' does not exist.")


class BaseDictFactoryBuilder:
    _base_request = StrictDict()
    _build_strategy = DictBuildStrategy()
    _strategy_locked = False  # Lock when a new strategy is set

    def __init__(self, strategy: BuildStrategy = None, **defaults):
        self._base_request = copy.deepcopy(self.__class__._base_request)
        self._base_request.update(defaults)
        self._modifications = {}
        self._deletions = set()  # Track fields to delete

        if strategy is not None and not self._strategy_locked:
            self._build_strategy = strategy
            self._strategy_locked = True

    def __call__(self, **changes):
        new_instance = copy.deepcopy(self)
        new_instance._modifications.update(changes)

        # Ensure the strategy context is shared, not copied
        new_instance._strategy_context = self._build_strategy

        return new_instance

    def delete_fields(self, *field_names):
        new_instance = copy.deepcopy(self)

        new_instance._deletions.update(field_names)
        for key in field_names:
            new_instance._modifications.pop(key, None)

        return new_instance

    # for read only purpose
    def get_base_request(self):
        return self._base_request

    # def mutate(self, strategy=None):
    #     mutated_instance = copy.deepcopy(self)
    #     if strategy is not None:
    #         mutated_instance._build_strategy = strategy
    #     return mutated_instance

    def build(self):
        return self._build_strategy.build(self._base_request, self._modifications, self._deletions)

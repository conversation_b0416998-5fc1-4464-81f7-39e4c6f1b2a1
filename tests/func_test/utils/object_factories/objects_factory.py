import json
from dataclasses import dataclass, field
from datetime import datetime
from collections import namedtuple

from tests.func_test.utils.factory_builder import StrictDict, MongoBuildStrategy, DictBuildStrategy, \
    BaseDictFactoryBuilder

from app_models.mobiles import AppVersionList, AppList, app_platforms_
from app_models.consumer_models import Bundles, region_names_, regions_, category_names_, Vendors, Profiles

from tests.func_test.utils.db_utils import ext_doc_fields


class VendorFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        **ext_doc_fields(
            Vendors,
            bundles_count=30,
            is_active=True,
            minimal_balance=1,
            temp_token="temp-access-token",
            apply_inventory=True,
            supports_empty_profiles=True,
            has_constant_bundle_count=True,
            get_profile_from_vendor=True,
            has_balance=False,
            support_topup=True
        )
    )

    def __init__(self, vendor_name=None, **defaults):
        super().__init__(
            **defaults,
            strategy=MongoBuildStrategy(Vendors),
            vendor_name=vendor_name
        )


class AppVersionListFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        **ext_doc_fields(
            AppVersionList,
            create_date=datetime.utcnow(),
            force_update=True,
            obsolete_versions=["1.old.0"],
            still_valid=True,
            total_download="20",
            total_request=20,
            total_verify=20,
            update_date=datetime.utcnow(),
            user_agents=["test_user_agent"]
        )
    )

    def __init__(self, version_token, app_list: AppList = None, **defaults):
        super().__init__(
            **defaults,
            strategy=MongoBuildStrategy(AppVersionList),
            app_id=app_list.to_dbref() if app_list is not None else None,
            version_number=app_platforms_.index(app_list.app_platform) if app_list is not None else None,
            version_token=version_token
        )


class ProfilesFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        **ext_doc_fields(
            Profiles,
            create_datetime=datetime.utcnow(),
            profile_names="te",
            smdp_address="rsp.test.client.com",
            status=True,
        )
    )

    def __init__(self, vendor_name, iccid, sku="profile-id-unique", qr_code_value="qr_code_value", availability=None,
                 **defaults):
        super().__init__(
            **defaults,
            strategy=MongoBuildStrategy(Profiles),
            vendor_name=vendor_name,
            iccid=iccid,
            sku=sku,
            qr_code_value=qr_code_value,
            availability=availability
        )


class BundleFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        **ext_doc_fields(
            Bundles,
            allocated_unit=0,
            bundle_category=category_names_[2],
            bundle_duration=30,
            bundle_marketing_name="special-bundle-marketing-name",
            bundle_name="special-bundle-name",
            category_name="1",
            consumed_unit=0,
            country_code_list=["EG"],
            country_list=["EG"],
            create_datetime=datetime.utcnow(),
            data_amount=30,
            data_unit="GB",
            deleted=False,
            fullspeed_data_amount=16,
            is_active=True,
            profile_names="te",
            region_code=region_names_[1],
            region_name=regions_[1],
            rate_revenue=1,
            retail_price=5,
            unit_price="10",
            validity_amount="29",
            group_id='yaman',
            unlimited=False
        )

    )

    def __init__(self, bundle_code, bundle_vendor_code, vendor_name, **defaults):
        super().__init__(
            strategy=MongoBuildStrategy(Bundles),
            bundle_code=bundle_code,
            bundle_vendor_code=bundle_vendor_code,
            vendor_name=vendor_name,
            **defaults,
        )


class KeycloakOpenIdUserInfoResponseFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        sub="248289761001",
        name="Satoru Gojo",
        given_name="Satoru",
        family_name="Gojo",
        preferred_username=None,
        role_name="normal_user",
        email=None,
        picture="http://example.com/janedoe/me.jpg",
        history_id="history_id"
    )

    def __init__(self, email, **defaults):
        super().__init__(**defaults, strategy=DictBuildStrategy(), preferred_username=email, email=email)


class KeycloakOpenIdIntrospectResponseFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        exp=1682638756,
        iat=1682602756,
        jti='105ee738-993d-447e-8507-ee0cd4aa36c3',
        iss='http://localhost:8080/realms/MontyMobile',
        sub='1087f5f9-48ed-4509-8a89-9a077a3d7211',
        typ='Bearer',
        azp='admin-cli',
        session_state='0efd540d-bf9d-452f-b647-24245319ce2c',
        name='zoro',
        given_name='Satoru',
        family_name='Gojo',
        preferred_username='Satoru',
        email=None,
        email_verified=False,
        acr='1',
        scope='email profile',
        active=True,
        role_name="normal_user",
        sid='0efd540d-bf9d-452f-b647-24245319ce2c',
        client_id='admin-cli',
        username='Satoru'
    )

    def __init__(self, **defaults):
        super().__init__(**defaults, strategy=DictBuildStrategy())


class StripeLoginResponseFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        refresh_token='refresh_token',
        access_token='access_token',
        expires_in='1800',
        status='success'
    )

    def __init__(self, **defaults):
        super().__init__(**defaults, strategy=DictBuildStrategy())


class StripeCreatePaymentFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        status=True,
        client_secret='4de2a6c6be9a4933b8a26e202da38fd376e530ac4590c2cdb9b0c9d582544ad47ac2c24e799d5fce6791b8',
        publishable_key='pk_test_3423DSADfS7c2XApZl0JtAqDuGoOJuyTyLmbDLPfUokXrASDASDASDQBBR00iIJjENRB',
        customer_id="cust_3131",
        ephemeral_key='',
        payment_intent_id='18baedfbdb02fcd32580d0d652aece672d80ed8e784128942e7b3cb4ea3a65200c14280e1f1f963e490301660cc9'
    )

    def __init__(self, **defaults):
        super().__init__(**defaults, strategy=DictBuildStrategy())


class MockRequestBuilder(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        bundle_code=None,
        country_code=None,
        version_token=None,
        payment_method=None,
        app_token=None,
        promo_code=None,
        searched_countries=None,
        affiliate_id=None,
        referral_code=None,
        topup_code=None,
        iccid=None
    )

    def __init__(self, bundle_code, country_code, version_token, payment_method=None, app_token=None,
                 promo_code=None, searched_countries=None, affiliate_id=None, referral_code=None, topup_code=None,
                 iccid=None, **defaults):
        super().__init__(
            **defaults,
            strategy=DictBuildStrategy(),
            bundle_code=bundle_code,
            country_code=country_code,
            version_token=version_token,
            payment_method=payment_method,
            app_token=app_token,
            promo_code=promo_code,
            searched_countries=searched_countries,
            affiliate_id=affiliate_id,
            referral_code=referral_code,
            topup_code=topup_code,
            iccid=iccid
        )


class StripeCallbackRequest(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        status=None,
        client_secret=None,
        stripe_request_id=None,
        event=None,

        email=None,
        livemode=None,
        name=None,
        balance=None,
        amount_received=None,
        outcome=None,
        paid=None,
        payment_intent=None,
        failure_balance_transaction=None,
        failure_code=None,
        failure_message=None,
        receipt_url=None,
        payment_method_types=None,
        customer=None,
        created=None,
        response=None,
        assert_list=None,

    )

    def __init__(self, **defaults):
        super().__init__(
            strategy=DictBuildStrategy(),
            **defaults,
        )


class VendorEnvironmentFactory:
    _VendorData = namedtuple('VendorData', ['vendor', 'bundles', 'profiles'])

    def __init__(self, vendor_name):
        self._vendor_name = vendor_name

        self._vendor_factory = VendorFactory(vendor_name=vendor_name)
        self._bundle_factories = {}
        self._profile_factories = {}

    def _validate_if_key_already_exists(self, tag):
        if tag in (self._profile_factories or self._bundle_factories):
            raise KeyError(f"key '{tag}' already used.")

    def add_bundle(self, tag,
                   bundle_code, bundle_vendor_code, bundle_vendor_name=None, supplier_vendor=None, **bundle_args):
        self._validate_if_key_already_exists(tag)
        self._bundle_factories[tag] = BundleFactory(
            bundle_code=bundle_code,
            bundle_vendor_code=bundle_vendor_code,
            bundle_vendor_name=bundle_vendor_name,
            supplier_vendor=supplier_vendor,
            vendor_name=self._vendor_name,
            **bundle_args
        )

        return self

    def add_profile(self, tag, iccid, bundle_code=None, **profile_args):
        self._validate_if_key_already_exists(tag)
        self._profile_factories[tag] = ProfilesFactory(
            bundle_code=bundle_code,
            iccid=iccid,
            vendor_name=self._vendor_name,
            **profile_args
        )
        return self

    def build(self):
        vendor = self._vendor_factory.build()

        # -------------------------------------------------
        # Handle Additional bundle logic
        # -------------------------------------------------
        bundles = {}
        for key, factory in self._bundle_factories.items():

            values_dict = {}

            b_request = factory.get_base_request()

            if b_request["supplier_vendor"] is None:
                values_dict["supplier_vendor"] = str(vendor.id)

            if b_request["bundle_vendor_name"] is None:
                values_dict["bundle_vendor_name"] = vendor.vendor_name

            bundles[key] = factory(**values_dict).build()

        # -------------------------------------------------
        # Handle Additional profile logic
        # -------------------------------------------------

        profiles = {key: factory.build() for key, factory in self._profile_factories.items()}

        # Use the namedtuple for the return value.
        return self._VendorData(vendor=vendor, bundles=bundles, profiles=profiles)


class HeaderBuilder:
    def __init__(self):
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    def add_auth(self, token):
        self.headers["Authorization"] = f"{token}"
        return self

    def content_type(self, content_type):
        self.headers["Content-Type"] = content_type
        return self

    def accept(self, accept):
        self.headers["Accept"] = accept
        return self

    def custom_header(self, key, value):
        self.headers[key] = value
        return self


class ApiResponse:
    def __init__(self, status):
        """if status = 2 it's an error response"""
        if status == 2:
            self.response = {
                "status": False,
                "data": None,
                "responseCode": 2,
                "title": "Failure",
                "message": ["error_message"],
                "developerMessage": "error_message",
                "totalCount": None
            }

    def set_field(self, field_name, value):
        self.response[field_name] = value
        return self

    def with_message(self, value):
        """To handle the cases where we might have more than one error option"""
        self.response["message"].append(value)
        return self

    def build(self):
        return self.response


@dataclass
class ApiResponseV1:
    status: bool
    data: dict
    responseCode: int
    title: str
    message: str
    developerMessage: str
    totalCount: str


@dataclass
class AssignStripeTestCaseFailure:
    bundle_code: str
    topup_code: str
    iccid: str
    country_code: str
    payment_method: int
    version_token: str
    promo_code: str
    searched_countries: []
    response: ApiResponseV1 = None

    @property
    def data(self):
        return json.dumps({
            "bundle_code": self.bundle_code, "version_token": self.version_token, "topup_code": self.topup_code,
            "payment_method": self.payment_method, "iccid": self.iccid, "country_code": self.country_code,
        })

    assert_list: list = field(default_factory=lambda: [])


class MontyResellerTopupResponseFactory(BaseDictFactoryBuilder):
    _base_request = StrictDict(
        additional_currency_code="SAR",
        developer_message="Operation Succcessful",
        iccid="iccid",
        message="Bundle Assigned Successfully",
        order_id="507f191e810c19729de860ea",
        remaining_wallet_balance=30,
        remaining_wallet_balance_in_additional_currency=30,
        reseller_id="507f191e810c19729de860ea",
        response_code="1",
        title="Success"
    )

    def __init__(self, **defaults):
        super().__init__(**defaults, strategy=DictBuildStrategy())

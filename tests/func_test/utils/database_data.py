import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from hashlib import md5

from app_models.ai_assistant_models import AdvancedSearch
from app_models.consumer_models import Bundles, Countries, Vendors, Profiles, TransactionLogs, RewardHistory, UserIccid, \
    UserBundleLog, CashbackHistory, category_names_, Regions
from app_models.main_models import BlockedEmails, EmailSettings, KeycloackSettings, OperatorList, Settings
from app_models.mobiles import AppRedeemCodeRecord, AppEmailVerification, AppVersionList, AppList, AppUserDetails, \
    vouchers_promo_codes, status, RedeemCodes, PromoRedeemLogs, app_platforms_
from app_models.reseller_models import Order_history, BundleData

from instance import consumer_config
from src.cipher import Crypt
from src.controllers.esimgo import generate_matching_id
from src.global_helpers.utils import generate_temp_otp
from tests.func_test.utils.object_factories.objects_factory import BundleFactory, ProfilesFactory, \
    AppVersionListFactory, VendorFactory


def encrypt_value(encryption_key, text_to_be_encrypted):
    encrypt_helper = Crypt(aes_key=encryption_key)
    return encrypt_helper.encrypt(text_to_be_encrypted)


def setup_system_settings_data():
    keycloak_setting = (
            KeycloackSettings.objects(op_name=consumer_config.operator_name).first() or KeycloackSettings(**{
        "op_name": consumer_config.operator_name, "client_id": "admin-cli",
        'client_key': "1234555555555555555555550",
        'releam_name': 'orange', 'admin_releam': "master", 'admin_key': '01'
    }).save())

    email_setting = (EmailSettings.objects(email="<EMAIL>").first() or EmailSettings(**{
        "email": "<EMAIL>", 'username': 'test', 'password': "test", 'smtp_server': "protocol.com",
        'smtp_port': 1
    }).save())

    settings = (Settings.objects(contact_email="<EMAIL>").first() or Settings(**{
        "contact_email": "<EMAIL>",
        "esim_email": "<EMAIL>",
        "merchant_key": "",
        "merchant_password": "",
        "fcm_registration": "AAAALNoLyt8:APA91bFfFmhytmTcFAjGBZKyud-68WBh4hgoJh-dcTG5Faz14SpepZEHk_cupAx8P0PNrDm-8khFXB6pNXuJ5vgtlyw_auua59PodaCiyl7qqGXFakiPf_j4uMg7soi20U13ZtSI9bbN",
        "whatsapp_misisdn": "447418375735",
        "client_keycloack_secret": "",
        "reward_amount_limit_usd": 20,
        "reward_per_purchase": 2,
        "reward_type_category": 1,
        "percentage_of_reward": 10,
        "purchase_threshold_for_reward": 3,
        "lng_version": 4,
        "marketing_email": "<EMAIL>",
        "referral_program_enabled": True,
        "minimum_allowed_bundle_price": 3.99,
        "limit_bundle": 10,
        "monitor_email": "<EMAIL>",
        "first_captcha_block_count": 1000,
        "orders_assigned_since_minutes": 1000,
        "orders_assigned_since_minutes_permanent_block": 1500,
        "second_captcha_block_count": 1000
    }).save())
    return (keycloak_setting, email_setting, settings)


def setup_app_managed_data():
    blocked_emails = (BlockedEmails.objects(emails=["<EMAIL>"]).first() or BlockedEmails(**{
        "emails": ["<EMAIL>"],
        "subdomain": ["blocked-subdomain.com"]
    }).save())

    country = (Countries.objects(iso3_code="LBN").first() or Countries(**{
        "country_name": "Lebanon",
        "iso2_code": "LB",
        "iso3_code": "LBN",
        "alternative_country": ["LBN"],
        "region_code": ["me"],
        "currency": "Lebanese Pound",
        "currency_symbol": "LBP",
        "zone_name": "Asia/Beirut"

    }).save())

    return (blocked_emails, country)


def setup_operator():
    # ------------------------------------------------------------------------------
    # App for Orange Operator
    # ------------------------------------------------------------------------------
    operator = (
            OperatorList.objects(op_name="Orange").first() or
            OperatorList(**{
                "cert_name": "test_file.pem",
                "create_date": datetime.utcnow(),
                "from_email": "<EMAIL>",
                "op_country": "Egypt",
                "op_fci": b"000",
                "op_name": "Orange",
                "profile_type": "Organization",
                "request_identifier": "Orange",
                "salt": b"01111",
                "sk_name": "OrangeB2c",
                "unverified_context": True,
            }).save())

    app = (
            AppList.objects(op_name=operator.op_name).first() or
            AppList(**{
                "op_name": operator.op_name, 'app_name': 'B2COrg', 'app_platform': app_platforms_[0],
                "update_date": datetime.utcnow()
            }).save())

    app_version = (
            AppVersionList.objects(version_token="2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE").first() or
            AppVersionListFactory(app_list=app,
                                  version_token="2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE").build()
    )

    # ------------------------------------------------------------------------------
    # App without An Operator
    # ------------------------------------------------------------------------------
    app_without_operator = (
            AppList.objects(op_name="NoOperator").first() or
            AppList(**{
                "op_name": "NoOperator", 'app_name': 'B2COrg', 'app_platform': app_platforms_[0],
                "update_date": datetime.utcnow()
            }).save())

    app_version_without_operator = (
            AppVersionList.objects(version_token="XZCXZC").first() or
            AppVersionListFactory(app_list=app_without_operator, version_token="XZCXZC").build()
    )

    # ------------------------------------------------------------------------------
    # App version without An Operator and an app
    # ------------------------------------------------------------------------------
    app_version_without_app_id = (
            AppVersionList.objects(version_token="ASDASDSAD").first() or
            AppVersionListFactory(app_list=None, version_token="ASDASDSAD").build()
    )

    return (operator, app, app_version, app_without_operator, app_version_without_operator, app_version_without_app_id)


def setup_user():
    app_email_verification = (
            AppEmailVerification.objects(user_email="<EMAIL>").first() or
            AppEmailVerification(**{"user_email": "<EMAIL>", "is_deleted": False}).save()
    )

    user = (
            AppUserDetails.objects(user_email=app_email_verification.user_email).first() or
            AppUserDetails(**{
                "user_token": "Token", "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE",
                "user_name": app_email_verification.user_email,
                "user_email": app_email_verification.user_email, "balance": 150, "fcm_token": "token firebase",
                "ipv4_address": "*******"
            }).save()
    )

    already_redeemed_app_email_verification = (
            AppEmailVerification.objects(user_email="<EMAIL>").first() or
            AppEmailVerification(**{"user_email": "<EMAIL>", "is_deleted": False}).save()
    )

    already_redeemed_user = (
            AppUserDetails.objects(user_email=already_redeemed_app_email_verification.user_email).first() or
            AppUserDetails(**{
                "user_token": "already_redeemed_user_token",
                "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE",
                "user_name": already_redeemed_app_email_verification.user_email,
                'user_email': already_redeemed_app_email_verification.user_email, "balance": 150,
                "fcm_token": "token firebase", "ipv4_address": "*******"
            }).save()
    )

    from_user = AppUserDetails(**{
        "user_token": "rich-Token", "version_token": "2.2.0", "user_name": "luffy",
        'user_email': "<EMAIL>", "balance": 1, "ipv4_address": "*******"
    }).save()

    return (user, app_email_verification, from_user)


def setup_vendors():
    # ----------------------------------------------------
    # setup Monty Mobile vendor
    # ----------------------------------------------------
    vendor_monty = VendorFactory(vendor_name="Monty Mobile",
                                 apply_inventory=False,
                                 supports_empty_profiles=True,
                                 has_constant_bundle_count=False,
                                 get_profile_from_vendor=False
                                 ).build()
    vendor_monty_profile = ProfilesFactory(vendor_name=vendor_monty.vendor_name,
                                           iccid="22222222222222222220").build()
    # ----------------------------------------------------
    # setup eSIMgo vendor
    # ----------------------------------------------------
    esimgo_vendor = VendorFactory(vendor_name="eSIMGo",
                                  apply_inventory=True,
                                  supports_empty_profiles=False,
                                  has_constant_bundle_count=False,
                                  get_profile_from_vendor=True,
                                  has_balance=True
                                  ).build()

    esimgo_vendor_profile = ProfilesFactory(vendor_name=esimgo_vendor.vendor_name,
                                            iccid="22245678901234567890").build()
    # ----------------------------------------------------
    # setup flexiroam vendor
    # ----------------------------------------------------
    flexi_vendor = VendorFactory(vendor_name="Flexiroam",
                                 apply_inventory=True,
                                 supports_empty_profiles=True,
                                 has_constant_bundle_count=True,
                                 get_profile_from_vendor=False
                                 ).build()

    flexi_profile = ProfilesFactory(vendor_name=flexi_vendor.vendor_name,
                                    iccid="12345678901234567890").build()

    flexi_free_profile = ProfilesFactory(vendor_name=flexi_vendor.vendor_name,
                                         iccid="22305670901234567890",
                                         sku="profile-id-unique-new-one",
                                         qr_code_value=None,
                                         availability="Free").build()

    flexiroam_profile = ProfilesFactory(vendor_name=flexi_vendor.vendor_name,
                                        iccid="22345678901234567890",
                                        qr_code_value=None,
                                        availability="NotFree").build()

    _ = ProfilesFactory(vendor_name=flexi_vendor.vendor_name, sku="profile-id-11-unique",
                        iccid="22214678901234567890").build()

    _ = ProfilesFactory(vendor_name=flexi_vendor.vendor_name, sku="profile-id-11-unique",
                        iccid="22224678901234567890").build()

    # ----------------------------------------------------
    # setup eSIMgo Mock vendor
    # ----------------------------------------------------
    esim_go_mock_vendor = VendorFactory(vendor_name="eSIMGoMock").build()

    # ----------------------------------------------------
    # setup Vodafone vendor
    # ----------------------------------------------------
    vodafone_vendor = VendorFactory(vendor_name="Vodafone").build()

    return (vendor_monty,
            vendor_monty_profile,
            esimgo_vendor,
            esimgo_vendor_profile,
            flexi_vendor,
            flexi_profile,
            flexi_free_profile,
            flexiroam_profile,
            esim_go_mock_vendor,
            vodafone_vendor
            )


@dataclass
class SetupResponse:
    """Alternative to the tuple headache in the old setup"""
    already_redeemed_user_code: AppRedeemCodeRecord = field(init=False)
    alternative_flexi_bundle_for_vodafone: Bundles = field(init=False)
    app: AppList = field(init=False)
    app_email_verification: AppEmailVerification = field(init=False)
    app_version: AppVersionList = field(init=False)
    app_version_without_app_id: AppVersionList = field(init=False)
    app_version_without_operator: AppVersionList = field(init=False)
    app_without_operator: AppVersionList = field(init=False)
    blocked_emails: BlockedEmails = field(init=False)
    bundle: Bundles = field(init=False)
    bundle_for_vendor_monty_with_insufficient_allocated_units: Bundles = field(init=False)
    bundle_inactive: Bundles = field(init=False)
    bundle_with_insufficient_allocated_units: Bundles = field(init=False)
    country: Countries = field(init=False)
    dark_continental_vodafone_bundle: Bundles = field(init=False)
    email_setting: EmailSettings = field(init=False)
    esim_go_bundle: Bundles = field(init=False)
    esim_go_bundle_usa: Bundles = field(init=False)
    esim_go_mock_bundle: Bundles = field(init=False)
    esim_go_mock_vendor: Vendors = field(init=False)
    esim_go_vendor: Vendors = field(init=False)
    esim_go_vendor_profiles: Profiles = field(init=False)
    flexi_bundle_without_profile: Profiles = field(init=False)
    free_for_user_profile_attached_to_bundle: Profiles = field(init=False)
    free_profile: Profiles = field(init=False)
    from_user: AppUserDetails = field(init=False)
    keycloak_setting: KeycloackSettings = field(init=False)
    not_consumed_bundle: Bundles = field(init=False)
    operator: OperatorList = field(init=False)
    order_history_2: Order_history = field(init=False)
    order_history_2_transaction_logs: TransactionLogs = field(init=False)
    profile: Profiles = field(init=False)
    reward: RewardHistory = field(init=False)
    un_used_profile: Profiles = field(init=False)
    unused_user_iccid: UserIccid = field(init=False)
    unused_user_iccid_for_unused_profile: UserIccid = field(init=False)
    user: AppUserDetails = field(init=False)
    user_bundle_log: UserBundleLog = field(init=False)
    vendor: Vendors = field(init=False)
    vendor_monty: Vendors = field(init=False)
    vendor_monty_profiles: Profiles = field(init=False)
    vodafone_bundle: Bundles = field(init=False)
    vodafone_vendor: Vendors = field(init=False)
    flexi_bundle_one_profile_left: Bundles = field(init=False)
    flexi_bundle_above_threshold: Bundles = field(init=False)
    flexi_bundle_below_threshold: Bundles = field(init=False)
    esimgo_bundle_one_profile_left: Bundles = field(init=False)
    esimgo_bundle_above_threshold: Bundles = field(init=False)
    esimgo_bundle_below_threshold: Bundles = field(init=False)

    monty_reseller_vendor: Vendors = field(init=False)
    monty_reseller_normal_bundle: Bundles = field(init=False)
    monty_reseller_successful_order_history: Order_history = field(init=False)
    monty_reseller_profile: Profiles = field(init=False)


# NEWER VERSION of the setup() method
def setup_db() -> SetupResponse:
    _db: SetupResponse = SetupResponse()

    _db.keycloak_setting, _db.email_setting, _db.settings = setup_system_settings_data()

    _db.blocked_emails, _db.country = setup_app_managed_data()

    _db.operator, _db.app, _db.app_version, _db.app_without_operator, _db.app_version_without_operator, _db.app_version_without_app_id = setup_operator()

    _db.user, _db.app_email_verification, _db.from_user = setup_user()

    (_db.vendor_monty,
     _db.vendor_monty_profiles,
     _db.esim_go_vendor,
     _db.esim_go_vendor_profiles,
     _db.vendor,
     _db.profile,
     _db.free_profile,
     _db.un_used_profile,
     _db.esim_go_mock_vendor,
     _db.vodafone_vendor) = setup_vendors()

    _db.bundle_for_vendor_monty_with_insufficient_allocated_units = BundleFactory(
        bundle_code="monty-vendor-insufficient-allocated-units-bundle-code",
        vendor_name=_db.vendor_monty.vendor_name,
        bundle_vendor_name=_db.vendor_monty.vendor_name,
        bundle_vendor_code="monty-vendor-insufficient-allocated-units-bundle-vendor-code",
        supplier_vendor=str(_db.vendor_monty.id),
        allocated_unit=20,
        consumed_unit=21
    ).build()

    _db.bundle_with_insufficient_allocated_units = BundleFactory(
        bundle_code="insufficient-allocated-units-bundle-code",
        bundle_vendor_code="insufficient-allocated-units-bundle-vendor-code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=20,
        consumed_unit=20
    ).build()

    _db.bundle_inactive = BundleFactory(
        bundle_category=category_names_[0],
        bundle_code="100G-unreleased",
        bundle_marketing_name="fastest-very-realistic-G",
        bundle_name="100G",
        bundle_vendor_code="100G-KI-FA-01",
        bundle_vendor_name=_db.vendor.vendor_name,
        is_active=False,
        retail_price=1,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vendor.vendor_name,
    ).build()

    _db.bundle = BundleFactory(
        bundle_code="4G-released",
        bundle_marketing_name="fastest-4G",
        bundle_name="4G",
        bundle_vendor_code="4G-KI-FA-01",
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vendor.vendor_name,
        allocated_unit=1000,
        retail_price=1,
    ).build()

    _db.free_for_user_profile_attached_to_bundle = ProfilesFactory(
        bundle_code=_db.bundle.bundle_code,
        vendor_name=_db.vendor.vendor_name, iccid="123456789",
        availability="Free",
        expiry_date=datetime.utcnow().replace(
            year=datetime.utcnow().year + 2),
        plan_uid="plan_uid_1234"
    ).build()

    _db.flexi_bundle_without_profile = BundleFactory(
        bundle_code="empty-bundle",
        bundle_marketing_name="fastest-4G",
        bundle_name="flexi-empty-name",
        bundle_vendor_code=f"{_db.vendor.id}",
        bundle_vendor_name=_db.vendor.vendor_name,
        profile_names="flexi-te",
        retail_price=1,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vendor.vendor_name,
    ).build()

    _db.not_consumed_bundle = BundleFactory(
        bundle_code="5G-released",
        bundle_marketing_name="fastest-4G",
        bundle_name="5G",
        bundle_vendor_code=f"{_db.vendor.id}-not-consumed",
        bundle_vendor_name=_db.vendor.vendor_name,
        consumed_unit=1,
        country_list=["UAE"],
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vendor.vendor_name,
        allocated_unit=30,
        data_amount=30,
        data_unit="GB",
        retail_price=1,
    ).build()

    _db.esim_go_mock_bundle = BundleFactory(
        bundle_code="5G-Go",
        bundle_marketing_name="fastest-4G",
        bundle_name="4G",
        bundle_vendor_code=f"{_db.vendor.id}",
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.esim_go_mock_vendor.vendor_name,
        retail_price=1,
    ).build()

    _db.vodafone_bundle = BundleFactory(
        bundle_code="5G-Vodafone",
        bundle_marketing_name="fastest-4G",
        bundle_name="5G",
        bundle_vendor_code="5G-Vodafone",
        bundle_vendor_name=_db.vendor.vendor_name,
        country_code_list=["FR"],
        country_list=["FR"],
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vodafone_vendor.vendor_name,
        allocated_unit=30,
        retail_price=1,
        rate_revenue=1
    ).build()

    _db.alternative_flexi_bundle_for_vodafone = BundleFactory(
        bundle_code="5G-releasedAlternative",
        bundle_marketing_name="fastest-4G",
        bundle_name="5G",
        bundle_vendor_code="5G-releasedAlternative",
        bundle_vendor_name=_db.vendor.vendor_name,
        country_code_list=["FR"],
        country_list=["FR"],
        retail_price=2,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.vendor.vendor_name,
    ).build()

    _db.esim_go_bundle = BundleFactory(
        bundle_code="2G-released",
        bundle_marketing_name="fastest-4G",
        bundle_name="2G",
        bundle_vendor_code="2G-released",
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.esim_go_vendor.vendor_name,
        allocated_unit=30,
    ).build()

    _db.esim_go_bundle_usa = BundleFactory(
        bundle_code="2G-fast",
        bundle_marketing_name="fastest-4G",
        bundle_name="2G",
        bundle_vendor_code="2G-fast",
        bundle_vendor_name=_db.vendor.vendor_name,
        country_list=["USA"],
        supplier_vendor=f"{_db.vendor.id}",
        vendor_name=_db.esim_go_vendor.vendor_name,
        allocated_unit=30,
        retail_price=1,
    ).build()

    _db.dark_continental_vodafone_bundle = BundleFactory(
        bundle_code="5G-DK-Vodafone",
        bundle_vendor_code="5G-DK-Vodafone",
        bundle_marketing_name="fastest-4G",
        bundle_name="5G",
        bundle_vendor_name=_db.vendor.vendor_name,
        country_list=["DK"],
        supplier_vendor=str(_db.vendor.id),
        vendor_name=_db.vodafone_vendor.vendor_name,
        allocated_unit=20,
        consumed_unit=20,
        retail_price=1
    ).build()

    _db.unused_user_iccid_for_unused_profile = UserIccid(**{
        "email": _db.user.user_email, "status": "unused", "datetime": datetime.now(),
        "bundle_code": _db.bundle.bundle_code,
        "iccid": _db.un_used_profile.iccid, "plan_uid": "123", "payment_otp": "123456-00"
    }).save()

    _db.unused_user_iccid = UserIccid(**{
        "email": _db.user.user_email, "status": "unused", "datetime": datetime.now(),
        "bundle_code": _db.bundle.bundle_code,
        "iccid": _db.profile.iccid, "plan_uid": "123", "payment_otp": "123456"
    }).save()

    UserIccid(**{
        "email": _db.user.user_email, "status": "unused", "datetime": datetime.now(),
        "bundle_code": _db.esim_go_bundle.bundle_code,
        "iccid": "22245678901234567890", "plan_uid": "123", "payment_otp": "123456"
    }).save()

    UserIccid(**{
        "email": _db.user.user_email, "status": "unused", "datetime": datetime.now(),
        "bundle_code": _db.not_consumed_bundle.bundle_code,
        "iccid": "22214678901234567890", "plan_uid": "123", "payment_otp": "123456"
    }).save()

    UserIccid(**{
        "email": _db.user.user_email, "status": "unused", "datetime": datetime.now(),
        "bundle_code": _db.not_consumed_bundle.bundle_code,
        "iccid": "22224678901234567890", "plan_uid": "123", "payment_otp": "123456"
    }).save()

    _db.user_bundle_log = UserBundleLog(**{
        "email": _db.user.user_email, "otp": _db.unused_user_iccid.payment_otp, "payment_status": True,
        "payment_topup": True,
        "bundle": f"{_db.bundle.id}", "bundle_code": _db.bundle.bundle_code, "country_code": "EG",
        "topup_code": _db.bundle.bundle_code,
        "payment_id": "plink_1N3Gzu2eZvKYlo2Cm3pKBbku",
    }).save()

    _ = Order_history(
        **{"client_email": _db.user.user_email, "order_status": "Successful", "bundle_code": _db.bundle.bundle_code,
           "bundle_date": _db.bundle, "order_type": "BuyBundle"})

    _db.order_history_2 = Order_history(
        **{
            "order_number": "order_history_2",
            "client_email": _db.user.user_email,
            "order_status": "Pending",
            "bundle_code": _db.bundle.bundle_code,
            "bundle_data": BundleData(**{
                "bundle_marketing_name": _db.bundle.bundle_marketing_name,
                "coverage": "Global",
                "bundle_duration": _db.bundle.bundle_duration,
                "data_amount": _db.bundle.data_amount,
                "data_unit": _db.bundle.data_unit,
                "bundle_name": _db.bundle.bundle_name,
                "bundle_code": _db.bundle.bundle_code
            }),
            "order_type": "BuyBundle",
            "stripe_client_secret": md5("unhashed_client_secret".encode()).hexdigest(),
            "payment_intent_secret": md5("unhashed_stripe_request_id".encode()).hexdigest(),
            "payment_category": 1,  # credit_card
            "iccid": _db.free_for_user_profile_attached_to_bundle.iccid,
            "cashback_rewarded": False
        }
    ).save()

    _db.order_history_2_transaction_logs = TransactionLogs(**{
        "order_number": _db.order_history_2.order_number
    }).save

    _db.reward = RewardHistory(**{
        "referral_date": datetime.utcnow(), "from_user_email": _db.from_user.user_email,
        "purchase_date": datetime.utcnow(), "order_number": "", "rewarded_amount": 5,
        "to_user_email": _db.user.user_email
    }).save()

    PromoRedeemLogs(**{
        # this customer email comes from webhook itself
        "username": _db.app_email_verification.user_email,
        "promo_code": "EID",
        "datetime": datetime.utcnow,
        "amount": 1,
        "currency_code": "USD",
        "discount": 1
    }).save()

    for _ in range(_db.settings.purchase_threshold_for_reward - 1):
        CashbackHistory(**{
            "user_email": _db.user.user_email,
            "purchase_date": datetime.utcnow,
            "bundle_code": _db.bundle.bundle_code,
            "order_number": f"fullmetal_{uuid.uuid4()}",
            "rewarded_amount": 0
        }).save()

    _db.already_redeemed_user_code = AppRedeemCodeRecord(**{
        "username": "<EMAIL>",
        "create_date": datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0),
        "count": 1,
        "cooldown_date": datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0),
    }).save()

    RedeemCodes(**{
        "redeem_code": encrypt_value(encryption_key=consumer_config.promo_code_key,
                                     text_to_be_encrypted="valid-redeem-code"),
        "expiry_datetime": datetime.utcnow() + timedelta(days=5),
        "category": vouchers_promo_codes[1],
        "status": status[0]
    }).save()

    _db.flexi_bundle_one_profile_left = BundleFactory(
        bundle_code="flexi_bundle_one_profile_left",
        bundle_vendor_code="flexi_bundle_one_profile_left__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=21,
        consumed_unit=20,
        profile_names="Yaman"
    ).build()
    generate_dummy_profile(count=1, bundle_codes=[_db.flexi_bundle_one_profile_left.bundle_code])
    generate_dummy_profile(count=10,
                           profile_names=_db.flexi_bundle_one_profile_left.profile_names,
                           vendor_name=_db.flexi_bundle_one_profile_left.vendor_name,
                           status=True)

    _db.flexi_bundle_above_threshold = BundleFactory(
        bundle_code="flexi_bundle_many_profiles_left",
        bundle_vendor_code="flexi_bundle_many_profiles_left__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=20,
        consumed_unit=0,
        profile_names="Yaman"
    ).build()
    generate_dummy_profile(count=_db.settings.limit_bundle * 2,
                           bundle_codes=[_db.flexi_bundle_above_threshold.bundle_code])

    _db.flexi_bundle_below_threshold = BundleFactory(
        bundle_code="flexi_bundle_below_threshold",
        bundle_vendor_code="flexi_bundle_below_threshold__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=20,
        consumed_unit=11,
        profile_names="Yaman"
    ).build()

    generate_dummy_profile(count=_db.settings.limit_bundle - 1,
                           bundle_codes=[_db.flexi_bundle_below_threshold.bundle_code])

    _db.esimgo_bundle_one_profile_left = BundleFactory(
        bundle_code="esimgo_bundle_one_profile_left",
        bundle_vendor_code="esimgo_bundle_one_profile_left__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=21,
        consumed_unit=20
    ).build()
    generate_dummy_profile(count=1, bundle_codes=[_db.esimgo_bundle_one_profile_left.bundle_code])

    _db.esimgo_bundle_above_threshold = BundleFactory(
        bundle_code="esimgo_bundle_above_threshold",
        bundle_vendor_code="esimgo_bundle_above_threshold__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=20,
        consumed_unit=0
    ).build()
    generate_dummy_profile(count=_db.settings.limit_bundle * 2,
                           bundle_codes=[_db.esimgo_bundle_above_threshold.bundle_code])

    _db.esimgo_bundle_below_threshold = BundleFactory(
        bundle_code="esimgo_bundle_below_threshold",
        bundle_vendor_code="esimgo_bundle_below_threshold__vendor_code",
        vendor_name=_db.vendor.vendor_name,
        bundle_vendor_name=_db.vendor.vendor_name,
        supplier_vendor=str(_db.vendor.id),
        allocated_unit=20,
        consumed_unit=11
    ).build()

    generate_dummy_profile(count=_db.settings.limit_bundle - 1,
                           bundle_codes=[_db.esimgo_bundle_below_threshold.bundle_code])

    AdvancedSearch(**{
        "category": "destination",
        "category_code": "single_country",
        "category_value": "",
        "min": 0.0,
        "max": 0.0,
        "selectable": True,
        "ranged": False
    }).save()

    AdvancedSearch(**{
        "category": "destination",
        "category_code": "region",
        "category_value": "",
        "min": 0.0,
        "max": 0.0,
        "selectable": True,
        "ranged": False
    }).save()

    AdvancedSearch(**{
        "category": "destination",
        "category_code": "multiple_countries",
        "category_value": "",
        "min": 0.0,
        "max": 0.0,
        "selectable": True,
        "ranged": False
    }).save()

    AdvancedSearch(**{
        "category": "duration",
        "category_code": "choice_1",
        "category_value": "",
        "min": 1.0,
        "max": 4.0,
        "selectable": True,
        "ranged": True
    }).save()

    AdvancedSearch(**{
        "category": "duration",
        "category_code": "choice_2",
        "category_value": "",
        "min": 0,
        "max": -1,
        "selectable": True,
        "ranged": True
    }).save()

    AdvancedSearch(**{
        "category": "duration",
        "category_code": "choice_3",
        "category_value": "",
        "min": 0.0,
        "max": 100.0,
        "selectable": True,
        "ranged": True
    }).save()

    AdvancedSearch(**{
        "category": "duration",
        "category_code": "choice_4",
        "category_value": "",
        "min": 13.0,
        "max": -1,
        "selectable": True,
        "ranged": True
    }).save()

    AdvancedSearch(**{
        "category": "consumption",
        "category_code": "low",
        "category_value": "",
        "min": 0.1,
        "max": 100,
        "selectable": True,
        "ranged": True,
        "display_name": "low",
        "display_name_translation": [
            {
                "language_code": "en",
                "display_name": "low"
            },
            {
                "language_code": "ar",
                "display_name": "ضعيف"
            }
        ]
    }).save()

    AdvancedSearch(**{
        "category": "consumption",
        "category_code": "moderate",
        "category_value": "",
        "min": 11.0,
        "max": 20.0,
        "selectable": True,
        "ranged": True,
        "display_name": "moderate",
        "display_name_translation": [
            {
                "language_code": "en",
                "display_name": "moderate"
            },
            {
                "language_code": "ar",
                "display_name": "متوسط"
            }
        ]
    }).save()

    AdvancedSearch(**{
        "category": "consumption",
        "category_code": "high",
        "category_value": "",
        "min": 0.0,
        "max": 300.0,
        "selectable": True,
        "ranged": True,
        "display_name": "high",
        "display_name_translation": [
            {
                "language_code": "en",
                "display_name": "high"
            },
            {
                "language_code": "ar",
                "display_name": "قوي"
            }
        ]
    }).save()

    Regions(**{
        "region_code": "an",
        "region_name": "Antarctica",
        "country_code_list": [],
        "zone_name": "Antarctica-esim"
    }).save()

    Regions(
        **{
            "region_code": "me",
            "region_name": "Middle East",
            "country_code_list": ["SAU", "PSE", "TUR"],
            "zone_name": "Middle-East-esim"
        }).save()

    Regions(
        **{
            "region_code": "af",
            "region_name": "Africa",
            "country_code_list": ["ZAF", "NGA", "KEN"],
            "zone_name": "Africa-esim"
        }).save()

    Regions(
        **{
            "region_code": "sa",
            "region_name": "South America",
            "country_code_list": ["BRA", "ARG", "CHL"],
            "zone_name": "South-America-esim"
        }).save()

    Regions(
        **{
            "region_code": "as",
            "region_name": "Asia",
            "country_code_list": ["CHN", "JPN", "IND"],
            "zone_name": "Asia-esim"
        }).save()

    Regions(
        **{
            "region_code": "eu",
            "region_name": "Europe",
            "country_code_list": ["GBR", "FRA", "DEU"],
            "zone_name": "Europe-esim"
        }).save()

    Regions(
        **{
            "region_code": "na",
            "region_name": "North America",
            "country_code_list": ["USA", "CAN", "MEX"],
            "zone_name": "North-America-esim"
        }).save()
    _db.monty_reseller_vendor = VendorFactory(vendor_name="Monty Reseller",
                                              apply_inventory=False,
                                              supports_empty_profiles=False,
                                              has_constant_bundle_count=False,
                                              get_profile_from_vendor=True
                                              ).build()

    _db.monty_reseller_normal_bundle = BundleFactory(bundle_code="PLAN_IND_200MB_30D",
                                                     bundle_vendor_code="PLAN_IND_200MB_30D__VENDOR_CODE",
                                                     bundle_vendor_name="PLAN_IND_200MB_30D__VENDOR_CODE",
                                                     supplier_vendor="lala",
                                                     vendor_name=_db.monty_reseller_vendor.vendor_name,
                                                     bundle_duration=30,
                                                     bundle_category="country",
                                                     bundle_marketing_name="Monty Mobile - 200MB 1Month India (Trial - Profile J)",
                                                     bundle_name="India200MB",
                                                     country_code_list=["IND"],
                                                     country_list=["India"],
                                                     create_datetime=datetime.utcnow(),
                                                     currency_code="USD",
                                                     data_amount=200,
                                                     data_unit="MB",
                                                     deleted=False,
                                                     is_active=True,
                                                     retail_price=5,
                                                     allocated_unit=10,
                                                     consumed_unit=0,
                                                     unlimited=False,
                                                     validity_amount="30").build()

    _db.monty_reseller_profile = ProfilesFactory(
        vendor_name="Monty Reseller",
        iccid="12345678903213123123",
        expiry_date=datetime.utcnow() + timedelta(days=1000)
    ).build()
    _db.monty_reseller_successful_order_history = Order_history(**{
        "client_email": _db.user.user_email,
        "order_status": "Successful",
        "payment_date": datetime.utcnow(),
        "date_created": datetime.utcnow(),
        "order_number": str(uuid.uuid4()),
        "bundle_code": _db.monty_reseller_normal_bundle.bundle_code,
        "order_type": "BuyBundle",
        "bundle_marketing_name": _db.monty_reseller_normal_bundle.bundle_marketing_name,
        "country_name": "country_1",
        "reseller_type": "subscriber",
        "iccid": _db.monty_reseller_profile.iccid,
        "expiry_date": datetime.utcnow() + timedelta(days=30),
        "plan_status": "Active",
        "bundle_data": {
            "vendor_name": _db.monty_reseller_normal_bundle.vendor_name,
            "bundle_code": _db.monty_reseller_normal_bundle.bundle_code,
            "bundle_name": _db.monty_reseller_normal_bundle.bundle_name,
            "bundle_marketing_name": _db.monty_reseller_normal_bundle.bundle_marketing_name,
            "bundle_duration": _db.monty_reseller_normal_bundle.bundle_duration,
            "retail_price": _db.monty_reseller_normal_bundle.retail_price,
            "currency_code": _db.monty_reseller_normal_bundle.currency_code,
            "data_amount": _db.monty_reseller_normal_bundle.data_amount,
            "data_unit": _db.monty_reseller_normal_bundle.data_unit
        }
    }).save()

    return _db


def generate_dummy_profile(
        count: int = 5,
        bundle_codes: list = None,
        profile_names: str = "",
        vendor_name: str = "",
        status: bool = False
):
    if bundle_codes is None:
        bundle_codes = []
    iccid_list = []
    if not bundle_codes and not (profile_names or vendor_name):
        raise Exception("profile_names and vendor_name should be provided when no bundle_code is passed")
    if bundle_codes:
        for bundle in Bundles.objects(bundle_code__in=bundle_codes):
            for _ in range(count):
                matching_id = f"{generate_matching_id()}"
                iccid = f"{generate_temp_otp(20)}"
                smdp_address = "rsp.nottherealtruphone.com"
                qr_code_value = f"LPA:1${smdp_address}${matching_id}"
                profile: Profiles = Profiles(iccid=iccid,
                                             sku=iccid,
                                             smdp_address=smdp_address,
                                             matching_id=matching_id,
                                             qr_code_value=qr_code_value,
                                             profile_names=bundle.profile_names,
                                             vendor_name=bundle.vendor_name,
                                             bundle_code=bundle.bundle_code,
                                             plan_uid=f"{uuid.uuid4()}",
                                             expiry_date=datetime.utcnow() + timedelta(days=365),
                                             status=True,
                                             ).save()
                iccid_list.append(iccid)
        return iccid_list

    for _ in range(count):
        matching_id = f"{generate_matching_id()}"
        iccid = f"{generate_temp_otp(20)}"
        smdp_address = "rsp.nottherealtruphone.com"
        qr_code_value = f"LPA:1${smdp_address}${matching_id}"
        profile: Profiles = Profiles(iccid=iccid,
                                     sku=iccid,
                                     profile_names=profile_names,
                                     vendor_name=vendor_name,
                                     smdp_address=smdp_address,
                                     matching_id=matching_id,
                                     qr_code_value=qr_code_value,
                                     status=status
                                     ).save()
        iccid_list.append(iccid)

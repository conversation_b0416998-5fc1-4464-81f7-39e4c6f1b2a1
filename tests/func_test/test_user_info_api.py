import unittest
from unittest import mock

import app_models.mobiles
from app_models.main_models import Settings
from app_models.mobiles import AppEmailVerification
from app_models.mobiles import AppUserDetails

from src import app
from src.global_helpers.phone_utils import process_phone_number
from tests.func_test.utils.object_factories.mocked_keycloak_open_id import MockedKeycloakOpenId, \
    MockedKeycloakOpenIdFactory
from tests.setup_test_data_for_auth import setup
from src.global_helpers.utils import is_nonempty_nonspace


class UserInfoTestCase(unittest.TestCase):
    MOCKED_KEYCLOAK_SERVER_URL = 'http://localhost:8080'
    MOCKED_KEYCLOAK_CLIENT_ID = 'admin-cli'
    MOCKED_KEYCLOAK_REALM_NAME = 'master'
    MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = '0yyttrewqfssqddffgnbvcxww0020gfs'

    def setUp(self):
        self.client = app.test_client()
        self.app, self.app_version, self.app_version_without_app_id, self.app_without_operator, \
            self.app_version_without_operator, self.operator, self.app_email_verification, self.user, \
            self.keycloak_setting, self.email_setting = setup()
        self.user_info_url = "/v2/user-info"

    def tearDown(self):
        self.app.delete()
        self.app_version.delete()
        self.app_version_without_app_id.delete()
        self.app_without_operator.delete()
        self.app_version_without_operator.delete()
        self.operator.delete()
        AppEmailVerification.objects.delete()
        self.user.delete()
        self.keycloak_setting.delete()
        self.email_setting.delete()
        Settings.objects(contact_email="<EMAIL>").delete()

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                sub="248289761001", name="zoro Shimotsuki", given_name="zoro", family_name="Shimotsuki",
                preferred_username="<EMAIL>", role_name="normal_user", email="<EMAIL>",
                picture="http://example.com/janedoe/me.jpg", history_id="history_id",
            ).build()
        ]
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                given_name='zoro', family_name='Shimotsuki', preferred_username='zoro',
            ).build()
        ]
    )
    def test_success_normal_user_flow_also_we_found_user_in_our_db(self, *args):
        """
            Flow:
            user try to log in and his role is normal_user, and we can find user in out db using his email
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.user_info_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json["data"]["balance"], self.user.balance)
        self.assertEqual(response.json["data"]["currency_code"], 'USD')
        self.assertEqual(response.json["data"]["email"], self.user.user_email)
        self.assertEqual(response.json["data"]["is_verified"], True)
        self.assertEqual(response.json["data"]["role_name"], 1)
        self.assertEqual(response.json["data"]["user_token"], self.user.user_token)
        self.assertEqual(response.json["developerMessage"], "")
        self.assertEqual(response.json["message"], "action done")
        self.assertEqual(response.json["responseCode"], 1)
        self.assertEqual(response.json["status"], True)
        self.assertEqual(response.json["title"], "Success")

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                name="zoro Shimotsuki", given_name="zoro", family_name="Shimotsuki",
                preferred_username="<EMAIL>", email="<EMAIL>",
                picture="http://example.com/janedoe/me.jpg", history_id="history_id",

            ).build()
        ]
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                given_name='zoro', family_name='Shimotsuki', preferred_username='<EMAIL>',
                email='<EMAIL>',
            ).build()
        ]
    )
    def test_failure_normal_user_flow_also_we_cant_found_user_in_our_db(self, *args):
        """
            Flow:
            user try to log in and his role is normal_user, but we can't find user in out db using his email
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.user_info_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json["data"], {})
        self.assertEqual(response.json["developerMessage"], "Failed")
        self.assertEqual(response.json["message"], "Operation Failed")
        self.assertEqual(response.json["responseCode"], 2)
        self.assertEqual(response.json["status"], False)
        self.assertEqual(response.json["title"], "Failure")

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                name="zoro Shimotsuki", given_name="zoro", family_name="Shimotsuki",
                role_name="limited_access", picture="http://example.com/janedoe/me.jpg",
                history_id="history_id", preferred_username="<EMAIL>",
            ).build()
        ]
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                given_name='zoro', family_name='Shimotsuki', preferred_username='zoro'
            ).build()
        ]
    )
    def test_success_limited_access_user_with_email_verification_record_flow(self, *args):
        """
            Flow:
            user pass the keycloak validation, but we found out his role is limited access and there is
            AppEmailVerification for this user
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.user_info_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json["data"]["email"], self.user.user_email)
        self.assertEqual(response.json["data"]["role_name"], 2)
        self.assertEqual(response.json["developerMessage"], "")
        self.assertEqual(response.json["message"], "action done")
        self.assertEqual(response.json["responseCode"], 1)
        self.assertEqual(response.json["status"], True)
        self.assertEqual(response.json["title"], "Success")

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                sub="248289761001",
                name="zoro Shimotsuki",
                given_name="zoro",
                family_name="Shimotsuki",
                preferred_username="<EMAIL>",
                role_name="limited_access",
                email="<EMAIL>",
                picture="http://example.com/janedoe/me.jpg",
                history_id="history_id",
            ).build()
        ]
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                given_name='zoro', family_name='Shimotsuki', preferred_username='<EMAIL>',
                username='<EMAIL>'
            ).build()
        ]
    )
    def test_failure_limited_access_user_without_email_verification_record_flow(self, *args):
        """
            Flow:
            user pass the keycloak validation, but we found out his role is limited access and there is no
            AppEmailVerification for this user
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.user_info_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json["data"], {})
        self.assertEqual(response.json["developerMessage"], "Failed")
        self.assertEqual(response.json["message"], "Operation Failed")
        self.assertEqual(response.json["responseCode"], 2)
        self.assertEqual(response.json["status"], False)
        self.assertEqual(response.json["title"], "Failure")

    def test_failure_keycloak_is_reachable_flow(self):
        """
            Flow
            user not pass the keycloak validation due to
            keycloak not reachable Realm does not exist
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.user_info_url, headers=headers)
        self.assertEqual(response.status_code, 401)
        self.assertEqual(response.json["data"], {})
        self.assertEqual(response.json["responseCode"], 2)
        self.assertEqual(response.json["totalCount"], None)
        self.assertEqual(response.json["status"], False)

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        return_value=
        MockedKeycloakOpenIdFactory().introspect(
            name="zoro Shimotsuki", given_name="zoro", family_name="Shimotsuki",
            preferred_username="<EMAIL>", picture="http://example.com/janedoe/me.jpg", history_id="history_id"
        ).build()
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        return_value=
        MockedKeycloakOpenIdFactory().introspect(
            given_name='zoro', family_name='Shimotsuki', preferred_username='zoro'
        ).build()

    )
    def test_happy_path_where_get_user_info_add_referral_code_when_not_available(self, *args):

        invalid_cases = [
            (0, "Field Does not Exist",),
            (1, "Field Exists but is None"),
            (2, "Field Exists but is Whitespace"),
            (3, "Field Exists but is Blank")
        ]

        # ARRANGE
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }

        for id, title in invalid_cases:
            with self.subTest(title):
                # ARRANGE
                if id == 0:
                    AppUserDetails.objects(user_email="<EMAIL>").update_one(unset__referral_code=1)
                elif id == 1:
                    AppUserDetails.objects(user_email="<EMAIL>").update_one(set__referral_code=None)
                elif id == 2:
                    AppUserDetails.objects(user_email="<EMAIL>").update_one(set__referral_code="  ")
                elif id == 3:
                    AppUserDetails.objects(user_email="<EMAIL>").update_one(set__referral_code="")

                # ACT
                actual_response = self.client.get(self.user_info_url, headers=headers)

                # ASSERT
                self.assertTrue(is_nonempty_nonspace(actual_response.json["data"]["referral_code"]))

                # ASSERT DB
                user = AppUserDetails.objects(user_email="<EMAIL>").first()
                self.assertTrue(is_nonempty_nonspace(user.referral_code))

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                name="zoro Shimotsuki", given_name="zoro", family_name="Shimotsuki",
                picture="http://example.com/janedoe/me.jpg", history_id="history_id",
            ).build()
        ]
    )
    @mock.patch(
        "src.controllers.auth.KeycloakOpenID",
        side_effect=[
            MockedKeycloakOpenIdFactory().introspect(
                given_name='zoro', family_name='Shimotsuki', preferred_username='zoro',
            ).build()
        ]
    )
    def test_happy_path_where_get_user_info_does_not_add_a_referral_code_when_user_has_one(self, *args):

        # ARRANGE
        AppUserDetails.objects(user_email="<EMAIL>").update_one(set__referral_code="referral_code")

        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }

        # ACT
        actual_response = self.client.get(self.user_info_url, headers=headers)

        # ASSERT
        self.assertTrue(actual_response.json["data"]["referral_code"])
        self.assertEqual(actual_response.json["data"]["referral_code"], "referral_code")

        # ASSERT DB
        user = AppUserDetails.objects(user_email="<EMAIL>").first()
        self.assertTrue(is_nonempty_nonspace(user.referral_code))
        self.assertEqual(user.referral_code, "referral_code")

    def test_update_user_info_happy_path(self):
        # ARRANGE
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }

        mocked_keycloak_service = mock.patch("src.global_helpers.wrappers.KeycloakOpenID",
                                             side_effect=[
                                                 MockedKeycloakOpenIdFactory().introspect(**{
                                                     "auth_time": **********,
                                                     "aud": "account",
                                                     "azp": "realm-1-client-1",
                                                     "allowed-origins": [
                                                         "/*"
                                                     ],
                                                     "realm_access": {
                                                         "roles": [
                                                             "offline_access",
                                                             "uma_authorization",
                                                             "default-roles-realm-1"
                                                         ]
                                                     },
                                                     "scope": "profile email openid",
                                                     "preferred_username": "<EMAIL>",
                                                 }).build()

                                             ])

        valid_payload_body_list = [
            (
                'valid full data',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the country_code and msisdn while still returning them in the response, form 1(is empty)',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '',
                 'expected_msisdn': '', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "",
                    "msisdn": ""
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the country_code and msisdn while still returning them in the response, form 2(is None)',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '',
                 'expected_msisdn': '', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": None,
                    "msisdn": None
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the first_name while still returning them in the response, form 1(is empty)',
                {'expected_first_name': '', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the first_name while still returning them in the response, form 1(is None)',
                {'expected_first_name': '', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": None,
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the last_name while still returning them in the response, form 1(is empty)',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': '', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "",
                    "country_code": "1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid full data while signaling you want to delete the last_name while still returning them in the response, form 1(is None)',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': '', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": None,
                    "country_code": "1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid country code form with "+" sign international identifier',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "+1",
                    "msisdn": "**********"
                },
                False, None,
            ),
            (
                'valid msisdn form with symbols and stuff',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "+[1]",
                    "msisdn": "[202]-(555)-<0123>"
                },
                False, None,
            ),
            (
                'valid partial data 1',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': True},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                },
                True, "OP-1"
            ),
            (
                'valid partial data 2',
                {'expected_first_name': 'Lorem ipsum', 'expected_last_name': 'Shoe Shine', 'expected_country_code': '1',
                 'expected_msisdn': '**********', 'expected_is_newsletter_subscribed': False},
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": False,
                },
                True, "OP-1"
            ),
        ]

        # ACT: Param Test
        for test_title, expected_result_dict, request_payload_dict, additional_operation, operation_identifier in valid_payload_body_list:

            if additional_operation is True and operation_identifier is not None and str(operation_identifier).replace(
                    " ", "") != "":
                if operation_identifier == "OP-1":
                    AppUserDetails.objects(email=request_payload_dict["email"]).update(**{
                        "country_code": expected_result_dict["expected_country_code"],
                        "msisdn": expected_result_dict["expected_msisdn"],
                    })
                elif operation_identifier == "OP-2":
                    AppUserDetails.objects(email=request_payload_dict["email"]).update(**{
                        "first_name": expected_result_dict["expected_first_name"],
                        "last_name": expected_result_dict["expected_last_name"],
                    })

            with self.subTest(msg=f"{test_title}"):
                with mocked_keycloak_service:
                    # ACT
                    real_response = self.client.put(self.user_info_url, headers=headers, json=request_payload_dict)

                    # ASSERT
                    self.assertEqual(real_response.status_code, 200)
                    self.assertEqual(real_response.json['responseCode'], 1)
                    self.assertEqual(real_response.json['message'], "action done")

                    self.assertEqual(real_response.json['data'].get("msisdn"), expected_result_dict['expected_msisdn'])
                    self.assertEqual(real_response.json['data'].get("country_code"),
                                     expected_result_dict['expected_country_code'])

                    # -- verify the update in MongoDB
                    updated_document = app_models.mobiles.AppUserDetails.objects.get(user_email='<EMAIL>')
                    self.assertIsNotNone(updated_document)
                    self.assertEqual(updated_document.first_name, expected_result_dict['expected_first_name'])
                    self.assertEqual(updated_document.last_name, expected_result_dict['expected_last_name'])
                    self.assertEqual(updated_document.country_code, expected_result_dict['expected_country_code'])
                    self.assertEqual(updated_document.msisdn, expected_result_dict['expected_msisdn'])
                    self.assertEqual(updated_document.is_newsletter_subscribed,
                                     expected_result_dict['expected_is_newsletter_subscribed'])

    def test_update_user_info_sad_path(self):
        # ARRANGE
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }

        mocked_keycloak_service = mock.patch("src.global_helpers.wrappers.KeycloakOpenID",
                                             side_effect=[
                                                 MockedKeycloakOpenIdFactory().introspect(**{
                                                     "auth_time": **********,
                                                     "aud": "account",
                                                     "azp": "realm-1-client-1",
                                                     "allowed-origins": [
                                                         "/*"
                                                     ],
                                                     "realm_access": {
                                                         "roles": [
                                                             "offline_access",
                                                             "uma_authorization",
                                                             "default-roles-realm-1"
                                                         ]
                                                     },
                                                     "resource_access": {
                                                         "account": {
                                                             "roles": [
                                                                 "manage-account",
                                                                 "manage-account-links",
                                                                 "view-profile"
                                                             ]
                                                         }
                                                     },
                                                     "scope": "profile email openid",
                                                     "preferred_username": "<EMAIL>",
                                                     "email": "<EMAIL>",

                                                 }

                                                                                          ).build()

                                             ])

        invalid_payload_body_list = [
            (
                'Email not present when it is required',
                'Email is required.',
                {
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Is newsletter subscribed not present when it is required',
                'Opting in or out of the newsletter subscription is required.',
                {
                    "email": "<EMAIL>",
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'First name contains numbers',
                'first_name must contain alphabets only.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum99999999999",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'First name exceeds 100 characters',
                'first_name must not exceed 100 characters.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum" * 15,
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'First name is whitespace',
                'first_name must contain alphabets only.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": " ",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Last name contains numbers',
                'last_name must contain alphabets only.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine 2050",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Last name exceeds 100 characters',
                'last_name must not exceed 100 characters.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine" * 11,
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Last name is whitespace',
                'last_name must contain alphabets only.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": " ",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Email Mismatch Between Request and Keycloak Token',
                'You are not authorized to change this account.',
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "**********"
                }
            ),
            (
                'Msisdn is not valid for valid country code',
                "The phone number is not valid.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": "********"
                }
            ),
            (
                'Country code is not valid',
                "The phone number is not valid.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "498419149",
                    "msisdn": "**********"
                }
            ),
            (
                'Provided only the Msisdn without a country_code(is empty)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "",
                    "msisdn": "**********"
                }
            ),
            (
                'Provided only the Msisdn without a country_code(is blank)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": " ",
                    "msisdn": "**********"
                }
            ),
            (
                'Provided only the Msisdn without a country_code(is None)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": None,
                    "msisdn": "**********"
                }
            ),
            (
                'Provided only the Msisdn without a country_code(field omitted)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "msisdn": "**********"
                }
            ),
            (
                'Provided only the country_code without a Msisdn(is blank)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": ""
                }
            ),
            (
                'Provided only the country_code without a Msisdn(is empty)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": " "
                }
            ),
            (
                'Provided only the country_code without a Msisdn(is None)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": "1",
                    "msisdn": None
                }
            ),
            (
                'Provided only the country_code without a Msisdn(field omitted)',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "country_code": "1"
                }
            ),
            (
                'Provided both country_code and msisdn as whitespaces',
                "Both 'country_code' and 'msisdn' must be provided and non-empty, or both left as None.",
                {
                    "email": "<EMAIL>",
                    "is_newsletter_subscribed": True,
                    "first_name": "Lorem ipsum",
                    "last_name": "Shoe Shine",
                    "country_code": " ",
                    "msisdn": " "
                }
            ),

        ]

        # ACT: Param Test
        for test_title, err_msg, request_payload_dict in invalid_payload_body_list:
            with self.subTest(msg=f"{test_title}"):
                with mocked_keycloak_service:
                    # ACT
                    real_response = self.client.put(self.user_info_url, headers=headers, json=request_payload_dict)

                    # ASSERT
                    self.assertEqual(real_response.status_code, 200)
                    self.assertEqual(real_response.json['responseCode'], 2)
                    self.assertEqual(real_response.json['message'], err_msg)

    def test_process_phone_number_happy_path(self):
        # ARRANGE

        valid_payload_body_list = [
            (
                'valid US phone number with (country_code)+(local_number) format 1',
                {"country_and_region_code": "+1", "local_phone_number": "************"},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": mock.ANY,
                    "expected_region_code": "US"
                }
            ),
            (
                'valid US phone number with (country_code)+(local_number) format 2',
                {"country_and_region_code": "1", "local_phone_number": "[20-25-55-01-32]"},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": mock.ANY,
                    "expected_region_code": "US"
                }
            ),
            (
                'valid US phone number with (country_code)+(local_number) format 3',
                {"country_and_region_code": "1", "local_phone_number": "2025550132"},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": mock.ANY,
                    "expected_region_code": "US"
                }
            ),
            (
                'valid US American samoa phone number with (country_code)+(region_code)+(local_number) format 1',
                {"country_and_region_code": "1 684", "local_phone_number": "73-31-23-4"},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": mock.ANY,
                    "expected_region_code": "AS"
                }
            ),
            (
                'valid US American samoa phone number with (country_code)+(region_code)+(local_number) format 2',
                {"country_and_region_code": "**** 8 4", "local_phone_number": "73-31-23-4"},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": mock.ANY,
                    "expected_region_code": "AS"
                }
            ),
            (
                'Both country_and_region_code and local_phone_number are None',
                {"country_and_region_code": None, "local_phone_number": None},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": None
                }
            ),
            (
                'Both country_and_region_code and local_phone_number are empty',
                {"country_and_region_code": "", "local_phone_number": ""},
                {
                    "expected_is_failed": False,
                    "expected_additional_info": None
                }
            )
        ]

        # ACT: Param Test
        for test_title, params, expected in valid_payload_body_list:
            with self.subTest(msg=f"Testing {test_title}"):
                # ACT
                real_response = process_phone_number(params["country_and_region_code"], params["local_phone_number"])

                # ASSERT
                self.assertEqual(real_response["is_failed"], expected["expected_is_failed"])

                if expected["expected_additional_info"] is not None:
                    self.assertEqual((real_response["additional_info"])["region_code"],
                                     expected["expected_region_code"])

    def test_process_phone_number_sad_path(self):
        # ARRANGE

        invalid_payload_combinations = [
            (
                'Invalid country_code, does not exist',
                {"country_and_region_code": "1494194169841321312313", "local_phone_number": "************"},
            ),
            (
                'Invalid local_phone_number for a valid country_and_region_code, does not exist',
                {"country_and_region_code": "+961", "local_phone_number": "7785"},
            ),
            (
                'country_and_region_code is filled, while local_phone_number is None',
                {"country_and_region_code": "+961", "local_phone_number": None},
            ),
            (
                'country_and_region_code is filled, while local_phone_number is empty',
                {"country_and_region_code": "+961", "local_phone_number": ""},
            ),
            (
                'country_and_region_code is filled, while local_phone_number is blank',
                {"country_and_region_code": "+961", "local_phone_number": " "},
            ),
            (
                'country_and_region_code is None, while local_phone_number is filled',
                {"country_and_region_code": None, "local_phone_number": "7824585"},
            ),
            (
                'country_and_region_code is empty, while local_phone_number is filled',
                {"country_and_region_code": "", "local_phone_number": "7824585"},
            ),
            (
                'country_and_region_code is blank, while local_phone_number is filled',
                {"country_and_region_code": " ", "local_phone_number": "7824585"},
            ),
        ]

        # ACT: Param Test
        for test_title, params in invalid_payload_combinations:
            with self.subTest(msg=f"Testing {test_title}"):
                # ACT
                real_response = process_phone_number(params["country_and_region_code"], params["local_phone_number"])

                # ASSERT
                self.assertTrue(real_response["is_failed"])

import unittest
from datetime import datetime, timedelta
from hashlib import md5
from unittest import mock

from b2c_helpers.mock_send_email import DummySendEmail
from flask import json

from src import app
from app_models.consumer_models import Bundles, Profiles, UserIccid, Vendors, region_names_, regions_, UserBundleLog, \
    TransactionLogs
from app_models.main_models import Settings, EmailSettings, RunnableScripts
from app_models.mobiles import AppList, app_platforms_, AppVersionList, AppEmailVerification, AppUserDetails
from b2c_helpers.db_helper import add_user_bundle_log, generate_temp_otp, add_user_iccid, \
     send_fcm_notification
from b2c_helpers.constaints import VODAFONE_VENDOR, FLEXIROAM_VENDOR, ESIMGO_VENDOR

from b2c_helpers.mocked_requests import MockedRequest

from src.global_helpers.utils  import random_url_token
from src.global_helpers.email_helpers import send_email_invoice
from instance import consumer_config
from tests.func_test.mock_thread import Mock<PERSON>hread

from tests.func_test import test_assign_stripe_api_data_manager

# @unittest.skip("stripe callback has drastically changed, this is an old extremly outdated test")
# class StripeCallbackAllocateFromInventory(unittest.TestCase):
#     MOCKED_KEYCLOAK_SERVER_URL = 'http://localhost.mocked.keycloak.com'
#     MOCKED_KEYCLOAK_CLIENT_ID = 'admin-cli'
#     MOCKED_KEYCLOAK_REALM_NAME = 'master'
#     MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = '0yyttrewqfssqddffgnbvcxww0020gfs'
#     CUSTOMER_EMAIL = "<EMAIL>"
#     STRIPE_API_ACCESS_TOKEN = "Stripe Api Access Token <Keycloak>"
#
#     def setUp(self):
#         app.testing = True
#         self.client = app.test_client()
#         self.stripe_callback_url = "/stripe-callback"
#
#         test_assign_stripe_api_data_manager.setup_operator()
#
#         self.vodafone_vendor = Vendors(**{
#             "vendor_name": VODAFONE_VENDOR, "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
#             "minimal_balance": 1, "is_active": True, 'number_of_expiry_days': 90
#         }).save()
#
#         self.vodafone_bundle = Bundles(**{
#             "vendor_name": self.vodafone_vendor.vendor_name, "bundle_code": "5G-Vodafone", "bundle_name": "5G",
#             "bundle_marketing_name": "fastest-4G", "category_name": "1", "region_code": region_names_[1],
#             "retail_price": 1, "bundle_vendor_name": self.vodafone_vendor.vendor_name,
#             "region_name": regions_[1], "bundle_vendor_code": "5G-Vodafone",
#             "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
#             "allocated_unit": 30,
#             "consumed_unit": 25,
#             "supplier_vendor": f"{self.vodafone_vendor.id}", "bundle_category": "country", "country_list": ["FR"],
#             "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
#             "validity_amount": "29", "daily_used": 0,
#             "profile_names": "", "is_active": True, "country_code_list": ["FR"]
#         }).save()
#
#         self.allocated_profile = Profiles(**{
#             "vendor_name": self.vodafone_vendor.vendor_name, "sku": "profile-id-unique-new-one",
#             "iccid": "22305670901234567890", "plan_uid": "5G-KLI-55",
#             "qr_code_value": "", "profile_names": "", "smdp_address": "rsp.2.test.client.com",
#             'matching_id': "None", "bundle_code": self.vodafone_bundle.bundle_code,
#             "create_datetime": datetime.utcnow(), "availability": 'Free', "status": True,
#             'expiry_date': datetime.utcnow() + timedelta(90)
#         }).save()
#
#         self.flexi_vendor = Vendors(**{
#             "vendor_name": FLEXIROAM_VENDOR, "vendor_prefix": "flexi", "vendor_suffix": "flexi",
#             "bundles_count": 30, "minimal_balance": 1, "is_active": True, 'number_of_expiry_days': None
#         }).save()
#
#         self.flexi_bundle = Bundles(**{
#             "vendor_name": FLEXIROAM_VENDOR, "bundle_code": "5G-Flexi", "bundle_name": "5G-flexi",
#             "bundle_marketing_name": "fastest-flexi-4G", "category_name": "1", "region_code": region_names_[1],
#             "retail_price": 1, "bundle_vendor_name": FLEXIROAM_VENDOR,
#             "region_name": regions_[1], "bundle_vendor_code": "5G-Flexi",
#             "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
#             "allocated_unit": 30,
#             "consumed_unit": 25,
#             "supplier_vendor": f"{self.vodafone_vendor.id}", "bundle_category": "country", "country_list": ["EG"],
#             "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
#             "validity_amount": "29", "daily_used": 0,
#             "profile_names": "te", "is_active": True, "country_code_list": ["EG"]
#         }).save()
#
#         self.flexi_allocated_profile = Profiles(**{
#             "vendor_name": FLEXIROAM_VENDOR, "sku": "profile-flexi-id-unique-new-one",
#             "iccid": "22305670901234567899", "plan_uid": "5G-KLI-Flexi-55",
#             "qr_code_value": "", "profile_names": "te", "smdp_address": "rsp.2.test.client.com",
#             'matching_id': "None", "bundle_code": self.flexi_bundle.bundle_code,
#             "create_datetime": datetime.utcnow(), "availability": 'Free', "status": True,
#             'expiry_date': datetime.utcnow() + timedelta(90),
#         }).save()
#
#         self.esimgo_vendor = Vendors(**{
#             "vendor_name": ESIMGO_VENDOR, "vendor_prefix": "esimgo", "vendor_suffix": "go",
#             "bundles_count": 30, "minimal_balance": 1, "is_active": True, 'number_of_expiry_days': None
#         }).save()
#
#         self.esimgo_bundle = Bundles(**{
#             "vendor_name": ESIMGO_VENDOR, "bundle_code": "5G-EsimGo", "bundle_name": "5G-EsimGo",
#             "bundle_marketing_name": "fastest-EsimGo-4G", "category_name": "1", "region_code": region_names_[1],
#             "retail_price": 1, "bundle_vendor_name": ESIMGO_VENDOR,
#             "region_name": regions_[1], "bundle_vendor_code": "5G-EsimGo",
#             "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
#             "allocated_unit": 30,
#             "consumed_unit": 25,
#             "supplier_vendor": f"{self.esimgo_vendor.id}", "bundle_category": "country", "country_list": ["EG"],
#             "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
#             "validity_amount": "29", "daily_used": 0,
#             "profile_names": "", "is_active": True, "country_code_list": ["EG"]
#         }).save()
#
#         self.esimgo_allocated_profile = Profiles(**{
#             "vendor_name": ESIMGO_VENDOR, "sku": "profile-EsimGo-id-unique-new-one",
#             "iccid": "22305670901234567889", "plan_uid": "5G-KLI-EsimGo-55",
#             "qr_code_value": "", "profile_names": "", "smdp_address": "rsp.2.test.client.com",
#             'matching_id': "None", "bundle_code": self.esimgo_bundle.bundle_code,
#             "create_datetime": datetime.utcnow(), "availability": 'Free', "status": True,
#             'expiry_date': datetime.utcnow() + timedelta(365),
#         }).save()
#
#         self.app = AppList(**{
#             "op_name": "Orange", 'app_name': 'B2COrg', 'app_platform': app_platforms_[0],
#             "update_date": datetime.utcnow()
#         }).save()
#
#         self.app_version = AppVersionList(**{
#             "version_token": "2.2.0", "app_id": self.app.to_dbref(),
#             "total_request": 20, "total_verify": 20, "total_download": "20", "update_date": datetime.utcnow(),
#             "create_date": datetime.utcnow(),
#         }).save()
#
#         self.settings = Settings(**{
#             "contact_email": "<EMAIL>", "esim_email": "<EMAIL>", "merchant_key": "test",
#             "merchant_password": "1234567890", "fcm_registration": "123456", "whatsapp_misisdn": "0101517485",
#             "reward_amount_limit_usd": 10, "percentage_of_reward": 10, "purchase_threshold_for_reward": 3,
#             "limited_bundle": 5
#         }).save()
#
#         self.app_email_verification = AppEmailVerification(**{
#             "user_email": "<EMAIL>"
#         }).save()
#
#         self.user = AppUserDetails(**{
#             "user_token": "Token", "version_token": "1", "user_name": self.app_email_verification.user_email,
#             'user_email': self.app_email_verification.user_email, "balance": 150
#         }).save()
#         self.email_setting = EmailSettings(**{
#             "email": "<EMAIL>", 'username': 'test', 'password': "test",
#             'smtp_server': "protocol.com", 'smtp_port': 1
#         }).save()
#
#     def tearDown(self):
#         self.vodafone_vendor.delete()
#         self.vodafone_bundle.delete()
#         self.allocated_profile.delete()
#         self.app.delete()
#         self.app_version.delete()
#         self.settings.delete()
#         self.app_email_verification.delete()
#         self.user.delete()
#         UserBundleLog.objects(email=self.CUSTOMER_EMAIL).delete()
#         UserIccid.objects(email=self.CUSTOMER_EMAIL).delete()
#         self.email_setting.delete()
#         self.flexi_vendor.delete()
#         self.flexi_bundle.delete()
#         self.flexi_allocated_profile.delete()
#         self.esimgo_vendor.delete()
#         self.esimgo_bundle.delete()
#         self.esimgo_allocated_profile.delete()
#
#     def setup_payment_data(self, bundle, profile):
#         data = {
#             "stripe_request_id": "we_1N7wlo2eZvKYlo2CYrlRYoUzFlexi", "event": "payment_intent.succeeded",
#             "email": self.CUSTOMER_EMAIL, "livemode": False, "name": "customer-name", "balance": 0,
#             "amount_received": bundle.retail_price, "outcome": {}, "paid": True,
#             "payment_intent": "payment_intent_id", "failure_balance_transaction": "",
#             "failure_code": "", "failure_message": "", "status": "succeeded", "customer": "cust_id",
#             "receipt_url": "stripe.com/receipts/<id>/", "payment_method_types": "card",
#             "client_secret": "whsec_sP8iSRHHQzAVJW5O6USVdRBz9UjhwwU3-FLEXI", "created": datetime.now(),
#         }
#         stripe_client_secret = md5(data['client_secret'].encode()).hexdigest()
#
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": bundle.bundle_code, "country_code": "EG",
#             "amount": bundle.retail_price, "paid_amount_credit_card": bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(bundle.bundle_duration),
#             "order_number": "whsec_sP8iSRHHQzAVJW5O6USVdRBz9UjhwwU3-FLEXI",
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "payment_status": False,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": bundle.currency_code,
#             "validity_days": bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": 1, 'stripe_client_secret': stripe_client_secret,
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": bundle.bundle_code,
#             "country_code": "EG",
#             "iccid": profile.iccid,
#             "activation_code": profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "plan_uid": profile.plan_uid,
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         profile.update(availability="Assigned")
#         return data
#
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_email_invoice, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[MockedRequest(
#             method="POST", url=consumer_config.stripe_login_url,
#             headers={}, status_code=200, data={}, json={
#                 "username": consumer_config.stripe_superadmin_username,
#                 "password": consumer_config.stripe_superadmin_password,
#             }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#         )]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZvKYlo2CYrlRYoUz",
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#         ]
#     )
#     def test_callback_with_bundle_where_its_linked_to_profile_from_inventory(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": "Bearer mocked_token"
#         }
#
#         data = self.setup_payment_data(self.vodafone_bundle, self.allocated_profile)
#         stripe_callback_response = self.client.post(self.stripe_callback_url, headers=headers, data=json.dumps(data))
#         self.assertDictEqual(
#             stripe_callback_response.json,
#             {
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                 'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0
#             }
#         )
#         self.assertIsNotNone(
#             TransactionLogs.objects(
#                 order_number=data["client_secret"],
#                 transaction_status=True
#             ).first()
#         )
#         self.vodafone_bundle.reload()
#         self.assertEqual(
#             self.vodafone_bundle.daily_used, 1
#         )
#         self.allocated_profile.reload()
#         self.assertEqual(
#             self.allocated_profile.availability,
#             'Assigned'
#         )
#
#         self.assertEqual(
#             self.allocated_profile.expiry_date.date(),
#             self.vodafone_vendor.expiry_date.date()
#         )
#         self.assertEqual(
#             self.allocated_profile.expiry_notified,
#             False
#         )
#         self.assertEqual(
#             UserIccid.objects(iccid=self.allocated_profile.iccid).first().expiry_date.date(),
#             self.vodafone_vendor.expiry_date.date()
#         )
#
#         self.assertIsNotNone(
#             RunnableScripts.objects(
#                 script="AllocateProfiles", vendor_name=VODAFONE_VENDOR, bundle_code=self.vodafone_bundle.bundle_code,
#             ).first()
#         )
#
#     @mock.patch(
#         "app_wapi.wapi_resources.v2_1.global_helpers.email_helpers.send_email"
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_email_invoice, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[MockedRequest(
#             method="POST", url=consumer_config.stripe_login_url,
#             headers={}, status_code=200, data={}, json={
#                 "username": consumer_config.stripe_superadmin_username,
#                 "password": consumer_config.stripe_superadmin_password,
#             }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#         )]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZvKYlo2CYrlRYoUzFlexi",
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#         ]
#     )
#     def test_callback_with_bundle_where_its_linked_to_profile_from_inventory_flexi_flow(self, *args):
#         dummy_sendemail = DummySendEmail()
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": "Bearer mocked_token"
#         }
#
#         data = self.setup_payment_data(self.flexi_bundle, self.flexi_allocated_profile)
#
#         stripe_callback_response = self.client.post(self.stripe_callback_url, headers=headers, data=json.dumps(data))
#         self.assertDictEqual(
#             stripe_callback_response.json,
#             {
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                 'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0
#             }
#         )
#         self.assertIsNotNone(
#             TransactionLogs.objects(
#                 order_number=data["client_secret"],
#                 transaction_status=True
#             ).first()
#         )
#         self.flexi_bundle.reload()
#         self.assertEqual(
#             self.flexi_bundle.daily_used, 1
#         )
#         self.flexi_allocated_profile.reload()
#         self.assertEqual(
#             self.flexi_allocated_profile.availability,
#             'Assigned'
#         )
#         self.assertEqual(
#             self.flexi_allocated_profile.expiry_date,
#             None
#         )
#         self.assertEqual(
#             self.flexi_allocated_profile.expiry_notified,
#             False
#         )
#         self.assertEqual(
#             UserIccid.objects(iccid=self.flexi_allocated_profile.iccid).first().expiry_date,
#             None
#         )
#
#         # self.assertEqual(len(dummy_sendemail.outbox), 1)
#         #
#         # self.assertEqual(
#         #     dummy_sendemail.outbox[0].header,
#         #     'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#         #     + '\n' + f"Subject:------URGENT------ Bundle: {self.flexi_bundle.bundle_code} deactivated" + ' \n'
#         # )
#         # self.assertIn(
#         #     f"{FLEXIROAM_VENDOR} due to No Available SIMs",
#         #     dummy_sendemail.outbox[0].msg,
#         # )
#
#         # self.assertIsNotNone(
#         #     RunnableScripts.objects(
#         #         script="AllocateProfiles", vendor_name=FLEXIROAM_VENDOR, bundle_code=self.flexi_bundle.bundle_code,
#         #     )
#         # )
#
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_email_invoice, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[MockedRequest(
#             method="POST", url=consumer_config.stripe_login_url,
#             headers={}, status_code=200, data={}, json={
#                 "username": consumer_config.stripe_superadmin_username,
#                 "password": consumer_config.stripe_superadmin_password,
#             }, response={"access_token": STRIPE_API_ACCESS_TOKEN}
#         )]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZvKYlo2CYrlRYoUzFlexi",
#                 headers={"access_token": STRIPE_API_ACCESS_TOKEN}, status_code=200, data={}, json={},
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}], method='GET'
#             ),
#         ]
#     )
#     def test_callback_with_bundle_where_its_linked_to_profile_from_inventory_esimgo_flow(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#             "Authorization": "Bearer mocked_token"
#         }
#
#         data = self.setup_payment_data(self.esimgo_bundle, self.esimgo_allocated_profile)
#
#         stripe_callback_response = self.client.post(self.stripe_callback_url, headers=headers, data=json.dumps(data))
#         self.assertDictEqual(
#             stripe_callback_response.json,
#             {
#                 'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                 'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0
#             }
#         )
#         self.assertIsNotNone(
#             TransactionLogs.objects(
#                 order_number=data["client_secret"],
#                 transaction_status=True
#             ).first()
#         )
#         self.esimgo_bundle.reload()
#         self.assertEqual(
#             self.esimgo_bundle.daily_used, 1
#         )
#         self.esimgo_allocated_profile.reload()
#         self.assertEqual(
#             self.esimgo_allocated_profile.availability,
#             'Assigned'
#         )
#
#         self.assertEqual(
#             self.esimgo_allocated_profile.expiry_date,
#             None
#         )
#         self.assertEqual(
#             self.esimgo_allocated_profile.expiry_notified,
#             False
#         )
#         self.assertEqual(
#             UserIccid.objects(iccid=self.esimgo_allocated_profile.iccid).first().expiry_date,
#             None
#         )
#
#         self.assertIsNotNone(
#             RunnableScripts.objects(
#                 script="AllocateProfiles", vendor_name=ESIMGO_VENDOR, bundle_code=self.esimgo_bundle.bundle_code,
#             )
#         )


from src import app

import unittest
from datetime import datetime, timedelta
from unittest import mock

from app_models.consumer_models import Vendors, Bundles, regions_, region_names_, Profiles, UserIccid
from b2c_helpers.db_helper import add_user_iccid, add_user_bundle_log, generate_temp_otp

from src.global_helpers.utils import random_url_token
from src.services.allocation_helpers import PaymentMethod
from tests.func_test.utils.object_factories.mocked_keycloak_open_id import Mocked<PERSON><PERSON><PERSON>loakOpenId, \
    MockedKeycloakOpenIdFactory

from tests.func_test.utils import db_utils
from tests.func_test.test_assign_stripe_api_data_manager import setup_operator
from app_models.reseller_models import Order_history

class TransactionHistoryTestCases(unittest.TestCase):
    CUSTOMER_EMAIL = "<EMAIL>"
    MOCKED_KEYCLOAK_SERVER_URL = 'http://localhost.mocked.keycloak.com'
    MOCKED_KEYCLOAK_CLIENT_ID = 'admin-cli'
    MOCKED_KEYCLOAK_REALM_NAME = 'master'
    MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = '0yyttrewqfssqddffgnbvcxww0020gfs'

    @classmethod
    def setUpClass(cls):
        db_utils.wipe_db_data_clean()

    def setUp(self):
        setup_operator()

        self.vendor = Vendors(**{
            "vendor_name": "Flexiroam", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "temp_token": "temp-access-token"
        }).save()
        otp = generate_temp_otp(12)
        self.not_consumed_bundle = Bundles(**{
            "vendor_name": self.vendor.vendor_name, "bundle_code": "5G-released", "bundle_name": "5G",
            "bundle_category": "country", "retail_price": 1, "allocated_unit": 30, "consumed_unit": 1,
            "bundle_marketing_name": "fastest-4G", "category_name": "1", "region_code": region_names_[1],
            "region_name": regions_[1], "bundle_vendor_code": "5G-released",
            "bundle_vendor_name": self.vendor.vendor_name,
            "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
            "validity_amount": "29", "supplier_vendor": f"{self.vendor.id}",
            "profile_names": "te", "otp": otp, "country_code_list": ["EG"], "country_list": ["EG"],
            "preview_for": ["subscriber", "reseller"]
        }).save()

        self.user_bundle_log = add_user_bundle_log({
            "email": self.CUSTOMER_EMAIL, "bundle_code": self.not_consumed_bundle.bundle_code, "country_code": "EG",
            "amount": self.not_consumed_bundle.retail_price,
            "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
            "validy_date": datetime.today() + timedelta(self.not_consumed_bundle.bundle_duration),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative",
            "history_log": 'hist_' + str(generate_temp_otp(12)), "payment_id": random_url_token(),
            "currency_code": self.not_consumed_bundle.currency_code, "payment_status": True,
            "validity_days": self.not_consumed_bundle.bundle_duration,
            "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE",
            "payment_category": PaymentMethod.credit_card.value, "promo_code": "", "payment_topup": False
        })

        self.client = app.test_client()

        self.profile = Profiles(**{
            "vendor_name": self.vendor.vendor_name, "sku": "profile-id-unique", "iccid": "12345678901234567890",
            "qr_code_value": "qr_code_value", "profile_names": "te", "smdp_address": "rsp.test.client.com",
            'matching_id': None,
            "create_datetime": datetime.utcnow()
        }).save()

        self.user_iccid = add_user_iccid({
            "email": self.CUSTOMER_EMAIL,
            "bundle_code": self.user_bundle_log.bundle_code,
            "country_code": self.user_bundle_log.country_code,
            "iccid": self.profile.iccid,
            "activation_code": self.profile.matching_id,
            "datetime": datetime.utcnow(),
            "payment_otp": self.user_bundle_log.otp,
            "cancel_otp": self.user_bundle_log.cancel_otp,
            "status": "used",
            "plan_uid": "plan_uid_mock"
        })

        _ = Order_history(**{
            "client_email":  self.CUSTOMER_EMAIL,
            "order_status": "Successful",
            "payment_date": datetime.utcnow(),
            "date_created": datetime.utcnow(),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative",
            "bundle_code": self.not_consumed_bundle.bundle_code,
            "bundle_date": self.not_consumed_bundle,
            "order_type": "BuyBundle",
            "bundle_marketing_name": self.not_consumed_bundle.bundle_marketing_name,
            "country_name": "country_1",
            "reseller_type": "subscriber",
            "iccid": self.profile.iccid,
            "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
            "paid_amount_wallet": 0
        }).save()

        self.transaction_history_url = f"/v2/transaction-history/{self.profile.iccid}"

    def tearDown(self):
        self.vendor.delete()
        self.not_consumed_bundle.delete()
        self.user_bundle_log.delete()
        self.user_iccid.delete()
        UserIccid.objects(email=self.CUSTOMER_EMAIL).delete()
        self.profile.delete()

    @mock.patch(
        "src.global_helpers.wrappers.KeycloakOpenID",
        return_value=
        MockedKeycloakOpenIdFactory()
        .userinfo(name= "sanemi", given_name= "sanemi", family_name= "shinz", email= CUSTOMER_EMAIL)
        .introspect(
            given_name= 'sanemi', family_name= 'shinz', preferred_username= 'sanemi',
            email= CUSTOMER_EMAIL,  username= 'sanemi'
        ).build()
    )
    def test_get_transaction_history(self, *args):
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        response = self.client.get(self.transaction_history_url, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json["data"]["transaction_history"], [])
        self.assertEqual(
            response.json["data"]["active_bundle"]["amount_paid"], self.not_consumed_bundle.retail_price)
        self.assertEqual(
            response.json["data"]["active_bundle"]["bundle_code"], self.not_consumed_bundle.bundle_code)
        self.assertEqual(
            response.json["data"]["active_bundle"]["bundle_type"], "Primary Bundle")
        self.assertEqual(
            response.json["data"]["active_bundle"]["bundle_name"], self.not_consumed_bundle.bundle_name)
        self.assertEqual(
            response.json["data"]["active_bundle"]["data_amount"], self.not_consumed_bundle.data_amount)
        self.assertEqual(
            response.json["data"]["active_bundle"]["data_unit"], self.not_consumed_bundle.data_unit)
        self.assertEqual(response.json["data"]["active_bundle"]["country_list"], self.not_consumed_bundle.country_list)
        self.assertEqual(response.json["data"]["active_bundle"]["country_code_list"],
                         self.not_consumed_bundle.country_code_list)
        self.assertEqual(
            response.json["data"]["active_bundle"]["display_name"].upper(), self.not_consumed_bundle.country_list[0])
        self.assertEqual(
            response.json["data"]["active_bundle"]["order_number"],
            self.user_bundle_log.order_number)
        self.assertEqual(response.json["totalCount"], 0)

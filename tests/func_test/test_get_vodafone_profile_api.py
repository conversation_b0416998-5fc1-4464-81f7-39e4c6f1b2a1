from src import app

import json
import unittest
from dataclasses import dataclass
from datetime import datetime, timedelta
from unittest import mock

import mongoengine.errors
from app_models.consumer_models import Profiles, UserIccid, UserBundleLog, TransactionLogs, \
    CallbackHistory
from app_models.main_models import Settings, NotificationLogs, HistoryLogs
from app_models.mobiles import AppVersionList
from b2c_helpers.db_helper import  send_fcm_notification
from b2c_helpers.constaints import VODAFONE_VENDOR


from src.global_helpers.utils import random_url_token, generate_temp_otp
from src.global_helpers.email_helpers import send_email_invoice
from src.global_helpers.db_helper import add_user_bundle_log, add_user_iccid, UserIccidStatus
from src.services.allocation_helpers import PaymentMethod
from instance import consumer_config
from tests.setup_test_data_for_assign_bundle import setup
from b2c_helpers.mock_send_email import DummySendEmail

from tests.func_test.mock_thread import Mock<PERSON>hread
from tests.func_test.mocked_requests import MockedRequest

from tests.func_test.utils import db_utils

from app_models.reseller_models import Order_history

@dataclass()
class GetVodafoneProfileRequestResponse:
    notificationType: str
    acknowledgementID: str
    customReference: str
    idValue: str
    activationCode: str

    response: dict
    assert_list: list

    @property
    def data(self):
        return json.dumps({
            "notificationType": self.notificationType, "acknowledgementID": self.acknowledgementID,
            "idValue": self.idValue, "customReference": self.customReference, "activationCode": self.activationCode
        })


class GetVodafoneProfileTestCases(unittest.TestCase):
    CUSTOMER_EMAIL = "<EMAIL>"

    @classmethod
    def setUpClass(cls):
        db_utils.wipe_db_data_clean()

    def setUp(self):
        self.client = app.test_client()
        AppVersionList.objects(
            version_token="2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE").first() or AppVersionList(**{
            "version_token": "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE", "app_id": "dsdsd",
            "total_request": 20, "total_verify": 20, "total_download": "20"
        }).save()
        try:
            self.app, self.app_version, self.app_version_without_app_id, self.app_without_operator, \
            self.app_version_without_operator, self.operator, self.app_email_verification, self.user, \
            self.keycloak_setting, self.email_setting, self.vendor, self.bundle, self.esim_go_mock_bundle, \
            self.profile, self.unused_user_iccid, self.esim_go_bundle, self.not_consumed_bundle, \
            self.flexi_bundle_without_profile, self.user_bundle_log, self.esim_go_mock_vendor, self.esim_go_vendor, \
            self.reward, self.from_user, self.vodafone_vendor, self.vodafone_bundle, \
            self.unused_user_iccid_for_unused_profile, self.un_used_profile, self.esim_go_bundle_usa, \
            self.alternative_flexi_bundle_for_vodafone, self.dark_continental_vodafone_bundle, self.free_profile \
                = setup()
        except mongoengine.errors.NotUniqueError as e:
            self.tearDown()
            raise e

        self.get_vodafone_profile_url = "/v2/get-profile"

    def tearDown(self):
        self.app.delete()
        self.app_version.delete()
        self.app_version_without_app_id.delete()
        self.app_without_operator.delete()
        self.app_version_without_operator.delete()
        self.operator.delete()
        self.app_email_verification.delete()
        self.user.delete()
        self.keycloak_setting.delete()
        self.email_setting.delete()
        self.vendor.delete()
        self.bundle.delete()
        self.esim_go_mock_bundle.delete()
        self.profile.delete()
        self.unused_user_iccid.delete()
        self.esim_go_bundle.delete()
        self.not_consumed_bundle.delete()
        self.flexi_bundle_without_profile.delete()
        self.user_bundle_log.delete()
        self.esim_go_mock_vendor.delete()
        self.esim_go_vendor.delete()
        Settings.objects(contact_email="<EMAIL>").delete()
        self.reward.delete()
        self.from_user.delete()
        self.vodafone_bundle.delete()
        self.vodafone_vendor.delete()
        TransactionLogs.objects(order_number="whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi").delete()
        self.un_used_profile.delete()
        self.unused_user_iccid_for_unused_profile.delete()
        UserBundleLog.objects(email=self.CUSTOMER_EMAIL).delete()
        UserIccid.objects(email=self.CUSTOMER_EMAIL).delete()
        Profiles.objects(
            iccid__in=[
                "22345678901234567890", '12345678998765432112',
                "12345678901254567899", "22245678901234567890", "22214678901234567890", "22224678901234567890"]
        ).delete()
        NotificationLogs.objects(email=self.CUSTOMER_EMAIL).delete()
        HistoryLogs.objects(email=self.CUSTOMER_EMAIL).delete()
        self.esim_go_bundle_usa.delete()
        TransactionLogs.objects(order_number__in=["we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone",
                                                  "we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative"]).delete()
        self.alternative_flexi_bundle_for_vodafone.delete()
        self.dark_continental_vodafone_bundle.delete()
        self.free_profile.delete()
        CallbackHistory.objects(order_number="whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative").delete()
        Profiles.objects(matching_id="84629-0D5B7-CD230-54B7F").delete()

    @mock.patch(
        "threading.Thread",
        side_effect=[
            MockThread(
                target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
                name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
            ),
            MockThread(
                target=send_email_invoice,
                args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
                      "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
            ),
            MockThread(
                target=send_fcm_notification,
                args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
                      "template", "image_file_final", True), name=f"Thread-to-send-cashback-fcm-{datetime.utcnow()}"
            )
        ]
    )
    @mock.patch(
        "b2c_helpers.support_helper.send_email",
        side_effect=[lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(
            email_settings, to_user, subject, body, has_attchament)]
    )
    @mock.patch(
        "requests.request",
        side_effect=[
            MockedRequest(
                method="GET", url=f"{consumer_config.vodafone_url}/network/things/iccids/12345678998765432112",
                headers={"Authorization": f"Bearer {consumer_config.vodafone_token}"}, status_code=200,
                data={}, response={
                    "thing": {
                        "profiles": [
                            {
                                "msisdn": "12345"
                            }
                        ]
                    }
                },
            )
        ]
    )
    def test_success_flow_we_get_iccid_and_user_iccid_updated(self, *args):
        self.skipTest("Outdate, does not do the same behaviour as it is described anymore")
        headers = {
            "Content-Type": "application/json",
        }
        user_bundle_log = add_user_bundle_log({
            "email": self.CUSTOMER_EMAIL, "bundle_code": self.not_consumed_bundle.bundle_code, "country_code": "EG",
            "amount": self.not_consumed_bundle.retail_price, "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
            "validy_date": datetime.today() + timedelta(self.not_consumed_bundle.bundle_duration),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative",
            "history_log": 'hist_' + str(generate_temp_otp(12)), "payment_id": random_url_token(),
            "currency_code": self.not_consumed_bundle.currency_code,
            "validity_days": self.not_consumed_bundle.bundle_duration, "version_token": self.app_version.version_token,
            "payment_category": PaymentMethod.credit_card.value, "promo_code": ""
        })
        user_iccid = add_user_iccid({
            "email": self.CUSTOMER_EMAIL,
            "bundle_code": user_bundle_log.bundle_code,
            "country_code": user_bundle_log.country_code,
            "iccid": self.profile.iccid,
            "activation_code": self.profile.matching_id,
            "datetime": datetime.utcnow(),
            "payment_otp": user_bundle_log.otp,
            "cancel_otp": user_bundle_log.cancel_otp,
        })

        user_order_history = Order_history(**{
            "client_email":  self.CUSTOMER_EMAIL,
            "order_status": "Successful",
            "payment_date": datetime.utcnow(),
            "date_created": datetime.utcnow(),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative",
            "bundle_code": self.not_consumed_bundle.bundle_code,
            "bundle_date": self.not_consumed_bundle,
            "order_type": "BuyBundle",
            "bundle_marketing_name": self.not_consumed_bundle.bundle_marketing_name,
            "country_name": "country_1",
            "reseller_type": "subscriber",
            "iccid": self.profile.iccid,
            "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
            "paid_amount_wallet": 0,
            "plan_uid": "id-from-vodafone",
        }).save()

        test_success = GetVodafoneProfileRequestResponse(
            notificationType="RSP:Allocated", acknowledgementID="id-from-vodafone",
            customReference=user_bundle_log.order_number, idValue="12345678998765432112",
            activationCode="LPA:1$client2.app2.rsp.com$84629-0D5B7-CD230-54B7F",
            response={},
            assert_list=[
                lambda: self.assertIsNotNone(
                    CallbackHistory.objects(
                        notification_type="RSP:Allocated", iccid="12345678998765432112", order_reference="id-from-vodafone",
                        order_number=user_bundle_log.order_number
                    ).first()
                ),
                lambda: self.assertIsNotNone(
                    Profiles.objects(iccid="12345678998765432112", vendor_name=VODAFONE_VENDOR, sku="12345678998765432112",
                                     availability="Assigned", status=True, smdp_address="client2.app2.rsp.com",
                                     has_lpa=True, matching_id="84629-0D5B7-CD230-54B7F",
                                     qr_code_value="LPA:1$client2.app2.rsp.com$84629-0D5B7-CD230-54B7F").first()
                ),
                lambda: self.assertEqual(
                    Profiles.objects(iccid="12345678998765432112").first().expiry_date.date(),
                    (datetime.utcnow() + timedelta(90)).date()
                ),
                lambda: self.assertEqual(
                    user_order_history.iccid, "12345678998765432112"
                ),
                lambda: self.assertEqual(
                    user_iccid.original_iccid, self.profile.iccid
                ),
                lambda: self.assertEqual(
                    user_iccid.activation_code, "84629-0D5B7-CD230-54B7F"
                ),
                lambda: self.assertEqual(
                    user_iccid.status, UserIccidStatus.USED
                ),
                lambda: self.assertEqual(
                    user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(90)).date()
                )
            ]
        )


        self.client.post(self.get_vodafone_profile_url, headers=headers, data=test_success.data)
        user_iccid.reload()
        for assert_case in test_success.assert_list:
            assert_case()

import os
import json
import unittest
from datetime import datetime, timedelta
from unittest import mock
from unittest.mock import MagicMock

from src import app
from app_models.consumer_models import Bundles, Profiles, Vendors, region_names_, regions_, Countries
from app_models.main_models import Settings, RunnableScripts
from app_models.reseller_models import Order_history
from app_models.mobiles import (AppList, app_platforms_, AppVersionList,
                                AppEmailVerification, AppUserDetails)
from b2c_helpers.constaints import VODAFONE_VENDOR
from tests.func_test.utils.db_utils import wipe_db_data_clean
from src.services.allocation_helpers import PaymentMethod
from tests.func_test.mocked_requests import MockedRequest
from instance import consumer_config
from tests.func_test.utils.object_factories.objects_factory import (KeycloakOpenIdUserInfoResponseFactory,
                                                                    KeycloakOpenIdIntrospectResponseFactory,
                                                                    HeaderBuilder)

"""
This test case examines the system's behavior in two scenarios involving the initial purchase of a bundle:

   * EXISTING BUNDLE: The test verifies the system's response when attempting to purchase a bundle that already 
   exists in the inventory. 

   * NON EXISTING BUNDLE: The test evaluates how the system handles the purchase of a bundle that is not available in 
   the inventory and needs to be acquired directly from the vendor.
"""


# TODO: Add test cases for remaining vendors (MontyMobile, Flexiroam, ESIMGo, Indosat).
# TODO: Include test cases for credit card payment method.
@mock.patch.multiple(
    "keycloak.KeycloakOpenID",
    userinfo=MagicMock(return_value=KeycloakOpenIdUserInfoResponseFactory(email="<EMAIL>").build()),
    introspect=MagicMock(return_value=KeycloakOpenIdIntrospectResponseFactory(email="<EMAIL>").build())
)
@mock.patch.multiple(
    "b2c_helpers.support_helper",
    send_email=MagicMock(return_value=None)
)
class AssignStripeFromInventory(unittest.TestCase):
    BASIC_HEADER = HeaderBuilder().add_auth("Bearer mocked_token").custom_header(key="device_id", value="device_id")
    ASSIGN_STRIPE_URL = "/v2/assign"
    TEST_EMAIL = "<EMAIL>"
    VERSION_TOKEN = "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE"
    ICCID = "22305670901234567890"
    COUNTRY_CODE = 'ARE'

    @classmethod
    def setUpClass(cls):
        wipe_db_data_clean()

    def setUp(self):
        os.environ.update({"ENV": "test"})
        wipe_db_data_clean()
        self.client = app.test_client()

        # Setup required data in the database
        self.vodafone_vendor: Vendors = self._create_vendor(
            vendor_name=VODAFONE_VENDOR,
            is_active=True,
            minimal_balance=0,
            bundles_count=0,
            apply_inventory=True,
            get_profile_from_vendor=True,
            supports_empty_profiles=False,
            has_balance=False,
            has_constant_bundle_count=False,
            supports_profile_reservation=False
        )
        self.vodafone_bundle = self._create_bundle("Vodafone-Bundle", "Vodafone-Bundle", "fastest-4G", 1, 1, "country",
                                                   ["ARE"],
                                                   ["ARE"])
        self.without_profile_in_inventory = self._create_bundle("6G-Vodafone", "6G-Vodafone", "fastest-6G", 1, 1,
                                                                "country",
                                                                ["ARE"], ["ARE"], "te")
        self.without_profile_in_inventory_2 = self._create_bundle("7G-Vodafone", "7G-Vodafone", "fastest-6G", 1, 1,
                                                                  "country",
                                                                  ["ARE"], ["ARE"], "te")
        self.country = self._create_country("United Arab Emirates", "AE", "ARE", ["ARE", "UAE"],
                                            "United-Arab-Emirates-esim")
        self.allocated_profile = self._create_profile(self.ICCID, "profile-id-unique-new-one",
                                                      self.vodafone_bundle.bundle_code,
                                                      self.vodafone_vendor.vendor_name)
        self.app = self._create_app("Orange", 'B2COrg', app_platforms_[0])
        self.app_version = self._create_app_version(self.VERSION_TOKEN, self.app)
        self.settings = self._create_settings()
        self.app_email_verification = self._create_email_verification(self.TEST_EMAIL)
        self.user = self._create_user(self.TEST_EMAIL, self.VERSION_TOKEN, 1500)

    def tearDown(self):
        # Clean up the database after each test
        wipe_db_data_clean()

    def _create_vendor(
            self,
            vendor_name,
            bundles_count,
            minimal_balance,
            is_active,
            **kwargs,
    ):
        for attr in kwargs.keys():
            if not getattr(Vendors, attr, False):
                raise ValueError(f"Cant attribute {attr} to class Vendors")
        return (
                Vendors.objects(vendor_name=vendor_name).first()
                or Vendors(
            vendor_name=vendor_name,
            bundles_count=bundles_count,
            minimal_balance=minimal_balance,
            is_active=is_active,
            support_topup=True,
            **kwargs,
        ).save()
        )


    def _create_bundle(self, bundle_code, bundle_name, bundle_marketing_name, retail_price, rate_revenue,
                       bundle_category, country_list, country_code_list, profile_names=""):
        # Create a bundle if it doesn't already exist.
        return Bundles.objects(bundle_code=bundle_code).first() or Bundles(
            vendor_name=self.vodafone_vendor.vendor_name, bundle_code=bundle_code, bundle_name=bundle_name,
            bundle_marketing_name=bundle_marketing_name, category_name="1", region_code=region_names_[1],
            retail_price=retail_price, bundle_vendor_name=self.vodafone_vendor.vendor_name, region_name=regions_[1],
            bundle_vendor_code=bundle_code, rate_revenue=rate_revenue, create_datetime=datetime.utcnow(),
            bundle_duration=30, allocated_unit=30, supplier_vendor=f"{self.vodafone_vendor.id}",
            bundle_category=bundle_category, country_list=country_list, unit_price="10", data_amount=20,
            fullspeed_data_amount=16, data_unit="GB", validity_amount="28", profile_names=profile_names,
            is_active=True, country_code_list=country_code_list, preview_for=["subscriber", "reseller"]).save()

    def _create_profile(self, iccid, sku, bundle_code, vendor_name):
        # Create a profile if it doesn't already exist
        return Profiles.objects(iccid=iccid).first() or Profiles(
            vendor_name=vendor_name, sku=sku, iccid=iccid, qr_code_value=None, profile_names="",
            smdp_address="rsp.2.test.client.com", matching_id="None", bundle_code=bundle_code,
            plan_uid="5g-KLOW-20", create_datetime=datetime.utcnow(), availability='Free', status=True,
            expiry_date=datetime.utcnow() + timedelta(90)).save()

    def _create_country(self, country_name, iso2_code, iso3_code, alternative_country, zone_name):
        # Create a country if it doesn't already exist
        return Countries.objects(iso3_code=iso3_code).first() or Countries(
            country_name=country_name, iso2_code=iso2_code, iso3_code=iso3_code,
            alternative_country=alternative_country, currency="", currency_symbol="",
            zone_name=zone_name).save()

    def _create_app(self, op_name, app_name, app_platform):
        # Create an app if it doesn't already exist.
        return AppList.objects(op_name=op_name).first() or AppList(
            op_name=op_name, app_name=app_name, app_platform=app_platform,
            update_date=datetime.utcnow()).save()

    def _create_app_version(self, version_token, app):
        # Create an app version if it doesn't already exist.
        return AppVersionList.objects(version_token=version_token).first() or AppVersionList(
            version_token=version_token, app_id=app.to_dbref(),
            total_request=20, total_verify=20, total_download="20",
            update_date=datetime.utcnow(), create_date=datetime.utcnow()).save()

    def _create_settings(self):
        # Create settings if they don't already exist.
        return Settings.objects(contact_email="<EMAIL>").first() or Settings(
            contact_email="<EMAIL>", esim_email="<EMAIL>",
            merchant_key="test", merchant_password="1234567890", fcm_registration="123456",
            whatsapp_misisdn="0101517485", reward_amount_limit_usd=10, percentage_of_reward=10,
            purchase_threshold_for_reward=3, limit_bundle=5).save()

    def _create_email_verification(self, user_email):
        # Create an email verification entry if it doesn't already exist.
        return AppEmailVerification.objects(user_email=user_email).first() or AppEmailVerification(
            user_email=user_email).save()

    def _create_user(self, user_name, version_token, balance):
        # Create a user if they don't already exist.
        return AppUserDetails.objects(user_name=user_name).first() or AppUserDetails(
            user_token="Token", version_token=version_token, user_name=user_name,
            user_email=user_name, balance=balance, ipv4_address="*******").save()

    def test_allocating_vodafone_bundle_and_there_is_only_one_profile_support_in_inventory(self):
        """
        Test allocating Vodafone bundle when there is only one profile in inventory.
        """
        test_cases = [
            {
                "name": "Vodafone Allocation From Inventory(when only 1 profile available)::Prime "
                        "Purchase::Wallet::Success",
                "request": {"bundle_code": self.vodafone_bundle.bundle_code,
                            "version_token": self.app_version.version_token,
                            "topup_code": "", "payment_method": PaymentMethod.wallet.value, "iccid": "",
                            "country_code": self.COUNTRY_CODE,
                            "searched_countries": [self.COUNTRY_CODE], "app_token": ""},
                "response": {"status_code": 200, "status": True, "message": "Success"}
            }
        ]

        for tc in test_cases:
            with self.subTest(tc["name"]):
                response = self.client.post(self.ASSIGN_STRIPE_URL, headers=self.BASIC_HEADER.headers,
                                            data=json.dumps(tc["request"]))
                # Assert the response status and content
                self.assertEqual(response.status_code, tc["response"]["status_code"])
                self.assertEqual(response.json["status"], tc["response"]["status"])
                self.assertEqual(response.json["message"], tc["response"]["message"])

                # Verify the order history entry
                order_history = Order_history.objects(order_number=response.json["data"]["history_log_id"]).first()
                self.assertIsNotNone(order_history)
                self.assertIsNotNone(
                    RunnableScripts.objects(
                        bundle_code=tc["request"]["bundle_code"], state='Accepted', informer='From API',
                        script='AllocateProfiles', daily_used=self.settings.limit_bundle
                    ).first()
                )

    @mock.patch(
        "requests.request",
        side_effect=[
            MockedRequest(
                method="GET", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/*",
                headers={"Authorization": f"Bearer {consumer_config.vodafone_token}"}, status_code=500,
                data={}, response={'acknowledgement': {'id': ''}}
            ),
            MockedRequest(
                method="GET", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/*",
                headers={"Authorization": f"Bearer {consumer_config.vodafone_token}"}, status_code=200,
                data={}, response={'acknowledgement': {'id': '377d992d-7110-495b-a89f-1927f1f2144c'}}
            )
        ]
    )
    def test_allocating_vodafone_bundle_and_there_is_no_profile_in_inventory(self, *args):
        """
        Test allocating Vodafone bundle when there is no profile in inventory.
        """
        test_cases = [
            {
                "name": "Vodafone Allocation(0 profile in the inventory)::Prime Purchase::Wallet::Failed - Vendor Unavailable",
                "request": {"bundle_code": self.without_profile_in_inventory.bundle_code,
                            "version_token": self.app_version.version_token,
                            "topup_code": "", "payment_method": PaymentMethod.wallet.value, "iccid": "",
                            "country_code": self.COUNTRY_CODE,
                            "searched_countries": [self.COUNTRY_CODE], "app_token": ""},
                "response": {"status_code": 200, "status": False, "message": "",
                             "developerMessage": "Exception at post, as: cannot unpack non-iterable bool object"
                             }
            },
            {
                "name": "Vodafone Allocation(0 profile in the inventory)::Prime Purchase::Wallet::Success",
                "request": {"bundle_code": self.without_profile_in_inventory_2.bundle_code,
                            "version_token": self.app_version.version_token,
                            "topup_code": "", "payment_method": PaymentMethod.wallet.value, "iccid": "",
                            "country_code": self.COUNTRY_CODE,
                            "searched_countries": [self.COUNTRY_CODE], "app_token": ""},
                "response": {"status_code": 200, "status": True, "message": "Success", "developerMessage": ""}
            }
        ]

        for tc in test_cases:
            with self.subTest(tc["name"]):
                response = self.client.post(self.ASSIGN_STRIPE_URL, headers=self.BASIC_HEADER.headers,
                                            data=json.dumps(tc["request"]))
                # Assert the response status and content
                self.assertEqual(response.status_code, tc["response"]["status_code"])
                self.assertEqual(response.json["status"], tc["response"]["status"])
                self.assertEqual(response.json["message"], tc["response"]["message"])
                self.assertEqual(response.json["developerMessage"], tc["response"]["developerMessage"])
                self.assertIsNotNone(
                    RunnableScripts.objects(
                        bundle_code=tc["request"]["bundle_code"], state='Accepted', informer='From API',
                        script='AllocateProfiles', daily_used=self.settings.limit_bundle
                    ).first()
                )

                # Verify the order history entry
                if tc["response"]["status"]:
                    order_history = Order_history.objects(order_number=response.json["data"]["history_log_id"]).first()
                    self.assertIsNotNone(order_history)

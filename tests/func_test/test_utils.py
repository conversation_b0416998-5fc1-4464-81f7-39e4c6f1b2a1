import unittest

from src.global_helpers.utils import is_only_letters_including_diacritics


class IsOnlyLettersIncludingDiacriticsTestCases(unittest.TestCase):

    def test_sad_path(self):
        _common_msg_1 = "Input cannot start or end with a space, nor contain multiple consecutive spaces."
        _common_msg_2 = "Input can only contain alphabetic characters and single spaces between words."
        _common_msg_3 = "Input must contain at least one alphabetic character."

        invalid_data = [
            (
                "starts with a space",
                " <PERSON>",
                _common_msg_1
            ),
            (
                "Ends with a space",
                "<PERSON> ",
                _common_msg_1
            ),
            (
                "starts with a double space",
                "  <PERSON>",
                _common_msg_1
            ),
            (
                "Ends with a double space",
                "<PERSON>  ",
                _common_msg_1
            ),
            (
                "Is only numbers",
                "123",
                _common_msg_2
            ),
            (
                "Has letters and numbers",
                "hello12",
                _common_msg_2
            ),
            (
                "Includes non-alphabetical characters",
                "hello-u",
                _common_msg_2
            ),
            (
                "Includes non-alphabetical characters, on a different language",
                "مستر-محمد",
                _common_msg_2
            ),
            (
                "Includes more than a single space, on a different language",
                "مستر         محمد",
                _common_msg_1
            ),
            (
                "Includes more than a single space(x-2)",
                "<PERSON>  Jaafar",
                _common_msg_1
            ),
            (
                "Includes more than a single space(x-3)",
                "Mohammad   Jaafar",
                _common_msg_1
            ),
            (
                "Includes more than a single space(x-something)",
                "Mohammad                                         Jaafar",
                _common_msg_1
            ),
            (
                "Includes non-alphabetical characters(a diacritic), on a different language",
                "ً",
                _common_msg_3
            ),
            (
                "Input is None",
                None,
                _common_msg_3
            ),
            (
                "Input is blank",
                " ",
                _common_msg_1
            ),
            (
                "Input is empty",
                "",
                _common_msg_3
            ),
        ]

        for test_title, input_param, err_message in invalid_data:
            with self.subTest(msg=f"{test_title}"):
                # ACT
                real_response: tuple = is_only_letters_including_diacritics(input_param)

                # ASSERT
                self.assertFalse(real_response[0])
                self.assertEqual(real_response[1], err_message)

    def test_happy_path(self):

        valid_data = [
            (
                "Input is Simple Arabic",
                "محمد"
            ),
            (
                "Input is Simple Russian",
                "Привет"
            ),
            (
                "Input is Simple English",
                "Hello"
            ),
            (
                "Input is Simple Chinese",
                "こんにちは 世界"
            ),
            (
                "Input contains a single space between words",
                "Mister Mohammad"
            ),
            (
                "Input contains a single space between words and is different language",
                "مستر محمد"
            ),
            (
                "Input is Arabic and contains diacritics",
                "مُحَمَّدٌ"
            ),
            (
                "Input is Spanish and contains diacritics",
                "Niño"
            ),
            (
                "Input is French and contains diacritics",
                "François"
            ),
            (
                "Input is German and contains diacritics",
                "Müller"
            ),
            (
                "Input is German and contains diacritics",
                "Löwe"
            ),
            (
                "Input is Turkish and contains diacritics",
                "Dağ"
            ),
            (
                "Input is Vietnamese and contains diacritics",
                "Nguyễn"
            ),
            (
                "Input is Vietnamese and contains diacritics",
                "Phạm"
            ),
            (
                "Input is Polish and contains diacritics",
                "Władysław"
            ),
            (
                "Input is Polish and contains diacritics",
                "Błażej"
            ),
            (
                "Input is Czech and contains diacritics",
                "Čapek"
            ),
            (
                "Input is Czech and contains diacritics",
                "Pražák"
            ),
            (
                "Input is Scandinavian and contains diacritics",
                "Björn"
            ),
            (
                "Input is Icelandic and contains diacritics",
                "Þórño"
            )
        ]

        for test_title, input_param in valid_data:
            with self.subTest(msg=f"{test_title}"):
                # ACT
                real_response: tuple = is_only_letters_including_diacritics(input_param)

                # ASSERT
                self.assertTrue(real_response[0])
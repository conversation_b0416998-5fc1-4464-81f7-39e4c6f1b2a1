import unittest
from datetime import datetime, timedelta
from unittest import mock

from src import app

from app_models.consumer_models import Vendors, Bundles, regions_, region_names_, UserIccid, Profiles, UserBundleLog, \
    _icicid_status
from b2c_helpers.db_helper import generate_temp_otp
from b2c_helpers.constaints import FLEXIROAM_VENDOR

from src.global_helpers.utils import random_url_token

from src.services.allocation_helpers import PaymentMethod

from tests.func_test import test_assign_stripe_api_data_manager
from tests.func_test.utils import db_utils
from app_models.reseller_models import Order_history

from tests.func_test.utils.object_factories.objects_factory import (KeycloakOpenIdUserInfoResponseFactory,
                                                                    KeycloakOpenIdIntrospectResponseFactory)
import logging
logger = logging.getLogger(__name__)

class UserActiveBundlesTestCase(unittest.TestCase):
    CUSTOMER_EMAIL = "<EMAIL>"
    MOCKED_KEYCLOAK_SERVER_URL = 'http://localhost.mocked.keycloak.com'
    MOCKED_KEYCLOAK_CLIENT_ID = 'admin-cli'
    MOCKED_KEYCLOAK_REALM_NAME = 'master'
    MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = '0yyttrewqfssqddffgnbvcxww0020gfs'

    @classmethod
    def setUpClass(cls):
        db_utils.wipe_db_data_clean()

    def setUp(self):
        self.client = app.test_client()
        self.vendor = Vendors(**{
            "vendor_name": FLEXIROAM_VENDOR, "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "temp_token": "temp-access-token"
        }).save()

        test_assign_stripe_api_data_manager.setup_operator()

        self.bundle = Bundles(**{
            "vendor_name": self.vendor.vendor_name, "bundle_code": "4G-released", "bundle_name": "4G",
            "bundle_category": "country", "retail_price": 1, "country_list": ["EG"], "country_code_list": ["EG"],
            "bundle_marketing_name": "fastest-4G", "category_name": "1", "region_code": region_names_[1],
            "region_name": regions_[1], "bundle_vendor_code": FLEXIROAM_VENDOR + "0152",
            "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
            "validity_amount": "30", "supplier_vendor": f"{self.vendor.id}", "profile_names": "te",
            "bundle_vendor_name": self.vendor.vendor_name
        }).save()

        self.user_bundle_log = UserBundleLog(**{
            "email": self.CUSTOMER_EMAIL, "bundle_code": self.bundle.bundle_code, "country_code": "EG",
            "amount": self.bundle.retail_price, "paid_amount_credit_card": self.bundle.retail_price,
            "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
            "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
            "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi",
            "history_log": 'hist_' + str(generate_temp_otp(12)), "payment_category": PaymentMethod.credit_card.value,
            "payment_id": random_url_token(), "promo_code": "", "currency_code": self.bundle.currency_code,
            "validity_days": self.bundle.bundle_duration, "version_token": "2.2.0", "payment_status": True
        }).save()

        self.profile = Profiles(**{
            "vendor_name": self.vendor.vendor_name, "sku": "profile-id-unique-new-one", "iccid": "22305670901234567890",
            "qr_code_value": None, "profile_names": self.bundle.profile_names, "smdp_address": "rsp.2.test.client.com",
            'matching_id': "None", "create_datetime": datetime.utcnow(), "availability": 'Assigned',
            "status": True
        }).save()

        self.user_iccid = UserIccid(**{
            "email": self.CUSTOMER_EMAIL, "status": "unused", "datetime": datetime.now(),
            "bundle_code": self.bundle.bundle_code, "iccid": self.profile.iccid, "plan_uid": "123",
            "payment_otp": self.user_bundle_log.otp, "profile_availability": False
        }).save()

        _ = Order_history(**{
            "client_email": self.CUSTOMER_EMAIL,
            "order_status": "Successful",
            "payment_date": datetime.utcnow(),
            "date_created": datetime.utcnow(),
            "order_number": "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi",
            "bundle_code": self.bundle.bundle_code,
            "order_type": "BuyBundle",
            "bundle_marketing_name": self.bundle.bundle_marketing_name,
            "country_name": "country_1",
            "reseller_type": "subscriber",
            "iccid": self.profile.iccid,
            "plan_status": "Active",
            "bundle_data": {
                "vendor_name": self.bundle.vendor_name,
                "bundle_code": self.bundle.bundle_code,
                "bundle_name": self.bundle.bundle_name,
                "bundle_marketing_name": self.bundle.bundle_marketing_name,
                "bundle_duration": self.bundle.bundle_duration,
                "retail_price": self.bundle.retail_price,
                "currency_code": self.bundle.currency_code,
                "data_amount": self.bundle.data_amount,
                "data_unit": self.bundle.data_unit
            }
        }).save()

        self.user_consumption_api_url = '/v2/user-bundles'

    def tearDown(self):
        self.vendor.delete()
        self.bundle.delete()
        self.profile.delete()
        self.user_iccid.delete()
        self.user_bundle_log.delete()

    @mock.patch.multiple(
        "keycloak.KeycloakOpenID",
        userinfo=mock.MagicMock(
            return_value=KeycloakOpenIdUserInfoResponseFactory(email=CUSTOMER_EMAIL).build()),
        introspect=mock.MagicMock(
            return_value=KeycloakOpenIdIntrospectResponseFactory(email=CUSTOMER_EMAIL).build())
    )
    def test_list_user_consumption(self, *args):

        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }
        user_consumption_response = self.client.get(self.user_consumption_api_url + "?status=used", headers=headers)
        self.assertEqual(user_consumption_response.status_code, 200)
        self.assertEqual(
            self.profile.availability != "Expired",
            user_consumption_response.json["data"]["bundles"][0]["profile_availability"]
        )
        self.assertEqual(
            self.bundle.validity_amount, str(user_consumption_response.json["data"]["bundles"][0]["validity_amount"]))
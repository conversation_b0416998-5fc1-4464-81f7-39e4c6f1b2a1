from app_models.mobiles import AppUserDetails, DeviceData

class HeaderBuilder:
    def __init__(self):
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    def add_auth(self, token):
        self.headers["Authorization"] = f"{token}"
        return self

    def content_type(self, content_type):
        self.headers["Content-Type"] = content_type
        return self

    def accept(self, accept):
        self.headers["Accept"] = accept
        return self

    def custom_header(self, key, value):
        self.headers[key] = value
        return self


class RequestBuilder:
    def __init__(self):
        self.request = {
            "app_version": "2.7.7",
            "mobile_network_code": "",
            "mobile_country_code": "",
            "system_name": "Android",
            "system_version": "11",
            "name": "Galaxy S21",
            "model_name": "SM-G991B"
        }

    def with_fcm_token(self, token):
        self.request["fcm_token"] = token
        return self

    def with_device_id(self, device_id):
        self.request["device_id"] = device_id
        return self


class AppUserDetailBuilder:
    """Builds the MongoDB AppUserDetails Doc"""

    def __init__(self, unique_identifier):
        self._unique_identifier = unique_identifier
        self._email = f"app_user_email{self._unique_identifier}@email.com"
        self._app_user_details = {
            "user_token": f"app_user_token{self._unique_identifier}",
            "auth_bearer": f"app_user_auth_bearer{self._unique_identifier}",
            "device_id": f"app_user_device_id{self._unique_identifier}",
            "fcm_token": f"app_user_fcm_token{self._unique_identifier}",
            "version_token": "2.2.0",
            "first_name": "app_user_first_name",
            "last_name": "app_user_app_last_name",
            "user_name": self._email,
            "user_email": self._email,
            "email": self._email
        }

    def with_auth_bearer(self, auth_bearer):
        """Overwrites the auth_bearer"""
        self._app_user_details["auth_bearer"] = f"{auth_bearer}"
        return self

    def with_device_id(self, device_id):
        """Overwrites the device_id"""
        self._app_user_details["device_id"] = f"{device_id}"
        return self

    def with_fcm_token(self, fcm_token):
        """Overwrites the fcm_token"""
        self._app_user_details["fcm_token"] = f"{fcm_token}"
        return self

    def with_email(self, email):
        """Overwrites the email"""
        self._app_user_details["user_name"] = email
        self._app_user_details["user_email"] = email
        self._app_user_details["email"] = email
        return self

    def build(self) -> AppUserDetails:
        """Generates the AppUserDetails mongo doc"""
        return AppUserDetails(**self._app_user_details)

    def get_app_user_details(self) -> dict:
        """Returns the pythonic app user details dictionary"""
        return self._app_user_details


class DeviceDataBuilder:
    def __init__(self, unique_identifier):
        self._unique_identifier = unique_identifier
        self._device_data = {
            "device_id": f"device_data_device_id{self._unique_identifier}",
            "fcm_token": f"device_data_fcm_token{self._unique_identifier}",
            "datetime": "2024-02-27T08:54:41.233+00:00",
            "app_version": "2.7.7",
            "mobile_network_code": "",
            "mobile_country_code": "",
            "system_name": "Android",
            "system_version": "11",
            "name": "Galaxy S21",
            "model_name": "SM-G991B"
        }

    def with_device_id(self, device_id):
        """Overwrites the device_id, which is supposed to match a device_id in AppUserDetails mongo document"""
        self._device_data["device_id"] = device_id
        return self

    def with_fcm_token(self, fcm_token):
        """Overwrites the fcm_token, which is supposed to match a device_id in AppUserDetails mongo document, it is used to map DeviceData to the correct user"""
        self._device_data["fcm_token"] = fcm_token
        return self

    def build(self) -> DeviceData:
        """Generates the AppUserDetails mongo doc"""
        return DeviceData(**self._device_data)

    def get_device_data(self) -> dict:
        """Returns the pythonic app user details dictionary"""
        return self._device_data

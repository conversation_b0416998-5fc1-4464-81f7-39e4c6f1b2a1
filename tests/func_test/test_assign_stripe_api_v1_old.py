import json
import os
import unittest
from dataclasses import dataclass, field

import mongoengine.errors
from app_models.consumer_models import Bundles, Profiles, UserIccid, UserBundleLog, Countries, TransactionLogs, \
    CashbackHistory
from app_models.main_models import Settings

from src import app
from src.services.allocation_helpers import PaymentMethod
from tests.setup_test_data_for_assign_bundle import setup


class AssignStripeTestCases(unittest.TestCase):
    MOCKED_KEYCLOAK_SERVER_URL = 'http://localhost.mocked.keycloak.com'
    MOCKED_KEYCLOAK_CLIENT_ID = 'admin-cli'
    MOCKED_KEYCLOAK_REALM_NAME = 'master'
    MOCKED_KEYCLOAK_CLIENT_SECRET_KEY = '0yyttrewqfssqddffgnbvcxww0020gfs'

    def setUp(self):
        os.environ.update({"ENV": "test"})
        self.client = app.test_client()
        try:
            self.app, self.app_version, self.app_version_without_app_id, self.app_without_operator, \
            self.app_version_without_operator, self.operator, self.app_email_verification, self.user, \
            self.keycloak_setting, self.email_setting, self.vendor, self.bundle, self.esim_go_mock_bundle, \
            self.profile, self.unused_user_iccid, self.esim_go_bundle, self.not_consumed_bundle, \
            self.flexi_bundle_without_profile, self.user_bundle_log, self.esim_go_mock_vendor, self.esim_go_vendor, \
            self.reward, self.from_user, self.vodafone_vendor, self.vodafone_bundle, \
            self.unused_user_iccid_for_unused_profile, self.un_used_profile, self.esim_go_bundle_usa, \
            self.alternative_flexi_bundle_for_vodafone, self.dark_continental_vodafone_bundle, self.free_profile = setup()
        except mongoengine.errors.NotUniqueError as e:
            self.tearDown()
            raise e
        self.assign_stripe_url = "/v2/assign"

    def tearDown(self):
        self.app.delete()
        self.app_version.delete()
        self.app_version_without_app_id.delete()
        self.app_without_operator.delete()
        self.app_version_without_operator.delete()
        self.operator.delete()
        self.app_email_verification.delete()
        self.user.delete()
        self.keycloak_setting.delete()
        self.email_setting.delete()
        self.vendor.delete()
        self.bundle.delete()
        self.esim_go_mock_bundle.delete()
        self.profile.delete()
        self.unused_user_iccid.delete()
        self.esim_go_bundle.delete()
        self.not_consumed_bundle.delete()
        self.flexi_bundle_without_profile.delete()
        self.user_bundle_log.delete()
        self.esim_go_mock_vendor.delete()
        self.esim_go_vendor.delete()
        Settings.objects(contact_email="<EMAIL>").delete()
        self.reward.delete()
        self.from_user.delete()
        self.vodafone_bundle.delete()
        self.vodafone_vendor.delete()
        Countries.objects.delete()
        CashbackHistory.objects(user_email="<EMAIL>").delete()
        self.un_used_profile.delete()
        self.unused_user_iccid_for_unused_profile.delete()
        Profiles.objects(
            iccid__in=[
                "22345678901234567890",
                "12345678901254567899", "22245678901234567890", "22214678901234567890", "22224678901234567890"]
        ).delete()
        self.esim_go_bundle_usa.delete()
        self.alternative_flexi_bundle_for_vodafone.delete()
        self.dark_continental_vodafone_bundle.delete()
        self.free_profile.delete()
        UserIccid.objects(email="<EMAIL>").delete()

    # @mock.patch(
    #     "b2c_helpers.db_helper.send_email",
    #     side_effect=[
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #     ]
    # )
    # @mock.patch(
    #     "src.global_helpers.db_helper.KeycloakOpenID",
    #     side_effect=[
    #         # test_case_where_bundle_code_length_is_zero
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_app_version_not_found
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_payment_method_not_valid
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_bundle_info_not_found_topup_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_bundle_info_not_found_bundle_code_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_iccid
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_flexi
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_esim_go_mock
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_consumed_all_bundle_units
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_no_enough_organization_balance
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "limited_access", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             user_info_response={
    #                 "sub": "248289761001", "name": "Satoru Gojo", "given_name": "Satoru", "family_name": "Gojo",
    #                 "preferred_username": "<EMAIL>", "role_name": "normal_user", "email": "<EMAIL>",
    #                 "picture": "http://example.com/janedoe/me.jpg", "history_id": "history_id"
    #             }
    #         ),
    #     ]
    # )
    # @mock.patch(
    #     "src.services.allocation_helpers.KeycloakOpenID",
    #     side_effect=[
    #         # test_case_where_bundle_info_not_found_topup_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756,
    #                 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli',
    #                 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo',
    #                 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1',
    #                 'scope': 'email profile',
    #                 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'client_id': 'admin-cli', 'username': 'Satoru'
    #             }
    #         ),
    #         # test_case_where_bundle_info_not_found_bundle_code_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756,
    #                 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli',
    #                 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo',
    #                 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1',
    #                 'scope': 'email profile',
    #                 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'client_id': 'admin-cli', 'username': 'Satoru'
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_iccid
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756,
    #                 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli',
    #                 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo',
    #                 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1',
    #                 'scope': 'email profile',
    #                 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'client_id': 'admin-cli', 'username': 'Satoru'
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_flexi
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756,
    #                 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli',
    #                 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo',
    #                 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1',
    #                 'scope': 'email profile',
    #                 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'client_id': 'admin-cli', 'username': 'Satoru'
    #             }
    #         ),
    #         # test_case_where_get_sim_info_using_esim_go_mock
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756,
    #                 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli',
    #                 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo',
    #                 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1',
    #                 'scope': 'email profile',
    #                 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'client_id': 'admin-cli', 'username': 'Satoru'
    #             }
    #         ),
    #         # test_case_where_consumed_all_bundle_units
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3', 'username': 'Satoru',
    #                 'iss': 'http://localhost:8080/realms/MontyMobile', 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile', 'client_id': 'admin-cli',
    #                 'active': True, 'role_name': 'normal_user', 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_no_enough_organization_balance
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile', 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli',
    #                 'active': True, 'role_name': 'normal_user', 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli',
    #                 'active': True, 'role_name': 'normal_user', 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli', 'active': True, 'role_name': 'normal_user', 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli', 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli', 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli', 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         MockedKeycloakOpenId(
    #             server_url=MOCKED_KEYCLOAK_SERVER_URL, client_id=MOCKED_KEYCLOAK_CLIENT_ID,
    #             realm_name=MOCKED_KEYCLOAK_REALM_NAME, client_secret_key=MOCKED_KEYCLOAK_CLIENT_SECRET_KEY,
    #             introspect_response={
    #                 'exp': 1682638756, 'iat': 1682602756, 'jti': '105ee738-993d-447e-8507-ee0cd4aa36c3',
    #                 'username': 'Satoru', 'iss': 'http://localhost:8080/realms/MontyMobile',
    #                 'sub': '1087f5f9-48ed-4509-8a89-9a077a3d7211',
    #                 'typ': 'Bearer', 'azp': 'admin-cli', 'session_state': '0efd540d-bf9d-452f-b647-24245319ce2c',
    #                 'name': 'zoro', 'given_name': 'Satoru', 'family_name': 'Gojo', 'preferred_username': 'Satoru',
    #                 'email': '<EMAIL>', 'email_verified': False, 'acr': '1', 'scope': 'email profile',
    #                 'client_id': 'admin-cli', 'active': True, 'role_name': 'normal_user',
    #                 'sid': '0efd540d-bf9d-452f-b647-24245319ce2c'
    #             }
    #         ),
    #     ]
    # )
    # @mock.patch(
    #     "app_wapi.wapi_resources.v2_1.global_helpers.email_helpers.send_email",
    #     side_effect=[
    #         # test_case_where_get_sim_info_using_iccid
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_get_sim_info_using_flexi
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_get_sim_info_using_esim_go_mock
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_consumed_all_bundle_units
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_no_enough_organization_balance
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed
    #         # Couldn't authenticate with stripe microservice
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #         # ------URGENT------Couldn't allocate bundle
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         # ------URGENT------Couldn't allocate bundle
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         # ------URGENT------Couldn't allocate bundle
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         # ------URGENT------Couldn't allocate bundle
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         # ------URGENT------Couldn't allocate bundle
    #         lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
    #             email_settings, to_user, subject, body, has_attchament),
    #     ]
    # )
    # @mock.patch(
    #     "requests.request",
    #     side_effect=[
    #         # test_case_where_no_enough_organization_balance
    #         MockedRequest(
    #             method="GET", url=f"{consumer_config.esimgo_url}/v2.2/organisation",
    #             headers={"X-API-Key": consumer_config.esim_go_token}, status_code=200, data={}, response={
    #                 "organisations": [{
    #                     "balance": 0
    #                 }]
    #             }
    #         ),
    #         # test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed
    #         # mock login while trying to pay wallet but switched to card
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=400, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #         # mock login while trying to pay by wallet
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=200, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={"access_token": "Bearer-Token"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         # mock get_esim_go_balance for the above flow
    #         MockedRequest(
    #             method="GET", url=f"{consumer_config.esimgo_url}/v2.2/organisation",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
    #             response={"organisations": [{"balance": 900}]}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=200, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={"access_token": "Bearer-Token"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedRequest(
    #             method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
    #             json={"type": "transaction", "assign": False, "Order": [{
    #                 "type": "bundle", "quantity": 1, "item": "2G-released"
    #             }]}, response={"message": "Bearer-Token"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedRequest(
    #             method="POST", url=f"{consumer_config.esimgo_url}/v2.2/esims/apply",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=400, data={},
    #             json={"iccid": "12345678901234567890", "name": "2G-released", "repeat": 1},
    #             response={"message": "Because Tangero hanging out with deman"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedRequest(
    #             method="GET", url=f"{consumer_config.esimgo_url}/v2.2/organisation",
    #             headers={"X-API-Key": consumer_config.esim_go_token}, status_code=200, data={}, response={
    #                 "organisations": [{
    #                     "balance": 250
    #                 }]
    #             }
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=200, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={"access_token": "Bearer-Token"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedRequest(
    #             method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
    #             json={"type": "transaction", "assign": True, "Order": [{
    #                 "type": "bundle", "quantity": 1, "item": "2G-released"
    #             }]}, response={"orderReference": "0125102154444"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         # first try
    #         MockedRequest(
    #             method="GET", url=f"{consumer_config.esimgo_url}/v2.2/esims/assignments/0125102154444",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=400, data={},
    #             json={}, response={"message": "Esim Go Error Response Sample"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         # second try
    #         MockedRequest(
    #             method="GET", url=f"{consumer_config.esimgo_url}/v2.2/esims/assignments/0125102154444",
    #             headers={'X-API-Key': consumer_config.esim_go_token}, status_code=400, data={},
    #             json={}, response={"message": "Esim Go Error Response Sample"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         # mock login while trying to pay py wallet Vodafone Bundle
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=200, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={"access_token": "Vodafone Access Token from Stripe api"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         MockedRequest(
    #             method="POST", url=f"{consumer_config.vodafone_url}/network/top-up/iccids/{12345678901234567890}",
    #             headers={"ResponseURLs": consumer_config.callback_get_iccid, "Authorization": f"Bearer {consumer_config.vodafone_token}"},
    #             status_code=400, data={}, json={"customReference": "order-number", "bundleID": "5G-Vodafone"},
    #             response={"acknowledgement": "Error"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         MockedRequest(
    #             method="POST", url=consumer_config.stripe_login_url,
    #             headers={}, status_code=200, data={}, json={
    #                 "username": consumer_config.stripe_superadmin_username,
    #                 "password": consumer_config.stripe_superadmin_password,
    #             }, response={"access_token": "Bearer-Toke-To-Buy-Vodafone Bundle"}
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         MockedRequest(
    #             method="POST", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/5G-Vodafone",
    #             headers={
    #                 'ResponseURLs': consumer_config.callback_get_iccid,
    #                 'Authorization': "Bearer " + str(consumer_config.vodafone_token)
    #             }, status_code=404, data={}, json={
    #                 "customReference": "order-number-from-user-bundle-log",
    #             }, response={}
    #         ),
    #     ]
    # )
    # @mock.patch(
    #     "requests.get",
    #     side_effect=[
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #         # stripe_transaction_status while paying using wallet
    #         MockedRequest(
    #             method="GET", url=consumer_config.stripe_transactions_url + "?stripe_request_id=payment_intent.succeeded",
    #             headers={"access_token": "Bearer-Token"}, status_code=200, data={}, json={},
    #             response=[{"status": "succeeded", "event": "payment_intent.succeeded"}]
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow
    #         MockedRequest(
    #             method="GET",
    #             url=consumer_config.stripe_transactions_url + "?stripe_request_id=payment_intent.succeeded",
    #             headers={"access_token": "Bearer-Token"}, status_code=200, data={}, json={},
    #             response=[{"status": "succeeded", "event": "payment_intent.succeeded"}]
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow
    #         MockedRequest(
    #             method="GET",
    #             url=consumer_config.stripe_transactions_url + "?stripe_request_id=payment_intent.succeeded",
    #             headers={"access_token": "Bearer-Token"}, status_code=200, data={}, json={},
    #             response=[{"status": "succeeded", "event": "payment_intent.succeeded"}]
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup
    #         MockedRequest(
    #             method="GET",
    #             url=consumer_config.stripe_transactions_url + "?stripe_request_id=payment_intent.succeeded",
    #             headers={"access_token": "Bearer-Token"}, status_code=200, data={}, json={},
    #             response=[{"status": "succeeded", "event": "payment_intent.succeeded", "title": "Vodafone Top-up"}]
    #         ),
    #         # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
    #         MockedRequest(
    #             method="GET",
    #             url=consumer_config.stripe_transactions_url + "?stripe_request_id=payment_intent.succeeded",
    #             headers={"access_token": "Bearer-Token"}, status_code=200, data={}, json={},
    #             response=[{"status": "succeeded", "event": "payment_intent.succeeded", "title": "Vodafone Bundle"}]
    #         )
    #     ]
    # )
    # @mock.patch(
    #     "requests.post",
    #     # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow
    #     # allocate_flexi_bundle while paying using wallet
    #     side_effect=[
    #         MockedRequest(
    #             method="POST",
    #             # here is temp_token from vendor info document
    #             url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
    #             json={
    #                 "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
    #             }, response={"data": None, "success": False}
    #         )
    #     ]
    # )
    def test_assign_stripe_bundle_failure(self, *args):
        self.skipTest("Old AssignStripe test cases, left for reference")
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer mocked_token"
        }

        @dataclass
        class ApiResponse:
            status: bool
            data: dict
            responseCode: int
            title: str
            message: str
            developerMessage: str
            totalCount: str

        @dataclass
        class AssignStripeTestCaseFailure:
            bundle_code: str
            topup_code: str
            iccid: str
            country_code: str
            payment_method: int
            version_token: str
            promo_code: str
            searched_countries: []
            response: ApiResponse = None

            assert_list: list = field(default_factory=lambda: [])

            @property
            def data(self):
                return json.dumps({
                    "bundle_code": self.bundle_code, "version_token": self.version_token, "topup_code": self.topup_code,
                    "payment_method": self.payment_method, "iccid": self.iccid, "country_code": self.country_code,
                })

        test_case_where_bundle_code_length_is_zero = AssignStripeTestCaseFailure(
            bundle_code="", version_token=self.app_version.version_token, payment_method=1,
            iccid=self.profile.iccid, country_code="EG", topup_code=self.bundle.bundle_code,
            promo_code="", searched_countries=['EG'], response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failed", message="bundle_code cannot be empty.",
                developerMessage="bundle_code cannot be empty.", totalCount=None)
        )
        test_case_where_app_version_not_found = AssignStripeTestCaseFailure(
            bundle_code=self.bundle.bundle_code, version_token="fake-app-version-0-1", payment_method=1,
            iccid=self.profile.iccid, country_code="EG", topup_code=self.bundle.bundle_code,
            promo_code="", searched_countries=['EG'], response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failed", message="invalid version_token.",
                developerMessage="invalid version_token.", totalCount=None)
        )
        test_case_where_payment_method_not_valid = AssignStripeTestCaseFailure(
            bundle_code=self.bundle.bundle_code, version_token=self.app_version.version_token, payment_method=3,
            iccid=self.profile.iccid, country_code="EG", topup_code=self.bundle.bundle_code,
            promo_code="", searched_countries=['EG'], response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failed", message="invalid payment_method.",
                developerMessage="invalid payment_method.", totalCount=None)
        )
        test_case_where_bundle_info_not_found_topup_flow = AssignStripeTestCaseFailure(
            bundle_code="000", version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, promo_code="", searched_countries=['EG'],
            iccid="fake iccid", country_code="EG", topup_code="not-found-bundle-info",
            response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failed", message="Bundle not found",
                developerMessage="", totalCount=None)
        )
        test_case_where_bundle_info_not_found_bundle_code_flow = AssignStripeTestCaseFailure(
            bundle_code="000", version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, promo_code="", searched_countries=['EG'],
            iccid=self.profile.iccid, country_code="EG", topup_code="",
            response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failed", message="Bundle not found",
                developerMessage="", totalCount=None)
        )
        test_case_where_get_sim_info_using_iccid = AssignStripeTestCaseFailure(
            bundle_code=self.bundle.bundle_code, version_token=self.app_version.version_token,
            iccid="123123123123123123123123", country_code="EG", topup_code="", promo_code="", searched_countries=['EG']
            , payment_method=PaymentMethod.credit_card.value, response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failure", message="No profiles available",
                developerMessage="", totalCount=None),
            assert_list=[
                lambda: self.assertTrue(
                    Bundles.objects(bundle_code=self.bundle.bundle_code).first().is_active is False),

            ]
        )
        test_case_where_get_sim_info_using_flexi = AssignStripeTestCaseFailure(
            bundle_code=self.flexi_bundle_without_profile.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, iccid="", country_code="EG",
            topup_code=self.flexi_bundle_without_profile.bundle_code, promo_code="", searched_countries=['EG'],
            response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failure", message="No profiles available",
                developerMessage="", totalCount=None),
            assert_list=[
                lambda: self.assertTrue(
                    Bundles.objects(bundle_code=self.bundle.bundle_code).first().is_active is False),
            ]
        )
        test_case_where_get_sim_info_using_esim_go_mock = AssignStripeTestCaseFailure(
            bundle_code=self.esim_go_mock_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, iccid="", topup_code=self.esim_go_mock_bundle.bundle_code,
            promo_code="", searched_countries=['EG'], country_code="EG", response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failure", message="No profiles available",
                developerMessage="", totalCount=None),
            assert_list=[
                lambda: self.assertTrue(
                    Bundles.objects(bundle_code=self.bundle.bundle_code).first().is_active is False),
            ]
        )
        test_case_where_consumed_all_bundle_units = AssignStripeTestCaseFailure(
            bundle_code=self.bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, iccid="", topup_code=self.bundle.bundle_code,
            promo_code="", searched_countries=['EG'], country_code="EG", response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failure", message="Unavailable bundle",
                developerMessage="", totalCount=None),
            assert_list=[
                lambda: self.assertFalse(
                    Bundles.objects(bundle_code=self.bundle.bundle_code).first().is_active),
                lambda: self.assertEqual(
                    Profiles.objects(
                        vendor_name=self.bundle.vendor_name, status=True, profile_names=self.bundle.profile_names
                    ).first().availability, "Assigned"
                )
            ]
        )
        test_case_where_no_enough_organization_balance = AssignStripeTestCaseFailure(
            bundle_code=self.esim_go_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.credit_card.value, iccid="22245678901234567890",
            topup_code=self.esim_go_bundle.bundle_code, promo_code="", searched_countries=['EG'],
            country_code="EG", response=ApiResponse(
                status=False, data={}, responseCode=2, title="Failure", message="Please try again later",
                developerMessage="Support", totalCount=None),
            assert_list=[
                lambda: self.assertFalse(
                    Bundles.objects(bundle_code=self.bundle.bundle_code).first().is_active)
            ]
        )
        # because he is limited access user, so he cant use his wallet
        test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed = \
            AssignStripeTestCaseFailure(
                bundle_code=self.not_consumed_bundle.bundle_code, version_token=self.app_version.version_token,
                payment_method=PaymentMethod.wallet.value, iccid="22214678901234567890",
                topup_code=self.not_consumed_bundle.bundle_code, promo_code="", searched_countries=['egp'],
                country_code="EG", response=ApiResponse(
                    status=False, data={"access_permission": PaymentMethod.wallet.value}, responseCode=2, title="Failure", developerMessage="",
                    totalCount=None, message="Couldn't authenticate with stripe microservice"),
                assert_list=[
                    lambda: self.assertIsNotNone(
                        UserIccid.objects(
                            bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                            iccid=self.profile.iccid,
                        )),
                    lambda: self.assertIsNotNone(
                        UserBundleLog.objects(
                            bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                            payment_method=PaymentMethod.credit_card.value)
                    )
                ]
            )
        test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow = AssignStripeTestCaseFailure(
            bundle_code=self.not_consumed_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.wallet.value, iccid="22224678901234567890",
            topup_code=self.not_consumed_bundle.bundle_code, promo_code="", searched_countries=['egp'],
            country_code="EG", response=ApiResponse(
                status=False, data={"access_permission": 1}, responseCode=3, title="Failure", developerMessage="",
                totalCount=None, message="Failed in allocation , please contact the support"),
            assert_list=[
                lambda: self.assertIsNotNone(
                    UserIccid.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        iccid=self.profile.iccid,
                    ),
                    "we are creating user iccid right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    UserBundleLog.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        paid_amount_wallet=self.not_consumed_bundle.retail_price
                    ),
                    "we are creating user bundle log right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    TransactionLogs.objects(
                        order_number=UserBundleLog.objects(
                            bundle_code=self.not_consumed_bundle.bundle_code).first().order_number),
                    "we are creating TransactionLogs object with status true in payment_callback"
                )
            ]
        )
        test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow = AssignStripeTestCaseFailure(
            bundle_code=self.esim_go_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.wallet.value, iccid="22245678901234567890", promo_code="",
            searched_countries=['egp'], topup_code=self.esim_go_bundle.bundle_code, country_code="EG", response=ApiResponse(
                status=False, data={"access_permission": 1}, responseCode=3, title="Failure", developerMessage="",
                totalCount=None, message="Failed in allocation , please contact the support"),
            assert_list=[
                lambda: self.assertIsNotNone(
                    UserIccid.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        iccid=self.profile.iccid,
                    ),
                    "we are creating user iccid right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    UserBundleLog.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        paid_amount_wallet=self.not_consumed_bundle.retail_price
                    ),
                    "we are creating user bundle log right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    TransactionLogs.objects(
                        order_number=UserBundleLog.objects(
                            bundle_code=self.not_consumed_bundle.bundle_code).first().order_number),
                    "we are creating TransactionLogs object with status true in payment_callback"
                )
            ]
        )
        test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow = AssignStripeTestCaseFailure(
            bundle_code=self.esim_go_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.wallet.value, iccid=self.profile.iccid, promo_code="",
            searched_countries=['egp'], topup_code="", country_code="EG", response=ApiResponse(
                status=False, data={"access_permission": 1}, responseCode=3, title="Failure", developerMessage="",
                totalCount=None, message="Failed in allocation , please contact the support"),
            assert_list=[
                lambda: self.assertIsNotNone(
                    UserIccid.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        iccid=self.profile.iccid,
                    ),
                    "we are creating user iccid right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    UserBundleLog.objects(
                        bundle_code=self.not_consumed_bundle.bundle_code, country_code="EG",
                        paid_amount_wallet=self.not_consumed_bundle.retail_price
                    ),
                    "we are creating user bundle log right before letting user pay"
                ),
                lambda: self.assertIsNotNone(
                    TransactionLogs.objects(
                        order_number=UserBundleLog.objects(
                            bundle_code=self.not_consumed_bundle.bundle_code).first().order_number),
                    "we are creating TransactionLogs object with status true in payment_callback"
                )
            ]
        )
        test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup = AssignStripeTestCaseFailure(
            bundle_code=self.vodafone_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.wallet.value, iccid=self.profile.iccid, promo_code="",
            searched_countries=['egp'], topup_code=self.vodafone_bundle.bundle_code, country_code="EG", response=ApiResponse(
                status=False, data={"access_permission": 1}, responseCode=3, title="Failure", developerMessage="",
                totalCount=None, message="Failed in allocation , please contact the support")
        )
        test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow = AssignStripeTestCaseFailure(
            bundle_code=self.vodafone_bundle.bundle_code, version_token=self.app_version.version_token,
            payment_method=PaymentMethod.wallet.value, iccid=self.profile.iccid, promo_code="",
            searched_countries=['egp'], topup_code="", country_code="EG",
            response=ApiResponse(
                status=False, data={"access_permission": 1}, responseCode=3, title="Failure", developerMessage="",
                totalCount=None, message="Failed in allocation , please contact the support")
        )

        test_cases = [
            test_case_where_bundle_code_length_is_zero,
            test_case_where_app_version_not_found,
            test_case_where_payment_method_not_valid,
            test_case_where_bundle_info_not_found_topup_flow,
            test_case_where_bundle_info_not_found_bundle_code_flow,
            test_case_where_get_sim_info_using_iccid,
            test_case_where_get_sim_info_using_flexi,
            test_case_where_get_sim_info_using_esim_go_mock,
            test_case_where_consumed_all_bundle_units,
            test_case_where_no_enough_organization_balance,
            test_case_where_limited_access_user_try_to_pay_with_wallet_switched_to_stripe_and_login_failed,
            test_case_where_normal_user_want_pay_by_wallet_has_reward_before_flexi_flow,
            test_case_where_normal_user_want_pay_by_wallet_has_reward_before_topup_esimgo_flow,
            test_case_where_normal_user_want_pay_by_wallet_has_reward_before_esimgo_flow,
            # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow_topup,
            # test_case_where_normal_user_want_pay_by_wallet_has_reward_before_vodafone_flow
        ]

        for test_case in test_cases:
            self.esim_go_bundle.update(is_active=True)
            response = self.client.post(self.assign_stripe_url, headers=headers, data=test_case.data)
            self.assertEqual(response.json['status'], test_case.response.status)
            self.assertEqual(response.json['data'], test_case.response.data)
            self.assertEqual(response.json['title'], test_case.response.title)
            self.assertEqual(response.json['message'], test_case.response.message)
            self.assertEqual(response.json['developerMessage'], test_case.response.developerMessage)
            self.assertEqual(response.json['totalCount'], test_case.response.totalCount)
            for assert_case in test_case.assert_list:
                assert_case()

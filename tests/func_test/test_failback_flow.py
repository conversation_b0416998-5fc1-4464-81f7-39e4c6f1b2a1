import unittest
from dataclasses import dataclass
from datetime import datetime, timedelta
from hashlib import md5
from unittest import mock

import requests
from app_models.consumer_models import TransactionLogs, Profiles, UserBundleLog, UserIccid, \
    PaymentLogsTransactions, Vendors, CashbackHistory, Bundles, region_names_, regions_, TopupBundles
from requests import JSONDecodeError
from urllib3.exceptions import NewConnectionError
from b2c_helpers.constaints import VODAFONE_VENDOR, FLEXIROAM_VENDOR

from src import app
from app_models.main_models import Settings, NotificationLogs, HistoryLogs
from flask import json

from src.global_helpers.db_helper import add_user_bundle_log, add_user_iccid
from src.global_helpers.utils import random_url_token, generate_temp_otp
from src.global_helpers.email_helpers import send_email_invoice
from b2c_helpers.db_helper import  send_fcm_notification

import mongoengine.errors

from src.global_helpers.db_helper import User<PERSON>ccidStatus, \
    ESIM_GO_VENDOR_NAME
from src.services.allocation_helpers import PaymentMethod
from instance import consumer_config
from tests.setup_test_data_for_assign_bundle import setup
from b2c_helpers.mock_send_email import DummySendEmail
from tests.func_test.mock_thread import MockThread
from tests.func_test.mocked_requests import MockedRequest


@dataclass()
class StripeCallbackRequest:
    stripe_request_id: str
    event: str
    email: str
    livemode: str
    name: str
    balance: int
    amount_received: float
    outcome: dict
    paid: bool
    payment_intent: str
    failure_balance_transaction: int
    failure_code: str
    failure_message: str
    status: str
    receipt_url: str
    payment_method_types: str
    customer: str
    client_secret: str
    created: str

    response: dict

    assert_list: list

    @property
    def data(self):
        return json.dumps({
            "stripe_request_id": self.stripe_request_id, "event": self.event, "customer": self.customer,
            "email": self.email, "livemode": self.livemode, "name": self.name, "balance": self.balance,
            "amount_received": self.amount_received, "outcome": self.outcome, "paid": self.paid,
            "payment_intent": self.payment_intent,
            "failure_balance_transaction": self.failure_balance_transaction,
            "failure_code": self.failure_code, "failure_message": self.failure_message, "status": self.status,
            "receipt_url": self.receipt_url, "payment_method_types": self.payment_method_types,
            "client_secret": self.client_secret, "created": self.created
    })

# @unittest.skip("Behvaiour of the API under test drastically changed, this test case is extremely outdated, beyond the possibility of salvaging it, requires a completely new up to date test cases")
# class FailBackAssignBundleTestCases(unittest.TestCase):
#     CUSTOMER_EMAIL = "<EMAIL>"
#     NEW_CONNECTION_ERROR_MESSAGE = """HTTPSConnectionPool(host='devapi.flexiroam.com', port=443): Max retries exceeded \
#     with url: /plan/load/v1 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7f2e4c3cca60>
#     : Failed to establish a new connection: [Errno 111] Connection refused'))"""
#
#     def setUp(self):
#         self.client = app.test_client()
#
#         try:
#             self.app, self.app_version, self.app_version_without_app_id, self.app_without_operator, \
#             self.app_version_without_operator, self.operator, self.app_email_verification, self.user, \
#             self.keycloak_setting, self.email_setting, self.vendor, self.bundle, self.esim_go_mock_bundle, \
#             self.profile, self.unused_user_iccid, self.esim_go_bundle, self.not_consumed_bundle, \
#             self.flexi_bundle_without_profile, self.user_bundle_log, self.esim_go_mock_vendor, self.esim_go_vendor, \
#             self.reward, self.from_user, self.vodafone_vendor, self.vodafone_bundle, \
#             self.unused_user_iccid_for_unused_profile, self.un_used_profile, self.esim_go_bundle_usa, \
#             self.alternative_flexi_bundle_for_vodafone, self.dark_continental_vodafone_bundle, self.free_profile \
#                 = setup()
#             self.flexi_bundle_with_only_one_sim_available = Bundles(**{
#                 "vendor_name": self.vendor.vendor_name, "bundle_code": "44-EG-F", "bundle_name": "44-EG-F",
#                 "bundle_marketing_name": "fastest-44-EG-F", "category_name": "1", "region_code": region_names_[1],
#                 "retail_price": 100, "supplier_vendor": f"{self.vendor.id}", "validity_amount": "1",
#                 "region_name": regions_[1], "bundle_vendor_code": "44-EG-F", 'consumed_unit': 3,
#                 "bundle_vendor_name": self.vendor.vendor_name, "bundle_category": "country", 'allocated_unit': 4,
#                 "rate_revenue": 1, "create_datetime": datetime.utcnow(), "bundle_duration": 30,
#                 "unit_price": "20", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
#                 "profile_names": "te", "country_list": ["WER"], "country_code_list": ["EG"], "is_active": True
#             }).save()
#         except mongoengine.errors.NotUniqueError as e:
#             self.tearDown()
#             raise e
#
#         self.stripe_callback_url = "/stripe-callback"
#
#     def tearDown(self):
#         self.app.delete()
#         self.app_version.delete()
#         self.app_version_without_app_id.delete()
#         self.app_without_operator.delete()
#         self.app_version_without_operator.delete()
#         self.operator.delete()
#         self.app_email_verification.delete()
#         self.user.delete()
#         self.keycloak_setting.delete()
#         self.email_setting.delete()
#         self.vendor.delete()
#         self.bundle.delete()
#         self.esim_go_mock_bundle.delete()
#         self.profile.delete()
#         self.unused_user_iccid.delete()
#         self.esim_go_bundle.delete()
#         self.not_consumed_bundle.delete()
#         self.flexi_bundle_without_profile.delete()
#         self.user_bundle_log.delete()
#         self.esim_go_mock_vendor.delete()
#         self.esim_go_vendor.delete()
#         Settings.objects(contact_email="<EMAIL>").delete()
#         self.reward.delete()
#         self.from_user.delete()
#         self.vodafone_bundle.delete()
#         self.vodafone_vendor.delete()
#         TransactionLogs.objects(order_number="whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi").delete()
#         self.un_used_profile.delete()
#         self.unused_user_iccid_for_unused_profile.delete()
#         UserBundleLog.objects(email=self.CUSTOMER_EMAIL).delete()
#         UserIccid.objects(email=self.CUSTOMER_EMAIL).delete()
#         Profiles.objects(
#             iccid__in=[
#                 "22345678901234567890",
#                 "12345678901254567899", "22245678901234567890", "22214678901234567890", "22224678901234567890"]
#         ).delete()
#         NotificationLogs.objects(email=self.CUSTOMER_EMAIL).delete()
#         HistoryLogs.objects(email=self.CUSTOMER_EMAIL).delete()
#         self.esim_go_bundle_usa.delete()
#         TransactionLogs.objects(order_number__in=["we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone", "we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative"]).delete()
#         self.alternative_flexi_bundle_for_vodafone.delete()
#         self.dark_continental_vodafone_bundle.delete()
#         self.free_profile.delete()
#         CashbackHistory.objects(user_email=self.CUSTOMER_EMAIL).delete()
#
#         UserIccid.objects(email="<EMAIL>").delete()
#         self.flexi_bundle_with_only_one_sim_available.delete()
#         TopupBundles.objects(email=self.CUSTOMER_EMAIL).delete()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi flow>"}
#             ),
#             # test_case_where_flexi_failed_due_to_connection_error
#             # here we switched to esim go vendor because error happened in flexi side
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response={"orderReference": "0125102154444"}
#             ),
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockedRequest(
#                 method="GET", url=f"{consumer_config.esimgo_url}/v2.2/esims/assignments/0125102154444",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={}, response={"message": "Esim Go Error Response Sample"},
#                 text="12345678901254567899,SS:00:W0:R0:Q0:T1,rsp.test.client.com",
#                 content="ICCID,Matching ID,RSP URL,\n12345678901254567899,SS:00:W0:R0:Q0:T1,rsp.test.client.com"
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response="", raise_error=True, error_instance=requests.exceptions.Timeout("TIMEOUT")
#             )
#         ]
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             # test_case_where_flexi_failed_due_to_connection_error
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success",
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     def test_assign_flexi_bundle_failed_due_to_timeout_error(self, *args):
#         """
#             we will switch to esim go because flexi not available for assigning bundles
#         """
#         dummy_sendemail = DummySendEmail()
#
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.bundle.bundle_code, "country_code": "EG",
#             "amount": self.bundle.retail_price, "paid_amount_credit_card": self.bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
#             "order_number": stripe_client_secret, "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.bundle.currency_code,
#             "validity_days": self.bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value,
#             "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_flexi_failed_due_to_connection_error = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate flexi bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_code, self.esim_go_bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.bundle_code, self.esim_go_bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     # 150 is defined in setup for auth file
#                     self.user.balance, 150,
#                     "balance should not be changed as long as user not payed using his wallet"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.plan_uid, "0125102154444"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.status, UserIccidStatus.USED
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.iccid, "12345678901254567899"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.activation_code, "SS:00:W0:R0:Q0:T1"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.original_iccid, self.profile.iccid
#                 ),
#                 lambda: self.assertEqual(
#                     self.esim_go_bundle.allocated_unit, 31
#                 ),
#                 lambda: self.assertTrue(
#                     user_bundle_log.payment_status
#                 ),
#                 lambda: self.assertIsNotNone(
#                     NotificationLogs.objects(
#                         email=self.CUSTOMER_EMAIL, transaction="BuyBundle"
#                     ).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         iccid="12345678901254567899", bundle_name=self.esim_go_bundle.bundle_name,
#                         bundle_marketing_name=self.esim_go_bundle.bundle_marketing_name,
#                         price=self.bundle.retail_price, bundle_code=self.esim_go_bundle.bundle_code,
#                         data_amount=self.esim_go_bundle.data_amount, data_unit=self.esim_go_bundle.data_unit,
#                         bundle_duration=self.esim_go_bundle.bundle_duration, order_number=user_bundle_log.order_number
#                     ).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=flexiroam_vendor_name, is_active=True).count(), 0,
#                     "if vendor responses with timeout or connection error we should mark all bundles from this vendor"
#                     " to be not active"
#                 )
#             ]
#         )
#
#         test_cases = [
#             test_case_where_flexi_failed_due_to_connection_error
#         ]
#
#         for test_case in test_cases:
#             response = self.client.post(self.stripe_callback_url, headers=headers, data=test_case.data)
#
#             self.assertEqual(len(dummy_sendemail.outbox), 2)
#
#             self.assertEqual(
#                 dummy_sendemail.outbox[0].header,
#                 'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#                 + '\n' + f"Subject:------URGENT------ Vendor: {flexiroam_vendor_name} deactivated" + ' \n'
#             )
#             self.assertIn(
#                 f"vendor: {flexiroam_vendor_name} deactivated due to: TIMEOUT",
#                 dummy_sendemail.outbox[0].msg,
#             )
#
#             self.assertEqual(response.status_code, 200)
#             self.assertDictEqual(response.json, test_case.response)
#             user_iccid.reload()
#             user_bundle_log.reload()
#             self.esim_go_bundle.reload()
#             for assert_case in test_case.assert_list:
#                 assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"}
#             ),
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response="", raise_error=True,
#                 error_instance=NewConnectionError("self", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 #url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 url=None,
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not esim go bundle because we switched to flexi due to error from esim go
#                         "plan_uid": "4G-released",
#                     }
#                 }
#             ),
#             MockedRequest(
#                 method="POST",
#                 # mock consumption from flexi
#                 url=f"{consumer_config.flexiroam_url}/plan/simplan/v1'", headers={"token": "temp-access-token"},
#                 status_code=200, data={}, json={"sku": "[profile-id-unique]"}, response={
#                     "data": [
#                         {
#                             "end_date": datetime.utcnow() + timedelta(days=10)
#                         }
#                     ]
#                 }
#             ),
#
#         ]
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success"
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     def test_assign_esim_go_bundle_failed_due_to_timeout_error(self, *args):
#         """
#             esim go failed, so we will try to assign bundle from flexi to user.
#         """
#         dummy_sendemail = DummySendEmail()
#
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "EG",
#             "amount": self.esim_go_bundle.retail_price, "paid_amount_credit_card": self.esim_go_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.free_profile.iccid,
#             "activation_code": self.free_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_esim_go_failed_due_to_connection_error = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim go bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=ESIM_GO_VENDOR_NAME, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertEqual(
#                     # after successful buying bundle from flexi we are updating user iccid to be expired by response
#                     # from flexi consumption api
#                     user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(days=10)).date()
#                 ),
#                 lambda: self.assertEqual(
#                     # 150 is defined in setup for auth file
#                     self.user.balance, 150,
#                     "balance should not be changed as long as user not payed using his wallet"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.status, UserIccidStatus.USED
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.plan_uid, self.bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.iccid, self.profile.iccid
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.original_iccid, self.free_profile.iccid
#                 ),
#                 lambda: self.assertIsNotNone(
#                     user_iccid.qr_code_path
#                 ),
#                 lambda: self.assertIsNotNone(
#                     NotificationLogs.objects(
#                         email=self.CUSTOMER_EMAIL, transaction="BuyBundle"
#                     ).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         bundle_name=self.bundle.bundle_name,
#                         bundle_marketing_name=self.bundle.bundle_marketing_name,
#                         price=self.esim_go_bundle.retail_price, bundle_code=self.bundle.bundle_code,
#                         data_amount=self.bundle.data_amount, data_unit=self.bundle.data_unit,
#                         bundle_duration=self.bundle.bundle_duration, order_number=user_bundle_log.order_number
#                     ).first()
#                 ),
#                 lambda: self.assertTrue(
#                     user_bundle_log.payment_status
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_code, self.bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.data_amount, self.bundle.data_amount
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.country_code_list, self.bundle.country_code_list
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_duration, self.bundle.bundle_duration
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_esim_go_failed_due_to_connection_error.data)
#         self.assertEqual(response.status_code, 200)
#         self.assertDictEqual(response.json, test_case_where_esim_go_failed_due_to_connection_error.response)
#         user_iccid.reload()
#         user_bundle_log.reload()
#
#         self.assertEqual(len(dummy_sendemail.outbox), 2)
#
#         self.assertEqual(
#             dummy_sendemail.outbox[0].header,
#             'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#             + '\n' + f"Subject:------URGENT------ Vendor: {ESIM_GO_VENDOR_NAME} deactivated" + ' \n'
#         )
#         self.assertIn(
#             f"vendor: {ESIM_GO_VENDOR_NAME} deactivated due to",
#             dummy_sendemail.outbox[0].msg,
#         )
#         #
#         # self.assertEqual(
#         #     dummy_sendemail.outbox[1].header,
#         #     'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#         #     + '\n' + f"Subject:------URGENT------ Bundle: {self.bundle.bundle_vendor_code} deactivated" + ' \n'
#         # )
#         # self.assertIn(
#         #     "No Available SIMs",
#         #     dummy_sendemail.outbox[1].msg,
#         # )
#
#         for assert_case in test_case_where_esim_go_failed_due_to_connection_error.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"}
#             ),
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response="", raise_error=True,
#                 error_instance=NewConnectionError("self", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not esim go bundle because we switched to flexi due to error from esim go
#                         "plan_uid": "4G-released",
#                     }
#                 }
#             ),
#             MockedRequest(
#                 method="POST",
#                 # mock consumption from flexi
#                 url=f"{consumer_config.flexiroam_url}/plan/simplan/v1'", headers={"token": "temp-access-token"},
#                 status_code=200, data={}, json={"sku": "[profile-id-unique]"}, response={
#                     "data": [
#                         {
#                             "end_date": datetime.utcnow() + timedelta(days=10)
#                         }
#                     ]
#                 }
#             ),
#
#         ]
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification(
#                     settings="setting model",
#                     fcm_token="",
#                     ios_version=False,
#                     # cashback_percent from settings
#                     data={"category": 4, "cashback_percent": 10},
#                     status="success"
#                 ),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#         ]
#     )
#     def test_assign_esim_go_bundle_failed_due_to_timeout_error_without_available_sim(self, *args):
#         """
#             esim go failed, so we will try to assign bundle from flexi to user.
#         """
#         dummy_sendemail = DummySendEmail()
#
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "EG",
#             "amount": self.esim_go_bundle.retail_price, "paid_amount_credit_card": self.esim_go_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.free_profile.iccid,
#             "activation_code": self.free_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_esim_go_failed_due_to_connection_error = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim go bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=ESIM_GO_VENDOR_NAME, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertEqual(
#                     # after successful buying bundle from flexi we are updating user iccid to be expired by response
#                     # from flexi consumption api
#                     user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(days=10)).date()
#                 ),
#                 lambda: self.assertEqual(
#                     # 150 is defined in setup for auth file
#                     self.user.balance, 150,
#                     "balance should not be changed as long as user not payed using his wallet"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.status, UserIccidStatus.USED
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.plan_uid, self.bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.iccid, self.profile.iccid
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.original_iccid, self.free_profile.iccid
#                 ),
#                 lambda: self.assertIsNotNone(
#                     user_iccid.qr_code_path
#                 ),
#                 lambda: self.assertIsNotNone(
#                     NotificationLogs.objects(
#                         email=self.CUSTOMER_EMAIL, transaction="BuyBundle"
#                     ).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         bundle_name=self.bundle.bundle_name,
#                         bundle_marketing_name=self.bundle.bundle_marketing_name,
#                         price=self.esim_go_bundle.retail_price, bundle_code=self.bundle.bundle_code,
#                         data_amount=self.bundle.data_amount, data_unit=self.bundle.data_unit,
#                         bundle_duration=self.bundle.bundle_duration, order_number=user_bundle_log.order_number
#                     ).first()
#                 ),
#                 lambda: self.assertTrue(
#                     user_bundle_log.payment_status
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_code, self.bundle.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.data_amount, self.bundle.data_amount
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.country_code_list, self.bundle.country_code_list
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_duration, self.bundle.bundle_duration
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_esim_go_failed_due_to_connection_error.data)
#         self.assertEqual(response.status_code, 200)
#         self.assertDictEqual(response.json, test_case_where_esim_go_failed_due_to_connection_error.response)
#         user_iccid.reload()
#         user_bundle_log.reload()
#
#         self.assertEqual(len(dummy_sendemail.outbox), 2)
#
#         self.assertEqual(
#             dummy_sendemail.outbox[0].header,
#             'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#             + '\n' + f"Subject:------URGENT------ Vendor: {ESIM_GO_VENDOR_NAME} deactivated" + ' \n'
#         )
#         self.assertIn(
#             f"vendor: {ESIM_GO_VENDOR_NAME} deactivated due to",
#             dummy_sendemail.outbox[0].msg,
#         )
#         #
#         # self.assertEqual(
#         #     dummy_sendemail.outbox[1].header,
#         #     'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#         #     + '\n' + f"Subject:------URGENT------ Bundle: {self.bundle.bundle_vendor_code} deactivated" + ' \n'
#         # )
#         # self.assertIn(
#         #     f"No Available SIMs",
#         #     dummy_sendemail.outbox[1].msg,
#         # )
#
#         for assert_case in test_case_where_esim_go_failed_due_to_connection_error.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi not alternative>"}
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi_no_alternative",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi no alternative>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "5G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response="", raise_error=True, error_instance=NewConnectionError("self", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#         ]
#     )
#     def test_assign_flexi_bundle_failed_and_we_didnt_find_alternative_bundles(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.not_consumed_bundle.bundle_code, "country_code": "EG",
#             "amount": self.not_consumed_bundle.retail_price, "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.not_consumed_bundle.bundle_duration),
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.not_consumed_bundle.currency_code,
#             "validity_days": self.not_consumed_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_flexi_failed_due_to_connection_error_but_no_alternative_bundles = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi_no_alternative", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.not_consumed_bundle.retail_price, outcome={}, paid=True, payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(), response={},
#             # system state when we call stripe callback and failed to allocate flexi bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=flexiroam_vendor_name, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_flexi_failed_due_to_connection_error_but_no_alternative_bundles.data)
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_failed_due_to_connection_error_but_no_alternative_bundles.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esim go not alternative>"}
#             ),
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-fast"
#                 }]}, response="", raise_error=True,
#                 error_instance=NewConnectionError("message: ", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi no alternative>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     def test_assign_esim_go_bundle_failed_and_we_didnt_find_alternative_bundles(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle_usa.bundle_code, "country_code": "USA",
#             "amount": self.esim_go_bundle_usa.retail_price, "paid_amount_credit_card": self.esim_go_bundle_usa.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle_usa.bundle_duration),
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle_usa.currency_code,
#             "validity_days": self.esim_go_bundle_usa.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_esim_go_failed_due_to_connection_error_and_no_alternative = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim go bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=ESIM_GO_VENDOR_NAME, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_esim_go_failed_due_to_connection_error_and_no_alternative.data)
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_esim_go_failed_due_to_connection_error_and_no_alternative.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament)
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 #url=consumer_config.stripe_login_url,
#                 url=None,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <vodafone flow>"}
#             ),
#             # simulate there is NewConnectionError with vodafone apis
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/5G-Vodafone",
#                 headers={
#                     'ResponseURLs': consumer_config.callback_get_iccid,
#                     'Authorization': "Bearer " + str(consumer_config.vodafone_token)
#                 }, status_code=200, data={}, json={"customReference": "we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone"},
#                 response="", raise_error=True,
#                 error_instance=NewConnectionError("Message: ", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 # url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone",
#                 url=None,
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <vodafone flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1",
#                 headers={"token": "temp_token"},
#                 status_code=200, data={}, json={
#                     # plan_code is bundle code for flexi because vodafone not responding
#                     "sku": "[profile-id-unique]", "plan_code": "5G-releasedAlternative", "plan_start_type_id": "1", "discount": ""
#                 }, method='POST', response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not vodafone bundle because we switched to flexi due to error in vodafone api
#                         "plan_uid": "5G-releasedAlternative",
#                     }
#                 }
#             ),
#             MockedRequest(
#                 method="POST",
#                 # mock consumption from flexi
#                 url=f"{consumer_config.flexiroam_url}/plan/simplan/v1'", headers={"token": "temp-access-token"},
#                 status_code=200, data={}, json={"sku": "[profile-id-unique]"}, response={
#                     "data": [
#                         {
#                             "end_date": datetime.utcnow() + timedelta(days=10)
#                         }
#                     ]
#                 }
#             ),
#             MockedRequest(
#                 method="POST",
#                 # mock consumption from flexi
#                 url=f"{consumer_config.flexiroam_url}/plan/simplan/v1'", headers={"token": "temp-access-token"},
#                 status_code=200, data={}, json={"sku": "[profile-id-unique]"}, response={
#                     "data": [
#                         {
#                             "end_date": datetime.utcnow() + timedelta(days=10)
#                         }
#                     ]
#                 }
#             ),
#         ]
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             )
#         ]
#     )
#     def test_assign_vodafone_bundle_failed_due_to_timeout_error(self, *args):
#         """
#             here we will switch to flexi because the error that raise from vodafone side
#         """
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.vodafone_bundle.bundle_code, "country_code": "USA",
#             "paid_amount_credit_card": self.vodafone_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.vodafone_bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.vodafone_bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.vodafone_bundle.currency_code,
#             "validity_days": self.vodafone_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value, "order_number": stripe_client_secret,
#             "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.free_profile.iccid,
#             "activation_code": self.free_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_vodafone_failed_to_assign_bundle_due_to_new_connection_error = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate Vodafone bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=VODAFONE_VENDOR, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertEqual(
#                     # after successful buying bundle from flexi we are updating user iccid to be expired by response
#                     # from flexi consumption api
#                     user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(days=10)).date()
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.status, UserIccidStatus.USED
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.plan_uid, "5G-releasedAlternative"
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.iccid, self.profile.iccid,
#                 ),
#                 lambda: self.assertEqual(
#                     user_iccid.activation_code, self.profile.matching_id,
#                 ),
#                 lambda: self.assertEqual(
#                   user_bundle_log.bundle_code, self.alternative_flexi_bundle_for_vodafone.bundle_code
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.data_amount, self.alternative_flexi_bundle_for_vodafone.data_amount
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.country_code_list, self.alternative_flexi_bundle_for_vodafone.country_code_list
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.bundle_duration, self.alternative_flexi_bundle_for_vodafone.bundle_duration
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.amount, self.alternative_flexi_bundle_for_vodafone.retail_price
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.validity_days, self.alternative_flexi_bundle_for_vodafone.bundle_duration
#                 ),
#                 lambda: self.assertEqual(
#                     user_bundle_log.validy_date.date(),
#                     (datetime.today() + timedelta(days=self.alternative_flexi_bundle_for_vodafone.bundle_duration)).date()
#                 ),
#                 lambda: self.assertTrue(
#                     user_bundle_log.payment_status
#                 ),
#                 lambda: self.assertIsNotNone(
#                     NotificationLogs.objects(
#                         email=self.CUSTOMER_EMAIL, transaction="BuyBundle"
#                     ).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     HistoryLogs.objects(
#                         iccid=self.profile.iccid, bundle_name=self.alternative_flexi_bundle_for_vodafone.bundle_name,
#                         bundle_marketing_name=self.alternative_flexi_bundle_for_vodafone.bundle_marketing_name,
#                         price=self.vodafone_bundle.retail_price,
#                         bundle_code=self.alternative_flexi_bundle_for_vodafone.bundle_code,
#                         data_amount=self.alternative_flexi_bundle_for_vodafone.data_amount,
#                         data_unit=self.alternative_flexi_bundle_for_vodafone.data_unit,
#                         bundle_duration=self.alternative_flexi_bundle_for_vodafone.bundle_duration,
#                         order_number=user_bundle_log.order_number
#                     ).first()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_vodafone_failed_to_assign_bundle_due_to_new_connection_error.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_vodafone_failed_to_assign_bundle_due_to_new_connection_error.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <DK vodafone flow>"}
#             ),
#             # simulate there is NewConnectionError with vodafone apis
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/5G-DK-Vodafone",
#                 headers={
#                     'ResponseURLs': consumer_config.callback_get_iccid,
#                     'Authorization': "Bearer " + str(consumer_config.vodafone_token)
#                 }, status_code=200, data={}, json={"customReference": "we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone"},
#                 response="", raise_error=True,
#                 error_instance=NewConnectionError("Message: ", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <dk-vodafone flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#         ]
#     )
#     def test_assign_vodafone_bundle_failed_and_we_didnt_find_alternative_bundles(self, *args):
#         self.skipTest("outdated")
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.dark_continental_vodafone_bundle.bundle_code,
#             "paid_amount_credit_card": self.dark_continental_vodafone_bundle.retail_price, "country_code": "DK",
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.dark_continental_vodafone_bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.dark_continental_vodafone_bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.dark_continental_vodafone_bundle.currency_code,
#             "validity_days": self.dark_continental_vodafone_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value, "order_number": stripe_client_secret,
#             'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_vodafone_failed_due_to_connection_error_and_no_alternative = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate Vodafone bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=VODAFONE_VENDOR, is_active=True).count(), 0
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_vodafone_failed_due_to_connection_error_and_no_alternative.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_vodafone_failed_due_to_connection_error_and_no_alternative.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.db_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
#                 email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"}
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             # test_case_where_flexi_failed_due_to_timeout_error
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response="", raise_error=True, error_instance=requests.exceptions.Timeout("TIMEOUT")
#             )
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#         ]
#     )
#     def test_assign_flexi_top_up_bundle_failed(self, *args):
#         """
#             in case there is failure in assign top-up bundle we will not retry at all
#         """
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi-top-up"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.bundle.bundle_code, "country_code": "USA",
#             "paid_amount_credit_card": self.bundle.retail_price, "payment_category": PaymentMethod.credit_card.value,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": self.bundle.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.bundle.currency_code,
#             "validity_days": self.bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         test_case_where_flexi_top_up_failed = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate flexi topup bundle
#             payment_intent="payment_intent_id", assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertFalse(
#                     self.bundle.is_active
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_details="allocate_per_vendor",
#                         method_failure_message="TIMEOUT"
#                     ).first()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_flexi_top_up_failed.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.bundle.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_top_up_failed.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.db_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
#                 email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esim_gp topup flow>"}
#             ),
#             # mock topup_esimgo_bundle
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.esimgo_url}/v2.2/orders", headers={"token": ""}, status_code=400, data={},
#                 json={
#                     "type": "transaction", "assign": False,
#                     "Order": [{"type": "bundle", "quantity": 1, "item": "2G-released"}]
#                 }, response="", raise_error=True, error_instance=requests.exceptions.Timeout("TIMEOUT")
#             )
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_topup",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <esim_go topup flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#         ]
#     )
#     def test_assign_esim_go_top_up_bundle_failed(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = 'we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go-top-up'
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "USA",
#             "paid_amount_credit_card": self.esim_go_bundle.retail_price, "payment_category": PaymentMethod.credit_card.value,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": self.esim_go_bundle.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.esim_go_bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         test_case_where_flexi_top_up_failed = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_topup", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim_go topup bundle
#             payment_intent="payment_intent_id", assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertFalse(
#                     self.esim_go_bundle.is_active
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_details="allocate_per_vendor",
#                         method_failure_message=""
#                     ).first()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_flexi_top_up_failed.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.esim_go_bundle.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_top_up_failed.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.db_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail(
#                 email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <vodafone topup flow>"}
#             ),
#             # mock topup_vodafone_bundle
#             MockedRequest(
#                 method="PUT",
#                 url=f"{consumer_config.vodafone_url}/network/top-up/iccids/12345678901234567890",
#                 headers={
#                     'ResponseURLs': consumer_config.callback_get_iccid,
#                     'Authorization': "Bearer " + str(consumer_config.vodafone_token)
#                 },
#                 status_code=400, data={},
#                 json={
#                     "customReference": "we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone-top-up",
#                     "bundleID": "5G-Vodafone"
#                 }, response="", raise_error=True, error_instance=requests.exceptions.Timeout("TIMEOUT")
#             )
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone_topup",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <vodafone topup flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     def test_assign_vodafone_top_up_bundle_failed(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone-top-up"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.vodafone_bundle.bundle_code, "country_code": "USA",
#             "paid_amount_credit_card": self.vodafone_bundle.retail_price, "payment_category": PaymentMethod.credit_card.value,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": self.vodafone_bundle.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.vodafone_bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.vodafone_bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.vodafone_bundle.currency_code,
#             "validity_days": self.vodafone_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         test_case_where_flexi_top_up_failed = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_vodafone_topup", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim_go topup bundle
#             payment_intent="payment_intent_id", assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertFalse(
#                     self.vodafone_bundle.is_active
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_details="allocate_per_vendor",
#                         method_failure_message=None
#                     ).first()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_flexi_top_up_failed.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.vodafone_bundle.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_top_up_failed.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi not alternative>"}
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi_no_alternative",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi no alternative>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "5G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response="", raise_error=True,
#                 error_instance=JSONDecodeError("Expecting value: line 1 column 1 (char 0)", "", 0)
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings,
#                                                                                                        to_user, subject,
#                                                                                                        body,
#                                                                                                        has_attchament),
#         ]
#     )
#     def test_assign_flexi_bundle_failed_and_we_didnt_find_alternative_bundles_due_to_json_decode_error(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_flexi_no_alternative"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.not_consumed_bundle.bundle_code, "country_code": "EG",
#             "amount": self.not_consumed_bundle.retail_price,
#             "paid_amount_credit_card": self.not_consumed_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.not_consumed_bundle.bundle_duration),
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.not_consumed_bundle.currency_code,
#             "validity_days": self.not_consumed_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_flexi_failed_due_to_json_error_but_no_alternative_bundles = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi_no_alternative", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=self.not_consumed_bundle.retail_price, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(), response={},
#             # system state when we call stripe callback and failed to allocate flexi bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertFalse(
#                     self.not_consumed_bundle.is_active
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=flexiroam_vendor_name, is_active=True).count(), 1
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_flexi_failed_due_to_json_error_but_no_alternative_bundles.data)
#         self.assertEqual(response.status_code, 200)
#         self.not_consumed_bundle.reload()
#         for assert_case in test_case_where_flexi_failed_due_to_json_error_but_no_alternative_bundles.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esim go not alternative>"}
#             ),
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-fast"
#                 }]}, response="", raise_error=True,
#                 error_instance=JSONDecodeError("Expecting value: line 1 column 1 (char 0)", "", 0)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi no alternative>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     def test_assign_esim_go_bundle_failed_due_to_json_error_and_we_didnt_find_alternative_bundles(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle_usa.bundle_code, "country_code": "USA",
#             "amount": self.esim_go_bundle_usa.retail_price,
#             "paid_amount_credit_card": self.esim_go_bundle_usa.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle_usa.bundle_duration),
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle_usa.currency_code,
#             "validity_days": self.esim_go_bundle_usa.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_esim_go_failed_due_to_json_error_and_no_alternative = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go_no_alternative", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate esim go bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=ESIM_GO_VENDOR_NAME, is_active=True).count(), 1
#                 ),
#                 lambda: self.assertFalse(
#                     self.esim_go_bundle_usa.is_active
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_esim_go_failed_due_to_json_error_and_no_alternative.data)
#         self.assertEqual(response.status_code, 200)
#         self.esim_go_bundle_usa.reload()
#         for assert_case in test_case_where_esim_go_failed_due_to_json_error_and_no_alternative.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <DK vodafone flow>"}
#             ),
#             # simulate there is NewConnectionError with vodafone apis
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.vodafone_url}/network/things/consumer-profile/5G-DK-Vodafone",
#                 headers={
#                     'ResponseURLs': consumer_config.callback_get_iccid,
#                     'Authorization': "Bearer " + str(consumer_config.vodafone_token)
#                 }, status_code=200, data={}, json={"customReference": "we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone"},
#                 response="", raise_error=True,
#                 error_instance=JSONDecodeError("Expecting value: line 1 column 1 (char 0)", "", 0)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <dk-vodafone flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email",
#         side_effect=[
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#             lambda email_settings, to_user, subject, body, has_attchament: DummySendEmail().send_email(email_settings, to_user, subject, body, has_attchament),
#         ]
#     )
#     def test_assign_vodafone_bundle_failed_due_to_json_error_and_we_didnt_find_alternative_bundles(self, *args):
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.dark_continental_vodafone_bundle.bundle_code,
#             "paid_amount_credit_card": self.dark_continental_vodafone_bundle.retail_price, "country_code": "DK",
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.dark_continental_vodafone_bundle.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "amount": self.dark_continental_vodafone_bundle.retail_price,
#             "payment_id": random_url_token(), "promo_code": "",
#             "currency_code": self.dark_continental_vodafone_bundle.currency_code,
#             "validity_days": self.dark_continental_vodafone_bundle.bundle_duration,
#             "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value,
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_vodafone_failed_due_to_json_error_and_no_alternative = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_dr_vodafone", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate Vodafone bundle
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertEqual(
#                     Vendors.objects(vendor_name=VODAFONE_VENDOR, is_active=True).count(), 1
#                 ),
#                 lambda: self.assertFalse(
#                     self.dark_continental_vodafone_bundle.is_active
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_status="", method_details="allocate_per_vendor",
#                         method_failure_message="Couldn't allocate"
#                     )
#                 )
#             ]
#         )
#
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_vodafone_failed_due_to_json_error_and_no_alternative.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.dark_continental_vodafone_bundle.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_vodafone_failed_due_to_json_error_and_no_alternative.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-cashback-fcm-{datetime.utcnow()}"
#             )
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not esim go bundle because we switched to flexi due to error from esim go
#                         "plan_uid": "4G-released",
#                     }
#                 }
#             ),
#             MockedRequest(
#                 method="POST",
#                 # mock consumption from flexi
#                 url=f"{consumer_config.flexiroam_url}/plan/simplan/v1'", headers={"token": "temp-access-token"},
#                 status_code=200, data={}, json={"sku": "[profile-id-unique]"}, response={
#                     "data": [
#                         {
#                             "end_date": datetime.utcnow() + timedelta(days=10)
#                         }
#                     ]
#                 }
#             ),
#
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"}
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     def test_assign_flexi_top_up_bundle_success(self, *args):
#         dummy_sendemail = DummySendEmail()
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi-top-up"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.flexi_bundle_with_only_one_sim_available.bundle_code, "country_code": "USA",
#             "paid_amount_credit_card": self.flexi_bundle_with_only_one_sim_available.retail_price, "payment_category": PaymentMethod.credit_card.value,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": self.flexi_bundle_with_only_one_sim_available.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.flexi_bundle_with_only_one_sim_available.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)), "amount": self.flexi_bundle_with_only_one_sim_available.retail_price,
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.flexi_bundle_with_only_one_sim_available.currency_code,
#             "validity_days": self.flexi_bundle_with_only_one_sim_available.bundle_duration, "version_token": self.app_version.version_token,
#             "order_number": stripe_client_secret, "stripe_client_secret": md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         test_case_where_flexi_top_up_failed = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate flexi topup bundle
#             payment_intent="payment_intent_id", assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_details="allocate_per_vendor",
#                     ).first()
#                 ),
#                 lambda: self.assertEqual(self.flexi_bundle_with_only_one_sim_available.consumed_unit, 4),
#                 lambda: self.assertEqual(
#                     user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(days=10)).date()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_flexi_top_up_failed.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.flexi_bundle_with_only_one_sim_available.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_top_up_failed.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "threading.Thread",
#         side_effect=[
#             MockThread(
#                 target=send_fcm_notification, args=("settings", "fcm_token", "ios-version", "", "success", True),
#                 name=f"Thread-to-send-fcm-notification-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_email_invoice,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-email-invoice-{datetime.utcnow()}"
#             ),
#             MockThread(
#                 target=send_fcm_notification,
#                 args=("email_settings", CUSTOMER_EMAIL, "user_name", 'e-SIM', "email_subj", "data_to_send", "qr_code",
#                       "template", "image_file_final", True), name=f"Thread-to-send-cashback-fcm-{datetime.utcnow()}"
#             )
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200, data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1", "discount": ""
#                 }, response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not esim go bundle because we switched to flexi due to error from esim go
#                         "plan_uid": "4G-released",
#                     }
#                 }
#             ),
#
#         ]
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"}
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <flexi topup flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     def test_assign_flexi_top_up_bundle_success_without_consumption_response(self, *args):
#         dummy_sendemail = DummySendEmail()
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "we_1N7wlo2eZnKYlo2CYrlRYoUz_flexi-top-up"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.flexi_bundle_with_only_one_sim_available.bundle_code,
#             "country_code": "USA",
#             "paid_amount_credit_card": self.flexi_bundle_with_only_one_sim_available.retail_price,
#             "payment_category": PaymentMethod.credit_card.value,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12),
#             "topup_code": self.flexi_bundle_with_only_one_sim_available.bundle_code,
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.flexi_bundle_with_only_one_sim_available.bundle_duration),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "amount": self.flexi_bundle_with_only_one_sim_available.retail_price,
#             "payment_id": random_url_token(), "promo_code": "",
#             "currency_code": self.flexi_bundle_with_only_one_sim_available.currency_code,
#             "validity_days": self.flexi_bundle_with_only_one_sim_available.bundle_duration,
#             "version_token": self.app_version.version_token,
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest()
#         })
#         user_iccid = add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.profile.iccid,
#             "activation_code": self.profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#         test_case_where_flexi_top_up_failed = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_felxi_topup", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=user_bundle_log.order_number, created=datetime.utcnow(),
#             response={'data': {'send_qr_code': True}, 'developerMessage': '', 'message': 'Bundle added and email sent',
#                       'responseCode': 1, 'status': True, 'title': 'Success', 'total_count': 0},
#             # system state when we call stripe callback and failed to allocate flexi topup bundle
#             payment_intent="payment_intent_id", assert_list=[
#                 lambda: self.assertIsNotNone(
#                     TransactionLogs.objects(
#                         order_number=user_bundle_log.order_number, bundle_status=True,
#                         transaction_status=True).first()
#                 ),
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL, order_number=user_bundle_log.order_number,
#                         method_name="stripe_callback", method_details="allocate_per_vendor",
#                     ).first()
#                 ),
#                 lambda: self.assertEqual(self.flexi_bundle_with_only_one_sim_available.consumed_unit, 4),
#                 lambda: self.assertEqual(
#                     user_iccid.expiry_date.date(), (datetime.utcnow() + timedelta(days=90)).date()
#                 )
#             ]
#         )
#         response = self.client.post(
#             self.stripe_callback_url, headers=headers, data=test_case_where_flexi_top_up_failed.data)
#         user_iccid.reload()
#         user_bundle_log.reload()
#         self.flexi_bundle_with_only_one_sim_available.reload()
#         self.assertEqual(response.status_code, 200)
#         for assert_case in test_case_where_flexi_top_up_failed.assert_list:
#             assert_case()
#
#     @mock.patch(
#         "b2c_helpers.support_helper.send_email"
#     )
#     @mock.patch(
#         "requests.request",
#         side_effect=[
#             MockedRequest(
#                 method="POST", url=consumer_config.stripe_login_url,
#                 headers={}, status_code=200, data={}, json={
#                     "username": consumer_config.stripe_superadmin_username,
#                     "password": consumer_config.stripe_superadmin_password,
#                 }, response={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"}
#             ),
#             MockedRequest(
#                 method="POST", url=f"{consumer_config.esimgo_url}/v2.2/orders",
#                 headers={'X-API-Key': consumer_config.esim_go_token}, status_code=200, data={},
#                 json={"type": "transaction", "assign": True, "Order": [{
#                     "type": "bundle", "quantity": 1, "item": "2G-released"
#                 }]}, response="", raise_error=True,
#                 error_instance=NewConnectionError("self", NEW_CONNECTION_ERROR_MESSAGE)
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.get",
#         side_effect=[
#             MockedRequest(
#                 url=consumer_config.stripe_transactions_url + "?stripe_request_id=we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go",
#                 headers={"access_token": "Bearer Access Token for Login into stripe api <esimgo flow>"},
#                 status_code=200, data={}, json={}, method='GET',
#                 response=[{"status": "succeeded", "event": "payment_intent.succeeded"}],
#             ),
#         ]
#     )
#     @mock.patch(
#         "requests.post",
#         side_effect=[
#             MockedRequest(
#                 method="POST",
#                 # here is temp_token from vendor info document
#                 url=f"{consumer_config.flexiroam_url}/plan/load/v1", headers={"token": ""}, status_code=200,
#                 data={},
#                 json={
#                     "sku": "[profile-id-unique]", "plan_code": "4G-released", "plan_start_type_id": "1",
#                     "discount": ""
#                 }, response={
#                     "success": True,
#                     "data": {
#                         # here bundle id not esim go bundle because we switched to flexi due to error from esim go
#                         "plan_uid": "4G-released",
#                     }
#                 }
#             )
#         ]
#     )
#     def test_fallback_if_we_found_active_bundle_but_linked_to_inactive_vendor_we_should_ignore_it(
#             self, *args):
#         Vendors.objects(vendor_name=flexiroam_vendor_name).update(is_active=False)
#         dummy_sendemail = DummySendEmail()
#
#         mock_send_email = args[-1]
#         mock_send_email.side_effect = dummy_sendemail.send_email
#         headers = {
#             "Content-Type": "application/json",
#         }
#
#         stripe_client_secret = "whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go"
#         user_bundle_log = add_user_bundle_log({
#             "email": self.CUSTOMER_EMAIL, "bundle_code": self.esim_go_bundle.bundle_code, "country_code": "EG",
#             "amount": self.esim_go_bundle.retail_price, "paid_amount_credit_card": self.esim_go_bundle.retail_price,
#             "paid_amount_wallet": 0, "qr_code_pin": generate_temp_otp(12), "topup_code": "",
#             "payment_date": datetime.utcnow(), "otp": generate_temp_otp(12), "cancel_otp": generate_temp_otp(12),
#             "validy_date": datetime.today() + timedelta(self.esim_go_bundle.bundle_duration),
#             "order_number": stripe_client_secret, 'stripe_client_secret': md5(stripe_client_secret.encode()).hexdigest(),
#             "history_log": 'hist_' + str(generate_temp_otp(12)),
#             "payment_id": random_url_token(), "promo_code": "", "currency_code": self.esim_go_bundle.currency_code,
#             "validity_days": self.esim_go_bundle.bundle_duration, "version_token": self.app_version.version_token,
#             "payment_category": PaymentMethod.credit_card.value
#         })
#         add_user_iccid({
#             "email": self.CUSTOMER_EMAIL,
#             "bundle_code": user_bundle_log.bundle_code,
#             "country_code": user_bundle_log.country_code,
#             "iccid": self.free_profile.iccid,
#             "activation_code": self.free_profile.matching_id,
#             "datetime": datetime.utcnow(),
#             "payment_otp": user_bundle_log.otp,
#             "cancel_otp": user_bundle_log.cancel_otp,
#         })
#
#         test_case_where_esim_go_failed_due_to_connection_error = StripeCallbackRequest(
#             stripe_request_id="we_1N7wlo2eZnKYlo2CYrlRYoUz_esim_go", event="payment_intent.succeeded",
#             email=self.CUSTOMER_EMAIL, livemode=False, name="customer-name", balance=0,
#             amount_received=user_bundle_log.paid_amount_credit_card, outcome={}, paid=True,
#             payment_intent="payment_intent_id",
#             failure_balance_transaction=0, failure_code="", failure_message="", status="succeeded",
#             receipt_url="stripe.com/receipts/<id>/", payment_method_types='card', customer="cust_id",
#             client_secret=stripe_client_secret, created=datetime.utcnow(),
#             response={
#                 'data': {}, 'developerMessage': '', 'message': '', 'responseCode': 1, 'status': False,
#                 'title': 'Failed', 'total_count': 0},
#             assert_list=[
#                 lambda: self.assertIsNotNone(
#                     PaymentLogsTransactions.objects(
#                         user_email=self.CUSTOMER_EMAIL,
#                         order_number='whsec_sP8iSRHHQzAnJW5O6USVdRBz9UjhwwU3_esim_go').first()
#                 )
#             ]
#         )
#
#         fallback_response = self.client.post(
#             self.stripe_callback_url, headers=headers,
#             data=test_case_where_esim_go_failed_due_to_connection_error.data)
#         self.assertEqual(fallback_response.status_code, 200)
#         self.assertDictEqual(fallback_response.json, test_case_where_esim_go_failed_due_to_connection_error.response)
#
#         self.assertEqual(len(dummy_sendemail.outbox), 2)
#
#         self.assertEqual(
#             dummy_sendemail.outbox[1].header,
#             'To:' + Settings.objects.first().esim_email + '\n' + 'From: ' + self.email_setting.username
#             + '\n' + "Subject:------URGENT------Couldn't allocate bundle" + ' \n'
#         )
#         self.assertIn(
#             f"Couldn't allocate bundle {self.esim_go_bundle.bundle_vendor_code} for",
#             dummy_sendemail.outbox[1].msg,
#         )

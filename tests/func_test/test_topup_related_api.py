import unittest
from unittest import mock
from src import app
from datetime import datetime, timedelta
from tests.func_test.utils.object_factories.objects_factory import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    KeycloakOpenIdUserInfoResponseFactory,
    KeycloakOpenIdIntrospectResponseFactory
)
from b2c_helpers.constaints import MONTYMO<PERSON>LE_VENDOR, VODAFONE_VENDOR
from app_models.consumer_models import Bundles, Profiles, Vendors, region_names_, regions_, _icicid_status
from tests.func_test.utils import db_utils
from app_models.mobiles import AppList, app_platforms_, AppVersionList
from app_models.reseller_models import Order_history
from unittest.mock import MagicMock


@mock.patch.multiple(
    "keycloak.KeycloakOpenID",
    userinfo=MagicMock(return_value=KeycloakOpenIdUserInfoResponseFactory(email="<EMAIL>").build()),
    introspect=MagicMock(
        return_value=KeycloakOpenIdIntrospectResponseFactory(email="<EMAIL>").build())
)
class GetTopupRelatedWithExpiryTestCase(unittest.TestCase):
    CUSTOMER_EMAIL = "<EMAIL>"
    VERSION_TOKEN = "2x93FRjGwzFHC7QVT7ZGNUnggXiBxlpct8fawRHWmLE"
    ICCID = "22305670901234567890"
    ICCID2 = "22305670901234567891"
    BASIC_HEADER = HeaderBuilder().add_auth("Bearer mocked_token").custom_header(key="device_id", value="device_id")

    def setUp(self):
        self.client = app.test_client()
        self.get_topup_related_api_url = '/v2/topup-related/{}/{}'

        # Setup required data in the database
        self.app = self._create_app("Orange", 'B2COrg', app_platforms_[0])
        self.app_version = self._create_app_version(self.VERSION_TOKEN, self.app)
        self.monty_vendor = self._create_vendor(MONTYMOBILE_VENDOR, "Mo", "ne", 30, 1, True, False)
        self.vodafone_vendor = self._create_vendor(VODAFONE_VENDOR, "Vo", "ne", 30, 1, True, True)
        self._create_bundles()
        self.monty_profile = self._create_profile(self.ICCID, self.monty_vendor.vendor_name)
        self.vodafone_profile = self._create_profile(self.ICCID2, self.vodafone_vendor.vendor_name,
                                                     (datetime.utcnow() + timedelta(days=20)))

        self.order_history = self._create_order_history(self.ICCID, self.monty_bundle.bundle_name,
                                                        self.monty_bundle.bundle_code, "PU-123454678",
                                                        "Or-123456789", (datetime.utcnow() + timedelta(days=30)))
        self.vodafone_order_history = self._create_order_history(self.ICCID2, self.vodafone_bundle.bundle_name,
                                                                 self.vodafone_bundle.bundle_name, "PU-123454679",
                                                                 "Or-1234567890",
                                                                 (datetime.utcnow() + timedelta(days=30)))

    def _create_bundles(self):
        self.monty_bundle = self._create_bundle(MONTYMOBILE_VENDOR, "Monty-Bundle", "Monty-Bundle", "fastest-4G", 1, 1,
                                                "country",
                                                ["ARE"], ["ARE"])
        self.monty_bundle_2 = self._create_bundle(MONTYMOBILE_VENDOR, "Monty-Bundle2", "Monty-Bundle2", "fastest-4G-2",
                                                  2, 2, "country",
                                                  ["ARE"], ["ARE"])
        self.monty_bundle_3 = self._create_bundle(MONTYMOBILE_VENDOR, "Monty-Bundle3", "Monty-Bundle3", "fastest-4G-3",
                                                  1, 1,
                                                  "country", ["IND"], ["IND"])
        self.vodafone_bundle = self._create_bundle(VODAFONE_VENDOR, "Vodafone-Bundle", "Vodafone-Bundle", "fastest-4G",
                                                   1, 1, "country",
                                                   ["ARE"], ["ARE"])
        self.vodafone_bundle_2 = self._create_bundle(VODAFONE_VENDOR, "Vodafone-Bundle-2", "Vodafone-Bundle-2",
                                                     "fastest-4G", 2, 1, "country",
                                                     ["ARE"], ["ARE"])

    def _create_bundle(self, vendor_name, bundle_code, bundle_name, bundle_marketing_name, retail_price, rate_revenue,
                       bundle_category, country_list, country_code_list, profile_names=""):
        return Bundles.objects(bundle_code=bundle_code).first() or Bundles(
            vendor_name=vendor_name, bundle_code=bundle_code, bundle_name=bundle_name,
            bundle_marketing_name=bundle_marketing_name, category_name="1", region_code=region_names_[1],
            retail_price=retail_price, bundle_vendor_name=self.monty_vendor.vendor_name, region_name=regions_[1],
            bundle_vendor_code=bundle_code, rate_revenue=rate_revenue, create_datetime=datetime.utcnow(),
            bundle_duration=30, allocated_unit=30,
            bundle_category=bundle_category, country_list=country_list, unit_price="10", data_amount=20,
            fullspeed_data_amount=16, data_unit="GB", validity_amount="28", profile_names=profile_names,
            is_active=True, country_code_list=country_code_list, consumed_unit=20,
            preview_for=["subscriber", "reseller"]).save()

    def _create_profile(self, iccid, vendor_name, expiry_date=None):
        return Profiles.objects(iccid=iccid).first() or Profiles(
            vendor_name=vendor_name, sku="profile-id-unique2", iccid=iccid, qr_code_value="qr_code_value",
            profile_names="te", smdp_address="rsp.test.client.com", matching_id=None, create_datetime=datetime.utcnow(),
            status=_icicid_status[0], expiry_date=expiry_date,
        ).save()

    def _create_vendor(self, vendor_name, vendor_prefix, vendor_suffix, bundles_count, minimal_balance, is_active,
                       profile_expiry=False):
        return Vendors.objects(vendor_name=vendor_name).first() or Vendors(
            vendor_name=vendor_name, vendor_prefix=vendor_prefix, vendor_suffix=vendor_suffix,
            bundles_count=bundles_count, minimal_balance=minimal_balance, is_active=is_active, support_topup=True,
            apply_expiry=profile_expiry).save()

    def _create_app(self, op_name, app_name, app_platform):
        return AppList.objects(op_name=op_name).first() or AppList(
            op_name=op_name, app_name=app_name, app_platform=app_platform, update_date=datetime.utcnow()).save()

    def _create_app_version(self, version_token, app):
        return AppVersionList.objects(version_token=version_token).first() or AppVersionList(
            version_token=version_token, app_id=app.to_dbref(), total_request=20, total_verify=20, total_download="20",
            update_date=datetime.utcnow(), create_date=datetime.utcnow()).save()

    def _create_order_history(self, iccid, bundle_name, bundle_code, plan_uid, order_id, expiry_date):
        return Order_history.objects(version_token=iccid).first() or Order_history(
            order_status="Successful", bundle_price=20, bundle_code=bundle_code, bundle_marketing_name=bundle_name,
            client_name="sanemi", client_email=self.CUSTOMER_EMAIL, remaining_wallet_balance=100,
            country_code="ARE", country_name="United Arab Emirates", smdp_address="testsmdpaddress", iccid=iccid,
            plan_status="Active", plan_uid=plan_uid, original_bundle_code=bundle_code, bundle_category="country",
            order_number=order_id, expiry_date=expiry_date, reseller_type="subscriber").save()

    def tearDown(self):
        db_utils.wipe_db_data_clean()

    def test_topup_related_with_expiry_failures(self, *args):
        """
        Test get top-up related with expiry failure scenarios.
        """
        test_cases = [
            {
                "name": "TopUpRelatedWithExpiry::User ICCID Not In The Order History",
                "request": {"iccid": "68976589334625278143", "bundle_code": "fexi-1gb"},
                "response": {"status_code": 200, "status": False, "message": "Iccid not relative",
                             "developerMessage": ""}
            },
            {
                "name": "TopUpRelatedWithExpiry::Bundle Not Found",
                "request": {"iccid": self.ICCID, "bundle_code": "fexi-1gb"},
                "response": {"status_code": 200, "status": False, "message": "No bundles found",
                             "developerMessage": "No bundles found"}
            }
        ]
        for tc in test_cases:
            with self.subTest(tc["name"]):
                response = self.client.get(
                    self.get_topup_related_api_url.format(tc["request"]["bundle_code"], tc["request"]["iccid"]),
                    headers=self.BASIC_HEADER.headers)
                self.assertEqual(response.status_code, tc["response"]["status_code"])
                self.assertEqual(response.json["status"], tc["response"]["status"])
                self.assertEqual(response.json["message"], tc["response"]["message"])

    def test_topup_related_with_expiry_success(self, *args):
        """
               Test get top-up related with expiry success scenarios.
        """
        test_cases = [
            {
                "name": "TopUpRelatedWithExpiry::No Top Up Bundles If Vendor Inactive",
                "request": {"iccid": self.ICCID, "bundle_code": self.monty_bundle.bundle_code},
                "response": {"status_code": 200, "status": True, "message": "",
                             "developerMessage": "", "total": 0}
            },
            {
                "name": "TopUpRelatedWithExpiry::No Top Up Bundles If Vendor Doesn't Support Topup Feature",
                "request": {"iccid": self.ICCID, "bundle_code": self.monty_bundle.bundle_code},
                "response": {"status_code": 200, "status": True, "message": "",
                             "developerMessage": "", "total": 0}
            },
            {
                # In this case, when apply_expiry=False for vendors, it means that the eSIM profiles provided by the
                # vendor do not have a specific expiry date. Therefore, the profile expiry date does not need to be
                # considered when retrieving the top-ups.
                "name": "TopUpRelatedWithExpiry::Get All Available Top up",
                "request": {"iccid": self.ICCID, "bundle_code": self.monty_bundle.bundle_code},
                "response": {"status_code": 200, "status": True, "message": "",
                             "developerMessage": "", "total": 2}
            },
            {
                # In this case, when apply_expiry=True for vendors, it means that the eSIM profiles provided by the
                # vendor have a specific expiry date. Therefore, we need to consider this expiry date when fetching
                # the top-ups, ensuring that the top-up expiry date is earlier than the profile expiry date.
                "name": "TopUpRelatedWithExpiry::Profile Expiry :: No top-ups Within The Profile's Expiry Date",
                "request": {"iccid": self.ICCID2, "bundle_code": self.vodafone_bundle.bundle_code},
                "response": {"status_code": 200, "status": True, "message": "",
                             "developerMessage": "", "total": 0}
            },
            {
                # Same as above, profile with expiry.
                "name": "TopUpRelatedWithExpiry::Profile Expiry :: top-ups Within The Profile's Expiry Date",
                "request": {"iccid": self.ICCID2, "bundle_code": self.vodafone_bundle.bundle_code},
                "response": {"status_code": 200, "status": True, "message": "",
                             "developerMessage": "", "total": 2}
            },
        ]
        for tc in test_cases:
            with self.subTest(tc["name"]):
                self._update_vendor_and_profile_status(tc["name"])

                response = self.client.get(
                    self.get_topup_related_api_url.format(tc["request"]["bundle_code"], tc["request"]["iccid"]),
                    headers=self.BASIC_HEADER.headers)
                self.assertEqual(response.status_code, tc["response"]["status_code"])
                self.assertEqual(response.json["status"], tc["response"]["status"])
                self.assertEqual(response.json["message"], tc["response"]["message"])
                self.assertEqual(response.json["total"], tc["response"]["total"])

    def _update_vendor_and_profile_status(self, test_case_name):
        if test_case_name == "TopUpRelatedWithExpiry::No Top Up Bundles If Vendor Inactive":
            self.monty_vendor.is_active = False
        elif test_case_name == "TopUpRelatedWithExpiry::No Top Up Bundles If Vendor Doesn't Support Topup Feature":
            self.monty_vendor.is_active = True
            self.monty_vendor.support_topup = False
        else:
            self.monty_vendor.is_active = True
            self.monty_vendor.support_topup = True
        self.monty_vendor.save()

        if test_case_name == "TopUpRelatedWithExpiry::Profile Expiry :: top-ups Within The Profile's Expiry Date":
            self.vodafone_profile.expiry_date = (datetime.utcnow() + timedelta(days=40))
            self.vodafone_profile.save()
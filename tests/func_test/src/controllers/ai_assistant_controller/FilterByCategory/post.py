import json
import pprint
import unittest
import uuid
from datetime import timezone, timedelta, datetime
from unittest import mock

from app_models.ai_assistant_models import advanced_search_category_category_code, \
    AdvancedSearchBundleResult, AdvancedSearch
from app_models.consumer_models import Bundles

from src import app
from tests.func_test.utils.database_data import setup_db, SetupResponse
from tests.func_test.utils.db_utils import wipe_db_data_clean
from tests.func_test.utils.mock_path_constants import WRAPPERS_KEYCLOAK_OPEN_ID
from tests.func_test.utils.object_factories.mocked_keycloak_open_id import MockedKeycloakOpenIdFactory
from tests.func_test.utils.object_factories.objects_factory import BundleFactory

filter_by_category_url = '/v2/ai-assistant/filter'


class BaseFixture(unittest.TestCase):
    def setUp(self):
        wipe_db_data_clean()
        self.database: SetupResponse = setup_db()
        self.client = app.test_client()

        __keycloak_patcher = mock.patch(WRAPPERS_KEYCLOAK_OPEN_ID)
        self.keycloak = __keycloak_patcher.start()

        self.keycloak.return_value = MockedKeycloakOpenIdFactory().userinfo(
            name="sanemi", given_name="sanemi", family_name="shinz", email=self.database.user.user_email,

        ).introspect(
            given_name='sanemi', family_name='shinz', preferred_username='sanemi',
            email=self.database.user.user_email, username='sanemi'
        ).build()

    def get_expected_keys_in_adv_result_in_db_dict(self, country_code_list=True, region_code_list=True,
                                                   consumption_min=True, consumption_max=True, duration_min=True,
                                                   duration_max=True):
        all_fields_set = set()

        if country_code_list:
            all_fields_set.add("country_code_list")

        if region_code_list:
            all_fields_set.add("region_code_list")

        if consumption_min:
            all_fields_set.add("consumption_min")
            all_fields_set.add("consumption_min_unit")

        if consumption_max:
            all_fields_set.add("consumption_max")
            all_fields_set.add("consumption_max_unit")

        if duration_min:
            all_fields_set.add("duration_min")

        if duration_max:
            all_fields_set.add("duration_max")

        return all_fields_set


class QAReportedIssues(BaseFixture):

    def test_case_where_setting_high_consumption_returning_low_consumption_1_issue(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        json_request = {
            "countries": [
                "GBR"
            ],
            "duration": advanced_search_category_category_code.duration.choice_4.name,
            "consumption": advanced_search_category_category_code.consumption.high.name,
            "bundles_limit": 15
        }

        b1 = Bundles(**{
            "vendor_name": "eSIMGo",
            "bundle_code": "GBR_279eb03b-c6d2-48af-972d-609c4cb7f0ed",
            "bundle_name": "UnitedKingdom5GB 30Days",
            "bundle_marketing_name": "The World is Yours!",
            "bundle_category": "country",
            "region_code": "eu",
            "region_name": "Europe",
            "bundle_vendor_code": "esim_5GB_30D_GB_V2",
            "bundle_vendor_name": "eSIM, 5GB, 30 Days, United Kingdom, V2",
            "create_datetime": datetime(year=2024, month=4, day=8, hour=16, minute=20, second=6, microsecond=977000,
                                        tzinfo=timezone(timedelta(hours=3))),
            "bundle_duration": 30,
            "unit_price": 4.36,
            "rate_revenue": 27.5,
            "retail_price": 5.56,
            "currency_code": "USD",
            "data_amount": 5,
            "fullspeed_data_amount": 5,
            "data_unit": "GB",
            "validity_amount": "30",
            "profile_names": "",
            "allocated_unit": 25,
            "reserved_unit": 0,
            "consumed_unit": 0,
            "consumed_unit_subscriber": 0,
            "is_region": False,
            "is_active": True,
            "deleted": False,
            "country_list": ["United Kingdom"],
            "country_code_list": ["GBR"],
            "missing_country_code": [],
            "plan_type": "Data only",
            "plan_type_code": 1,
            "activity_policy": "The validity period starts when the eSIM connects to any supported networks.",
            "activity_policy_code": 3,
            "available_netwok": "Available networks will be displayed upon activation",
            "available_netwok_code": 4,
            "top_up_plan": "Available top-up will be displayed upon activation",
            "top_up_plan_code": 5,
            "ekyc": "Not required",
            "ekyc_code": 0,
            "daily_used": 0,
            "update_at": datetime(year=2024, month=4, day=8, hour=16, minute=20, second=6, microsecond=977000,
                                  tzinfo=timezone(timedelta(hours=3))),
            "allocate_profiles": True,
            "maximum_profiles_number": -1,
            "preview_for": ["subscriber", "reseller"],
            "group_id": "Standard Bundles April 2024",
            "support_topup": True,
            "unlimited": False,
            "previous_daily_used": 0
        }).save()

        b2 = Bundles(**{
            "vendor_name": "eSIMGo",
            "bundle_code": "GBR_a0de750a-3afc-4dc6-9f7c-c4e24662977f",
            "bundle_name": "UnitedKingdom3GB 30Days",
            "bundle_marketing_name": "The World is Yours!",
            "bundle_category": "country",
            "region_code": "eu",
            "region_name": "Europe",
            "bundle_vendor_code": "esim_3GB_30D_GB_V2",
            "bundle_vendor_name": "eSIM, 3GB, 30 Days, United Kingdom, V2",
            "create_datetime": datetime(year=2024, month=4, day=8, hour=16, minute=18, second=15, microsecond=977000,
                                        tzinfo=timezone(timedelta(hours=3))),
            "bundle_duration": 30,
            "unit_price": 2.83,
            "rate_revenue": 27.5,
            "retail_price": 3.61,
            "currency_code": "USD",
            "data_amount": 3,
            "fullspeed_data_amount": 3,
            "data_unit": "GB",
            "validity_amount": "30",
            "profile_names": "",
            "allocated_unit": 3,
            "reserved_unit": 0,
            "consumed_unit": 0,
            "consumed_unit_subscriber": 0,
            "is_region": False,
            "is_active": True,
            "deleted": False,
            "country_list": ["United Kingdom"],
            "country_code_list": ["GBR"],
            "missing_country_code": [],
            "plan_type": "Data only",
            "plan_type_code": 1,
            "activity_policy": "The validity period starts when the eSIM connects to any supported networks.",
            "activity_policy_code": 3,
            "available_netwok": "Available networks will be displayed upon activation",
            "available_netwok_code": 4,
            "top_up_plan": "Available top-up will be displayed upon activation",
            "top_up_plan_code": 5,
            "ekyc": "Not required",
            "ekyc_code": 0,
            "daily_used": 0,
            "update_at": datetime(year=2024, month=4, day=8, hour=16, minute=18, second=15, microsecond=977000,
                                  tzinfo=timezone(timedelta(hours=3))),
            "allocate_profiles": True,
            "maximum_profiles_number": -1,
            "preview_for": ["subscriber", "reseller"],
            "group_id": "Standard Bundles April 2024",
            "support_topup": True,
            "unlimited": False,
            "previous_daily_used": 0
        }).save()

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.low.name
        ).update(set__min=0.1, set__max=5.0)

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.moderate.name
        ).update(set__min=6.0, set__max=10.0)

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.high.name
        ).update(set__min=10.0, set__max=500.0)

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.duration.name,
            category_code=advanced_search_category_category_code.duration.choice_4.name
        ).update(set__min=30.0, set__max=-1.0)

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # Assert
        expected_bundle_codes_to_not_be_returned_list = [
            b1.bundle_code,
            b2.bundle_code
        ]

        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

    def test_case_where_setting_high_consumption_returning_low_consumption_1_solution(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.high.name,
            "bundles_limit": 15
        }

        bundle_1 = BundleFactory(
            bundle_code=f"bundle_1_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=51,
            data_unit="GB",
            unlimited=False,
            bundle_duration=1000
        ).build()

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=101,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        b1 = Bundles(**{
            "vendor_name": "eSIMGo",
            "bundle_code": "GBR_279eb03b-c6d2-48af-972d-609c4cb7f0ed",
            "bundle_name": "UnitedKingdom5GB 30Days",
            "bundle_marketing_name": "The World is Yours!",
            "bundle_category": "country",
            "region_code": "eu",
            "region_name": "Europe",
            "bundle_vendor_code": "esim_5GB_30D_GB_V2",
            "bundle_vendor_name": "eSIM, 5GB, 30 Days, United Kingdom, V2",
            "create_datetime": datetime(year=2024, month=4, day=8, hour=16, minute=20, second=6, microsecond=977000,
                                        tzinfo=timezone(timedelta(hours=3))),
            "bundle_duration": 30,
            "unit_price": 4.36,
            "rate_revenue": 27.5,
            "retail_price": 5.56,
            "currency_code": "USD",
            "data_amount": 5,
            "fullspeed_data_amount": 5,
            "data_unit": "GB",
            "validity_amount": "30",
            "profile_names": "",
            "allocated_unit": 25,
            "reserved_unit": 0,
            "consumed_unit": 0,
            "consumed_unit_subscriber": 0,
            "is_region": False,
            "is_active": True,
            "deleted": False,
            "country_list": ["United Kingdom"],
            "country_code_list": ["GBR"],
            "missing_country_code": [],
            "plan_type": "Data only",
            "plan_type_code": 1,
            "activity_policy": "The validity period starts when the eSIM connects to any supported networks.",
            "activity_policy_code": 3,
            "available_netwok": "Available networks will be displayed upon activation",
            "available_netwok_code": 4,
            "top_up_plan": "Available top-up will be displayed upon activation",
            "top_up_plan_code": 5,
            "ekyc": "Not required",
            "ekyc_code": 0,
            "daily_used": 0,
            "update_at": datetime(year=2024, month=4, day=8, hour=16, minute=20, second=6, microsecond=977000,
                                  tzinfo=timezone(timedelta(hours=3))),
            "allocate_profiles": True,
            "maximum_profiles_number": -1,
            "preview_for": ["subscriber", "reseller"],
            "group_id": "Standard Bundles April 2024",
            "support_topup": True,
            "unlimited": False,
            "previous_daily_used": 0
        }).save()

        b2 = Bundles(**{
            "vendor_name": "eSIMGo",
            "bundle_code": "GBR_a0de750a-3afc-4dc6-9f7c-c4e24662977f",
            "bundle_name": "UnitedKingdom3GB 30Days",
            "bundle_marketing_name": "The World is Yours!",
            "bundle_category": "country",
            "region_code": "eu",
            "region_name": "Europe",
            "bundle_vendor_code": "esim_3GB_30D_GB_V2",
            "bundle_vendor_name": "eSIM, 3GB, 30 Days, United Kingdom, V2",
            "create_datetime": datetime(year=2024, month=4, day=8, hour=16, minute=18, second=15, microsecond=977000,
                                        tzinfo=timezone(timedelta(hours=3))),
            "bundle_duration": 30,
            "unit_price": 2.83,
            "rate_revenue": 27.5,
            "retail_price": 3.61,
            "currency_code": "USD",
            "data_amount": 3,
            "fullspeed_data_amount": 3,
            "data_unit": "GB",
            "validity_amount": "30",
            "profile_names": "",
            "allocated_unit": 3,
            "reserved_unit": 0,
            "consumed_unit": 0,
            "consumed_unit_subscriber": 0,
            "is_region": False,
            "is_active": True,
            "deleted": False,
            "country_list": ["United Kingdom"],
            "country_code_list": ["GBR"],
            "missing_country_code": [],
            "plan_type": "Data only",
            "plan_type_code": 1,
            "activity_policy": "The validity period starts when the eSIM connects to any supported networks.",
            "activity_policy_code": 3,
            "available_netwok": "Available networks will be displayed upon activation",
            "available_netwok_code": 4,
            "top_up_plan": "Available top-up will be displayed upon activation",
            "top_up_plan_code": 5,
            "ekyc": "Not required",
            "ekyc_code": 0,
            "daily_used": 0,
            "update_at": datetime(year=2024, month=4, day=8, hour=16, minute=18, second=15, microsecond=977000,
                                  tzinfo=timezone(timedelta(hours=3))),
            "allocate_profiles": True,
            "maximum_profiles_number": -1,
            "preview_for": ["subscriber", "reseller"],
            "group_id": "Standard Bundles April 2024",
            "support_topup": True,
            "unlimited": False,
            "previous_daily_used": 0
        }).save()

        expected_bundle_codes_to_be_returned_list = [
            bundle_3.bundle_code,
            b1.bundle_code,
            b2.bundle_code
        ]

        expected_bundle_codes_to_not_be_returned_list = [
            bundle_1.bundle_code,
            bundle_2.bundle_code
        ]

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.low.name
        ).update(set__min=0.1, set__max=5.0)

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.moderate.name
        ).update(set__min=6.0, set__max=10.0)

        expected_normalized_min = 100
        expected_normalized_max = 50
        AdvancedSearch.objects(
            category=advanced_search_category_category_code.consumption.name,
            category_code=advanced_search_category_category_code.consumption.high.name
        ).update(set__min=0.1, set__max=50)

        AdvancedSearch.objects(
            category=advanced_search_category_category_code.duration.name,
            category_code=advanced_search_category_category_code.duration.choice_4.name
        ).update(set__min=30.0, set__max=-1.0)

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # Assert
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                self.assertGreaterEqual(bundle["data_amount"], expected_normalized_min)
            elif bundle["data_unit"] == "GB":
                self.assertLessEqual(bundle["data_amount"], expected_normalized_max)

            self.assertFalse(bundle["unlimited"])

class GetCategoriesDurationTestCases(BaseFixture):
    def test_where_invalid_duration_passed(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        json_request = {
            "countries": [
                "GBR"
            ],
            "duration": "dsadsadasdasd",
            "consumption": advanced_search_category_category_code.consumption.high.name,
            "bundles_limit": 15
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        self.assertFalse(actual_response.json['status'])
        self.assertEqual(actual_response.json['message'].replace(" ", ""), f"Must be one of: { ','.join(advanced_search_category_category_code.duration.get_sub_categories_names())}.".replace(" ", ""))

class GetCategoriesConsumptionTestCases(BaseFixture):

    def test_normal_flow_without_consumption(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(consumption_min=False,
                                                                                            consumption_max=False)

        json_request = {
            "countries": ["EG"],
            "regions": ["EUR"],
            "duration": advanced_search_category_category_code.duration.choice_1.name,
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id, ).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertGreaterEqual(1, len(actual_response.json['data']))

    def test_where_invalid_consumption_passed(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        json_request = {
            "countries": [
                "GBR"
            ],
            "duration": advanced_search_category_category_code.duration.choice_4.name,
            "consumption": "dadadasdasda",
            "bundles_limit": 15
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        self.assertFalse(actual_response.json['status'])
        self.assertEqual(actual_response.json['message'].replace(" ", ""), f"Must be one of: { ','.join(advanced_search_category_category_code.consumption.get_sub_categories_names())}.".replace(" ", ""))

    def test_happy_path_result_with_consumption_min_max_no_unlimited_data_request(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False)

        self.database.bundle.update(
            data_amount=100,
            data_unit="MB",
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        bundle_4 = BundleFactory(
            bundle_code=f"bundle_4_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=110,
            data_unit="GB",
            unlimited=False,
            bundle_duration=4000
        ).build()

        bundle_5 = BundleFactory(
            bundle_code=f"bundle_5_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True,
            bundle_duration=5000
        ).build()

        bundle_6 = BundleFactory(
            bundle_code=f"bundle_6_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=True,
            bundle_duration=6000
        ).build()

        expected_normalized_min = 100
        expected_normalized_max = 100
        AdvancedSearch.objects(category="consumption", category_code="low").update(set__min=0.1, set__max=100)

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name,
            "bundles_limit": 15
        }

        expected_bundle_codes_to_be_returned_list = [self.database.bundle.bundle_code, bundle_2.bundle_code]

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                self.assertGreaterEqual(bundle["data_amount"], expected_normalized_min)
            elif bundle["data_unit"] == "GB":
                self.assertLessEqual(bundle["data_amount"], expected_normalized_max)

            self.assertFalse(bundle["unlimited"])

    def test_happy_path_result_with_consumption_min_no_max_no_unlimited_data_request(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False,
                                                                                            consumption_max=False)

        self.database.bundle.update(
            data_amount=99,
            data_unit="MB",
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_2_1 = BundleFactory(
            bundle_code=f"bundle_2_1_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="MB",
            unlimited=False,
            bundle_duration=2100
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        bundle_4 = BundleFactory(
            bundle_code=f"bundle_4_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=110,
            data_unit="GB",
            unlimited=False,
            bundle_duration=4000
        ).build()

        bundle_5 = BundleFactory(
            bundle_code=f"bundle_5_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True,
            bundle_duration=5000
        ).build()

        bundle_6 = BundleFactory(
            bundle_code=f"bundle_6_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=True,
            bundle_duration=6000
        ).build()

        expected_normalized_min = 100
        expected_normalized_max = None
        AdvancedSearch.objects(category="consumption", category_code="low").update(set__min=0.1, set__max=None)

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name,
            "bundles_limit": 15
        }

        expected_bundle_codes_to_be_returned_list = [bundle_2_1.bundle_code, bundle_2.bundle_code, bundle_4.bundle_code]
        expected_bundle_codes_to_not_be_returned_list = [
            self.database.bundle.bundle_code,
            bundle_3.bundle_code,
            bundle_5.bundle_code,
            bundle_6.bundle_code
        ]

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                self.assertGreaterEqual(bundle["data_amount"], expected_normalized_min)
            elif bundle["data_unit"] == "GB":
                continue

            self.assertFalse(bundle["unlimited"])

    def test_happy_path_result_with_consumption_max_no_min_no_unlimited_data_request(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False,
                                                                                            consumption_min=False)

        self.database.bundle.update(
            data_amount=101,
            data_unit="GB",
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_2_1 = BundleFactory(
            bundle_code=f"bundle_2_1_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="MB",
            unlimited=False,
            bundle_duration=2100
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        bundle_4 = BundleFactory(
            bundle_code=f"bundle_4_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="GB",
            unlimited=False,
            bundle_duration=4000
        ).build()

        bundle_5 = BundleFactory(
            bundle_code=f"bundle_5_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True,
            bundle_duration=5000
        ).build()

        bundle_6 = BundleFactory(
            bundle_code=f"bundle_6_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=True,
            bundle_duration=6000
        ).build()

        expected_normalized_min = None
        expected_normalized_max = 100
        AdvancedSearch.objects(category="consumption", category_code="low").update(set__min=None, set__max=100)

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name,
            "bundles_limit": 15
        }

        expected_bundle_codes_to_be_returned_list = [
            bundle_2.bundle_code,
            bundle_2_1.bundle_code,
            bundle_3.bundle_code,
            bundle_4.bundle_code
        ]
        expected_bundle_codes_to_not_be_returned_list = [
            self.database.bundle.bundle_code,
            bundle_5.bundle_code,
            bundle_6.bundle_code
        ]

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                continue
            elif bundle["data_unit"] == "GB":
                self.assertLessEqual(bundle["data_amount"], expected_normalized_max)

            self.assertFalse(bundle["unlimited"])

    def test_happy_path_result_with_consumption_min_max_with_max_unlimited(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False)

        self.database.bundle.update(
            data_amount=99,
            data_unit="MB",
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_2_1 = BundleFactory(
            bundle_code=f"bundle_2_1_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="MB",
            unlimited=False,
            bundle_duration=2100
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        bundle_4 = BundleFactory(
            bundle_code=f"bundle_4_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=110,
            data_unit="GB",
            unlimited=False,
            bundle_duration=4000
        ).build()

        bundle_5 = BundleFactory(
            bundle_code=f"bundle_5_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True,
            bundle_duration=5000
        ).build()

        bundle_6 = BundleFactory(
            bundle_code=f"bundle_6_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=True,
            bundle_duration=6000
        ).build()

        expected_normalized_min = 100
        expected_normalized_max = None
        AdvancedSearch.objects(category="consumption", category_code="low").update(set__min=0.1,
                                                                                   set__max=advanced_search_category_category_code.INFINITY)

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name,
            "bundles_limit": 15
        }

        expected_bundle_codes_to_be_returned_list = [bundle_2_1.bundle_code, bundle_2.bundle_code, bundle_4.bundle_code,
                                                     bundle_5.bundle_code, bundle_6.bundle_code]
        expected_bundle_codes_with_unlimited = [bundle_5.bundle_code, bundle_6.bundle_code]
        expected_bundle_codes_to_not_be_returned_list = [
            self.database.bundle.bundle_code,
            bundle_3.bundle_code,
        ]

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                self.assertGreaterEqual(bundle["data_amount"], expected_normalized_min)
            elif bundle["data_unit"] == "GB":
                continue

            if bundle["bundle_code"] in expected_bundle_codes_with_unlimited:
                self.assertTrue(bundle["unlimited"])

    def test_happy_path_result_with_consumption_min_max_with_min_max_unlimited(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False)

        self.database.bundle.update(
            data_amount=101,
            data_unit="GB",
            bundle_duration=11000,
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code=f"bundle_2_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False,
            bundle_duration=2000
        ).build()

        bundle_2_1 = BundleFactory(
            bundle_code=f"bundle_2_1_{str(uuid.uuid4())}",
            bundle_marketing_name=str(uuid.uuid4()),
            bundle_name=str(uuid.uuid4()),
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="MB",
            unlimited=False,
            bundle_duration=2100
        ).build()

        bundle_3 = BundleFactory(
            bundle_code=f"bundle_3_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="MB",
            unlimited=False,
            bundle_duration=3000
        ).build()

        bundle_4 = BundleFactory(
            bundle_code=f"bundle_4_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=99,
            data_unit="GB",
            unlimited=False,
            bundle_duration=4000
        ).build()

        bundle_5 = BundleFactory(
            bundle_code=f"bundle_5_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True,
            bundle_duration=5000
        ).build()

        bundle_6 = BundleFactory(
            bundle_code=f"bundle_6_{str(uuid.uuid4())}",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=True,
            bundle_duration=6000
        ).build()

        expected_normalized_min = None
        expected_normalized_max = None
        AdvancedSearch.objects(category="consumption", category_code="low").update(set__min=-1, set__max=-1)

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name,
            "bundles_limit": 15
        }

        expected_bundle_codes_to_be_returned_list = [
            bundle_5.bundle_code,
            bundle_6.bundle_code
        ]

        expected_bundle_codes_with_unlimited = [bundle_5.bundle_code, bundle_6.bundle_code]

        expected_bundle_codes_to_not_be_returned_list = [
            self.database.bundle.bundle_code,
            bundle_2.bundle_code,
            bundle_2_1.bundle_code,
            bundle_3.bundle_code,
            bundle_4.bundle_code,
        ]

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        actual_bundle_code_list = [b["bundle_code"] for b in actual_response.json['data']]

        for expected_bundle_code in expected_bundle_codes_to_be_returned_list:
            self.assertIn(expected_bundle_code, actual_bundle_code_list)

        for unexpected_bundle_code in expected_bundle_codes_to_not_be_returned_list:
            self.assertNotIn(unexpected_bundle_code, actual_bundle_code_list)

        for bundle in actual_response.json['data']:
            if bundle["data_unit"] == "MB":
                continue
            elif bundle["data_unit"] == "GB":
                continue

            if bundle["bundle_code"] in expected_bundle_codes_with_unlimited:
                self.assertTrue(bundle["unlimited"])

    def test_happy_path_result_with_consumption_min_max_both_unlimited(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False,
                                                                                            region_code_list=False,
                                                                                            duration_min=False,
                                                                                            duration_max=False,
                                                                                            consumption_max=False)

        self.database.bundle.update(
            data_amount=100,
            data_unit="MB",
            unlimited=False
        )

        bundle_2 = BundleFactory(
            bundle_code="4G-released_2",
            bundle_marketing_name="fastest-4G_2",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=120,
            data_unit="MB",
            unlimited=False
        ).build()

        bundle_3 = BundleFactory(
            bundle_code="4G-released_3",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=200,
            data_unit="MB",
            unlimited=False
        ).build()

        bundle_3_1 = BundleFactory(
            bundle_code="4G-released_3_1",
            bundle_marketing_name="fastest-4G-3",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=100,
            data_unit="GB",
            unlimited=False
        ).build()

        bundle_4 = BundleFactory(
            bundle_code="4G-released_4",
            bundle_marketing_name="fastest-4G-4",
            bundle_name="4G",
            bundle_vendor_code="4G-KI-FA-01",
            bundle_vendor_name=self.database.vendor.vendor_name,
            supplier_vendor=f"{self.database.vendor.id}",
            vendor_name=self.database.vendor.vendor_name,
            allocated_unit=1000,
            retail_price=1,
            data_amount=-1,
            data_unit="GB",
            unlimited=True
        ).build()

        AdvancedSearch.objects(category="consumption", category_code="low").update(
            set__min=0.0,
            set__max=advanced_search_category_category_code.INFINITY
        )

        json_request = {
            "consumption": advanced_search_category_category_code.consumption.low.name
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertEqual(1, len(actual_response.json['data']))
        actual_data_bundle_codes = [b["bundle_code"] for b in actual_response.json['data']]
        expected_bundle_codes = []


class GetCategoriesGeneralTestCases(BaseFixture):

    def test_happy_path_normal_flow_no_exceptions(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict()

        json_request = {
            "countries": ["EG"],
            "regions": ["EUR"],
            "duration": advanced_search_category_category_code.duration.choice_1.name,
            "consumption": advanced_search_category_category_code.consumption.high.name
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertGreaterEqual(1, len(actual_response.json['data']))

    def test_normal_flow_without_countries(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(country_code_list=False)

        json_request = {
            "regions": ["EUR"],
            "duration": advanced_search_category_category_code.duration.choice_1.name,
            "consumption": advanced_search_category_category_code.consumption.high.name
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertGreaterEqual(1, len(actual_response.json['data']))

    def test_normal_flow_without_regions(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(region_code_list=False)

        json_request = {
            "countries": ["EG"],
            "duration": advanced_search_category_category_code.duration.choice_1.name,
            "consumption": advanced_search_category_category_code.consumption.high.name
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertGreaterEqual(1, len(actual_response.json['data']))

    def test_normal_flow_without_duration(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        expected_keys_in_adv_result_in_db = self.get_expected_keys_in_adv_result_in_db_dict(duration_min=False,
                                                                                            duration_max=False)

        json_request = {
            "countries": ["EG"],
            "regions": ["EUR"],
            "consumption": advanced_search_category_category_code.consumption.high.name
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNotNone(actual_advanced_search_bundle_result)
        actual_adv_result_in_db = actual_advanced_search_bundle_result.to_mongo().to_dict()
        self.assertEqual(set(actual_adv_result_in_db["search_criteria"].keys()), expected_keys_in_adv_result_in_db)

        self.assertTrue(actual_response.json['status'])
        self.assertGreaterEqual(1, len(actual_response.json['data']))

    def test_normal_flow_without_any_filters(self):
        # ARRANGE
        default_header = {
            "Authorization": "Bearer a_bear"
        }

        json_request = {
        }

        # ACT
        actual_response = self.client.post(filter_by_category_url, headers=default_header, json=json_request)

        # ASSERT
        actual_advanced_search_bundle_result = AdvancedSearchBundleResult.objects(
            user_oid=self.database.user.id).first()
        self.assertIsNone(actual_advanced_search_bundle_result)

        self.assertTrue(actual_response.json['status'])
        self.assertEqual(0, len(actual_response.json['data']))

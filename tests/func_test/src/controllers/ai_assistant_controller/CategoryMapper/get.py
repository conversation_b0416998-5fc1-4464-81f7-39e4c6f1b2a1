
import unittest

from app_models.ai_assistant_models import AdvancedSearch, advanced_search_category_category_code

from src import app
from tests.func_test.utils.database_data import setup_db, SetupResponse
from tests.func_test.utils.db_utils import wipe_db_data_clean

category_mapper_url = '/v2/ai-assistant/categories'


class BaseFixture(unittest.TestCase):
    def setUp(self):
        wipe_db_data_clean()
        self.database: SetupResponse = setup_db()
        self.client = app.test_client()

# HOW THE DATA LOOKS LIKE
# dict = {'consumption': {'data': [{'display_name': 'low', 'id': 'low'},
#                                  {'display_name': 'moderate', 'id': 'moderate'},
#                                  {'display_name': 'high', 'id': 'high'}],
#                         'is_consumption': True},
#         'destination': {'country': {'data': [{'display_name': 'Lebanon', 'id': 'LBN'}],
#                                     'is_multiple_countries': True,
#                                     'is_single_country': True},
#                         'region': {'data': [{'display_name': 'Antarctica', 'id': 'an'},
#                                             {'display_name': 'Middle East',
#                                              'id': 'me'},
#                                             {'display_name': 'Africa', 'id': 'af'},
#                                             {'display_name': 'South America',
#                                              'id': 'sa'},
#                                             {'display_name': 'Asia', 'id': 'as'},
#                                             {'display_name': 'Europe', 'id': 'eu'},
#                                             {'display_name': 'North America',
#                                              'id': 'na'}],
#                                    'is_region': True}},
#         'duration': {'data': [{'display_name': '1-4', 'id': 'choice_1'},
#                               {'display_name': '0+', 'id': 'choice_2'},
#                               {'display_name': '0-100', 'id': 'choice_3'},
#                               {'display_name': '13+', 'id': 'choice_4'}],
#                      'is_duration': True}}


class when_category_inactive(BaseFixture):
    def test_should_return_false_when_no_category_found(self):
        AdvancedSearch.objects(
            category__in=advanced_search_category_category_code.get_category_names()
        ).delete()

        # ARRANGE
        default_header = {
            "Accept-Language": "en",
        }

        # ACT
        actual_response = self.client.get(category_mapper_url, headers=default_header)

        # ASSERT
        self.assertDictEqual(
            actual_response.json["data"],
            {
                'consumption': {'data': [], 'is_consumption': False},
                'destination': {
                    'country': {'data': [], 'is_multiple_countries': False, 'is_single_country': False},
                    'region': {'data': [], 'is_region': False}
                },
                'duration': {'data': [], 'is_duration': False}
            })

    def test_should_return_false_when_subcategory_inactive(self):
        AdvancedSearch.objects(
            category__in=advanced_search_category_category_code.get_category_names()
        ).update(
            selectable=False
        )

        # ARRANGE
        default_header = {
            "Accept-Language": "en",
        }

        # ACT
        actual_response = self.client.get(category_mapper_url, headers=default_header)

        # ASSERT
        response_data = actual_response.json["data"]

        self.assertDictEqual(
            actual_response.json["data"],
            {
                'consumption': {'data': [], 'is_consumption': False},
                'destination': {
                    'country': {'data': [], 'is_multiple_countries': False, 'is_single_country': False},
                    'region': {'data': [], 'is_region': False}
                },
                'duration': {'data': [], 'is_duration': False}
            })

class when_normal_flow_commences(BaseFixture):

    def test_should_return_data(self):
        # ARRANGE
        default_header = {
            "Accept-Language": "en",
        }

        # ACT
        actual_response = self.client.get(category_mapper_url, headers=default_header)

        # ASSERT

        consumption = actual_response.json["data"][advanced_search_category_category_code.consumption.name]
        self.assertGreater(len(consumption['data']), 0)
        self.assertTrue(consumption['is_consumption'])

        destination = actual_response.json["data"][advanced_search_category_category_code.destination.name]

        country = destination['country']
        self.assertGreater(len(country['data']), 0)
        self.assertTrue(country['is_multiple_countries'])
        self.assertTrue(country['is_single_country'])

        region = destination['region']
        self.assertGreater(len(region['data']), 0)
        self.assertTrue(region['is_region'])

        duration = actual_response.json["data"][advanced_search_category_category_code.duration.name]
        self.assertGreater(len(duration['data']), 0)
        self.assertTrue(duration['is_duration'])


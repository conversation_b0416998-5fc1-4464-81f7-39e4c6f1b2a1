class MockedRequest:
    def __init__(
            self, method, url, headers, data, status_code, response={}, text="", json={}, content="",
            raise_error=False, error_instance=None):
        self.method = method
        self.url = url
        self.headers = headers
        self.data = data
        self._status_code = status_code
        self.response = response
        self.content = str.encode(content)
        self.text = text
        self.raise_error = raise_error
        self.error_instance = error_instance
        self.text = text

    @property
    def status_code(self):
        return self._status_code

    def json(self):
        if self.raise_error:
            raise self.error_instance
        return self.response

class MockResponse:
    def __init__(self, json_data, status_code):
        self.json_data = json_data
        self.status_code = status_code

    def json(self):
        return self.json_data
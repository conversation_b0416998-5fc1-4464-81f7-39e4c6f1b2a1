import datetime
from unittest import TestCase
from unittest import mock
import os
from unittest.mock import MagicMock
from app_models.consumer_models import (
    Bundles,
    Vendors,
    regions_,
    region_names_,
    Profiles,
)
from constaints import vodafone_vendor_name
from app_helpers.cron_helpers import vodafone_save_profiles
from app_models import consumer_models


class SaveVodafoneProfile(TestCase):
    def setUp(self):

        self.vendor = (
            Vendors.objects(vendor_name="Vodafone").first()
            or Vendors(
                **{
                    "vendor_name": "Vodafone",
                    "vendor_prefix": "Vo",
                    "vendor_suffix": "ne",
                    "bundles_count": 30,
                    "minimal_balance": 1,
                    "temp_token": "temp-access-token",
                    "is_active": True,
                }
            ).save()
        )

        self.bundle = (
            Bundles.objects(bundle_code="03142023150845_01032_2").first()
            or Bundles(
                **{
                    "vendor_name": "Vodafone",
                    "bundle_code": "03142023150845_01032_2",
                    "bundle_name": "Europe10GB15days",
                    "bundle_category": "country",
                    "bundle_marketing_name": "fastest-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "retail_price": 1,
                    "region_name": regions_[1],
                    "bundle_vendor_code": "01032_2",
                    "bundle_vendor_name": "Vodafone",
                    "rate_revenue": 1,
                    "create_datetime": datetime.datetime.utcnow(),
                    "bundle_duration": 30,
                    "supplier_vendor": "00",
                    "unit_price": "10",
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "validity_amount": "29",
                    "daily_used": 1,
                    "profile_names": "te",
                    "country_list": ["EG"],
                    "country_code_list": ["EG"],
                    "is_active": True,
                }
            ).save()
        )

        self.bundle2 = (
            Bundles.objects(bundle_code="03142023150847_01038_1").first()
            or Bundles(
                **{
                    "vendor_name": "Vodafone",
                    "bundle_code": "03142023150847_01038_1",
                    "bundle_name": "NorthAmerica10GB15days",
                    "bundle_category": "country",
                    "bundle_marketing_name": "fastest-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "retail_price": 1,
                    "region_name": regions_[1],
                    "bundle_vendor_code": "01038_1",
                    "bundle_vendor_name": "Vodafone",
                    "rate_revenue": 1,
                    "create_datetime": datetime.datetime.utcnow(),
                    "bundle_duration": 30,
                    "supplier_vendor": "00",
                    "unit_price": "10",
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "validity_amount": "29",
                    "daily_used": 2,
                    "profile_names": "te",
                    "country_list": ["EG"],
                    "country_code_list": ["EG"],
                    "is_active": True,
                }
            ).save()
        )

    def tearDown(self):
        self.bundle.delete()
        self.bundle2.delete()
        self.vendor.delete()
        Profiles.objects(vendor_name=vodafone_vendor_name).delete()

    @mock.patch("requests.request")
    def test_profile_saved(self, mock_request):
        # Set up the mock response
        mock_response = MagicMock()
        mock_response.status_code = 200  # or any other status code you want to test
        mock_response.json.return_value = {"acknowledgement": {"id": "some_id"}}
        mock_request.return_value = mock_response
        profiles = vodafone_save_profiles()
        profiles = consumer_models.Profiles.objects(vendor_name="Vodafone")
        assert len(profiles) > 0
        for p in profiles:
            assert p.vendor_name == "Vodafone"

import datetime
from unittest import TestCase
import os
from app_models.consumer_models import Bundles, Vendors, regions_, region_names_, Profiles

from app_helpers.cron_helpers import check_profile_expiry
from app_models import consumer_models
from app_models.reseller_models import Order_history



class CreateProfileExpiryTest(TestCase):
    
    ''' @classmethod
    def setUpClass(cls):
        cls.client = app.test_client()

        mongo_client = pymongo.MongoClient(consumer_config.new_host_)
        cls.db = mongo_client.get_database(consumer_config.decrypted_db_name_alias)'''

    def setUp(self):
        self.vendor = (Vendors.objects(vendor_name="Flexiroam").first() or Vendors(**{
            "vendor_name": "Flexiroam", "vendor_prefix": "Vo", "vendor_suffix": "ne", "bundles_count": 30,
            "minimal_balance": 1, "temp_token": "temp-access-token", "is_active": True
        }).save())

        self.bundle = (Bundles.objects(bundle_code="4G-released").first() or Bundles(**{
            "vendor_name": self.vendor.vendor_name, "bundle_code": "4G-released", "bundle_name": "4G", "bundle_category": "country",
            "bundle_marketing_name": "fastest-4G", "category_name": "1", "region_code": region_names_[1], "retail_price": 1,
            "region_name": regions_[1], "bundle_vendor_code": "4G-released", "bundle_vendor_name": self.vendor.vendor_name,
            "rate_revenue": 1, "create_datetime": datetime.datetime.utcnow(), "bundle_duration": 30, "supplier_vendor": "00",
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB", "validity_amount": "29",
            "profile_names": "te", "country_list": ["EG"], "country_code_list": ["EG"], "is_active": True
        }).save())
        order_1 = {
            "order_status": "Successful", "iccid": "1234567891234567891", "bundle_price": 4,
            "bundle_code": self.bundle.bundle_code, "bundle_retail_price": 10,
            "bundle_marketing_name": "Cool-Name", "date_created": datetime.datetime.utcnow() - datetime.timedelta(days=100),
            "bundle_name": "CoolName", "matching_id": "0088:1144:5566:2233:0000",
            "smdp_address": "rsp.test.client.com", "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
            "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": "vendor_bundle_code",
            "remaining_wallet_balance": 490, "bundle_category": "country",
            "country_name": "EGY", "country_code": "EGY", "profile_availability": True
        }
        #order_history_collection = self.db.get_collection("order_history")
        self.flexi_order_history_success_1 = (Order_history.objects(
                                             iccid="1234567891234567891").first() or Order_history(**{
            "order_status": "Successful", "iccid": "1234567891234567891", "bundle_price": 4,
            "bundle_code": self.bundle.bundle_code, "bundle_retail_price": 10,
            "bundle_marketing_name": "Cool-Name", "date_created": datetime.datetime.utcnow() - datetime.timedelta(days=100),
            "bundle_name": "CoolName", "matching_id": "0088:1144:5566:2233:0000",
            "smdp_address": "rsp.test.client.com", "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
            "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": "vendor_bundle_code",
            "remaining_wallet_balance": 490, "bundle_category": "country",
            "country_name": "EGY", "country_code": "EGY", "profile_availability": True
        , "reseller_id": "1234567891234567891234", "branch_id": "1234567891234567891234"},
        ).save())

        self.bundle_2 = (Bundles.objects(bundle_code="5G-released").first() or Bundles(**{
            "vendor_name": self.vendor.vendor_name, "bundle_code": "5G-released", "bundle_name": "5G",
            "bundle_category": "country", "bundle_vendor_name": self.vendor.vendor_name, "validity_amount": "29",
            "bundle_marketing_name": "fastest-5G", "category_name": "1", "region_code": region_names_[1],
            "retail_price": 1, "region_name": regions_[1], "bundle_vendor_code": "5G-released-00-qw",
            "rate_revenue": 1, "create_datetime": datetime.datetime.utcnow(), "bundle_duration": 30, "supplier_vendor": "00",
            "unit_price": "10", "data_amount": 30, "fullspeed_data_amount": 16, "data_unit": "GB",
            "profile_names": "te", "country_list": ["EG"], "country_code_list": ["EG"], "is_active": True
        }).save())

        self.flexi_order_history_success_2 = (Order_history.objects(
                                             iccid="9876543219876543219").first() or Order_history(**{
            "order_status": "Successful", "iccid": "9876543219876543219", "bundle_price": 4,
            "bundle_code": self.bundle_2.bundle_code, "bundle_retail_price": 10,
            "bundle_marketing_name": "Cool-Name-2", "date_created": datetime.datetime.utcnow() - datetime.timedelta(days=100),
            "bundle_name": "CoolName2", "matching_id": "0088:1144:5566:2233:0000",
            "smdp_address": "rsp.test.client.com", "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
            "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": "vendor_bundle_code",
            "remaining_wallet_balance": 490, "bundle_category": "country",
            "country_name": "EGY", "country_code": "EGY", "profile_availability": True
        , "reseller_id": "1234567891234567891234", "branch_id": "1234567891234567891234"},
        ).save())

        self.profile_1 = (Profiles.objects(iccid="111111111112222222222").first() or Profiles(**{
            "vendor_name": self.vendor.vendor_name, "sku": "2019092980645", "iccid": "111111111112222222222",
            "availability": "Assigned"
        }).save())
        self.flexi_order_history_success_3 = (Order_history.objects(
                                             iccid="111111111112222222222").first() or Order_history(**{
            "order_status": "Successful", "iccid": "111111111112222222222", "bundle_price": 4,
            "bundle_code": "Code-3", "bundle_retail_price": 10,
            "bundle_marketing_name": "Cool-Name-3", "date_created": datetime.datetime.utcnow() - datetime.timedelta(days=100),
            "bundle_name": "CoolName3", "matching_id": "0088:1144:5566:2233:0000",
            "smdp_address": "rsp.test.client.com", "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
            "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": "vendor_bundle_code",
            "remaining_wallet_balance": 490, "bundle_category": "country",
            "country_name": "EGY", "country_code": "EGY", "profile_availability": True, "reseller_id": "1234567891234567891234", "branch_id": "1234567891234567891234"},
        ).save())


        self.flexi_order_history_success_4 = (Order_history.objects(
                                             iccid="111111111112222222224").first() or Order_history(**{
            "order_status": "Successful", "iccid": "111111111112222222224", "bundle_price": 4,
            "bundle_code": "Code-3", "bundle_retail_price": 10,
            "bundle_marketing_name": "Cool-Name-3",
            "date_created": datetime.datetime.utcnow() - datetime.timedelta(days=30),
            "bundle_name": "CoolName3", "matching_id": "0088:1144:5566:2233:0000",
            "smdp_address": "rsp.test.client.com", "activation_code": "LPA:1$SS:00:W0:R0:Q0:T1$rsp.test.client.com",
            "client_name": "aboda", "client_email": "<EMAIL>", "plan_uid": "vendor_bundle_code",
            "remaining_wallet_balance": 490, "bundle_category": "country",
            "country_name": "EGY", "country_code": "EGY", "profile_availability": True, "reseller_id": "1234567891234567891236", "branch_id": "1234567891234567891236"}
        ).save())


        self.profile = (Profiles.objects(iccid="111111111112222222224").first() or Profiles(** {
         "vendor_name":self.vendor.vendor_name, "sku":  "2019092980646", "iccid": "111111111112222222224",  "availability": "Assigned"
        }).save())


    def tearDown(self):
        self.flexi_order_history_success_1.delete()
        self.flexi_order_history_success_2.delete()
        self.flexi_order_history_success_3.delete()
        self.flexi_order_history_success_4.delete()

        self.profile.delete()
        self.profile_1.delete()
        self.bundle.delete()
        self.bundle_2.delete()
        self.vendor.delete()

    def test_profile_expiry(self):
        threshold = datetime.datetime.now() - datetime.timedelta(days=91)

        order_history = Order_history.objects(order_status='Successful',date_created__lt=threshold).order_by('-date_created').first()
        check_profile_expiry()
        profiles = consumer_models.Profiles.objects.filter(iccid=order_history.iccid, availability="Expired")
        assert profiles.count() ==0

    def test_profile_with_iccid_not_passed_90_days(self):
        check_profile_expiry()
        profiles = consumer_models.Profiles.objects.filter(iccid='111111111112222222224', availability="Expired")
        print(profiles)
        assert profiles.count() == 0, "Field to filter to profiles that matched 90 days"

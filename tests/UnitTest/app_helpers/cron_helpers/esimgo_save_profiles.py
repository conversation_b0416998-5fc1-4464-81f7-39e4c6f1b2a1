import copy
import unittest
import uuid
from datetime import datetime
from unittest import mock

from app_models.consumer_models import Vendors, Bundles, category_names_, region_names_, regions_

from app_helpers.cron_helpers import esimgo_save_profiles, ESIM_GO_VENDOR

from app_main import app


class BaseFixture(unittest.TestCase):
    def setUp(self):
        Vendors.objects.delete()
        Bundles.objects.delete()

        self.esimgo_vendor = Vendors(
            vendor_name=ESIM_GO_VENDOR,
            vendor_code='SAMP001',
            rate_revenue=30.0,
            vendor_prefix='SV-',
            vendor_suffix='2024',
            default_data_unit='GB',
            apply_expiry=True,
            number_of_expiry_days=30,
            bundles_count=150,
            minimal_balance=75,
            support_topup=True,
            is_active=True,
            sent_data=True,
            temp_token='abc123token',
            total_profiles_bought=500,
            apply_inventory=False,
            supports_empty_profiles=False,
            has_constant_bundle_count=False,
            get_profile_from_vendor=False,
            token_expiry=datetime(2024, 12, 31),
            currency_exchange_rate=1.25,
            esim_installation=True,
            has_balance=True,
            vendor_expiry_days=180,
            vendor_start_bundle=True,
            last_update_bundles_date=datetime(2024, 8, 30)
        ).save()

        __esimgo_bundle_uuid = uuid.uuid4()
        self.esimgo_bundle = Bundles(
            bundle_code=str(__esimgo_bundle_uuid),
            bundle_marketing_name=f"esimgo_bundle_marketing_name_{str(__esimgo_bundle_uuid)}",
            bundle_name=f"esimgo_bundle_name_{str(__esimgo_bundle_uuid)}",
            bundle_vendor_code=f"esimgo_bundle_vendor_code_{str(__esimgo_bundle_uuid)}",
            bundle_vendor_name=self.esimgo_vendor.vendor_name,
            supplier_vendor=f"{self.esimgo_vendor.id}",
            vendor_name=self.esimgo_vendor.vendor_name,
            retail_price=1,
            allocated_unit=0,
            bundle_category=category_names_[2],
            bundle_duration=30,
            category_name="1",
            consumed_unit=0,
            country_code_list=["EG"],
            country_list=["EG"],
            create_datetime=datetime.utcnow(),
            data_amount=30,
            data_unit="GB",
            deleted=False,
            fullspeed_data_amount=16,
            is_active=True,
            profile_names="te",
            region_code=region_names_[1],
            region_name=regions_[1],
            rate_revenue=1,
            unit_price="10",
            validity_amount="29",
            group_id='yaman',
            unlimited=False,
            allocate_profiles=True,
            maximum_profiles_number=10,
            daily_used=1000
        ).save()

        # ---------------------------------------------------------------------------------
        # Mock Patch to control the result of the method enough_vendor_balance
        # ---------------------------------------------------------------------------------
        __enough_vendor_balance_mock_patcher = mock.patch("app_helpers.cron_helpers.enough_vendor_balance")
        self.enough_vendor_balance_mock = __enough_vendor_balance_mock_patcher.start()
        self.addCleanup(__enough_vendor_balance_mock_patcher.stop)
        self.enough_vendor_balance_mock.return_value = True

        # ---------------------------------------------------------------------------------
        # Mock Patch to control the result of the method enough_organization_balance
        # ---------------------------------------------------------------------------------
        __enough_organization_balance_mock_patcher = mock.patch("app_helpers.cron_helpers.enough_organization_balance")
        self.enough_organization_balance_mock = __enough_organization_balance_mock_patcher.start()
        self.addCleanup(__enough_organization_balance_mock_patcher.stop)
        self.enough_organization_balance_mock.return_value = True

        # ---------------------------------------------------------------------------------
        # Mock Patch to control the result of the method enough_organization_balance
        # ---------------------------------------------------------------------------------
        __save_esimgo_profiles_mock_patcher = mock.patch("app_helpers.cron_helpers.save_esimgo_profiles")
        self.save_esimgo_profiles_mock = __save_esimgo_profiles_mock_patcher.start()
        self.addCleanup(__save_esimgo_profiles_mock_patcher.stop)
        self.save_esimgo_profiles_mock.return_value = 10

        # ----------------------------------------------------------------
        # Used to control email sending
        # ----------------------------------------------------------------
        __send_email_patcher = mock.patch("smtplib.SMTP")
        self.send_email = __send_email_patcher.start()
        self.addCleanup(__send_email_patcher.stop)


class when_bundle_code_none_and_daily_used_zero(BaseFixture):

    def setUp(self):
        super().setUp()

        self.bundle_code = None
        self.daily_used = 0

    def test_should_fail_when_enough_vendor_balance_is_false(self):
        self.enough_vendor_balance_mock.return_value = False

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("Insufficient balance", actual_returned_message)

    def test_should_fail_when_vendor_is_not_active(self):
        self.esimgo_vendor.update(is_active=False)

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("not active", actual_returned_message)

    def test_should_fail_when_no_bundles_found_for_vendor(self):
        Bundles.objects(bundle_vendor_name=ESIM_GO_VENDOR).delete()

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("No bundles", actual_returned_message)

    def test_should_fail_and_update_daily_used_when_no_specific_bundle_found_for_vendor(self):
        self.esimgo_bundle.update(is_active=False, daily_used=1000)

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)

        self.assertEqual(actual_returned_result, 0)
        self.assertIn("No bundles", actual_returned_message)

        actual_esimgo_bundle: Bundles = Bundles.objects(id=self.esimgo_bundle.id).first()
        self.assertEqual(actual_esimgo_bundle.daily_used, 0)

    def test_should_fail_when_there_isnt_enough_organization_balance(self):
        self.enough_organization_balance_mock.return_value = False

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)

        self.assertEqual(actual_returned_result, 0)
        self.assertIn("balance", actual_returned_message)

    def test_should_succeed(self):
        old_esimgo_bundle = copy.deepcopy(self.esimgo_bundle.to_mongo().to_dict())

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code, daily_used=self.daily_used)

        self.assertEqual(actual_returned_result, self.save_esimgo_profiles_mock.return_value)

        actual_esimgo_bundle: Bundles = Bundles.objects(id=self.esimgo_bundle.id).first()
        self.assertEqual(actual_esimgo_bundle.daily_used, 0)
        self.assertEqual(actual_esimgo_bundle.previous_daily_used, old_esimgo_bundle["daily_used"])


class when_bundle_code_and_daily_used_zero(BaseFixture):

    def setUp(self):
        super().setUp()

        self.bundle_code = self.esimgo_bundle.bundle_code
        self.daily_used = 0

    def test_should_fail_when_enough_vendor_balance_is_false(self):
        self.enough_vendor_balance_mock.return_value = False

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code,
                                                                               daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("Insufficient balance", actual_returned_message)

    def test_should_fail_when_vendor_is_not_active(self):
        self.esimgo_vendor.update(is_active=False)

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code,
                                                                               daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("not active", actual_returned_message)

    def test_should_fail_when_no_bundles_found_for_vendor(self):
        Bundles.objects(bundle_vendor_name=ESIM_GO_VENDOR).delete()

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code,
                                                                               daily_used=self.daily_used)
        self.assertEqual(actual_returned_result, 0)
        self.assertIn("No bundles", actual_returned_message)

    def test_should_fail_when_there_isnt_enough_organization_balance(self):
        self.enough_organization_balance_mock.return_value = False

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code,
                                                                               daily_used=self.daily_used)

        self.assertEqual(actual_returned_result, 0)
        self.assertIn("balance", actual_returned_message)

    def test_should_succeed(self):

        actual_returned_result, actual_returned_message = esimgo_save_profiles(bundle_code=self.bundle_code,
                                                                               daily_used=self.daily_used)

        self.assertEqual(actual_returned_result, self.save_esimgo_profiles_mock.return_value)

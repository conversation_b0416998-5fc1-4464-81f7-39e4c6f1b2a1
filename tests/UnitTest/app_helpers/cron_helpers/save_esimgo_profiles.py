import copy
import unittest
import uuid
from datetime import datetime
from unittest import mock
from unittest.mock import MagicMock

from app_models.consumer_models import category_names_, region_names_, regions_, Bundles, Vendors, Profiles

from app_helpers.cron_helpers import ESIM_GO_VENDOR, save_esimgo_profiles

from app_main import app

class BaseFixture(unittest.TestCase):
    def setUp(self):
        Vendors.objects.delete()
        Bundles.objects.delete()
        Profiles.objects.delete()

        self.esimgo_vendor = Vendors(
            vendor_name=ESIM_GO_VENDOR,
            vendor_code='SAMP001',
            rate_revenue=30.0,
            vendor_prefix='SV-',
            vendor_suffix='2024',
            default_data_unit='GB',
            apply_expiry=True,
            number_of_expiry_days=30,
            bundles_count=150,
            minimal_balance=75,
            support_topup=True,
            is_active=True,
            sent_data=True,
            temp_token='abc123token',
            total_profiles_bought=500,
            apply_inventory=False,
            supports_empty_profiles=False,
            has_constant_bundle_count=False,
            get_profile_from_vendor=False,
            token_expiry=datetime(2024, 12, 31),
            currency_exchange_rate=1.25,
            esim_installation=True,
            has_balance=True,
            vendor_expiry_days=180,
            vendor_start_bundle=True,
            last_update_bundles_date=datetime(2024, 8, 30)
        ).save()

        __esimgo_bundle_uuid = uuid.uuid4()
        self.esimgo_bundle = Bundles(
            bundle_code=str(__esimgo_bundle_uuid),
            bundle_marketing_name=f"esimgo_bundle_marketing_name_{str(__esimgo_bundle_uuid)}",
            bundle_name=f"esimgo_bundle_name_{str(__esimgo_bundle_uuid)}",
            bundle_vendor_code=f"esimgo_bundle_vendor_code_{str(__esimgo_bundle_uuid)}",
            bundle_vendor_name=self.esimgo_vendor.vendor_name,
            supplier_vendor=f"{self.esimgo_vendor.id}",
            vendor_name=self.esimgo_vendor.vendor_name,
            retail_price=1,
            allocated_unit=0,
            bundle_category=category_names_[2],
            bundle_duration=30,
            category_name="1",
            consumed_unit=0,
            country_code_list=["EG"],
            country_list=["EG"],
            create_datetime=datetime.utcnow(),
            data_amount=30,
            data_unit="GB",
            deleted=False,
            fullspeed_data_amount=16,
            is_active=True,
            profile_names="te",
            region_code=region_names_[1],
            region_name=regions_[1],
            rate_revenue=1,
            unit_price="10",
            validity_amount="29",
            group_id='yaman',
            unlimited=False,
            allocate_profiles=True,
            maximum_profiles_number=10,
            daily_used=1000
        ).save()

        __assign_profiles_mock_patcher = mock.patch("app_helpers.cron_helpers.ESIMGo.assign_profiles")
        self.assign_profiles_mock = __assign_profiles_mock_patcher.start()
        self.addCleanup(__assign_profiles_mock_patcher.stop)
        self.assign_profiles_mock.return_value = {
            "orderReference": "ref",
            "order": [
                {
                    "esims": [
                        {
                            "iccid": "iccid_12345",
                            "matchingId": "matchingId_12345",
                            "smdpAddress": "smdpAddress_12345"
                        }
                    ]
                }
            ],
            "total": 1
        }


class TestCases(BaseFixture):
    def test_should_return_total_profile_added_when_daily_used_greater_than_number_free_profiles(self):

        old_esimgo_bundle: Bundles = copy.deepcopy(self.esimgo_bundle)

        actual_result = save_esimgo_profiles(
            bundle_code=self.esimgo_bundle.bundle_code,
            daily_used=1,
            bundle_vendor_code= self.esimgo_bundle.bundle_vendor_code,
            original_daily_used=0)

        self.assertEqual(actual_result, 1)

        actual_updated_esimgo_bundle: Bundles = Bundles.objects(id=self.esimgo_bundle.id).first()

        self.assertTrue(actual_updated_esimgo_bundle.is_active)
        self.assertEqual(actual_updated_esimgo_bundle.allocated_unit - old_esimgo_bundle.allocated_unit, 1)

        actual_saved_profile = Profiles.objects(
            vendor_name=ESIM_GO_VENDOR,
            iccid=self.assign_profiles_mock.return_value["order"][0]["esims"][0]["iccid"],
            sku=self.assign_profiles_mock.return_value["order"][0]["esims"][0]["iccid"],
            status=True,
            bundle_code=self.esimgo_bundle.bundle_code,
            matching_id=self.assign_profiles_mock.return_value["order"][0]["esims"][0]["matchingId"]
        ).first()

        self.assertIsNotNone(actual_saved_profile)

    def test_should_return_none_when_daily_used_less_than_or_equal_number_free_profiles(self):

        actual_result = save_esimgo_profiles(
            bundle_code=self.esimgo_bundle.bundle_code,
            daily_used=0,
            bundle_vendor_code= self.esimgo_bundle.bundle_vendor_code,
            original_daily_used=0)

        self.assertIsNone(actual_result)
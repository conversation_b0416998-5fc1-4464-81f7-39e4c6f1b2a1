import os
import unittest
from datetime import datetime, timedelta
from app_models.consumer_models import (
    Bundles,
    Vendors,
    regions_,
    region_names_,
    Profiles,
)
from app_helpers.cron_helpers import vodafone_sync_profiles_count
from app_models.main_models import Settings, EmailSettings
from constaints import vodafone_vendor_name
from app_main import app


class TestVodafoneSyncProfilesCount(unittest.TestCase):
    def setUp(self):
        app.testing = True
        self.vodafone_vendor = (
            Vendors.objects(vendor_name=vodafone_vendor_name).first()
            or Vendors(
                **{
                    "vendor_name": vodafone_vendor_name,
                    "vendor_prefix": "Vo",
                    "vendor_suffix": "ne",
                    "bundles_count": 30,
                    "minimal_balance": 1,
                    "is_active": True,
                    "total_profiles_bought": 5000,
                }
            ).save()
        )

        self.settings = (
            Settings.objects(contact_email="<EMAIL>").first()
            or Settings(
                **{
                    "contact_email": "<EMAIL>",
                    "esim_email": "<EMAIL>",
                    "merchant_key": "merchant_key",
                    "merchant_password": "merchant_password",
                    "fcm_registration": "AAAAIyVYBdg:APA91bHuttY19nLtOvFzwJAeU3KUMwvIGvPmHgoRbnwp6S1HITHqEGFerUqoQCDhzq7hxzoQadOQX87FI1hZC0zPMZA9OiWq-Org4mkt3-pnRpPBK7xVPKOkdSCf4ZzR0J3xCMhrfqkI",
                    "whatsapp_misisdn": "1",
                    "client_keycloack_secret": "",
                    "reward_amount_limit_usd": 11,
                    "reward_per_purchase": 2,
                    "reward_type_category": 1,
                    "reward_user_refer_limit": 5,
                    "percentage_of_reward": 15,
                    "purchase_threshold_for_reward": 3,
                    "marketing_email": "<EMAIL>",
                    "notify_duration": 1,
                }
            ).save()
        )

        self.vodafone_bundle = (
            Bundles.objects(bundle_code="5G-Vod-used").first()
            or Bundles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "bundle_code": "5G-Vod-used",
                    "bundle_name": "used-5G",
                    "bundle_marketing_name": "fastest-used-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "retail_price": 1,
                    "bundle_vendor_name": self.vodafone_vendor.vendor_name,
                    "region_name": regions_[1],
                    "bundle_vendor_code": "5G-Vod-used",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "allocated_unit": 30,
                    "supplier_vendor": f"{self.vodafone_vendor.id}",
                    "bundle_category": "country",
                    "country_list": ["FR"],
                    "unit_price": "10",
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "validity_amount": "29",
                    "profile_names": "",
                    "is_active": True,
                    "country_code_list": ["FR"],
                }
            ).save()
        )

        self.vodafone_bundle2 = (
            Bundles.objects(bundle_code="3G-Vod-used").first()
            or Bundles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "bundle_code": "3G-Vod-used",
                    "bundle_name": "used-3G",
                    "bundle_marketing_name": "fastest-used-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "retail_price": 1,
                    "bundle_vendor_name": self.vodafone_vendor.vendor_name,
                    "region_name": regions_[1],
                    "bundle_vendor_code": "5G-Vod-used",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "allocated_unit": 30,
                    "supplier_vendor": f"{self.vodafone_vendor.id}",
                    "bundle_category": "country",
                    "country_list": ["FR"],
                    "unit_price": "10",
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "validity_amount": "29",
                    "profile_names": "",
                    "is_active": True,
                    "country_code_list": ["FR"],
                }
            ).save()
        )

        self.vodafone_bundle3 = (
            Bundles.objects(bundle_code="4G-Vod-used").first()
            or Bundles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "bundle_code": "4G-Vod-used",
                    "bundle_name": "used-4G",
                    "bundle_marketing_name": "fastest-used-4G",
                    "category_name": "1",
                    "region_code": region_names_[1],
                    "retail_price": 1,
                    "bundle_vendor_name": self.vodafone_vendor.vendor_name,
                    "region_name": regions_[1],
                    "bundle_vendor_code": "5G-Vod-used",
                    "rate_revenue": 1,
                    "create_datetime": datetime.utcnow(),
                    "bundle_duration": 30,
                    "allocated_unit": 30,
                    "supplier_vendor": f"{self.vodafone_vendor.id}",
                    "bundle_category": "country",
                    "country_list": ["FR"],
                    "unit_price": "10",
                    "data_amount": 30,
                    "fullspeed_data_amount": 16,
                    "data_unit": "GB",
                    "validity_amount": "29",
                    "profile_names": "",
                    "is_active": True,
                    "country_code_list": ["FR"],
                }
            ).save()
        )

        self.email_settings = (
            EmailSettings.objects(email="<EMAIL>").first()
            or EmailSettings(
                **{
                    "email": "<EMAIL>",
                    "username": "<EMAIL>",
                    "password": "s$XQR7R3@t",
                    "smtp_server": "smtp.office365.com",
                    "smtp_port": 587,
                }
            ).save()
        )

        self.profile = (
            Profiles.objects(iccid="22305670901234567890").first()
            or Profiles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "sku": "profile-id-unique-new-one",
                    "qr_code_value": None,
                    "profile_names": "",
                    "smdp_address": "rsp.2.test.client.com",
                    "matching_id": "None",
                    "bundle_code": self.vodafone_bundle.bundle_code,
                    "plan_uid": "5g-KLOW-20",
                    "create_datetime": datetime.utcnow(),
                    "availability": "Free",
                    "status": True,
                    "expiry_date": datetime.utcnow() + timedelta(90),
                    "iccid": "22305670901234567890",
                }
            ).save()
        )

        self.profile2 = (
            Profiles.objects(iccid="22305670001234567899").first()
            or Profiles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "sku": "22305670001234567899",
                    "qr_code_value": None,
                    "profile_names": "",
                    "smdp_address": "rsp.2.test.client.com",
                    "matching_id": "None",
                    "bundle_code": self.vodafone_bundle.bundle_code,
                    "plan_uid": "5g-KLOW-20",
                    "create_datetime": datetime.utcnow(),
                    "availability": "Free",
                    "status": True,
                    "expiry_date": datetime.utcnow() + timedelta(90),
                    "iccid": "22305670001234567899",
                }
            ).save()
        )

        self.profile3 = (
            Profiles.objects(iccid="22305670001234567890").first()
            or Profiles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "sku": "profile-nique-new-one",
                    "qr_code_value": None,
                    "profile_names": "",
                    "smdp_address": "rsp.2.test.client.com",
                    "matching_id": "None",
                    "bundle_code": self.vodafone_bundle2.bundle_code,
                    "plan_uid": "5g-KLOW-20",
                    "create_datetime": datetime.utcnow(),
                    "availability": "Free",
                    "status": True,
                    "expiry_date": datetime.utcnow() + timedelta(90),
                    "iccid": "22305670001234567890",
                }
            ).save()
        )

        self.profile4 = (
            Profiles.objects(iccid="22305670001234567891").first()
            or Profiles(
                **{
                    "vendor_name": self.vodafone_vendor.vendor_name,
                    "sku": "22305670001234567891",
                    "qr_code_value": None,
                    "profile_names": "",
                    "smdp_address": "rsp.2.test.client.com",
                    "matching_id": "None",
                    "bundle_code": self.vodafone_bundle3.bundle_code,
                    "plan_uid": "5g-KLOW-20",
                    "create_datetime": datetime.utcnow(),
                    "availability": "Free",
                    "status": True,
                    "expiry_date": datetime.utcnow() + timedelta(90),
                    "iccid": "22305670001234567891",
                }
            ).save()
        )

    def tearDown(self):
        self.vodafone_vendor.delete()
        self.vodafone_bundle.delete()
        self.vodafone_bundle3.delete()
        self.vodafone_bundle2.delete()
        Profiles.objects(vendor_name=vodafone_vendor_name).delete()
        self.settings.delete()
        self.email_settings.delete()
        Settings.objects(contact_email="<EMAIL>").delete()

    def test_vodafone_sync_profiles_count(self):
        sent_emails = []

        # Correct the indentation of the vodafone_sync_profiles_count call
        (
            message,
            vodafone_free_profiles_count,
            total_number_of_needed_profiles,
        ) = vodafone_sync_profiles_count()
        if message != "":
            self.assertEqual(message, "Emails sent successfully")
            self.assertEqual(2, vodafone_free_profiles_count)
            self.assertEqual(4996, total_number_of_needed_profiles)

import logging
from logging.config import dictConfig


logging_config = dict(
    version=1,
    formatters={
        'helper_formatter': {
            'format':
            '%(asctime)s %(filename)s:%(lineno)s - %(funcName)20s %(name)-12s %(levelname)-8s %(message)s'
        }
        },
    handlers={
        'helper_handler': {
            'class': 'logging.StreamHandler',
            'formatter': 'helper_formatter',
            'level': logging.INFO
    }},
    root={
        'handlers': ['helper_handler'],
        'level': logging.INFO,
    },
)

dictConfig(logging_config)


def get_logger(name):
    logger = logging.getLogger(name)
    return logger

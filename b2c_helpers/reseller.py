import datetime
import json
import logging
import types
import time
import requests
from app_models.reseller_models import TokenSuperAdmin, Reseller
from bson import ObjectId
import secrets
import hmac
import hashlib

from b2c_helpers.db_helper import save_reseller_notification, get_reseller_by_id


class ResellerHelper:
    def __init__(self, *args, **kwargs):
        self.instance_config: types.ModuleType = kwargs.get("instance_config")
        if not getattr(self.instance_config, "reseller_url"):
            raise AttributeError("Reseller URL not found in instance_config")
        if not getattr(self.instance_config, "reseller_super_admin_username"):
            raise AttributeError("Reseller super admin username not found in instance_config")
        if not getattr(self.instance_config, "reseller_super_admin_password"):
            raise AttributeError("Reseller super admin password not found in instance_config")
        self.base_url = self.instance_config.reseller_url
        self.affiliate_url = self.base_url + "/Affiliate?affiliate_id={affiliate_id}"
        self.send_consumption_email_url = (self.instance_config.reseller_url
                                           + '/Orders/SendConsumptionEmail?reseller_id={reseller_id}&notification_type={notification_type}')

    def get_token_super_admin_reseller(self):
        try:
            payload = json.dumps({
                "username": self.instance_config.reseller_super_admin_username,
                "password": self.instance_config.reseller_super_admin_password
            })
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            r = requests.request("POST", '{}/Agent/login'.format(self.instance_config.reseller_url), headers=headers,
                                 data=payload)
            response = r.json()
            if r.status_code != 200:
                return False, "Couldn't login Reseller"
            return response, "Success"

        except Exception as e:
            logging.error(e)
            return False, "Couldn't login Reseller"

    def send_data_consumption_email_api_reseller(self, order_id, reseller_id, notification_type, iccid):
        notification_type_mapping = {
            "StartBundle": "Started",
            "limit_80":    "Eighty",
            "limit_100":   "Expired"
        }
        notification_type = notification_type_mapping.get(notification_type, notification_type)
        access_token, message = self.get_token_super_admin_reseller()
        logging.info(message)
        super_admin = TokenSuperAdmin.objects(role="super_admin").first()
        if access_token:
            if not super_admin:
                TokenSuperAdmin(role="super_admin", temp_token=access_token['access_token']).save()
            else:
                TokenSuperAdmin.objects(role="super_admin").update(set__temp_token=access_token['access_token'])

        self.start_reseller_notify(notification_type, iccid, order_id, reseller_id)

        self.send_consumption_email_url = self.send_consumption_email_url.format(reseller_id=reseller_id,
                                                                                 notification_type=notification_type)

        payload = json.dumps({"order_id": order_id})

        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Access-Token': access_token['access_token']
        }

        response = requests.request("POST", self.send_consumption_email_url, headers=headers, data=payload)
        print(response.text)
        return response

    def get_affiliate_program(self, affiliate_id):
        response = requests.request("GET", self.affiliate_url.format(affiliate_id=affiliate_id))
        return response.json() if response.status_code == 200 else {}

    def generate_signature(self, payload, secret_key):
        payload_str = json.dumps(payload, sort_keys=True)  # Ensure sorted keys for consistency
        signature = hmac.new(secret_key.encode(), payload_str.encode(), hashlib.sha256).hexdigest()
        return signature

    def push_callback_notification(self, url, json_payload, reseller= None, signature=None):

        max_retries = 2
        timeout = 25
        timeout = (10, 30)
        retry_delay = 5
        headers = {}
        if signature:
            headers["Signature"] = signature
        if reseller.api_key:
            headers["API-Key"] = reseller.api_key
        if reseller.tenant:
            headers["Tenant"] = reseller.tenant

        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=json_payload, headers=headers, timeout=timeout)
                logging.info("response returned: %s", response)
                if response.status_code == 200:
                    logging.info("Notification successfully pushed to {}".format(url))
                    return True
                else:
                    logging.warning(
                        "Received status code {} while pushing notification to {}".format(response.status_code, url))
            except requests.Timeout:
                logging.warning("Request timed out while pushing notification to {}".format(url))
            except requests.RequestException as e:
                logging.warning("Error occurred while pushing notification to {}: {}".format(url, e))

            # Retry after delay
            if attempt < max_retries - 1:
                time.sleep(retry_delay)

        logging.error("Failed to push notification to {} after {} attempts".format(url, max_retries))

    def send_reseller_push_notification(self, iccid, order_id, event_type, reseller_id):
        try:
            logging.info("we are sending push notification as {}".format(event_type))
            reseller = Reseller.objects(_id=ObjectId(reseller_id)).first()

            if not reseller:
                logging.error("Reseller not found")
                return

            if not reseller.secret_key:
                reseller.secret_key = secrets.token_hex(32)
                reseller.save()

            if event_type == "StartBundle":
                event_type = "BUNDLE_STARTED"
            elif event_type == "limit_80":
                event_type = "BUNDLE_SIGNIFICANT_CONSUMPTION"
            elif event_type == "limit_100":
                event_type = "BUNDLE_EXPIRED"
            else:
                return

            event_type_mapping = {
                "BUNDLE_STARTED",
                "BUNDLE_SIGNIFICANT_CONSUMPTION",
                "BUNDLE_EXPIRED"
            }


            json_payload = {
                "iccid": iccid,
                "order_id": order_id,
                "event_type": event_type,
                "event_date": str(datetime.datetime.utcnow()),
                "signature": self.generate_signature(payload={
                    "iccid": iccid,
                    "order_id": order_id,
                    "event_type": event_type,
                    "event_date": str(datetime.datetime.utcnow())
                }, secret_key=reseller.secret_key),
                "allowed_event_types": [e for e in event_type_mapping]
            }

            if reseller and reseller.callback_url:
                # IF later on signature is required, we can get reseller.signature and send it in this function
                self.push_callback_notification(reseller.callback_url, json_payload, reseller)

        except Exception as e:
            logging.error(str(e))

    def start_reseller_notify(self, event_type, iccid, order_id, reseller_id):
        # start pre-notify reseller for consumption notification
        payload = {
            "event_date": datetime.datetime.utcnow().isoformat(),
            "event_type": event_type,
            "iccid": iccid,
            "order_id": order_id
        }
        # get reseller by id
        reseller_by_id = get_reseller_by_id(reseller_id)
        if not reseller_by_id:
            logging.warning("Reseller %s not found", reseller_id)
            return
        # no need to continue in case notification type is not webhook
        if reseller_by_id.notification_type != "webhook":
            logging.warning("Reseller %s will not be notified with consumption notification", reseller_id)
            return
        # no need to continue in case webhook url not found
        if not reseller_by_id.consumption_url:
            logging.warning("Reseller %s will not be notified due webhook url not found", reseller_id)
            return

        save_reseller_notification(reseller_id, iccid, order_id, reseller_by_id.notification_type, event_type,
                                   json.dumps(payload))

from datetime import datetime

from app_models.consumer_models import Bundles, Profiles
from bson import ObjectId
from werkzeug import exceptions
from .db_helper import get_bundle_by_code, get_vendor_info, get_stage_preview_for_bundles
from .bundle_formatter import bundle_formatter
from .constaints import ASCENDING_ORDER
from .inputs import TopupRelatedV2FilterInput


def get_stage_allocated_unit_shall_be_greater_than_consumed_unit(vendor=None):
    stage_allocated_unit_shall_be_greater_than_consumed_unit = {
        '$match': {
            '$expr': {
                '$gt': ["$allocated_unit", "$consumed_unit"]
            }
        }
    }
    return stage_allocated_unit_shall_be_greater_than_consumed_unit


def get_stage_unit_price_and_group_id_check(group_id=None):
    stage_unit_price_shall_be_greater_than_zero = {
        '$match': {
            '$expr': {
                '$gt': ["$unit_price", 0]
            }
        }
    }
    if group_id:
        stage_unit_price_shall_be_greater_than_zero['$match']['group_id'] = group_id
    return stage_unit_price_shall_be_greater_than_zero


def get_stage_join_with_vendor():
    stage_join_with_vendor = {
        '$lookup': {
            'from': 'vendors',
            'localField': 'vendor_name',
            'foreignField': 'vendor_name',
            'as': 'vendor_info'
        }
    }
    return stage_join_with_vendor


def get_stage_unpack_vendor():
    stage_unpack_vendor = {
        '$unwind': '$vendor_info'
    }
    return stage_unpack_vendor


def get_stage_check_vendor_activation():
    stage_check_vendor_activation = {
        '$match': {
            'vendor_info.is_active': True,
            'vendor_info.support_topup': True
        }
    }
    return stage_check_vendor_activation


def get_stage_join_with_reseller_bundle_price(reseller_id):
    stage_join_with_reseller_bundle_price = {
        '$lookup': {
            'from': 'reseller_bundle_price',
            'as': 'result',
            'let': {
                'rebundle_code': '$bundle_code'
            },
            'pipeline': [{
                '$match': {
                    '$expr': {
                        '$and': [
                            {
                                '$eq': [
                                    '$reseller_id', ObjectId(reseller_id)
                                ]
                            }, {
                                '$eq': [
                                    '$bundle_code', '$$rebundle_code'
                                ]
                            }
                        ]
                    }
                }
            }]
        }
    }
    return stage_join_with_reseller_bundle_price


def get_stage_join_with_reseller(reseller_id):
    stage_join_with_reseller = {
        '$lookup': {
            'from': 'reseller',
            'as': 'result_reseller',
            'pipeline': [
                {
                    '$match': {
                        '_id': ObjectId(reseller_id)
                    }
                }
            ]
        }
    }
    return stage_join_with_reseller


def get_stage_unpack_retail_price():
    stage_unpack_retail_price = {
        '$unwind': {
            'path': '$result',
            'preserveNullAndEmptyArrays': True
        }
    }
    return stage_unpack_retail_price


def get_stage_unpack_reseller():
    stage_unpack_reseller = {
        '$unwind': {
            'path': '$result_reseller',
            'preserveNullAndEmptyArrays': True
        }
    }
    return stage_unpack_reseller


def get_stage_sorting(sort_by, sorting_order):
    stage_sorting = {
        '$sort': {
            sort_by: sorting_order,
            'bundle_name': ASCENDING_ORDER,
            'bundle_code': ASCENDING_ORDER
        }
    }
    return stage_sorting


def get_stage_skip(skip):
    stage_skip = {
        '$skip': skip
    }
    return stage_skip


def get_stage_limit(page_size):
    stage_limit = {
        '$limit': page_size
    }
    return stage_limit


def get_stage_match_bundle_that_active_bundle_tag_bundle_name(bundle_name=None, bundle_tag=None, region_code=None):
    stage_match_bundle_that_active_bundle_tag_bundle_name = {
        '$match': {
            'is_active_corp': {'$ne': False},
            'is_active': {'$ne': False},
        }
    }
    if bundle_name:
        stage_match_bundle_that_active_bundle_tag_bundle_name['match']['bundle_name'] = {
            "$regex": bundle_name,
            "$options": "i"  # The "i" option makes the search case-insensitive
        }
    if bundle_tag:
        stage_match_bundle_that_active_bundle_tag_bundle_name["$match"]["bundle_tag"] = {
            "$regex": bundle_tag,
            "$options": "i"  # The "i" option makes the search case-insensitive
        }
    return stage_match_bundle_that_active_bundle_tag_bundle_name


def get_stage_return_only_needed_values():
    stage_return_only_needed_values = {
        '$project': {
            'custom_bundle_name': "$result.custom_bundle_name",
            'bundle_name': 1,
            'bundle_tag': "$result.bundle_tag",
            'lowercase_bundle_name': {"$toLower": "$bundle_name"},
            'bundle_marketing_name': 1,
            'bundle_code': 1,
            'region_name': 1,
            'country_code_list': 1,
            'country_list': 1,
            'region_code': 1,
            'reseller_retail_price': '$result.bundle_retail_price',
            'retail_price': '$result.corp_retail_price',
            'rate_revenue': '$result_reseller.rate_revenue',
            'corp_rate_revenue': '$result_reseller.corp_rate_revenue',
            'bundle_duration': 1,
            'data_amount': 1,
            'data_unit': 1,
            'currency_code': 1,
            'bundle_category': 1,
            'unit_price': 1,
            'is_active': '$result.is_active',
            'is_active_corp': '$result.is_active_corp',
            'unlimited':1
        }
    }
    return stage_return_only_needed_values


def get_stage_add_bundle_name_field():
    stage_add_bundle_name_field = {
        '$addFields': {
            'bundle_name': {
                '$cond': {
                    'if': {
                        '$ifNull': [
                            '$custom_bundle_name', False
                        ]
                    },
                    'then': '$custom_bundle_name',
                    'else': '$bundle_name'
                }
            }
        }
    }
    return stage_add_bundle_name_field


def get_stage_calculate_retail_price_based_on_unit_price():
    stage_calculate_retail_price_based_on_unit_price = {
        '$addFields': {
            'retail_price': {
                '$function': {
                    'body': 'function(retail_price, unit_price, corp_rate_revenue){ if (!retail_price){retail_price= (unit_price * (100 + corp_rate_revenue)) / 100;}return retail_price}',
                    'args': [
                        '$retail_price', '$unit_price', '$corp_rate_revenue'
                    ],
                    'lang': 'js'
                }
            }
        }
    }
    return stage_calculate_retail_price_based_on_unit_price


def get_stage_add_reseller_retail_price():
    stage_add_reseller_retail_price = {
        '$addFields': {
            'reseller_retail_price': {
                '$function': {
                    'body': 'function(reseller_retail_price, reseller_unit_price, rate_revenue){ if (!reseller_retail_price){ reseller_retail_price= (reseller_unit_price * (100 + rate_revenue)) / 100; } return reseller_retail_price}',
                    'args': [
                        '$reseller_retail_price', '$retail_price', '$rate_revenue'
                    ],
                    'lang': 'js'
                }
            }
        }
    }
    return stage_add_reseller_retail_price


def get_pipeline_for_topup_related_v2_reseller(
        topup_related_v2_input: TopupRelatedV2FilterInput, skip=0, page_size=50, sort_by='retail_price',
        sorting_order=ASCENDING_ORDER, bundle_name=None, bundle_tag=None, region_code=None, vendor_name=None,
        currency_code=None):
    pipeline = [
        get_stage_preview_for_bundles("reseller"),
        get_stage_unit_price_and_group_id_check(topup_related_v2_input.group_id),
        get_stage_allocated_unit_shall_be_greater_than_consumed_unit(vendor_name),
        get_stage_join_with_reseller_bundle_price(topup_related_v2_input.reseller_id),
        get_stage_unpack_retail_price(),
        get_stage_join_with_reseller(topup_related_v2_input.reseller_id),
        get_stage_unpack_reseller(),
        get_stage_join_with_vendor(),
        get_stage_unpack_vendor(),
        get_stage_check_vendor_activation(),
        get_stage_return_only_needed_values(),
        get_stage_add_bundle_name_field(),
        get_stage_match_bundle_that_active_bundle_tag_bundle_name(),
        get_stage_calculate_retail_price_based_on_unit_price(),
        get_stage_add_reseller_retail_price(),
        get_stage_sorting(sort_by, sorting_order),
        get_stage_skip(skip),
        get_stage_limit(page_size)
    ]
    if currency_code:
        pipeline.extend([{
            '$addFields': {
                'additional_currency_code': currency_code
            }
        }, {
            '$lookup': {
                'from': 'currency_codes',
                'let': {'additional_currency_code': '$additional_currency_code'},
                'pipeline': [
                    {
                        '$match': {
                            '$expr': {'$eq': ['$currency_code', '$$additional_currency_code']},
                            'is_available': True
                        }
                    }
                ],
                'as': 'currency'
            }
        }, {
            '$unwind': {
                'path': '$currency',
                'preserveNullAndEmptyArrays': True
            }
        }, {
            '$addFields': {
                'reseller_retail_price_in_additional_currency': {
                    '$cond': {
                        'if': {
                            '$ifNull': [
                                '$currency', False
                            ]
                        },
                        'then': {
                            '$multiply': [
                                '$reseller_retail_price', '$currency.currency_rate'
                            ]
                        },
                        'else': None
                    }
                }
            }
        }, {
            '$addFields': {
                'retail_price_in_additional_currency': {
                    '$cond': {
                        'if': {
                            '$ifNull': [
                                '$currency', False
                            ]
                        },
                        'then': {
                            '$multiply': [
                                '$retail_price', '$currency.currency_rate'
                            ]
                        },
                        'else': None
                    }
                }
            }
        }, {
            '$addFields': {
                'reseller_retail_price_in_additional_currency': {
                    '$round': [
                        '$reseller_retail_price_in_additional_currency', 2
                    ]
                },
                'retail_price_in_additional_currency': {
                    '$round': [
                        '$retail_price_in_additional_currency', 2
                    ]
                }
            }
        }])

    return pipeline


def get_topup_related_bundle_query_set(pipeline, query_set):
    return Bundles.objects(**query_set).aggregate(*pipeline)


def get_topup_related_v2_reseller(
        reseller_id, bundle_code, country_code, region_code, bundle_name, bundle_tag, page_number=1,
        page_size=50, sort_by='retail_price', sorting_order=ASCENDING_ORDER, currency_code=None, iccid=None):
    bundle = get_bundle_by_code(bundle_code)
    if not bundle or "reseller" not in bundle.preview_for:
        return [], 0

    if country_code and (country_code not in bundle.country_code_list):
        return [], 0

    topup_related_v2_input = TopupRelatedV2FilterInput(
        reseller_id=reseller_id, bundle_category=bundle.bundle_category, country_code=bundle.country_code_list,
        bundle_code=bundle_code, profile_name=bundle.profile_names, vendor_name=bundle.vendor_name,
        region_code=region_code or bundle.region_code, bundle_name=bundle.bundle_name, is_topup=True,
        group_id=bundle.group_id
    )

    skip = (page_number - 1) * page_size

    pipeline = get_pipeline_for_topup_related_v2_reseller(
        topup_related_v2_input=topup_related_v2_input, skip=skip, page_size=page_size, sort_by=sort_by,
        sorting_order=sorting_order, bundle_name=bundle_name, bundle_tag=bundle_tag, region_code=region_code,
        vendor_name=bundle.vendor_name, currency_code=currency_code
    )

    query_filter = {
        'bundle_category': topup_related_v2_input.bundle_category,
        'profile_names': topup_related_v2_input.profile_name,
        'vendor_name': topup_related_v2_input.vendor_name,
        'region_code': topup_related_v2_input.region_code,
        'country_code_list': topup_related_v2_input.country_code,
        'support_topup__ne': False
    }

    if topup_related_v2_input.is_topup:
        query_filter['deleted'] = False

    vendor = get_vendor_info(bundle.vendor_name)
    if vendor and not vendor.apply_expiry:
        return bundle_formatter(get_topup_related_bundle_query_set(pipeline, query_filter), currency_code=currency_code)

    if iccid is not None:
        profile = Profiles.objects(iccid=iccid, availability__ne='Expired').first()
        if not profile:
            return [], 0

        expiry_date = profile.expiry_date
        if not expiry_date:
            max_bundle_duration = 90
        else:
            max_bundle_duration = (expiry_date - datetime.utcnow()).days

        if max_bundle_duration < 0:
            print(f"max_bundle_duration is less than Zero for iccid: {iccid}")
            return [], 0

        query_filter['bundle_duration__lte'] = max_bundle_duration

    return bundle_formatter(get_topup_related_bundle_query_set(pipeline, query_filter), currency_code=currency_code)

import base64
import logging
import os
import datetime
import re
import smtplib
import uuid
from email.mime.image import MIMEImage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from app_models import user_email_template_models
from app_models.consumer_models import Languages
from app_models.user_email_template_models import template_category
from mongoengine import Q

from b2c_helpers.common_utils import is_nonempty_nonspace
from b2c_helpers.support_helper import get_email_settings
from jinja2 import Environment, FileSystemLoader, select_autoescape, Template, StrictUndefined, meta
import pyqrcode
from base64 import b64decode

branch_limit_exceed_message = '''
Dear {}

The maximum spending limit for your branch with the following details, has been reached:

Branch Name: {}

Branch limit: {}

Please be advised that we'll no longer process your eSIM orders from this branch. Remember you can always change your branch limit manually. You can do this by logging into your account, and adjusting the branch limit.

1. Go to your reseller account page

2. Click "View Branches" to view all active branches.

3. Select "Edit branch" and adjust the branch limit

If you need help, or have any questions please don't hesitate to reach out to us <NAME_EMAIL>

IMPORTANT: This is an automatic system generated message. Please do not reply.

Monty eSIM Team'''


def create_qr_code(plain_str_for_qr_generation, file_generate=None):
    url = pyqrcode.create(plain_str_for_qr_generation)
    """
    Create QR Image
    """
    im_qr_bytes = b64decode(url.png_as_base64_str(scale=8))
    if file_generate is not None:
        with open(file_generate, "wb") as fh:
            fh.write(im_qr_bytes)
    return im_qr_bytes


def get_email_template(category: str, language_code: str = None, is_active=True):
    invalid_language_message = "language_code is invalid or unsupported."

    if not is_nonempty_nonspace(category):
        raise ValueError("category must be a valid value")

    retrieved_languages = Languages.objects(Q(language_code=language_code) | Q(default_lang=True))

    fallback_language_code = None
    found_primary_language = False
    for language in retrieved_languages:
        if language.language_code != language_code:
            fallback_language_code = language.language_code
        else:
            found_primary_language = True

    if not language_code and not found_primary_language:
        raise ValueError(invalid_language_message)

    match_conditions = [
        {"contents.language_code": language_code}
    ]

    if fallback_language_code:
        match_conditions.append({"contents.language_code": fallback_language_code})

    aggregate_pipe = [
        {
            "$match": {
                "category": category,
                "is_active": is_active
            }
        },
        {
            "$unwind": "$contents"
        },
        {
            "$match": {
                "$or": match_conditions
            }
        },
        {
            "$group": {
                "_id": "$_id",
                "category": {"$first": "$category"},
                "is_active": {"$first": "$is_active"},
                "contents": {
                    "$push": {
                        "content": "$contents.content",
                        "language_code": "$contents.language_code",
                        "email_subject": "$contents.email_subject",
                        "is_primary": {
                            "$cond": {
                                "if": {"$eq": ["$contents.language_code", language_code]},
                                "then": True,
                                "else": False
                            }
                        },
                        "is_fallback": {
                            "$cond": {
                                "if": {"$and": [
                                    {"$eq": ["$contents.language_code", fallback_language_code]},
                                    {"$ne": [fallback_language_code, None]}
                                ]},
                                "then": True,
                                "else": False
                            }
                        }
                    }
                }
            }
        }
    ]

    retrieved_templates_aggr = user_email_template_models.UserEmailTemplates.objects().aggregate(aggregate_pipe)

    try:
        result = next(retrieved_templates_aggr)
    except StopIteration:
        result = None

    return result


def extract_variables_from_template(template):
    control_structure_keywords = [
        'if',  # Begin an if block
        'elif',  # Additional condition in an if block
        'else',  # Alternative branch in an if block
        'endif',  # End an if block
        'for',  # Begin a for loop
        'endfor',  # End a for loop
        'macro',  # Define a macro
        'endmacro',  # End a macro
        'block',  # Define a block in a template
        'endblock',  # End a block
        'extends',  # Inherit from another template
        'include',  # Include another template
        'import',  # Import macros from another template
        'from',  # Import specific items from a module
        'set',  # Define a variable
        'raw',  # Start raw block
        'endraw',  # End raw block
        'comment',  # Start a comment
        'endcomment',  # End a comment
        'defined'
    ]

    # Regular expression patterns
    jinjia_env = Environment()
    parsed_content = jinjia_env.parse(template)
    prefixes = meta.find_undeclared_variables(parsed_content)
    prefix_pattern = "|".join(prefixes)

    variable_pattern = fr"(?:{prefix_pattern})\.[a-zA-Z0-9_]+"

    # Find all matches
    matches = re.findall(variable_pattern, template)

    # Extract and display variables
    variables = set()
    for match in matches:
        if match not in control_structure_keywords:
            variables.add(match)

    combined_set = variables.union(prefixes)

    sorted_variables = sorted(combined_set)

    filtered_elements = set()

    for element in sorted_variables:
        # If the element is not a prefix of any other element in the list, keep it
        if not any(e.startswith(f"{element}.") for e in sorted_variables):
            filtered_elements.add(element)

    return sorted(list(filtered_elements))


def check_keys_in_list(fields_list, data_dict):
    """
    # Sample data
    fields_list = ['data.name', 'header', 'page_title', 'user.age', 'user.is_active', 'user.name', 'data.address.ad1']
    data_dict = {
        'user': {'age': 'AGE', 'is_active': True, 'name': 'NAME'},
        'data': {'name': 'DATA NAME', 'address': { 'ad1': "AD1" }},
        'header': 'HEADER',
        'page_title': 'PAGE_TITLE'
    }
    """

    def extract_keys(d, prefix=''):
        """ Recursively extract all possible keys from a nested dictionary. """
        keys = set()
        for k, v in d.items():
            full_key = f"{prefix}.{k}" if prefix else k
            if isinstance(v, dict):
                keys.update(extract_keys(v, full_key))
            else:
                keys.add(full_key)
        return keys

    # Extract all possible keys from data_dict
    available_keys = extract_keys(data_dict)
    # Find unbounded fields
    unbounded_fields = set(fields_list) - available_keys

    return unbounded_fields


def construct_data_dict(keys, **kwargs):
    def list_to_dict(keys):
        result = {}

        for key in keys:
            parts = key.split('.')
            d = result

            for part in parts[:-1]:
                if part not in d:
                    d[part] = {}
                d = d[part]

            d[parts[-1]] = ""

        return result

    def update_dict_with_top_level(base_dict, **kwargs):
        def set_value_at_level(d, key, value):
            for k in list(d.keys()):
                if isinstance(d[k], dict):
                    set_value_at_level(d[k], key, value)
            if key in d:
                d[key] = value

        for key, value in kwargs.items():
            set_value_at_level(base_dict, key, value)

        return base_dict

    def set_special_key(d, special_key, value):
        """Recursively searches for the special_key and sets its value if found."""
        if special_key in d:
            d[special_key] = value
        for k, v in d.items():
            if isinstance(v, dict):
                set_special_key(v, special_key, value)

    def find_value_in_dict(d, target_key):
        """Recursively searches for the target_key in the dictionary and returns its value if found."""
        if target_key in d:
            return d[target_key]
        for value in d.values():
            if isinstance(value, dict):
                found = find_value_in_dict(value, target_key)
                if found:
                    return found
        return None

    base_dict = list_to_dict(keys)
    updated_dict = update_dict_with_top_level(base_dict, **kwargs)

    # ----------------------------------------------------------------------
    # Special handling for any fields that require special handling
    # ----------------------------------------------------------------------
    iccid = kwargs.get('iccid', '')
    fe_dynamic_url = kwargs.get('fe_dynamic_url', '')

    if iccid:
        if not find_value_in_dict(updated_dict, 'fe_url'):
            set_special_key(updated_dict, 'fe_url', "{}/consumption?iccid={}".format(fe_dynamic_url, iccid))

    data_amount_value = find_value_in_dict(updated_dict, 'data_amount')
    if data_amount_value is not None:
        if data_amount_value < 0:
            set_special_key(updated_dict, 'data_amount', "∞")
            set_special_key(updated_dict, 'data_unit', "")
        else:
            set_special_key(updated_dict, 'data_amount', str(data_amount_value))

    return updated_dict


def decoded_email_template(text_to_be_decoded):
    base64_bytes = text_to_be_decoded.encode('utf-8')
    original_bytes = base64.b64decode(base64_bytes)
    decoded_html_template = original_bytes.decode('utf-8')

    return decoded_html_template


def encrypt_email_template(text_to_be_encrypted):
    text_bytes = text_to_be_encrypted.encode('utf-8')

    # Encode the bytes to Base64
    base64_bytes = base64.b64encode(text_bytes)

    text_base64 = base64_bytes.decode('utf-8')

    return text_base64


def render_email_template(html_template_data, strict_undefined=True, exclusion_list=None, **data):
    """
    exclusion_list = the values that can be empty, it makes the strictness of the method partial.

    val = render_email_template(
            html_template_data=html_template_data_from_mongodb,
            exclusion_list=[],
            jinja_data={
            "data": {
                "user": "Mohammad Jaafar",
                "bundle_name": "bundle_123456",
                "montyesim_msisdn": "!234"
            },
            "fe_url": "1234"
        }
        )
    """

    if not html_template_data:
        raise ValueError("Unable to find any template.")

    html_to_be_decoded = None
    for temp in html_template_data["contents"]:
        if temp.get("content", None):
            html_to_be_decoded = temp["content"]
            if temp["is_primary"]:
                break

    if not html_to_be_decoded:
        raise ValueError("Failed to decode HTML Template.")

    html_template = decoded_email_template(html_to_be_decoded)

    extracted_fields = extract_variables_from_template(html_template)

    if not data:
        jinja_data = {}
    else:
        jinja_data = construct_data_dict(extracted_fields, **data)

    if not strict_undefined:
        return Template(html_template).render(**jinja_data), jinja_data

    # StrictUndefined Only checks for Top Level, this code checks for low level as well

    unbounded_fields = check_keys_in_list(fields_list=extracted_fields, data_dict=jinja_data)

    if exclusion_list and len(exclusion_list) > 0:
        unbounded_fields = unbounded_fields - set(exclusion_list)

    if len(unbounded_fields) > 0:
        raise ValueError(f"Following fields have not been supplied: {', '.join(unbounded_fields)}")

    # partially strict
    if exclusion_list and len(exclusion_list) > 0:
        return Template(html_template).render(**jinja_data), jinja_data

    return Template(html_template, undefined=StrictUndefined).render(**jinja_data), jinja_data


class SubscriberEmailHelper:
    email_verification_keys = ["subject", "greeting", "verification_message",
                               'pin_note', 'press_directly',
                               'verify_link_text', 'team_name']

    # data_consumption = []
    def __init__(self, *args, **kwargs):
        # self.template_mapping = None
        self.instance_config = kwargs.get("instance_config")
        if not hasattr(self.instance_config, "front_end_url"):
            raise AttributeError("Frontend dynamic URL not found in instance_config")
        self.template_mapping = None
        self.fe_dynamic_url = self.instance_config.front_end_url
        self.template_base_path = kwargs.get("template_base_path", 'html_templates/')
        self.customer = kwargs.get("customer")

        if self.customer.language == None or self.customer.language == "":
            self.customer.language = "en"
        self.language_object = Languages.objects(language_code=self.customer.language).first()

        self.email_settings = get_email_settings()
        # Ensure essential attributes are available

    def send_email(self, subject, body, attachment=True, has_file=True, file_path=None):
        smtp_server = smtplib.SMTP(self.email_settings.smtp_server, self.email_settings.smtp_port)
        smtp_server.ehlo()
        smtp_server.starttls()
        smtp_server.login(self.email_settings.username, self.email_settings.password)
        header = (f"To: {self.customer.user_email}\n"
                  f"From: {self.email_settings.username}\n"
                  f"Subject: {subject}\n")
        msg = (f"{header}\n"
               f"{body}\n\n")
        if attachment:
            msg = MIMEMultipart()
            msg['Subject'] = subject
            if has_file and file_path is not None:
                with open(file_path, 'rb') as f:
                    img_data = f.read()
                image = MIMEImage(img_data, name='Qrcode.png')
                msg.add_header('Content-ID', 'Qrcode')
                msg.attach(image)

            text = MIMEText(body, 'html')
            msg.attach(text)
            msg = msg.as_string()
        smtp_server.sendmail(self.email_settings.username, self.customer.user_email, msg)
        smtp_server.close()

    @classmethod
    def add_missing_keys(cls, email_template_keys, email_json):
        for key in email_template_keys:
            # Check if the key exists in any dictionary in A
            key_exists = any(key in d for d in email_json)
            # If the key doesn't exist in any dictionary, add it with an empty string value
            if not key_exists:
                email_json[key] = ''
        return email_json

    def get_template_path(self, filename: str) -> str:
        """Returns the complete path to the email template."""
        return os.path.join(self.template_base_path, filename)

    def render_email_template(self, template_filename: str, **kwargs) -> str:
        """Renders the given email template with provided context variables."""
        path = self.get_template_path(template_filename)
        env = Environment(loader=FileSystemLoader(searchpath=os.path.dirname(path)),
                          autoescape=select_autoescape(['html', 'xml']))
        template = env.get_template(os.path.basename(path))
        kwargs["app_name"] = "e-SIM"
        return template.render(**kwargs)

    def _send_email_generic(self, html_template, exclusion_list=None, file_path=None, **data_kwargs):

        try:

            subject = None
            for temp in html_template["contents"]:
                if temp.get("content", None):
                    subject = temp["email_subject"]
                    if temp["is_primary"]:
                        break

            if data_kwargs:
                data_kwargs["fe_dynamic_url"] = self.fe_dynamic_url

            body, jinja_data = render_email_template(html_template_data=html_template, exclusion_list=exclusion_list,
                                                     **data_kwargs)

            self.send_email(subject=subject, body=body, file_path=file_path)

            return jinja_data
        except Exception as exception:
            logging.info("Couldn't send email for customer %s, exception: %s", self.customer.user_email, exception)

    def __get_html_template(self, category: str):

        exclusion_dict = {
            template_category.consumption_email: ['data.user'],
            template_category.consumption_low_usage_email: ['data.user'],
            template_category.verification_email: ["data.user_name"],
            template_category.send_invoice_email: ["data.user", "data.price"],
            template_category.send_topup_email: ["data.user", "data.price", "data.coverage"]
        }

        lang_obj = self.language_object.to_mongo().to_dict() if self.language_object else {}
        lang_code = lang_obj.get("language_code", None)
        html_template = get_email_template(category=category, language_code=lang_code)

        keys_that_can_be_empty = exclusion_dict[category] if category in exclusion_dict else []

        return html_template, keys_that_can_be_empty

    def send_consumption_email(self, **data):

        html_template, keys_that_can_be_empty = self.__get_html_template(category=template_category.consumption_email)

        jinja_data_for_debugging = self._send_email_generic(html_template, exclusion_list=keys_that_can_be_empty,
                                                            **data)

        return jinja_data_for_debugging

    def send_consumption_email__low_usage(self, **data):

        html_template, keys_that_can_be_empty = self.__get_html_template(
            category=template_category.consumption_low_usage_email)

        jinja_data_for_debugging = self._send_email_generic(html_template, exclusion_list=keys_that_can_be_empty,
                                                            **data)

        return jinja_data_for_debugging

    def send_email_verification(self, **data):

        html_template, keys_that_can_be_empty = self.__get_html_template(category=template_category.verification_email)

        jinja_data_for_debugging = self._send_email_generic(html_template, exclusion_list=keys_that_can_be_empty,
                                                            **data)

        return jinja_data_for_debugging

    def send_invoice_email(self, **data):

        html_template, keys_that_can_be_empty = self.__get_html_template(category=template_category.send_invoice_email)

        qr_path = os.path.join(os.path.abspath("src/qr_codes"), f"qr-{uuid.uuid4()}.png")
        create_qr_code(data.get("qr_code_value"), qr_path)

        jinja_data_for_debugging = self._send_email_generic(html_template, file_path=qr_path,
                                                            exclusion_list=keys_that_can_be_empty, **data)

        os.remove(qr_path)

        return jinja_data_for_debugging

    def send_topup_email(self, **data):

        html_template, keys_that_can_be_empty = self.__get_html_template(category=template_category.send_topup_email)

        jinja_data_for_debugging = self._send_email_generic(html_template, exclusion_list=keys_that_can_be_empty,
                                                            **data)

        return jinja_data_for_debugging

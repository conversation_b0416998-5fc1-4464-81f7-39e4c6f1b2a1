def bundle_formatter(bundles,currency_code=None):
    bundles_list = []
    total = 0

    for bundle in bundles:
        total = total + 1

        country_code_list = bundle['country_code_list']
        if 'ISR' in country_code_list:
            country_code_list.remove('ISR')

        country_list = bundle["country_list"]
        if 'Israel' in country_list:
            country_list.remove('Israel')

        new_bundle = {
            "bundle_code": bundle["bundle_code"],
            "bundle_name": bundle['bundle_name'],
            "bundle_marketing_name": bundle['bundle_marketing_name'],
            "bundle_category": bundle['bundle_category'],
            "country_code": country_code_list,
            "country_name": country_list,
            "region_code": bundle['region_code'],
            "region_name": bundle['region_name'],
            "currency_code_list": [bundle['currency_code']],
            "data_unit": bundle["data_unit"],
            "gprs_limit": bundle["data_amount"],
            "subscriber_price": bundle['retail_price'],
            "validity": bundle["bundle_duration"],
            'reseller_retail_price': bundle.get('reseller_retail_price', 0),
            'unit_price': bundle.get('unit_price', 0),
            'reseller_rate_revenue': bundle.get('rate_revenue', 0),
            'corp_rate_revenue': bundle.get('corp_rate_revenue', 0),
            'unlimited': bundle['unlimited']
        }
        if currency_code:
            new_bundle["additional_currency_code"] = bundle.get('additional_currency_code', None)
            new_bundle["subscriber_price_in_additional_currency"] = bundle.get("retail_price_in_additional_currency",
                                                                               None)
            new_bundle["reseller_retail_price_in_additional_currency"] = bundle.get(
                "reseller_retail_price_in_additional_currency", None)
        bundles_list.append(new_bundle)
    return bundles_list, total

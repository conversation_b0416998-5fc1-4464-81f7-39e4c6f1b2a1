import logging

import mongoengine
from app_models.consumer_models import Profiles, Bundles, ConsumptionCache
from app_models.reseller_models import Order_history

from b2c_helpers.api_helper import get_all_topups_balance
from b2c_helpers.constaints import BAYOBAB_VENDOR, TELKOMSEL_VENDOR, LOTUS_FLARE_VENDOR
from b2c_helpers.db_helper import accumulate_vendor_data, update_plan_started
from b2c_helpers.errors import AlreadyReceived
from b2c_helpers.vendors import (
    Flexiroam,
    ESIMGo,
    Vodafone,
    MontyMobile,
    Indosat,
    Orange,
    MontyReseller,
    FlexiroamAPI,
    Bayobab,
    TelkomselVendor,
)
from b2c_helpers.vendors_integrations.lotus_flare_vendor import LotusFlare
from b2c_helpers.webhook_helpers import WebhookHelper
from b2c_helpers.support_helper import cache_consumption_data

logger = logging.getLogger(__name__)


# 'get_vendor_user_consumption' Only Exists in 'get_accumulated_user_consumption' and is found in 'esim-b2c-api-py'
# 'update_plan_started' Only Exists in 'get_accumulated_user_consumption' and is found in 'esim-b2c-api-py'
# 'get_all_topups_balance' Exists in this one and another one 'get_iccid_details_of_user' and is found in
# 'esim-b2c-api-py' added 'instance_config' support for it
def get_vendor_user_consumption(instance_config, profile: Profiles):
    data = {
        "data_allocated": "-1",
        "data_used": "-1",
        "data_unit": "MB",
        "coverage": "",
        "display_name": "",
        "unlimited": False,
    }
    iccid = str(profile.iccid)
    order_history: Order_history = Order_history.objects(iccid=iccid, reseller_type="subscriber", order_status="Successful").first()
    coverage = order_history.bundle_data["coverage"]
    display_name = order_history.bundle_data["display_name"]
    unlimited = order_history.bundle_data["unlimited"]
    data_allocated = 0
    data_used = 0
    try:
        vendor_name = profile.vendor_name
        sku = str(profile.sku)
        vendor_msisdn = str(profile.msisdn)
        data["coverage"] = coverage
        data["display_name"] = display_name
        data["unlimited"] = unlimited

        remaining_quantity: float = 0
        if vendor_name == "Flexiroam":
            flexiroam_consumption_response = Flexiroam().get_profile_consumption(sku)
            bundles = flexiroam_consumption_response["data"]
            for bundle in bundles:
                data_allocated += float(bundle.get("data_allocated"))
                remaining_quantity += float(bundle.get("data_balance"))
            total_data_amount, total_amount_paid, total_bundle_duration, queued_bundles = accumulate_vendor_data(iccid)
            data_allocated = total_data_amount * 1000
            data_used = data_allocated - remaining_quantity

        elif vendor_name == "eSIMGo":
            esimgo_consumption_response = ESIMGo().get_profile_consumption(iccid)
            bundles = esimgo_consumption_response["bundles"]
            for bundle in bundles:
                for data_ in bundle["assignments"]:
                    remaining_quantity += data_["remainingQuantity"]

            total_data_amount, total_amount_paid, total_bundle_duration, queued_bundles = accumulate_vendor_data(iccid)
            data_allocated = total_data_amount * 1000

            remaining_quantity /= 1000 * 1000
            data_used = data_allocated - remaining_quantity

        elif vendor_name == "Vodafone":
            vodafone_consumption_response = Vodafone().get_profile_consumption(iccid)
            data_allocated = get_all_topups_balance(instance_config, iccid)
            bundles = vodafone_consumption_response.get("bundle")
            for bundle in bundles:
                remaining_quantity += float(bundle.get("bundleBalance"))

            if data_allocated == 0:
                data_allocated = remaining_quantity

            data_used = data_allocated - remaining_quantity

        elif vendor_name == "Monty Mobile":
            json_response = MontyMobile().get_profile_consumption(sku)
            bundles = json_response.get("data", {}).get("plans", [])

            for bundle in bundles:
                if bundle.get("data_allocated") != bundle.get("data_balance"):
                    update_plan_started(iccid)
                data_allocated += float(bundle.get("data_allocated")) / 1024
                remaining_quantity += float(bundle.get("data_balance")) / 1024
            data_used = data_allocated - remaining_quantity

        elif vendor_name == BAYOBAB_VENDOR:
            bayobab = Bayobab()
            order_history: Order_history = Order_history.objects(iccid=iccid, order_status="Successful")

            # Fetch current data consumption details from Bayobab for the ICCID
            # For Bayobab, plans which are not started the consumption, they will return balance as 0.
            response = bayobab.fetch_consumption(iccid)

            if not response:
                raise ValueError(f"Error occurred while fetching consumption for the profile: {iccid}")

            if response.get("errorCode"):
                raise ValueError(f"Error while fetching consumption for profile {iccid}, error: {response.get('errorMessage')}")

            for order in order_history:
                bundle = Bundles.objects(vendor_name=BAYOBAB_VENDOR, bundle_code=order.bundle_code).first()
                if not bundle:
                    continue

                allocated_data_mb = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
                data_allocated += allocated_data_mb

                # Fully consumed if plan is expired
                if order.plan_status == "Expired":
                    data_used += allocated_data_mb
                    continue

                plan_balance_mb = 0

                # Search for corresponding plan UID in the consumption response
                for plan in response.get("content", []):
                    if order.plan_uid == plan.get("id"):
                        for balance in plan.get("balance", []):
                            if balance.get("service") == "DATA":
                                plan_balance_mb = balance.get("value", 0)
                                break  # Found balance, no need to continue

                if plan_balance_mb:
                    data_used += allocated_data_mb - plan_balance_mb
                elif order.plan_started:
                    # Assume full consumption if started but no balance
                    data_used += allocated_data_mb

        elif vendor_name == "Indosat":
            indosat = Indosat()

            consumptions = indosat.get_terminal_rating(iccid)

            total_data_amount, total_amount_paid, total_bundle_duration, queued_bundles = accumulate_vendor_data(iccid)

            if consumptions:

                consumptions_length = len(consumptions)

                queued_bundles_length = len(queued_bundles)

                missing_data = []

                if consumptions_length < queued_bundles_length:
                    expired_plans = queued_bundles[0 : queued_bundles_length - consumptions_length]

                    missing_data = [{"ratePlanName": plan["bundle_vendor_code"], "dataRemaining": 0} for plan in expired_plans]

                    consumptions = missing_data + consumptions

                total_data_remaining = 0

                for plan in consumptions:

                    data_remaining = plan.get("dataRemaining", None)

                    if data_remaining is not None:

                        data_remaining = float(data_remaining)

                    else:
                        # check ctd usage and overage limit reached and status
                        current_consumption = indosat.get_device_consumption(iccid=iccid)
                        current_status = current_consumption.get("status")
                        current_overage_limit_reached = current_consumption.get("overageLimitReached")
                        if (current_status == "DEACTIVATED") or current_overage_limit_reached:
                            data_remaining = 0
                            data_used = total_data_amount * 1024
                            continue

                        bundle = Bundles.objects(bundle_vendor_code=plan["ratePlanName"]).first()

                        if bundle:
                            data_remaining = bundle.data_amount * 1024

                    total_data_remaining += float(data_remaining)

                # rate_plan = consumption.get('ratePlan', "")
                # bundle = consumer_models.Bundles.objects(bundle_vendor_code=rate_plan).first()
                # data_allocated = bundle.data_amount
                # data_unit = bundle.data_unit
                # if data_unit == "GB":
                #     data_allocated = round(float(data_allocated * 1024),2)
                #
                # to be checked if needed
                # if total_data_amount:
                #     data_allocated = total_data_amount * 1000
                # TODO REMOVE EXPIRED BUNDLES AS CONSUMPTION GETS LATEST ONLY
                data_allocated = total_data_amount * 1024
                if total_data_remaining > 0:
                    data_used = data_allocated - total_data_remaining

            else:
                raise ValueError(f"No consumptions in Indosat for iccid {iccid}")

        elif vendor_name == "Orange":
            vendor_orange = Orange()
            data_object = vendor_orange.get_profile_consumption(vendor_msisdn)
            if not data_object:
                raise ValueError(f"No consumptions in Orange for iccid {iccid}")
            bundles = data_object["buckets"]
            for bundle in bundles:
                for data in bundle["bucketBalances"]:
                    data_allocated += float(data["initialValue"])
                    remaining_quantity += float(data["remainingValue"])
            if not bundles:
                raise ValueError(f"No plans found for iccid {iccid}")
            data_used = data_allocated - remaining_quantity
        elif vendor_name == "Monty Reseller":
            monty_reseller = MontyReseller()
            consumption = monty_reseller.get_consumption(order_history.plan_uid)
            data_allocated = consumption.get("data_allocated", 0)
            data_used = consumption.get("data_used", 0)
        elif vendor_name == "FlexiroamV2":
            flexiroam_vendor = FlexiroamAPI()
            plan_status = True
            not_started_count = 0
            total_data_amount, _, _, queued_bundles = accumulate_vendor_data(iccid)
            res = flexiroam_vendor.get_sim_details(sku)

            if not res:
                raise ValueError(f"Error while getting consumption for iccid {iccid}")

            if not res.get("active_plans"):
                raise ValueError(f"No active plans are present for the iccid: {iccid}")
            bundles = res["active_plans"]
            for bundle in bundles:
                if not bundle.get("activated_on"):
                    plan_status = False
                else:
                    not_started_count += 1

                data_allocated += float(bundle.get("initial_balance"))
                remaining_quantity += float(bundle.get("balance"))

            data_allocated = total_data_amount * 1000
            if plan_status and queued_bundles == not_started_count:
                remaining_quantity = data_allocated
            data_used = data_allocated - remaining_quantity

        elif vendor_name == TELKOMSEL_VENDOR:
            telkomsel_vendor = TelkomselVendor()
            order_history: mongoengine.QuerySet = Order_history.objects(iccid=iccid, order_status="Successful")

            # Fetch current data consumption details from Telkomsel for the ICCID
            response, msg = telkomsel_vendor.get_profile_history(profile.msisdn)
        elif vendor_name == LOTUS_FLARE_VENDOR:
            lotus_flare_vendor = LotusFlare()
            order_history: mongoengine.QuerySet = Order_history.objects(iccid=iccid, order_status="Successful")

            response, msg = lotus_flare_vendor.get_order()
            if not response:
                raise ValueError(f"Error occurred while fetching consumption for the profile: {iccid}")

            allocated_data_mb = 0
            for order in order_history:
                bundle = Bundles.objects(vendor_name=TELKOMSEL_VENDOR, bundle_code=order.bundle_code).first()
                if not bundle:
                    continue

                allocated_data_mb = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
                data_allocated += allocated_data_mb

                # Fully consumed if plan is expired
                if order.plan_status == "Expired":
                    data_used += allocated_data_mb
                    continue

            # Get Usage From Response
            plan_balance_mb = response["data"]["attributes"]["ctdDataUsage"]

            if plan_balance_mb:
                data_used += allocated_data_mb - plan_balance_mb
            else:
                # Assume full consumption if started but no balance
                data_used += allocated_data_mb

        if data_allocated < data_used or data_allocated < 0:
            raise ValueError(f"Error while calculation consumption for iccid: {iccid}")
        else:
            data = {
                "data_unit": "MB",
                "data_allocated": str(round(data_allocated, 2)),
                "data_used": str(round(data_used, 2)),
                "usage_percent": round((data_used / data_allocated) * 100),
                "display_name": display_name,
                "coverage": coverage,
                "unlimited": unlimited,
            }
            # Cache the profile consumption record for future use if there is no response from the vendor on subsequent
            # requests.
            cache_consumption_data(profile, data)
            return data
    except Exception as e:
        logger.error("Exception while loading fetching consumption from vendor side [%s] for iccid %s", e, iccid)
        logger.info("Checking to get consumption from cache data for iccid %s", iccid)
        if profile.consumption_cache:
            cache = profile.consumption_cache
            logger.info(f"Using cached data for iccid {iccid}")
            data = {
                "data_unit": "MB",
                "data_allocated": cache.data_allocated,
                "data_used": cache.data_used,
                "usage_percent": round((float(cache.data_used) / float(cache.data_allocated)) * 100),
                "display_name": display_name,
                "coverage": coverage,
                "unlimited": unlimited,
                "cached_at": cache.cached_at,
            }
        return data


class AccumulatedBundleConsumptionByIccid:
    def __init__(self, config):
        self.instance_config = config

    def get_accumulated_user_consumption(
        self, email: str = None, iccid: int = None, order_number: str = None, plan_started: bool = None
    ) -> object:

        main_order_history__search_query = mongoengine.Q(order_status="Successful", reseller_type="subscriber")

        logging.info(f"[FIREBASE-SEND-NOTIFICATION] main_order_history__search_query:: {main_order_history__search_query}")

        if order_number is not None:
            main_order_history__search_query = main_order_history__search_query & mongoengine.Q(order_number=order_number)
        else:
            main_order_history__search_query = (
                main_order_history__search_query
                & (mongoengine.Q(client_email=email) | mongoengine.Q(shared_with=email))
                & mongoengine.Q(iccid=iccid)
            )

        if plan_started is not None:
            main_order_history__search_query = main_order_history__search_query & mongoengine.Q(plant_started=plan_started)

        order_history: Order_history = Order_history.objects(main_order_history__search_query).first()

        if not order_history:
            if iccid is not None:
                logging.error("Did not find order history for iccid %s", iccid)
            elif order_number is not None:
                logging.error("Did not find order history for order number %s", order_number)

        profile = None
        if iccid is not None:
            profile = Profiles.objects(iccid=iccid).first()
        elif order_number is not None:
            profile = Profiles.objects(iccid=order_history.iccid).first()

        if not profile:
            logging.exception("Couldn't find profile for iccid %s although order exists", iccid)
            return None

        bundle: Bundles = Bundles.objects(bundle_code=order_history.bundle_code).first()
        if not bundle:
            logging.exception("Couldn't find bundle %s although order exists", order_history.bundle_code)
            return None
        data: dict = get_vendor_user_consumption(self.instance_config, profile)
        if not data or type(data) != dict:
            return None

        if float(data.get("data_used", 0)) <= 0:
            return data

        usage_percent = data.get("usage_percent", 0)

        if usage_percent in range(0, 70):
            #   if the user is still below 70% usage, send the start notification
            usage_percent = 1
        elif usage_percent in range(70, 100):
            usage_percent = 80
        else:
            usage_percent = 100

        class MiniNotificationHelperClass(WebhookHelper):
            NOTIFICATIONS_TO_SAVE = [1, 80, 100]

            def check_if_user_should_be_notified(self, per_plan_notifications: bool = True):
                return super().check_if_user_should_be_notified(per_plan_notifications=False)

            def limit_notification(self, payload=None) -> None:

                logging.info(f"[FIREBASE-SEND-NOTIFICATION] [limit_notification] Limit notification started with payload = {payload}")

                self.order_history: Order_history = Order_history.objects(
                    iccid=iccid, order_status="Successful", plan_status__ne="Expired", reseller_type="subscriber"
                ).first()
                if not self.order_history:
                    logging.info("Couldn't find non-expired successful order for iccid %s", iccid)
                    return

                if usage_percent == 1:
                    logging.info("%s started consumption", iccid)
                    self.usage_percent = 1

                elif usage_percent == 80:
                    logging.info("%s reached 80%% of total data", iccid)
                    self.usage_percent = 80

                elif usage_percent == 100:
                    logging.info("%s reached 100%% of total data", iccid)
                    self.usage_percent = 100

                else:
                    logging.info("%s reached %d%% of total data", iccid, usage_percent)
                    return

                try:
                    logging.info(
                        f"[FIREBASE-SEND-NOTIFICATION] [limit_notification] self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:::: {self.order_history.reseller_type} == {self.SUBSCRIBER_reseller_type} => {self.order_history.reseller_type == self.SUBSCRIBER_reseller_type}"
                    )
                    if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                        return self.check_subscriber_order_to_notify_user()
                    else:
                        return None

                except AlreadyReceived as message:
                    logging.info("%s", message)

        notification_response = MiniNotificationHelperClass(instance_config=self.instance_config).limit_notification()

        logging.info(f"[FIREBASE-SEND-NOTIFICATION] [limit_notification] Notification response:: {notification_response}")

        return data

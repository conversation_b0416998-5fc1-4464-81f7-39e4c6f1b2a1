import logging
from pymongo.errors import PyMongoError

def handle_exception(exception):
    if isinstance(exception, PyMongoError):
        return "A database error occurred. Please try again later."
    elif isinstance(exception, KeyError):
        return f"Missing key: {str(exception)}"
    elif isinstance(exception, ValueError):
        return str(exception)
    return "An unexpected error occurred. Please try again later."
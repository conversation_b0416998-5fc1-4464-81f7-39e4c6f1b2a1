import logging
import base64
import requests
from b2c_helpers.errors import UnauthorizedError
from b2c_helpers.vendors import VendorMetaClass, raise_if_401_403

logger = logging.getLogger(__name__)


# --- Singtel Class ---
class Singtel(VendorMetaClass):
    """
    Class for making API calls to Singtel MVNE services, adapted from the
    provided Postman collection.
    """

    retry: bool = True
    vendor_name: str = "Singtel"
    headers: dict = {}  # Will be populated with Authorization header after successful authentication
    access_token: str = None
    refresh_token: str = None
    timeout: int = 30  # Default request timeout in seconds

    def __init__(self, domain_name: str, username: str, password: str):
        """
        Initializes the Singtel API client.

        :param domain_name: The base domain name for the Singtel MVNE API (e.g., "api.singtel.com").
        :type domain_name: str
        :param username: The username for API authentication.
        :type username: str
        :param password: The password for API authentication.
        :type password: str
        """
        if not (domain_name and username and password):
            raise ValueError("domain_name, username, and password are required for Singtel initialization.")

        self.domain_name = domain_name.strip("/")  # Remove any trailing slashes
        self.username = username
        self.password = password

        # Define API Endpoints based on the Postman Collection structure
        self.auth_token_url = f"http://{self.domain_name}/authentication/token/"
        self.refresh_token_url = f"http://{self.domain_name}/refreshToken/"

        self.base_api_url = f"http://{self.domain_name}/apiservice"
        # Endpoints are designed for specific service IDs based on the Postman collection
        self.query_sim_details_url = f"{self.base_api_url}/query-sim-details/{{serviceId}}"
        self.query_plan_url = f"{self.base_api_url}/query-plan/{{serviceId}}"
        self.customer_usage_url = f"{self.base_api_url}/customer-usage"
        self.modify_service_url = f"{self.base_api_url}/modify-service"
        # This is the endpoint *your system* would expose to receive notifications from Singtel
        self.outbound_notification_endpoint_example = f"{self.base_api_url}/outBoundNotification"

        # Attempt initial authentication upon class instantiation
        try:
            self._authenticate()
        except UnauthorizedError:
            logger.error("Initial authentication failed during Singtel client setup. Please check your credentials.")
            # Depending on your application's needs, you might raise here or handle gracefully

    def _authenticate(self):
        """
        Authenticates with the Singtel MVNE API using Basic Auth to get an access token.
        The access token is then stored and used for later API calls.
        """
        auth_string = f"{self.username}:{self.password}"
        encoded_auth_string = base64.b64encode(auth_string.encode()).decode()
        auth_headers = {"Authorization": f"Basic {encoded_auth_string}", "Content-Type": "application/json"}
        # Postman collection's /authentication/token example has an empty JSON body
        payload = {}

        logger.info("Attempting to authenticate with Singtel MVNE API...")
        try:
            response = requests.post(self.auth_token_url, headers=auth_headers, json=payload, timeout=self.timeout)
            response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
            token_data = response.json()
            self.access_token = token_data.get("access_token")
            self.refresh_token = token_data.get("refresh_token")

            if not self.access_token:
                raise ValueError("Access token was not received in the authentication response.")

            # Set the X-Authorization header for all subsequent API calls
            self.headers = {"X-Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"}
            logger.info("Successfully authenticated and set access token.")
        except requests.exceptions.RequestException as e:
            logger.error("Authentication request failed: %s", e)
            raise UnauthorizedError(f"Authentication failed: {e}")
        except Exception as e:
            logger.error("An unexpected error occurred during authentication: %s", e)
            raise UnauthorizedError(f"Unexpected authentication error: {e}")

    def _refresh_access_token(self):
        """
        Refreshes the current access token using the refresh token.
        If no refresh token is available, it attempts a full re-authentication.
        """
        if not self.refresh_token:
            logger.warning("No refresh token available. Attempting full re-authentication.")
            return self._authenticate()  # Fallback to full authentication

        payload = {"refresh_token": self.refresh_token}
        # Postman collection's /refreshToken example does not use 'X-Authorization' header
        refresh_headers = {"Content-Type": "application/json"}

        logger.info("Attempting to refresh access token...")
        try:
            response = requests.post(self.refresh_token_url, headers=refresh_headers, json=payload, timeout=self.timeout)
            response.raise_for_status()
            token_data = response.json()
            self.access_token = token_data.get("access_token")
            # Refresh token might also be new after refresh, update it
            self.refresh_token = token_data.get("refresh_token", self.refresh_token)

            if not self.access_token:
                raise ValueError("New access token not received during token refresh.")

            self.headers["X-Authorization"] = f"Bearer {self.access_token}"  # Update main headers
            logger.info("Successfully refreshed access token.")
            return True
        except requests.exceptions.RequestException as e:
            logger.error("Token refresh request failed: %s. Attempting full re-authentication.", e)
            return self._authenticate()  # Fallback to full authentication
        except Exception as e:
            logger.error(
                "An unexpected error occurred during token refresh: %s. Attempting full re-authentication.",
                e
            )
            return self._authenticate()

    def _make_api_request(self, method: str, url: str, retries: int = 1, **kwargs):
        """
        A helper method to execute API requests, automatically handling token refresh and retries
        for Unauthorized errors.

        :param method: The HTTP method (e.g., "GET", "POST").
        :type method: str
        :param url: The full URL of the API endpoint.
        :type url: str
        :param retries: Number of times to retry the request on UnauthorizedError. Defaults to 1.
        :type retries: int
        :param kwargs: Additional keyword arguments to pass to `requests.request` (e.g., `json`, `params`, `data`).

        :return: The response object from the API call.
        :rtype: requests.Response

        :raises UnauthorizedError: If authentication fails after all retries.
        :raises requests.exceptions.RequestException: For other HTTP-related errors.
        """
        for i in range(retries + 1):  # +1 for the initial attempt
            try:
                # Ensure we have an access token before making a request
                if not self.access_token:
                    logger.warning("Access token is missing. Attempting authentication before request.")
                    self._authenticate()

                response = requests.request(method, url, headers=self.headers, timeout=self.timeout, **kwargs)
                raise_if_401_403(response)  # This will raise UnauthorizedError for 401/403
                return response
            except UnauthorizedError:
                if i < retries:
                    logger.warning(
                        "Request unauthorized (attempt %s/%s). Attempting to refresh token and retry.",
                        i + 1, retries + 1
                    )
                    self._refresh_access_token()
                    return None
                else:
                    logger.error("Request to %s failed: Unauthorized after all retries. Giving up.", url)
                    raise  # Re-raise if retries are exhausted
            except requests.exceptions.RequestException as e:
                logger.error("API request to %s failed: %s", url, e)
                raise  # Re-raise other request exceptions immediately
        return None

    def get_esim_profile_details(self, service_id: str):
        """
        Retrieves detailed information for a specific eSIM profile.
        Maps to 'Query Sim Details' (GET /apiservice/query-sim-details/{serviceId})
        in the Postman collection.

        :param service_id: The unique identifier of the service/eSIM profile.
        :type service_id: str

        :return: A dictionary containing the eSIM profile details, or an empty dictionary on error.
        :rtype: dict
        """
        if not service_id:
            logger.error("service_id is required to get eSIM profile details.")
            return {}
        try:
            url = self.query_sim_details_url.format(serviceId=service_id)
            response = self._make_api_request("GET", url)
            return response.json()
        except Exception as e:
            logger.error(
                "<Singtel.get_esim_profile_details> Error getting eSIM profile details for serviceId '%s': %s",
                service_id, e
            )
            return {}

    def get_data_plan_details(self, service_id: str, group_plan: bool = None, page: int = None, size: int = None, status: str = None):
        """
        Retrieves details for a specific data plan associated with a service.
        Maps to 'Query Plan' (GET /apiservice/query-plan/{serviceId})
        in the Postman collection.

        Note: This endpoint is primarily for querying a plan associated with a *specific serviceId*,
        not a general catalog of *all* available plans in the MVNE platform.

        :param service_id: The unique identifier of the service whose plan details are sought.
        :type service_id: str
        :param group_plan: Filter by group plan status.
        :type group_plan: bool, optional
        :param page: The page number for pagination.
        :type page: int, optional
        :param size: The number of items per page.
        :type size: int, optional
        :param status: Filter by plan status.
        :type status: str, optional

        :return: A dictionary containing the data plan details, or an empty dictionary on error.
        :rtype: dict
        """
        if not service_id:
            logger.error("service_id is required to get data plan details.")
            return {}

        try:
            url = self.query_plan_url.format(serviceId=service_id)
            params = {}
            if group_plan is not None:
                params["groupPlan"] = group_plan
            if page is not None:
                params["page"] = page
            if size is not None:
                params["size"] = size
            if status is not None:
                params["status"] = status

            response = self._make_api_request("GET", url, params=params)
            return response.json()
        except Exception as e:
            logger.error(
                "<Singtel.get_data_plan_details> Error getting data plan details for serviceId '%s': %s",
                service_id, e
            )
            return {}

    def get_data_plan_consumption(self, msisdn: str = None, account_id: str = None, from_date: str = None, to_date: str = None):
        """
        Retrieves data plan consumption details for a specific customer or eSIM profile.
        Maps to 'Customer Usage' (POST /apiservice/customer-usage)
        in the Postman collection.

        :param msisdn: The MSISDN (mobile number) of the customer/profile.
        :type msisdn: str, optional
        :param account_id: The account ID of the customer/profile.
        :type account_id: str, optional
        :param from_date: Start date for usage query (e.g., "YYYY-MM-DD").
        :type from_date: str, optional
        :param to_date: End date for usage query (e.g., "YYYY-MM-DD").
        :type to_date: str, optional

        :return: A dictionary containing consumption data, or an empty dictionary on error.
        :rtype: dict
        """
        payload = {}
        if msisdn:
            payload["msisdn"] = msisdn
        if account_id:
            payload["accountId"] = account_id
        if from_date:
            payload["fromDate"] = from_date
        if to_date:
            payload["toDate"] = to_date

        if not (msisdn or account_id):
            logger.error("Either 'msisdn' or 'accountId' is required to get data plan consumption.")
            return {}

        try:
            response = self._make_api_request("POST", self.customer_usage_url, json=payload)
            return response.json()
        except Exception as e:
            logger.error("<Singtel.get_data_plan_consumption> Error getting data plan consumption: %s", e)
            return {}

    def top_up_plan(self, service_id: str, plan_id: str, plan_name: str, **kwargs):
        """
        Applies a top-up or modifies a plan on a specific eSIM profile.
        Maps to 'Modify Service' (POST /apiservice/modify-service)
        in the Postman collection.

        :param service_id: The unique identifier of the service/eSIM profile to modify.
        :type service_id: str
        :param plan_id: The ID of the new plan to apply or top-up with.
        :type plan_id: str
        :param plan_name: The name of the new plan.
        :type plan_name: str
        :param kwargs: Additional key-value pairs to include in the subscription modification payload.

        :return: The response from the modification API, or an empty dictionary on error.
        :rtype: dict
        """
        payload = {
            "serviceId": service_id,
            "subscriptions": [
                {
                    "planId": plan_id,
                    "planName": plan_name,
                    # You can add other subscription properties here based on your needs
                    # from the Postman collection's 'Modify Service' example.
                }
            ],
            # Add other overall modification details if necessary (e.g., 'type', 'status')
        }
        payload.update(kwargs)  # Allow passing additional kwargs for flexibility

        try:
            response = self._make_api_request("POST", self.modify_service_url, json=payload)
            return response.json()
        except Exception as e:
            logger.error(
                "<Singtel.top_up_plan> Error topping up plan for serviceId '%s': %s",
                service_id, e
            )
            return {}

    def handle_outbound_notification(self, notification_data: dict):
        """
        This method is an example of how *your system* would process an inbound webhook
        (an "Outbound Notification" from Singtel).
        You would typically expose an HTTP POST endpoint in your application that receives
        data from Singtel at the URL specified by `http://{domainName}/apiservice/outBoundNotification`.
        This method *does not* make an API call to Singtel; it simulates receiving one.

        :param notification_data: The payload received from Singtel's outbound notification.
        :type notification_data: dict

        :return: A response acknowledging receipt, or indicating processing status.
        :rtype: dict
        """
        logger.info("Received outbound notification from Singtel: %s", notification_data)
        # Implement your specific logic here to process the notification data.
        # For example, you might:
        # - Update your internal database with consumption details
        # - Trigger alerts based on usage thresholds
        # - Log the event for auditing purposes
        # In a real web application, this would typically return an HTTP 200 OK.
        return {"status": "success", "message": "Notification received and processed successfully"}
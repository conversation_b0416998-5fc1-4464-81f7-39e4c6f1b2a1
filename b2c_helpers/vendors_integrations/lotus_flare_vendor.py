import logging

from instance import consumer_config as instance_config
from jose import jwt

from b2c_helpers.support_helper import get_vendor_info
from b2c_helpers.vendors import VendorMetaClass

logger = logging.getLogger(__name__)
import datetime
import time
import requests
import jwt
from types import ModuleType


class LotusFlareAccessCred:
    """
    Access credentials for LotusFlare API.
    """

    def __init__(self, access_token="", expired_at=None):
        """
        Initialize a new instance of LotusFlareAccessCred.

        :param access_token: The access token for authenticating with LotusFlare services, defaults to "".
        :type access_token: str, optional
        """
        self.access_token = access_token
        self.expired_at = expired_at


class LotusFlare(metaclass=VendorMetaClass):
    """
    LotusFlare client for interacting with the LotusFlare API for eSIM management.

    :param instance_config: Configuration module containing API credentials and base URL.
    :type instance_config: module
    :raises ValueError: If required vendor info or attributes are missing.
    """

    vendor_name: str = "LotusFlare"
    access_cred: LotusFlareAccessCred = None

    def __init__(self):

        vendor = get_vendor_info(self.vendor_name)
        required_attrs = [
            "lotus_base_url",
            "lotus_client_id",
            "lotus_client_key",
        ]
        self.code_messages = {
            0: "Success",
            1001: "Uncaught Error",
            2002: "Invalid request: invalid fields",
            2003: "Invalid request: missing fields",
            2007: "Invalid token",
            232007: "Invalid token",
            3002: "Product not found",
            3003: "Invalid parameters",
            4002: "Out of stock",
            4003: "Failed to initialize eSIM",
            4006: "Failed to bind subscription",
            4007: "Failed to activate subscription",
            4008: "Failed to terminate subscription",
            4011: "Order not found",
            4021: "ICCID not found",
            5003: "Account not found",
            5008: "Invalid account",
        }
        if not (instance_config and isinstance(instance_config, ModuleType) and vendor):
            raise ValueError("Required LotusFlare module instance_config and vendor object are missing.")

        if not all(hasattr(instance_config, attr) for attr in required_attrs):
            raise ValueError("LotusFlare instance_config module must contain required attributes: " f"{', '.join(required_attrs)}")

        self.vendor = vendor
        self.instance_config = instance_config

        self.lotus_base_url = self.instance_config.lotus_base_url
        self.lotus_client_id = self.instance_config.lotus_client_id
        self.lotus_client_key = self.instance_config.lotus_client_key

        self.auth_url = self.lotus_base_url + "/account/api/v1/auth"
        self.get_products_url = self.lotus_base_url + "/product/api/v1/get_products"
        self.get_product_url = self.lotus_base_url + "/product/api/v1/get_product?id={product_id}"
        self.allocate_esim_url = self.lotus_base_url + "/order/api/v1/allocate_esim"
        self.allocate_esims_url = self.lotus_base_url + "/order/api/v1/allocate_esims"
        self.get_order_url = self.lotus_base_url + "/order/api/v1/get_order?id={order_id}"
        self.get_esim_orders_url = self.lotus_base_url + "/order/api/v1/get_esim_orders"
        self.create_order_url = self.lotus_base_url + "/order/api/v1/create_order"
        self.get_esim_status = self.lotus_base_url + "/order/api/v1/get_esim_status?iccid={iccid}"
        self.activate_subscription_url = self.lotus_base_url + "/order/api/v1/activate_subscription"
        self.terminate_subscription_url = self.lotus_base_url + "/order/api/v1/terminate_subscription"

    @staticmethod
    def is_token_expired(expired_at: int) -> bool:
        """
        Check whether the current token has expired based on stored expiry time.
        """
        current_time = int(time.time())
        return current_time >= expired_at

    def login(self):
        """
        Authenticate with LotusFlare and obtain a new access token if necessary.

        :return: None
        """
        try:
            token = self.access_cred.access_token if self.access_cred else ""
            token_expired = not token or LotusFlare.is_token_expired(self.access_cred.expired_at)

            if token_expired:
                logger.info("Token is missing or expired. Fetching a new one...")
                payload = {
                    "client_id": self.lotus_client_id,
                    "client_key": self.lotus_client_key,
                }
                response = requests.post(self.auth_url, json=payload, timeout=self.timeout)

                if response.status_code == 200:
                    resp_dict = response.json()
                    http_code = resp_dict.get("code")
                    error_code = resp_dict.get("errorCode")
                    message = resp_dict.get("message", "No message provided")

                    if http_code == 0 and error_code is None and "data" in resp_dict:
                        token = resp_dict["data"]["access_token"]
                        decoded = jwt.decode(token, key="", options={"verify_signature": False})
                        exp_timestamp = decoded.get("exp", 0)

                        self.access_cred = LotusFlareAccessCred(
                            access_token=token, expired_at=datetime.datetime.utcfromtimestamp(exp_timestamp - 10)
                        )
                        logger.info("New token stored. Expires at: %s", self.access_cred.expired_at)
                    else:
                        readable = self.code_messages.get(error_code, "Unknown error code")
                        logger.error("LotusFlare error (%s): %s - %s", error_code, readable, message)
                else:
                    logger.error(
                        "LotusFlare token request failed: %s (status code %d)",
                        response.text,
                        response.status_code,
                    )
            else:
                logger.info("Token is still valid.")
        except Exception as e:
            logger.exception("Exception occurred during LotusFlare token request: %s", e)

    def get_products(self):
        """
        Retrieve the list of products from the LotusFlare API.

        :return: A list of product dictionaries if successful, otherwise None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            response = requests.post(self.get_products_url, headers=headers, json={}, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                http_code = resp_dict.get("code")
                error_code = resp_dict.get("errorCode")
                message = resp_dict.get("message", "No message provided")

                if http_code == 0 and error_code is None:
                    return resp_dict["data"]["products"]
                else:
                    readable = self.code_messages.get(error_code, "Unknown error code")
                    logger.error("LotusFlare error (%s): %s - %s", error_code, readable, message)

            else:
                logger.error(
                    "Failed to fetch products: %s (status code %d)",
                    response.text,
                    response.status_code,
                )

        except Exception as e:
            logger.exception("Exception while fetching products: %s", e)

    def get_product(self, product_id: str):
        """
        Retrieve a specific product by its ID from the LotusFlare API.

        :param product_id: The ID of the product to retrieve.
        :type product_id: str
        :return: Product information dictionary if successful, otherwise None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            url = self.get_product_url.format(product_id=product_id)

            response = requests.get(url, headers=headers, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                http_code = resp_dict.get("code")
                error_code = resp_dict.get("errorCode")
                message = resp_dict.get("message", "No message provided")

                if http_code == 0 and error_code is None and "data" in resp_dict:
                    product_data = resp_dict["data"]
                    return product_data
                else:
                    readable = self.code_messages.get(error_code, "Unknown error code")
                    logger.error("LotusFlare error (%s): %s - %s", error_code, readable, message)

            else:
                logger.error(
                    "Failed to fetch product: %s (status code %d)",
                    response.text,
                    response.status_code,
                )

        except Exception as e:
            logger.exception("Exception while fetching product: %s", e)

    def allocate_esim(self):
        """
        Allocate an eSIM from the LotusFlare API.

        :return: Allocated eSIM data dictionary if successful, otherwise None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            response = requests.post(
                self.allocate_esim_url,
                headers=headers,
                json={},  # Assuming no payload is needed; modify if required
                timeout=getattr(self, "timeout", self.timeout),
            )

            if response.status_code == 200:
                resp_dict = response.json()
                http_code = resp_dict.get("code")
                error_code = resp_dict.get("errorCode")
                message = resp_dict.get("message", "No message provided")

                if http_code == 0 and error_code is None and "data" in resp_dict:
                    return resp_dict["data"]["esim"]
                else:
                    readable = self.code_messages.get(error_code, "Unknown error code")
                    logger.error("LotusFlare error (%s): %s - %s", error_code or http_code, readable, message)

            else:
                logger.error(
                    "Failed to allocate eSIM: %s (status code %d)",
                    response.text,
                    response.status_code,
                )

        except Exception as e:
            logger.exception("Exception while allocating eSIM: %s", e)

    def allocate_esims(self, quantity: int = 1):
        """
        Allocate multiple eSIMs from the LotusFlare API.

        :param quantity: Number of eSIMs to allocate, defaults to 1.
        :type quantity: int, optional
        :return: List of eSIM data dictionaries if successful, otherwise None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            payload = {"quantity": quantity}

            response = requests.post(self.allocate_esims_url, headers=headers, json=payload, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                http_code = resp_dict.get("code")
                error_code = resp_dict.get("errorCode")
                message = resp_dict.get("message", "No message provided")

                if http_code == 0 and error_code is None and "data" in resp_dict:
                    return resp_dict["data"]["esims"]
                else:
                    readable = self.code_messages.get(error_code, "Unknown error code")
                    logger.error("LotusFlare error (%s): %s - %s", error_code or http_code, readable, message)

            else:
                logger.error(
                    "Failed to allocate eSIMs: %s (status code %d)",
                    response.text,
                    response.status_code,
                )

        except Exception as e:
            logger.exception("Exception while allocating eSIMs: %s", e)

    def create_order(
        self,
        operation_type: str,
        product_id: str,
        activation_date: str,
        customer: dict,
        iccid: str = None,
        auto_allocate_esim: bool = False,
    ):
        """
        Create a new or top-up order via the LotusFlare API.

        :param operation_type: The operation type ("NEW" or "TOPUP").
        :type operation_type: str
        :param product_id: Unique identifier for the product (UUID).
        :type product_id: str
        :param activation_date: Activation date in ISO 8601 format.
        :type activation_date: str
        :param customer: Dictionary with customer details ('first_name', 'last_name', 'email').
        :type customer: dict
        :param iccid: ICCID of the eSIM to use. Required if `auto_allocate_esim` is False.
        :type iccid: str, optional
        :param auto_allocate_esim: Automatically allocate eSIM if True, otherwise use given ICCID.
        :type auto_allocate_esim: bool, optional
        :raises ValueError: If `auto_allocate_esim` is False and `iccid` is not provided.
        :return: Created order details if successful, otherwise None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            payload = {
                "operation_type": operation_type,
                "auto_allocate_esim": str(auto_allocate_esim).lower(),
                "iccid": str(iccid),
                "product": {
                    "id": product_id,
                    "activation_date": activation_date,
                },
                "customer": customer,
            }

            if not auto_allocate_esim:
                if not iccid:
                    raise ValueError("ICCID is required when auto_allocate_esim is False.")
                payload["iccid"] = iccid

            response = requests.post(self.create_order_url, headers=headers, json=payload, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, "Unknown error code")

                if code == 0 and "data" in resp_dict:
                    return resp_dict["data"]
                else:
                    logger.error("Order creation failed with code %d: %s", code, message)

            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while creating order: %s", e)

        return response.json()

    def activate_subscription(self, order_id: str):
        """
        Activate a subscription associated with a given order ID.

        This method sends an authenticated request to the LotusFlare API to activate a subscription
        using the provided order ID.

        :param order_id: The unique identifier of the order whose subscription should be activated.
        :type order_id: str
        :return: A dictionary containing the activation response data if successful; otherwise, None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            payload = {"order_id": order_id}

            response = requests.post(
                self.activate_subscription_url, headers=headers, json=payload, timeout=getattr(self, "timeout", self.timeout)
            )

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, f"Unknown error (code {code})")

                if code == 0 and "data" in resp_dict:
                    return resp_dict["data"]
                else:
                    logger.error("Activation failed with code %d: %s", code, message)
            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while activating subscription: %s", e)

        return resp_dict

    def terminate_subscription(self, order_id: str):
        """
        Terminate an active subscription using the provided ICCID.

        Sends a request to the LotusFlare API to terminate a subscription associated with a specific eSIM ICCID.

        :param iccid: The ICCID of the eSIM whose subscription should be terminated.
        :return: A dictionary containing termination response data if successful; otherwise, None.
        """

        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            payload = {"order_id": order_id}

            response = requests.post(
                self.terminate_subscription_url, headers=headers, json=payload, timeout=getattr(self, "timeout", self.timeout)
            )

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, f"Unknown error (code {code})")

                if code == 0 and "data" in resp_dict:
                    return resp_dict["data"]
                else:
                    logger.error("Termination failed with code %d: %s", code, message)
            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while terminating subscription: %s", e)

        return resp_dict

    def get_esim_orders(self, iccid: str):
        """
        Retrieve all eSIM orders.

        Sends an authenticated request to fetch all eSIM orders.

        :return: A list of dictionaries containing eSIM order details if successful; otherwise, None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            url = f"{self.get_esim_orders_url}?iccid={iccid}"

            response = requests.get(url, headers=headers, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, f"Unknown error (code {code})")

                if code == 0 and "data" in resp_dict and "orders" in resp_dict["data"]:
                    return resp_dict["data"]["orders"]
                else:
                    logger.error("Fetching eSIM orders failed with code %d: %s", code, message)
            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while fetching eSIM orders: %s", e)

        return resp_dict

    def get_order(self, order_id: str):
        """
        Retrieve details of an order using its order ID.

        Sends an authenticated GET request to retrieve order information.

        :param order_id: The ID of the order to retrieve.
        :type order_id: str
        :return: A dictionary containing order details if successful; otherwise, None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            url = self.get_order_url.format(order_id=order_id)

            response = requests.get(url, headers=headers, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, f"Unknown error (code {code})")

                if code == 0 and "data" in resp_dict:
                    return resp_dict["data"]
                else:
                    logger.error("Get order failed with code %d: %s", code, message)
            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while getting order: %s", e)

        return resp_dict

    def get_esim_status(self, iccid: str):
        """
        Get the status of an eSIM using its ICCID.

        This method sends a GET request to retrieve the status of a specific eSIM.

        :param iccid: The ICCID of the eSIM whose status is to be fetched.
        :type iccid: str
        :return: A dictionary containing the eSIM status if successful; otherwise, None.
        """
        try:
            self.login()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.access_cred.access_token}",
            }

            url = self.get_esim_status.format(iccid=iccid)
            response = requests.get(url, headers=headers, timeout=getattr(self, "timeout", self.timeout))

            if response.status_code == 200:
                resp_dict = response.json()
                code = resp_dict.get("code")
                message = self.code_messages.get(code, f"Unknown error (code {code})")

                if code == 0 and "data" in resp_dict:
                    return resp_dict["data"]
                else:
                    logger.error("Get eSIM status failed with code %d: %s", code, message)
            else:
                logger.error("HTTP error %d: %s", response.status_code, response.text)

        except Exception as e:
            logger.exception("Exception while getting eSIM status: %s", e)

        return resp_dict

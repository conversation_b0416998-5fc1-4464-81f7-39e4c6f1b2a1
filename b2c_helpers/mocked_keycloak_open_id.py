class MockedKeycloakOpenId:
    def __init__(self, server_url, client_id, realm_name, client_secret_key, user_info_response=None,
                 introspect_response=None):
        self.user_info_response = user_info_response
        self.server_url = server_url
        self.client_id = client_id
        self.realm_name = realm_name
        self.client_secret_key = client_secret_key
        self.introspect_response = introspect_response

    def userinfo(self, _):
        return self.user_info_response

    def introspect(self, _):
        return self.introspect_response

import requests
import logging


class Branch:
    base_url = "https://api2.branch.io/v1/url"

    def __init__(self, branch_key):
        """
        Branch helper class
        :param branch_key: Your Branch API key
        """
        self.branch_key = branch_key

        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }

    def generate_dynamic_link(
        self, canonical_url: str, stage: str, feature: str = "", campaign: str = "", **kwargs
    ):
        """
        Generate a Branch deep link
        :param canonical_url: The main URL for the link
        :param feature: Feature name for the link
        :param campaign: Campaign name for the link
        :param stage: Stage name for the link
        :param kwargs: Additional parameters to include in the link
        """
        payload = {
            "branch_key": self.branch_key,
            "data": {
                "$web_only": False,
                "$desktop_web_only": False,
                "$mobile_web_only": False,
                "$canonical_url": canonical_url,
                "$desktop_url": ""
            },
            "stage": stage
        }
        if feature:
            payload["feature"] = feature
        if campaign:
            payload["campaign"] = campaign
        # Add any additional URL parameters to the canonical_url
        if kwargs:
            separator = "&" if "?" in canonical_url else "?"
            payload["data"]["$canonical_url"] += f"{separator}" + "&".join(
                f"{k}={v}" for k, v in kwargs.items()
            )
        payload["data"]["$desktop_url"] = payload["data"]["$canonical_url"]
        try:
            import curlify

            response = requests.post(self.base_url, headers=self.headers, json=payload)
            print(curlify.to_curl(response.request))
            response.raise_for_status()
            return response.json().get("url")
        except requests.exceptions.RequestException as e:
            logging.error(f"Error generating Branch deep link: {e}")
            return None

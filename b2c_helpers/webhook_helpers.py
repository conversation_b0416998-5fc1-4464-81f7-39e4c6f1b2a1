import datetime
import logging
import threading
import uuid
import xml.etree.ElementTree as ET
from dataclasses import dataclass, field
from typing import Any, Dict

from app_models.consumer_models import Bundles, CallbackHistory, Labels, Profiles
from app_models.main_models import NotificationLogs
from app_models.mobiles import AppUserDetails
from app_models.reseller_models import Order_history
from instance import consumer_config
from mongoengine.queryset.visitor import Q

from b2c_helpers.db_helper import accumulate_vendor_data, generate_temp_otp, get_all_topups_balance, get_user, save_notification_logs
from b2c_helpers.email_helper import SubscriberEmailHelper
from b2c_helpers.errors import AlreadyReceived
from b2c_helpers.firebase import Firebase
from b2c_helpers.reseller import ResellerHelper
from b2c_helpers.support_helper import get_setting
from b2c_helpers.constaints import BAYOBAB_VENDOR
from b2c_helpers.vendors import Bayobab

logger = logging.getLogger(__name__)
"""
    i want to convert all print statements to logging statements with %s and not fstrings
"""
ESIMGO_NOTIFICATION_TO_SAVE = [1, 80, 100]
COMIUM_NOTIFICATION_TO_SAVE = ["PLAN-STARTED", "PLAN-EXPIRED", "PLAN-100", "PLAN-80"]
VODAFONE_NOTIFICATION_TO_SAVE = [
    "thing activated",
    "BundleLowUsage",
    "BundleDepletion",
    "BundleValidityExpiryWarning",
    "BundleValidityExpiry",
]
FLEXIROAM_NOTIFICATION_TO_SAVE = [
    "Plan Started and Selected",
    "Data Utilization",
    "Plan Expired",
]

INDOSAT_NOTIFICATION_TO_SAVE = [
    "SESSION_START",
    "CTD_USAGE",
    "PREPAID_PLAN_COMPLETION",
    "EXPIRATION",
    "DATA_LIMIT",
]


class WebhookHelper:
    instance_config = None
    firebase = None
    reseller_helper = None
    subscriber_helper = None
    customer = None
    order_history: Order_history
    usage_percent: int
    NOTIFICATIONS_TO_SAVE: list
    notify_user: bool
    start_trigger: str
    eighty_percent: str
    hundred_percent: str
    RESELLER_reseller_type = "reseller"
    SUBSCRIBER_reseller_type = "subscriber"

    def __init__(self, *args, **kwargs):
        self.instance_config = kwargs.get("instance_config")
        if not self.instance_config:
            raise AttributeError("Instance config not found")
        self.firebase = None
        if not kwargs.get("only_for_reseller", False):
            self.firebase = Firebase(
                settings=get_setting(),
                credentials_file_path=self.instance_config.firebase_ios_file,
            )
        self.customer = None
        self.subscriber_helper = None
        self.reseller_helper = ResellerHelper(instance_config=self.instance_config)
        self.notify_user = False

    def initialize_subscriber_helper(self, customer):
        self.customer = customer
        self.subscriber_helper = SubscriberEmailHelper(instance_config=self.instance_config, customer=self.customer)

    def check_subscriber_order_to_notify_user(self):

        logging.info(f"[FIREBASE-SEND-NOTIFICATION] [WebhookHelper] [check_subscriber_order_to_notify_user] started")

        if not self.order_history.bundle_data.unlimited or self.usage_percent == 1:
            self.check_if_user_should_be_notified()
            logging.info(
                f"[FIREBASE-SEND-NOTIFICATION] [WebhookHelper] [check_subscriber_order_to_notify_user] check_if_user_should_be_notified()= {self.notify_user}"
            )

        user: AppUserDetails = get_user(self.order_history.client_email)
        if not user:
            raise Exception("Failed to find user while attempting to notify.")

        remaining_quantity: float = (100 - self.usage_percent / 100) * self.order_history.bundle_data.data_amount
        username: str = user.first_name or user.user_email

        label: Labels = Labels.objects(iccid=self.order_history.iccid, client_email=self.order_history.client_email).first()
        if label:
            bundle_label_name = label.label_name
            bundle_name = label.label_name
        else:
            bundle_label_name = self.order_history.bundle_data.bundle_marketing_name
            bundle_name = self.order_history.bundle_data.bundle_name

        consumption_email_data = dict(
            user_email=username,
            remaining_amount=f"{remaining_quantity * 1000} MB",
            iccid=self.order_history.iccid,
            bundle_name=bundle_name,
            data_usage_consumption=self.usage_percent,
            montyesim_msisdn=get_setting().whatsapp_misisdn,
        )

        add_feedback_date: bool = False
        expiry_date: datetime.datetime = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
            days=self.order_history.bundle_data.bundle_duration
        )
        validity_date: datetime.datetime.date = datetime.datetime.date(expiry_date)
        transaction_message: str
        transaction: str
        notf_id: str = f"notf_{uuid.uuid4()}"
        # Set customer in the WebhookHelper class
        self.customer = user
        self.initialize_subscriber_helper(self.customer)

        if self.usage_percent == 1:
            if not self.order_history.plan_started:
                self.order_history.update(expiry_date=expiry_date, plan_status="Active", plan_started=True)
                logger.info(
                    "%s order_history updated successfully for Started notifications with ICCID: %s",
                    self.order_history.bundle_data.vendor_name,
                    self.order_history.iccid,
                )

            transaction = "StartBundle"
            self.check_if_notification_already_received(transaction=transaction)
            transaction_message = f"Your {bundle_label_name} plan is now active and valid until {validity_date}."
            # Check whether user has top-ups, if yes don't notify with top-up plan starting

        elif self.usage_percent == 80:
            transaction = "limit_80"
            self.check_if_notification_already_received(transaction=transaction)
            transaction_message = (
                f"Dear {username}, you have reached a significant level of consumption, please " f"check available top-ups"
            )
            # If user doesn't have any left top-ups
            if self.notify_user:
                self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)

        elif self.usage_percent == 100:
            transaction = "limit_100"

            #   update order before checking if the user should be notified or not
            self.order_history.update(
                plan_status="Expired",
                expiry_date=datetime.datetime.now(datetime.timezone.utc),
            )

            # For bayobab we need to detach the expired offer since they have maximum loaded plan limit
            if self.order_history.bundle_data.vendor_name == BAYOBAB_VENDOR:
                self.detach_bayobab_expired_offer()

            queued_topup_order = Order_history.objects(
                order_status="Successful",
                iccid=self.order_history.iccid,
                plan_status="Pending",
            ).first()
            if queued_topup_order:
                queued_topup_order.update(plan_status="Active")
            self.check_if_notification_already_received(transaction=transaction)
            add_feedback_date = True
            transaction_message = (
                f"Dear {username}, Your {bundle_label_name} plan has expired. You have "
                f"reached your consumption limit. You can check available top ups to "
                f"activate your plan."
            )

            if self.notify_user:
                self.subscriber_helper.send_consumption_email(**consumption_email_data)

        if self.usage_percent in self.NOTIFICATIONS_TO_SAVE and self.notify_user:
            notif_log = dict(
                notification_id=notf_id,
                email=self.order_history.client_email,
                transaction_message=transaction_message,
                transaction=transaction,
                iccid=self.order_history.iccid,
                bundle_code=self.order_history.bundle_data.bundle_code,
                reseller_type=self.SUBSCRIBER_reseller_type,
            )
            if add_feedback_date:
                notif_log["customer_feedback_date"] = datetime.datetime.date(
                    datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=2)
                )
            save_notification_logs(notif_log)
            data = {
                "username": username,
                "message": transaction_message,
                "validity_date": validity_date,
                "customer_email": self.order_history.client_email,
                "bundle_marketing_name": bundle_label_name,
            }
            if self.usage_percent == 1:
                data["category"] = "6"

            logger.info("[FIREBASE-SEND-NOTIFICATION] started")
            return self.firebase.send_notification(customer=user, data=data, transaction=transaction)

    def check_reseller_order_to_notify_user(self):
        callback_required = None
        notify_user = False
        save_notif = False
        bundle_code = self.order_history.bundle_code
        bundle: Bundles = Bundles.objects(bundle_code=bundle_code).first()
        bundle_duration = bundle.bundle_duration
        email = self.order_history.client_email
        notification_type = None
        transaction_message = None
        notf_id: str = f"notf_{uuid.uuid4()}"

        if self.usage_percent == 1:
            notification_type = "StartBundle"
            expiry_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=bundle_duration)
            validity_date: datetime.datetime.date = datetime.datetime.date(expiry_date)
            if not self.order_history.plan_started:
                self.order_history.update(expiry_date=expiry_date, plan_status="Active", plan_started=True)
                logger.info(
                    "%s order_history updated successfully for Started notifications with ICCID: %s",
                    bundle.vendor_name,
                    self.order_history.iccid,
                )
            self.check_if_notification_already_received(transaction=notification_type)
            save_notif = True
            callback_required = True
            transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {validity_date}."

        elif self.usage_percent == 80:
            if bundle.unlimited:
                return
            notification_type = "limit_80"
            self.check_if_notification_already_received(notification_type)
            transaction_message = f"Dear {email}, you have reached a significant level of consumption, please " f"check available top-ups"
            # If user doesn't have any left top-ups
            order = (
                Order_history.objects(
                    reseller_type="reseller",
                    iccid=self.order_history.iccid,
                    plan_status__ne="Expired",
                    order_status="Successful",
                ).count()
                == 1
            )
            if not self.order_history.email_count_key and order:
                notify_user = True
                callback_required = True
                save_notif = True
            if notify_user:
                self.order_history.update(set__email_count_key=2)

        elif self.usage_percent == 100:
            if bundle.unlimited:
                return
            notification_type = "limit_100"
            self.check_if_notification_already_received(notification_type)
            transaction_message = (
                f"Dear {email}, Your {bundle.bundle_name} plan has expired. You have "
                f"reached your consumption limit. You can check available top ups to "
                f"activate your plan."
            )
            # If user doesn't have any left top-ups
            self.order_history.update(
                plan_status="Expired",
                expiry_date=datetime.datetime.now(datetime.timezone.utc),
            )
            # For bayobab we need to detach the expired offer since they have maximum loaded plan limit
            if bundle.vendor_name == BAYOBAB_VENDOR:
                self.detach_bayobab_expired_offer()

            save_notif = notify_user = callback_required = Order_history.objects(
                reseller_type="reseller",
                iccid=self.order_history.iccid,
                plan_status__ne="Expired",
                order_status="Successful",
            ).count() == 0 and (self.order_history.email_count_key == 0 or self.order_history.email_count_key == 2)
            if notify_user:
                self.order_history.update(set__email_count_key=1)

        if self.usage_percent in self.NOTIFICATIONS_TO_SAVE:
            if save_notif:
                save_notification(
                    notf_id,
                    email,
                    self.order_history.iccid,
                    transaction_message,
                    notification_type,
                    bundle_code,
                    self.RESELLER_reseller_type,
                )
            if notify_user:
                send_email_response = self.reseller_helper.send_data_consumption_email_api_reseller(
                    str(self.order_history.id), str(self.order_history.reseller_id.id), notification_type, str(self.order_history.iccid)
                )
                try:
                    if send_email_response.status_code == 200:
                        user_notified_message = send_email_response.json()
                    else:
                        user_notified_message = send_email_response.json().get("detail", "")

                except Exception as e:
                    user_notified_message = "Error Sending email" + str(e)

                new_doc = {
                    "notification_type": notification_type,
                    "order_reference": self.order_history.plan_uid,
                    "order_number": str(self.order_history.id),
                    "iccid": self.order_history.iccid,
                    "user_notified": user_notified_message,
                }
                CallbackHistory(**new_doc).save()
            if callback_required:
                self.reseller_helper.send_reseller_push_notification(
                    self.order_history.iccid,
                    str(self.order_history.id),
                    notification_type,
                    str(self.order_history.reseller_id.id),
                )

    def detach_bayobab_expired_offer(self) -> None:
        """
        Detach the expired offer from the profile in Bayobab and log the outcome.

        Logs success if the detachment was successful, otherwise logs an error.

        Returns:
            None
        """
        iccid = self.order_history.iccid
        plan_uid = self.order_history.plan_uid
        bayobab = Bayobab()

        if bayobab.detach_offer(iccid=iccid, offer_id=plan_uid):
            logger.info("Successfully detached expired plan %s from profile %s.", plan_uid, iccid)
        else:
            logger.error("Failed to detach expired plan %s from profile %s.", plan_uid, iccid)

    def check_if_user_should_be_notified(self, per_plan_notifications: bool = True):
        if self.usage_percent == 0:
            self.notify_user = False
        elif self.usage_percent == 1:
            self.notify_user = (
                Order_history.objects(reseller_type="subscriber", iccid=self.order_history.iccid).first().plan_status == "Active"
            )
        elif self.usage_percent in [80, 100]:
            if not per_plan_notifications:
                self.notify_user = True
            else:
                no_more_topups_left = (
                    Order_history.objects(
                        iccid=self.order_history.iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                )
                self.notify_user = no_more_topups_left
        else:
            self.notify_user = False

    def check_if_notification_already_received(self, transaction: str):
        filters = Q(email=self.order_history.client_email) & Q(iccid=self.order_history.iccid)
        if self.usage_percent == 1:
            #   when notified about a plan starting, check the whole notification to check whether
            #   the start bundle notification was received or not, contrary to other notifications
            #   where we check if the LAST notification is the same as the one consumed.
            filters &= Q(transaction=transaction)

        last_notification: NotificationLogs = NotificationLogs.objects(filters).first()
        if last_notification and last_notification.transaction == transaction:
            logger.debug(
                "Notification %s already received for iccid %s",
                transaction,
                self.order_history.iccid,
            )
            raise AlreadyReceived(f"Notification {transaction} already received for ICCID {self.order_history.iccid}")

    def limit_notification(self, data) -> None:
        raise NotImplementedError("Must implement custom logic for vendor")


class ESIMGoWebhookHelper(WebhookHelper):
    def limit_notification(self, esimgo_limit_input):
        logger.debug("Received esimgo_limit_notification: %s", esimgo_limit_input)

        iccid = esimgo_limit_input.get("iccid")
        notf_id: str = f"notf_{uuid.uuid4()}"
        alert_type = esimgo_limit_input.get("alertType")
        remaining_quantity = esimgo_limit_input.get("bundle", {}).get("remainingQuantity", 0)
        initial_quantity = esimgo_limit_input.get("bundle", {}).get("initialQuantity", 1)
        usage_percent = 100 - round((remaining_quantity / initial_quantity) * 100)
        if usage_percent in range(1, 6):
            usage_percent = 1
        if usage_percent in range(80, 100):
            usage_percent = 80
        add_feedback_date = False
        notify_user = False
        save_notif = False
        # Subscriber code
        order_history: Order_history
        if order_history := Order_history.objects(
            reseller_type="subscriber",
            iccid=iccid,
            order_status="Successful",
            plan_status__ne="Expired",
        ).first():
            iccid: str = order_history.iccid
            email: str = order_history.client_email
            user: AppUserDetails = get_user(email)
            expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=order_history.bundle_data.bundle_duration)
            validity_date = datetime.datetime.date(expiry_date)
            transaction: str
            transaction_message: str
            # Set customer in the WebhookHelper class
            self.customer = user
            self.initialize_subscriber_helper(self.customer)

            user_email = None
            if user.first_name != "":
                username = user_email = user.first_name.capitalize()

            else:
                username = user.user_email

            label = Labels.objects(iccid=iccid, client_email=email).first()
            if label:
                bundle_label_name = label["label_name"]
                bundle_name = label["label_name"]
            else:
                bundle_label_name = order_history.bundle_data.bundle_marketing_name
                bundle_name = order_history.bundle_data.bundle_name

            if alert_type != "Utilisation":
                return
            if usage_percent == 1:

                if not order_history.plan_started:
                    order_history.update(
                        **{
                            "expiry_date": expiry_date,
                            "plan_status": "Active",
                            "plan_started": True,
                        }
                    )
                    logger.info("eSIMGO order_history updated successfully for StartBundle notifications with ICCID %s", iccid)
                transaction = "StartBundle"
                if NotificationLogs.objects(
                    email=email,
                    iccid=iccid,
                    transaction=transaction,
                ).first():
                    return

                transaction_message = f"Your {bundle_label_name} plan is " f"now active and valid until {validity_date}."
                # Check whether user has top-ups, if yes don't notify with top-up plan starting

                notify_user = (
                    Order_history.objects(
                        reseller_type="subscriber",
                        iccid=iccid,
                        plan_uid__nin=[None, ""],
                    )
                    .first()
                    .plan_status
                    == "Active"
                )

            elif usage_percent == 80:
                if order_history.bundle_data.unlimited:
                    return
                transaction = "limit_80"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, you have reached a significant level of consumption, please " f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        plan_status="Pending",
                        reseller_type="subscriber",
                    ).count()
                    == 0
                ):
                    bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "iccid": iccid,
                        "bundle_name": bundle_name,
                        "data_usage_consumption": 80,
                        "remaining_amount": f"{remaining_quantity / (1000 * 1000)} MB",
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)

            elif usage_percent == 100:
                if order_history.bundle_data.unlimited and order_history.expiry_date > datetime.datetime.utcnow():
                    return
                transaction = "limit_100"

                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()

                if pending_order:
                    bundle_data = pending_order.bundle_data

                    # Prepare the updates
                    updates = {"plan_status": "Active"}

                    # If the bundle data is unlimited, calculate and update expiry date and other fields
                    if bundle_data.unlimited:
                        expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_data.bundle_duration)
                        updates.update(
                            {
                                "plan_started": True,
                                "expiry_date": expiry_date,
                            }
                        )

                    pending_order.update(**updates)

                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                add_feedback_date = True
                transaction_message = (
                    f"Dear {username}, Your {bundle_label_name} plan has expired. You have "
                    f"reached your consumption limit. You can check available top ups to "
                    f"activate your plan."
                )
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "remaining_amount": f"{0} MB",
                        "iccid": iccid,
                        "bundle_name": bundle_name,
                        "data_usage_consumption": 100,
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email(**consumption_email_data)

            if usage_percent in ESIMGO_NOTIFICATION_TO_SAVE and notify_user:
                notif_log = {
                    "notification_id": notf_id,
                    "email": email,
                    "transaction_message": transaction_message,
                    "transaction": transaction,
                    "iccid": iccid,
                    "bundle_code": order_history.bundle_data.bundle_code,
                    "reseller_type": "subscriber",
                }
                if add_feedback_date:
                    notif_log["customer_feedback_date"] = datetime.datetime.date(datetime.datetime.utcnow() + datetime.timedelta(days=2))
                save_notification_logs(notif_log)
                data = {
                    "username": username,
                    "message": transaction_message,
                    "validity_date": validity_date,
                    "customer_email": email,
                    "bundle_marketing_name": bundle_label_name,
                }
                if usage_percent == 1:
                    data["category"] = "6"

                self.firebase.send_notification(customer=user, data=data, transaction=transaction)
        # Reseller code
        if order_history := Order_history.objects(
            reseller_type="reseller",
            iccid=iccid,
            order_status="Successful",
            plan_status__ne="Expired",
        ).first():

            logger.info("eSIMGO order_history as reseller %s", order_history["order_number"])
            callback_required = False

            transaction_message = None
            notification_type = None

            email: str = order_history.client_email
            bundle_code = order_history.bundle_code
            bundle = Bundles.objects(bundle_code=bundle_code).first()

            if alert_type != "Utilisation":
                return

            if usage_percent == 1:
                notification_type = "StartBundle"
                bundle_duration = bundle.bundle_duration
                expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_duration)

                if not order_history.plan_started:
                    order_history.update(
                        **{
                            "expiry_date": expiry_date,
                            "plan_status": "Active",
                            "plan_started": True,
                        }
                    )
                    logger.info("eSIMGO order_history updated successfully for Started notifications with ICCID: %s", iccid)
                if NotificationLogs.objects(email=email, iccid=iccid, transaction=notification_type).first():
                    return
                save_notif = True
                callback_required = True

                transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {expiry_date}."

                #   insert code here if plan started is needed later on
            elif usage_percent == 80:
                if bundle.unlimited:
                    return
                notification_type = "limit_80"

                notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                if notification and notification.transaction == notification_type:
                    return
                transaction_message = (
                    f"Dear {email}, you have reached a significant level of consumption, please " f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                order = (
                    Order_history.objects(
                        reseller_type="reseller",
                        iccid=iccid,
                        plan_status__ne="Expired",
                        order_status="Successful",
                    ).count()
                    == 1
                )
                if not order_history.email_count_key and order:
                    notify_user = True
                    callback_required = True
                    save_notif = True
                if notify_user:
                    order_history.update(set__email_count_key=2)

            elif usage_percent == 100:
                if bundle.unlimited and order_history.expiry_date > datetime.datetime.utcnow():
                    return
                # If user doesn't have any left top-ups
                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="reseller",
                    plan_status__nin=["Active", "Expired"],
                ).first()
                if pending_order:
                    # Access bundle data once to avoid repeated lookups
                    pending_bundle = Bundles.objects(bundle_code=pending_order.bundle_code).first()

                    # Prepare the updates
                    updates = {"plan_status": "Active"}

                    # If the bundle data is unlimited, calculate and update expiry date and other fields
                    if pending_bundle.unlimited:
                        expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=pending_bundle.bundle_duration)
                        updates.update(
                            {
                                "plan_started": True,
                                "expiry_date": expiry_date,
                            }
                        )

                    pending_order.update(**updates)

                notification_type = "limit_100"
                notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                if notification and notification.transaction == notification_type:
                    return
                transaction_message = (
                    f"Dear {email}, Your {bundle.bundle_name} plan has expired. You have "
                    f"reached your consumption limit. You can check available top ups to "
                    f"activate your plan."
                )
                save_notif = notify_user = callback_required = Order_history.objects(
                    reseller_type="reseller",
                    iccid=iccid,
                    plan_status__ne="Expired",
                    order_status="Successful",
                ).count() == 0 and (order_history.email_count_key == 0 or order_history.email_count_key == 2)
                if notify_user:
                    order_history.update(set__email_count_key=1)

            if usage_percent in ESIMGO_NOTIFICATION_TO_SAVE:
                if save_notif:
                    save_notification(notf_id, email, iccid, transaction_message, notification_type, bundle_code, "reseller")

                if notify_user:
                    send_email_response = self.reseller_helper.send_data_consumption_email_api_reseller(
                        str(order_history.id), str(order_history.reseller_id.id), notification_type, str(order_history.iccid)
                    )
                    try:
                        if send_email_response.status_code == 200:
                            user_notified_message = send_email_response.json()
                        else:
                            user_notified_message = send_email_response.json().get("detail", "")

                    except Exception as e:
                        user_notified_message = "Error Sending email" + str(e)

                    new_doc = {
                        "notification_type": notification_type,
                        "order_reference": order_history.plan_uid,
                        "order_number": str(order_history.id),
                        "iccid": iccid,
                        "user_notified": user_notified_message,
                    }
                    CallbackHistory(**new_doc).save()
                if callback_required:
                    thread = threading.Thread(
                        target=self.reseller_helper.send_reseller_push_notification,
                        args=(
                            iccid,
                            str(order_history.id),
                            notification_type,
                            str(order_history.reseller_id.id),
                        ),
                    )
                    thread.start()


class VodafoneWebhookHelper(WebhookHelper):
    def limit_notification(self, vodafone_limit_input):
        logger.debug("Received vodafone_limit_notification: %s", vodafone_limit_input)

        id_type = vodafone_limit_input.get("idType", None)
        msisdn = vodafone_limit_input.get("idValue", None)
        notification_type = vodafone_limit_input.get("notificationType", None)
        notf_id = "notf_" + str(generate_temp_otp(12))
        # Subscriber code
        if id_type != "msisdn" or not msisdn:
            return
        if order_history := Order_history.objects(
            reseller_type="subscriber", vendor_msisdn=msisdn, order_status="Successful"
        ).first():  # Subscriber code

            iccid = order_history.iccid
            email = order_history.client_email
            user = get_user(email)
            bundle_code = order_history.bundle_code
            validity_date = datetime.datetime.date(
                datetime.datetime.utcnow() + datetime.timedelta(days=order_history.bundle_data.bundle_duration)
            )
            add_feedback_date = False
            transaction = "NONE"
            transaction_message = "NONE"
            # Set customer in the WebhookHelper class
            self.customer = user
            self.initialize_subscriber_helper(self.customer)

            user_email = None
            if user.first_name != "":
                username = user_email = user.first_name.capitalize()

            else:
                username = user.user_email

            label = Labels.objects(iccid=iccid, client_email=email).first()
            if label:
                bundle_label_name = label["label_name"]
                bundle_name = label["label_name"]
            else:
                bundle_label_name = order_history.bundle_data.bundle_marketing_name
                bundle_name = order_history.bundle_data.bundle_name

            if notification_type == "thing activated":
                transaction = "StartBundle"
                expiry_date = vodafone_limit_input.get("plannedActiveEndDate")
                expiry_date = datetime.datetime.strptime(expiry_date, "%Y-%m-%dT%H:%M:%S.%fZ")

                if not order_history.plan_started:
                    order_history.update(**{"expiry_date": expiry_date, "plan_started": True})
                    logger.info(
                        "Vodafone order_history updated successfully for StartBundle notifications with ICCID: %s",
                    )
                if NotificationLogs.objects(email=email, iccid=iccid, transaction=transaction).first():
                    return
                transaction_message = f"Your {bundle_label_name} plan is " f"now active and valid until {validity_date}."
            elif notification_type == "BundleLowUsage":
                data_allocated = get_all_topups_balance(iccid, instance_config=consumer_config)
                bundle_data_amount_remaining = 200  # always by vodafone
                bundle_data_unit = order_history.bundle_data.data_unit
                percentage = round(bundle_data_amount_remaining / data_allocated) * 100
                transaction = "limit_80"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, you have reached a significant level of consumption, " f"please check available top-ups"
                )

                consumption_email_data = {
                    "user": user_email,
                    "iccid": iccid,
                    "bundle_name": bundle_name,
                    "data_usage_consumption": 80,
                    "remaining_amount": f"{bundle_data_amount_remaining} MB",
                    "montyesim_msisdn": get_setting().whatsapp_misisdn,
                }

                consumption_email_data["category"] = 5

                self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)
            elif notification_type == "BundleDepletion":
                transaction = "limit_100"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                add_feedback_date = True
                transaction_message = (
                    f"Dear User, Your {bundle_label_name} plan has expired. You have "
                    f"reached your consumption limit. You can check available top ups to "
                    f"activate your plan."
                )
                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                bundle_data_unit = order_history.bundle_data.data_unit
                consumption_email_data = {
                    "user": user_email,
                    "bundle_name": bundle_name,
                    "data_usage_consumption": 100,
                    "remaining_amount": f"{0} MB",
                    "iccid": iccid,
                    "montyesim_msisdn": get_setting().whatsapp_misisdn,
                }

                consumption_email_data["category"] = 5

                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")
                self.subscriber_helper.send_consumption_email(**consumption_email_data)
                logger.info("bundle depleted. set all orders to expired for user %s", email)
            elif notification_type == "BundleValidityExpiryWarning":  # to be updated
                transaction = "limit_80"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, Your {bundle_label_name} plan will expire soon. "
                    f"Any remaining data will be lost. You can check available top ups to activate "
                    f"your plan."
                )
            elif notification_type == "BundleValidityExpiry":  # to be updated
                transaction = "limit_100"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, Your {bundle_label_name} plan has expired. "
                    f"You can check available top ups to activate your plan."
                )
                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")
                logger.info("bundle expired. set all orders to expired for user %s", email)
            elif notification_type == "Cooldown":
                order_history.update(set__plan_status="Expired")
                profile_msisdn_or_iccid_query = Q(msisdn=msisdn) | Q(iccid=iccid)
                if profile := Profiles.objects(profile_msisdn_or_iccid_query).first():
                    days_ago = (datetime.datetime.utcnow() - profile.create_datetime).days
                    logger.info("Vodafone profile %s was created %s days ago and is expired completely, disabling...", msisdn, days_ago)
                    profile.update(set__status=False)
                else:
                    error = f"Couldn't find profile for msisdn {msisdn} or iccid {iccid}"
                    logger.error(error)
                    raise ValueError(error)
            if notification_type in VODAFONE_NOTIFICATION_TO_SAVE:
                notif_log = {
                    "notification_id": notf_id,
                    "email": email,
                    "transaction_message": transaction_message,
                    "transaction": transaction,
                    "iccid": iccid,
                    "bundle_code": order_history.bundle_data.bundle_code,
                    "reseller_type": "subscriber",
                }
                if add_feedback_date:
                    notif_log["customer_feedback_date"] = datetime.datetime.date(datetime.datetime.now() + datetime.timedelta(days=2))
                save_notification_logs(notif_log)
                user = get_user(email)
                data = {
                    "username": username,
                    "message": transaction_message,
                    "validity_date": validity_date,
                    "customer_email": email,
                    "bundle_marketing_name": bundle_label_name,
                }
                if notification_type == "thing activated":
                    data["category"] = "6"
                self.firebase.send_notification(customer=user, data=data, transaction=transaction)

        # Reseller code
        else:
            order = Order_history.objects(
                reseller_type="reseller",
                vendor_msisdn=msisdn,
                order_status="Successful",
            ).first()
            iccid = order.iccid
            email = order.client_email
            bundle_code = order.bundle_code
            bundle = Bundles.objects(bundle_code=bundle_code).first()
            logger.info("order_history as reseller %s", order["order_number"])
            if order:
                # for reseller
                callback_required = None
                reseller_notification_type = None
                transaction_message = None
                save_notif = False
                if notification_type == "thing activated":
                    logger.info("received plan started notif for iccid %s", order.iccid)
                    reseller_notification_type = "StartBundle"
                    bundle_duration = bundle.bundle_duration
                    expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_duration)

                    if not order.plan_started:
                        order.update(
                            **{
                                "expiry_date": expiry_date,
                                "plan_status": "Active",
                                "plan_started": True,
                            }
                        )
                        logger.info(
                            "Vodafone order_history updated successfully for Started notifications with ICCID: %s",
                        )
                    if NotificationLogs.objects(email=email, iccid=iccid, transaction=reseller_notification_type).first():
                        return
                    save_notif = True
                    callback_required = True
                    transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {expiry_date}."

                if notification_type == "BundleDepletion" or notification_type == "BundleValidityExpiry":
                    logger.info("received plan expired notif for iccid %s notif type %s", order.iccid, notification_type)
                    reseller_notification_type = "limit_100"
                    notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                    if notification and notification.transaction == reseller_notification_type:
                        return
                    transaction_message = (
                        f"Dear {email}, Your {bundle.bundle_name} plan has expired. You have "
                        f"reached your consumption limit. You can check available top ups to "
                        f"activate your plan."
                    )
                    # for vodafone all bundles and topup = 1 bundle so we remove .first()
                    res = Order_history.objects(vendor_msisdn=msisdn)
                    if res:
                        res.update(set__plan_status="Expired")
                    pending_order = Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).first()
                    if pending_order:
                        pending_order.update(plan_status="Active")
                    self.reseller_helper.send_data_consumption_email_api_reseller(
                        str(order.id), str(order.reseller_id.id), reseller_notification_type, str(order.iccid)
                    )
                    callback_required = True
                    save_notif = True

                elif notification_type == "BundleLowUsage":
                    logger.info("received plan low usage notif for iccid %s notif type %s", order.iccid, notification_type)
                    reseller_notification_type = "limit_80"
                    notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                    if notification and notification.transaction == reseller_notification_type:
                        return
                    transaction_message = (
                        f"Dear {email}, you have reached a significant level of consumption, please " f"check available top-ups"
                    )
                    self.reseller_helper.send_data_consumption_email_api_reseller(
                        str(order.id), str(order.reseller_id.id), reseller_notification_type, str(order.iccid)
                    )

                    callback_required = True
                    save_notif = True
                elif notification_type == "Cooldown":
                    # user_iccid.update(set__status="expired")
                    profile_msisdn_or_iccid_query = Q(msisdn=msisdn) | Q(iccid=order.iccid)
                    if profile := Profiles.objects(profile_msisdn_or_iccid_query).first():
                        days_ago = (datetime.datetime.utcnow() - profile.create_datetime).days
                        logger.info("Vodafone profile %s was created %s days ago and is expired completely, disabling...", msisdn, days_ago)
                        profile.update(set__status=False)
                    else:
                        error = f"Couldn't find profile for msisdn {msisdn} or iccid {order.iccid}"
                        logger.error(error)
                        raise ValueError(error)
                if notification_type in VODAFONE_NOTIFICATION_TO_SAVE:
                    if save_notif:
                        save_notification(notf_id, email, iccid, transaction_message, reseller_notification_type, bundle_code, "reseller")
                    if callback_required:
                        thread = threading.Thread(
                            target=self.reseller_helper.send_reseller_push_notification,
                            args=(
                                order.iccid,
                                str(order.id),
                                reseller_notification_type,
                                str(order.reseller_id.id),
                            ),
                        )
                        thread.start()


class FlexiWebhookHelper(WebhookHelper):
    def limit_notification(self, flexi_limit_input):
        logger.debug("Received flexi_limit_notification: %s", flexi_limit_input)

        plan_uid = flexi_limit_input.get("plan_uid")
        notification_type = flexi_limit_input.get("notification_type")
        message = flexi_limit_input.get("message")
        notify_user = False
        add_feedback_date = False
        transaction_message: str
        notf_id: str = f"notf_{generate_temp_otp(12)}"
        # Subscriber code
        if order_history := Order_history.objects(
            plan_uid=plan_uid,
            order_status="Successful",
            reseller_type="subscriber",
            plan_status__ne="Expired",
        ).first():  # Subscriber code
            iccid: str = order_history.iccid
            email: str = order_history.client_email
            bundle_code: str = order_history.bundle_code
            user: AppUserDetails = get_user(email)
            expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=order_history.bundle_data.bundle_duration)
            validity_date = datetime.datetime.date(expiry_date)
            transaction: str
            # Set customer in the WebhookHelper class
            self.customer = user
            self.initialize_subscriber_helper(self.customer)

            user_email = None
            if user.first_name != "":
                username = user_email = user.first_name.capitalize()
            else:
                username = user.user_email

            label = Labels.objects(iccid=iccid, client_email=email).first()
            if label:
                bundle_label_name = label["label_name"]
                bundle_name = label["label_name"]
            else:
                bundle_label_name = order_history.bundle_data.bundle_marketing_name
                bundle_name = order_history.bundle_data.bundle_name
            if notification_type == "Plan Status":

                if message == "Plan Started and Selected":
                    transaction = "StartBundle"
                    if not order_history.plan_started:
                        order_history.update(
                            **{
                                "plan_started": True,
                                "expiry_date": expiry_date,
                                "plan_status": "Active",
                            }
                        )
                        logger.info("Flexi v1 order_history updated successfully for StartBundle notifications with ICCID: %s", iccid)
                    if NotificationLogs.objects(email=email, iccid=iccid, transaction=transaction).first():
                        return
                    transaction_message = f"Your {bundle_label_name} plan is " f"now active and valid until {expiry_date}."
                    # Check whether user has top-ups, if yes don't notify with top-up plan starting
                    notify_user = (
                        Order_history.objects(
                            reseller_type="subscriber",
                            iccid=iccid,
                            plan_uid__nin=[None, ""],
                        )
                        .first()
                        .plan_status
                        == "Active"
                    )
                elif message == "Plan Expired":
                    transaction = "limit_100"

                    if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                        if notification.transaction == transaction:
                            return
                    add_feedback_date = True

                    transaction_message = (
                        f"Dear {username}, Your {bundle_label_name} plan has expired. You have "
                        f"reached your consumption limit. You can check available top ups to "
                        f"activate your plan."
                    )
                    order_history.update(
                        **{
                            "plan_status": "Expired",
                            "expiry_date": datetime.datetime.utcnow(),
                        }
                    )
                    pending_order = Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).first()
                    if pending_order:
                        pending_order.update(plan_status="Active")
                    if (
                        notify_user := Order_history.objects(
                            reseller_type="subscriber",
                            iccid=iccid,
                            plan_status="Pending",
                            plan_uid__nin=[None, ""],
                        ).count()
                        == 0
                    ):
                        bundle_data_unit = order_history.bundle_data.data_unit
                        consumption_email_data = {
                            "user": user_email,
                            "data_usage_consumption": 100,
                            "remaining_amount": f"{0} MB",
                            "bundle_name": bundle_name,
                            "iccid": iccid,
                            "montyesim_msisdn": get_setting().whatsapp_misisdn,
                        }

                        consumption_email_data["category"] = 5

                        self.subscriber_helper.send_consumption_email(**consumption_email_data)
                    logger.info("bundle depleted. set all orders to expired for user %s", email)
            elif notification_type == "Data Utilization":
                transaction = "limit_80"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, you have reached a significant level of consumption, please " f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "iccid": iccid,
                        "bundle_name": bundle_name,
                        "data_usage_consumption": 80,
                        "remaining_amount": f"{order_history.bundle_data.data_amount * 0.2} MB",
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)

            if notify_user:
                notif_log = {
                    "notification_id": notf_id,
                    "email": email,
                    "transaction_message": transaction_message,
                    "transaction": transaction,
                    "iccid": iccid,
                    "bundle_code": order_history.bundle_data.bundle_code,
                    "reseller_type": "subscriber",
                }
                if add_feedback_date:
                    notif_log["customer_feedback_date"] = datetime.datetime.date(datetime.datetime.now() + datetime.timedelta(days=2))
                save_notification_logs(notif_log)
                data = {
                    "username": username,
                    "message": transaction_message,
                    "validity_date": validity_date,
                    "customer_email": email,
                    "bundle_marketing_name": bundle_label_name,
                }
                if message == "Plan Started and Selected":
                    data["category"] = "6"
                self.firebase.send_notification(customer=user, data=data, transaction=transaction)

        # Reseller code
        elif order_history := Order_history.objects(
            reseller_type="reseller",
            plan_uid=plan_uid,
            order_status="Successful",
            plan_status__ne="Expired",
        ).first():
            callback_required = None
            save_notif = False
            reseller_notification_type = None
            transaction_message = None
            iccid: str = order_history.iccid
            email: str = order_history.client_email
            bundle_code = order_history.bundle_code
            bundle = Bundles.objects(bundle_code=bundle_code).first()
            if notification_type == "Plan Status":
                if message == "Plan Started and Selected":
                    reseller_notification_type = "StartBundle"
                    bundle_duration = bundle.bundle_duration
                    expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_duration)

                    if not order_history.plan_started:
                        order_history.update(
                            **{
                                "expiry_date": expiry_date,
                                "plan_status": "Active",
                                "plan_started": True,
                            }
                        )
                        logger.info("order_history updated successfully for Started notifications with ICCID %s", iccid)
                    if NotificationLogs.objects(email=email, iccid=iccid, transaction=reseller_notification_type).first():
                        return
                    save_notif = True
                    callback_required = True

                    transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {expiry_date}."
                elif message == "Plan Expired":
                    reseller_notification_type = "limit_100"

                    notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                    if notification and notification.transaction == reseller_notification_type:
                        return
                    transaction_message = (
                        f"Dear {email}, Your {bundle.bundle_name} plan has expired. You have "
                        f"reached your consumption limit. You can check available top ups to "
                        f"activate your plan."
                    )
                    order_history.update(
                        **{
                            "plan_status": "Expired",
                            "expiry_date": datetime.datetime.utcnow(),
                        }
                    )
                    pending_order = Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).first()
                    if pending_order:
                        pending_order.update(plan_status="Active")
                    # If user doesn't have any left top-ups
                    save_notif = notify_user = callback_required = (
                        Order_history.objects(
                            iccid=iccid,
                            plan_status__ne="Expired",
                            order_status="Successful",
                            reseller_type="reseller",
                        ).count()
                        == 0
                    )

            elif notification_type == "Data Utilization":
                reseller_notification_type = "limit_80"
                message = "Data Utilization"
                notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                if notification and notification.transaction == reseller_notification_type:
                    return
                transaction_message = (
                    f"Dear {email}, you have reached a significant level of consumption, please " f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                save_notif = notify_user = callback_required = (
                    Order_history.objects(
                        iccid=iccid,
                        plan_status__ne="Expired",
                        order_status="Successful",
                        reseller_type="reseller",
                    ).count()
                    == 0
                )
            if message in FLEXIROAM_NOTIFICATION_TO_SAVE:
                if save_notif:
                    save_notification(notf_id, email, iccid, transaction_message, reseller_notification_type, bundle_code, "reseller")
                if notify_user:
                    self.reseller_helper.send_data_consumption_email_api_reseller(
                        str(order_history.id), str(order_history.reseller_id.id), reseller_notification_type, str(order_history.iccid)
                    )
                if callback_required:
                    thread = threading.Thread(
                        target=self.reseller_helper.send_reseller_push_notification,
                        args=(
                            order_history.iccid,
                            str(order_history.id),
                            reseller_notification_type,
                            str(order_history.reseller_id.id),
                        ),
                    )
                    thread.start()


class ComiumWebhookHelper(WebhookHelper):

    def limit_notification(self, comium_limit_input):
        logger.debug("Received comium_limit_notification: %s", comium_limit_input)

        notf_id: str = f"notf_{generate_temp_otp(12)}"
        plan_uid = comium_limit_input.get("plan_uid")
        notification_type = comium_limit_input.get("notification_type")
        expiry_date = comium_limit_input.get("expiry_date")
        notify_user = False
        add_feedback_date = False
        # Subscriber code
        if order_history := Order_history.objects(
            plan_uid=plan_uid,
            order_status="Successful",
            reseller_type="subscriber",
            plan_status__ne="Expired",
        ).first():  # Subscriber code
            iccid: str = order_history.iccid
            email: str = order_history.client_email
            bundle_code: str = order_history.bundle_code
            user: AppUserDetails = get_user(email)
            transaction_message: str
            transaction: str
            # Set customer in the WebhookHelper class
            self.customer = user
            self.initialize_subscriber_helper(self.customer)

            user_email = None
            if user.first_name != "":
                username = user.first_name
                user_email = user.first_name.capitalize()
            else:
                username = user.user_email

            label = Labels.objects(iccid=iccid, client_email=email).first()
            if label:
                bundle_label_name = label["label_name"]
                bundle_name = label["label_name"]
            else:
                bundle_label_name = order_history.bundle_data.bundle_marketing_name
                bundle_name = order_history.bundle_data.bundle_name
            if notification_type == "PLAN-STARTED":
                transaction = "StartBundle"
                # If the expiry date is None in the webhook response, generate it based on the bundle duration
                expiry_date = expiry_date or datetime.datetime.utcnow() + datetime.timedelta(days=order_history.bundle_data.bundle_duration)
                if not order_history.plan_started:
                    order_history.update(**{"plan_status": "Active", "plan_started": True, "expiry_date": expiry_date})
                    logger.info("Monty Mobile order_history updated successfully for StartBundle notifications with ICCID: %s", iccid)
                # Return if notification is already processed
                if NotificationLogs.objects(email=email, iccid=iccid, transaction=transaction).first():
                    return
                transaction_message = f"Your {bundle_label_name} plan is now active and valid until {expiry_date}."
                # Check whether user has top-ups, if yes don't notify with top-up plan starting
                notify_user = (
                    Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                    )
                    .first()
                    .plan_status
                    == "Active"
                )
            elif notification_type == "PLAN-EXPIRED":
                transaction = "limit_100"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                add_feedback_date = True

                transaction_message = (
                    f"Dear {username}, Your {bundle_label_name} plan has expired. "
                    f"You can check available top ups to activate your plan."
                )

                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")
                logger.info("bundle allowance expired. set all orders to expired for user %s", email)
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    # bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "data_usage_consumption": 100,
                        "remaining_amount": f"{0} MB",
                        "bundle_name": bundle_name,
                        "iccid": iccid,
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email(**consumption_email_data)
                    logger.info("bundle depleted. set all orders to expired for user %s", email)
            elif notification_type == "PLAN-100":
                transaction = "limit_100"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                add_feedback_date = True

                transaction_message = (
                    f"Dear {username}, Your {bundle_label_name} plan has expired. You have "
                    f"reached your consumption limit. You can check available top ups to "
                    f"activate your plan."
                )
                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    # bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "data_usage_consumption": 100,
                        "remaining_amount": f"{0} MB",
                        "bundle_name": bundle_name,
                        "iccid": iccid,
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email(**consumption_email_data)
                    logger.info("bundle depleted. set all orders to expired for user %s", email)
            elif notification_type == "PLAN-80":
                transaction = "limit_80"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                transaction_message = (
                    f"Dear {username}, you have reached a significant level of consumption, please " f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    # bundle_data_unit = order_history.bundle_data.data_unit
                    consumption_email_data = {
                        "user": user_email,
                        "iccid": iccid,
                        "bundle_name": bundle_name,
                        "data_usage_consumption": 80,
                        "remaining_amount": f"{order_history.bundle_data.data_amount * 0.2} MB",
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)

            if notify_user:
                notif_log = {
                    "notification_id": notf_id,
                    "email": email,
                    "transaction_message": transaction_message,
                    "transaction": transaction,
                    "iccid": iccid,
                    "bundle_code": order_history.bundle_data.bundle_code,
                }
                if add_feedback_date:
                    notif_log["customer_feedback_date"] = datetime.datetime.date(datetime.datetime.utcnow() + datetime.timedelta(days=2))
                save_notification_logs(notif_log)
                data = {
                    "username": username,
                    "message": transaction_message,
                    "validity_date": expiry_date,
                    "customer_email": email,
                    "bundle_marketing_name": bundle_label_name,
                }
                if notification_type == "PLAN-STARTED":
                    data["category"] = "6"
                self.firebase.send_notification(customer=user, data=data, transaction=transaction)

        # Reseller code
        elif order_history := Order_history.objects(
            reseller_type="reseller",
            plan_uid=plan_uid,
            order_status="Successful",
            plan_status__ne="Expired",
        ).first():
            callback_required = None
            save_notif = False
            iccid: str = order_history.iccid
            email: str = order_history.client_email
            reseller_notification_type = None
            transaction_message = None
            bundle_code = order_history.bundle_code
            bundle = Bundles.objects(bundle_code=bundle_code).first()
            if notification_type == "PLAN-STARTED":
                reseller_notification_type = "StartBundle"
                bundle_duration = bundle.bundle_duration
                # If the expiry date is None in the webhook response, generate it based on the bundle duration
                expiry_date = expiry_date or datetime.datetime.utcnow() + datetime.timedelta(days=bundle_duration)
                if not order_history.plan_started:
                    order_history.update(
                        **{
                            "expiry_date": expiry_date,
                            "plan_status": "Active",
                            "plan_started": True,
                        }
                    )
                    logger.info("Monty Mobile order_history updated successfully for Started notifications with ICCID: %", iccid)

                if NotificationLogs.objects(email=email, iccid=iccid, transaction=reseller_notification_type).first():
                    return
                save_notif = True
                callback_required = True
                transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {expiry_date}."
                #   insert code here if plan started is needed later on
                pass
            elif notification_type in ["PLAN-100", "PLAN-EXPIRED"]:
                reseller_notification_type = "limit_100"

                notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                if notification and notification.transaction == reseller_notification_type:
                    return
                transaction_message = (
                    f"Dear {email}, Your {bundle.bundle_name} plan has expired. " f"You can check available top ups to activate your plan."
                )

                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")
                # If user doesn't have any left top-ups
                save_notif = notify_user = callback_required = (
                    Order_history.objects(
                        iccid=iccid,
                        plan_status__ne="Expired",
                        order_status="Successful",
                    ).count()
                    == 0
                )

            elif notification_type == "PLAN-80":
                reseller_notification_type = "limit_80"
                notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                if notification and notification.transaction == reseller_notification_type:
                    return

                transaction_message = (
                    f"Dear {email}, your data {bundle.bundle_name} have reached a significant level of consumption, please "
                    f"check available top-ups"
                )
                # If user doesn't have any left top-ups
                save_notif = notify_user = callback_required = (
                    Order_history.objects(
                        reseller_type="reseller",
                        iccid=iccid,
                        plan_status__ne="Expired",
                        order_status="Successful",
                    ).count()
                    == 1
                )
            if notification_type in COMIUM_NOTIFICATION_TO_SAVE:
                if save_notif:
                    save_notification(notf_id, email, iccid, transaction_message, reseller_notification_type, bundle_code, "reseller")
                if notify_user:
                    self.reseller_helper.send_data_consumption_email_api_reseller(
                        str(order_history.id), str(order_history.reseller_id.id), reseller_notification_type, str(order_history.iccid)
                    )
                if callback_required:
                    thread = threading.Thread(
                        target=self.reseller_helper.send_reseller_push_notification,
                        args=(
                            order_history.iccid,
                            str(order_history.id),
                            reseller_notification_type,
                            str(order_history.reseller_id.id),
                        ),
                    )
                    thread.start()


class IndosatWebhookHelper(WebhookHelper):
    def limit_notification(self, indosat_limit_input):
        logger.debug("Received indosat_limit_notification: %s", indosat_limit_input)

        notify_user = False
        eventId = indosat_limit_input.get("eventId", None)
        notification_type = indosat_limit_input.get("eventType", None)
        data = indosat_limit_input.get("data", None)
        data_xml = ET.fromstring(data)
        notf_id = "notf_" + eventId

        data_dict = {}

        ns = {"ns": "http://api.jasperwireless.com/ws/schema"}

        for child in data_xml:
            element_name = child.tag.replace(f'{{{ns["ns"]}}}', "")  # Remove namespace
            element_value = child.text
            data_dict[element_name] = element_value
        iccid = data_dict.get("iccid", None)
        if not iccid:
            return
        if order_history := Order_history.objects(
            iccid=iccid,
            reseller_type="subscriber",
            order_status="Successful",
            plan_status__ne="Expired",
        ).first():  # Subscriber code
            email = order_history.client_email
            user = get_user(email)
            expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=order_history.bundle_data.bundle_duration)
            validity_date = datetime.datetime.date(expiry_date)
            add_feedback_date = False
            transaction_message = "NONE"
            transaction = "NONE"
            # Set customer in the WebhookHelper class
            self.customer = user
            self.initialize_subscriber_helper(self.customer)
            user_email = None
            if user.first_name != "":
                username = user_email = user.first_name.capitalize()

            else:
                username = user.user_email

            label = Labels.objects(iccid=iccid, client_email=email).first()
            if label:
                bundle_label_name = label["label_name"]
                bundle_name = label["label_name"]
            else:
                bundle_label_name = order_history.bundle_data.bundle_marketing_name
                bundle_name = order_history.bundle_data.bundle_name

            if notification_type == "SESSION_START":

                if not order_history.plan_started:
                    order_history.update(
                        **{
                            "plan_started": True,
                            "expiry_date": expiry_date,
                            "plan_status": "Active",
                        }
                    )
                    logger.info("Indosat order_history updated successfully for StartBundle notifications with ICCID: %s", iccid)

                for order in Order_history.objects(iccid=iccid, order_status="Successful", plan_status="Pending"):
                    expiry_date = expiry_date + datetime.timedelta(days=order.bundle_data.bundle_duration)
                    order.update(expiry_date=expiry_date)
                transaction = "StartBundle"
                if NotificationLogs.objects(email=email, iccid=iccid, transaction=transaction).first():
                    return
                transaction_message = f"Your {bundle_label_name} plan is now active and valid until {validity_date}."
                # Check whether user has top-ups, if yes don't notify with top-up plan starting
                notify_user = (
                    Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                    )
                    .first()
                    .plan_status
                    == "Active"
                )

            elif notification_type == "DATA_LIMIT":
                total_included_usage = float(data_dict.get("totalIncludedUsage"))
                total_actual_usage = float(data_dict.get("totalActualUsage"))
                usage_percentage = (total_actual_usage / total_included_usage) * 100

                if 80 <= usage_percentage <= 95:
                    bundle_data_amount_remaining = round((total_included_usage - total_actual_usage) / (1024 * 1024), 2)
                    transaction = "limit_80"
                    if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                        if notification.transaction == transaction:
                            return
                    transaction_message = (
                        f"Dear {username}, you have reached a significant level of consumption, " f"please check available top-ups"
                    )
                    if (
                        notify_user := Order_history.objects(
                            iccid=iccid,
                            order_status="Successful",
                            reseller_type="subscriber",
                            plan_status="Pending",
                        ).count()
                        == 0
                    ):
                        consumption_email_data = {
                            "user": user_email,
                            "iccid": iccid,
                            "bundle_name": bundle_name,
                            "data_usage_consumption": 80,
                            "remaining_amount": f"{bundle_data_amount_remaining} MB",
                            "montyesim_msisdn": get_setting().whatsapp_misisdn,
                        }

                        consumption_email_data["category"] = 5

                        self.subscriber_helper.send_consumption_email__low_usage(**consumption_email_data)
            elif notification_type == "PREPAID_PLAN_COMPLETION":
                transaction = "limit_100"
                if notification := NotificationLogs.objects(email=email, iccid=iccid).first():
                    if notification.transaction == transaction:
                        return
                add_feedback_date = True
                transaction_message = (
                    f"Dear User, Your {bundle_label_name} plan has expired. You have "
                    f"reached your consumption limit. You can check available top ups to "
                    f"activate your plan."
                )
                order_history.update(
                    **{
                        "plan_status": "Expired",
                        "expiry_date": datetime.datetime.utcnow(),
                    }
                )
                if (
                    notify_user := Order_history.objects(
                        iccid=iccid,
                        order_status="Successful",
                        reseller_type="subscriber",
                        plan_status="Pending",
                    ).count()
                    == 0
                ):
                    consumption_email_data = {
                        "user": user_email,
                        "bundle_name": bundle_name,
                        "data_usage_consumption": 100,
                        "remaining_amount": f"{0} MB",
                        "iccid": iccid,
                        "montyesim_msisdn": get_setting().whatsapp_misisdn,
                    }

                    consumption_email_data["category"] = 5

                    self.subscriber_helper.send_consumption_email(**consumption_email_data)

                pending_order = Order_history.objects(
                    iccid=iccid,
                    order_status="Successful",
                    reseller_type="subscriber",
                    plan_status="Pending",
                ).first()
                if pending_order:
                    pending_order.update(plan_status="Active")

            if notify_user and notification_type in INDOSAT_NOTIFICATION_TO_SAVE:
                notif_log = {
                    "notification_id": notf_id,
                    "email": email,
                    "transaction_message": transaction_message,
                    "transaction": transaction,
                    "iccid": iccid,
                    "bundle_code": order_history.bundle_data.bundle_code,
                    "reseller_type": "subscriber",
                }
                if add_feedback_date:
                    notif_log["customer_feedback_date"] = datetime.datetime.date(datetime.datetime.utcnow() + datetime.timedelta(days=2))

                save_notification_logs(notif_log)
                user = get_user(email)
                data = {
                    "username": username,
                    "message": transaction_message,
                    "validity_date": validity_date,
                    "customer_email": email,
                    "bundle_marketing_name": bundle_label_name,
                }
                if notification_type == "SESSION_START":
                    data["category"] = "6"
                self.firebase.send_notification(customer=user, data=data, transaction=transaction)

        else:  # Reseller code
            order = Order_history.objects(
                reseller_type="reseller",
                iccid=iccid,
                order_status="Successful",
                plan_status__ne="Expired",
            ).first()
            if order:
                email = order.client_email
                bundle_code = order.bundle_code
                bundle = Bundles.objects(bundle_code=bundle_code).first()
                callback_required = None
                save_notif = False
                transaction_message = None
                reseller_notification_type = None
                if notification_type == "PREPAID_PLAN_COMPLETION":
                    reseller_notification_type = "limit_100"
                    notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                    if notification and notification.transaction == reseller_notification_type:
                        return
                    transaction_message = (
                        f"Dear User, Your {bundle.bundle_name} plan has expired. You have "
                        f"reached your consumption limit. You can check available top ups to "
                        f"activate your plan."
                    )
                    order.update(set__plan_status="Expired")
                    # If user doesn't have any left top-ups
                    save_notif = notify_user = callback_required = (
                        Order_history.objects(
                            iccid=iccid,
                            plan_status__ne="Expired",
                            order_status="Successful",
                        ).count()
                        == 0
                    )

                elif notification_type == "DATA_LIMIT":
                    total_included_usage = float(data_dict.get("totalIncludedUsage"))
                    total_actual_usage = float(data_dict.get("totalActualUsage"))
                    usage_percentage = (total_actual_usage / total_included_usage) * 100

                    if 80 <= usage_percentage <= 95:
                        reseller_notification_type = "limit_80"
                        notification = NotificationLogs.objects(email=email, iccid=iccid).first()
                        if notification and notification.transaction == reseller_notification_type:
                            return
                        transaction_message = (
                            f"Dear {email}, you have reached a significant level of consumption, " f"please check available top-ups"
                        )
                        # If user doesn't have any left top-ups
                        save_notif = notify_user = callback_required = (
                            Order_history.objects(
                                iccid=iccid,
                                plan_status__ne="Expired",
                                order_status="Successful",
                            ).count()
                            == 1
                        )
                    else:
                        notify_user = False

                elif notification_type == "SESSION_START":
                    reseller_notification_type = "StartBundle"
                    bundle_duration = bundle.bundle_duration
                    expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_duration)
                    if not order.plan_started:
                        order.update(
                            **{
                                "expiry_date": expiry_date,
                                "plan_status": "Active",
                                "plan_started": True,
                            }
                        )
                        logger.info("Indosat order_history updated successfully for Started notifications with ICCID: %s", iccid)

                    if NotificationLogs.objects(email=email, iccid=iccid, transaction=reseller_notification_type).first():
                        return
                    validity_date = datetime.datetime.date(expiry_date)
                    save_notif = True
                    transaction_message = f"Your {bundle.bundle_name} plan is now active and valid until {validity_date}."
                    # If user doesn't have any left top-ups
                    notify_user = callback_required = (
                        Order_history.objects(
                            iccid=iccid,
                            plan_status__exists=True,
                            plan_status__ne="Active",
                            order_status="Successful",
                        ).count()
                        == 0
                    )
                if notification_type in INDOSAT_NOTIFICATION_TO_SAVE:
                    if save_notif:
                        save_notification(notf_id, email, iccid, transaction_message, reseller_notification_type, bundle_code, "reseller")
                    if notify_user:
                        self.reseller_helper.send_data_consumption_email_api_reseller(
                            str(order.id), str(order.reseller_id.id), reseller_notification_type, str(order.iccid)
                        )
                    if callback_required:
                        thread = threading.Thread(
                            target=self.reseller_helper.send_reseller_push_notification,
                            args=(
                                order.iccid,
                                str(order.id),
                                reseller_notification_type,
                                str(order.reseller_id.id),
                            ),
                        )
                        thread.start()


class MontyResellerWebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = [1, 80, 100]
    start_trigger = "BUNDLE_STARTED"
    eighty_percent = "BUNDLE_SIGNIFICANT_CONSUMPTION"
    hundred_percent = "BUNDLE_EXPIRED"

    @dataclass
    class MontyResellerNotificationSchema:
        iccid: str
        order_id: str
        event_type: str
        event_date: str

    def limit_notification(self, data: [MontyResellerNotificationSchema, dict]) -> None:
        logger.debug("Received monty_reseller_limit_notification: %s", data)

        iccid: str = data.iccid
        if data.event_type == self.start_trigger:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 1
        elif data.event_type == self.eighty_percent:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 80
        elif data.event_type == self.hundred_percent:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 100

        else:
            return
        self.order_history: Order_history = Order_history.objects(iccid=iccid, order_status="Successful", plan_status__ne="Expired").first()

        if not self.order_history:
            return
        try:
            if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                self.check_subscriber_order_to_notify_user()
            elif self.order_history.reseller_type == self.RESELLER_reseller_type:
                pass
        except AlreadyReceived as message:
            logger.info(message)
            pass


class FlexiroamV2WebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = [1, 80, 100]
    start_trigger = "Plan Started, Selected and Active"
    eighty_percent = "Data Utilization 80%"
    hundred_percent = "Plan Fully Utilized"

    @dataclass
    class FlexiroamV2NotificationSchema:
        plan_uuid: str
        sku: int
        iccid: str
        plan_code: str
        notification_type: str
        message: str
        last_updated_at: str  # string-formatted date

    def limit_notification(self, data: [FlexiroamV2NotificationSchema, dict]) -> None:
        logger.debug("Received flexi_v2_limit_notification: %s", data)

        iccid: str = data.iccid
        plan_uid: str = data.plan_uuid
        if data.message == self.start_trigger:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 1
        elif data.message == self.eighty_percent:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 80
        elif data.message == self.hundred_percent:
            logger.info(f"{data.iccid} started consumption")
            self.usage_percent = 100
        else:
            return
        self.order_history: Order_history = Order_history.objects(
            iccid=iccid,
            order_status="Successful",
            plan_status__ne="Expired",
            plan_uid=plan_uid,
        ).first()

        if not self.order_history:
            return
        try:
            if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                self.check_subscriber_order_to_notify_user()
            elif self.order_history.reseller_type == self.RESELLER_reseller_type:
                self.check_reseller_order_to_notify_user()
        except AlreadyReceived as message:
            logger.info(message)
            pass


class OrangeWebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = [1, 80, 100]
    start_trigger = "start consumption"
    eighty_percent = "test 80"
    hundred_percent = "hundred percent"

    @dataclass
    class CSVRow:
        ssn: str = field(metadata={"description": "Social Security Number"})
        voice_msisdn: str = field(metadata={"description": "Voice MSISDN"})
        data_msisdn: str = field(metadata={"description": "Data MSISDN"})
        trigger_type: str = field(metadata={"description": "Type of trigger"})
        trigger_name: str = field(metadata={"description": "Name of trigger"})
        trigger_identifier: str = field(metadata={"description": "Identifier of trigger"})
        triggering_value: str = field(metadata={"description": "Value that triggered the event"})
        threshold_value: str = field(metadata={"description": "Threshold value for the trigger"})
        business_rule: str = field(metadata={"description": "Applicable business rule"})
        countries: str = field(metadata={"description": "Affected countries"})
        operators: str = field(metadata={"description": "Affected operators"})
        alarm_identifier: str = field(metadata={"description": "Identifier of the alarm"})
        alarm_creation_date: str = field(metadata={"description": "Date when the alarm was created"})
        alarm_level: str = field(metadata={"description": "Level of the alarm"})

        @classmethod
        def get_example(cls) -> Dict[str, Any]:
            return {
                "ssn": "123456789",
                "voice_msisdn": "9876543210",
                "data_msisdn": "9876543210",
                "trigger_type": "USAGE",
                "trigger_name": "START_BUNDLE",
                "trigger_identifier": "SB001",
                "triggering_value": "100",
                "threshold_value": "100",
                "business_rule": "RULE_001",
                "countries": "USA",
                "operators": "OPERATOR_A",
                "alarm_identifier": "ALM001",
                "alarm_creation_date": "2024-06-19T11:08:00.003Z",
                "alarm_level": "HIGH",
            }

    def limit_notification(self, data: CSVRow):
        logger.debug("Received orange_limit_notification: %s", data)
        iccid: str
        iccid = data.ssn

        self.order_history: Order_history = Order_history.objects(iccid=iccid, order_status="Successful", plan_status__ne="Expired").first()

        if not self.order_history:
            return

        total_data_amount, _, _, _ = accumulate_vendor_data(iccid)

        triggering_value = float(data.triggering_value.split(" ")[0])
        if "MB" in str(data.triggering_value.split(" ")[1]).upper():
            triggering_value /= 1024

        initial_quantity = float(total_data_amount)
        remaining_quantity = abs(initial_quantity - round(triggering_value, 1))
        usage_percent = 100 - round((remaining_quantity / initial_quantity) * 100)

        if usage_percent in range(70):
            logger.info(f"{iccid} started consumption")
            self.usage_percent = 1
        elif usage_percent in range(70, 99):
            logger.info(f"{iccid} reached 80% of total data")
            self.usage_percent = 80
        elif usage_percent == 100:
            logger.info(f"{iccid} reached 100% of total data")
            self.usage_percent = 100
        else:
            logger.info(f"{iccid} reached {usage_percent}% of total data")
            return

        try:
            if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                self.check_subscriber_order_to_notify_user()
            elif self.order_history.reseller_type == self.RESELLER_reseller_type:
                self.check_reseller_order_to_notify_user()
        except AlreadyReceived as message:
            logger.info(message)
            pass

    def check_if_user_should_be_notified(self, per_plan_notifications: bool = True):
        return super().check_if_user_should_be_notified(per_plan_notifications=False)


def save_notification(notf_id, email, iccid, transaction_message, notification_type, bundle_code, reseller_type):
    notif_log = {
        "notification_id": notf_id,
        "email": email,
        "transaction_message": transaction_message,
        "transaction": notification_type,
        "iccid": iccid,
        "bundle_code": bundle_code,
        "reseller_type": reseller_type,
    }
    save_notification_logs(notif_log)


class BayobabWebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = [1, 80, 100]
    NOTIFICATION_MAP = {"plan-started": 1, "plan-80": 80, "plan-100": 100}

    def limit_notification(self, bayobab_limit_input):
        """Processes Bayobab limit notifications and updates order history."""
        logger.debug("Received Bayobab notification: %s", bayobab_limit_input)

        iccid = bayobab_limit_input.get("iccid")
        notification = bayobab_limit_input.get("userDescription")

        if not iccid or not notification:
            logger.warning("Missing required fields in Bayobab notification: %s", bayobab_limit_input)
            return

        self.usage_percent = self.NOTIFICATION_MAP.get(notification)
        if self.usage_percent is None:
            logger.warning("Received an unsupported notification type: %s", notification)
            return

        plan_started = self.usage_percent != 1  # Plan is not started only for "plan-started"
        logger.info("Processing notification '%s' for ICCID: %s, Usage: %d%%", notification, iccid, self.usage_percent)

        # Fetch order history
        self.order_history = Order_history.objects(
            iccid=iccid, order_status="Successful", plan_status__ne="Expired", plan_started=plan_started
        ).first()

        if not self.order_history:
            logger.warning("No active order history found for ICCID: %s with plan_started=%s", iccid, plan_started)
            return

        try:
            if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                logger.info("Notifying subscriber for ICCID: %s", iccid)
                self.check_subscriber_order_to_notify_user()
            elif self.order_history.reseller_type == self.RESELLER_reseller_type:
                logger.info("Notifying reseller for ICCID: %s", iccid)
                self.check_reseller_order_to_notify_user()
        except AlreadyReceived as message:
            logger.info("Notification already received for ICCID %s: %s", iccid, message)
        except Exception as e:
            logger.error("Error processing notification for ICCID %s: %s", iccid, str(e))
            pass


class TelkomselWebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = [1, 80, 100]
    NOTIFICATION_MAP = {"plan-started": 1, "plan-80": 80, "plan-100": 100}

    def limit_notification(self, telkomsel_limit_input):
        """Processes Telkomsel limit notifications and updates order history."""
        logger.info("Received Telkomsel notification: %s", telkomsel_limit_input)
        return


class LotusFlareWebhookHelper(WebhookHelper):
    NOTIFICATIONS_TO_SAVE = ["DATA_USAGE"]

    def usage_notification(self, lotusflare_payload: Dict[str, Any]) -> None:
        """Processes LotusFlare data usage notifications and updates order history."""
        logger.debug("Received LotusFlare notification: %s", lotusflare_payload)

        iccid = str(lotusflare_payload.get("data", {}).get("iccid"))
        event_type = lotusflare_payload.get("event_type")
        usage_percentage = lotusflare_payload.get("data", {}).get("used_percentage")

        if not iccid or not event_type or usage_percentage is None:
            logger.warning("Missing required fields in LotusFlare notification: %s", lotusflare_payload)
            return

        if event_type not in self.NOTIFICATIONS_TO_SAVE:
            logger.warning("Unsupported event type received: %s", event_type)
            return

        self.usage_percent = int(usage_percentage)
        logger.info("Processing '%s' event for ICCID: %s with usage: %d%%", event_type, iccid, self.usage_percent)

        self.order_history = Order_history.objects(iccid=iccid, order_status="Successful", plan_status__ne="Expired").first()

        if not self.order_history:
            logger.warning("No active order found for ICCID: %s", iccid)
            return

        try:
            if self.order_history.reseller_type == self.SUBSCRIBER_reseller_type:
                logger.info("Notifying subscriber for ICCID: %s", iccid)
                self.check_subscriber_order_to_notify_user()
            elif self.order_history.reseller_type == self.RESELLER_reseller_type:
                logger.info("Notifying reseller for ICCID: %s", iccid)
                self.check_reseller_order_to_notify_user()
        except AlreadyReceived as message:
            logger.info("Notification already received for ICCID %s: %s", iccid, message)
        except Exception as e:
            logger.error("Error processing LotusFlare notification for ICCID %s: %s", iccid, str(e))

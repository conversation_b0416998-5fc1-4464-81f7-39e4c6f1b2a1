import datetime
import time
from collections import defaultdict
from decimal import Decimal, InvalidOperation
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
from uuid import uuid4
import json
import secrets
import string

import mongoengine
import pymongo
import requests
import logging

from mongoengine import QuerySet

from app_models import consumer_models, main_models, mobiles, reseller_models
from app_models.reseller_models import Order_history, NotificationHistory
from datetime import timed<PERSON><PERSON>
from flask import request
from mongoengine.queryset.visitor import Q
from bson.objectid import ObjectId
from urllib3.exceptions import NewConnectionError

from helpers.constaints import TELKOMSEL_VENDOR
from helpers.support_helper import (
    get_setting,
    send_custom_support_email,
    deactivate_vendor,
    add_to_update_bundle_version,
    get_vendor_info,
    send_custom_monitor_email,
    cache_consumption_data
)
from helpers.utils import try_except_func

from helpers.constaints import (
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_TOPUP_BUNDLE,
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR,
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE,
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE_ALT,
    MONTYMOBILE_VENDOR,
    INDOSAT_VENDOR,
    FLEXIROAM_VENDOR,
    ESIMGO_VENDOR,
    VODAFONE_VENDOR,
    ORANGE_VENDOR, FLEXIROAM_VENDOR_V2, BAYOBAB_VENDOR
)
from helpers.support_helper import deactivate_bundle
from b2c_helpers.vendors import MontyMobile, Vodafone, ESIMGo, Indosat, Orange, Flexiroam, FlexiroamAPI, Bayobab, TelkomselVendor
from app_models.consumer_models import Countries, Profiles, Regions, Vendors, Bundles
import threading
import re
from logging_config import logger
from swagger_server.models.region_grouped_bundles_response import RegionGroupedBundlesResponse
from swagger_server.models.region_grouped_bundles_response_regions import RegionGroupedBundlesResponseRegions
from swagger_server.models.reseller_scoped_bundle import ResellerScopedBundle
from swagger_server.models.scoped_bundles_response import ScopedBundlesResponse

debug_ = True
app_json = "application/json"
allocation_exception = "couldn`t allocate bundle"
first = "$first"
country_code_list_col = "$country_code_list"
bundle_marketing_name = "$bundle_marketing_name"
group_expr = "$group"
bundle_name_col = "$bundle_name"
bundle_code_col = "$bundle_code"
country_list_col = "$country_list"
retail_price_col = "$retail_price"
bundle_duration_col = "$bundle_duration"
region_code_col = "$region_code"
data_amount_col = "$data_amount"
data_unit_col = "$data_unit"
currency_code_col = "$currency_code"
validity_amount_col = "$validity_amount"
project_expr = "$project"
sort_operation = "$sort"
region_name_col = "$region_name"
rate_revenue_col = "$rate_revenue"
flexi_number_of_profiles_needed = 3


# region CLASSES
class Fallback:

    def get_in_active_vendors(self):
        self.in_active_vendors = consumer_models.Vendors.objects(is_active=False).values_list("vendor_name")

    def __init__(self, allocate_per_vendor):
        self.allocate_per_vendor = allocate_per_vendor
        # max number of retrying to fix the error in vendor api
        self.MAX_NUMBER_OF_RETRY = 3
        self.in_active_vendors = []

    def is_retryable(self, allocate, topup_code, bundle_code, number_of_retry, vendor_name):
        is_assigning_success = allocate.get("success")
        if is_assigning_success:
            print(f"<allocate_per_vendor> we will not retry if assigning success: {is_assigning_success}")
            return False
        else:
            logging.error(f"<allocate_per_vendor> Allocation False, deactivating bundle {bundle_code}")
            deactivate_bundle(bundle_code, vendor_name=vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE_ALT.format(datetime.datetime.utcnow(), bundle_code),
            )

        is_assigning_topup = topup_code != ""
        if is_assigning_topup:
            deactivate_bundle(bundle_code, vendor_name=vendor_name, deactivate_topup=True)
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE_ALT.format(datetime.datetime.utcnow(), bundle_code),
            )
            logging.error("fallback wont work since we're trying to topup profile")
            return False

        # stop retrying we reach maximum retry
        if number_of_retry == self.MAX_NUMBER_OF_RETRY:
            return False

        return True

    def get_alternative(
        self,
        exception,
        email,
        bundle_code,
        bundle_duration,
        data_amount,
        country_code_list,
        exclude_vendor_names
    ):
        logger.error("error happened while buying bundle %s for user %s: %s", bundle_code, email, exception)

        logger.error("trying to find an alternative bundle other than %s for user %s", bundle_code, email)
        filter_kwargs = {
            "bundle_duration__gte": bundle_duration,
            "data_amount__gte": data_amount,
            "deleted": False,
            "country_code_list__all": country_code_list,
            "vendor_name__nin": [*exclude_vendor_names, *self.in_active_vendors],
            "is_active": True,
        }
        alternative_bundle = get_alternative_bundle(filter_kwargs)

        if not alternative_bundle:
            print(f"<allocate_per_vendor Fallback> we didnt found alternative bundle: {filter_kwargs}")
            return False

        return alternative_bundle


class ResellerFallback(Fallback):

    @staticmethod
    def get_reseller(instance_config, reseller_id):
        db = get_db_connection(instance_config)
        reseller_collection = db.get_collection("reseller")
        reseller = reseller_collection.find_one({"_id": ObjectId(reseller_id)})
        return reseller

    @staticmethod
    def get_branch(instance_config):
        db = get_db_connection(instance_config)
        branch_collection = db.get_collection("branch")
        branch = branch_collection.find_one({"_id": ObjectId(request.args.to_dict().get("branch_id"))})
        return branch

    @staticmethod
    def get_subscriber_price(unit_price, corp_rate_revenue):
        if not corp_rate_revenue:
            corp_rate_revenue = 0
        subscriber_price = unit_price * (100 + corp_rate_revenue) / 100
        return subscriber_price

    def __call__(self, number_of_retry=0, exclude_vendor_names=[], **kwargs):
        self.get_in_active_vendors()
        vendor_name = kwargs.get("vendor_name")
        bundle_vendor_code = kwargs.get("bundle_vendor_code")
        bundle_code = kwargs.get("bundle_code")
        email = kwargs.get("email")
        bundle_log = kwargs.get("bundle_log")
        instance_config = kwargs.get("instance_config")
        old_user_iccid = kwargs.get("user_iccid")
        otp = kwargs.get("otp")
        reseller_id = kwargs.get("reseller_id")
        category = kwargs.get("category")

        try:
            logger.info("start allocate bundle: %s for user: %s", bundle_code, email)
            allocate, exception, plan_uid, iccid, user_iccid, bundle_code = self.allocate_per_vendor(
                vendor_name,
                bundle_vendor_code,
                bundle_code,
                email,
                bundle_log,
                instance_config,
                old_user_iccid,
                otp,
                category,
                reseller_id,
            )
        except Exception as error:
            logger.exception(
                f"<allocate_per_vendor> error: {error} happened while we are trying to allocate bundle:{bundle_code}  user: {email}"
            )
            allocate = {"success": False, "send_email": False}
            plan_uid = iccid = user_iccid = None
            exception = str(error)

        is_topup = request.json.get("order_id", "")

        is_retryable = self.is_retryable(allocate, is_topup, bundle_code, number_of_retry, vendor_name)
        if not is_retryable:
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code

        # vendor_names already used and failed
        exclude_vendor_names = [*exclude_vendor_names, vendor_name]
        number_of_retry += 1

        bundle_info = get_bundle_info(bundle_code=bundle_code, topup_code="")
        if not bundle_info:
            logger.warning("bundle %s not found", bundle_code)
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code

        vendor: Vendors = Vendors.objects(vendor_name=vendor_name).first()
        if not vendor:
            logger.warning("vendor %s not found", vendor_name)
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code

        alternative_bundle: Bundles = self.get_alternative(
            exception,
            email,
            bundle_code,
            bundle_info.bundle_duration,
            bundle_info.data_amount,
            bundle_info.country_code_list,
            exclude_vendor_names
        )

        if not alternative_bundle:
            logger.warning("alternative bundle not found")
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code

        alternative_vendor: Vendors = Vendors.objects(vendor_name=alternative_bundle.vendor_name).first()
        if not alternative_vendor:
            logger.warning("alternative vendor not found")
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code

        if not alternative_vendor.supports_empty_profiles:
            # remove later on but for now this is how it will work trust me
            old_user_iccid = {"iccid": "", "qr_code_value": ""}
        else:
            new_profile, _ = get_sim_info(
                iccid="",
                vendor_name=alternative_bundle.vendor_name,
                profile_names=alternative_bundle.profile_names,
                bundle=alternative_bundle
            )
            no_new_profile = isinstance(new_profile, dict) or new_profile is False
            if no_new_profile:
                logger.warning("no new profile found for alternative vendor %s", alternative_bundle.vendor_name)
                return allocate, exception, plan_uid, iccid, user_iccid, bundle_code
            else:
                old_user_iccid = new_profile

        logger.info("found an alternative bundle: %s", alternative_bundle.bundle_code)
        return self.__call__(
            vendor_name=alternative_bundle.vendor_name,
            bundle_vendor_code=alternative_bundle.bundle_vendor_code,
            bundle_code=alternative_bundle.bundle_code,
            email=email,
            bundle_log=bundle_log,
            instance_config=instance_config,
            user_iccid=old_user_iccid,
            otp=otp,
            category=category,
            reseller_id=reseller_id,
            number_of_retry=number_of_retry,
            exclude_vendor_names=[
                *exclude_vendor_names,
                alternative_bundle.vendor_name,
            ],
        )


class AllocationBundle:
    def __init__(self):
        super().__init__()
        self.bundle_info = None
        self.user_iccid = None
        self.sim_info = None
        self.bundle_log = None
        self.is_topup = None


    def assign_bundle(
        self,
        bundle_code,
        email,
        instance_config,
        iccid="",
        reseller_id="",
        category=2,
        update_daily_used=True,
        assign_by_reserve=False,
    ):
        """
        start_bundle : True if expiry date is based on purchase date like Comium , if False , the expiry date will be changed after scanning
        expiry_days : is the expiry date of the bundle before scanning , 365 for Indosat, eSIMGo and Vodafone before scanning the profile and 90 days for Flexiroam
        expiry_date_profile :  If the vendor has "apply_expiry" as True so we will send the number of expiry days . If False will return zero which mean that profile can refill as much as subscriber can
        """
        result = {
            "status": False,
            "failure_reason": "",
            "iccid": "",
            "matching_id": "",
            "smdp_address": "",
            "activation_code": "",
            "vendor_start_bundle": False,
            "vendor_expiry_days": 365,
            "vendor_expiry_date_profile": 0,
        }

        bundle_info = self.get_bundle_object(bundle_code)
        if bundle_info and (int(bundle_info.allocated_unit) > int(bundle_info.consumed_unit)):
            vendor_name = bundle_info.vendor_name
            profile_name = bundle_info.profile_names
            # check available iccid (Free) to be assigned to a user
            error_message = "No Profile found related to bundle"
            self.is_topup = iccid != ""
            profile = None
            vendor_msisdn = ""
            if iccid == "":
                profile = check_inventory_availability(
                    bundle_info,
                    update_related_models=update_daily_used,
                    reseller_id=reseller_id,
                )
            if profile:
                from_inventory = True
                allocate = {"success": True}
                plan_uid = profile.plan_uid
                user_iccid = profile
                vendor_msisdn = profile.msisdn
            else:
                from_inventory = False
                assigned_iccid, error_message = get_sim_info(
                    iccid,
                    vendor_name,
                    profile_name,
                    bundle_info,
                    reseller_id,
                    instance_config,
                )

                allocate, error_message, plan_uid, iccid, user_iccid, bundle_code = allocate_per_vendor(
                    vendor_name=vendor_name,
                    bundle_vendor_code=bundle_info.bundle_vendor_code,
                    bundle_code=bundle_code,
                    email=email,
                    bundle_log=None,
                    instance_config=instance_config,
                    user_iccid=assigned_iccid,
                    otp="",
                    reseller_id=reseller_id,
                    category=category,
                )

                # If allocation failed, find the assigned profile and revert it to "Free" to make it available again.
                if not iccid and vendor_name in {MONTYMOBILE_VENDOR, ORANGE_VENDOR, BAYOBAB_VENDOR, "eSIMGoMock"} and assigned_iccid:
                    iccid_value = assigned_iccid.iccid
                    if iccid_value and not allocate["success"]:
                        assigned_profile = Profiles.objects(iccid=iccid_value, availability="Assigned").first()
                        if assigned_profile:
                            logger.warning("Allocation failed for profile %s, reverting to Free", iccid_value)
                            assigned_profile.update(set__availability="Free", set__reseller_id=None)

            if allocate["success"]:

                formatted_bundle = format_bundles(reseller_id=reseller_id, bundle_code=bundle_code)
                # on success increment consumed_unit
                self.update_bundle(
                    get_bundle_info(bundle_code=bundle_code, topup_code=""),
                    allocate,
                    user_iccid,
                    from_inventory=from_inventory,
                    assign_by_reserve=assign_by_reserve,
                )
                expiry_date_ = user_iccid["expiry_date"] if "expiry_date" in user_iccid else ""
                result = {
                    "status": True,
                    "iccid": user_iccid["iccid"],
                    "matching_id": user_iccid["matching_id"],
                    "smdp_address": user_iccid["smdp_address"],
                    "activation_code": user_iccid["qr_code_value"],
                    "plan_uid": plan_uid,
                    "vendor_msisdn": vendor_msisdn,
                    "bundle_info": formatted_bundle,
                    "expiry_date": expiry_date_,
                }

                vendor = Vendors.objects(vendor_name=vendor_name).first()
                # If apply_expiry =false so vendor_expiry_date_profile =0 which mean than the customer
                # can refill his profile as much as he would.
                vendor_expiry_date_profile = 0
                vendor_expiry_days = 0
                if vendor:
                    if vendor.apply_expiry:
                        vendor_expiry_date_profile = vendor.number_of_expiry_days

                    if vendor.vendor_start_bundle:
                        vendor_expiry_days = bundle_info.bundle_duration

                    vendor_start_bundle = vendor.vendor_start_bundle
                    result["vendor_start_bundle"] = vendor_start_bundle
                    result["vendor_expiry_days"] = vendor_expiry_days
                    result["vendor_expiry_date_profile"] = vendor_expiry_date_profile

                """else:
                result["failure_reason"] = exception
                result["bundle_info"] = format_bundles(
                    reseller_id=reseller_id, bundle_code=bundle_code)"""
            else:
                if error_message != "":
                    print("error message ", error_message)
                    result["failure_reason"] = error_message
                else:
                    result["failure_reason"] = "ICCID has not been assigned - Allocation Failed"
        else:
            print(f"Reseller Id: {reseller_id} and bundle Code: {bundle_code}")
            deactivate_bundle(bundle_code)
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(
                    datetime.datetime.utcnow(),
                    bundle_code,
                    FLEXIROAM_VENDOR,
                    "allocated_unit is Equal consumed_unit",
                ),
            )
            result["failure_reason"] = "Bundle not found - Get bundle failed"
            result["bundle_info"] = format_bundles(reseller_id=reseller_id, bundle_code=bundle_code)
        return result

    @staticmethod
    def get_bundle_object(bundle_code, deleted=False):
        """
        get bundle object that user want to buy
        """
        if not deleted:
            bundle_object = consumer_models.Bundles.objects(bundle_code=bundle_code).first()
        else:
            bundle_object = consumer_models.Bundles.objects(bundle_code=bundle_code, deleted=deleted).first()
        return bundle_object

    @try_except_func
    def update_bundle(
        self,
        bundle,
        allocate,
        assigned_iccid,
        from_inventory=False,
        assign_by_reserve=False,
    ):
        """
        After loading plan from the vendor, update allocated and consumed bundle units and check bundle status.
        """
        logger.info("Processing bundle consumed count update for: %s", bundle.bundle_code)

        if not allocate["success"]:
            logger.warning("Skipping consumed count update: unsuccessful purchase for bundle %s", bundle.bundle_code)
            return

        message = ""
        vendor = get_vendor_info(bundle.vendor_name)

        if bundle.vendor_name == MONTYMOBILE_VENDOR:
            count = check_available_iccid_count(bundle.vendor_name, assigned_iccid.profile_names)
        else:
            count = check_free_iccid_count(bundle.vendor_name, bundle.profile_names, bundle.bundle_code)

        if count == 0 and vendor and not vendor.apply_inventory:
            logger.warning("Deactivating bundle %s: No available profiles", bundle.bundle_code)
            message = f"Deactivate bundle (No available profiles): {bundle.bundle_code}"
            bundle.update(set__is_active=False)

        if not from_inventory:
            bundle.update(inc__allocated_unit=1)
            logger.info("Incremented allocated unit for bundle %s", bundle.bundle_code)

        if bundle.allocated_unit < int(bundle.consumed_unit) + 1:
            logger.warning("Deactivating bundle %s: Reached consumed count limit", bundle.bundle_code)
            message = (
                f"Deactivate bundle (Reached bundle inventory limit): {bundle.bundle_code}"
            )
            bundle.update(set__is_active=False)

        bundle.reload()
        if not bundle.is_active:
            logger.error("Bundle %s is now inactive. Sending alert email.", bundle.bundle_code)
            add_to_update_bundle_version([message])
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle.bundle_vendor_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(
                    datetime.datetime.utcnow(),
                    bundle.bundle_vendor_code,
                    bundle.vendor_name,
                    "No Available SIMs",
                ),
            )

        bundle.update(inc__consumed_unit=1)
        logger.info("Incremented consumed unit for bundle %s", bundle.bundle_code)

        if assign_by_reserve:
            if (
                bundle.vendor_name != FLEXIROAM_VENDOR
                or bundle.vendor_name != MONTYMOBILE_VENDOR
            ) and not from_inventory:
                bundle.update(dec__allocated_unit=1)
                logger.info("Decremented allocated unit due to reserve assignment for bundle %s", bundle.bundle_code)
            bundle.update(dec__consumed_unit=1)
            logger.info("Decremented consumed unit due to reserve assignment for bundle %s", bundle.bundle_code)

    def send_email(
        self,
        data_to_send,
        default_response,
        check_validity,
        settings,
        user,
        payment_log,
        image_file,
        otp_dict,
        instance,
    ):
        """
        Structure of otp_dict =>
        otp_dict = {'notification_id': 12, 'history_id': 12}
        """
        if data_to_send["topup_code"] != "":
            data = get_info(self.bundle_info, self.user_iccid.iccid, True)
            default_response["data"]["send_qr_code"] = True

            update_paid_user_bundle(check_validity)

            check_validity.payment_topup = True
            check_validity.save()
            new_data = {
                "bundle_code": data_to_send["bundle_code"],
                "topup_code": data_to_send["topup_code"],
            }
            send_fcm_notification(
                settings,
                user.fcm_token,
                data_to_send["ios_version"],
                new_data,
                data_to_send["status"],
            )

            default_response["message"] = "Topup added"
            default_response["title"] = "Success"
            data["qr_code_value"] = data["activation_code"]
            save_payment_log(payment_log)
            self.user_iccid.qr_code_path = image_file
            self.user_iccid.save()
            transaction_message = "Kindly note that you have bought {} ".format(data["bundle_name"])
            notf_id = "notf_" + str(otp_dict["notification_id"])
            notif_log = {
                "notification_id": notf_id,
                "email": data_to_send["email"],
                "transaction_message": transaction_message,
                "transaction": "BuyTopup",
                "reseller_type": "subscriber"
            }

            save_notification_logs(notif_log)
            history_log_id = "hist_" + str(otp_dict["history_id"])
            save_history_log(
                self.user_iccid,
                check_validity,
                data,
                check_validity.bundle_code,
                self.bundle_info,
                history_log_id,
                "BuyTopup",
                instance,
            )
            return default_response
        else:

            default_response["title"] = "Success"
            data = get_info(self.bundle_info, data_to_send["iccid"], True)
            self.user_iccid.qr_code_path = image_file

            default_response["title"] = "Success"
            default_response["data"]["send_qr_code"] = True
            default_response["message"] = "Bundle added and email sent"
            check_validity.payment_status = True

            check_validity.save()
            payment_log["bundle_status"] = True
            notf_id = "notf_" + str(otp_dict["notification_id"])
            transaction_message = "Kindly note that you have bought {} ".format(data["bundle_name"])
            notif_log = {
                "notification_id": notf_id,
                "email": data_to_send["email"],
                "transaction_message": transaction_message,
                "transaction": "BuyBundle",
                "reseller_type": "subscriber"
            }
            save_notification_logs(notif_log)
            history_log_id = "hist_" + str(otp_dict["history_id"])

            data["qr_code_value"] = data["activation_code"]
            save_history_log(
                self.user_iccid,
                check_validity,
                data,
                check_validity.bundle_code,
                self.bundle_info,
                history_log_id,
                "BuyBundle",
                instance,
            )
            save_user_bundle(check_validity)
            self.user_iccid.status = "used"
            self.user_iccid.save()
            status = "success"
            data = {
                "bundle_code": data_to_send["bundle_code"],
                "topup_code": data_to_send["topup_code"],
            }
            send_fcm_notification(settings, user.fcm_token, data_to_send["ios_version"], data, status)
            save_payment_log(payment_log)
            return default_response


# endregion
# region Bundles
def get_bundle_info(bundle_code, topup_code):
    if topup_code != "":
        bundle = get_bundle_by_code(topup_code)
    else:
        bundle = get_bundle_by_code(bundle_code)
    return bundle


def get_stage_preview_for_bundles(preview_for):
    stage_preview_for_bundles = {"$match": {"preview_for": preview_for}}
    return stage_preview_for_bundles


@try_except_func
def get_country_code_list_by_region(region_code):
    return consumer_models.Countries.objects(region_code=region_code).distinct("iso3_code")


def get_info(bundle_info, iccid, send_coverage):
    data = {
        "price": bundle_info.retail_price,
        "currency": bundle_info.currency_code,
        "bundle_name": bundle_info.bundle_marketing_name,
        "validity": bundle_info.bundle_duration,
    }
    if send_coverage:
        coverage = "global"
        if bundle_info.bundle_category == "region":
            coverage = bundle_info.region_name
        elif bundle_info.bundle_category == "country":
            coverage = bundle_info.country_list[0]
        data["coverage"] = coverage

        profile = get_profile_from_iccid(iccid)
        if profile:
            data["matching_id"] = profile.matching_id
            data["smdp_address"] = profile.smdp_address

            data["has_lpa"] = profile.has_lpa
            data["activation_code"] = profile.qr_code_value
    return data


@try_except_func
def get_bundle_by_code(bundle_code, not_deleted=True):
    if not_deleted:
        return consumer_models.Bundles.objects(bundle_code=bundle_code, deleted=False).first()
    else:
        return consumer_models.Bundles.objects(bundle_code=bundle_code).first()


def get_alternative_bundle(filter_kwargs):
    alternative_bundle = consumer_models.Bundles.objects(**filter_kwargs).order_by("retail_price").first()
    return alternative_bundle


@try_except_func
def get_related_topup_bundles(
    reseller_id,
    bundle_code,
    page_size=50,
    number_of_page=1,
    country_code=None,
    sort_by=None,
    bundle_name=None,
    sorting_order=1,
    is_topup=True,
    requested_bundle_code=None,
    bundle_tag=None,
    iccid=None,
    currency_code=None,
):
    result = {"status": True, "title": "Success", "message": "", "data": {}}

    bundle = get_bundle_by_code(bundle_code=bundle_code)
    if bundle and "reseller" in bundle.preview_for:
        search_country_code = None
        search_country_code = bundle.country_code_list
        profile_name_param = None
        bundle_category_param = bundle.bundle_category
        vendor_name_param = bundle.vendor_name
        region_code_param = bundle.region_code

        if bundle.vendor_name != ESIMGO_VENDOR:
            profile_name_param = bundle.profile_names
            if country_code:
                if country_code in bundle.country_code_list:
                    search_country_code = country_code
                else:
                    result["status"] = False
                    result["title"] = "Value Error"
                    result["message"] = "Country code entered does not match the bundle's country code"
                    return result
        else:
            if country_code and country_code not in bundle.country_code_list:
                result["status"] = False
                result["title"] = "Value Error"
                result["message"] = "Country code entered does not match the bundle's country code"
                return result
            search_country_code = bundle.country_code_list

        bundles = format_bundles(
            reseller_id,
            bundle_category=bundle_category_param,
            profile_name=profile_name_param,
            vendor_name=vendor_name_param,
            region_code=region_code_param,
            country_code=search_country_code,
            page_size=page_size,
            number_of_page=number_of_page,
            sort_by=sort_by,
            bundle_name=bundle_name,
            sorting_order=sorting_order,
            is_topup=is_topup,
            bundle_code=requested_bundle_code,
            bundle_tag=bundle_tag,
            iccid=iccid,
            currency_code=currency_code,
        )

        if bundles:
            result["message"] = "Related bundles"
            result["data"] = bundles
        elif bundles == ValueError:
            result["status"] = False
            result["title"] = "Value Error"
            result["message"] = "Bundle category value is incorrect [only global, region, or country]"
        else:
            result["status"] = False
            result["title"] = "No bundles"
            result["message"] = "No bundles found"
    else:
        result["status"] = False
        result["title"] = "No bundle"
        result["message"] = "Bundle not found"
    return result


def get_all_bundles_filtered(
    reseller_id="",
    page_size=50,
    skip=0,
    bundle_category=None,
    country_code=None,
    bundle_code=None,
    profile_name=None,
    vendor_name=None,
    region_code=None,
    bundle_name=None,
    sort_by="retail_price",
    sorting_order=1,
    reseller_admin_view=False,
    super_admin_view=False,
    is_topup=False,
    bundle_tag=None,
    iccid=None,
    currency_code=None,
    country_code_array=None,
):
    """
    Fetch bundles object
    options:
        - by bundle category (global, region, country)
        - by country code
        - by both
        - by none to get all bundles
        - by bundle_code
        - by profile_name
        - by profile_name and bundle_category
        - by region_code
    """
    lookup_pipeline = ""
    reseller_pipeline = ""
    contains_bundle_name = None
    query_set = Q()

    match_query = {
        "$match": {
            "$expr": {"$gt": ["$allocated_unit", "$consumed_unit"]},
            "$expr": {"$gt": ["$unit_price", 0]},
        }
    }

    if region_code:
        match_query["$match"]["$or"] = [
            {"region_code": {"$ne": ""}, "region_code": region_code},
            {
                "$and": [
                    {"country_code_list": {"$ne": None}},
                    {
                        "$expr": {
                            "$gt": [
                                {
                                    "$size": {
                                        "$setIntersection": [
                                            "$country_code_list",
                                            get_country_code_list_by_region(region_code),
                                        ]
                                    }
                                },
                                1,
                            ]
                        }
                    },
                ]
            },
        ]

    lookup_vendor = {
        "$lookup": {
            "from": "vendors",
            "localField": "vendor_name",
            "foreignField": "vendor_name",
            "as": "vendor_info",
        }
    }
    unwind_vendor = {"$unwind": "$vendor_info"}
    check_vendor_activation = {"$match": {"vendor_info.is_active": True}}
    if is_topup:
        check_vendor_activation = {"$match": {"vendor_info.is_active": True, "vendor_info.support_topup": True}}

    if reseller_id:
        lookup_pipeline = [
            {
                "$match": {
                    "$expr": {
                        "$and": [
                            {"$eq": ["$reseller_id", ObjectId(reseller_id)]},
                            {"$eq": [bundle_code_col, "$$rebundle_code"]},
                        ]
                    }
                }
            }
        ]

        reseller_pipeline = [{"$match": {"_id": ObjectId(reseller_id)}}]

        lookup_aggreation = {
            "$lookup": {
                "from": "reseller_bundle_price",
                "as": "result",
                "let": {"rebundle_code": bundle_code_col},
                "pipeline": lookup_pipeline,
            }
        }

        lookup_reseller = {
            "$lookup": {
                "from": "reseller",
                "as": "result_reseller",
                "pipeline": reseller_pipeline,
            }
        }

        unwind_retail_price = {"$unwind": {"path": "$result", "preserveNullAndEmptyArrays": True}}

        unwind_reseller = {"$unwind": {"path": "$result_reseller", "preserveNullAndEmptyArrays": True}}

        if sort_by:
            sort_bundles = {sort_by: sorting_order, "bundle_name": 1, "bundle_code": 1}
        else:
            sort_bundles = {
                "reseller_retail_price": 1,
                "bundle_name": 1,
                "bundle_code": 1,
            }

        match_reseller = {"$match": {}}
        if super_admin_view is not True:
            match_reseller["$match"]["is_active_corp"] = {"$ne": False}

            if reseller_admin_view is not True:
                match_reseller["$match"]["is_active"] = {"$ne": False}

        if bundle_tag:
            match_reseller["$match"]["bundle_tag"] = {
                "$regex": bundle_tag,
                "$options": "i",  # The "i" option makes the search case-insensitive
            }
        if bundle_name:
            match_reseller["$match"]["bundle_name"] = {
                "$regex": bundle_name,
                "$options": "i",  # The "i" option makes the search case-insensitive
            }

        pipeline = [
            get_stage_preview_for_bundles("reseller"),
            match_query,
            lookup_aggreation,
            unwind_retail_price,
            lookup_reseller,
            unwind_reseller,
            lookup_vendor,
            unwind_vendor,
            check_vendor_activation,
            {
                "$project": {
                    "custom_bundle_name": "$result.custom_bundle_name",
                    "bundle_name": 1,
                    "bundle_tag": "$result.bundle_tag",
                    "lowercase_bundle_name": {"$toLower": "$bundle_name"},
                    "bundle_marketing_name": 1,
                    "bundle_code": 1,
                    "region_name": 1,
                    "country_code_list": 1,
                    "country_list": 1,
                    "region_code": 1,
                    "reseller_retail_price": "$result.bundle_retail_price",
                    "retail_price": "$result.corp_retail_price",
                    "rate_revenue": "$result_reseller.rate_revenue",
                    "corp_rate_revenue": "$result_reseller.corp_rate_revenue",
                    "bundle_duration": 1,
                    "data_amount": 1,
                    "data_unit": 1,
                    "currency_code": 1,
                    "bundle_category": 1,
                    "unit_price": 1,
                    "is_active": "$result.is_active",
                    "is_active_corp": "$result.is_active_corp",
                    "unlimited": 1,
                    "refill_group": 1,
                    "support_topup": 1,
                    "supplier_vendor": 1,
                }
            },
            {
                "$addFields": {
                    "bundle_name": {
                        "$cond": {
                            "if": {"$ifNull": ["$custom_bundle_name", False]},
                            "then": "$custom_bundle_name",
                            "else": "$bundle_name",
                        }
                    }
                }
            },
            match_reseller,
            {
                "$addFields": {
                    "retail_price": {
                        "$function": {
                            "body": "function(retail_price, unit_price, corp_rate_revenue){ if (!retail_price){retail_price= (unit_price * (100 + corp_rate_revenue)) / 100;}return retail_price}",
                            "args": [
                                "$retail_price",
                                "$unit_price",
                                "$corp_rate_revenue",
                            ],
                            "lang": "js",
                        }
                    }
                }
            },
            {
                "$addFields": {
                    "reseller_retail_price": {
                        "$function": {
                            "body": "function(reseller_retail_price, reseller_unit_price, rate_revenue){ if (!reseller_retail_price){ reseller_retail_price= (reseller_unit_price * (100 + rate_revenue)) / 100; } return reseller_retail_price}",
                            "args": [
                                "$reseller_retail_price",
                                "$retail_price",
                                "$rate_revenue",
                            ],
                            "lang": "js",
                        }
                    }
                }
            },
            {"$sort": sort_bundles},
            {"$skip": skip},
            {"$limit": page_size},
        ]

        if currency_code:
            pipeline.extend(
                [
                    {"$addFields": {"additional_currency_code": currency_code}},
                    {
                        "$lookup": {
                            "from": "currency_codes",
                            "let": {"additional_currency_code": "$additional_currency_code"},
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {
                                            "$eq": [
                                                "$currency_code",
                                                "$$additional_currency_code",
                                            ]
                                        },
                                        "is_available": True,
                                    }
                                }
                            ],
                            "as": "currency",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$currency",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$addFields": {
                            "reseller_retail_price_in_additional_currency": {
                                "$cond": {
                                    "if": {"$ifNull": ["$currency", False]},
                                    "then": {
                                        "$multiply": [
                                            "$reseller_retail_price",
                                            "$currency.currency_rate",
                                        ]
                                    },
                                    "else": None,
                                }
                            }
                        }
                    },
                    {
                        "$addFields": {
                            "retail_price_in_additional_currency": {
                                "$cond": {
                                    "if": {"$ifNull": ["$currency", False]},
                                    "then": {
                                        "$multiply": [
                                            "$retail_price",
                                            "$currency.currency_rate",
                                        ]
                                    },
                                    "else": None,
                                }
                            }
                        }
                    },
                    {
                        "$addFields": {
                            "reseller_retail_price_in_additional_currency": {
                                "$round": [
                                    "$reseller_retail_price_in_additional_currency",
                                    2,
                                ]
                            },
                            "retail_price_in_additional_currency": {"$round": ["$retail_price_in_additional_currency", 2]},
                        }
                    },
                ]
            )

        total_bundle_count_pipeline = [
            get_stage_preview_for_bundles("reseller"),
            match_query,
            lookup_aggreation,
            unwind_retail_price,
            lookup_vendor,
            unwind_vendor,
            check_vendor_activation,
            {
                "$project": {
                    "bundle_code": 1,
                    "is_active": "$result.is_active",
                    "is_active_corp": "$result.is_active_corp",
                    "custom_bundle_name": "$result.custom_bundle_name",
                    "bundle_name": 1,
                    "bundle_tag": "$result.bundle_tag",
                }
            },
            {
                "$addFields": {
                    "bundle_name": {
                        "$cond": {
                            "if": {"$ifNull": ["$custom_bundle_name", False]},
                            "then": "$custom_bundle_name",
                            "else": "$bundle_name",
                        }
                    }
                }
            },
            match_reseller,
            {group_expr: {"_id": None, "count": {"$sum": 1}}},
        ]

        if iccid:
            profile = consumer_models.Profiles.objects(iccid=iccid, availability__ne="Expired").first()
            if profile:
                expiry_date = profile.expiry_date
                if not expiry_date:
                    max_bundle_duration = 90
                else:
                    max_bundle_duration = (expiry_date - datetime.datetime.utcnow()).days
                query_set = query_set & Q(bundle_duration__lte=max_bundle_duration)
            else:
                return []
    else:

        if sort_by == "retail_price":
            sort_bundles = {
                "retail_price": 1,
                "lowercase_bundle_name": 1,
                "bundle_code": 1,
            }
        elif not sort_by:
            sort_bundles = {"retail_price": 1, "bundle_code": 1}

        else:
            sort_bundles = {sort_by: 1, "retail_price": 1, "bundle_code": 1}
        pipeline = [
            get_stage_preview_for_bundles("subscriber"),
            lookup_vendor,
            unwind_vendor,
            check_vendor_activation,
            {
                "$addFields": {
                    "priority": {
                        "$function": {
                            "body": 'function(bundle_category) {switch (bundle_category){case ("global"): return 1;case ("region"): return 2;case ("country"): return 3;}return 1}',
                            "args": ["$bundle_category"],
                            "lang": "js",
                        }
                    }
                }
            },
            {"$addFields": {"lowercase_bundle_name": {"$toLower": "$bundle_name"}}},
            {"$sort": {"retail_price": 1, "priority": 1}},
            {
                "$group": {
                    "_id": {
                        "bundle_duration": "$bundle_duration",
                        "data_amount": "$data_amount",
                    },
                    "bundle_name": {"$first": "$bundle_name"},
                    "bundle_marketing_name": {"$first": "$bundle_marketing_name"},
                    "bundle_code": {"$first": "$bundle_code"},
                    "region_name": {"$first": "$region_name"},
                    "country_code_list": {"$first": "$country_code_list"},
                    "country_list": {"$first": "$country_list"},
                    "region_code": {"$first": "$region_code"},
                    "rate_revenue": {"$first": "$rate_revenue"},
                    "retail_price": {"$first": "$retail_price"},
                    "data_unit": {"$first": "$data_unit"},
                    "data_amount": {"$first": "$data_amount"},
                    "bundle_duration": {"$first": "$bundle_duration"},
                    "currency_code": {"$first": "$currency_code"},
                    "bundle_category": {"$first": "$bundle_category"},
                    "priority": {"$first": "$priority"},
                }
            },
            {"$sort": sort_bundles},
        ]
        total_bundle_count_pipeline = [
            get_stage_preview_for_bundles("subscriber"),
            lookup_vendor,
            unwind_vendor,
            check_vendor_activation,
            {group_expr: {"_id": None, "count": {"$sum": 1}}},
        ]

    new_pipeline = [
        {
            "$facet": {
                "bundles": pipeline,
                "total_bundle_count": total_bundle_count_pipeline,
            }
        }
    ]

    if bundle_category:
        bundle_category_options = ["global", "region", "country"]
        if bundle_category not in bundle_category_options:
            error = "Bundle category value is incorrect [only global, region, or country]"
            return ValueError(error)

        query_set = query_set & Q(bundle_category=bundle_category)

    if country_code:
        # get bundles of country
        if vendor_name == ESIMGO_VENDOR:
            query_set = query_set & Q(country_code_list=country_code)
        else:
            if type(country_code) == str:
                search_country_code = [country_code.upper()]
            else:
                search_country_code = country_code
            query_set = query_set & Q(country_code_list__in=search_country_code)

    # if bundle_name:
    #     query_set = query_set & Q(bundle_name__icontains=bundle_name)

    if country_code_array is not None:
        match_query["$match"] = {
            "country_code_list": {"$all": [country_code.upper() for country_code in country_code_array]},
        }
    if bundle_code:
        query_set = query_set & Q(bundle_code=bundle_code)

    if profile_name:
        query_set = query_set & Q(profile_names=profile_name)

    if vendor_name:
        query_set = query_set & Q(vendor_name=vendor_name)

    if is_topup:
        query_set = query_set & Q(deleted=False)

    else:

        query_set = query_set & Q(is_active=True, deleted=False)
    return consumer_models.Bundles.objects(query_set).aggregate(*new_pipeline)


@try_except_func
def format_bundles(
    reseller_id=None,
    bundle_category=None,
    country_code=None,
    page_size=50,
    number_of_page=1,
    bundle_code=None,
    profile_name=None,
    vendor_name=None,
    subscriber_country_code_format=False,
    region_code=None,
    bundle_name=None,
    sort_by=None,
    sorting_order=1,
    reseller_admin_view=False,
    super_admin_view=False,
    is_topup=False,
    requested_bundle_code=None,
    bundle_tag=None,
    iccid=None,
    currency_code=None,
    country_code_array=None,
):
    """
    format data retrieved to be viewed
    """
    country_code_list = []
    country_list = []

    formatted_bundles = []
    total_bundle_count = 0

    all_bundles = {
        "bundles": formatted_bundles,
        "total_bundles_count": total_bundle_count,
    }

    skip = (number_of_page - 1) * page_size
    bundles_object = get_all_bundles_filtered(
        reseller_id,
        page_size,
        skip,
        bundle_category,
        country_code,
        bundle_code,
        profile_name,
        vendor_name,
        region_code,
        bundle_name,
        sort_by,
        sorting_order,
        reseller_admin_view,
        super_admin_view,
        is_topup,
        bundle_tag,
        iccid,
        currency_code,
        country_code_array,
    )

    fetch_bundles = list(bundles_object)
    if fetch_bundles:

        for y in fetch_bundles:
            bundles = y["bundles"]
            total_bundle_count = y["total_bundle_count"]
            if total_bundle_count:
                total_bundle_count = total_bundle_count[0]["count"]

        if bundles:
            for bundle in bundles:
                # separate bundles into category according to category_name
                new_bundle = {
                    "bundle_code": bundle["bundle_code"],
                    "bundle_name": bundle["bundle_name"],
                    "bundle_marketing_name": bundle["bundle_marketing_name"],
                    "bundle_category": bundle["bundle_category"],
                    "country_code": country_code_list,
                    "country_name": country_list,
                    "region_code": bundle["region_code"],
                    "region_name": bundle["region_name"],
                    "currency_code_list": [bundle["currency_code"]],
                    "data_unit": bundle["data_unit"],
                    "gprs_limit": bundle["data_amount"],
                    "subscriber_price": bundle["retail_price"],
                    "validity": bundle["bundle_duration"],
                    "unlimited": bundle.get("unlimited", False),
                    "supplier_vendor": bundle.get("supplier_vendor", ""),
                    "refill_group": bundle.get("refill_group", ""),
                    "support_topup": bundle.get("support_topup", ""),
                }

                if reseller_id:
                    new_bundle["reseller_retail_price"] = bundle.get("reseller_retail_price", 0)
                    new_bundle["unit_price"] = bundle.get("unit_price", 0)
                    new_bundle["reseller_rate_revenue"] = bundle.get("rate_revenue", 0)
                    new_bundle["corp_rate_revenue"] = bundle.get("corp_rate_revenue", 0)
                    new_bundle["bundle_tag"] = bundle.get("bundle_tag", None)
                    if reseller_admin_view:
                        new_bundle["is_active"] = bundle.get("is_active", True)
                    if super_admin_view:
                        new_bundle["is_active_corp"] = bundle.get("is_active_corp", True)
                        new_bundle["is_active"] = bundle.get("is_active", True)
                    if currency_code:
                        new_bundle["additional_currency_code"] = bundle.get("additional_currency_code", None)
                        new_bundle["subscriber_price_in_additional_currency"] = bundle.get("retail_price_in_additional_currency", None)
                        new_bundle["reseller_retail_price_in_additional_currency"] = bundle.get(
                            "reseller_retail_price_in_additional_currency", None
                        )

                country_code_list = bundle["country_code_list"]
                country_list = bundle["country_list"]
                if "ISR" in country_code_list:
                    country_code_list.remove("ISR")
                new_bundle["country_code"] = country_code_list

                if "Israel" in country_list:
                    country_list.remove("Israel")
                new_bundle["country_name"] = country_list

                if bundle_code:
                    return new_bundle
                elif subscriber_country_code_format:
                    new_bundle["retail_price"] = bundle["retail_price"]
                    new_bundle.pop("subscriber_price")
                formatted_bundles.append(new_bundle)

            all_bundles = {
                "bundles": formatted_bundles,
                "total_bundles_count": total_bundle_count,
            }
    return all_bundles


# endregion
# region COUNTIRES/ REGIONS
def get_all_regions(
    reseller_id="",
    bundle_category=None,
    region_code=None,
    reseller_admin_view=False,
    super_admin_view=False,
):
    query_set = Q()

    lookup_pipeline = [
        {
            "$match": {
                "$expr": {
                    "$and": [
                        {"$eq": ["$reseller_id", ObjectId(reseller_id)]},
                        {"$eq": ["$bundle_code", "$$rebundle_code"]},
                    ]
                }
            }
        }
    ]

    lookup_aggreation = {
        "$lookup": {
            "from": "reseller_bundle_price",
            "as": "result",
            "let": {"rebundle_code": "$bundle_code"},
            "pipeline": lookup_pipeline,
        }
    }

    unwind_bundle_is_active = {"$unwind": {"path": "$result", "preserveNullAndEmptyArrays": True}}

    match_reseller = {"$match": {}}
    if super_admin_view is not True:
        match_reseller["$match"]["is_active_corp"] = {"$ne": False}

        if reseller_admin_view is not True:
            match_reseller["$match"]["is_active"] = {"$ne": False}

    pipeline = [
        get_stage_preview_for_bundles("reseller"),
        {"$match": {"bundle_category": {"$ne": "country"}}},
        lookup_aggreation,
        unwind_bundle_is_active,
        {
            "$project": {
                "is_active": "$result.is_active",
                "is_active_corp": "$result.is_active_corp",
                "bundle_name": "$result.custom_bundle_name",
                "bundle_tag": "$result.bundle_tag",
                "country_code_list": 1,
            }
        },
        match_reseller,
        {
            "$group": {
                "_id": None,
                "country_code_list": {"$addToSet": "$country_code_list"},
            }
        },
        {
            "$project": {
                "_id": 0,
                "country_code_list": {
                    "$reduce": {
                        "input": "$country_code_list",
                        "initialValue": [],
                        "in": {"$setUnion": ["$$value", "$$this"]},
                    }
                },
            }
        },
        {
            "$lookup": {
                "from": "countries",
                "localField": "country_code_list",
                "foreignField": "iso3_code",
                "as": "countries",
            }
        },
        {"$unwind": {"path": "$countries"}},
        {"$project": {"region_code": {"$first": "$countries.region_code"}}},
        {"$match": {"region_code": {"$ne": None}}},
        {"$group": {"_id": None, "regions": {"$addToSet": "$region_code"}}},
        {"$project": {"_id": 0, "regions": {"$setUnion": ["$regions", []]}}},
    ]

    query_set = query_set & Q(is_active=True, deleted=False)

    result = consumer_models.Bundles.objects(query_set).aggregate(*pipeline)

    return result


def get_all_countries(
    reseller_id="",
    bundle_category=None,
    region_code=None,
    reseller_admin_view=False,
    super_admin_view=False,
):
    query_set = Q()

    lookup_pipeline = [
        {
            "$match": {
                "$expr": {
                    "$and": [
                        {"$eq": ["$reseller_id", ObjectId(reseller_id)]},
                        {"$eq": ["$bundle_code", "$$rebundle_code"]},
                    ]
                }
            }
        }
    ]

    lookup_aggreation = {
        "$lookup": {
            "from": "reseller_bundle_price",
            "as": "result",
            "let": {"rebundle_code": "$bundle_code"},
            "pipeline": lookup_pipeline,
        }
    }

    unwind_bundle_is_active = {"$unwind": {"path": "$result", "preserveNullAndEmptyArrays": True}}

    match_reseller = {"$match": {}}
    if super_admin_view is not True:
        match_reseller["$match"]["is_active_corp"] = {"$ne": False}

        if reseller_admin_view is not True:
            match_reseller["$match"]["is_active"] = {"$ne": False}

    pipeline = [
        get_stage_preview_for_bundles("reseller"),
        lookup_aggreation,
        unwind_bundle_is_active,
        {
            "$project": {
                "is_active": "$result.is_active",
                "is_active_corp": "$result.is_active_corp",
                "bundle_name": "$result.custom_bundle_name",
                "bundle_tag": "$result.bundle_tag",
                "country_code_list": 1,
            }
        },
        match_reseller,
        {
            "$group": {
                "_id": None,
                "country_code_list": {"$addToSet": "$country_code_list"},
            }
        },
        {
            "$project": {
                "_id": 0,
                "country_code_list": {
                    "$reduce": {
                        "input": "$country_code_list",
                        "initialValue": [],
                        "in": {"$setUnion": ["$$value", "$$this"]},
                    }
                },
            }
        },
        {
            "$lookup": {
                "from": "countries",
                "localField": "country_code_list",
                "foreignField": "iso3_code",
                "as": "countries",
            }
        },
        {"$project": {"country_code_list": 0, "countries._id": 0}},
    ]

    query_set = query_set & Q(is_active=True, deleted=False)

    result = consumer_models.Bundles.objects(query_set).aggregate(*pipeline)
    return result


def get_all_regions(
    reseller_id="",
    bundle_category=None,
    region_code=None,
    reseller_admin_view=False,
    super_admin_view=False,
):
    query_set = Q()

    lookup_pipeline = [
        {
            "$match": {
                "$expr": {
                    "$and": [
                        {"$eq": ["$reseller_id", ObjectId(reseller_id)]},
                        {"$eq": ["$bundle_code", "$$rebundle_code"]},
                    ]
                }
            }
        }
    ]

    lookup_aggreation = {
        "$lookup": {
            "from": "reseller_bundle_price",
            "as": "result",
            "let": {"rebundle_code": "$bundle_code"},
            "pipeline": lookup_pipeline,
        }
    }

    unwind_bundle_is_active = {"$unwind": {"path": "$result", "preserveNullAndEmptyArrays": True}}

    match_reseller = {"$match": {}}
    if super_admin_view is not True:
        match_reseller["$match"]["is_active_corp"] = {"$ne": False}

        if reseller_admin_view is not True:
            match_reseller["$match"]["is_active"] = {"$ne": False}

    pipeline = [
        get_stage_preview_for_bundles("reseller"),
        {"$match": {"bundle_category": {"$ne": "country"}}},
        lookup_aggreation,
        unwind_bundle_is_active,
        {
            "$project": {
                "is_active": "$result.is_active",
                "is_active_corp": "$result.is_active_corp",
                "bundle_name": "$result.custom_bundle_name",
                "bundle_tag": "$result.bundle_tag",
                "country_code_list": 1,
            }
        },
        match_reseller,
        {
            "$group": {
                "_id": None,
                "country_code_list": {"$addToSet": "$country_code_list"},
            }
        },
        {
            "$project": {
                "_id": 0,
                "country_code_list": {
                    "$reduce": {
                        "input": "$country_code_list",
                        "initialValue": [],
                        "in": {"$setUnion": ["$$value", "$$this"]},
                    }
                },
            }
        },
        {
            "$lookup": {
                "from": "countries",
                "localField": "country_code_list",
                "foreignField": "iso3_code",
                "as": "countries",
            }
        },
        {"$unwind": {"path": "$countries"}},
        {"$project": {"region_code": {"$first": "$countries.region_code"}}},
        {"$match": {"region_code": {"$ne": None}}},
        {"$group": {"_id": None, "regions": {"$addToSet": "$region_code"}}},
        {"$project": {"_id": 0, "regions": {"$setUnion": ["$regions", []]}}},
    ]

    query_set = query_set & Q(is_active=True, deleted=False)

    result = consumer_models.Bundles.objects(query_set).aggregate(*pipeline)

    return result


def get_all_countries(
    reseller_id="",
    bundle_category=None,
    region_code=None,
    reseller_admin_view=False,
    super_admin_view=False,
):
    query_set = Q()

    lookup_pipeline = [
        {
            "$match": {
                "$expr": {
                    "$and": [
                        {"$eq": ["$reseller_id", ObjectId(reseller_id)]},
                        {"$eq": ["$bundle_code", "$$rebundle_code"]},
                    ]
                }
            }
        }
    ]

    lookup_aggreation = {
        "$lookup": {
            "from": "reseller_bundle_price",
            "as": "result",
            "let": {"rebundle_code": "$bundle_code"},
            "pipeline": lookup_pipeline,
        }
    }

    unwind_bundle_is_active = {"$unwind": {"path": "$result", "preserveNullAndEmptyArrays": True}}

    match_reseller = {"$match": {}}
    if super_admin_view is not True:
        match_reseller["$match"]["is_active_corp"] = {"$ne": False}

        if reseller_admin_view is not True:
            match_reseller["$match"]["is_active"] = {"$ne": False}

    pipeline = [
        get_stage_preview_for_bundles("reseller"),
        lookup_aggreation,
        unwind_bundle_is_active,
        {
            "$project": {
                "is_active": "$result.is_active",
                "is_active_corp": "$result.is_active_corp",
                "bundle_name": "$result.custom_bundle_name",
                "bundle_tag": "$result.bundle_tag",
                "country_code_list": 1,
            }
        },
        match_reseller,
        {
            "$group": {
                "_id": None,
                "country_code_list": {"$addToSet": "$country_code_list"},
            }
        },
        {
            "$project": {
                "_id": 0,
                "country_code_list": {
                    "$reduce": {
                        "input": "$country_code_list",
                        "initialValue": [],
                        "in": {"$setUnion": ["$$value", "$$this"]},
                    }
                },
            }
        },
        {
            "$lookup": {
                "from": "countries",
                "localField": "country_code_list",
                "foreignField": "iso3_code",
                "as": "countries",
            }
        },
        {"$project": {"country_code_list": 0, "countries._id": 0}},
    ]

    query_set = query_set & Q(is_active=True, deleted=False)

    result = consumer_models.Bundles.objects(query_set).aggregate(*pipeline)
    return result


# endregion
# region PROFILES
def get_profile_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid), availability__ne="Expired").first()
    if profile:
        return profile
    return None


@try_except_func
def get_profile_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid), status=True).first()
    if profile:
        return profile
    return None


def get_profile_expiry_date_for_vendor(vendor: consumer_models.Vendors):
    return vendor.number_of_expiry_days if vendor.number_of_expiry_days else 365


def check_profile_history(iccid):
    profile = get_profile_from_iccid(iccid)
    if not profile:
        return False

    vendor_name = profile.vendor_name
    if vendor_name == FLEXIROAM_VENDOR:
        flexiroam_helper = Flexiroam()
        res = flexiroam_helper.get_profiles(page=1, sku=profile.sku)
        logger.info("vendor_name =Flexiroam")
        for alert in res.get("data", ""):
            if alert.get("last_connection"):
                return True
        return False

    if vendor_name == MONTYMOBILE_VENDOR:
        return False

    if vendor_name == ESIMGO_VENDOR:
        return False

    if vendor_name == VODAFONE_VENDOR:
        vodafone_helper = Vodafone()
        res = vodafone_helper.get_profile_status(iccid=iccid)
        if res.get("codes", {}).get("rspState", "") != "RELEASED":
            return True
        return False

    if vendor_name == INDOSAT_VENDOR:
        return False


@try_except_func
def check_available_iccid(vendor_name, profile_names, reseller_id="", sku=None):
    query_set = (
        Q(vendor_name=vendor_name) & Q(availability="Free") & Q(status=True) & Q(plan_uid__in=[None, ""]) & Q(bundle_code__in=[None, ""])
    )
    if sku:
        query_set = query_set & Q(sku=sku)

    cleaned_profile_name = profile_names.replace(" ", "") if isinstance(profile_names, str) else ""
    if cleaned_profile_name:
        if len(cleaned_profile_name) > 1:
            query_set &= Q(profile_names=cleaned_profile_name)
        else:
            query_set &= Q(profile_names__contains=cleaned_profile_name)
    else:
        query_set &= Q(profile_names=None) | Q(profile_names="")

    # Check for profile expiry.
    if vendor_name == MONTYMOBILE_VENDOR:
        vendor: Vendors = Vendors.objects(vendor_name=MONTYMOBILE_VENDOR).first()

        expiry_days: int = vendor.number_of_expiry_days or 90
        query_set &= Q(expiry_date__gte=datetime.datetime.utcnow() + datetime.timedelta(days=expiry_days))

    find_iccid = consumer_models.Profiles.objects(query_set).first()
    if find_iccid:
        find_iccid.availability = "Assigned"
        find_iccid.reseller_id = reseller_id
        find_iccid.save()
        return find_iccid
    logger.warning("No profiles available for the vendor %s", vendor_name)
    return False


@try_except_func
def check_free_iccid_count(vendor_name, profile_names, bundle_code=None):
    query_set = check_iccid_from_profiles(vendor_name, profile_names)
    if bundle_code:
        query_set = (
            query_set
            & Q(bundle_code=bundle_code)
            & Q(plan_uid__ne=None)
            & Q(iccid__ne=None)
            & Q(expiry_date__gte=datetime.datetime.utcnow())
        )
    return consumer_models.Profiles.objects(query_set).count()


@try_except_func
def check_available_iccid_count(vendor_name, profile_names):
    return consumer_models.Profiles.objects(
        vendor_name=vendor_name,
        availability="Free",
        status=True,
        profile_names=profile_names,
    ).count()


def get_sim_info(iccid, vendor_name, profile_names, bundle, reseller_id="", instance_config=None):
    """
    gets sim info (profile) using iccid
    if iccid is not available => creates a profile if vendor is Flexiroam  Monty Mobile and Bayobab
    """
    sim_info = {"iccid": "", "qr_code_value": ""}
    error_message = ""

    if iccid != "":
        sim_info = get_profile_from_iccid(iccid)
    else:
        if vendor_name in [MONTYMOBILE_VENDOR, ORANGE_VENDOR, BAYOBAB_VENDOR]:
            sku = None
            sim_info = check_available_iccid(vendor_name, profile_names, reseller_id)

            if not sim_info:
                bundle.is_active = False
                bundle.save()
                add_to_update_bundle_version([f"Deactivate bundle: {bundle.bundle_code}"])

                error_message = "No Profile found related to bundle"
                return False, error_message

        elif vendor_name == "eSIMGoMock":
            sim_info = get_first_available_iccid(vendor_name)

    return sim_info, error_message


@try_except_func
def check_iccid_from_profiles(vendor_name, profile_names):
    query_set = Q(vendor_name=vendor_name) & Q(availability="Free") & Q(status=True)

    if vendor_name == FLEXIROAM_VENDOR or vendor_name == MONTYMOBILE_VENDOR:
        if len(profile_names) > 1:
            new_profile_name = profile_names.replace(" ", "")
            query_set = query_set & Q(profile_names=new_profile_name)

        elif len(profile_names) == 1:
            query_set = query_set & Q(profile_names__contains=profile_names)

        elif profile_names != "":
            query_set = query_set & Q(profile_names=profile_names)

        else:
            query_set = query_set & (Q(profile_names=None) or Q(profile_names=""))
    return query_set


@try_except_func
def get_first_available_iccid(vendor_name):
    query_set = check_iccid_from_profiles(vendor_name, "")

    print(f"[{datetime.datetime.utcnow()}] query_set: {query_set}")

    find_iccid = consumer_models.Profiles.objects(query_set).first()

    if find_iccid:
        find_iccid.availability = "Assigned"

        find_iccid.save()

        return find_iccid

    return False


@try_except_func
def save_profile(profile_info, reseller_id):
    iccid = profile_info.get("iccid")
    sku = profile_info.get("iccid")
    smdp_address = profile_info.get("smdpAddress")
    matching_id = profile_info.get("matchingId")

    qr_code_value = "LPA:1${}${}".format(smdp_address, matching_id)
    new_data = {
        "vendor_name": ESIMGO_VENDOR,
        "iccid": iccid,
        "sku": sku,
        "availability": "Assigned",
        "status": True,
        "smdp_address": smdp_address,
        "matching_id": matching_id,
        "has_lpa": True,
        "qr_code_value": qr_code_value,
        "reseller_id": reseller_id,
    }
    if consumer_models.Profiles(**new_data).save():
        return iccid, smdp_address, matching_id
    return False, False, False


@try_except_func
def get_sku_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid)).first()
    if profile:
        return profile.sku
    return None


# endregion
# region ALLOCATION/ ASSIGN/ BILLINGS/ PAYMENETS


def thread_activate_sim_status(iccid):
    indosat = Indosat()
    try:

        result = indosat.activate_sim_status(iccid=iccid)
        if not result:
            print("Exception: Could not set Activate Profile  for iccid", str(iccid))
            send_custom_support_email(
                "Indosat: Could not activate profile",
                f"Indosat: Could not not activate profile for iccid:{str(iccid)}",
            )
    except Exception as e:
        print("Exception: Could not set Activate Profile  for iccid", str(iccid))


def thread_topup_and_set_renewal(iccid):
    indosat = Indosat()
    try:

        set_renewal_mode_response = indosat.set_renewal_mode_to_named_plan(iccid=iccid)
        if not set_renewal_mode_response:
            print(
                "Exception: Could not set Renewal Mode to Named Plan for iccid",
                str(iccid),
            )
            send_custom_support_email(
                "Indosat: Could not set Renewal Mode to Named Plan",
                f"Indosat: Could not set Renewal Mode to Named Plan for iccid:{str(iccid)}",
            )

    except Exception as e:
        print(f"Exception: Could not set Renewal Mode to Named Plan for iccid {str(iccid)}")
        send_custom_support_email(
            "Indosat: Could not set Renewal Mode to Named Plan",
            f"Indosat Could not set Renewal Mode to Named Plan for iccid:{str(iccid)} with exception:{str(e)}",
        )


def create_profile(
    vendor_name,
    iccid,
    smdp_address,
    matching_id,
    qr_code_value,
    sku=None,
    profile_names=None,
    profile_exist=None,
):
    try:
        if sku is None:
            sku = iccid

        profile_data = {
            "vendor_name": vendor_name,
            "iccid": iccid,
            "sku": str(sku),
            "smdp_address": str(smdp_address),
            "matching_id": matching_id,
            "qr_code_value": qr_code_value,
            "payment_date": datetime.datetime.utcnow(),
            "availability": "Assigned",
            "reserved": True,
            "date_of_reservation": datetime.datetime.utcnow(),
        }

        if profile_names is not None:
            profile_data["profile_names"] = profile_names
            # Create and save the profile
        if profile_exist is not None:
            profile_exist.update(**profile_data)

        else:
            Profiles(**profile_data).save()
            print("Profile saved:", iccid)

        print("profile saved ", iccid)
    except Exception as e:
        print("exception ", e)


def get_profile_info_flexiroam_task(sku):
    try:
        flexiroam_helper = FlexiroamAPI()
        profile_info = flexiroam_helper.get_sim_details(sku)
        if not profile_info:
            print("Exception: Could not get Sim Details Profile  for sku", str(sku))
            send_custom_support_email(
                "Flexiroam: Could not get sim details",
                f"Flexiroam: Could not get sim details for sku:{str(sku)}",
            )
    except Exception as e:
        print("Exception:Flexiroam: Could not get sim details for sku:", str(sku))

    active_plans = profile_info.get("active_plans", [])
    plan_uid = active_plans[0].get("plan_uuid") if active_plans else ""
    return plan_uid, profile_info


def handle_flexi_order(order, bundle, flexiroam_helper, is_topup=False):
    if is_topup:
        profile = Profiles.objects(iccid=order["iccid"]).first()
        res = flexiroam_helper.buy_topup(bundle.bundle_vendor_code, int(profile.sku))
    else:
        res = flexiroam_helper.buy_plan(bundle.bundle_vendor_code, True)

    logging.info(f"Received result: {res}")
    order_type = "Top-up" if is_topup else "Buy Bundle"
    if isinstance(res, dict) and res.get("error"):
        error_message = res["error"]

        logging.error(f"{order_type} order %s failed", order["order_number"])

        send_custom_support_email(
            f": Could not {order_type} Flexiroam",
            f"Couldn't {order_type.lower()} {bundle.bundle_vendor_code} "
            f"for {'SKU ' + profile.sku if order else 'new profile'} for error message {error_message}",
        )

        logging.error(
            f"Order %s couldn't {order_type.lower()} with bundle %s for error message %s",
            order["iccid"],
            order["bundle_code"],
            error_message,
        )

        if not is_topup:
            bundle.update(set__is_active=False)

        return {
            "success": False,
            "plan_uid": "",
            "iccid": "",
            "smdp_address": "",
            "matching_id": "",
            "send_email": True,
        }, "Couldn't allocate"

    plan_uid, profile_info = get_profile_info_flexiroam_task(res.get("sku", ""))
    print("plan_uid, profile_info", plan_uid, profile_info)

    if is_topup and profile:
        matching_id = profile.matching_id
        iccid = profile.iccid
        smdp_address = profile.smdp_address
        qr_code_value = f"LPA:1${smdp_address}${matching_id}"

    else:
        profile_check = consumer_models.Profiles.objects(sku=str(res.get("sku"))).first()
        if profile_check and getattr(profile_check, "bundle_code", None):
            if profile_check.bundle_code.strip() != "":
                send_custom_support_email(
                    f": Could not {order_type} Flexiroam",
                    f"Profile already exists for SKU {res.get('sku')} and is already assigned to {profile_check.bundle_code}",
                )

                logging.error(f"Profile already exists for SKU {res.get('sku')} and is already assigned to {profile_check.bundle_code}")
                deactivate_bundle(bundle.bundle_code)
                return False, False, ""

        parts = res.get("esim_lpa").split("$")
        iccid = str(profile_info.get("details", {}).get("iccid", ""))
        qr_code_value = res.get("esim_lpa")
        smdp_address = parts[1]
        matching_id = parts[2]
        create_profile(
            bundle.vendor_name,
            iccid,
            smdp_address,
            matching_id,
            qr_code_value,
            res.get("sku"),
            bundle.profile_names,
            profile_check,
        )
    return {
        "success": True,
        "plan_uid": plan_uid,
        "sku": res.get("sku"),
        "iccid": iccid,
        "smdp_address": smdp_address,
        "profile_names": bundle.profile_names,
        "qr_code_value": qr_code_value,
        "matching_id": matching_id,
        "send_email": False,
    }, "Waiting reply"


@ResellerFallback
def allocate_per_vendor(
    vendor_name,
    bundle_vendor_code,
    bundle_code,
    email,
    bundle_log,
    instance_config,
    user_iccid=None,
    otp=None,
    category=2,
    reseller_id="",
):
    """
    Category:
        Reseller = 1
        Subscriber = 2
    allocate bundle for user based on vendor.
    """
    #  "start_bundle":False,
    #  "expiry_days":365,
    #  "expiry_date_profile":0
    allocate, exception, plan_uid, iccid = None, None, None, None

    if vendor_name == FLEXIROAM_VENDOR:
        if not user_iccid:
            allocate = {"success": False}
            exception = "No Profiles Available for this bundle"
            logging.error("No profiles available for bundle %s", bundle_code)
            return allocate, exception, plan_uid, iccid, user_iccid, bundle_code
        allocate, exception = allocate_flexi_bundle(user_iccid, str(bundle_vendor_code), instance_config)
        if allocate["success"]:
            iccid = user_iccid.iccid
            if allocate["data"] is not None and "plan_uid" in allocate["data"]:
                plan_uid = allocate["data"]["plan_uid"]
                sku = get_profile_from_iccid(iccid)
                plan_data = get_user_consumption(sku, None, instance_config, str(bundle_vendor_code))
                if plan_data:
                    expiry_date = plan_data["data"][0]["end_date"]
                else:
                    expiry_date = datetime.datetime.utcnow() + timedelta(days=90)
                user_iccid.expiry_date = expiry_date
                user_iccid.save()
        else:
            exception = allocate.get("message", "")

    elif vendor_name == FLEXIROAM_VENDOR_V2:
        flexiroam_helper = FlexiroamAPI()
        bundle = Bundles.objects(bundle_code=bundle_code).first()
        is_topup = False

        if user_iccid and user_iccid["iccid"] != "":
            is_topup = True

        if not is_topup:
            allocate, exception = handle_flexi_order(user_iccid, bundle, flexiroam_helper)

            if allocate["success"]:
                plan_uid = allocate["plan_uid"]
                expiry_date = datetime.datetime.utcnow() + timedelta(days=90)
                user_iccid = {
                    "iccid": allocate["iccid"],
                    "matching_id": allocate["matching_id"],
                    "smdp_address": allocate["smdp_address"],
                    "qr_code_value": allocate["qr_code_value"],
                    "expiry_date": expiry_date,
                }

            allocate["send_email"] = False

        else:
            allocate, exception = handle_flexi_order(user_iccid, bundle, flexiroam_helper, is_topup=True)

            if allocate["success"]:
                plan_uid = allocate["plan_uid"]
                expiry_date = datetime.datetime.utcnow() + timedelta(days=90)
                user_iccid = {
                    "iccid": allocate["iccid"],
                    "matching_id": allocate["matching_id"],
                    "smdp_address": allocate["smdp_address"],
                    "qr_code_value": allocate["qr_code_value"],
                    "expiry_date": expiry_date,
                }
            allocate["send_email"] = False

    elif vendor_name == VODAFONE_VENDOR:
        # is_topup = True if user_iccid['iccid'] != "" else False
        is_topup = False
        if user_iccid is not None and user_iccid["iccid"] != "":
            is_topup = True
        if is_topup == False:
            allocate, exception = allocate_vodafone_bundle(bundle_vendor_code, instance_config)
            print(" allocate, exception ", allocate, exception)
            if allocate["success"]:
                # bundle_log.order_number = allocate['order_reference']
                # bundle_log.save()
                plan_uid = allocate["order_reference"]
                user_iccid = {
                    "iccid": "",
                    "matching_id": "",
                    "smdp_address": "",
                    "qr_code_value": "",
                }

            allocate["send_email"] = False

        else:
            allocate, exception = topup_vodafone_bundle(
                bundle_vendor_code=bundle_vendor_code,
                user_iccid=user_iccid,
                instance_config=instance_config,
                reseller_id=reseller_id,
            )

            print("allocate, exception", allocate, exception)
            if allocate["success"]:
                plan_uid = allocate["order_reference"]
                user_iccid = {
                    "iccid": "",
                    "matching_id": "",
                    "smdp_address": "",
                    "qr_code_value": "",
                }
            allocate["send_email"] = False

    elif vendor_name == ESIMGO_VENDOR:
        if category == 2:
            bundle_log = get_bundle_log(otp)
            is_topup = True if bundle_log.topup_code != "" else False
        else:
            is_topup = True if user_iccid["iccid"] != "" else False
        if is_topup:
            allocate, exception = topup_esimgo_bundle(
                bundle_vendor_code=bundle_vendor_code,
                user_iccid=user_iccid,
                instance_config=instance_config,
            )
        else:
            allocate, exception = allocate_esimgo_bundle(bundle_vendor_code, instance_config, reseller_id)
            print("New eSIM will be assigned for bundle {}".format(bundle_vendor_code))
            print(f"<eSIMGo> allocation response: {allocate} exception: {exception}")

        if allocate["success"]:
            if category == 2:
                user_iccid = get_user_iccid(str(bundle_code), otp, email)
                if user_iccid:
                    user_iccid.iccid = allocate["iccid"]
                    user_iccid.activation_code = allocate["matching_id"]
                    user_iccid.expiry_date = datetime.utcnow() + timedelta(days=365)
                    user_iccid.save()
                    iccid = allocate["iccid"]
                    plan_uid = allocate["order_reference"]
                    user_iccid = get_user_iccid(str(bundle_code), otp, email)
                else:
                    exception = "User ICCID not found"
            iccid = allocate["iccid"]
            plan_uid = allocate["order_reference"]
            if category == 2:
                user_iccid = get_user_iccid(str(bundle_code), otp, email)
                if user_iccid is None:
                    exception = "User ICCID not found"
            else:
                user_iccid = get_profile_from_iccid(iccid)
                if user_iccid is None:
                    exception = "User Profile not found"

    elif vendor_name == "eSIMGoMock":

        if category == 1:
            order_reference = generate_temp_otp(7)
            iccid = user_iccid["iccid"]
            profile = get_profile_from_iccid(iccid)
            if profile is not None:
                # user_iccid.smdp_address = profile.smdp_address
                # user_iccid.matching_id = profile.matching_id
                #
                # user_iccid.save()

                user_iccid = profile
                if user_iccid is None:
                    exception = "User Profile not found"
                plan_uid = order_reference
                allocate = {
                    "success": True,
                    "order_reference": order_reference,
                    "iccid": profile.iccid,
                    "smdp_address": profile.smdp_address,
                    "matching_id": profile.matching_id,
                    "send_email": True,
                }
                exception = ""
            else:
                allocate = {
                    "success": False,
                    "order_reference": order_reference,
                    "iccid": user_iccid,
                    "smdp_address": "",
                    "matching_id": "",
                    "send_email": False,
                }
                exception = "Couldn't allocate"

    elif vendor_name == MONTYMOBILE_VENDOR:
        resp, exception = allocate_montymobile_bundle(profile=user_iccid, plan_code=bundle_vendor_code)
        if resp.get("success", False):
            iccid = user_iccid.iccid
            if resp.get("data") is not None and "plan_uid" in resp["data"]["simplan"]:
                plan_uid = resp["data"]["simplan"]["plan_uid"]
            allocate = {"success": True}
        else:
            allocate = {"success": False}

    elif vendor_name == BAYOBAB_VENDOR:
        if user_iccid:
            bayobab = Bayobab()
            resp = bayobab.attach_offer(user_iccid.iccid, str(bundle_vendor_code))
            if resp and not resp.get("errorCode"):
                allocate = {"success":True}
                iccid = user_iccid.iccid
                plan_uid = resp.get("content", [{}])[0].get("id")

            else:
                allocate = {"success": False}
                exception = resp.get("errorMessage")
        else:
            allocate = {"success": False}
            exception = f"No profiles available to load {bundle_vendor_code}"

    elif vendor_name == INDOSAT_VENDOR:

        if user_iccid is not None and user_iccid["iccid"] != "":

            indosat = Indosat()
            iccid = user_iccid["iccid"]
            allocate_response = False
            indosat_profile = indosat.get_profile_detailed(iccid=iccid)

            if indosat_profile and indosat_profile.get("status") == "DEACTIVATED":
                allocate_response = indosat.change_base_rate_plan(iccid=iccid, rate_plan=bundle_vendor_code)
                thread1 = threading.Thread(target=thread_activate_sim_status, args=(iccid,))
                thread1.start()

            else:
                allocate_response = indosat.topup_iccid(iccid=iccid, rate_plan=bundle_vendor_code)
                thread2 = threading.Thread(target=thread_topup_and_set_renewal, args=(iccid,))
                thread2.start()

            if allocate_response:
                plan_uid = str(uuid4())
                allocate = {"success": True}

            else:
                exception = "Couldn't Topup Profile"

        else:
            exception = "No Profiles Available for this bundle"
            allocate = {"success": False}

    elif vendor_name == ORANGE_VENDOR:
        if user_iccid is not None and user_iccid["iccid"] != "":
            orange_helper = Orange()
            iccid = user_iccid["iccid"]

            bundle: Bundles = Bundles.objects(bundle_vendor_code=bundle_vendor_code, vendor_name=vendor_name).first()
            data_amount = bundle.data_amount
            trigger_data_amount = bundle.data_amount
            data_unit = bundle.data_unit

            if data_unit == "MB":
                #   if bundle data unit is MB, the trigger data amount should be converted to GB
                trigger_data_amount /= 1024

            if data_unit == "GB":
                #   on the other hand, if data unit is GB
                #   data amount (to be topped up) should be converted to MB
                data_amount *= 1024

            end_date = (datetime.datetime.utcnow() + timedelta(days=bundle.bundle_duration)).strftime("%Y-%m-%d")

            order = Order_history.objects(iccid=iccid, order_status="Successful").first()
            if order:
                order_end_date = order.expiry_date
                if order_end_date:
                    end_date = (order.expiry_date + timedelta(days=bundle.bundle_duration)).strftime("%Y-%m-%d")

            total_data_amount, _, total_bundle_duration, _ = accumulate_vendor_data(iccid)
            trigger_data_amount += total_data_amount

            data_amount = str(data_amount)
            trigger_data_amount = str(trigger_data_amount)

            allocate_response = orange_helper.topup_iccid(
                iccid=iccid,
                data_amount=data_amount,
                zone_name=bundle.zone_name,
                emails=[instance_config.outlook_subscription_email],
                trigger_data_amount=trigger_data_amount,
                end_date=end_date,
                subscription_id=user_iccid["subscription_id"],
            )

            if allocate_response:
                logging.info("topped up orange iccid %s successfully", iccid)
                plan_uid = str(uuid4())
                allocate = {"success": True}
            else:
                logging.error("couldn't topup profile %s problem from vendor side", iccid)
                exception = "Couldn't Topup Profile, Problem from vendor side"

        else:
            exception = "No Profiles Available for this bundle"
            allocate = {"success": False}

    elif vendor_name == TELKOMSEL_VENDOR:
        if user_iccid:
            telkomsel_vendor = TelkomselVendor()
            profile = get_profile_from_iccid(iccid)
            resp, msg = telkomsel_vendor.update_profile_status(profile.sku, "IN-BILLING")
            if resp:
                allocate = {"success": True}
                iccid = user_iccid.iccid
                plan_uid = resp.get["data"]["attributes"]["uuid"]
            else:
                allocate = {"success": False}
                exception = f"Failed to assign offer to iccid{user_iccid.iccid} with offer {bundle_vendor_code} and response is {resp}",
        else:
            allocate = {"success": False}
            exception = f"No profiles available to load {bundle_vendor_code}"

    return allocate, exception, plan_uid, iccid, user_iccid, bundle_code


@try_except_func
def update_paid_user_bundle(bundle_doc):
    query_set = (
        Q(country_code=bundle_doc.country_code) & Q(email=bundle_doc.email) & Q(bundle_code=bundle_doc.bundle_code) & Q(payment_status=True)
    )
    user_bundle = consumer_models.UserBundle.objects(query_set).first()
    if user_bundle:
        user_bundle.amount = round(float(user_bundle.amount) + float(bundle_doc.amount), 3)
        new_date = user_bundle.validy_date + timedelta(bundle_doc.validity_days)
        user_bundle.validy_date = new_date
        user_bundle.topup_code.append(bundle_doc.topup_code)
        user_bundle.save()
        return True
    return False


@try_except_func
def save_payment_log(new_doc):
    res = consumer_models.TransactionLogs.objects(order_number=new_doc["order_number"]).first()
    if not res:
        return consumer_models.TransactionLogs(**new_doc).save()
    return True


# endregion
# region CONSUMPTION
def get_all_topups_balance(iccid, instance_config):
    try:
        url = f"{instance_config.vodafone_url}/network/top-up/history/iccids/{iccid}/90"
        headers = {"Authorization": "Bearer " + instance_config.vodafone_token}
        response = requests.request("GET", url, headers=headers)
        cumulative_topup_balance = 0
        if response.status_code == 200:
            for bundle in response.json():
                if bundle.get("topUp"):
                    cumulative_topup_balance += int(bundle["topUp"]["value"])
                elif bundle.get("initial"):
                    cumulative_topup_balance += int(bundle["initial"]["value"])

        return round((float(cumulative_topup_balance)), 2)
    except Exception as e:
        print("User cannot view his total bundles", e)
        return -1


##ToDo add other vendors
@try_except_func
def get_user_consumption(sku, bundle_info, instance_config, bundle_vendor_code=""):
    bundles = {}
    if sku.vendor_name == "eSIMGo":

        url = "{}/v2.3/esims/{}/bundles?{}".format(instance_config.esimgo_url, sku.iccid, sku.iccid)
        print(url)
        headers = {"X-API-Key": instance_config.esim_go_token}
        response = requests.request("GET", url, headers=headers)
        if response.status_code == 200:
            bundles = {
                "data": [
                    {
                        "data_allocated": 0,
                        "data_used": 0,
                        "data_remaining": 0,
                    }
                ]
            }
            bundles_json = response.json()["bundles"]
            consumption_json = None
            for i in range(0, len(bundles_json)):
                if bundles_json[i]["name"] == bundle_info.bundle_vendor_code:
                    consumption_json = bundles_json[i]["assignments"][0]
                    print("consumption_json: ", consumption_json)
                    break
            if consumption_json is None:
                return {}
            data_allocated = consumption_json["initialQuantity"]
            remaining_quantity = consumption_json["remainingQuantity"]
            end_time = consumption_json["endTime"] if "endTime" in consumption_json else None
            data_allocated /= 1000 * 1000
            remaining_quantity /= 1000 * 1000
            data_used = data_allocated - remaining_quantity
            bundles["data"][0]["data_allocated"] = data_allocated
            bundles["data"][0]["data_remaining"] = remaining_quantity
            bundles["data"][0]["data_used"] = data_used
            bundles["data"][0]["end_date"] = end_time
        return bundles

    if sku.vendor_name == VODAFONE_VENDOR:
        url = "{}/network/things/iccids/{}/bundle".format(instance_config.vodafone_url, sku.iccid)
        headers = {"Authorization": "Bearer " + instance_config.vodafone_token}
        response = requests.request("GET", url, headers=headers)

        if response.status_code == 200:
            bundles = {
                "data": [
                    {
                        "data_allocated": 0,
                        "data_used": 0,
                    }
                ]
            }
            bundles_json = response.json()["bundle"]
            for json_obj in bundles_json:
                # Extracting each field for the current object
                bundle_type = json_obj["bundleType"]
                bundle_balance = json_obj["bundleBalance"]
                bundle_expiry = json_obj["bundleExpiry"]
                is_bundle_depleted = json_obj["isBundleDepleted"]
                is_bundle_expired = json_obj["isBundleExpired"]

            if bundles_json is None:
                return {}
            end_date = bundle_expiry if "bundleExpiry" in json_obj else None
            data_used = round((float(bundle_balance)), 2)
            bundles["data"][0]["data_used"] = data_used
            bundles["data"][0]["end_date"] = end_date
        return bundles

    elif sku.vendor_name == MONTYMOBILE_VENDOR:
        vendor_montymobile = get_vendor_info(vendor_name=sku.vendor_name)
        monty_mobile = MontyMobile()
        json_response = monty_mobile.get_profile_consumption(str(sku))
        if not json_response:
            print("An error occured while get_profile_consumption for MontyMobile")

        bundles = {
            "data": [
                {
                    "data_allocated": 0,
                    "data_used": 0,
                    "data_remaining": 0,
                }
            ]
        }
        bundles_json_data = json_response.json()["data"]
        consumption_json = None
        if bundle_vendor_code == "":
            bundle_vendor_code = bundle_info.bundle_vendor_code
        for i in range(0, len(bundles_json_data)):

            if bundles_json_data[i]["plan_code"] == bundle_vendor_code:
                consumption_json = bundles_json_data[i]
                data_allocated = consumption_json["data_allocated"]
                remaining_quantity = consumption_json["data_balance"]
                data_used = consumption_json["data_used"]
                end_date = consumption_json["end_date"]
                bundles["data"][0]["data_allocated"] = data_allocated
                bundles["data"][0]["data_remaining"] = remaining_quantity
                bundles["data"][0]["data_used"] = data_used
                bundles["data"][0]["end_date"] = end_date
                break
        if consumption_json is None:
            return {}
    return bundles


def accumulate_vendor_data_draft_for_flexiroam(iccid):
    try:
        if profile := consumer_models.Profiles.objects(iccid=iccid).first():
            accumulation_pipeline = [
                {"$match": {"iccid": iccid, "order_status": "Successful"}},
                {
                    "$lookup": {
                        "from": "bundles",
                        "localField": "bundle_code",
                        "foreignField": "bundle_code",
                        "as": "bundles",
                    }
                },
                {"$unwind": "$bundles"},
                {
                    "$addFields": {
                        "bundles.data_amount": {
                            "$cond": [
                                {"$eq": ["$bundles.data_unit", "MB"]},
                                {"$divide": ["$bundles.data_amount", 1000]},
                                "$bundles.data_amount",
                            ]
                        }
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_data_amount": {"$sum": "$bundles.data_amount"},
                        "total_amount_paid": {"$sum": "$bundle_retail_price"},
                        "total_bundle_duration": {"$sum": "$bundles.bundle_duration"},
                        "total_samples": {
                            "$sum": 1,
                        },
                    }
                },
                {
                    "$addFields": {
                        "total_data_amount": {
                            "$round": ["$total_data_amount", 2],
                        },
                        "total_amount_paid": {
                            "$round": ["$total_amount_paid", 2],
                        },
                    },
                },
            ]
            user_iccid_otp_object_list = reseller_models.Order_history.objects(iccid=iccid).aggregate(*accumulation_pipeline)
            accumulated_data = list(user_iccid_otp_object_list)
            if accumulated_data:  # Check if accumulated_data is not empty
                accumulated_data = accumulated_data[0]  # Access the first element of the list
                return (
                    accumulated_data["total_data_amount"],
                    accumulated_data["total_amount_paid"],
                    accumulated_data["total_bundle_duration"],
                    accumulated_data["total_samples"],
                )
            else:
                return 0, 0, 0, 0
    except Exception as e:
        print(f"<accumulate_vendor_data> error while accumulating data {e}")
        return 0, 0, 0, 0


def accumulate_vendor_data(iccid):
    try:
        if profile := consumer_models.Profiles.objects(iccid=iccid).first():
            accumulation_pipeline = [
                {"$match": {"iccid": iccid, "order_status": "Successful"}},
                {
                    "$lookup": {
                        "from": "bundles",
                        "localField": "bundle_code",
                        "foreignField": "bundle_code",
                        "as": "bundles",
                    }
                },
                {"$unwind": "$bundles"},
                {
                    "$addFields": {
                        "bundles.data_amount": {
                            "$cond": [
                                {"$eq": ["$bundles.data_unit", "MB"]},
                                {"$divide": ["$bundles.data_amount", 1000]},
                                "$bundles.data_amount",
                            ]
                        }
                    }
                },
                {"$sort": {"date_created": 1}},
                {
                    "$group": {
                        "_id": None,
                        "total_data_amount": {"$sum": "$bundles.data_amount"},
                        "total_amount_paid": {
                            "$sum": {
                                "$cond": {
                                    "if": {"reseller_type": "subscriber"},
                                    "then": {
                                        "$add": [
                                            "$paid_amount_credit_card",
                                            "$paid_amount_wallet",
                                        ]
                                    },
                                    "else": "$bundle_retail_price",
                                }
                            }
                        },
                        "total_bundle_duration": {"$sum": "$bundles.bundle_duration"},
                        "queued_bundles": {
                            "$push": {
                                "bundle_vendor_code": "$bundles.bundle_vendor_code",
                                "data_amount": "$bundles.data_amount",
                            }
                        },
                    }
                },
                {
                    "$addFields": {
                        "total_data_amount": {
                            "$round": ["$total_data_amount", 2],
                        },
                        "total_amount_paid": {
                            "$round": ["$total_amount_paid", 2],
                        },
                    },
                },
            ]
            user_iccid_otp_object_list = reseller_models.Order_history.objects(iccid=iccid).aggregate(*accumulation_pipeline)
            accumulated_data = list(user_iccid_otp_object_list)
            if accumulated_data:  # Check if accumulated_data is not empty
                accumulated_data = accumulated_data[0]  # Access the first element of the list
                return (
                    accumulated_data["total_data_amount"],
                    accumulated_data["total_amount_paid"],
                    accumulated_data["total_bundle_duration"],
                    accumulated_data["queued_bundles"],
                )
            else:
                return 0, 0, 0, []
    except Exception as e:
        print(f"<accumulate_vendor_data> error while accumulating data {e}")
        return 0, 0, 0


@try_except_func
def get_vendor_user_consumption(profile: consumer_models.Profiles, instance_config, unlimited):
    data = {"data_allocated": "0", "data_used": "0", "data_unit": "MB"}

    profile_status = ""
    iccid = str(profile.iccid)
    data_allocated = 0
    data_used = 0
    try:
        timeout = int(getattr(instance_config, "http_connection_timeout", 10))
        vendor_name = profile.vendor_name
        sku = str(profile.sku)
        vendor_msisdn = str(profile.msisdn)
        remaining_quantity: float = 0
        if vendor_name == FLEXIROAM_VENDOR:
            if access_token := get_vendor_info(vendor_name).temp_token:
                headers = {"token": access_token}
                payload = {"sku": sku}
                url = f"{instance_config.flexiroam_url}/plan/simplan/v1"
                flexiroam_consumption_response = requests.post(url, headers=headers, data=payload, timeout=timeout)
                print(
                    "flexiroam_consumption_response.status_code",
                    flexiroam_consumption_response.status_code,
                    flexiroam_consumption_response.text,
                )
                if flexiroam_consumption_response.status_code != 200:
                    result, exception = get_token_flexiroam(
                        instance_config.flexiroam_url, instance_config.flexiroam_username, instance_config.flexiroam_password
                    )
                    if result:
                        access_token = result["data"]["token"]
                        vendor = get_vendor_info(vendor_name)
                        vendor.update(set__temp_token=access_token)
                        headers = {"token": access_token}
                        payload = {"sku": sku}
                        url = f"{instance_config.flexiroam_url}/plan/simplan/v1"
                        flexiroam_consumption_response = requests.post(url, headers=headers, data=payload)
                        print("Re-Login: flexiroam_consumption_response.status_code", flexiroam_consumption_response.status_code)
                plan_status = True
                not_started_count = 0

                if flexiroam_consumption_response.status_code == 200:
                    bundles = flexiroam_consumption_response.json()["data"]
                    for bundle in bundles:
                        if bundle.get("plan_status") != "Plan Not Started":
                            plan_status = False
                            print("plan_status is false")
                        else:
                            not_started_count += 1
                        data_allocated += float(bundle.get("data_allocated"))
                        remaining_quantity += float(bundle.get("data_balance"))
                    total_data_amount, total_amount_paid, total_bundle_duration, total_count = accumulate_vendor_data_draft_for_flexiroam(
                        iccid
                    )
                    print("total_data_amount, total_cont", total_data_amount, total_count)
                    if total_data_amount:
                        data_allocated = total_data_amount * 1000
                        if plan_status and total_count == not_started_count:
                            print("in if plan_status and total_count == not_started_count ")
                            remaining_quantity = data_allocated
                    data_used = data_allocated - remaining_quantity
                    profile_status = get_profile_status_for_each_vendor(vendor_name=FLEXIROAM_VENDOR, sku=profile.sku, iccid=profile.iccid)
                else:
                    exception_message = flexiroam_consumption_response.json()
                    raise ValueError(
                        f"Exception while fetching Consumption for Flexiroam iccid: {iccid}, exception:-{exception_message}")

        elif vendor_name == FLEXIROAM_VENDOR_V2:
            flexiroam_vendor = FlexiroamAPI()
            plan_status = True
            not_started_count = 0
            total_data_amount, _, _, queued_bundles = accumulate_vendor_data(iccid)
            profile_status = get_profile_status_for_each_vendor(vendor_name=FLEXIROAM_VENDOR_V2,
                                                                sku=profile.sku,
                                                                iccid=profile.iccid)
            res = flexiroam_vendor.get_sim_details(sku)

            if not res:
                raise ValueError(f"Exception while fetching Consumption for Flexiroam iccid: {iccid}")

            if not res.get("active_plans"):
                raise ValueError(f"No active plans are present for the iccid: {iccid}")
            bundles = res["active_plans"]
            for bundle in bundles:
                if not bundle.get("activated_on"):
                    plan_status = False
                else:
                    not_started_count += 1

                remaining_quantity += float(bundle.get("balance"))

            data_allocated = total_data_amount * 1000
            if plan_status and queued_bundles == not_started_count:
                remaining_quantity = data_allocated
            data_used = data_allocated - remaining_quantity

        elif vendor_name == ESIMGO_VENDOR:
            url = f"{instance_config.esimgo_url}/v2.3/esims/{iccid}/bundles?includeUsed=true"
            headers = {"X-API-Key": instance_config.esim_go_token}
            esimgo_consumption_response = requests.request("GET", url, headers=headers, timeout=timeout)
            if esimgo_consumption_response.status_code == 200:
                bundles = esimgo_consumption_response.json()["bundles"]
                for bundle in bundles:
                    for data in bundle["assignments"]:
                        data_allocated += data["initialQuantity"]
                        remaining_quantity += data["remainingQuantity"]
                if not bundles:
                    raise ValueError(f"No Bundles in Get Esimgo Consumption Returned for iccid {iccid}")
                data_allocated /= 1000 * 1000
                remaining_quantity /= 1000 * 1000
                data_used = data_allocated - remaining_quantity
                profile_status = get_profile_status_for_each_vendor(vendor_name=ESIMGO_VENDOR, sku=profile.sku, iccid=profile.iccid)
            else:
                exception_message = esimgo_consumption_response.json()
                raise ValueError(f"Exception while fetching Consumption for Esimgo iccid: {iccid}, exception:-{exception_message}")

        elif vendor_name == VODAFONE_VENDOR:
            url = f"{instance_config.vodafone_url}/network/things/iccids/{iccid}/bundle"
            headers = {"Authorization": f"Bearer {instance_config.vodafone_token}"}
            vodafone_consumption_response = requests.request("GET", url, headers=headers, timeout=timeout)
            data_allocated = get_all_topups_balance(iccid, instance_config)
            if vodafone_consumption_response.status_code == 200 and data_allocated != -1:
                bundles = vodafone_consumption_response.json().get("bundle")
                for bundle in bundles:
                    remaining_quantity += float(bundle.get("bundleBalance"))
                if data_allocated == 0:
                    data_allocated = remaining_quantity

                data_used = data_allocated - remaining_quantity
                profile_status = get_profile_status_for_each_vendor(vendor_name=VODAFONE_VENDOR, sku=profile.sku, iccid=profile.iccid)

            else:
                exception_message = vodafone_consumption_response.json()
                raise ValueError(
                    f"Exception while fetching Consumption for Vodafone iccid: {iccid}, exception:-{exception_message}")

        elif vendor_name == MONTYMOBILE_VENDOR:
            vendor_montymobile = get_vendor_info(vendor_name=vendor_name)
            monty_mobile = MontyMobile()
            json_response = monty_mobile.get_profile_consumption(str(sku))
            if not json_response:
                raise ValueError(
                    f"An error occured while get_profile_consumption for Monty Mobile Monty Mobile iccid: {iccid}")
            if json_response.get("success"):
                bundles = json_response.get("data", {}).get("plans", [])
                for bundle in bundles:
                    data_allocated += float(bundle.get("data_allocated")) / 1024
                    remaining_quantity += float(bundle.get("data_balance")) / 1024

                data_used = data_allocated - remaining_quantity
                profile_status = get_profile_status_for_each_vendor(vendor_name=MONTYMOBILE_VENDOR, sku=profile.sku, iccid=profile.iccid)

            else:
                exception_message = json_response
                raise ValueError(
                    f"Exception while fetching Consumption for Monty Mobile iccid: {iccid}, exception:-{exception_message}")

        elif vendor_name == BAYOBAB_VENDOR:
            bayobab = Bayobab()
            order_history: Order_history = Order_history.objects(iccid=iccid, order_status="Successful")

            # Fetch current data consumption details from Bayobab for the ICCID
            # For Bayobab, plans which are not started the consumption, they will return balance as 0.
            response = bayobab.fetch_consumption(iccid)

            if not response:
                raise ValueError(f"Error occurred while fetching consumption for the profile: {iccid}")

            if response.get("errorCode"):
                raise ValueError(f"Error while fetching consumption for profile {iccid}, error: {response.get('errorMessage')}")

            for order in order_history:
                bundle = Bundles.objects(vendor_name=BAYOBAB_VENDOR, bundle_code=order.bundle_code).first()
                if not bundle:
                    continue

                allocated_data_mb = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
                data_allocated += allocated_data_mb

                # Fully consumed if plan is expired
                if order.plan_status == "Expired":
                    data_used += allocated_data_mb
                    continue

                plan_balance_mb = 0

                # Search for corresponding plan UID in the consumption response
                for plan in response.get("content", []):
                    if order.plan_uid == plan.get("id"):
                        for balance in plan.get("balance", []):
                            if balance.get("service") == "DATA":
                                plan_balance_mb = balance.get("value", 0)
                                break  # Found balance, no need to continue

                if plan_balance_mb:
                    data_used += allocated_data_mb - plan_balance_mb
                elif order.plan_started:
                    # Assume full consumption if started but no balance
                    data_used += allocated_data_mb
            profile_status = get_profile_status_for_each_vendor(vendor_name=BAYOBAB_VENDOR, iccid=iccid, sku=iccid)

        elif vendor_name == INDOSAT_VENDOR:

            indosat = Indosat()
            consumptions = indosat.get_terminal_rating(iccid)

            (total_data_amount, total_amount_paid, total_bundle_duration, queued_bundles) = accumulate_vendor_data(iccid)

            if consumptions:

                consumptions_length = len(consumptions)
                queued_bundles_length = len(queued_bundles)
                missing_data = []

                if consumptions_length < queued_bundles_length:
                    expired_plans = queued_bundles[0 : queued_bundles_length - consumptions_length]

                    missing_data = [{"ratePlanName": plan["bundle_vendor_code"], "dataRemaining": 0} for plan in expired_plans]

                    consumptions = missing_data + consumptions

                total_data_remaining = 0
                for plan in consumptions:
                    data_remaining = plan.get("dataRemaining", None)

                    if data_remaining:
                        data_remaining = float(data_remaining)

                    else:
                        # check ctd usage and overage limit reached and status
                        current_consumption = indosat.get_device_consumption(iccid=iccid)
                        current_status = current_consumption.get("status")
                        current_overage_limit_reached = current_consumption.get("overageLimitReached")
                        if current_status == "DEACTIVATED" or current_overage_limit_reached:
                            data_remaining = 0
                            data_used = total_data_amount * 1024
                            continue

                        bundle = consumer_models.Bundles.objects(bundle_vendor_code=plan["ratePlanName"], vendor_name=vendor_name).first()

                        if bundle:
                            data_remaining = bundle.data_amount * 1024

                    total_data_remaining += float(data_remaining)

                # rate_plan = consumption.get('ratePlan', "")
                # bundle = consumer_models.Bundles.objects(bundle_vendor_code=rate_plan).first()
                # data_allocated = bundle.data_amount
                # data_unit = bundle.data_unit
                # if data_unit == "GB":
                #     data_allocated = round(float(data_allocated * 1024),2)
                #
                # to be checked if needed
                # if total_data_amount:
                #     data_allocated = total_data_amount * 1000
                # TODO REMOVE EXPIRED BUNDLES AS CONSUMPTION GETS LATEST ONLY

                data_allocated = total_data_amount * 1024
                if total_data_remaining > 0:
                    data_used = data_allocated - total_data_remaining

                profile_status = get_profile_status_for_each_vendor(vendor_name=INDOSAT_VENDOR, sku=sku, iccid=iccid)
            else:
                raise ValueError(f"No consumptions in Indosat for iccid {iccid}")

        elif vendor_name == ORANGE_VENDOR:
            orange_helper = Orange()
            json_response = orange_helper.get_profile_consumption(str(vendor_msisdn))
            if json_response:
                bundles = json_response["buckets"]
                for bundle in bundles:
                    for data in bundle["bucketBalances"]:
                        data_allocated += float(data["initialValue"])
                        remaining_quantity += float(data["remainingValue"])
                if not bundles:
                    raise ValueError(f"o Bundles in Get Orange Consumption Returned for iccid {iccid}")
                data_used = data_allocated - remaining_quantity
                profile_dto = orange_helper.get_profiles(fetch_suspended=False, iccid=iccid)
                profile_status = profile_dto["connectivityDirectory"]["sim"]["status"]

            else:
                exception_message = json_response
                raise ValueError(
                    f"Exception while fetching Consumption for Orange iccid: {iccid}, exception:-{exception_message}")

        elif vendor_name == TELKOMSEL_VENDOR:
            telkomsel_vendor = TelkomselVendor()
            order_history: mongoengine.QuerySet = Order_history.objects(iccid=iccid, order_status="Successful")

            # Fetch current data consumption details from Telkomsel for the ICCID
            response, msg = telkomsel_vendor.get_profile_history(profile.msisdn)

            if not response:
                raise ValueError(f"Error occurred while fetching consumption for the profile: {iccid}")

            allocated_data_mb = 0
            for order in order_history:
                bundle = Bundles.objects(vendor_name=TELKOMSEL_VENDOR, bundle_code=order.bundle_code).first()
                if not bundle:
                    continue

                allocated_data_mb = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
                data_allocated += allocated_data_mb

                # Fully consumed if plan is expired
                if order.plan_status == "Expired":
                    data_used += allocated_data_mb
                    continue

            # Get Usage From Response
            plan_balance_mb = response["data"]["attributes"]["ctdDataUsage"]

            if plan_balance_mb:
                data_used += allocated_data_mb - plan_balance_mb
            else:
                # Assume full consumption if started but no balance
                data_used += allocated_data_mb

        margin = 0.05  # 5% margin
        if (data_allocated <= 0 or data_allocated * (1 + margin) < data_used) and not unlimited:
            raise ValueError("Invalid data allocation: data_allocated <= 0 or exceeded allowed usage with margin.")

        if data_used > data_allocated:  # To show user that he used all his data not more.
            data_used = data_allocated

        data = {
            "data_unit": "MB",
            "data_allocated": round(data_allocated, 2),
            "data_used": round(data_used, 2),
            "profile_status": profile_status,
        }
        # Cache the profile consumption record for future use if there is no response from the vendor on subsequent
        # requests.
        cache_consumption_data(profile, data)
        return data
    except Exception as e:
        logger.error("Exception while loading fetching consumption from vendor side [%s] for iccid %s", e, iccid)
        logger.info("Checking to get consumption from cache data for iccid %s", iccid)
        if profile.consumption_cache:
            cache = profile.consumption_cache
            logger.info(f"Using cached data for iccid {iccid}")
            data = {
                "data_unit": "MB",
                "data_allocated": float(cache.data_allocated),
                "data_used": float(cache.data_used),
                "profile_status": profile.profile_status,
                "cached_at": cache.cached_at,
            }
        return data


def get_consumption_reseller(bundle_code, iccid, instance):
    from b2c_helpers.webhook_helpers import WebhookHelper
    from b2c_helpers.errors import AlreadyReceived

    result = {
        "status": False,
        "failed_reason": "",
        "data_allocated": "",
        "data_used": "",
        "data_remaining": "",
        "data_unit": "",
        "policy_status": "",
        "plan_status": "",
        "end_date": "",
        "profile_status": "",
    }
    if not bundle_code or not iccid:
        result["failed_reason"] = "Missing input values (bundle_code, ICCID)"
        logging.error("Missing input values (bundle_code, ICCID)")
        return result

    profile = get_profile_from_iccid(iccid)
    if not profile:
        logging.error("Profile with iccid %s not found", iccid)
        result["failed_reason"] = "Profile not found (ICCID)"
        return result

    bundle_info = AllocationBundle.get_bundle_object(bundle_code=bundle_code, deleted=False)

    if not bundle_info:
        result["failed_reason"] = "Bundle not found (BUNDLE CODE)"
        return result

    unlimited = bundle_info.unlimited
    user_consumption = get_vendor_user_consumption(profile, instance, unlimited)
    logging.info("user_consumption response %s ", user_consumption)
    if not user_consumption:
        modify_plan_status = check_profile_history(iccid=iccid)
        if not modify_plan_status:
            result["failed_reason"] = "No data consumption"

        Order_history.objects(iccid=iccid, order_status="Successful", reseller_type="reseller").update(
            plan_status="Expired")
        logging.info("order history updated to plan status = Expired, user consumption from vendor side has an issue")
        total_data_amount, _, _, _ = accumulate_vendor_data(iccid)
        data_allocated = total_data_amount * 1000
        data_used = data_allocated
        result["status"] = True
        result["data_allocated"] = data_allocated
        result["data_remaining"] = 0
        result["data_used"] = data_used
        result["data_unit"] = "MB"
        result["policy_status"] = ""
        result["plan_status"] = "Expired"
        result["profile_status"] = ""
        result["unlimited"] = unlimited
        return result

    if bundle_info.vendor_name != VODAFONE_VENDOR:
        consumption = user_consumption
        data_allocated = float(consumption.get("data_allocated", 0))
        data_used = float(consumption.get("data_used", 0))
        data_remaining = data_allocated - data_used
        result["status"] = True
        result["data_allocated"] = data_allocated
        result["data_remaining"] = data_remaining
        result["data_used"] = data_used
        result["data_unit"] = "MB"
        result["policy_status"] = consumption.get("policy_status", "")
        result["plan_status"] = consumption.get("plan_status", "")
        result["profile_status"] = consumption.get("profile_status", "")
        result["unlimited"] = unlimited

    else:
        consumption = user_consumption
        data_allocated = float(consumption.get("data_allocated", 0))
        data_used = float(consumption.get("data_used", 0))
        data_remaining = data_allocated - data_used
        result["status"] = True
        result["data_allocated"] = data_allocated
        result["data_remaining"] = data_remaining
        result["data_used"] = data_used
        result["data_unit"] = "MB"
        result["profile_status"] = consumption.get("profile_status", "")
        result["unlimited"] = unlimited

    if not result.get("end_date"):
        profile_expiry_date = datetime.datetime.utcnow()
        if profile := consumer_models.Profiles.objects(iccid=iccid).first():
            vendor = consumer_models.Vendors.objects(vendor_name=profile.vendor_name).first()
            months_till_profile_exp = get_profile_expiry_date_for_vendor(vendor=vendor)
            profile_expiry_date = profile.create_datetime + timedelta(days=months_till_profile_exp)
        result["end_date"] = profile_expiry_date

    usage_percent = (
        round((result["data_used"] / result["data_allocated"]) * 100)
        if result["data_allocated"] != 0
        else 0
    )

    # Add cached at if present
    if consumption.get("cached_at", None):
        result["cached_at"] = consumption.get("cached_at")

    if usage_percent == 0:
        return result

    if usage_percent in range(1, 70):
        #   if the user is still below 70% usage, send the start notification
        usage_percent = 1
    elif usage_percent in range(70, 100):
        usage_percent = 80
    else:
        usage_percent = 100

    class MiniNotificationHelperClass(WebhookHelper):
        NOTIFICATIONS_TO_SAVE = [1, 80, 100]

        def check_if_user_should_be_notified(self, per_plan_notifications: bool = True):
            return super().check_if_user_should_be_notified(per_plan_notifications=False)

        def limit_notification(self, payload=None) -> None:

            self.order_history: Order_history = Order_history.objects(
                iccid=iccid, order_status="Successful", plan_status__ne="Expired", reseller_type="reseller"
            ).first()
            if not self.order_history:
                logging.info("Couldn't find non-expired successful order for iccid %s", iccid)
                return

            if usage_percent == 1:
                logging.info("%s started consumption", iccid)
                self.usage_percent = 1

            elif usage_percent == 80:
                logging.info("%s reached 80%% of total data", iccid)
                self.usage_percent = 80

            elif usage_percent == 100:
                logging.info("%s reached 100%% of total data", iccid)
                self.usage_percent = 100

            else:
                logging.info("%s reached %d%% of total data", iccid, usage_percent)
                return

            try:
                self.check_reseller_order_to_notify_user()

            except AlreadyReceived as ar:
                logging.warning("AlreadyReceived caught: %s", str(ar))

            except Exception as e:
                logging.error("Error while creating custom notification for iccid %s: %s", iccid, str(e))

    MiniNotificationHelperClass(instance_config=instance, only_for_reseller=True).limit_notification()
    return result


@try_except_func
def join_subscriber_into_reseller(reseller_id, page_size=10, skip=0, getQuery={}):
    if not isinstance(page_size, int) or page_size < 1:
        page_size = 100000000
    if not isinstance(skip, int) or skip < 0:
        skip = 0

    pipeline = [
        {
            "$match": {
                "reseller_type": "subscriber",
            }
        },
        {"$match": getQuery},
        {
            "$project": {
                "order_id": "$_id",
                "date_created": 1,
                "bundle_code": "$bundle_data.bundle_code",
                "bundle_name": "$bundle_data.bundle_name",
                "bundle_category": "$bundle_data.bundle_category",
                "bundle_marketing_name": "$bundle_data.bundle_marketing_name",
                "bundle_price": "$bundle_data.unit_price",
                "bundle_retail_price": "$bundle_data.retail_price",
                "country_code": "$bundle_data.country_code_list",
                "country_name": "$bundle_data.country_list",
                "client_email": 1,
                "iccid": 1,
                "activation_code": 1,
                "plan_uid": 1,
                "order_status": 1,
                "remaining_wallet_balance": "0",
                "client_name": "",
                "payment_otp": "$order_number",
            }
        },
        {
            "$facet": {
                "total_orders_count": [{"$count": "count"}],
                "orders": [{"$skip": skip}, {"$limit": page_size}],
            }
        },
    ]
    res = reseller_models.Order_history.objects()
    return res.aggregate(*pipeline)


def get_profile_status_for_each_vendor(vendor_name, sku, iccid):
    profile_status = ""
    installation_date = ""
    profile_object = consumer_models.Profiles.objects(iccid=iccid).first()

    if vendor_name == FLEXIROAM_VENDOR:
        res = Flexiroam().get_profiles(page=1, sku=sku)
        logger.info("get_profile_status_for_each_vendor for vendor_name =Flexiroam")
        for alert in res.get("data", ""):
            # if alert["installation_status"]["status"] == "Delete" and alert["installation_status"]["date"]:
            #     profile_status = "Released"
            if not alert.get("installation_status"):
                order_history = reseller_models.Order_history.objects(
                    Q(iccid=iccid) & (Q(plan_status="Active") | Q(plan_status="Expired"))
                ).first()
                if order_history:
                    profile_status = "Installed"
                else:
                    profile_status = "Released"
            else:
                profile_status = alert["installation_status"]["status"]
                installation_date = alert["installation_status"]["date"]
            if installation_date:
                profile_object.update(profile_status=profile_status, installation_date=installation_date)

    elif vendor_name == FLEXIROAM_VENDOR_V2:
        order_history = reseller_models.Order_history.objects(Q(iccid=iccid) & (Q(plan_status="Active") | Q(plan_status="Expired"))).first()
        if order_history:
            profile_status = "Installed"
        else:
            profile_status = "Released"

    elif vendor_name == VODAFONE_VENDOR:
        res = Vodafone().get_profile_status(iccid=iccid)
        profile_status = res.get("rspState", "")
        # if profile_status == "Deleted":
        #     profile_status = "Released"

    elif vendor_name == ESIMGO_VENDOR:
        res = ESIMGo().get_esim_details_and_status(iccid=iccid)
        profile_status = res.get("profileStatus", "")
        first_installation_date = res.get("firstInstalledDateTime", "")
        if first_installation_date:
            timestamp_seconds = first_installation_date / 1000
            dt_object = datetime.datetime.utcfromtimestamp(timestamp_seconds)
            installation_date = dt_object.strftime("%Y-%m-%d %H:%M %Z")
            profile_object.update(profile_status=profile_status, installation_date=installation_date)

    elif vendor_name == MONTYMOBILE_VENDOR:
        res = MontyMobile().get_profile_status(iccid=iccid)
        logger.info("get_profile_status_for_each_vendor for vendor_name=MontyMobile")
        if res["success"]:
            profile_status = res["install_status"]
        else:
            failed_reason = res["failed_reason"]
            print(
                "failure_reason for profile status in Monty Mobile Vendor",
                failed_reason,
            )

    elif vendor_name == BAYOBAB_VENDOR:
        logger.info("Fetching profile installation status for vendor: Bayobab, ICCID: %s", iccid)
        bayobab = Bayobab()
        res = bayobab.get_profile_install_status(iccid)

        if not res:
            logger.error("Bayobab failed to retrieve installation status for ICCID: %s", iccid)
        else:
            header_status = res.get("header", {}).get("functionExecutionStatus", {})

            if header_status.get("status") == "Executed-Success":
                profile_status = res.get("state")
                logger.info("Profile installation status fetched successfully for ICCID: %s, Status: %s", iccid, profile_status)
            else:
                logger.error("Bayobab installation status retrieval failed for ICCID: %s, Header: %s", iccid, header_status)

    elif vendor_name == INDOSAT_VENDOR:
        res = Indosat().get_profile_detailed(iccid)
        logger.info("get_profile_status_for_each_vendor for vendor_name = Indosat")
        profile_status = res.get("status", "")
        if profile_status == "TEST_READY" or profile_status == "ACTIVATION_READY":
            profile_status = "Released"
        elif profile_status == "ACTIVATED":
            profile_status = "Installed"
        installation_date = res.get("dateActivated", "")
        if installation_date:
            profile_object.update(profile_status=profile_status, installation_date=installation_date)
    return profile_status


# endregion
# region INVENTORY
def prepare_runnable_scripts_accepted(script, vendor_name, from_date=None, to_date=None, bundle_code="", daily_used=0, from_time=None, to_time=None, order_number=""):
    try:
        doc = {
            "script": script,
            "datetime": datetime.datetime.utcnow(),
            "vendor_name": vendor_name,
            "state": "Accepted",
            "informer": "From API",
        }
        if from_date is not None and to_date is not None:

            if from_time is None:
                from_time = "00:00:00"

            if to_time is None:
                to_time = "00:00:00"

            # Combine date and time for from_date and to_date
            from_datetime_str = f"{from_date} {from_time}"
            to_datetime_str = f"{to_date} {to_time}"

            # Convert to datetime objects
            doc["from_date"] = datetime.datetime.strptime(from_datetime_str, "%Y-%m-%d %H:%M:%S")
            doc["to_date"] = datetime.datetime.strptime(to_datetime_str, "%Y-%m-%d %H:%M:%S")

        if bundle_code != "":
            doc["bundle_code"] = bundle_code

        if order_number != "":
            doc["order_number"] = order_number

        if daily_used > 0:
            doc["daily_used"] = daily_used
        return main_models.RunnableScripts(**doc).save()
    except Exception as e:
        logger.exception(f"Exception in prepare_runnable_scripts as: {e}")
        return False


def check_inventory_availability(
    bundle_info: Bundles,
    is_topup: bool = False,
    update_related_models: bool = False,
    reseller_id: str = "",
    add_script=True,
):
    """
    update_related_models: will be True only if we are in allocation process after assigning step
    """
    settings = get_setting()

    #   safety flag, function is useless in case of topup
    if is_topup:
        return False

    #   get vendor with inventory enabled
    vendor: Vendors = Vendors.objects(vendor_name=bundle_info.vendor_name, apply_inventory=True).first()
    if not vendor:
        return False

    #   process profile_names as sometimes it might contain space characters
    profile_names: str = getattr(bundle_info, "profile_names", "").replace(" ", "")

    inventory_profiles = Profiles.objects(
        bundle_code=bundle_info.bundle_code,
        profile_names=profile_names,
        availability="Free",
        plan_uid__ne=None,
        iccid__ne=None,
        status=True,
        expiry_date__gte=datetime.datetime.utcnow(),
    )

    #   ratio set from settings * number of profiles bought today for this bundle
    number_of_profiles_according_to_daily_used: int = round(bundle_info.daily_used * settings.percentage_of_inventory)

    #   actual number of profiles sent to script to add
    number_of_profiles_to_add: int = max(number_of_profiles_according_to_daily_used, settings.limit_bundle)

    #   get first profile from available inventory profiles
    profile_from_inventory: Profiles = inventory_profiles.first()

    if not profile_from_inventory:
        logging.info(
            "0 inventory profiles left for bundle %s vendor %s",
            bundle_info.bundle_code,
            bundle_info.vendor_name,
        )
        #   add cron job which allocates <number_of_profiles_to_add> profiles
        prepare_runnable_scripts_accepted(
            script="AllocateProfiles",
            vendor_name=bundle_info.vendor_name,
            bundle_code=bundle_info.bundle_code,
            daily_used=number_of_profiles_to_add,
        )
        return False

    profile_from_inventory.update(availability="Assigned")
    profile_from_inventory.reload()

    #   check if any other user has already bought this profile
    if Order_history.objects(iccid=profile_from_inventory.iccid, order_status="Successful"):
        return check_inventory_availability(bundle_info, is_topup, update_related_models, reseller_id)

    #   check count of remaining profiles and add profile saving script if it's less than our threshold
    count_remaining_profiles = inventory_profiles.filter(id__ne=profile_from_inventory.id).count()
    low_profile_count = count_remaining_profiles <= number_of_profiles_to_add

    if low_profile_count:
        logging.info("<we are less than or equal limit_bundle> schedule_cron_job_that_will_go_and_save_new_profiles")
        prepare_runnable_scripts_accepted(
            script="AllocateProfiles",
            vendor_name=bundle_info.vendor_name,
            bundle_code=bundle_info.bundle_code,
            daily_used=number_of_profiles_to_add,
        )

    if update_related_models:
        # set profile expiry date based on vendor
        profile_from_inventory.update(expiry_date=vendor.expiry_date)
        profile_from_inventory.reload()
        bundle_info.update(inc__daily_used=1)
        if reseller_id:
            profile_from_inventory.update(reseller_id=reseller_id)
            profile_from_inventory.reload()
    return profile_from_inventory


@try_except_func
def get_user_iccid(bundle_code, otp=None, email=None, status="unused"):
    query_set = Q(bundle_code=bundle_code) & Q(status=status)
    if email is not None:
        query_set = query_set & Q(email=email)
    if otp is not None:
        query_set = query_set & Q(payment_otp=otp)

    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def get_bundle_log(otp):
    return consumer_models.UserBundleLog.objects(otp=otp).first()


@try_except_func
def save_user_bundle(bundle_doc):
    new_doc = {
        "email": bundle_doc.email,
        "otp": bundle_doc.otp,
        "country_code": bundle_doc.country_code,
        "payment_status": True,
        "bundle_code": bundle_doc.bundle_code,
        "data_code": [],
        "currency_code": bundle_doc.currency_code,
        "amount": bundle_doc.amount,
        "payment_date": bundle_doc.payment_date,
        "validy_date": bundle_doc.validy_date,
        "qr_code_pin": bundle_doc.qr_code_pin,
    }
    return consumer_models.UserBundle(**new_doc).save()


@try_except_func
def get_bundle_log(otp):
    return consumer_models.UserBundleLog.objects(otp=otp).first()


@try_except_func
def save_history_logs(log):
    return main_models.HistoryLogs(**log).save()


def save_history_log(
    user_iccid,
    check_validity,
    profile,
    bundle_code,
    bundle_info,
    history_log_id,
    notif_category,
    instance,
    history_sent="",
):
    coverage = "global"
    if bundle_info.bundle_category == "region":
        coverage = bundle_info.region_name
    elif bundle_info.bundle_category == "country":
        coverage = bundle_info.country_list[0]
    bundle_log = get_bundle_log(check_validity.otp)
    qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
        instance.decrypted_wp_qr_code,
        profile["matching_id"],
        profile["smdp_address"],
        profile["has_lpa"],
    )
    history_log = {
        "history_log_id": history_log_id,
        "email": user_iccid.email,
        "iccid": user_iccid.iccid,
        "bundle_name": bundle_info.bundle_name,
        "bundle_marketing_name": bundle_info.bundle_marketing_name,
        "coverage": coverage,
        "price": bundle_info.retail_price,
        "currency_code": bundle_info.currency_code,
        "bundle_code": bundle_code,
        "data_amount": bundle_info.data_amount,
        "data_unit": bundle_info.data_unit,
        "bundle_duration": bundle_info.bundle_duration,
        "order_number": bundle_log.order_number,
        "smdp_address": profile["smdp_address"],
        "activation_code": profile["qr_code_value"],
        "transaction": notif_category,
        "qr_code_link": qr_code_link,
    }
    if history_sent != "":
        history_log["sent_using"] = "history_sent"
    if check_validity.topup_code != "":
        history_log["topup_code"] = bundle_info.bundle_code
        history_log["transaction"] = "BuyTopup"
    save_history_logs(history_log)


# endregion
# region NOTIFICATIONS
def get_notification_message(message_category, status, message_to_send):
    if message_category == "buy_bundle":
        if status == "success":
            message = "Bundle added"
        else:
            message = "Payment canceled or failed"
    else:
        message = message_to_send
    return message


def send_notification(
    fcm_token,
    registration_ids,
    status,
    data,
    message_category="buy_bundle",
    message_to_send="",
):
    try:

        registration_ids = "key=" + str(registration_ids)
        url = "https://fcm.googleapis.com/fcm/send"
        payload = {
            "registration_ids": [fcm_token],
            "notification": {"content-available": True, "priority": "high"},
            "data": data,
        }
        message = get_notification_message(message_category, status, message_to_send)

        if status == "success":
            payload["purchased"] = True
            payload["notification"]["title"] = message
            payload["notification"]["body"] = message
            payload["data"]["purchased"] = True
        if status == "failed":
            payload["purchased"] = False
            payload["notification"]["title"] = message
            payload["notification"]["body"] = message
            payload["data"]["purchased"] = False
        headers = {"Content-Type": app_json, "Authorization": registration_ids}
        response = requests.request("POST", url, headers=headers, data=json.dumps(payload))

        new_resp = response.json()
        return new_resp
    except Exception as e:
        print("error ", str(e))


def send_fcm_notification(settings, fcm_token, ios_version, data, status):
    if settings and fcm_token != "" and ios_version:
        send_notification(fcm_token, settings.fcm_registration, status=status, data=data)


@try_except_func
def save_notification_logs(log):
    return main_models.NotificationLogs(**log).save()


def allocate_esimgo_bundle(bundle_vendor_code, instance_config, reseller_id):
    data = {
        "success": False,
        "order_reference": "",
        "iccid": "",
        "smdp_address": "",
        "matching_id": "",
        "send_email": False,
    }
    error = ""
    try:
        esimgo_helper = ESIMGo()
        esimgo_order_response = esimgo_helper.assign_profiles(bundle_vendor_code=bundle_vendor_code)
        if esimgo_order_response:
            order_reference = esimgo_order_response.get("orderReference")
            profile_info = esimgo_order_response.get("order", [{}])[0].get("esims", [{}])[0]
            print(f"The response contains data for order {order_reference}.")

            iccid, matching_id, smdp_address = (
                profile_info.get("iccid"),
                profile_info.get("matchingId"),
                profile_info.get("smdpAddress"),
            )

            (
                data["iccid"],
                data["matching_id"],
                data["smdp_address"],
                data["order_reference"],
            ) = (iccid, matching_id, smdp_address, order_reference)
            data["success"] = data["send_email"] = True
            save_profile(profile_info, reseller_id)

        return data, error
    except Exception as e:
        print(f"Exception in allocate_esimgo_bundle {str(e)}")
        return data, "Couldn't allocate"


def topup_esimgo_bundle(bundle_vendor_code, user_iccid, instance_config):
    esimgo_helper = ESIMGo()
    esimgo_create_order = esimgo_helper.assign_profiles(bundle_vendor_code=bundle_vendor_code, buy_bundle=False)
    order_reference = esimgo_create_order.get("orderReference")
    res = esimgo_helper.apply_topup(bundle_vendor_code=bundle_vendor_code, iccid=user_iccid.iccid)
    if res:
        profile = get_profile_from_iccid(user_iccid.iccid)
        return {
            "success": True,
            "order_reference": order_reference,
            "iccid": user_iccid.iccid,
            "smdp_address": profile.smdp_address,
            "matching_id": profile.matching_id,
        }, ""

    else:
        print("Topup Failed for ICCID {}".format(user_iccid.iccid))
        print("Reason: {}".format(str(esimgo_create_order["message"])))
        return {"success": False, "order_reference": None, "iccid": None}, "Bad Request"


# endregion
# region FLEXIROAM
def get_token_flexiroam(flexiroam_url, username, password):
    try:
        headers = {}
        payload = {"email": username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post("{}/user/login/v1".format(flexiroam_url), headers=headers, data=payload)
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"


def allocate_flexi_bundle(user_iccid, plan_code, instance_config):
    bundles = {}
    access_token = get_vendor_info(FLEXIROAM_VENDOR).temp_token

    if not access_token:
        return bundles, ""

    if not user_iccid:
        return bundles, ""

    sku = get_sku_from_iccid(user_iccid.iccid)
    if not sku:
        return bundles, ""

    lst_sku = "[" + sku + "]"
    payload = {
        "sku": lst_sku,
        "plan_code": plan_code,
        "plan_start_type_id": "1",
        "discount": "",
    }

    headers = {"token": access_token}
    try:
        response = requests.post(
            "{}/plan/load/v1".format(instance_config.flexiroam_url),
            headers=headers,
            json=payload,
            timeout=125.5,
        )
        if response.status_code != 200:
            print("r.status_code !=200 in allocate_flexi_bundle")
            result, exception = get_token_flexiroam(
                instance_config.flexiroam_url,
                instance_config.flexiroam_username,
                instance_config.flexiroam_password,
            )
            if result:
                access_token = result["data"]["token"]
                vendor = get_vendor_info(FLEXIROAM_VENDOR)
                vendor.update(set__temp_token=access_token)
            headers = {"token": access_token}
            response = requests.post(
                "{}/plan/load/v1".format(instance_config.flexiroam_url),
                headers=headers,
                json=payload,
                timeout=125.5,
            )

        bundles = response.json()
    except (
        requests.exceptions.ConnectionError,
        requests.exceptions.Timeout,
        NewConnectionError,
    ) as error:
        logger.exception(f"<allocate_flexi_bundle> remote error happened: {error} while buying bundle: {plan_code} iccid: {user_iccid}")
        # vendor is not available, so we should deactivate all bundles related to this vendor
        deactivate_vendor(FLEXIROAM_VENDOR)
        send_custom_monitor_email(
            subject=f"------URGENT------ Vendor: {FLEXIROAM_VENDOR} deactivated",
            body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(datetime.datetime.utcnow(), FLEXIROAM_VENDOR, error),
        )
        return bundles, str(error)
    except ValueError as error:

        logger.exception(f"<allocate_flexi_bundle> remote error happened: {error} while buying bundle: {plan_code} iccid: {user_iccid}")
        deactivate_bundle(plan_code)
        send_custom_monitor_email(
            subject=f"------URGENT------ Bundle: {plan_code} deactivated",
            body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(datetime.datetime.utcnow(), plan_code, FLEXIROAM_VENDOR, error),
        )
        return bundles, str(error)
    except Exception as error:
        logger.exception(f"<allocate_flexi_bundle> error happened: {error} while buying bundle: {plan_code} iccid: {user_iccid}")

        return bundles, str(error)

    return bundles, ""


def allocate_vodafone_bundle(bundle_code, instance_config, order_number=None):
    try:

        url = "{}/network/things/consumer-profile/{}".format(instance_config.vodafone_url, bundle_code)
        headers = {
            "ResponseURLs": instance_config.callback_get_iccid,
            "Authorization": "Bearer " + str(instance_config.vodafone_token),
        }
        try:
            # we only send order number in case flow is related to subscriber
            if order_number:
                payload = {"customReference": order_number}
                response = requests.request("POST", url, headers=headers, json=payload)
            else:
                payload = {}
                response = requests.request("POST", url, headers=headers, json=payload)
            res = response.json()
            order_reference = res["acknowledgement"]["id"]
            new_data = {"plan_uid": order_reference, "reseller_type": "reseller"}
            reseller_models.Order_history(**new_data).save()
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(f"<allocate_vodafone_bundle> remote error happened: {error} while buying bundle: {bundle_code}")
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(VODAFONE_VENDOR)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {VODAFONE_VENDOR} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(datetime.datetime.utcnow(), VODAFONE_VENDOR, error),
            )
            raise error
        except (ValueError, KeyError) as error:
            print(f"<allocate_vodafone_bundle> remote error happened: {error} while buying bundle: {bundle_code}")
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_bundle(bundle_code)
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(datetime.datetime.utcnow(), bundle_code, VODAFONE_VENDOR, error),
            )
            raise error
        except Exception as error:
            print(f"<allocate_vodafone_bundle> error happened: {error} while buying bundle: {bundle_code}")
            raise error

        if response.status_code == 200:
            print("order_reference ", order_reference)
            return {
                "success": True,
                "order_reference": order_reference,
                "iccid": "",
                "smdp_address": "",
                "matching_id": "",
                "send_email": False,
            }, "Waiting reply"
        return {
            "success": False,
            "order_reference": "",
            "iccid": "",
            "smdp_address": "",
            "matching_id": "",
            "send_email": False,
        }, "Couldn't allocate"
    except Exception as e:
        print("exception in allocation ", e)
        return {
            "success": False,
            "order_reference": "",
            "iccid": "",
            "smdp_address": "",
            "matching_id": "",
            "send_email": False,
        }, "Couldn't allocate"


@try_except_func
def topup_vodafone_bundle(bundle_vendor_code, user_iccid, instance_config, reseller_id=None):
    try:

        url = "{}/network/top-up/iccids/{}".format(instance_config.vodafone_url, user_iccid["iccid"])
        headers = {
            "ResponseURLs": instance_config.callback_get_iccid,
            "Authorization": "Bearer " + str(instance_config.vodafone_token),
        }
        payload = {"bundleID": bundle_vendor_code}

        response = requests.request("PUT", url, headers=headers, json=payload)
        print(response.json())
        print("response in topup_vodafone_bundle", response)
        if response.status_code == 202:
            res = response.json()
            order_reference = res["acknowledgement"]["id"]
            new_data = {"plan_uid": order_reference, "reseller_type": "reseller"}
            if reseller_id:
                new_data["reseller_id"] = ObjectId(reseller_id)
            reseller_models.Order_history(**new_data).save()
            print(
                " line 107744444 new order added successfuly with plan_uid =",
                order_reference,
                datetime.datetime.utcnow(),
            )
            print("order_reference ", order_reference)
            return {
                "success": True,
                "order_reference": order_reference,
                "iccid": "",
                "smdp_address": "",
                "matching_id": "",
            }, "Waiting reply"
        return {
            "success": False,
            "order_reference": "",
            "iccid": "",
            "smdp_address": "",
            "matching_id": "",
        }, "Couldn't allocate"

    except Exception as e:
        print("exception in allocation ", e)
        return {
            "success": False,
            "order_reference": "",
            "iccid": "",
            "smdp_address": "",
            "matching_id": "",
        }, "Couldn't allocate"


# endregion
# region MONTYMOBILE
def allocate_montymobile_bundle(profile: Profiles, plan_code: str) -> Tuple[Dict, str]:
    """
        wrapper function for vendor 'Monty Mobile' i.e 'Comium' which handles its API call
        TODO: Could be removed or refactored its usage later on
        returns the JSON response of the API call and a status message
    """
    resp: Dict
    message: str
    try:
        monty_mobile: MontyMobile = MontyMobile()
        # Not sure why are we getting profile again, but didn't dare to remove it cuz it might break something else
        sku: Profiles = get_sku_from_iccid(profile.iccid)
        if not sku:
            logger.error("profile not found for iccid %s", profile.iccid)
            return {}, f"profile {profile.iccid} not found"
        resp, message = monty_mobile.load_bundle_to_profile(iccid=profile.iccid, sku=profile.sku, bundle_vendor_code=plan_code)
        return resp, message
    except Exception as e:
        logger.error("faced exception while loading bundle %s to profile %s: %s", plan_code, profile.iccid, e)
        if not e:
            logger.warning("maybe this is an unauthorized error, check why logs")
        return {}, str(e)


# endregion
# region OTHERS
def generate_temp_otp(n=25):
    new_rand = "".join(secrets.choice(string.digits) for i in range(0, n))
    return new_rand


def get_db_connection(consumer_config):
    mongo_client = pymongo.MongoClient(consumer_config.new_host_)
    db = mongo_client.get_database(consumer_config.decrypted_db_name_alias)
    return db


@try_except_func
def get_user(user_email="", username=""):
    if user_email != "":
        query_set = Q(user_email=user_email)
    else:
        query_set = Q(user_name=username)

    current_user = mobiles.AppUserDetails.objects(query_set).first()
    if current_user.is_deleted:
        return None
    return current_user


# endregion
def get_version():
    return 81, "Last change: add filter in format bundles as country code list"


def update_plan_started(iccid):
    reseller_models.Order_history.objects(reseller_type="subscriber", iccid=iccid,
                                          plan_uid__ne="").update(
        set__plan_started=True)

def validate_email(validate, msg_lst, field_check, field_name):
    regex = r"^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$"
    if not (re.search(regex, field_check)):
        msg_lst.append("invalid {}".format(field_name))
        validate = False
        return validate, msg_lst
    return validate, msg_lst

def get_last_notification_not_notified_by_reseller(reseller_id: str, bundle_code: str) -> NotificationHistory:
    """
    Retrieve the most recent unnotified webhook notification for a given reseller and bundle code.

    This function returns the latest NotificationHistory document (if any) for the specified reseller
    and bundle code where no request has been sent, no response has been received, and the status is still failed.

    :param reseller_id: Mongo Object Id of the reseller.
    :param bundle_code: bundle_code field in 'bundles' collection for a specific bundle.
    """
    return reseller_models.NotificationHistory.objects(reseller_id=reseller_id,
                                                        bundle_code=bundle_code,
                                                        request_sent_time=None,
                                                        response_payload=None,
                                                        response_received_time=None,
                                                        status=False
                                                    ).order_by('-created_at').first()


# =============================================
# region reseller geoscoped bundles
# =============================================


def parse_data_size_value(value: Optional[str]) -> Optional[Tuple[str, Decimal]]:
    """
    Parses a string-based data size value (e.g. "0.5") into a normalized tuple:
    - If less than 1, it's converted to MB.
    - Otherwise, treated as GB.
    Returns a tuple of (unit, value).
    """
    if value is None:
        return None
    try:
        decimal_val = Decimal(value)
        if decimal_val < 1:
            return ("MB", decimal_val * 1000)
        else:
            return ("GB", decimal_val)
    except InvalidOperation:
        raise ValueError(f"Invalid data size value: {value}")


def parse_price_value(value: Optional[str]) -> Optional[Decimal]:
    """
    Parses a string price into a Decimal for consistent filtering.
    Returns None if the value is None.
    """
    if value is None:
        return None
    try:
        return Decimal(value)
    except InvalidOperation:
        raise ValueError(f"Invalid price value: {value}")


def build_filter_stages(
    data_size_min: Optional[str],
    data_size_max: Optional[str],
    validity_days_min: Optional[int],
    validity_days_max: Optional[int],
    price_min: Optional[str],
    price_max: Optional[str],
):
    """
    Builds MongoDB stages for optional filters: price, validity days, and data size.
    Reused by all pipelines (region/global/countries).
    """
    stages = []

    # Normalize validity into integer
    stages.append({"$addFields": {"normalized_validity_days": {"$toInt": "$validity_amount"}}})

    # Price filter
    price_filter = {}
    min_price = parse_price_value(price_min)
    max_price = parse_price_value(price_max)
    if min_price is not None and max_price is not None:
        if min_price > max_price:
            raise ValueError("Price min cannot be greater than price max")
    if min_price is not None:
        price_filter["$gte"] = float(min_price)
    if max_price is not None:
        price_filter["$lte"] = float(max_price)
    if price_filter:
        stages.append({"$match": {"retail_price": price_filter}})

    # Validity filter
    validity_filter = {}
    if validity_days_min is not None and validity_days_max is not None:
        if validity_days_min > validity_days_max:
            raise ValueError("Validity min cannot be greater than validity max")
    if validity_days_min is not None:
        validity_filter["$gte"] = validity_days_min
    if validity_days_max is not None:
        validity_filter["$lte"] = validity_days_max
    if validity_filter:
        stages.append({"$match": {"normalized_validity_days": validity_filter}})

    # Data size filter
    data_filters = []
    min_data = parse_data_size_value(data_size_min)
    max_data = parse_data_size_value(data_size_max)

    if min_data and max_data:
        min_unit, min_value = min_data
        max_unit, max_value = max_data

        # Case: GB → MB (invalid)
        if min_unit == "GB" and max_unit == "MB":
            raise ValueError("Cannot filter with min in GB and max in MB")

        # Case: Same units
        if min_unit == max_unit:
            if min_value > max_value:
                raise ValueError("Data size min cannot be greater than max when using the same unit")
            data_filters.append(
                {
                    "$and": [
                        {"data_unit": min_unit},
                        {"data_amount": {"$gte": float(min_value)}},
                        {"data_amount": {"$lte": float(max_value)}},
                    ]
                }
            )
        # Case: min=MB, max=GB → split two filters
        elif min_unit == "MB" and max_unit == "GB":
            data_filters.append({"$and": [{"data_unit": "MB"}, {"data_amount": {"$gte": float(min_value)}}]})
            data_filters.append({"$and": [{"data_unit": "GB"}, {"data_amount": {"$lte": float(max_value)}}]})

    elif min_data:
        unit, min_value = min_data

        if unit.lower() == "mb":
            # MB >= min OR any GB
            data_filters.append({"$and": [{"data_unit": "MB"}, {"data_amount": {"$gte": float(min_value)}}]})

            data_filters.append({"data_unit": "GB"})
        else:
            # Only GB >= min
            data_filters.append({"$and": [{"data_unit": "GB"}, {"data_amount": {"$gte": float(min_value)}}]})
    elif max_data:
        unit, max_value = max_data
        if unit.lower() == "gb":
            # GB <= max OR any MB
            data_filters.append({"$and": [{"data_unit": "GB"}, {"data_amount": {"$lte": float(max_value)}}]})

            data_filters.append({"data_unit": "MB"})
        else:
            # Only MB <= max
            data_filters.append({"$and": [{"data_unit": "MB"}, {"data_amount": {"$lte": float(max_value)}}]})

    if data_filters:
        stages.append({"$match": {"$or": data_filters}})

    return stages


def build_region_mappings_from_db():
    """
    Builds a dynamic mapping of region_code → list of ISO2 country codes, enriched with region display names.

    - Uses the `Countries` MongoDB collection to determine which countries belong to which regions.
    - Uses the `Regions` collection to retrieve user-friendly `region_name` values for each `region_code`.
    - Applies a special override: all countries belonging to 'Oceania' are remapped under 'as' (Asia).
    - Only includes regions that are defined in the `Countries.region_code` data — regions with no countries are excluded.
    - If a region's display name is not found in the `Regions` collection, 'Unknown' is used as fallback.

    Returns:
            List[Dict[str, Any]]: e.g.,
            [
                    {
                            "region": "as",
                            "region_name": "Asia",
                            "codes": ["AU", "CN", "IN", "NZ", ...]
                    },
                    {
                            "region": "eu",
                            "region_name": "Europe",
                            "codes": ["FR", "DE", "TR", ...]
                    },
                    ...
            ]
    """

    regions = Regions.objects.only("region_code", "region_name").as_pymongo()
    region_index = {r["region_code"].lower(): r["region_name"] for r in regions}

    countries = Countries.objects.only("iso2_code", "region_code").as_pymongo()

    region_map = defaultdict(set)
    for c in countries:
        iso2 = (c.get("iso2_code") or "").upper()
        for reg_code in c.get("region_code", []):
            reg_code = reg_code.lower()

            # Handle the 'oceania' to 'asia' conversion
            if reg_code == "oceania":
                reg_code = "as"

            region_map[reg_code].add(iso2)

    final = [
        {"region": reg_code, "region_name": region_index.get(reg_code, "Unknown"), "codes": sorted(list(codes))}
        for reg_code, codes in region_map.items()
    ]

    return final


# Internal cache variables
_cached_region_mappings = None
_cache_expiration = 0


def get_cached_region_mappings(ttl_seconds=60):
    """
    Returns a cached region mapping, refreshing from DB if cache expired.
    Helps avoid repeated DB hits for region remapping.
    """
    global _cached_region_mappings, _cache_expiration

    now = time.time()

    if _cached_region_mappings is None or now >= _cache_expiration:
        # Cache expired → refresh
        print("Cache expired — refreshing region mappings...")
        _cached_region_mappings = build_region_mappings_from_db()
        _cache_expiration = now + ttl_seconds
    else:
        print("Using cached region mappings")

    return _cached_region_mappings


def get_reseller_region_geoscope_bundle_pipeline(
    page=1,
    limit=20,
    data_size_min: Optional[str] = None,
    data_size_max: Optional[str] = None,
    validity_days_min: Optional[int] = None,
    validity_days_max: Optional[int] = None,
    price_min: Optional[str] = None,
    price_max: Optional[str] = None,
    show_unidentified=True
):
    """
    Builds the aggregation pipeline for `geoscope = region`.
    - Handles Turkey override logic
    - Applies dynamic region mappings
    - Splits bundles into region groups based on country composition
    - Optionally filters out 'unidentified' if required via the 'show_unidentified' option
    - Supports filtering + pagination at the *region level*
    """
    region_mappings = get_cached_region_mappings()

    # Special-case Manual overrides
    region_override_conditions = [
        {
            # If only Turkey exists in the bundle → assign to Europe
            "if": {
                "$and": [
                    {"$eq": [{"$size": "$country_code_list"}, 1]},
                    {"$eq": [{"$toUpper": {"$arrayElemAt": ["$country_code_list", 0]}}, "TR"]}
                ]
            },
            "then": "eu"
        }
    ]

    # Build chained condition: override → default mapping
    cond = None
    for rule in reversed(region_override_conditions):
        cond = {
            "$cond": [
                rule["if"],
                rule["then"],
                cond if cond else None
            ]
        }

    pipeline = [
        {
            "$match": {
                "is_active": True,
                "deleted": False,
                "preview_for": {"$in": ["reseller"]},
                "is_region": True
            }
        },
        *build_filter_stages(
            data_size_min,
            data_size_max,
            validity_days_min,
            validity_days_max,
            price_min,
            price_max
        ),
        {
            "$set": {
                "region_override_code": cond
            }
        },
        {
            "$set": {
                "region_matches": {
                    "$cond": [
                        {"$ne": ["$region_override_code", None]},
                        [{"region": "$region_override_code", "codes": "$country_code_list"}],
                        region_mappings
                    ]
                }
            }
        },
        {
            "$set": {
                "matched_regions": {
                    "$filter": {
                        "input": "$region_matches",
                        "as": "r",
                        "cond": {
                            "$gt": [
                                {
                                    "$size": {
                                        "$setIntersection": [
                                            {
                                                "$map": {
                                                    "input": "$$r.codes",
                                                    "as": "code",
                                                    "in": {"$toUpper": "$$code"}
                                                }
                                            },
                                            {
                                                "$map": {
                                                    "input": "$country_code_list",
                                                    "as": "c",
                                                    "in": {"$toUpper": "$$c"}
                                                }
                                            }
                                        ]
                                    }
                                },
                                0
                            ]
                        }
                    }
                }
            }
        },
        {
            "$unwind": {
                "path": "$matched_regions",
                "preserveNullAndEmptyArrays": True
            }
        },
        {
            "$set": {
                "region_group_code": {
                    "$ifNull": ["$matched_regions.region", "unidentified"]
                },
                "region_display_name": {
                    "$ifNull": ["$matched_regions.region_name", "Unknown"]
                },
                "country_code_list": {
                    "$cond": [
                        {"$eq": ["$matched_regions", None]},
                        "$country_code_list",
                        {
                            "$filter": {
                                "input": "$country_code_list",
                                "as": "code",
                                "cond": {
                                    "$in": [
                                        {"$toUpper": "$$code"},
                                        {
                                            "$map": {
                                                "input": {"$ifNull": ["$matched_regions.codes", []]},
                                                "as": "c",
                                                "in": {"$toUpper": "$$c"}
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        },
        {
            "$unset": [
                "region_matches",
                "matched_regions",
                "region_override_code"
            ]
        }
    ]

    if not show_unidentified:
        pipeline.append({
            "$match": {"region_group_code": {"$ne": "unidentified"}}
        })

    pipeline.append({
        "$facet": {
            "data": [
                {
                    "$group": {
                        "_id": "$region_group_code",
                        "region_name": {"$first": "$region_display_name"},
                        "bundles": {"$push": "$$ROOT"}
                    }
                },
                {"$sort": {"_id": 1}},
                {"$skip": (page - 1) * limit},
                {"$limit": limit}
            ],
            "total_bundle_count": [{"$count": "count"}],
            "total_region_count": [
                {"$group": {"_id": "$region_group_code"}},
                {"$count": "count"}
            ]
        }
    })

    return pipeline


def get_reseller_global_or_countries_geoscope_bundle_pipeline(
        geoscope: str,
        page: int = 1,
        limit: int = 20,
        data_size_min: Optional[str] = None,
        data_size_max: Optional[str] = None,
        validity_days_min: Optional[int] = None,
        validity_days_max: Optional[int] = None,
        price_min: Optional[str] = None,
        price_max: Optional[str] = None
):
    """
    Builds the aggregation pipeline for `geoscope = global` or `countries`.
    - Returns a flat list of bundles (no region grouping)
    - Supports price, data size, and validity filtering
    - Pagination is applied directly on flat documents
    """
    if geoscope not in {"global", "countries"}:
        raise ValueError("Invalid geoscope for this pipeline")
    
    # Step 0: Base filter
    match_stage = {
        "is_active": True,
        "deleted": False,
        "preview_for": {"$in": ["reseller"]}
    }
    
    # Step 2: Category-specific logic
    if geoscope == "countries":
        match_stage["bundle_category"] = {"$in": ["country", "region", "global"]}
    elif geoscope == "global":
        match_stage["bundle_category"] = "global"
        match_stage["$expr"] = {"$gte": [{"$size": "$country_code_list"}, 51]}
    
    pipeline = [
        {"$match": match_stage},
        *build_filter_stages(data_size_min, data_size_max, validity_days_min, validity_days_max, price_min, price_max),
        
        # Facet: Pagination + Total count
        {
            "$facet": {
                "data": [
                    {"$skip": (page - 1) * limit},
                    {"$limit": limit}
                ],
                "total_bundles_count": [{"$count": "count"}]
            }
        }
    ]
    
    return pipeline

def map_region_grouped_bundles(raw_result, page, limit) -> RegionGroupedBundlesResponse:
    total_bundle_count = 0
    total_region_count = raw_result[0].get("total_region_count", [{}])[0].get("count", 0)
    regions = []

    for group in raw_result[0].get("data", []):
        region_code = group["_id"]
        region_name = group.get("region_name", "Unknown")
        region_bundles = group.get("bundles", [])

        total_bundle_count += len(region_bundles)

        bundles = [
            ResellerScopedBundle(
                bundle_id=str(b.get("_id")),
                bundle_name=b.get("bundle_name", ""),
                supported_countries=b.get("country_code_list", []),
                region=b.get("region_name", "Unknown"),
                data_size=b.get("data_amount", 0),
                data_unit= b.get("data_unit", ""),
                validity_days=int(b.get("validity_amount", 0)),
                price=float(b.get("retail_price", 0)),
            )
            for b in region_bundles
        ]

        regions.append(RegionGroupedBundlesResponseRegions(
            region=region_code,
            region_name=region_name,
            bundles=bundles
        ))

    return RegionGroupedBundlesResponse(
        page=page,
        limit=limit,
        total_bundles=total_bundle_count,
        total_regions=total_region_count,
        regions=regions
    )


def map_flat_bundles(raw_result, page, limit) -> ScopedBundlesResponse:
    data = raw_result[0].get("data", [])
    total = raw_result[0].get("total_bundles_count", [{}])[0].get("count", 0)

    bundles = [
        ResellerScopedBundle(
            bundle_id=str(b.get("_id")),
            bundle_name=b.get("bundle_name", ""),
            supported_countries=b.get("country_code_list", []),
            region=b.get("region_name", "Unknown"),
            data_size=b.get("data_amount", 0),
            data_unit= b.get("data_unit", ""),
            validity_days=int(b.get("validity_amount", 0)),
            price=float(b.get("retail_price", 0)),
        )
        for b in data
    ]

    return ScopedBundlesResponse(
        page=page,
        limit=limit,
        total_bundles=total,
        bundles=bundles
    )
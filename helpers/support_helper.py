import logging
from app_models import consumer_models, main_models
import smtplib
from mongoengine.queryset.visitor import Q
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from datetime import datetime, timezone

from helpers.utils import try_except_func
from app_models.consumer_models import Profiles, Bundles, ConsumptionCache

logger = logging.getLogger(__name__)


@try_except_func
def get_vendor_info(vendor_name):
    vendor = consumer_models.Vendors.objects(vendor_name=vendor_name).first()
    return vendor


@try_except_func
def get_setting():
    return main_models.Settings.objects().first()


@try_except_func
def get_email_settings():
    return main_models.EmailSettings.objects().first()


@try_except_func
def send_email(email_settings, to_user, subject, body, has_attchament=False, has_file=True, file_path=None):
    smtp_server = smtplib.SMTP(email_settings.smtp_server, email_settings.smtp_port)
    smtp_server.ehlo()
    smtp_server.starttls()
    smtp_server.login(email_settings.username, email_settings.password)
    header = 'To:' + to_user + '\n' + 'From: ' + email_settings.username + '\n' + 'Subject:' + subject + ' \n'
    msg = header + '\n' + body + '\n\n'
    if has_attchament:
        msg = MIMEMultipart()
        msg['Subject'] = subject
        if has_file and file_path is not None:
            with open(file_path, 'rb') as f:
                img_data = f.read()
            image = MIMEImage(img_data, name='Qrcode.png')
            msg.add_header('Content-ID', 'Qrcode')
            msg.attach(image)

        text = MIMEText(body, 'html')
        msg.attach(text)
        msg = msg.as_string()
    smtp_server.sendmail(email_settings.username, to_user, msg)
    smtp_server.close()


def send_custom_support_email(subject, body):
    setting = get_setting()
    email_settings = get_email_settings()

    send_email(email_settings=email_settings, to_user=setting.esim_email,
               subject=subject, body=body, has_attchament=False)


def send_custom_monitor_email(subject, body):
    setting = get_setting()
    email_settings = get_email_settings()

    send_email(email_settings=email_settings, to_user=setting.monitor_email,
               subject=subject, body=body, has_attchament=False)


def deactivate_vendor(vendor_name):
    consumer_models.Vendors.objects(vendor_name=vendor_name).update(is_active=False)
    add_to_update_bundle_version([f"deactivate vendor: {vendor_name}"])
    print(f"<deactivate_vendor> vendor: {vendor_name} deactivated")
    return


def add_to_update_bundle_version(bundle_list):
    latest_version = main_models.UpdateBundleVersion.objects.order_by('-datetime').first()
    new_update_version = latest_version.bundle_version_number + 1 if latest_version else 1
    current_datetime = datetime.utcnow()
    new_version = main_models.UpdateBundleVersion(bundle_version_number=new_update_version, datetime=current_datetime,
                                                  description=bundle_list)
    new_version.save()


@try_except_func
def deactivate_bundle(bundle_identifier: str, deactivate_topup: bool = False, vendor_name: str = None):
    if not vendor_name:
        logging.warning(
            "vendor_name is not provided, this might update the bundle for the wrong vendor")

    query_set = Q(bundle_vendor_code=str(bundle_identifier)) | Q(bundle_code=str(bundle_identifier))
    if vendor_name:
        query_set = query_set & Q(vendor_name=vendor_name)
    bundle: consumer_models.Bundles = consumer_models.Bundles.objects(query_set).first()

    if not bundle:
        logging.warning("hello dear developer, you cant deactivate what does not exist! RETURNING")
        return
    add_to_update_bundle_version([f"Deactivate bundle: {bundle.bundle_code}"])
    bundle.update_at = datetime.utcnow()
    bundle.is_active = False
    if deactivate_topup:
        bundle.support_topup = False
        logging.info("deactivated topup capability for bundle %s of vendor %s", bundle.bundle_code, bundle.vendor_name)
    bundle.save()
    logging.info("deactivated bundle %s of vendor %s", bundle.bundle_code, bundle.vendor_name)


def calculate_mb(unit, amount=0):
    """
    Convert a given amount from the specified unit to MB (megabytes).

    :param amount: float, the size to convert
    :param unit: str, the unit of the input size ('KB', 'MB', 'GB', 'TB')
    """
    unit = unit.upper()  # Convert unit to uppercase for consistency
    conversion_factors = {
        "KB": 1 / 1024,  # 1 KB = 1/1024 MB
        "MB": 1,  # 1 MB = 1 MB
        "GB": 1024,  # 1 GB = 1024 MB
        "TB": 1024 * 1024  # 1 TB = 1024 * 1024 MB
    }

    if unit not in conversion_factors:
        raise ValueError(f"Unsupported unit '{unit}'. Please use one of the following: KB, MB, GB, TB.")

    return amount * conversion_factors[unit]


def add_data_amount_to_consumption_cache(iccid: str, bundle_code: str):
    """
    When purchasing a bundle, add the data amount of the bundle to the consumption cache to use in consumption
    fail over mechanism
    """
    profile = Profiles.objects(iccid=iccid).first()
    bundle = Bundles.objects(bundle_code=bundle_code).first()

    if not profile:
        logger.error("Profile with ICCID '%s' not found.", iccid)
        return

    if not bundle:
        logger.error("Bundle with code '%s' not found.", bundle_code)
        return

    try:
        size_in_mb = calculate_mb(bundle.data_unit, bundle.data_amount)
        if not size_in_mb:
            logger.error("Invalid data calculation for bundle: %s", bundle_code)
            return

        # Update or initialize the consumption cache
        if profile.consumption_cache:
            # Ensure data_allocated is a valid number, default to 0 if it's None or an empty string
            data_allocated = profile.consumption_cache.data_allocated
            data_allocated = float(data_allocated) if data_allocated and data_allocated.strip() else 0

            data_allocated += size_in_mb
            profile.consumption_cache.data_allocated = str(round(data_allocated, 2))

            profile.consumption_cache.cached_at = datetime.utcnow()

            # Ensure data_used is not None or empty
            profile.consumption_cache.data_used = profile.consumption_cache.data_used.strip() or "0"
            profile.consumption_cache.data_unit = "MB"
        else:
            # Create new cache with default values
            profile.consumption_cache = ConsumptionCache(
                cached_at=datetime.utcnow(),
                data_allocated=str(round(size_in_mb, 2)),
                data_used="0",
                data_unit="MB",
            )
        profile.save()
    except (ValueError, TypeError) as e:
        logger.error(
            "Error occurred while updating consumption cache for ICCID '%s'. Exception: %s",
            iccid, str(e),
        )


# Cache consumption details
def cache_consumption_data(profile: Profiles, data: dict):
    # Update profile_status if available
    if data.get("profile_status", None):
        profile.profile_status = data.get("profile_status")
    profile.consumption_cache = ConsumptionCache(
        cached_at=datetime.now(timezone.utc),
        data_allocated=str(data.get("data_allocated")),
        data_used=str(data.get("data_used")),
        data_unit=data.get("data_unit"),
    )
    profile.save()

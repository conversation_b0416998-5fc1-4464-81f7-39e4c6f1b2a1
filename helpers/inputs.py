class TopupRelatedV2FilterInput:
    def __init__(
            self, reseller_id="", bundle_category=None, country_code=None,
            bundle_code=None, profile_name=None, vendor_name=None, region_code=None, bundle_name=None,
            is_topup=False,group_id=None
    ):
        self.reseller_id = reseller_id
        self.region_code = region_code
        self.bundle_category = bundle_category
        self.country_code = country_code
        self.bundle_code = bundle_code
        self.profile_name = profile_name
        self.vendor_name = vendor_name
        self.bundle_name = bundle_name
        self.is_topup = is_topup
        self.group_id = group_id

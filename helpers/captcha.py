import requests
import logging

import instance.consumer_config as config

logger = logging.getLogger(__name__)


class CaptchaError(Exception):
    """Base exception for captcha verification issues."""

    pass


class CaptchaParameterError(CaptchaError, ValueError):
    """Exception for invalid parameters passed to captcha verification."""

    pass


class CaptchaHttpError(CaptchaError):
    """Exception for HTTP or network errors during captcha verification with Google."""

    def __init__(self, message, status_code=None, response_text=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


def verify_captcha_token(captcha_secret_key: str = "", response_token: str = "") -> (bool, str):
    """
    Sends captcha token generated by the client to google's api to verify it's validity.
    :param captcha_secret_key: The secret key provided by Google captcha
    :param response_token: The response token provided by the client
    """
    if not isinstance(captcha_secret_key, str) or not isinstance(response_token, str):
        raise CaptchaParameterError("captcha secret key or response token is not a string")

    captcha_secret_key = captcha_secret_key.strip()
    response_token = response_token.strip()

    if not captcha_secret_key:
        raise CaptchaParameterError("captcha secret key is not provided (configuration error)")
    if not response_token:
        raise CaptchaParameterError("response token is not provided by the client")

    logger.info("Verifying captcha token: %s", response_token)
    url: str = "https://www.google.com/recaptcha/api/siteverify"
    payload: dict = {"secret": captcha_secret_key, "response": response_token}

    try:
        response = requests.post(url, json=payload, timeout=config.http_connection_timeout)

        if not response.ok:
            logger.warning("Google reCAPTCHA API request failed with status %s Response: %s", response.status_code, response.text[:500])
            raise CaptchaHttpError(
                f"Failed to verify captcha token, Google API responded with status {response.status_code}",
                status_code=response.status_code,
                response_text=response.text,
            )
    except requests.exceptions.Timeout as e:
        logger.error("Timeout during captcha verification POST to Google: %s", e)
        raise CaptchaHttpError(f"Timeout verifying captcha with Google: {e}") from e
    except requests.exceptions.RequestException as e:  # Catches other network errors (ConnectionError, etc.)
        logger.error("Network error during captcha verification POST to Google: %s", e)
        raise CaptchaHttpError(f"Network error verifying captcha with Google: {e}") from e

    result: dict = response.json()
    status: bool = result.get("success", False) and result.get("score", 0) >= 0.5
    return status, ", ".join(result.get("error-codes", []))

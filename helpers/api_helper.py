import requests


def get_all_topups_balance(instance_config, iccid):
    try:
        url = f"{instance_config.vodafone_url}/network/top-up/history/iccids/{iccid}/90"
        headers = {'Authorization': "Bearer " + instance_config.vodafone_token}
        response = requests.request("GET", url, headers=headers)
        cumulative_topup_balance = 0
        if response.status_code == 200:
            for bundle in response.json():
                if bundle.get("topUp"):
                    cumulative_topup_balance += int(bundle["topUp"]["value"])
                elif bundle.get("initial"):
                    cumulative_topup_balance += int(bundle["initial"]["value"])

        return round((float(cumulative_topup_balance)), 2)
    except Exception as e:
        pass
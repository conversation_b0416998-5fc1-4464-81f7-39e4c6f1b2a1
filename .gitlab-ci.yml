variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task

services:
  - docker:dind

stages:
  - test
  - publish_latest
  - release_publish_stable
  - deploy

include:
  - component: gitlab.com/monty-mobile1/r-d/pipeline-components/semantic-release-pipeline-component/tag-release-publish-python-esim-b2c@~latest
    inputs:
      bump-version-and-tag-needs: publish_manifests_development
  - template: Jobs/SAST.gitlab-ci.yml

.ssh: &ssh
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )'
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY_ESIM_STAGE" | tr -d '\r' | ssh-add
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh

#functional_test:
#  stage: test
#  allow_failure: false
#  services:
#    - mongo:5.0.17
#  image: python:3.8
#  cache:
#    - key:
#        files:
#          - tests/test-requirements.txt
#          - requirements.txt
#      paths:
#        - .pip-cache/
#      policy: pull-push
#  before_script:
#    - apt-get update -q -y
#    - apt-get install -y -qq python3-pip
#    - ./swagger_server/test/install_requirements.sh
#    - source ./swagger_server/test/env.test.sh
#  variables:
#    ENV: 'test'
#    ENCRPYPT_UKEY: $ENCRPYPT_UKEY
#  coverage: '/Code coverage:\s*([1-9]?\d(?:\.\d+)?%|100(?:\.0+)?%)/'
#  script:
#    - coverage run -m xmlrunner discover -s swagger_server.test -v
#    - coverage report 2>&1 | tee coverage.txt
#    - |
#      COVERAGE=$(tail -n1 coverage.txt | awk '{print $NF}')
#      echo "Code coverage: ${COVERAGE}"
#    - coverage xml
#  artifacts:
#    paths:
#      - TEST-*.xml
#      - coverage.xml
#    reports:
#      junit: TEST-*.xml
#      coverage_report:
#        coverage_format: cobertura
#        path: coverage.xml
#    expire_in: 1 week
#  rules:
#    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
#    - if: $CI_COMMIT_TAG

sonarcloud-check:
  stage: test
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Security scanning
semgrep-sast:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

# Base manifest publishing configuration
.publish_manifests_base:
  inherit:
    variables: false
  variables:
    TRIGGER_BRANCH: $CI_COMMIT_BRANCH
  trigger:
    project: monty-mobile1/esim/b2c/cd/esim-b2c-reseller-api-manifests
    branch: main
    strategy: depend
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH =~ /^v-\d+\.\d+\.x$/

# Development environment
publish_manifests_development:
  extends: .publish_manifests_base
  stage: publish_latest
  variables:
    ENVIRONMENT: development
  needs:
    - build_latest_image_and_publish
    - generate_latest_image_tag
  environment: development

# Template for staging/preprod/prod environments
.publish_manifests_template:
  extends: .publish_manifests_base
  stage: release_publish_stable
  needs:
    - build_stable_image_and_publish
    - bump_version_and_tag

# Staging environment
publish_manifests_staging:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: staging
  environment: staging

# Pre-production environment
#publish_manifests_preprod:
#  extends: .publish_manifests_template
#  variables:
#    ENVIRONMENT: preprod
#  needs:
#    - publish_manifests_staging
#  when: manual
#  environment: preprod

# Production environment
publish_manifests_prod:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: production
  needs:
    - publish_manifests_staging
  when: manual
  environment: production
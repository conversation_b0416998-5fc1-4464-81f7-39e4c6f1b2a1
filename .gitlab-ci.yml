variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task

stages:
  - test
  - functional_test
  - publish_latest
  - release_publish_stable

services:
  - docker:dind

include:
  - component: gitlab.com/monty-mobile1/r-d/pipeline-components/semantic-release-pipeline-component/tag-release-publish-python-esim-b2c@~latest
    inputs:
      bump-version-and-tag-needs: publish_manifests_development
  - template: Jobs/SAST.gitlab-ci.yml

# Security scanning
semgrep-sast:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

pylint:
  stage: test
  image: python:3.8
  before_script:
    - set -a
    - apt-get update -q -y
    - apt-get install -y python3-pip
    - ./tests/install_requirements.sh
    - mkdir -p public/badges public/lint
    - echo undefined > public/badges/$CI_JOB_NAME.score
    - python3 -m pip install pylint-gitlab debugpy==1.8.0
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  allow_failure: true
  script:
    - python -m pylint --exit-zero --output-format=text $(find . -type f -name "*.py"  ! -path "instance/*" ) > pylint.txt
    - sed -n 's/^Your code has been rated at \([-0-9.]*\)\/.*/\1/p' pylint.txt > public/badges/$CI_JOB_NAME.score
    - python -m pylint --exit-zero --output-format=pylint_gitlab.GitlabCodeClimateReporter $(find $CI_PROJECT_DIR -type f -name "*.py" ! -path "instance/*" ! -path "**/tests/**" ! -name "test_*.py" ) > codeclimate.json
    - python -m pylint --exit-zero --output-format=pylint_gitlab.GitlabPagesHtmlReporter $(find $CI_PROJECT_DIR -type f -name "*.py" ! -path "instance/*" ! -path "**/venv/**" ! -path "**/tests/**" ! -name "test_*.py" ) > public/lint/index.html
  after_script:
    - anybadge --overwrite --label test --value=$(cat public/badges/$CI_JOB_NAME.score) --file=public/badges/test.svg 4=red 6=orange 8=yellow 10=green
    - |
      echo "Your score is: $(cat public/badges/$CI_JOB_NAME.score)"
  artifacts:
    paths:
      - public
    reports:
      codequality: codeclimate.json
    when: always

sonarcloud-check:
  stage: test
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

functional_test:
  stage: functional_test
  allow_failure: false
  services:
    - mongo:5.0.17
  image: python:3.8
  before_script:
    - apt-get update -q -y
    - apt-get install -y python3-pip
    - ./tests/install_requirements.sh
    - source ./tests/env.test.sh
  variables:
    ENV: 'test'
    ENCRPYPT_KEY: $ENCRPYPT_UKEY
  script:
    - coverage run -m unittest discover tests.UnitTest
    - coverage xml -o coverage.xml
    - coverage report
  artifacts:
    paths:
      - coverage.xml
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG

# Base manifest publishing configuration
.publish_manifests_base:
  inherit:
    variables: false
  variables:
    TRIGGER_BRANCH: $CI_COMMIT_BRANCH
  trigger:
    project: monty-mobile1/esim/b2c/cd/esim-b2c-mng-manifests
    branch: main
    strategy: depend
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH =~ /^v-\d+\.\d+\.x$/

# Development environment
publish_manifests_development:
  extends: .publish_manifests_base
  stage: publish_latest
  variables:
    ENVIRONMENT: development
  needs:
    - build_latest_image_and_publish
    - generate_latest_image_tag
  environment: development

# Template for staging/prod environments
.publish_manifests_template:
  extends: .publish_manifests_base
  stage: release_publish_stable
  needs:
    - build_stable_image_and_publish
    - bump_version_and_tag

# Staging environment
publish_manifests_staging:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: staging
  environment: staging

# Pre-production environment
#publish_manifests_preprod:
#  extends: .publish_manifests_template
#  variables:
#    ENVIRONMENT: preprod
#  needs:
#    - publish_manifests_staging
#  when: manual
#  environment: preprod

# Production environment
publish_manifests_prod:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: production
  needs:
    - publish_manifests_staging
  when: manual
  environment: production
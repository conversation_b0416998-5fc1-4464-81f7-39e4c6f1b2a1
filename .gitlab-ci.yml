stages:
  - test
  - publish_latest
  - release_publish_stable

services:
  - docker:dind

include:
  - component: gitlab.com/monty-mobile1/r-d/pipeline-components/semantic-release-pipeline-component/tag-release-publish-python-esim-b2c@~latest
    inputs:
      bump-version-and-tag-needs: publish_manifests_development
  - template: Jobs/SAST.gitlab-ci.yml

# Security scanning
semgrep-sast:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

# Base manifest publishing configuration
.publish_manifests_base:
  inherit:
    variables: false
  variables:
    TRIGGER_BRANCH: $CI_COMMIT_BRANCH
  trigger:
    project: monty-mobile1/esim/b2c/cd/esim-b2c-stripe-api-manifests
    branch: main
    strategy: depend
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH =~ /^v-\d+\.\d+\.x$/

# Development environment
publish_manifests_development:
  extends: .publish_manifests_base
  stage: publish_latest
  variables:
    ENVIRONMENT: development
  needs:
    - build_latest_image_and_publish
    - generate_latest_image_tag
  environment: development

# Template for staging/preprod/prod environments
.publish_manifests_template:
  extends: .publish_manifests_base
  stage: release_publish_stable
  needs:
    - build_stable_image_and_publish
    - bump_version_and_tag

# Staging environment
publish_manifests_staging:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: staging
  environment: staging

# Pre-production environment
#publish_manifests_preprod:
#  extends: .publish_manifests_template
#  variables:
#    ENVIRONMENT: preprod
#  needs:
#    - publish_manifests_staging
#  when: manual
#  environment: preprod

# Production environment
publish_manifests_prod:
  extends: .publish_manifests_template
  variables:
    ENVIRONMENT: production
  needs:
    - publish_manifests_staging
  when: manual
  environment: production
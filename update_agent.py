import connexion
from app_helpers import mongodb
from swagger_server.__main__ import app_flask
from swagger_server.models.add_agent_request import AddAgentRequest

from Services.role_services.update_monty_admin_permissions_service import update_monty_admin_permissions
from Services.authorization_services.keyCloak import KeycloakHelper
from instance import consumer_config
user_pass = consumer_config.user_pass
from Services.agent_services.add_agent_service import add_agent
from bson import ObjectId

agent_request = {
    "username": "monty.adminnnn",
    "email": "<EMAIL>",
    "name": "administrator",
    "password": user_pass,
    "role_id": ObjectId('63c94aadd25669ef1072787c'),
    "is_active": True
}

keycloak_helper = KeycloakHelper()
keycloakAgentRequestDict = agent_request.copy()
user_id_in_keycloak = keycloak_helper.create_user(keycloakAgentRequestDict)
agent_request.pop("password", "")
agent_request["keycloak_id"] = user_id_in_keycloak
insertAgentResult = mongodb.insert_one("agent", agent_request,apply_Tenancy=0)
keycloakAgentRequestDict["user_id"] = str(insertAgentResult.inserted_id)
keycloak_helper.update_user(user_id_in_keycloak, keycloakAgentRequestDict)



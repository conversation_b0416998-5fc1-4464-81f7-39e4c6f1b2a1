from app_models import mobiles
from main import app

with app.app_context():
    app_user_details = mobiles.AppUserDetails.objects()
    for app_user_detail in app_user_details:
        if app_user_detail.fcm_token is not None and app_user_detail.fcm_token != "":
            device_data = mobiles.DeviceData(
                user_email=app_user_detail.user_email,
                fcm_token=app_user_detail.fcm_token,
                datetime =app_user_detail.create_date
            )
            device_data.save()



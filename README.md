## Project Name

esim-b2c-api-py

## Description

This service will give the subscriber an access to buy a bundle after logging in.
After payment, the client will get a QR code by email, scan it and install his profile.

## Installation
 
'''Steps to create Venv and activate it

''' install requirements file
#pip install -r "requirements.txt"

### How to run?

You can run the script from the command-line using
#python esim-b2c-api-py.py


## Usage

The project depends on the `keycloak` install it and 
''' Configure it by running standalone.bat file
''' Create account (admin)
''' Create realm, users, roles for users, groups 

and then go to postman 

### Contributing

You are welcome to contribute to the code via pull requests.

## Project status

waiting for requirements.


## DevOps Section
### Run Dev Environment
this part is to run your service locally on your machine to provide local testing before push on the repositories.
**Requirements**
- Docker
- Docker Compose

**Environment Variables**
In the Scripts dirs you can find *variables.sh* which contain all variables required to build your image:
REGISTRY_GITLAB_URL, HARBOR_REPOSITORY, CI_PROJECT_NAME, CD_VERSION, ENV, GIT_USERNAME, GIT_ACCESS_TOKEN

**Instance**
In the instance you can find *.env.dev* file to put all variables required in your consumer configuration. Please fill it for your dev environment. This file only on your local machine it's ignored from *.gitignore*.

**Scripts**
First you need to know that if you want to run script please make sure that you call the script from the root level of repository. **@Scripts/your_script.sh**
In the repository you will find *Scripts* directory that contain multiple scripts:
- build_image.sh: this script is to build the Dockerfile under *Deployment/Docker*.
- run_service.sh: this script will call the docker-compose.dev.yml and run the service with mongodb.
- stop_service.sh: this script will stop and remove the containers that run the service.
- variables.sh: this script will export you dev environment variables.

To run the service all you need to do is to build the image using *./Scripts/build_image.sh* and then *./Scripts/run_service.sh*

### Pipeline CI CD
This part is to integrate the Pipeline CI CD with the repository to deploy on stage and production environment. We Integrate Ansible to deploy.
**Build**
Build stage will build the Dockerfile under *Deployment/Docker* with arguments *GIT_USERNAME* and *GIT_ACCESS_TOKEN* to pull requirment from models repository.
**Security Scan**
Security stage contain job that communicate with sonarqube project to scan the code and dependency for any vulnerability.
**Publish**
This stage contain job to push the image container to Harbor with tag getted from *CI_VERSION* variable.
**Deploy**
Deploy stage contain 2 jobs: first to deploy on stage environment and the second is to deploy on production environment. Each job will take the variables necessary for the targeted environment deployment.

### Pipeline CI / CD Workflow
This section provide a clear image about the deployment process. we use Ansible to deploy the services because this is the simplest way and give power to deploy on multiple hosts.
In the *Deployment* folder we can see *Ansible* directory that contain (inventories, group_vars, templates and deploy.yml).
- **inventories:** Contains *HOSTS* to specify where the playbook shoulds run. In this file we can see 3 hosts: dev, stage, production. The hosts depend on the branch.
- **group_vars:** Contains all variables needed to run the playbook.
- **templates:** Contain *.env.j2* file that will rendered to configure the docker compose file.
- **deploy.yml:** This is our playbook that contain modules to configure and deploy our service using docker and docker compose.

#esim-b2c-reseller-py


This service enables resellers to purchase bundles after logging in. The payment is deducted from their wallet, and they
receive the profile details either via an API endpoint and through email.

## Table Of Contents

- [About The Project](#about-the-project)
- [Getting Started](#getting-started)
    - [Prerequisites](#prerequisites)
    - [Installation](#installation)
    - [Configuration](#configuration)
    - [Running the Application](#running-the-application)
- [Contributing](#contributing)
- [Project Status](#project-status)
- [Conclusion](#conclusion)

## About the project

The esim-b2c-reseller-py service is designed to facilitate the purchasing of bundles by resellers. After a successful 
login, resellers can use their wallets for payment, and the service will deliver the purchased profile details through 
an API or email. This project is part of a larger ecosystem and integrates with MongoDB and Keycloak for data storage 
and authentication respectively.

## Getting Started

### Prerequisites

Ensure that you have the following installed on your system:
- Python(3.8.x)
- MongoDB
- Keycloak(18.0.0)

### Installation

1. **Clone the Project Repository**:
```bash
$ <NAME_EMAIL>:monty-mobile1/esim/b2c/esim-b2c-reseller-py0.1.git
$ cd  esim-b2c-reseller-py0.1
```

2. **Create a Virtual Environment**:
```bash
$ python -m venv venv
$ source venv/bin/activate   # On Windows, use `venv\Scripts\activate`
```

3. **Install Dependencies**:
```bash
$ pip3 install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim-b2c-models-py.git
$ pip3 install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim_b2c_helpers.git
$ pip install -r requirements.txt
```

### Configuration Guide

1. **Set Up Keycloak**
  - Install Keycloak and create a new realm(or import from the file if available).
  - Set up a client within this realm.

2. **Configure MongoDB**:
  - Set up MongoDB and import any necessary backup data.
  - Ensure that the MongoDB service is running and accessible.

3. **Environment File**:
  - In the instance directory, create a `.env.{ENV}` file (e.g., `.env.test`, `.env.production`).
  - Include essential environment variables such as database connection strings, Keycloak configuration, and other project-specific settings.
  - Ensure all sensitive data is added in an encrypted format.

4. **System Environment Configuration:**
  - Set the system environment variable `ENV` to `test` (or the appropriate environment name).
  - Set `ENCRYPT_UKEY` in the system environment to the encryption key used for securing the environment variables.

5. **Admin Agent Setup and API Access**

   - **Create Admin Agent:**
     1. Review the `missing_permission.json` file to verify the necessary permissions.
     2. Cross-check with `sagger_Server.yaml` to ensure all required permissions are included.
     3. If any permissions are missing, add them to the `missing_permission.json.
     4. Run the following script to create an admin agent:
     ```
     python add_agent.py
     ```

   - **Access Swagger APIs:**
     1. Navigate to the Swagger UI using the following URL format:
     ```
     http://<host>:<port>/api/v0/ui
     ```
     2. Replace `<host>` and `<port>` with your server's host address and port number.

   - **Authenticate Using the Agent Login API:**
     1. Use the Agent Login API with the credentials you set up earlier to obtain an access token.
     2. This token is required for accessing other API endpoints.

   - **Update Permissions for the Admin Agent:**
      1. If any permissions have been updated or added, first add it to `missing_permission.json` then use use the `/Roles/UMAP` endpoint to update the permissions for the admin agent.
      2. This ensures the admin agent has the most current and complete set of permissions.
   

### Running the Application:

To start the application, use the following command:
```bash
$ python wsgi.py
```
The application should now be running and accessible. Make sure to verify the service by making sample API calls or 
accessing the relevant endpoints.

## Conclusion

The esim-b2c-reseller-py service is a robust tool for resellers, enabling seamless bundle purchases with wallet 
payment and profile delivery.
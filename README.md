## Project Name

*********************

## Description

It's a Model–view–controller (MVC) that shows the admin panel 
It shows all users details, app email verification, user bundle, user bundle logs, user iccid, etc..


## Installation
 
'''link to go through the project from gitLab
https://vasdev.montymobile.com/esim/*********************

'''Steps to get started:
#cd ********************* (folder of the project)
#git clone ((link))

'''Steps to create Venv and activate it
1. create a virtual environment (File, settings, python interpreter, create new env)
2. #cd env 
3. #cd Scripts
4. .\activate.bat

''' install requirements file
#pip install -r "requirements.txt"

### How to run?

You can run the script from the command-line using
#python add_user.py
#python *********************.py


## Usage

*Just click on ((http://host:port)) given in Command Prompt
*Login via email and password used in add_user.py 
*It may show Reset Password panel


### Contributing

You are welcome to contribute to the code via pull requests.

## Project status

Waiting for requirements



## DevOps Section
### Run Dev Environment
this part is to run your service locally on your machine to provide local testing before push on the repositories.
**Requirements**
- Docker
- Docker Compose

**Environment Variables**
In the Scripts dirs you can find *variables.sh* which contain all variables required to build your image:
REGISTRY_GITLAB_URL, HARBOR_REPOSITORY, CI_PROJECT_NAME, CD_VERSION, ENV, GIT_USERNAME, GIT_ACCESS_TOKEN

**Instance**
In the instance you can find *.env.local* file to put all variables required in your consumer configuration. Please fill it for your dev environment. This file only on your local machine it's ignored from *.gitignore*.

**Scripts**
First you need to know that if you want to run script please make sure that you call the script from the root level of repository. **@Scripts/your_script.sh**
In the repository you will find *Scripts* directory that contain multiple scripts:
- build_image.sh: this script is to build the Dockerfile under *Deployment/Docker*.
- run_service.sh: this script will call the docker-compose.local.yml and run the service with mongodb.
- stop_service.sh: this script will stop and remove the containers that run the service.
- variables.sh: this script will export you dev environment variables.

To run the service all you need to do is to build the image using *./Scripts/build_image.sh* and then *./Scripts/run_service.sh*

### Pipeline CI CD
This part is to integrate the Pipeline CI CD with the repository to deploy on stage and production environment. We Integrate Ansible to deploy.
**Build**
Build stage will build the Dockerfile under *Deployment/Docker* with arguments *GIT_USERNAME* and *GIT_ACCESS_TOKEN* to pull requirment from models repository.
**Security Scan**
Security stage contain job that communicate with sonarqube project to scan the code and dependency for any vulnerability.

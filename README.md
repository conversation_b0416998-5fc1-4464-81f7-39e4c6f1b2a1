# esim-b2c-mng-api-py

esim-b2c-mng-api-py contain list of API's will be used in Power BI portal

## Installation
### Using virtual environment :

1- activate virtual env 

```bash
python3 -m venv venv
source venv/bin/activate
```
2 - install libraries from requirement.txt

```bash
pip3 install -r "requirements.txt"
```

3 - force install for models using username and token
 ```bash
pip install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/esim/esim-b2c-models-py.git
```
### Using virtual docker file :
 
specify the values of GIT_USERNAME and GIT_ACCESS_TOKEN

 ```bash
sudo  docker build . -t registry.montymobile.com/esim/esim-b2c-mng-api-py:Beta --build-arg CACHEBUST="$(date +%s)" --build-arg GIT_USERNAME=hayat.bourgi --build-arg GIT_ACCESS_TOKEN=JE8xf9Ed9dyeRAJqz_gp-f -f Deployment/Docker/Dockerfile

docker-compose -f Deployment/Docker/docker-compose.yml up -d
```
## Dependencies

This service is using mongo database , keycloack for authentication, flask as python framework

## Usage

for the first time please run add_user.py to create first user


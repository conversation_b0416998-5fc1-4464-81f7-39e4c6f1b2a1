#!/usr/bin/env python3
import os
import logging
import sys

# Import our logging configuration
import logging_config

# Create a logger for this script
logger = logging.getLogger(__name__)

def test_logging_levels():
    """Test all logging levels to verify configuration"""
    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")

if __name__ == "__main__":
    # Show the current log level
    print(f"Current log level: {logging.getLogger().getEffectiveLevel()}")
    print(f"Log level name: {logging.getLevelName(logging.getLogger().getEffectiveLevel())}")
    
    # Test all logging levels
    test_logging_levels()
    
    print("\nTo change the log level, run:")
    print("LOG_LEVEL=DEBUG python test_logging.py")

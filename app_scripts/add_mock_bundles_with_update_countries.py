#!/usr//venv3.7/bin/python
import sys
sys.path.append('/')
from app_helpers import db_helper
from app_extra import app
from instance import consumer_config as instance_config
from app_models import mobiles, consumer_models


def get_iso3(iso2_code):
    country = consumer_models.Countries.objects(iso2_code__contains=iso2_code).first()

    if country:
        return country
    else:
        return False

def get_iso2(country_code):
    country= consumer_models.Countries.objects(iso3_code=country_code).first()

    if country:
        return country
    else:
        return False


def update_country(country, list_countries, list_countries_code, country_name_dic, country_code_dic, mock=False):
    if   mock:
            country_code = get_iso2(country)
            if country_code != 'ISR':
                list_countries.append(country_code['country_name'].strip())
                list_countries_code.append(country_code.iso3_code.strip())
                country_name_dic[country] = country_code['country_name'].strip()
                country_code_dic[country] = country

    elif country['iso'] in country_code_dic:
        list_countries.append(country_name_dic[country])
        list_countries_code.append(country_code_dic[country])
    else:
            country_code = db_helper.get_iso3(country['iso'])
            if country_code and country_code.iso3_code != 'ISR':
                list_countries.append(country_code['country_name'].strip())
                list_countries_code.append(country_code.iso3_code.strip())
                country_name_dic[country['iso']] = country_code['country_name'].strip()
                country_code_dic[country['iso']] = country_code.iso3_code.strip()

    return list_countries, list_countries_code, country_name_dic, country_code_dic


with app.app_context():
    sys.path.append('/')
    try:
        for db in instance_config.lst_db_:
            bundles = db_helper.get_bundles_by_vendor('eSIMGoMock')
            for bundle in bundles:
                list_countries=[]
                list_countries_code=[]
                country_name_dic={}
                country_code_dic={}

                for country in bundle.country_code_list:

                    list_countries, list_countries_code, country_name_dic, country_code_dic = update_country(
                        country, list_countries, list_countries_code, country_name_dic, country_code_dic, mock=True)
                bundle['country_list'] = list_countries
                bundle['country_code_list'] = list_countries_code
                bundle.save()


    except Exception as e:
        pass

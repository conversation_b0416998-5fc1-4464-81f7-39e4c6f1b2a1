#!/usr/local/pyapps/venv3.7/bin/python
import sys
import requests
sys.path.append('../')
from app_helpers.script_helper import get_token_flexiroam
from app_helpers.db_helper import add_profiles, check_iccid
from datetime import datetime
from instance import consumer_config as instance_config
from app_extra import app


with app.app_context():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)
        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            payload = {'availability': 0,"sim_type":"eSIM"}
            r = requests.post('{}/product/inventory/view/v1'.format(instance_config.flexiroam_url), headers=headers,
                              data=payload)

            if  r.status_code==200:
                response = r.json()
                if response:

                    for profile in response['data']:
                        found_iccid = check_iccid(profile["iccid"])
                        if not found_iccid:
                            new_profile = {
                                "vendor_name":"Flexiroam",
                                "sku":profile["sku"],
                                "iccid":profile["iccid"],
                                "qr_code_value":profile["qr_code_value"],
                                "profile_names":profile["profile_names"],
                                "last_connection":profile["last_connection"],
                                "installation_status":profile["installation_status"]
                            }

                            result = add_profiles(new_profile)

    except Exception as e:
            print("exception ", str(e))


#!/usr//venv3.7/bin/python
import sys
sys.path.append('/')
from main import app
from app_models import consumer_models
from b2c_helpers.constaints import MONTYMOBILE_VENDOR
from mongoengine.queryset.visitor import Q

with app.app_context():
    sys.path.append('/')
    consumer_models.Bundles.objects().update(set__allocate_profiles=True)

    query = Q(vendor_name__ne=MONTYMOBILE_VENDOR) & Q(vendor_name__ne="Flexiroam")
    consumer_models.Bundles.objects(query).update(set__profile_names="")


    

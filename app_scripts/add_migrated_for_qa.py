#!/usr//venv3.7/bin/python
import sys
sys.path.append('/')
from main import app
from app_models import consumer_models, reseller_models

with app.app_context():
    sys.path.append('/')

    user_bundle_log =consumer_models.UserBundleLog.objects()
    order_history= reseller_models.Order_history.objects()

    for order in order_history:
        for user_bundle in user_bundle_log:
            if order.order_number == user_bundle.order_number:
                order.update(set__migrated=True)
                user_bundle.update(set__migrated=True)

import logging
from app_models.consumer_models import Bundles
from app_main import app

with app.app_context():
    puerto_name = "Puerto Rico"    # Case sensitive
    puerto_iso3 = "PRI"    # Case sensitive
    logging.info("Removing Puerto Rico from eSIMGo country codes and adding PRI to missing country codes")
    #   get all esimgo bundles
    bundles = Bundles.objects(vendor_name="eSIMGo")
    for bundle in bundles:
        try:
            #   flag to log bundle if it was modified
            bundle_modified = False
            logging.info(f"trying to modify bundle {bundle.bundle_code} with {len(bundle.country_list)} countries")
            #   Extract lists from object
            country_list = bundle.country_list
            country_code_list = bundle.country_code_list
            missing_country_codes_list = bundle.missing_country_code
            if puerto_name in country_list:
                bundle_modified = True
                country_list.remove(puerto_name)
            if puerto_iso3 in country_code_list:
                update = True
                country_code_list.remove(puerto_iso3)
            if puerto_iso3 not in missing_country_codes_list:
                update = True
                missing_country_codes_list.append(puerto_iso3)
            bundle.update(set__country_list=country_list,
                          set__country_code_list=country_code_list,
                          set__missing_country_code=missing_country_codes_list)
            if bundle_modified:
                logging.info(f"{bundle.bundle_code} modified successfully now has {len(bundle.country_list)} countries")
        except Exception as exception:
            logging.info(f"{bundle.bundle_code} exception while trying to modify as {exception}")
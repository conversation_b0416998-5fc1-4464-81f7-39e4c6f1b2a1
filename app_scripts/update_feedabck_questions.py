import sys

sys.path.append('/')
from app_models import main_models
from main import app

with app.app_context():
    sys.path.append('/')
    highest_question_code = 0
    questions = [
        "How satisfied are you with your experience using Monty eSIM for your recent trip?",
        "How was the network coverage and connectivity quality during your trip?",
        "How satisfied were you with the customer support provided by MontyeSIM?",
        "How was your experience when downloading and installing Monty eSIM app on your device?",
        "How likely are you to recommend MontyeSIM to a friend?"
    ]
    for question_text in questions:
        highest_question_code += 1
        # for production
        new_question = main_models.Question.objects(status=True, question_text=question_text).update(
            set__question_code=highest_question_code, set__language_code="en")

        # for Qa and Local
        if not new_question:
            questions = [
                {
                    "question_text": "How satisfied are you with your experience using Monty eSIM for your recent trip?",
                    "order": 1,
                    "status": True,
                    "question_code": 1,
                    "language_code":"en"
                },
                {
                    "question_text": "How was the network coverage and connectivity quality during your trip?",
                    "order": 2,
                    "status": True,
                    "question_code": 2,
                    "language_code": "en"
                },
                {
                    "question_text": "How satisfied were you with the customer support provided by MontyeSIM?",
                    "order": 3,
                    "status": True,
                    "question_code": 3,
                    "language_code": "en"
                },
                {
                    "question_text": "How was your experience when downloading and installing Monty eSIM app on your device?",
                    "order": 4,
                    "status": True,
                    "question_code": 4,
                    "language_code": "en"
                },
                {
                    "question_text": "How likely are you to recommend MontyeSIM to a friend?",
                    "order": 5,
                    "status": True,
                    "question_code": 5,
                    "language_code": "en"
                }
            ]

            for question_data in questions:
                new_question = main_models.Question(**question_data)
                new_question.save()

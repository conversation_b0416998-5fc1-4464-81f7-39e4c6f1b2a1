#!/usr//venv3.7/bin/python
from app_models.consumer_models import UserBundleLog, UserIccid, Bundles, Profiles
from app_models.reseller_models import Order_history, BundleData
from app_models.main_models import HistoryLogs
from main import app

with app.app_context():
    count = 0
    data = Order_history.objects()
    for order in data:
        if hasattr(order, 'reseller_type') and order.reseller_type == "subscriber":
            order.delete()
            count += 1

    print(f"Deleted {count} Order objects where reseller_type is 'subscriber'")

import logging

from mongoengine import Q

from main import app
from app_models.consumer_models import Bundles
with app.app_context():
    filter = Q(bundle_marketing_name__startswith=" ") | Q(bundle_marketing_name__endswith=" ")
    bundles = Bundles.objects(filter)
    logging.info(f"Updating {bundles.count()} bundles")
    for bundle in bundles:
        logging.info(f"Trying to update bundle marketing name of bundle {bundle.bundle_code}")
        bundle_marketing_name = bundle.bundle_marketing_name
        stripped_bundle_marketing_name = bundle_marketing_name.strip()
        logging.info(f"marketing name : {bundle_marketing_name} -----> {stripped_bundle_marketing_name}")
        if bundle.update(set__bundle_marketing_name=stripped_bundle_marketing_name):
            logging.info(f"success for: {bundle.bundle_code}")

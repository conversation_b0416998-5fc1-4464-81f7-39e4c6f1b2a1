#!/usr/local/pyapps/venv3.7/bin/python
import sys
import requests
sys.path.append('../')
from app_helpers.script_helper import get_token_flexiroam
from app_helpers.db_helper import add_bundle, check_bundle
from datetime import datetime
from instance import consumer_config as instance_config
from app_extra import app

def get_bundle_name(list_countries, bundle):
    now = datetime.utcnow()  # current date and time
    amount = ('%f' % float(bundle["plan"][0]["data_amount"])).rstrip('0').rstrip('.')
    if len(list_countries) > 1:
        if bundle["plan"][0]['category_id'] == "2":
            bundle_name = "Global_bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global_bundle" + "_" + now.strftime("%m%d%Y%H%M%S")
        else:

            bundle_name = bundle["plan"][0]["policy"][0]["policy_name"] + " " + str(amount) + bundle["plan"][0][
                "data_unit"]
            bundle_code = bundle["plan"][0]["policy"][0]["policy_name"] + "_" + now.strftime("%m%d%Y%H%M%S")
    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S")
    return bundle_name, bundle_code

def get_country_bundle_name(list_countries, bundle):
    now = datetime.now()  # current date and time
    amount = ('%f' % float(bundle["plan"][0]["data_amount"])).rstrip('0').rstrip('.')
    if len(list_countries) > 1:
        if bundle["plan"][0]['category_id']=="2":
            bundle_name = "Global bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global bundle" + "_" + now.strftime("%m%d%Y%H%M%S")
        else:

            bundle_name = bundle["plan"][0]["policy"][0]["policy_name"] + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = bundle["plan"][0]["policy"][0]["policy_name"] + "_" + now.strftime("%m%d%Y%H%M%S")
    else:


        bundle_name = list_countries[0] + " " + str(amount ) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S")
    return bundle_name, bundle_code

with app.app_context():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)
        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            payload = {'group_by_offering': 'yes'}
            r = requests.post('{}/plan/inventory/view/v1'.format(instance_config.flexiroam_url), headers=headers,
                              data=payload)
            bundles = r.json()
            if bundles:
                bundle_list = []
                list_countries_code = []
                for bundle in bundles['data']:

                    bundle_exist = False
                    bundle_exist = check_bundle(bundle['plan'][0]['plan_code'], 'Flexiroam')
                    print("bundle_exist ", bundle_exist)
                    if bundle_exist:
                        print("line 28 ")
                    else:
                        list_countries = []
                        new_bundle={
                            "vendor_name":"Flexiroam",
                            "bundle_name": bundle['title'],
                            "bundle_code": bundle['plan'][0]['plan_code'],
                            "bundle_vendor_name":bundle['title'],
                            "bundle_vendor_code":bundle['plan'][0]['plan_code'],
                            "supplier_vendor":bundle['vendor_code'],
                            "unit_price":bundle["unit_price"],
                            "data_amount":bundle["plan"][0]["data_amount"],
                            "fullspeed_data_amount":bundle["plan"][0]["fullspeed_data_amount"],
                            "data_unit":bundle["plan"][0]["data_unit"],
                            "validity_amount":bundle["plan"][0]["validity_amount"],

                        }

                        bundle_duration = 1


                        if bundle['plan_start_duration_unit']=='MONTH':
                            bundle_duration=30

                        new_bundle['bundle_duration']=bundle_duration
                        countries = bundle["plan"][0]['policy'][0]['policy_attributes'][0]['Country']
                        list_countries = [d['name'] for d in countries if 'name' in d]

                        list_countries_code = [d['value'] for d in countries if 'value' in d]
                        bundle_name, bundle_code = get_bundle_name(list_countries, bundle)

                        new_bundle['bundle_name'] = bundle_name

                        new_bundle['bundle_code'] = bundle_code


                        new_bundle['is_region'] = False
                        new_bundle["bundle_category"] = "region"
                        if int(bundle["plan"][0]["category_id"])==1:
                            new_bundle["category_name"] = "region_"
                            new_bundle['is_region']=True
                            new_bundle["bundle_category"]="region"

                        elif int(bundle["plan"][0]["category_id"]) == 2:
                            new_bundle['is_region'] = True
                            new_bundle["bundle_category"] = "global"
                            new_bundle["category_name"] = "global"

                        elif int(bundle["plan"][0]["category_id"]) == 3:
                            new_bundle['is_region'] = True
                            new_bundle["category_name"] = "country_"+list_countries_code[0]
                            new_bundle["bundle_category"]="country"

                        new_bundle['is_active']=True
                        new_bundle['country_list']=list_countries
                        new_bundle['country_code_list'] = list_countries_code

                        result = add_bundle(new_bundle)


    except Exception as e:
            print("exception ", str(e))


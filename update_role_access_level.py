import pymongo
from instance import consumer_config

# Set up the MongoDB connection details
database_name = consumer_config.decrypted_db_name_alias
mongo_db_url = consumer_config.new_host_
myclient = pymongo.MongoClient(mongo_db_url)

# Access the specified database and collection
database = myclient[database_name]
collection = database["roles"]

# Update specific records where the `name` field is "ResellerViewOrdersOnlyWithoutSomeColumns"
# Set the `access_level` field to "basic" for these records
collection.update_many(
    {"name": {"$eq": "ResellerViewOrdersOnlyWithoutSomeColumns"}},
    {"$set": {"access_level": "basic"}}
)

# Update specific records where the `name` field is "ResellerViewOrdersOnly"
# Set the `access_level` field to "medium" for these records
collection.update_many(
    {"name": {"$eq": "ResellerViewOrdersOnly"}},
    {"$set": {"access_level": "medium"}}
)

# For all other records (where `name` is neither "ResellerViewOrdersOnly" nor
# "ResellerViewOrdersOnlyWithoutSomeColumns"), set the `access_level` field to "sensitive"
collection.update_many(
    {"name": {"$nin": ["ResellerViewOrdersOnly", "ResellerViewOrdersOnlyWithoutSomeColumns"]}},
    {"$set": {"access_level": "sensitive"}}
)

#!/usr//venv3.7/bin/python
import sys
from main import app
from app_models import reseller_models
from mongoengine.queryset.visitor import Q
from instance import consumer_config
import inspect
from b2c_helpers.vendors import *


with app.app_context():
    sys.path.append("/")
    tenant_name = consumer_config.mongo_alias
    vendor_list = []
    for e in Vendors.objects():
        vendor_name = e.vendor_name
        vendor_list.append(vendor_name)

    active_vendors_list = vendor_list
    vendors_for_balance_deduction_list = vendor_list
    reseller_models.Reseller.objects().update(
        set__tenant_name=tenant_name,
        set__active_vendors_list=active_vendors_list,
        set__vendors_for_balance_deduction_list=vendors_for_balance_deduction_list,
    )
    reseller_models.Reseller.objects(
        Q(reseller_category__exists=False) | Q(reseller_category="")
    ).update(set__reseller_category="Enterprise")

    updated_records_count = reseller_models.Reseller.objects().count()
    print(
        f"Total Updated Records for reseller_type = reseller: {updated_records_count}"
    )

# logging_config.py
from logging.config import dictConfig
import os
import logging
try:
    dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {"standard": {"format": "[%(asctime)s] [%(process)d] [%(levelname)s] <%(name)s>: %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S %z"}},
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": os.getenv("LOG_LEVEL", "INFO"),
                    "formatter": "standard",
                    "stream": "ext://sys.stdout",
                },
                "file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": os.getenv("LOG_LEVEL", "INFO"),
                    "formatter": "standard",
                    "filename": "instance/app.log",
                    "maxBytes": 10485760,
                    "backupCount": 5,
                    "encoding": "utf8",
                },
            },
            "root": {"level": "INFO", "handlers": ["console", "file"]},
        }
    )
except Exception as e:
    print(f"Logging error {e}, will not use rotating file handler")
    logging.basicConfig(level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S %z", format="[%(asctime)s] [%(process)d] [%(levelname)s] <%(name)s>: %(message)s")
    print(f"Normal logging configured after error")

# We can add this test line to verify the configuration works
logging.info("Logging configuration loaded")

#!/bin/bash


source "$(dirname "$(realpath "$0")")/../variables.sh" "$@" --defaultUsage

RUN_MONGO_SCRIPT="$(dirname "$(realpath "$0")")/run_mongodb.sh"

echo "$RUN_MONGO_SCRIPT"

sudo docker stop mongodb-esim-b2c-admin-web-0 mongodb-esim-b2c-admin-web-1 mongodb-esim-b2c-admin-web-2 mongodb-replicator-esim-b2c-admin-web > /dev/null 2>&1
sudo docker rm mongodb-esim-b2c-admin-web-0 mongodb-esim-b2c-admin-web-1 mongodb-esim-b2c-admin-web-2 mongodb-replicator-esim-b2c-admin-web > /dev/null 2>&1
sudo docker volume rm mongo-esim-b2c-admin-web-0 mongo-esim-b2c-admin-web-1 mongo-esim-b2c-admin-web-2 > /dev/null 2>&1

chmod +x "$RUN_MONGO_SCRIPT"
"$RUN_MONGO_SCRIPT"


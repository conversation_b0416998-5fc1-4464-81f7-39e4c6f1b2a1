export REGISTRY_GITLAB_URL="registry.montymobile.com"
export HARBOR_REPOSITORY="esim"
export CI_PROJECT_NAME="esim-b2c-mng-py"
export CD_VERSION="0.0.0"
export ENV="dev"
export DEBUG="False"
export GIT_USERNAME="esim-b2c-models-bot"
export GIT_ACCESS_TOKEN="**************************"

export HARBOR_USERNAME="dockeresim"
export HARBOR_PASSWORD="b9IfmZKmodrNlVuSoHXALA9HmAkkdnQi"

SCRIPTS_DIR=$(dirname "$(realpath "${BASH_SOURCE[0]}")")
ROOT_DIR=$(dirname "$SCRIPTS_DIR")
export DEPLOYMENT_DIR="$ROOT_DIR/Deployment"
export DOCKER_DIR="$DEPLOYMENT_DIR/Docker"
export MONGO_DOCKER_DIR="$DOCKER_DIR/Mongo"

export GIT_HELPERS_USERNAME="esim_b2c_helpers-bot"
export GIT_HELPERS_ACCESS_TOKEN="**************************"
export GIT_HELPERS_VERSION_TAG="master"
#!/bin/bash

source Scripts/variables.sh

${3-sudo} docker build . -t $REGISTRY_GITLAB_URL/$HARBOR_REPOSITORY/$CI_PROJECT_NAME:Beta --build-arg CACHEBUST="$(date +%s)" --build-arg GIT_HELPERS_USERNAME=$GIT_HELPERS_USERNAME --build-arg GIT_HELPERS_ACCESS_TOKEN=$GIT_HELPERS_ACCESS_TOKEN --build-arg GIT_HELPERS_VERSION_TAG=$GIT_HELPERS_VERSION_TAG --build-arg GIT_USERNAME=$GIT_USERNAME --build-arg GIT_ACCESS_TOKEN=$GIT_ACCESS_TOKEN --build-arg PORT=$PORT -f Deployment/Docker/Dockerfile --label "app=esim-b2c"
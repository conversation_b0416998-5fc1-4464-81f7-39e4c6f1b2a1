import json
import time
from concurrent.futures import <PERSON><PERSON>oolExecutor

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select, WebDriverWait


def chunk_list(lst, chunk_size):
    """Split a list into chunks of specified size"""
    return [lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size)]


def process_bundles():
    # Your original function remains unchanged
    driver = webdriver.Chrome()
    wait = WebDriverWait(driver, 2)

    try:
        main_url = "http://localhost:5006"
        main_url = "https://admin.montyesim.com"
        driver.get(main_url)
        first_time = True

        email: WebElement = driver.find_element(by=By.XPATH, value='//*[@id="email"]')
        password: WebElement = driver.find_element(by=By.XPATH, value='//*[@id="password"]')
        submit: WebElement = driver.find_element(by=By.XPATH, value='//*[@id="submit"]')
        email.send_keys("<EMAIL>")
        password.send_keys("R@ch@ad!123456")
        # email.send_keys("<EMAIL>")
        # password.send_keys("Q@Acc0unnt@123")
        submit.click()

        driver.get(f"{main_url}/country-bundles/?flt0_21=0gb&flt1_7=esimgo&flt2_183=1&flt3_28=ulp")

        while True:
            # Get all rows in the table
            rows = driver.find_elements(By.CSS_SELECTOR, "table.model-list tbody tr")
            if not rows:
                print("No records found.")
                break

            for i in range(1000):
                try:
                    # Click the edit button in the row
                    edit_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[4]/table/tbody/tr[1]/td[2]/a")
                    edit_button.click()

                    # Wait for the edit form to load
                    wait.until(
                        EC.presence_of_element_located((By.XPATH, "/html/body/div/form/div[3]/div/textarea"))
                    )  # Replace with the actual ID or selector of the "bundle name" field

                    # Find the "bundle name" field and update its value
                    bundle_name_field = driver.find_element(
                        By.XPATH, "/html/body/div/form/div[3]/div/textarea"
                    )  # Replace with the actual ID
                    bundle_name = bundle_name_field.text
                    bundle_name = bundle_name.replace("0GB", " Unlimited")
                    bundle_name_field.clear()
                    bundle_name_field.send_keys(bundle_name)

                    # Click the save button
                    save_button = driver.find_element(
                        By.XPATH, "/html/body/div/form/div[39]/div/input[1]"
                    )  # Replace with the actual selector for the save button
                    save_button.click()

                    # Wait for the list view to reload
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "table.model-list")))

                except Exception as e:
                    print(f"Error processing row: {e}")
                    continue

            # Check if there is a next page and navigate to it
            try:
                next_page = driver.find_element(By.CSS_SELECTOR, "a[rel='next']")  # Replace with the actual selector for the "next" button
                next_page.click()
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "table.model-list")))
            except Exception as e:
                print("No more pages.")
                break

        # Close the browser
        driver.quit()

    except Exception as e:
        print(f"An error occurred: {str(e)}")

    finally:
        driver.quit()


def process_batch(batch):
    """Process a single batch of ICCIDs"""
    process_bundles(batch)
    return f"Completed processing batch of {len(batch)} ICCIDs"


if __name__ == "__main__":
    # List of ICCIDs to process

    # Process batches in parallel using ProcessPoolExecutor
    process_bundles()

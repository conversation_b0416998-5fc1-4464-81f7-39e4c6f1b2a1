import logging
from main import app
from app_models import consumer_models as cm
from app_models import reseller_models as rm

with app.app_context():
    bundles =  cm.Bundles.objects()
    for bundle in bundles:
        order_history = rm.Order_history.objects(reseller_type = "subscriber", bundle_code=bundle.bundle_code,  order_status= "Successful")
        for history in order_history:
            history.update(
            set__bundle_data__vendor_name=bundle.vendor_name,
            set__bundle_data__bundle_vendor_code=bundle.bundle_vendor_code,
            set__bundle_data__unlimited=bundle.unlimited)

    logging.info("Updated fields to order history ")

from app_models import mobiles as mb
import pymongo
import app_main
from typing import List, Dict
from pymongo.collection import Collection
from pymongo.database import Database

BATCH_SIZE = 5000

collections_with_no_user_ref = [
    'callback_history',
    'languages',
    'countries',
    'branch',
    'mcc_mnc',
    'keycloack_settings',
    'generate_csv',
    'update_bundle_version',
    'transaction_logs',
    'network_list',
    'covered_areas',
    'daily_scripts',
    'currency_codes',
    'app_version_list',
    'vendors',
    'transaction_history',
    'redeem_codes',
    'app_list',
    'page',
    'outlook_subscription',
    'default_roles',
    'IssueReports',
    'super_admin_token',
    'bundle_details',
    'bundles',
    'messages',
    'question',
    'runnable_scripts',
    'regions',
    'reseller_bundle_price',
    'roles',
    'promo_code',
    'country_version',
    'faq',
    'answer',
    'bundle_message_translation'
]


def get_db_connection(consumer_config) -> Database:
    mongo_client = pymongo.MongoClient(consumer_config.new_host_)
    return mongo_client.get_database(consumer_config.mongo_db_name_alias)


def get_collections_to_update(db: Database, exclude_list: List[str]) -> List[Dict]:
    return list(db.list_collections(filter={"name": {'$nin': exclude_list}}))


def update_collection(collection: Collection, users: List[mb.AppUserDetails]) -> None:
    bulk_operations = []
    for user in users:
        if not user.user_email:
            continue
        bulk_operations.append(pymongo.UpdateMany(
            {
                "$or": [
                    {"email": user.user_email},
                    {"user_email": user.user_email},
                    {"client_email": user.user_email}
                ],
                "subscriber_id_added": {"$ne": True}
            },
            {
                "$set": {
                    "subscriber_id": user.id,
                    "subscriber_id_added": True
                }
            }
        ))
    if bulk_operations:
        collection.bulk_write(bulk_operations, ordered=False)


def add_sub_id_to_collections():
    db = get_db_connection(app_main.instance_config)
    collections_to_update = get_collections_to_update(db, collections_with_no_user_ref)

    for i in range(0, mb.AppUserDetails.objects.count(), BATCH_SIZE):
        users = mb.AppUserDetails.objects[i:i + BATCH_SIZE]
        for collection_info in collections_to_update:
            collection = db.get_collection(collection_info["name"])
            print(f"Updating collection: {collection.name}")
            update_collection(collection, users)

        # Mark users as processed
        mb.AppUserDetails.objects(id__in=[user.id for user in users]).update(subscriber_id_added=True)


def handle_special_collections():
    db = get_db_connection(app_main.instance_config)

    shared_profiles = db.get_collection("shared_profiles")
    reward_history = db.get_collection("reward_history")

    for collection in [shared_profiles, reward_history]:
        bulk_operations = []
        for record in collection.find({"subscriber_id_added": {"$ne": True}}):
            if collection.name == "shared_profiles":
                issued_by = mb.AppUserDetails.objects(user_email=record.get("issued_by")).first()
                claimed_by = mb.AppUserDetails.objects(user_email=record.get("claimed_by")).first()
                bulk_operations.append(pymongo.UpdateOne(
                    {"_id": record["_id"]},
                    {"$set": {
                        "issued_by_id": issued_by.id if issued_by else None,
                        "claimed_by_id": claimed_by.id if claimed_by else None,
                        "subscriber_id_added": True
                    }}
                ))
            elif collection.name == "reward_history":
                from_user = mb.AppUserDetails.objects(user_email=record.get("from_user_email")).first()
                to_user = mb.AppUserDetails.objects(user_email=record.get("to_user_email")).first()
                bulk_operations.append(pymongo.UpdateOne(
                    {"_id": record["_id"]},
                    {"$set": {
                        "from_user_email_id": from_user.id if from_user else None,
                        "to_user_email_id": to_user.id if to_user else None,
                        "subscriber_id_added": True
                    }}
                ))

        if bulk_operations:
            collection.bulk_write(bulk_operations, ordered=False)


with app_main.app.app_context():
    add_sub_id_to_collections()
    handle_special_collections()
from app_models import consumer_models
from main import app
from mongoengine.queryset.visitor import Q

with app.app_context():
    for vendor in consumer_models.Vendors.objects:

        if vendor.vendor_name in ['eSIMGo', 'Vodafone', 'Indosat']:
            vendor.vendor_expiry_days = 365
        elif vendor.vendor_name in ['Monty Mobile', 'Monty Reseller']:
            vendor.vendor_expiry_days = 0
        elif vendor.vendor_name == 'Flexiroam':
            vendor.vendor_expiry_days = 90

        vendor.vendor_start_bundle = True if vendor.vendor_name == 'Monty Mobile' else False

        vendor.vendor_expiry_date_profile = vendor.number_of_expiry_days if vendor.apply_expiry else 0

        vendor.save()
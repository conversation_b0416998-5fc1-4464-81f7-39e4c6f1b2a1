import pymongo
from app_models.tenant_models import TenantSettings
from bson import ObjectId
from pymongo.cursor import Cursor
from pymongo.database import Database
from pymongo import UpdateOne
from pymongo.collection import Collection

import app_main

BATCH_SIZE = 5000


def get_db_connection(consumer_config) -> Database:
    mongo_client = pymongo.MongoClient(consumer_config.new_host_)
    return mongo_client.get_database(consumer_config.decrypted_db_name_alias)


def migrate_documents(source_collection: Collection, destination_collection: Collection) -> int:
    total_added = 0
    last_id: [ObjectId, None] = None
    for tenant in TenantSettings.objects():
        tenant_id: ObjectId = tenant.id
        while True:
            query: dict = {} if last_id is None else {'_id': {'$gt': last_id}}
            cursor: Cursor = source_collection.find(query).limit(BATCH_SIZE).sort('_id', 1)
            batch = list(cursor)
            if not batch:
                break

            last_id = batch[-1]['_id']

            # Use update with upsert instead of insert
            bulk_ops = [UpdateOne(
                filter={
                    "bundle_code": doc.get("bundle_code"),
                    "tenant_id": tenant_id
                },
                update={
                    "$setOnInsert": {
                        "bundle_code": doc.get("bundle_code"),
                        "tenant_id": tenant_id,
                        "retail_price": doc.get("retail_price"),
                        "bundle_marketing_name": doc.get("bundle_marketing_name"),
                        "bundle_name": doc.get("bundle_name")
                    }
                },
                upsert=True
            ) for doc in batch]

            result = destination_collection.bulk_write(bulk_ops, ordered=False)
            total_added += result.upserted_count

    return total_added


def add_bundle_details_to_tenant():
    tenant_id: str

    db = get_db_connection(app_main.instance_config)
    tenant_bundle_details_collection: Collection = db.get_collection("tenant_bundle_details")
    bundle_collection: Collection = db.get_collection("bundles")
    migrated = migrate_documents(bundle_collection, tenant_bundle_details_collection)
    print("migrated", migrated, "documents from bundles to tenant_bundle_details")


with app_main.app.app_context():
    add_bundle_details_to_tenant()

"""
This is a fix for the VodafoneWebhookHelper class in b2c_helpers/webhook_helpers.py.
The issue is that when handling the "Cooldown" notification type, it's currently updating
the profile's status to False, but it should also update the availability to "Expired".

To apply this fix:
1. Find the VodafoneWebhookHelper class in b2c_helpers/webhook_helpers.py
2. Find both occurrences of the "Cooldown" notification type handler
3. Replace:
   profile.update(set__status=False)
   
   With:
   profile.update(set__status=False, set__availability="Expired")
"""

# First occurrence (around line 899):
"""
elif notification_type == "Cooldown":
    order_history.update(set__plan_status="Expired")
    profile_msisdn_or_iccid_query = Q(msisdn=msisdn) | Q(iccid=iccid)
    if profile := Profiles.objects(profile_msisdn_or_iccid_query).first():
        days_ago = (datetime.datetime.utcnow() - profile.create_datetime).days
        logger.info("Vodafone profile %s was created %s days ago and is expired completely, disabling...",
                    msisdn, days_ago)
        profile.update(set__status=False, set__availability="Expired")  # <-- Updated line
    else:
        error = f"Couldn't find profile for msisdn {msisdn} or iccid {iccid}"
        logger.error(error)
        raise ValueError(error)
"""

# Second occurrence (around line 1026):
"""
elif notification_type == "Cooldown":
    # user_iccid.update(set__status="expired")
    profile_msisdn_or_iccid_query = Q(msisdn=msisdn) | Q(iccid=order.iccid)
    if profile := Profiles.objects(profile_msisdn_or_iccid_query).first():
        days_ago = (datetime.datetime.utcnow() - profile.create_datetime).days
        logger.info("Vodafone profile %s was created %s days ago and is expired completely, disabling...", msisdn, days_ago)
        profile.update(set__status=False, set__availability="Expired")  # <-- Updated line
    else:
        error = f"Couldn't find profile for msisdn {msisdn} or iccid {order.iccid}"
        logger.error(error)
        raise ValueError(error)
"""

import json

import requests

# Test the vlimit-notification endpoint with a Cooldown notification
url = "https://esim-b2c-api-staging.montylocal.net/vlimit-notification"

payload = {
    "idType": "msisdn",
    "idValue": "42961756096807648824", 
    "notificationType": "Cooldown",
    "executionDate": "2023-05-03T13:11:42.246Z",
    "cause": "Usage of bundle exceeds low usage limit."
}

headers = {
    'Content-Type': 'application/json'
}

response = requests.post(url, headers=headers, data=json.dumps(payload))

print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")

# After sending the notification, you should check the profile in the database
# to verify that both status=False and availability="Expired" are set correctly

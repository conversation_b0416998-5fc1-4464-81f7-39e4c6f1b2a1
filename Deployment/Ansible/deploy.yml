---

- name: Deploy to "{{ ENV }}"
  hosts: "{{ ENV }}"
  tasks:
    - name: Create Service Directories
      file:
        path: "{{ APPLICATION_DIRS }}"
        state: directory
        mode: '0750'

    - name: Copy Instance to "{{ APPLICATION_DIRS }}"
      copy:
        src: ../../instance
        dest: "{{ APPLICATION_DIRS }}/"
        mode: "755"
        directory_mode: "755"
      
    - name: Copy compose file to "{{ APPLICATION_DIRS }}"
      copy:
        src: "../Docker/{{ DOCKER_COMPOSE_FILE }}"
        dest: "{{ APPLICATION_DIRS }}/"
        
    - name: Copy docker compose environment variables to "{{ APPLICATION_DIRS }}"
      template:
        src: ./templates/.env.j2
        dest: "{{ APPLICATION_DIRS }}/.env"

    - name: Copy application environment variables to "{{ APPLICATION_DIRS }}"
      template:
        src: ./templates/.env.{{ ENV }}.j2
        dest: "{{ APPLICATION_DIRS }}/instance/.env.{{ ENV }}"
        mode: "755"

    - name: Log in with Harbor Registry
      command: sudo docker login --username "{{ CI_REGISTRY_USER }}" --password-stdin "{{ REGISTRY_GITLAB_URL }}"
      args:
        stdin: "{{ CI_REGISTRY_PASSWORD }}"
      register: command_output

    - name: deploy Docker Compose stack
      command: "{{ item }}"
      when: (ENV == "dev") or (ENV == "lux_dev")
      with_items:
        - sed -i "s/{{ CI_PROJECT_NAME }}:/{{ CI_PROJECT_NAME }}-dev:/g" {{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}
        - "sed -i 's/container_name: \"${CI_PROJECT_NAME}\"/container_name: \"${CI_PROJECT_NAME}-dev\"/g' {{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}"
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" down
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" up -d
      args:
        chdir: "{{ APPLICATION_DIRS }}"

    - name: deploy Docker Compose stack
      command: "{{ item }}"
      when: (ENV == "stage") or (ENV == "lux_stage")
      with_items:
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" down
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" up -d

    - name: deploy Docker Compose stack
      command: "{{ item }}"
      when: (ENV == "production") or (ENV == "lux_production")
      with_items:
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" down
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" up -d


    - name: deploy Docker Compose stack
      command: "{{ item }}"
      when: (ENV == "uat")
      with_items:
        - sed -i "s/{{ CI_PROJECT_NAME }}:/{{ CI_PROJECT_NAME }}-uat:/g" {{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}
        - "sed -i 's/container_name: \"${CI_PROJECT_NAME}\"/container_name: \"${CI_PROJECT_NAME}-uat\"/g' {{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}"
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" down
        - sudo docker compose -f "{{ APPLICATION_DIRS }}/{{ DOCKER_COMPOSE_FILE }}" up -d

    - debug:
        var: command_output.stdout_lines

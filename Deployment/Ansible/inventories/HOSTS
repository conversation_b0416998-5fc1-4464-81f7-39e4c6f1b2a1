development_server ansible_ssh_host={{IP_DEV_ENV}} ansible_ssh_user="{{USER_DEV_ENV}}"

stage_server ansible_ssh_common_args="-o StrictHostKeyChecking=no" ansible_ssh_host={{IP_STAGE_ENV}} ansible_ssh_user="{{USER_STAGE_ENV}}" ansible_ssh_private_key_file="{{ID_RSA_STAGING}}"

uat_server ansible_ssh_common_args="-o StrictHostKeyChecking=no" ansible_ssh_host={{IP_UAT_ENV}} ansible_ssh_user="{{USER_UAT_ENV}}" ansible_ssh_private_key_file="{{ID_RSA_UAT}}"

jumpbox ansible_ssh_common_args="-o StrictHostKeyChecking=no" ansible_ssh_host={{JUMPBOX_IP}} ansible_ssh_user="{{JUMPBOX_USER}}" ansible_ssh_private_key_file="{{ID_RSA_B2C_JUMPBOX_EC2}}"

production_server_app1 ansible_ssh_host={{B2C_APP1_IP}} ansible_ssh_user="{{B2C_APP1_USER}}" ansible_ssh_private_key_file="{{ID_RSA_B2C_APP1_EC2}}"
production_server_app2 ansible_ssh_host={{B2C_APP2_IP}} ansible_ssh_user="{{B2C_APP2_USER}}" ansible_ssh_private_key_file="{{ID_RSA_B2C_APP2_EC2}}"


lux_development_server ansible_ssh_common_args="-o StrictHostKeyChecking=no" ansible_ssh_host={{IP_DEVELOPMENT_LUX_ENV}} ansible_ssh_user="{{USER_DEVELOPMENT_LUX_ENV}}" ansible_ssh_private_key_file="{{ID_RSA_LUX_DEVELOPMENT}}"

lux_stage_server ansible_ssh_common_args="-o StrictHostKeyChecking=no" ansible_ssh_host={{IP_STAGE_LUX_ENV}} ansible_ssh_user="{{USER_STAGE_LUX_ENV}}" ansible_ssh_private_key_file="{{ID_RSA_LUX_STAGING}}"

lux_production_server_app1 ansible_ssh_host={{B2C_APP1_LUX_IP}} ansible_ssh_user="{{B2C_APP1_LUX_USER}}" ansible_ssh_private_key_file="{{ID_RSA_B2C_APP1_EC2_LUX}}"
lux_production_server_app2 ansible_ssh_host={{B2C_APP2_LUX_IP}} ansible_ssh_user="{{B2C_APP2_LUX_USER}}" ansible_ssh_private_key_file="{{ID_RSA_B2C_APP2_EC2_LUX}}"

[dev]
  stage_server

[stage]
  stage_server

[uat]
  uat_server

[production]
  production_server_app1
  production_server_app2
[production:vars]
  ansible_ssh_common_args='-o StrictHostKeyChecking=no'
  ansible_ssh_common_args='-o StrictHostKeyChecking=no'


[lux_dev]
  lux_development_server

[lux_stage]
  lux_stage_server

[lux_production]
  lux_production_server_app1
  lux_production_server_app2
[lux_production:vars]
  ansible_ssh_common_args='-o StrictHostKeyChecking=no'
  ansible_ssh_common_args='-o StrictHostKeyChecking=no'

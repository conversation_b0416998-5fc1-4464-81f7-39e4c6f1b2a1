---

# Local Environment
IP_DEV_ENV: "127.0.0.1"
USER_DEV_ENV: "khalil"

# StageEnvironment
IP_STAGE_ENV: "{{ lookup('env', 'STAGE_SERVER_IP') }}"
USER_STAGE_ENV: "{{ lookup('env', 'STAGE_SERVER_USER') }}"
ID_RSA_STAGING: "{{ lookup('env', 'ID_RSA') }}"

# Lux DevelopmentEnvironment
IP_DEVELOPMENT_LUX_ENV: "{{ lookup('env', 'DEVELOPMENT_SERVER_LUX_IP') }}"
USER_DEVELOPMENT_LUX_ENV: "{{ lookup('env', 'DEVELOPMENT_SERVER_LUX_USER') }}"
ID_RSA_LUX_DEVELOPMENT: "{{ lookup('env', 'ID_LUX_RSA') }}"

# Lux StageEnvironment
IP_STAGE_LUX_ENV: "{{ lookup('env', 'STAGE_SERVER_LUX_IP') }}"
USER_STAGE_LUX_ENV: "{{ lookup('env', 'STAGE_SERVER_LUX_USER') }}"
ID_RSA_LUX_STAGING: "{{ lookup('env', 'ID_LUX_RSA') }}"

# Preprod UAT Environment
IP_UAT_ENV: "{{ lookup('env', 'UAT_SERVER_IP') }}"
USER_UAT_ENV: "{{ lookup('env', 'UAT_SERVER_USER') }}"
ID_RSA_UAT: "{{ lookup('env', 'ID_RSA') }}"

#JUMPBOX
ID_RSA_B2C_JUMPBOX_EC2: "{{ lookup('env', 'ID_RSA_B2C_JUMPBOX_EC2') }}"
JUMPBOX_IP: "{{ lookup('env', 'JUMPBOX_IP') }}"
JUMPBOX_USER: "{{ lookup('env', 'JUMPBOX_USER') }}"

# APP1 Variables
B2C_APP1_IP: "{{ lookup('env', 'B2C_APP1_IP') }}"
B2C_APP1_USER: "{{ lookup('env', 'B2C_APP1_USER') }}"
ID_RSA_B2C_APP1_EC2: "{{ lookup('env', 'ID_RSA_B2C_APP1_EC2') }}"

# Lux APP1 Variables
B2C_APP1_LUX_IP: "{{ lookup('env', 'B2C_APP1_LUX_IP') }}"
B2C_APP1_LUX_USER: "{{ lookup('env', 'B2C_APP1_LUX_USER') }}"
ID_RSA_B2C_APP1_EC2_LUX: "{{ lookup('env', 'ID_RSA_B2C_APP1_EC2_LUX') }}"


# APP2 Variables
B2C_APP2_IP: "{{ lookup('env', 'B2C_APP2_IP') }}"
B2C_APP2_USER: "{{ lookup('env', 'B2C_APP2_USER') }}"
ID_RSA_B2C_APP2_EC2: "{{ lookup('env', 'ID_RSA_B2C_APP2_EC2') }}"

# Lux APP2 Variables
B2C_APP2_LUX_IP: "{{ lookup('env', 'B2C_APP2_LUX_IP') }}"
B2C_APP2_LUX_USER: "{{ lookup('env', 'B2C_APP2_LUX_USER') }}"
ID_RSA_B2C_APP2_EC2_LUX: "{{ lookup('env', 'ID_RSA_B2C_APP2_EC2_LUX') }}"

APPLICATION_DIRS: "~/app/esim_b2c/api{{ APPLICATION_DIRS_SUFFIX | default('') }}"
DOCKER_COMPOSE_FILE: docker-compose.yml
DOCKER_COMPOSE_FILE_DEV: docker-compose.local.yml


# Docker Compose Environment
ENV: "{{ lookup('env', 'ENV') }}"
REGISTRY_GITLAB_URL: "{{ lookup('env', 'REGISTRY_GITLAB_URL') }}"
HARBOR_REPOSITORY: "{{ lookup('env', 'HARBOR_REPOSITORY') }}"
CI_PROJECT_NAME: "{{ lookup('env', 'CI_PROJECT_NAME') }}"
CD_VERSION: "{{ lookup('env', 'CD_VERSION') }}"
ENCRPYPT_KEY: "{{ lookup('env', 'ENCRPYPT_KEY') }}"
PORT: "{{ lookup('env', 'PORT') }}"

CI_REGISTRY_PASSWORD: "{{ lookup('env', 'CI_REGISTRY_PASSWORD') }}"
CI_REGISTRY_USER: "{{ lookup('env', 'CI_REGISTRY_USER') }}"


#Application Environment
TARGET_HOST: "{{ ansible_default_ipv4.address }}"
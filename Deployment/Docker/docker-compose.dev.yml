version: '3.7'
services:

  esim-b2c-cron-jobs-py:
    image: registry.montymobile.com/esim/esim-b2c-mng-py-py:Beta
    container_name: esim-b2c-cron-jobs-py
    environment:
      ENV: dev
      DEBUG: "False"
    ports:
      - 5007:5007
    volumes:
      - ../../instance/:/home/<USER>/app/instance
    depends_on:
      - mongodb
    networks:
      - backend

#  mongodb:
#    image: mongo:4.0.8
#    container_name: mongodb
#    restart: unless-stopped
#    command: mongod --auth
#    environment:
#      MONGO_INITDB_ROOT_USERNAME: root
#      MONGO_INITDB_ROOT_PASSWORD: pass
#      MONGO_INITDB_DATABASE: rsp_extra
#      MONGODB_DATA_DIR: /data/db
#      MONDODB_LOG_DIR: /dev/null
#    volumes:
#      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
#    networks:
#      - backend

networks:
  backend:
    external: true

volumes:
  mongodbdata:
    driver: local
 
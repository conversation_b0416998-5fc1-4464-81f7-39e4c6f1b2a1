version: "3.7"
services:
  esim-b2c-admin-web-py:
    image: "${REGISTRY_GITLAB_URL}/${HARBOR_REPOSITORY}/${CI_PROJECT_NAME}:${CD_VERSION}"
    container_name: "${CI_PROJECT_NAME}"
    environment:
      ENV: "${ENV}"
      ENCRPYPT_KEY: "${ENCRPYPT_KEY}"
    volumes:
      - ./instance/:/home/<USER>/app/instance
      - ./logs:/home/<USER>/app/logs
    network_mode: host
    command:
      - /bin/bash
      - -c
      - |
        gunicorn --timeout 90 --workers=1 --threads=10 --worker-class=gthread app_extra_${ENV}:app -b 0.0.0.0:${PORT}

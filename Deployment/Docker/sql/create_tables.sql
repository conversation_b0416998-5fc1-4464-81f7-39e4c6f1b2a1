
-- esim_staging.vendor definition

-- Drop table

-- DROP TABLE vendor;

CREATE TABLE vendor (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	vendor_name varchar(255) NULL,
	is_active bool NULL,
	rate_revenue float8 NULL,
	CONSTRAINT vendor_pkey PRIMARY KEY (id)
);
-- esim_staging.bundle_inventory definition

-- Drop table

-- DROP TABLE bundle_inventory;

CREATE TABLE bundle_inventory (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	"date" timestamp(6) NULL,
	created_date timestamp(6) NULL,
	country_name varchar(255) NULL,
	country_code varchar(255) NULL,
	bundle_code varchar(255) NULL,
	data_amount float8 NULL,
	data_unit varchar(255) NULL,
	unit_price float8 NULL,
	retail_price float8 NULL,
	currency_code varchar(255) NULL,
	profile_name varchar(255) NULL,
	allocated_unit int8 NULL,
	consumed_unit int8 NULL,
	vendor_name varchar(255) NULL,
	bundle_name varchar(255) NULL,
	bundle_marketing_name varchar(255) NULL,
	CONSTRAINT "bundle inventory_pkey" PRIMARY KEY (id)
);
-- esim_staging.daily_dashboard_info definition

-- Drop table

-- DROP TABLE daily_dashboard_info;

CREATE TABLE daily_dashboard_info (
	id int4 NOT NULL,
	"date" timestamp NULL,
	created_date timestamp NULL,
	bundle_code varchar(255) NULL,
	bundle_marketing_name varchar(255) NULL,
	bundle_name varchar(255) NULL,
	bundle_duration int4 NULL,
	bundle_data_unit varchar(255) NULL,
	data_amount float8 NULL,
	CONSTRAINT daily_dashboard_info_pkey PRIMARY KEY (id)
);

-- esim_staging.daily_total_values definition

-- Drop table

-- DROP TABLE daily_total_values;

CREATE TABLE daily_total_values (
	id int4 NOT NULL,
	"date" timestamp NULL,
	created_date timestamp NULL,
	nb_existed_profiles int8 NULL,
	nb_existed_bundles int8 NULL,
	CONSTRAINT daily_total_values_pkey PRIMARY KEY (id)
);


-- esim_staging.profile definition

-- Drop table

-- DROP TABLE profile;

CREATE TABLE profile (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	"date" timestamp(6) NULL,
	created_date timestamp(6) NULL,
	vendor_name varchar(255) NULL,
	iccid varchar(255) NULL,
	profile_name varchar(255) NULL,
	smdp_address varchar(255) NULL,
	matching_id varchar(255) NULL,
	activation_code varchar(255) NULL,
	availability varchar(255) NULL,
	create_datetime timestamp(6) NULL,
	installed_datetime timestamp(6) NULL,
	status bool NULL,
	CONSTRAINT profiles_pkey PRIMARY KEY (id)
);
-- esim_staging.sale_volume definition

-- Drop table

-- DROP TABLE sale_volume;

CREATE TABLE sale_volume (
	id int4 NOT NULL,
	"date" timestamp NULL,
	created_date timestamp NULL,
	consumed_unit int8 NULL,
	country_name varchar(255) NULL,
	country_code varchar(255) NULL,
	bundle_name varchar(255) NULL,
	bundle_marketing_name varchar(255) NULL,
	bundle_code varchar(255) NULL,
	unit_price int8 NULL,
	retail_price int8 NULL,
	currency_code varchar(255) NULL,
	profit_cost int8 NULL,
	CONSTRAINT sale_volume_pkey PRIMARY KEY (id)
);


-- esim_staging.total definition

-- Drop table

-- DROP TABLE total;

CREATE TABLE total (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	"date" timestamp(6) NULL,
	created_date timestamp(6) NULL,
	registred_users int8 NULL,
	country_code varchar NULL,
	country_name varchar NULL,
	CONSTRAINT registred_pkey PRIMARY KEY (id)
);

-- esim_staging.user_bundle definition

-- Drop table

-- DROP TABLE user_bundle;

CREATE TABLE user_bundle (
	id int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY,
	"date" timestamp(6) NULL,
	created_date timestamp(6) NULL,
	username varchar(255) NULL,
	msisdn int8 NULL,
	bundle_name varchar(255) NULL,
	bundle_code varchar(255) NULL,
	iccid varchar(255) NULL,
	country_name varchar(255) NULL,
	amount float8 NULL,
	currency_code varchar(255) NULL,
	status varchar(255) NULL,
	country_code varchar(255) NULL,
	CONSTRAINT "user bundle_pkey" PRIMARY KEY (id)
);
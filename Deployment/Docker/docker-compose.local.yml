version: '3.7'
services:

  esim-b2c-admin-web-py:
    image: registry.montymobile.com/esim/esim-b2c-admin-web-py:Beta
    container_name: esim-b2c-admin-web-py
    environment:
      ENV: dev
      DEBUG: "False"
    ports:
      - 5006:5006
    volumes:
      - ../../instance/:/home/<USER>/app/instance
      - ../../logs:/home/<USER>/app/logs
    depends_on:
#     - mongodb-esim-b2c-admin-web-0
      - postgres
    networks:
      - backend

  postgres:
    image: postgres:14.1-alpine
    container_name: postgres
    # restart: always
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: 72d25Tnj
      POSTGRES_DB: esim_db
      POSTGRES_PORT: 5432
    ports:
      - 5438:5432
    networks:
      - backend
    volumes:
      # Issue in this line, Whenever docker finds data it skips initialization and doesnt create the tables.
      - ./postgres-data:/var/lib/postgresql/data
      # copy the sql script to create tables
      - ./sql/create_tables.sql:/docker-entrypoint-initdb.d/create_tables.sql
      # copy the sql script to fill tables
      - ./sql/fill_tables.sql:/docker-entrypoint-initdb.d/fill_tables.sql

#  mongodb:
#    image: mongo:4.0.8
#    container_name: mongodb
#    restart: unless-stopped
#    # command: mongod --auth
#    ports:
#      - 27018:27017
#    environment:
#      MONGO_INITDB_ROOT_USERNAME: admin
#      MONGO_INITDB_ROOT_PASSWORD: password
#      MONGO_INITDB_DATABASE: rsp_extra
#      MONGODB_DATA_DIR: /data/db
#      MONDODB_LOG_DIR: /dev/null
#    volumes:
#      - ./docker-entrypoint-initdb.d/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
#    networks:
#      - backend

networks:
  backend:
    external: true

volumes:
  mongodbdata:
    driver: local
  postgres_data:
    driver: local
 
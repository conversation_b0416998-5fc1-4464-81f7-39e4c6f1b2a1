version: '3.7'
services:

  esim-stripe-api-py:
    image: registry.montymobile.com/esim/esim-stripe-api-py:Beta
    container_name: esim-stripe-api-py
    command:
      - /bin/bash
      - -c
      - |
        source scripts/load_env.sh
        gunicorn -w 4 src.app:app -b 0.0.0.0:5005
    environment:
      ENV: dev
      DEBUG: "False"

    volumes:
      - ../../instance/:/home/<USER>/app/instance
    ports:
      - "${PORT}:${PORT}"
    depends_on:
      - mongodb
    networks:
      - backend

  mongodb:
    image: mongo:4.0.8
    container_name: mongodb
    restart: unless-stopped
    command: mongod --auth
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: pass
      MONGO_INITDB_DATABASE: rsp_extra
      MONGODB_DATA_DIR: /data/db
      MONDODB_LOG_DIR: /dev/null
    volumes:
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - backend

networks:
  backend:
    driver: bridge

volumes:
  mongodbdata:
    driver: local
 
#!/bin/sh

REPLICA_SET_SETUP_FILE=/app/replicaset-setup.js
CONTAINER_START_FILE=/app/container_start.sh

if [ -z "$PRIMARY" ]; then
  # In member container.

  sha1sum /app/replicaset-setup.js | base64 > /app/keyfile && chmod 400 /app/keyfile && chown mongodb /app/keyfile

#  if [ "$DATABASES" ]; then
#    mv -f /app/Databases/* /docker-entrypoint-initdb.d && chown -R mongodb /docker-entrypoint-initdb.d
#  fi
# for local only
  if [ "$PORT" = "27017" ]; then
    mv -f /app/Databases/* /docker-entrypoint-initdb.d && chown -R mongodb /docker-entrypoint-initdb.d
    fi
#  rm -rf "$CONTAINER_START_FILE" "$DATABASES_DIR" "$REPLICA_SET_SETUP_FILE"

#  docker-entrypoint.sh --replSet rs0 --keyFile /app/keyfile --auth --smallfiles --oplogSize 128 --port "${PORT-27017}"
  docker-entrypoint.sh --replSet rs0 --keyFile /app/keyfile --auth --oplogSize 128 --port "${PORT-27017}"

else
  # In replicator container.

#  rm -rf "$DATABASES_DIR" "$CONTAINER_START_FILE"

  # Initialize the replica set by running the setup JS file in the first mongodb container.

  until mongo "$PRIMARY" --authenticationDatabase "$MONGO_INITDB_DATABASE" -u "$MONGO_INITDB_ROOT_USERNAME" -p "$MONGO_INITDB_ROOT_PASSWORD" /app/replicaset-setup.js; do
      echo 'Retrying...'
      sleep 10
  done
  echo "Done Retrying"
  echo 'Ok'
fi
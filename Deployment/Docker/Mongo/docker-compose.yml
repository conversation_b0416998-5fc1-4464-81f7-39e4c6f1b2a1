version: "3.7"
services:

  mongodb-esim-b2c-admin-web-0:
    image: registry.montymobile.com/esim/esim-b2c-admin-web-py:mongodb

    container_name: "mongodb-esim-b2c-admin-web-0"
    ports: [ 27017:27017 ]
    networks:
      backend:
        ipv4_address: ************
    environment:
      PORT: 27017
      IP_ADDRESS: ************
      MONGO_RS: rs0/mongodb-esim-b2c-admin-web-0:27017,mongodb-esim-b2c-admin-web-1:27019,mongodb-esim-b2c-admin-web-2:27020
#      - DATABASES=1
      MONGO_INITDB_ROOT_USERNAME: user
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: admin
#      - MICROSERVICE_USERS=servicesdbmicroservice,5IB*5rQAL60kNaOL auditcomputemicroservice
    volumes:
      - mongo-esim-b2c-admin-web-0:/data/db

  mongodb-esim-b2c-admin-web-1:
    image: registry.montymobile.com/esim/esim-b2c-admin-web-py:mongodb

    container_name: "mongodb-esim-b2c-admin-web-1"
    ports: [ 27019:27019 ]
    networks:
      backend:
        ipv4_address: ************
    environment:
      PORT: 27019
      IP_ADDRESS: ************
      MONGO_RS: rs0/mongodb-esim-b2c-admin-web-0:27017,mongodb-esim-b2c-admin-web-1:27019,mongodb-esim-b2c-admin-web-2:27020
      #      - DATABASES=1
      MONGO_INITDB_ROOT_USERNAME: user
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: admin
    volumes:
      - mongo-esim-b2c-admin-web-1:/data/db

  mongodb-esim-b2c-admin-web-2:
    image: registry.montymobile.com/esim/esim-b2c-admin-web-py:mongodb

    container_name: "mongodb-esim-b2c-admin-web-2"
    ports: [ 27020:27020 ]
    networks:
      backend:
        ipv4_address: ************
    environment:
      PORT: 27020
      IP_ADDRESS: ************
      MONGO_RS: rs0/mongodb-esim-b2c-admin-web-0:27017,mongodb-esim-b2c-admin-web-1:27019,mongodb-esim-b2c-admin-web-2:27020
      #      - DATABASES=1
      MONGO_INITDB_ROOT_USERNAME: user
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: admin
    volumes:
      - mongo-esim-b2c-admin-web-2:/data/db

  mongodb-replicator-esim-b2c-admin-web:
    image: registry.montymobile.com/esim/esim-b2c-admin-web-py:mongodb

    container_name: "mongodb-replicator-esim-b2c-admin-web"
#    volumes:
#      - ./container_start.sh:/container_start.sh
#    entrypoint: ["bash", "container_start.sh" ]
    networks:
      backend:
        ipv4_address: ************
    environment:
      PORT: 27018
      PRIMARY: "mongodb-esim-b2c-admin-web-0:27017"
      MONGO_RS: rs0/mongodb-esim-b2c-admin-web-0:27017,mongodb-esim-b2c-admin-web-1:27019,mongodb-esim-b2c-admin-web-2:27020
      MONGO_INITDB_ROOT_USERNAME: user
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: admin
    depends_on:
      - mongodb-esim-b2c-admin-web-0
      - mongodb-esim-b2c-admin-web-1
      - mongodb-esim-b2c-admin-web-2

volumes:
  mongo-esim-b2c-admin-web-0:
    external: true
  mongo-esim-b2c-admin-web-1:
    external: true
  mongo-esim-b2c-admin-web-2:
    external: true

networks:
  backend:
    name: backend
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ************/24
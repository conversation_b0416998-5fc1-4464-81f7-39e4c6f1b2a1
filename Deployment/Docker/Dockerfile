FROM python:3.8-slim-buster

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

ARG GIT_HELPERS_USERNAME
ARG GIT_HELPERS_ACCESS_TOKEN
ARG GIT_HELPERS_VERSION_TAG

ARG GIT_USERNAME
ARG GIT_ACCESS_TOKEN

ARG PORT

RUN apt-get update
RUN apt-get install -y binutils git

RUN adduser --disabled-password --home /home/<USER>

RUN mkdir /home/<USER>/app/ && chown -R rsp:rsp /home/<USER>/app

COPY requirements.txt /home/<USER>/app/requirements.txt

WORKDIR /home/<USER>/app

USER rsp

ENV PATH="/home/<USER>/.local/bin:${PATH}"



ARG CACHEBUST=1

RUN pip install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim-b2c-models-py.git
RUN pip install --upgrade --force-reinstall git+https://$GIT_HELPERS_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim_b2c_helpers.git@$GIT_HELPERS_VERSION_TAG

ADD --chown=rsp:rsp . .

RUN pip install -r requirements.txt
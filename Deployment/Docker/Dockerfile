FROM python:3.8-slim-buster

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Install needed binaries for PyInstaller later.
ARG GIT_USERNAME
ARG GIT_ACCESS_TOKEN


ARG GIT_HELPERS_USERNAME
ARG GIT_HELPERS_ACCESS_TOKEN
ARG GIT_HELPERS_VERSION_TAG

ARG PORT

RUN apt-get update
RUN apt-get install -y binutils git

RUN adduser --disabled-password --home /home/<USER>

RUN mkdir /home/<USER>/app/ && chown -R rsp:rsp /home/<USER>/app
RUN mkdir /home/<USER>/app/logs/ && chown -R rsp:rsp /home/<USER>/app/logs

WORKDIR /home/<USER>/app

USER rsp

ENV PATH="/home/<USER>/.local/bin:${PATH}"

ADD --chown=rsp:rsp . .


RUN pip install --upgrade --force-reinstall git+https://$GIT_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim-b2c-models-py.git
RUN pip install --upgrade --force-reinstall git+https://$GIT_HELPERS_USERNAME:$<EMAIL>/monty-mobile1/esim/b2c/esim_b2c_helpers.git@$GIT_HELPERS_VERSION_TAG
RUN pip install -r requirements.txt

ENV LOG_FILE_PATH=/home/<USER>/app/logs

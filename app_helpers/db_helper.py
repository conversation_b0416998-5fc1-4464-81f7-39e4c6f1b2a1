import random
import string
from collections import defaultdict
from typing import Dict, List, Optional

from app_models import main_models, reseller_models
import hashlib
from datetime import timedelta

from app_models.consumer_models import Bundles
from app_models.reseller_models import Order_history, Reseller, NotificationHistory
from flask_login import current_user
from mongoengine.queryset.visitor import Q
from app_models import mobiles, consumer_models
from app_helpers.main_helper import try_except_func
from bson.objectid import ObjectId
from mongoengine.errors import NotUniqueError
import datetime
from app_helpers import script_helper
from app_helpers.script_helper import get_esimgo_balance
from instance import consumer_config as instance_config
import requests
from mongoengine.context_managers import switch_db
import json
import datetime
import re
import uuid
import logging

logger = logging.getLogger(__name__)

def get_version_token():
    resp = mobiles.AppVersionList.objects().first()
    if resp is not None:
        return resp.version_token
    return None


def check_access(id_, action):
    current = main_models.User.objects(id_admin_user=id_).first()
    actions = main_models.Actions.objects(actions=action).first()
    return current, actions


def prepare_runnable_scripts(script, vendor_name, from_date=None, to_date=None):
    try:
        doc = {"script": script, "datetime": datetime.datetime.utcnow(),
               "vendor_name": vendor_name,
               "state": "Waiting", "informer": current_user.email}
        if from_date is not None and to_date is not None:
            doc['from_date'] = datetime.datetime.strptime(from_date, "%Y-%m-%d 00:00:00")
            doc['to_date'] = datetime.datetime.strptime(to_date, "%Y-%m-%d 00:00:00")
        return main_models.RunnableScripts(**doc).save()
    except Exception as e:
        print("Exception in prepare_runnable_scripts as ", str(e))
        return False


def random_voucher(length):
    ran = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
    return str(ran)


def get_one_runnable_csv(script):
    try:
        return main_models.GenerateCsv.objects(script=script, state="Accepted").first()
    except Exception as e:
        print("function get_one_runnable_script  ", str(e))
        return False


def get_one_runnable_script(script=None):
    '''deprecated '''
    try:
        if script:
            return main_models.RunnableScripts.objects(script=script, state="Accepted").first()
        else:
            return main_models.RunnableScripts.objects(state="Accepted").first()
    except Exception as e:
        print("function get_one_runnable_script 42 ", str(e))
        return False


def update_runnable_scripts(ids):
    success = 0
    failed = 0
    for id in ids:
        runnable_object = get_runnable_script_object(id)
        if runnable_object.informer == current_user.email:
            failed += 1
        else:
            runnable_object.state = "Accepted"
            runnable_object.acceptor = current_user.email
            runnable_object.save()
            success += 1
    return success, failed


def update_generate_csv(ids):
    success = 0
    failed = 0
    for id in ids:
        runnable_object = get_generate_csv_object(id)
        if runnable_object.informer == current_user.email:
            failed += 1
        else:
            runnable_object.state = "Accepted"
            runnable_object.acceptor = current_user.email
            runnable_object.save()
            success += 1
    return success, failed


def get_runnable_script_object(id):
    return main_models.RunnableScripts.objects(id=id, state='Waiting').first()


def get_generate_csv_object(id):
    return main_models.GenerateCsv.objects(id=id, state='Waiting').first()


def reset_to_waiting_scripts_states(ids):
    total = 0
    for id in ids:
        run_obj = main_models.RunnableScripts.objects(id=id).first()
        run_obj.state = "Waiting"
        run_obj.save()
        total += 1
    return total


def reset_to_waiting_generate_csv(ids):
    total = 0
    for id in ids:
        run_obj = main_models.GenerateCsv.objects(id=id).first()
        run_obj.state = "Waiting"
        run_obj.save()
        total += 1
    return total


@try_except_func
def get_vendor_name(id):
    vendor = consumer_models.Vendors.objects(id=id).first()
    return vendor.vendor_name


@try_except_func
def get_iso3(iso_code):
    if len(iso_code) > 2:
        country = consumer_models.Countries.objects(iso3_code__contains=iso_code).first()
    else:
        country = consumer_models.Countries.objects(iso2_code=iso_code).first()
    if country:
        return country
    else:
        return False


@try_except_func
def get_version_from_token(version_token):
    resp = mobiles.AppVersionList.objects(version_token=version_token).first()
    return resp


@try_except_func
def check_user(email):
    email_found = mobiles.AppEmailVerification.objects(user_email=email).first()
    return email_found


@try_except_func
def check_unverified_user(email):
    email_found = mobiles.AppEmailVerification.objects(user_email=email).first()
    return email_found


@try_except_func
def get_email_setting(email):
    return main_models.EmailSettings.objects(email=email).first()


@try_except_func
def get_bundle(bundle_code):
    return consumer_models.Bundles.objects(bundle_code=bundle_code).first()


@try_except_func
def check_bundle(bundle_code, vendor_name, db_name=None):
    # from mongoengine import connect
    if db_name is not None:
        # and alias is not None:
        # connect(alias=alias)
        with switch_db(consumer_models.Bundles, db_name) as CustomModel:
            return CustomModel.objects(vendor_name=vendor_name, bundle_vendor_code=bundle_code).first()
    else:
        return consumer_models.Bundles.objects(vendor_name=vendor_name, bundle_vendor_code=bundle_code).first()


@try_except_func
def update_bundle(bundle_code, country_list, country_code_list, unit_price, db_name):
    if db_name is not None:
        with switch_db(consumer_models.Bundles, db_name) as CustomModel:
            return CustomModel.objects(bundle_code=bundle_code).update(set__country_code_list=country_code_list,
                                                                       set__country_list=country_list,
                                                                       set__unit_price=unit_price)
    else:
        return consumer_models.Bundles.objects(bundle_code=bundle_code).update(set__country_code_list=country_code_list,
                                                                               set__country_list=country_list,
                                                                               set__unit_price=unit_price)


def remove_space_profiles(profile_name):
    return profile_name.replace(" ", "")


@try_except_func
def get_bundle_details(list_options=[]):
    return consumer_models.BundleDetails.objects(default_message=True, category__in=list_options).order_by(
        '-detail_code')


@try_except_func
def get_bundles_by_vendor(vendor_name, is_active=None, deleted=None, bundle_code=None, allocate_profiles=None):
    query_set = Q(vendor_name=vendor_name)

    if allocate_profiles is not None:
        query_set = query_set & Q(allocate_profiles=allocate_profiles)

    if bundle_code:
        query_set = query_set & Q(bundle_code=bundle_code)

    else:
        if is_active is not None:
            query_set = query_set & Q(is_active=is_active)
        if deleted is not None:
            query_set = query_set & Q(deleted=deleted)

    return consumer_models.Bundles.objects(query_set)

@try_except_func
def get_profiles_by_vendor(vendor_name, availability, profile_status):
    query_set = Q(vendor_name=vendor_name, availability=availability, profile_status=profile_status)
    return consumer_models.Profiles.objects(query_set)

from mongoengine.errors import NotUniqueError

def set_bundle_name_code(bundle):

    amount = f"{float(bundle.get('gprs_limit')):.2f}".rstrip("0").rstrip(".")

    if len(bundle.get("country_code")) > 1:
        if bundle.get("bundle_category") == "global":
            bundle_name = f"Global_bundle {amount}{bundle.get('data_unit')}"
            bundle_code = f"Global_bundle_{uuid.uuid4()}"
        else:
            title = bundle["bundle_name"]
            title = re.sub(r"1Month|MB|GX|JX|B - |I - ", "", title).strip()
            bundle_name = f"{title} {amount}{bundle.get('data_unit')}"
            bundle_code = f"{title.replace(' ', '').lower()}-{uuid.uuid4()}"
    else:
        bundle_name = f"{bundle.get('country_name')[0]} {amount}{bundle.get('data_unit')}"
        bundle_code = f"{bundle.get('country_code')[0]}_{uuid.uuid4()}"

    # Add the "_UNLIMITED" label if the bundle is unlimited
    if bundle.get('unlimited', False):
        bundle_name += "_UNLIMITED"
        bundle_code += "_UNLIMITED"

    # Replace any special characters in bundle_name and bundle_code with underscores
    bundle_name = re.sub(r"[@#%+__&$\"']", "-", bundle_name)
    bundle_code = re.sub(r"[@#%+__&$\"']", "-", bundle_code)

    return bundle_name, bundle_code

def add_bundle(new_doc, db_name=None):
    try:
        print("Entered add_bundle", new_doc)
        if db_name is not None:
            with switch_db(consumer_models.Bundles, db_name) as CustomModel:
                return CustomModel(**new_doc).save()
        else:
            return consumer_models.Bundles(**new_doc).save()
    except NotUniqueError as not_unique:
        print("Exception at function: add_bundle, as: not_unique {}".format(str(not_unique)), flush=True)
        return False
    except Exception as e:
        print("Exception at function: add_bundle, as: {}".format(str(e)), flush=True)
        return False


@try_except_func
def activate_bundle(profile_name):
    consumer_models.Bundles.objects(profile_name=profile_name, deleted=False).update(
        set__is_active=True
    )
    add_to_update_bundle_version([f"Activate bundle with Profile Name: {profile_name}"])


@try_except_func
def reset_user_iccid(date_now):
    Order_history.objects(expiry_date__lte=date_now, plan_status="Active", order_status="Successful").update(set__plan_status="Expired")


@try_except_func
def reset_email_verification(date_now):
    new_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        days=30)
    mobiles.AppEmailVerification.objects(Q(is_verified=False) & Q(expiry_date__lte=date_now) | Q(count=5)).update(
        set__count=0, set__expiry_date=new_date)


@try_except_func
def reset_password_request(date_now):
    mobiles.AppUserPasswordReset.objects(
        Q(expiry_create_date__lte=date_now) | Q(expiry_date__lte=date_now) | Q(count=5)).delete()


@try_except_func
def reset_contact_us(date_now):
    mobiles.AppContactUs.objects(Q(expiry_date__lte=date_now) | Q(count=3)).delete()


@try_except_func
def add_profiles(new_doc):
    return consumer_models.Profiles(**new_doc).save()


@try_except_func
def check_iccid(iccid):
    return consumer_models.Profiles.objects(iccid=iccid).first()


@try_except_func
def password_check(passwd):
    validate = True
    msg_lst = []
    special_sym = ['$', '!', '@', '#', '%']
    if len(passwd) < 6:
        msg_lst.append('length should be at least 12')
        validate = False

    if len(passwd) > 20:
        msg_lst.append('length should be not be greater than 20')
        validate = False

    if not any(char.isdigit() for char in passwd):
        msg_lst.append('Password should have at least one numeral')
        validate = False

    if not any(char.isupper() for char in passwd):
        msg_lst.append('Password should have at least one uppercase letter')
        validate = False

    if not any(char.islower() for char in passwd):
        msg_lst.append('Password should have at least one lowercase letter')
        validate = False

    if not any(char in special_sym for char in passwd):
        msg_lst.append('Password should have at least one of the symbols $@#')
        validate = False

    return validate, msg_lst


@try_except_func
def create_email_verification_record(doc):
    del doc["version_number"]
    email_verification = mobiles.AppEmailVerification(**doc).save()
    return email_verification


@try_except_func
def create_reset_password_record(doc):
    return mobiles.AppUserPasswordReset(**doc).save()


@try_except_func
def get_reset_password_record(otp):
    return mobiles.AppUserPasswordReset.objects(otp=otp).first()


@try_except_func
def delete_reset_password_record(otp):
    return mobiles.AppUserPasswordReset.objects(otp=otp).delete()


@try_except_func
def get_app_from_id(app_id):
    if isinstance(app_id.id, str):
        app_id = ObjectId(app_id.id)
    return mobiles.AppList.objects(id=app_id.id).first()


@try_except_func
def get_operator_from_name(op_name):
    operator = main_models.OperatorList.objects(op_name=op_name).first()
    return operator


@try_except_func
def get_email_verification(token, pin):
    return mobiles.AppEmailVerification.objects(verification_token=token,
                                                verification_pin=pin, is_verified=False).first()


@try_except_func
def get_email_settings():
    return main_models.EmailSettings.objects().first()


@try_except_func
def get_user(email):
    current_user = mobiles.AppUserDetails.objects(user_email=email).first()
    return current_user


@try_except_func
def get_noverified_user(email):
    current_user = mobiles.AppEmailVerification.objects(user_email=email).first()
    return current_user


@try_except_func
def get_cards_by_user(user_id):
    cards = main_models.Cards.objects(user_id=user_id)
    return cards


@try_except_func
def get_cards_by_id(id_card):
    cards = main_models.Cards.objects(id_card=id_card).first()
    return cards


@try_except_func
def delete_card(user_id, id_card):
    cards = main_models.Cards.objects(user_id=user_id, id_card=id_card).delete()
    return cards


@try_except_func
def delete_user_details(email):
    found = mobiles.AppUserDetails.objects(user_email=email).all()
    if len(found) > 0:
        mobiles.AppUserDetails.objects(user_email=email).delete()
    return True


@try_except_func
def delete_user_verification(email):
    found = mobiles.AppEmailVerification.objects(user_email=email).all()
    if len(found) > 0:
        mobiles.AppEmailVerification.objects(user_email=email).delete()
    return True


@try_except_func
def delete_user_bundle(email):
    found = consumer_models.UserBundle.objects(email=email)
    if len(found) > 0:
        consumer_models.UserBundle.objects(email=email).delete()
    return True


def validate_password(request_doc, user, email, msg_lst):
    if 'new_password' in request_doc:
        password = request_doc['new_password']
        retype_password = request_doc['retype_password']
        old_password = request_doc['old_password']
        if password == retype_password:
            validate, msg_lst = password_check(password)
            if validate:
                old_password = hashlib.md5(old_password.encode('utf-8')).hexdigest()
                found = get_user_for_login(email, old_password)

                if found:
                    user.password = hashlib.md5(password.encode('utf-8')).hexdigest()
                else:
                    validate = False
                    msg_lst.append('Your password is wrong')
        else:
            msg_lst.append('New password and retype password should be the same')
            validate = False
    return validate, msg_lst


@try_except_func
def update_user_detail(request_doc, email):
    validate = True
    msg_lst = []
    user = get_user(email)
    if 'first_name' in request_doc and len(request_doc['first_name']) != 0:
        user.first_name = request_doc['first_name']
    if 'last_name' in request_doc and len(request_doc['last_name']) != 0:
        user.last_name = request_doc['last_name']
    if 'user_image' in request_doc:
        user.user_image = bytes(request_doc['user_image'], 'utf-8')
    if 'image_extension' in request_doc:
        user.image_extension = request_doc['image_extension']
    if 'fcm_token' in request_doc:
        user.fcm_token = request_doc['fcm_token']
    validate, msg_lst = validate_password(request_doc, user, email, msg_lst)

    if validate:
        user.save()
    return validate, msg_lst


@try_except_func
def create_or_update_user_details(doc):
    current_users = mobiles.AppUserDetails.objects(user_email=doc["user_email"])

    if len(current_users) == 0:
        result = mobiles.AppUserDetails(**doc).save()
        return result

    for current_user in current_users:
        result = app_versions_from_same_operator(current_user.version_token,
                                                 doc["version_token"])
        if result:
            current_user.user_token = doc["user_token"]
            current_user.version_token = doc["version_token"]
            current_user.verification_date = doc["verification_date"]
            current_user.request_ip = doc["request_ip"]
            current_user.save
            return True


@try_except_func
def update_user_details(user_email):
    current_user = mobiles.AppUserDetails.objects(user_email=user_email).first()
    return current_user


@try_except_func
def check_user_count(user_email):
    count = mobiles.AppUserDetails.objects(user_email=user_email).count()
    return count


@try_except_func
def app_versions_from_same_operator(version_token1, version_token2):
    version1_object = get_version_from_token(version_token=version_token1)
    version2_object = get_version_from_token(version_token=version_token2)
    if version1_object is None or version2_object is None:
        return None
    app1_object = get_app_from_id(version1_object.app_id)
    app2_object = get_app_from_id(version2_object.app_id)
    if app1_object is None or app2_object is None:
        return None
    return app1_object.op_name == app2_object.op_name


@try_except_func
def get_user_from_token(user_token):
    return mobiles.AppUserDetails.objects(user_token=user_token).first()


@try_except_func
def get_user_for_login(user_email):
    user = mobiles.AppUserDetails.objects(user_email=user_email).first()
    return user


@try_except_func
def get_user_by_email(email):
    return mobiles.AppUserDetails.objects(user_email=email).first()


@try_except_func
def get_top_countries_by_users(limiter, from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":
            {
                "$and":
                    [
                        {"create_date": {"$gte": date_from,
                                         "$lte": date_to}}
                    ]
            }},
        {
            "$group": {
                "_id": {"country": "$country"},
                "number_of_users": {"$sum": 1}
            }},

        {"$sort": {"number_of_users": -1}},
        {"$limit": limiter}

    ]
    return mobiles.AppUserDetails.objects().aggregate(*pipeline)


@try_except_func
def get_top_bundles_bought(limiter, from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":

            {
                "$and": [

                    {"payment_date": {"$gte": date_from, "$lte": date_to}},
                    {"payment_status": True},
                ]}
        },

        {
            "$group": {
                "_id": {"bundle_code": "$bundle_code", "topup_code": "$topup_code"},
                "number_of_bundles_bought": {"$sum": 1},
                'bundle_code': {'$first': "$bundle_code"},
                # "bundle_marketing_name": {"$first": "$bundle_marketing_name"},
                # "bundle_name": {"$first": "$bundle_name"},
                "bundle_duration": {"$first": "$bundle_duration"},
                "data_unit": {"$first": "$data_unit"},
                "data_amount": {"$first": "$data_amount"}

            }},

        {"$sort": {"number_of_bundles_bought": -1}},
        {"$limit": limiter}

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_allocated_bundles_pipeline(date1, date2):
    date_from = datetime.datetime.strptime(date1, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(date2, '%Y-%m-%d %H:%M:%S')

    # profile_price = 1
    # total = 0
    # total += (bundle.allocated_unit * bundle.unit_price) + profile_price

    pipeline = [
        {"$match":
            {
                "$and":
                    [
                        {"create_datetime": {"$gte": date_from,
                                             "$lte": date_to}},

                        {"allocated_unit": {"$gte": 0}},

                    ]
            }},

        {
            "$project": {

                "total": {
                    "$sum": [{
                        "$multiply": ["$allocated_unit", "$unit_price"]}, 1],

                }

            }
        }
    ]
    return consumer_models.Bundles.objects().aggregate(*pipeline)


@try_except_func
def get_profit_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]

    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_profit_topup_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_topup": True},  # note syntax

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'topup_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_daily_profit_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')

    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,
                "bundle_code": 1,
                "consumed_unit": "$bundles.consumed_unit",
                "bundle_name": "$bundles.bundle_name",
                "bundle_marketing_name": "$bundles.bundle_marketing_name",
                "unit_price": "$bundles.unit_price",
                "retail_price": "$bundles.retail_price",
                "currency_code": "$bundles.currency_code",
                "country_code_list": "$bundles.country_code_list",
                "country_list": "$bundles.country_list",

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]

    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_daily_profit_topup_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_topup": True},  # note syntax

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'topup_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,
                "bundle_code": 1,
                "consumed_unit": "$bundles.consumed_unit",
                "bundle_name": "$bundles.bundle_name",
                "bundle_marketing_name": "$bundles.bundle_marketing_name",
                "unit_price": "$bundles.unit_price",
                "retail_price": "$bundles.retail_price",
                "currency_code": "$bundles.currency_code",
                "country_code_list": "$bundles.country_code_list",
                "country_list": "$bundles.country_list",

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


'''match_query =         {"$match":
            {
                "$and":
                    [
                       { "payment_status":True},
                        {"payment_date": {"$gte": date_from,
                                          "$lte": date_to}}
                    ]
            }},
'''


@try_except_func
def save_history_logs(doc):
    main_models.HistoryLogs(**doc).save()


@try_except_func
def check_history(date_time, email, iccid, bundle_code, topup_code=""):
    query_set = (Q(datetime=date_time) & Q(email=email) & Q(iccid=str(iccid)) & Q(bundle_code=str(bundle_code)))
    if topup_code != "":
        query_set = query_set & (Q(topup_code=topup_code))
    res = main_models.HistoryLogs.objects(query_set).count()
    if res > 0:
        return True
    return False


@try_except_func
def get_user_history_bundle_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')

    pipeline = [
        {
            '$match': {
                '$and': [
                    {"payment_date": {"$gte": date_from,
                                      "$lt": date_to},
                     }, {
                        'payment_status': True
                    }, {
                        'payment_topup': False
                    }
                ]
            }
        }, {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'otp',
                'foreignField': 'payment_otp',
                'as': 'user_iccid'
            }
        }, {
            '$unwind': {
                'path': '$user_iccid',

            }
        }, {
            '$lookup': {
                'from': 'profiles',
                'localField': 'user_iccid.iccid',
                'foreignField': 'iccid',
                'as': 'profiles'
            }
        }, {
            '$unwind': {
                'path': '$profiles'
            }
        }, {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$bundle_code'
                },
                'bundle_code': {
                    '$first': '$bundle_code'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'currency_code': {
                    '$first': '$currency_code'
                },
                'amount': {
                    '$first': '$bundles.retail_price'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },
                'bundle_category': {
                    '$first': '$bundles.bundle_category'
                },
                'region_name': {
                    '$first': '$bundles.region_name'
                },
                'country_code_list': {
                    '$first': '$bundles.country_code_list'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'email': {
                    '$first': '$email'
                },
                'otp': {
                    '$first': '$otp'
                },
                'order_number': {
                    '$first': '$user_bundle_log.order_number'
                },
                'data_amount': {
                    '$first': '$bundles.data_amount'
                },
                'data_unit': {
                    '$first': '$bundles.data_unit'
                },
                'bundle_duration': {
                    '$first': '$bundles.bundle_duration'
                },
                'activation_code': {
                    '$first': '$profiles.qr_code_value'
                },
                'smdp_address': {
                    '$first': '$profiles.smdp_address'
                },
                'matching_id': {
                    '$first': '$profiles.matching_id'
                },
                'has_lpa': {
                    '$first': '$profiles.has_lpa'
                },
                'payment_date': {
                    '$first': '$payment_date'
                }
            }
        }
    ]

    return consumer_models.UserBundle.objects().aggregate(*pipeline)


@try_except_func
def get_user_topup_history_log_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {
            "$match":
                {
                    "$and": [
                        {"datetime": {"$gte": date_from,
                                      "$lt": date_to}},

                    ]}},
        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'topup_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'iccid',
                'foreignField': 'iccid',
                'as': 'user_iccid'
            }
        }, {
            '$unwind': {
                'path': '$user_iccid'
            }
        }, {
            '$lookup': {
                'from': 'user_bundle_log',
                'localField': 'user_iccid.payment_otp',
                'foreignField': 'otp',
                'as': 'user_bundle_log'
            }
        }, {
            '$unwind': {
                'path': '$user_bundle_log'
            }
        }, {
            '$lookup': {
                'from': 'profiles',
                'localField': 'iccid',
                'foreignField': 'iccid',
                'as': 'profiles'
            }
        }, {
            '$unwind': {
                'path': '$profiles'
            }
        }, {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$topup_code'
                },
                'datetime': {
                    '$first': '$user_bundle_log.payment_date'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'email': {
                    '$first': '$email'
                },
                'amount': {
                    '$first': '$bundles.retail_price'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_code': {
                    '$first': '$bundle_code'
                },
                'topup_code': {
                    '$first': '$topup_code'
                },
                'order_number': {
                    '$first': '$user_bundle_log.order_number'
                },
                'bundle_duration': {
                    '$first': '$bundles.bundle_duration'
                },
                'data_amount': {
                    '$first': '$bundles.data_amount'
                },
                'data_unit': {
                    '$first': '$bundles.data_unit'
                },
                'smdp_address': {
                    '$first': '$profiles.smdp_address'
                },
                'activation_code': {
                    '$first': '$profiles.qr_code_value'
                },
                'currency_code': {
                    '$first': '$bundles.currency_code'
                },
                'otp': {
                    '$first': '$otp'
                },
                'transaction': {
                    '$first': 'BuyTopup'
                },
                'sent_using': {
                    '$first': 'Email'
                },
                'transaction_status': {
                    '$first': 'unread'
                },
                'bundle_category': {
                    '$first': '$bundles.bundle_category'
                },
                'region_name': {
                    '$first': '$bundles.region_name'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'matching_id': {
                    '$first': '$profiles.matching_id'
                },
                'has_lpa': {
                    '$first': '$profiles.has_lpa'
                }
            }
        }, {
            '$project': {
                'datetime': 1,
                'iccid': 1,
                'email': 1,
                'amount': 1,
                'bundle_marketing_name': 1,
                'bundle_name': 1,
                'bundle_code': 1,
                'topup_code': 1,
                'order_number': 1,
                'bundle_duration': 1,
                'data_amount': 1,
                'data_unit': 1,
                'coverage': 1,
                'smdp_address': 1,
                'activation_code': 1,
                'currency_code': 1,
                'otp': 1,
                'qr_code_link': 1,
                'transaction': 1,
                'sent_using': 1,
                'transaction_status': 1,
                'matching_id': 1,
                'has_lpa': 1,
                'bundle_category': 1,
                'region_name': 1,
                'country_list': 1
            }
        }
    ]

    return consumer_models.TopupBundles.objects().aggregate(*pipeline)


def get_bundles_bought(from_date, to_date):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=from_date) & Q(payment_date__lte=to_date))
    return consumer_models.UserBundleLog.objects(query_set)


def get_all_bundles_bought(db_name=None):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    if db_name is not None:
        with switch_db(consumer_models.UserBundleLog, db_name) as CustomModel:
            return CustomModel.objects(query_set)
    else:

        return consumer_models.UserBundleLog.objects(query_set)


@try_except_func
def get_sold_bundles(from_date, to_date):
    query_set = (Q(create_datetime__gte=from_date) & Q(create_datetime__lte=to_date))
    query_set = query_set & (Q(consumed_unit__gt=0))
    bundles_sold = consumer_models.Bundles.objects(query_set)
    return bundles_sold


@try_except_func
def get_count_user_registred(date_from, date_to):
    query_set = Q(create_date__gte=date_from) & Q(create_date__lte=date_to)
    return mobiles.AppUserDetails.objects(query_set).count()


@try_except_func
def get_unpaid_user_bundle_log(country_code, bundle_code, data_code=0, topup_code='0', otp=None, email=None):
    query_set = (Q(country_code=country_code)
                 & Q(bundle_code=bundle_code))
    if email is not None:
        query_set = query_set & Q(email=email)
    if data_code > 0:
        query_set = query_set & Q(data_code=data_code) & Q(topup_code=topup_code) & Q(payment_topup=False)
    else:
        query_set = query_set & Q(payment_status=False)
    if otp is not None:
        query_set = query_set & Q(otp=otp)
    return consumer_models.UserBundleLog.objects(query_set).first()


@try_except_func
def get_cancel_unpaid_bundle_log_by(cancel_otp):
    return consumer_models.UserBundleLog.objects(cancel_otp=cancel_otp).first()


@try_except_func
def get_unpaid_orders(starting_date, ending_date):
    date_from = datetime.datetime.strptime(starting_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(ending_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {
            '$lookup': {
                'from': 'transaction_logs',
                'localField': 'order_number',
                'foreignField': 'order_number',
                'as': 'result'
            }
        },
        {
            '$match': {
                'result': []
            }
        }
    ]
    return reseller_models.Order_history.objects(
        Q(order_status__ne="Successful") &
        Q(order_type= "BuyBundle") &
        Q(date_created__lt=date_to) &
        Q(date_created__gte=date_from)
    ).aggregate(pipeline)




@try_except_func
def delete_bundle_log_per_otp(otp):
    return consumer_models.UserBundleLog.objects(otp=otp).delete()


@try_except_func
def get_count_bundle_sold(date1, date2):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=date1) & Q(payment_date__lte=date2))
    return consumer_models.UserBundleLog.objects(query_set).count()


@try_except_func
def get_user_bundle_logs(date_from, date_to):
    query_set = Q(datetime__gte=date_from) & Q(datetime__lte=date_to)
    return consumer_models.UserBundleLog.objects(query_set)


@try_except_func
def get_allocated_bundles(date1, date2):
    query_set = Q(allocated_unit__gt=0)
    query_set = query_set & (Q(create_datetime__gte=date1) & Q(create_datetime__lte=date2))
    return consumer_models.Bundles.objects(query_set)


@try_except_func
def get_user_bundle_logs_for_sales_volume(date_from, date_to):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=date_from) & Q(payment_date__lte=date_to))
    return consumer_models.UserBundleLog.objects(query_set).sum('amount')


@try_except_func
def get_profiles_used_count(date_from, date_to):
    query_set = Q(availability__ne="Free")
    query_set = query_set & (Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to))
    profiles_used_count = consumer_models.Profiles.objects(query_set).count()
    return profiles_used_count


@try_except_func
def get_profiles_exist_count(date_from, date_to):
    query_set = Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to)
    profile_exist = consumer_models.Profiles.objects(query_set).count()
    return profile_exist


@try_except_func
def get_bundles_exist_count(date_from, date_to):
    query_set = Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to)
    bundle_exist = consumer_models.Bundles.objects(query_set).count()
    return bundle_exist


@try_except_func
def get_bundle_code_from_iccid(iccid):
    return consumer_models.UserBundle.objects(iccid=iccid).first().bundle_code


@try_except_func
def get_user_email_from_iccid(iccid):
    return consumer_models.UserBundle.objects(iccid=iccid).first().email


@try_except_func
def get_user_bundles(date_from, date_to):
    query_set = (Q(datetime__gte=date_from) & Q(datetime__lte=date_to))
    return consumer_models.UserBundle.objects(query_set)


@try_except_func
def get_bundle_name_from_bundle_code(bundle_code):
    return consumer_models.Bundles.objects(bundle_code=bundle_code).first().bundle_name


@try_except_func
def get_status_from_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid).first().status


@try_except_func
def get_msisdn_from_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid).first().msisdn


@try_except_func
def get_country_name_from_country_code(country_code):
    return consumer_models.Countries.objects(iso3_code=country_code).first().country_name


@try_except_func
def get_country_code_from_country_name(country_name):
    return consumer_models.Countries.objects(country_name__contains=country_name).first()


@try_except_func
def get_specifc_bundles(special_chars, db_name=None):
    query_set = Q(bundle_code__contains="&")
    special_chars_arr = special_chars.split(",")
    for special_sym in special_chars_arr:
        query_set = query_set | Q(bundle_code__contains=special_sym)
    if db_name != None:
        with switch_db(consumer_models.Bundles, db_name) as CustomModel:
            return CustomModel.objects(query_set)
    else:

        return consumer_models.Bundles.objects(query_set)


@try_except_func
def get_all_bundles(db_name=None):
    if db_name != None:
        with switch_db(consumer_models.Bundles, db_name) as CustomModel:
            return CustomModel.objects()
    else:
        return consumer_models.Bundles.objects()


@try_except_func
def get_paid_user_bundle_log(country_code, bundle_code, data_code=0, topup_code=0, otp=None, email=None):
    query_set = (Q(country_code=country_code)
                 & Q(bundle_code=bundle_code))
    if email is not None:
        query_set = query_set & Q(email=email)
    if data_code > 0:
        query_set = query_set & Q(data_code__in=[data_code]) & Q(topup_code__in=[topup_code])
    else:
        query_set = query_set & Q(payment_status=True)
    if otp is not None:
        query_set = query_set & Q(otp=otp)
    return consumer_models.UserBundle.objects(query_set).first()


@try_except_func
def get_paid_bunlde_count(email):
    return consumer_models.UserBundle.objects(email=email).count()


@try_except_func
def get_paid_bunlde_info(email):
    return consumer_models.UserBundle.objects(email=email)


@try_except_func
def save_user_bundle(bundle_doc):
    new_doc = {"email": bundle_doc.email,
               "otp": bundle_doc.otp,
               "country_code": bundle_doc.country_code,
               "payment_status": True,
               "bundle_code": bundle_doc.bundle_code,
               "data_code": [],
               "currency_code": bundle_doc.currency_code,
               "amount": bundle_doc.amount,
               "payment_date": bundle_doc.payment_date,
               "validy_date": bundle_doc.validy_date}

    return consumer_models.UserBundle(**new_doc).save()


@try_except_func
def get_paid_user_bundle(bundle_code, email):
    query_set = (Q(bundle_code=bundle_code) & Q(email=email) & Q(payment_status=True))
    return consumer_models.UserBundle.objects(query_set).first()


@try_except_func
def expire_profile(iccid):
    consumer_models.Profiles.objects(iccid=iccid).update(
        set__availability="Expired",
        set__status=False
    )


@try_except_func
def get_applicable_expiry_vendors():
    return consumer_models.Vendors.objects(apply_expiry=True).distinct('vendor_name')


@try_except_func
def expire_profiles(threshold):
    logger.info("Started running check profile expiry")
    list_vendors = get_applicable_expiry_vendors()
    if not list_vendors:
        logger.info("No vendors are configured with profile expiry enabled. Skipping the profile expiration process.")
        return

    # Filter the profiles to expire
    profiles = consumer_models.Profiles.objects(
        Q(expiry_date__lte=threshold) & Q(availability__ne="Expired") & Q(vendor_name__in=list_vendors)
    ).only(
        "iccid"
    )

    iccids = [profile.iccid for profile in profiles]
    if not iccids:
        logger.info("There are no profiles that have reached their expiration date.")
        return

    consumer_models.Profiles.objects(Q(iccid__in=iccids)).update(set__availability="Expired")
    # Update order history for relevant ICCIDs
    reseller_models.Order_history.objects(Q(order_status="Successful") & Q(iccid__in=iccids)).update(set__profile_status="Expired")

    logger.info("%d profiles expired successfully", len(iccids))


@try_except_func
def get_vendor(vendor_name, db_name=None):
    if db_name is not None:

        with switch_db(consumer_models.Vendors, db_name) as CustomModel:
            return CustomModel.objects(vendor_name=vendor_name).first()
    else:
        return consumer_models.Vendors.objects(vendor_name=vendor_name).first()


def add_vendors():
    for vendor in ['Flexiroam', 'eSIMGo', 'Vodafone']:
        exist_vendor = get_vendor(vendor)
        if not exist_vendor:
            new_doc = {'vendor_name': vendor}
            consumer_models.Vendors(**new_doc).save()

    return True


@try_except_func
def get_reseller():
    reseller = reseller_models.Reseller.objects()
    return reseller


@try_except_func
def get_balance_of_reseller_by_code(code):
    reseller = reseller_models.Reseller.objects(reseller_code=code).first()
    return reseller


@try_except_func
def update_paid_user_bundle(bundle_doc):
    query_set = (Q(country_code=bundle_doc.country_code) & Q(email=bundle_doc.email)
                 & Q(bundle_code=bundle_doc.bundle_code) & Q(payment_status=True))
    user_bundle = consumer_models.UserBundle.objects(query_set).first()
    if user_bundle:
        user_bundle.amount = round(float(user_bundle.amount) + float(bundle_doc.amount), 3)
        new_date = user_bundle.validy_date + timedelta(bundle_doc.validity_days)
        user_bundle.validy_date = new_date
        user_bundle.data_code.append(bundle_doc.data_code)
        user_bundle.topup_code.append(bundle_doc.topup_code)
        user_bundle.save()
        return True
    return False


@try_except_func
def add_user_iccid(doc):
    return consumer_models.UserIccid(**doc).save()


@try_except_func
def get_user_iccid(bundle_code, otp=None, email=None, status='unused'):
    query_set = Q(bundle_code=bundle_code) & Q(status=status)
    if email is not None:
        query_set = query_set & Q(email=email)
    if otp is not None:
        query_set = query_set & Q(payment_otp=otp)
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def get_used_iccid(email=None, bundle_code=None):
    query_set = Q(email=email) & Q(status='used')
    if bundle_code is not None:
        query_set = query_set & Q(bundle_code=str(bundle_code))

    return consumer_models.UserIccid.objects(query_set).distinct('iccid')


@try_except_func
def get_all_used_iccid():
    return consumer_models.UserIccid.objects(status='used')


@try_except_func
def get_user_iccid_by_otp(otp=None):
    query_set = Q(payment_otp=otp)
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def get_unused_iccid(otp=None):
    query_set = Q(payment_otp=otp) & Q(status='unused')
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def check_used_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid, status__in=["used", "expired"]).count()


def activate_bundle_check(bundle_code, start_date, ending_date, bundle_list=[]):
    try:

        if bundle_code not in bundle_list:

            pipeline=[
            {
                '$match': {
                    'description.0': {
                        '$regex': bundle_code,
                        '$options': 'i'
                    }
                }
            }, {
                '$addFields': {
                    'description_matches': {
                        '$map': {
                            'input': '$description',
                            'as': 'desc',
                            'in': {
                                'no_profile': {
                                    '$regexMatch': {
                                        'input': '$$desc',
                                        'regex': 'No more left profiles',
                                        'options': 'i'
                                    }
                                },
                                'inactivate_bundle': {
                                    '$regexMatch': {
                                        'input': '$$desc',
                                        'regex': 'Inactivate bundle Manually',
                                        'options': 'i'
                                    }
                                },
                                'model_changed': {
                                    '$regexMatch': {
                                        'input': '$$desc',
                                        'regex': 'Model change',
                                        'options': 'i'
                                    }
                                },
                                'allocate_consumed_issue': {
                                    '$regexMatch': {
                                        'input': '$$desc',
                                        'regex': 'No more plans for bundle',
                                        'options': 'i'
                                    }
                                }
                            }
                        }
                    }
                }
            }, {
                '$addFields': {
                    'all_no_profile': '$description_matches.no_profile',
                    'all.inactivate_bundle': '$description_matches.inactivate_bundle',
                    'all_model_changed': '$description_matches.model_changed',
                    'allocate_consumed_issue':'$description_matches.allocate_consumed_issue'
                }
            }, {
                '$addFields': {
                    'activate_bundle': {
                        '$cond': {
                            'if': {
                                '$and': [
                                    {
                                        '$in': [
                                            True, '$all_no_profile'
                                        ]
                                    }, {
                                        '$not': {
                                            '$or': [
                                                '$inactivate_bundle', '$all_model_changed', 'allocate_consumed_issue'
                                            ]
                                        }
                                    }
                                ]
                            },
                            'then': True,
                            'else': False
                        }
                    }
                }
            }
            ]

            check_version = main_models.UpdateBundleVersion.objects(Q(datetime__lt=ending_date) & Q(
            datetime__gte=start_date)).aggregate(pipeline)

            for version_found in check_version:

                version_found['activate_bundle']=True
                if version_found['activate_bundle']  :

                    consumer_models.Bundles.objects(bundle_code=bundle_code).update(set__is_active=True)
                    bundle_list.append(bundle_code)
            return bundle_list
    except Exception as error :
        print(f"error in checking bundle {error}")
        return bundle_list

@try_except_func
def add_user_bundle_log(doc):
    return consumer_models.UserBundleLog(**doc).save()


def reset_free_profile(iccid, start_date, ending_date, vendors: list,bundle_list:list):

    if vendors:
        profile = consumer_models.Profiles.objects(iccid=str(iccid), vendor_name__in=vendors, availability="Assigned").first()
        if profile:
            profile.update(set__availability="Free")
            if profile.bundle_code != "":

                bundle_list= activate_bundle_check(profile.bundle_code, start_date, ending_date,bundle_list)

            return True, bundle_list

    else:
        profile = consumer_models.Profiles.objects(iccid=str(iccid)).first()

        if profile:

            profile.update(set__availability="Free")
            if profile.bundle_code != "":

                bundle_list=activate_bundle_check(profile.bundle_code, start_date, ending_date,bundle_list)
            return True, bundle_list
    return False, bundle_list


@try_except_func
def get_default_language():
    lan = consumer_models.Languages.objects.filter_by(default_lang=True).first()
    return lan.language_code


@try_except_func
def get_faq(language_code):
    faq = consumer_models.FAQ.objects(language_code=language_code)
    return faq


@try_except_func
def get_page(language_code, page_title_id):
    page = consumer_models.Page.objects(language_code=language_code, page_title_id=page_title_id).first()
    return page


@try_except_func
def get_setting():
    return main_models.Settings.objects().first()


@try_except_func
def keycloack_settings(op_name):
    return main_models.KeycloackSettings.objects(op_name=op_name).first()


@try_except_func
def get_all_objects_from_version(default_response, version_token) -> object:
    version_object = get_version_from_token(version_token=version_token)
    if version_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "version not found or not active"
        return None, None, None, default_response
    default_response["data"]["version_number"] = version_object.version_number
    version_doc = version_object
    app_object = get_app_from_id(version_doc.app_id)
    if app_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "application not found or not active"
        return None, None, None, default_response
    operator_object = get_operator_from_name(op_name=app_object.op_name)
    if operator_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "operator not found or not active"
        return None, None, None, default_response

    return version_object, app_object, operator_object, default_response


@try_except_func
def get_convered_area_from_current_zone(current_zone):
    return consumer_models.CoveredAreas.objects(current_zone=current_zone)


def refresh_superadmin_token_for_reseller(date_now, username, password):
    access_token, exception = script_helper.get_token_super_admin_reseller(instance_config.reseller_super_admin_username,
                                                                           instance_config.reseller_super_admin_password)
    payload = json.dumps({
        "username": username,
        "password": password
    })
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    r = requests.request("POST", '{}/Agent/login'.format(instance_config.reseller_url), headers=headers,
                         data=payload)
    response = r.json()
    new_refresh_token = response['access_token']
    access_token['access_token'] = new_refresh_token
    reseller_models.TokenSuperAdmin.objects(role="super_admin").update(set__temp_token=new_refresh_token)
    return new_refresh_token


def add_to_update_bundle_version(bundle_list):
    latest_version = main_models.UpdateBundleVersion.objects.order_by('-datetime').first()
    new_update_version = latest_version.bundle_version_number + 1 if latest_version else 1
    current_datetime = datetime.datetime.utcnow()
    new_version = main_models.UpdateBundleVersion(bundle_version_number=new_update_version, datetime=current_datetime,
                                                  description=bundle_list)
    new_version.save()


@try_except_func
def deactivate_bundle(bundle_code):
    bundle = consumer_models.Bundles.objects(bundle_code=str(bundle_code)).first()
    if bundle:
        add_to_update_bundle_version([f"Deactivate bundle: {bundle_code}"])
        bundle.update(set__update_at=datetime.datetime.utcnow(), set__is_active=False)


@try_except_func
def enough_organization_balance(bundle_info):
    vendor = consumer_models.Vendors.objects(vendor_name=bundle_info.vendor_name).first()
    if vendor and vendor.minimal_balance:
        minimal_balance = int(vendor.minimal_balance)
        organisation_balance = get_esimgo_balance()
        return organisation_balance > minimal_balance
    return False


@try_except_func
def get_paid_bundle_log(starting_date, ending_date):
    date_from = datetime.datetime.strptime(starting_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(ending_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {
            '$lookup': {
                'from': 'transaction_logs',
                'localField': 'order_number',
                'foreignField': 'order_number',
                'as': 'transaction_logs'
            }
        },
        {
            '$unwind': {
                'path': '$transaction_logs',
                'preserveNullAndEmptyArrays': False
            }
        }
    ]
    return consumer_models.UserBundleLog.objects(
        Q(payment_status=True) & Q(datetime__lt=date_to) & Q(datetime__gte=date_from)
    ).aggregate(pipeline)


@try_except_func
def get_unpaid_bundle_log(starting_date, ending_date):
    date_from = datetime.datetime.strptime(starting_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(ending_date, '%Y-%m-%d %H:%M:%S')
    pipeline=[
       {
            '$lookup': {
                'from': 'transaction_logs',
                'localField': 'order_number',
                'foreignField': 'order_number',
                'as': 'result'
            }
        }, {
            '$match': {
                'result': []
            }
        }
    ]

    return consumer_models.UserBundleLog.objects(
        Q(payment_status=False) & Q(payment_topup=False) &Q(topup_code="") & Q(datetime__lt=date_to) & Q(datetime__gte=date_from)).aggregate(pipeline)


def ignore_runnable_script(bundle_code=None, daily_used=0):
    if daily_used > 0 and bundle_code:
        main_models.RunnableScripts.objects(bundle_code=bundle_code, daily_used=daily_used, state="Accepted").update(set__state='Obsolete')



def run_runnable_scripts(script, vendor_name, bundle_code, daily_used, state="Accepted", profile_added=0, failure_reason=""):
    try:
        doc = {"script": script, "datetime": datetime.datetime.utcnow(),
               "vendor_name": vendor_name, "bundle_code":bundle_code, "daily_used":daily_used,
               "state": state,"profile_added":profile_added,"failure_reason":failure_reason}
        return main_models.RunnableScripts(**doc).save()
    except Exception as e:
        print("Exception in run_runnable_scripts as ", str(e))
        return False


@try_except_func
def get_failed_notifications() -> Dict[str, List[NotificationHistory]]:
    """
    Retrieve and group failed webhook notifications scheduled for retry.

    This function queries all reseller webhook notifications that have failed to deliver
    and are due for a retry based on the current timestamp. It returns a dictionary
    grouping these notifications by reseller ID.
    """
    failed_notifications = reseller_models.NotificationHistory.objects(notification_type="webhook",
                                                                       status=False,
                                                                       retry_on_failed_at__lt=datetime.datetime.utcnow())

    if not failed_notifications:
        return {}

    grouped: Dict[str, List[NotificationHistory]] = defaultdict(list)
    for notification in failed_notifications:
        reseller_id = str(notification.reseller_id)
        grouped[reseller_id].append(notification)
    return grouped


@try_except_func
def get_first_time_notifications() -> Dict[str, List[NotificationHistory]]:
    """
    Retrieve and group first-time webhook notifications that haven't been sent yet.

    This function fetches all webhook notifications that have not been attempted—identified
    by the absence of request and response timestamps and payloads—and groups them by reseller ID.
    """
    first_time_notifications = reseller_models.NotificationHistory.objects(request_sent_time=None,
                                                                       response_payload=None,
                                                                       response_received_time=None,
                                                                       status=False)

    if not first_time_notifications:
        return {}

    grouped: Dict[str, List[NotificationHistory]] = defaultdict(list)
    for notification in first_time_notifications:
        reseller_id = str(notification.reseller_id)
        grouped[reseller_id].append(notification)
    return grouped


@try_except_func
def get_reseller_by_id(reseller_id: str) -> Optional[Reseller]:
    """
    Retrieve a reseller document by its unique identifier.

    :param reseller_id: The ObjectId of the reseller as a string.
    """
    reseller = reseller_models.Reseller.objects(id=ObjectId(reseller_id)).first()
    return reseller


@try_except_func
def get_bundle_by_bundle_code(bundle_code: str) -> Optional[Bundles]:
    """
    Retrieve a bundle document based on the provided bundle code.

    :param bundle_code: The unique code identifying the bundle.
    """
    bundle = consumer_models.Bundles.objects(bundle_code=bundle_code).first()
    return bundle
import random
from app_models import main_models, reseller_models
import unittest
from flask_admin.contrib.mongoengine.filters import FilterEqual, BaseMongoEngineFilter, DateTimeBetweenFilter
from bson.objectid import ObjectId
from flask_admin.contrib.mongoengine.tools import parse_like_term
import hashlib
from datetime import timedelta
from flask_login import current_user
from mongoengine.queryset.visitor import Q
from mongoengine.context_managers import switch_db
from app_models import mobiles, consumer_models
from app_helpers.main_helper import print_test_cases_path, try_except_func
from bson.objectid import ObjectId
import datetime

from app_models.reseller_models import NotificationHistory

class FilterEqualNew(BaseMongoEngineFilter):
    def apply(self, query, value):
        if value != '':
            term, data = parse_like_term(value)
            if term == 'exact':
                data = ObjectId(data)
            query = query.filter(Q(**{'id': data}))
            return query

    def operation(self):
        return 'equals'


def get_version_token():
    resp = mobiles.AppVersionList.objects().first()
    if resp is not None:
        return resp.version_token
    return None


def check_access(id_, action):
    current = main_models.User.objects(id_admin_user=id_).first()
    actions = main_models.Actions.objects(actions=action).first()
    return current, actions


def prepare_runnable_scripts(script, vendor_name, from_date=None, to_date=None, bundle_code=""):
    try:
        doc = {"script": script, "datetime": datetime.datetime.utcnow(),
               "vendor_name": vendor_name,
               "state": "Waiting", "informer": current_user.email, "bundle_code":bundle_code}
        if from_date is not None and to_date is not None:
            doc['from_date'] = datetime.datetime.strptime(from_date, "%Y-%m-%d 00:00:00")
            doc['to_date'] = datetime.datetime.strptime(to_date, "%Y-%m-%d 00:00:00")
        return main_models.RunnableScripts(**doc).save()
    except Exception as e:
        print("Exception in prepare_runnable_scripts as ", str(e))
        return False


def generate_promo_code():
    code_chars = '0123456789' + 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz'
    code = ''
    for i in range(0, 8):
        slice_start = random.randint(0, len(code_chars) - 1)
        code += code_chars[slice_start: slice_start + 1]
    doc = {
        "promo_code": code,
        "discount_percent": "0",
        "create_date": datetime.datetime.utcnow,
        "is_active": False
    }
    return mobiles.PromoCode(**doc).save()


def get_one_runnable_script(script):
    try:
        return main_models.RunnableScripts.objects(script=script, state="Accepted").first()
    except Exception as e:
        print("function get_one_runnable_script 42 ", str(e))
        return False


def update_runnable_scripts(ids):
    success = 0
    failed = 0
    for id in ids:
        runnable_object = get_runnable_script_object(id)
        if runnable_object.informer == current_user.email:
            failed += 1
        else:
            runnable_object.state = "Accepted"
            runnable_object.acceptor = current_user.email
            runnable_object.save()
            success += 1
    return success, failed


def get_runnable_script_object(id):
    return main_models.RunnableScripts.objects(id=id, state='Waiting').first()


def reset_to_waiting_scripts_states(ids):
    total = 0
    for id in ids:
        run_obj = main_models.RunnableScripts.objects(id=id).first()
        run_obj.state = "Waiting"
        run_obj.save()
        total += 1
    return total


@try_except_func
def get_vendor_name(id):
    vendor = consumer_models.Vendors.objects(id=id).first()
    return vendor.vendor_name


@try_except_func
def get_iso3(iso2_code):
    country = consumer_models.Countries.objects(iso2_code__contains=iso2_code).first()
    if country:
        return country.iso3_code
    else:
        return False


@try_except_func
def get_version_from_token(version_token):
    resp = mobiles.AppVersionList.objects(version_token=version_token).first()
    return resp


@try_except_func
def check_user(email):
    email_found = mobiles.AppEmailVerification.objects(user_email=email).first()
    return email_found


@try_except_func
def check_unverified_user(email):
    email_found = mobiles.AppEmailVerification.objects(user_email=email).first()
    return email_found


@try_except_func
def get_email_setting(email):
    return main_models.EmailSettings.objects(email=email).first()


@try_except_func
def get_bundle(bundle_code, db_name=None):
    if db_name != None:
        with switch_db(consumer_models.Bundles, db_name) as CustomModel:
            return CustomModel.objects(bundle_code=bundle_code).first()
    return consumer_models.Bundles.objects(bundle_code=bundle_code).first()


@try_except_func
def check_bundle(bundle_code, vendor_name):
    return consumer_models.Bundles.objects(vendor_name=vendor_name, bundle_vendor_code=bundle_code).first()


@try_except_func
def get_bundles_by_vendor(vendor_name, db_name=None):
     if db_name != None:
            with switch_db(consumer_models.Bundles, db_name) as CustomModel:
                return CustomModel.objects(vendor_name=vendor_name)
     else:
        return consumer_models.Bundles.objects(vendor_name=vendor_name)


@try_except_func
def add_bundle(new_doc):
    return consumer_models.Bundles(**new_doc).save()


@try_except_func
def add_user_details(new_doc):
    return mobiles.AppUserDetails(**new_doc).save()


@try_except_func
def activate_bundle(profile_name):
    consumer_models.Bundles.objects(profile_name=profile_name, deleted=False).update(
        set__is_active=True
    )


@try_except_func
def reset_user_iccid(date_now):
    new_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        days=30)
    consumer_models.UserIccid.objects(Q(expiry_date__lte=date_now) | Q(counter=5)).update(set__counter=0,
                                                                                          set__expiry_date=new_date)


@try_except_func
def reset_email_verification(date_now):
    new_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        days=30)
    mobiles.AppEmailVerification.objects(Q(is_verified=False) & Q(expiry_date__lte=date_now) | Q(count=5)).update(
        set__count=0, set__expiry_date=new_date)


@try_except_func
def reset_password_request(date_now):
    new_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        days=30)
    mobiles.AppUserPasswordReset.objects(
        Q(expiry_create_date__lte=date_now) | Q(expiry_date__lte=date_now) | Q(count=5)).update(set__count=0,
                                                                                                set__expiry_create_date=new_date,
                                                                                                set__expiry_date=new_date)


@try_except_func
def reset_contact_us(date_now):
    mobiles.AppContactUs.objects(Q(expiry_date__lte=date_now) | Q(count=3)).delete()


@try_except_func
def add_profiles(new_doc):
    return consumer_models.Profiles(**new_doc).save()


@try_except_func
def check_iccid(iccid):
    return consumer_models.Profiles.objects(iccid=iccid).first()


@try_except_func
def password_check(passwd):
    validate = True
    msg_lst = []
    special_sym = ['$', '!', '@', '#', '%']
    if len(passwd) < 6:
        msg_lst.append('length should be at least 12')
        validate = False

    if len(passwd) > 20:
        msg_lst.append('length should be not be greater than 20')
        validate = False

    if not any(char.isdigit() for char in passwd):
        msg_lst.append('Password should have at least one numeral')
        validate = False

    if not any(char.isupper() for char in passwd):
        msg_lst.append('Password should have at least one uppercase letter')
        validate = False

    if not any(char.islower() for char in passwd):
        msg_lst.append('Password should have at least one lowercase letter')
        validate = False

    if not any(char in special_sym for char in passwd):
        msg_lst.append('Password should have at least one of the symbols $@#')
        validate = False

    return validate, msg_lst


@try_except_func
def create_email_verification_record(doc):
    del doc["version_number"]
    email_verification = mobiles.AppEmailVerification(**doc).save()
    return email_verification


@try_except_func
def create_reset_password_record(doc):
    return mobiles.AppUserPasswordReset(**doc).save()


@try_except_func
def get_reset_password_record(otp):
    return mobiles.AppUserPasswordReset.objects(otp=otp).first()


@try_except_func
def delete_reset_password_record(otp):
    return mobiles.AppUserPasswordReset.objects(otp=otp).delete()


@try_except_func
def get_app_from_id(app_id):
    if isinstance(app_id.id, str):
        app_id = ObjectId(app_id.id)
    return mobiles.AppList.objects(id=app_id.id).first()


@try_except_func
def get_operator_from_name(op_name):
    operator = main_models.OperatorList.objects(op_name=op_name).first()
    return operator


@try_except_func
def get_email_verification(token, pin):
    return mobiles.AppEmailVerification.objects(verification_token=token,
                                                verification_pin=pin, is_verified=False).first()


@try_except_func
def get_email_settings():
    return main_models.EmailSettings.objects().first()


@try_except_func
def get_user(email):
    current_user = mobiles.AppUserDetails.objects(user_email=email).first()
    return current_user


@try_except_func
def get_noverified_user(email):
    current_user = mobiles.AppEmailVerification.objects(user_email=email).first()
    return current_user


@try_except_func
def get_cards_by_user(user_id):
    cards = main_models.Cards.objects(user_id=user_id)
    return cards


@try_except_func
def get_cards_by_id(id_card):
    cards = main_models.Cards.objects(id_card=id_card).first()
    return cards


@try_except_func
def delete_card(user_id, id_card):
    cards = main_models.Cards.objects(user_id=user_id, id_card=id_card).delete()
    return cards


@try_except_func
def delete_user_details(email):
    found = mobiles.AppUserDetails.objects(user_email=email).all()
    if len(found) > 0:
        mobiles.AppUserDetails.objects(user_email=email).delete()
    return True


@try_except_func
def delete_user_verification(email):
    found = mobiles.AppEmailVerification.objects(user_email=email).all()
    if len(found) > 0:
        mobiles.AppEmailVerification.objects(user_email=email).delete()
    return True


@try_except_func
def delete_user_bundle(email):
    found = consumer_models.UserBundle.objects(email=email)
    if len(found) > 0:
        consumer_models.UserBundle.objects(email=email).delete()
    return True


def validate_password(request_doc, user, email, msg_lst):
    if 'new_password' in request_doc:
        password = request_doc['new_password']
        retype_password = request_doc['retype_password']
        old_password = request_doc['old_password']
        if password == retype_password:
            validate, msg_lst = password_check(password)
            if validate:
                old_password = hashlib.md5(old_password.encode('utf-8')).hexdigest()
                found = get_user_for_login(email, old_password)

                if found:
                    user.password = hashlib.md5(password.encode('utf-8')).hexdigest()
                else:
                    validate = False
                    msg_lst.append('Your password is wrong')
        else:
            msg_lst.append('New password and retype password should be the same')
            validate = False
    return validate, msg_lst


@try_except_func
def update_user_detail(request_doc, email):
    validate = True
    msg_lst = []
    user = get_user(email)
    if 'first_name' in request_doc and len(request_doc['first_name']) != 0:
        user.first_name = request_doc['first_name']
    if 'last_name' in request_doc and len(request_doc['last_name']) != 0:
        user.last_name = request_doc['last_name']
    if 'user_image' in request_doc:
        user.user_image = bytes(request_doc['user_image'], 'utf-8')
    if 'image_extension' in request_doc:
        user.image_extension = request_doc['image_extension']
    if 'fcm_token' in request_doc:
        user.fcm_token = request_doc['fcm_token']
    validate, msg_lst = validate_password(request_doc, user, email, msg_lst)

    if validate:
        user.save()
    return validate, msg_lst


@try_except_func
def create_or_update_user_details(doc):
    current_users = mobiles.AppUserDetails.objects(user_email=doc["user_email"])
    if len(current_users) == 0:
        result = mobiles.AppUserDetails(**doc).save()
        return result

    for current_user in current_users:
        result = app_versions_from_same_operator(current_user.version_token,
                                                 doc["version_token"])
        if result:
            current_user.user_token = doc["user_token"]
            current_user.version_token = doc["version_token"]
            current_user.verification_date = doc["verification_date"]
            current_user.request_ip = doc["request_ip"]
            current_user.save
            return True


@try_except_func
def update_user_details(user_email):
    current_user = mobiles.AppUserDetails.objects(user_email=user_email).first()
    return current_user


@try_except_func
def check_user_count(user_email):
    count = mobiles.AppUserDetails.objects(user_email=user_email).count()
    return count


@try_except_func
def app_versions_from_same_operator(version_token1, version_token2):
    version1_object = get_version_from_token(version_token=version_token1)
    version2_object = get_version_from_token(version_token=version_token2)
    if version1_object is None or version2_object is None:
        return None
    app1_object = get_app_from_id(version1_object.app_id)
    app2_object = get_app_from_id(version2_object.app_id)
    if app1_object is None or app2_object is None:
        return None
    return app1_object.op_name == app2_object.op_name


@try_except_func
def get_user_from_token(user_token):
    return mobiles.AppUserDetails.objects(user_token=user_token).first()


@try_except_func
def get_user_for_login(user_email):
    user = mobiles.AppUserDetails.objects(user_email=user_email).first()
    return user


@try_except_func
def get_user_by_email(email):
    return mobiles.AppUserDetails.objects(user_email=email).first()


@try_except_func
def get_top_countries_by_users(limiter, from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":
            {
                "$and":
                    [
                        {"create_date": {"$gte": date_from,
                                         "$lte": date_to}}
                    ]
            }},
        {
            "$group": {
                "_id": {"country": "$country"},
                "number_of_users": {"$sum": 1}
            }},

        {"$sort": {"number_of_users": -1}},
        {"$limit": limiter}

    ]
    return mobiles.AppUserDetails.objects().aggregate(*pipeline)


@try_except_func
def get_top_bundles_bought(limiter, from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":

            {
                "$and": [

                    {"payment_date": {"$gte": date_from, "$lte": date_to}},
                    {"payment_status": True},
                ]}
        },

        {
            "$group": {
                "_id": {"bundle_code": "$bundle_code", "topup_code": "$topup_code"},
                "number_of_bundles_bought": {"$sum": 1},
                'bundle_code': {'$first': "$bundle_code"},
                # "bundle_marketing_name": {"$first": "$bundle_marketing_name"},
                # "bundle_name": {"$first": "$bundle_name"},
                "bundle_duration": {"$first": "$bundle_duration"},
                "data_unit": {"$first": "$data_unit"},
                "data_amount": {"$first": "$data_amount"}

            }},

        {"$sort": {"number_of_bundles_bought": -1}},
        {"$limit": limiter}

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_allocated_bundles_pipeline(date1, date2):
    date_from = datetime.datetime.strptime(date1, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(date2, '%Y-%m-%d %H:%M:%S')

    # profile_price = 1
    # total = 0
    # total += (bundle.allocated_unit * bundle.unit_price) + profile_price

    pipeline = [
        {"$match":
            {
                "$and":
                    [
                        {"create_datetime": {"$gte": date_from,
                                             "$lte": date_to}},

                        {"allocated_unit": {"$gte": 0}},

                    ]
            }},

        {
            "$project": {

                "total": {
                    "$sum": [{
                        "$multiply": ["$allocated_unit", "$unit_price"]}, 1],

                }

            }
        }
    ]
    return consumer_models.Bundles.objects().aggregate(*pipeline)


@try_except_func
def get_profit_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]

    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_profit_topup_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_topup": True},  # note syntax

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'topup_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_daily_profit_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')

    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,
                "bundle_code": 1,
                "consumed_unit": "$bundles.consumed_unit",
                "bundle_name": "$bundles.bundle_name",
                "bundle_marketing_name": "$bundles.bundle_marketing_name",
                "unit_price": "$bundles.unit_price",
                "retail_price": "$bundles.retail_price",
                "currency_code": "$bundles.currency_code",
                "country_code_list": "$bundles.country_code_list",
                "country_list": "$bundles.country_list",

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]

    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


@try_except_func
def get_daily_profit_topup_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    monty_profit = 3

    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_topup": True},  # note syntax

                ]}},

        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'topup_code',
                'as': 'bundles'},
        },
        {"$unwind": "$bundles"},

        {
            "$project": {

                "amount": 1,
                "bundle_code": 1,
                "consumed_unit": "$bundles.consumed_unit",
                "bundle_name": "$bundles.bundle_name",
                "bundle_marketing_name": "$bundles.bundle_marketing_name",
                "unit_price": "$bundles.unit_price",
                "retail_price": "$bundles.retail_price",
                "currency_code": "$bundles.currency_code",
                "country_code_list": "$bundles.country_code_list",
                "country_list": "$bundles.country_list",

                "profit": {
                    "$multiply": [
                        {
                            "$divide": [
                                {
                                    "$subtract": ["$bundles.rate_revenue", monty_profit]}, 100]}, '$amount']}

            }

        }

    ]
    return consumer_models.UserBundleLog.objects().aggregate(*pipeline)


'''match_query =         {"$match":
            {
                "$and":
                    [
                       { "payment_status":True},
                        {"payment_date": {"$gte": date_from,
                                          "$lte": date_to}}
                    ]
            }},
'''


@try_except_func
def get_user_bundle_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":
            {
                "$and": [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},

                ]}},
        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        },
        {"$unwind": {
            "path": '$bundles'

        }},
        {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'payment_otp',
                'foreignField': 'otp',
                'as': 'user_iccid'
            }
        },
        {"$unwind": {
            "path": '$user_iccid'

        }},

        {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$bundle_code'
                },
                'bundle_code': {
                    '$first': '$bundle_code'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'currency_code': {
                    '$first': '$currency_code'
                },
                'amount': {
                    '$first': '$amount'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },

                'country_code_list': {
                    '$first': '$bundles.country_code_list'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'email': {
                    '$first': '$email'
                },
                'otp': {
                    '$first': '$otp'
                }

            }
        },

        {
            '$project': {
                'bundle_code': 1,
                'amount': 1,
                'currency_code': 1,
                'bundle_name': 1,
                'bundle_marketing_name': 1,
                'country_code_list': 1,
                'country_list': 1,
                'bundle': 1,
                'email': 1,
                'iccid': 1,
                'otp': 1
            }
        }

    ]
    return consumer_models.UserBundle.objects().aggregate(*pipeline)


@try_except_func
def get_user_topup_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {"$match":
            {
                "$and": [
                    {"datetime": {"$gte": date_from,
                                  "$lte": date_to}},

                ]}},
        {
            '$lookup': {
                'from': 'bundles',
                'localField': 'topup_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        },
        {"$unwind": {
            "path": '$bundles'
        }},
        {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'payment_otp',
                'foreignField': 'otp',
                'as': 'user_iccid'
            }
        },
        {"$unwind": {
            "path": '$user_iccid'

        }},

        {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$topup_code'
                },
                'bundle_code': {
                    '$first': '$topup_code'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'currency_code': {
                    '$first': '$bundles.currency_code'
                },
                'amount': {
                    '$first': '$bundles.retail_price'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },

                'country_code_list': {
                    '$first': '$bundles.country_code_list'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'email': {
                    '$first': '$email'
                },
                'otp': {
                    '$first': '$otp'
                }
            }
        },

        {
            '$project': {
                'bundle_code': 1,
                'amount': 1,
                'currency_code': 1,
                'bundle_name': 1,
                'bundle_marketing_name': 1,
                'country_code_list': 1,
                'country_list': 1,

                'email': 1,
                'iccid': 1,
                'otp': 1
            }
        }

    ]
    return consumer_models.TopupBundles.objects().aggregate(*pipeline)


@try_except_func
def get_bundles(from_date, to_date):
    query_set = (Q(create_datetime__gte=from_date) & Q(create_datetime__lte=to_date))
    return consumer_models.Bundles.objects(query_set)


def get_bundles_bought(from_date, to_date):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=from_date) & Q(payment_date__lte=to_date))
    return consumer_models.UserBundleLog.objects(query_set)


@try_except_func
def get_sold_bundles(from_date, to_date):
    query_set = (Q(create_datetime__gte=from_date) & Q(create_datetime__lte=to_date))
    query_set = query_set & (Q(consumed_unit__gt=0))
    bundles_sold = consumer_models.Bundles.objects(query_set)
    return bundles_sold


@try_except_func
def get_count_user_registred(date_from, date_to):
    query_set = Q(create_date__gte=date_from) & Q(create_date__lte=date_to)
    return mobiles.AppUserDetails.objects(query_set).count()


@try_except_func
def check_script_data(date1, date2, script_name):
    check_script = main_models.DailyScripts.objects(
        Q(from_date__gte=date1) & Q(to_date__lte=date2) & Q(script=script_name)).first()
    if check_script is not None:
        return False
    return True


@try_except_func
def get_unpaid_user_bundle_log(country_code, bundle_code, data_code=0, topup_code='0', otp=None, email=None):
    query_set = (Q(country_code=country_code)
                 & Q(bundle_code=bundle_code))
    if email is not None:
        query_set = query_set & Q(email=email)
    if data_code > 0:
        query_set = query_set & Q(data_code=data_code) & Q(topup_code=topup_code) & Q(payment_topup=False)
    else:
        query_set = query_set & Q(payment_status=False)
    if otp is not None:
        query_set = query_set & Q(otp=otp)
    return consumer_models.UserBundleLog.objects(query_set).first()


@try_except_func
def get_cancel_unpaid_bundle_log_by(cancel_otp):
    return consumer_models.UserBundleLog.objects(cancel_otp=cancel_otp).first()


@try_except_func
def get_unpaid_bundle_log(date_to_check):
    return consumer_models.UserBundleLog.objects(
        Q(payment_status=False) & Q(payment_topup=False) & Q(datetime__lte=date_to_check)).distinct('otp')


@try_except_func
def delete_bundle_log_per_otp(otp):
    return consumer_models.UserBundleLog.objects(otp=otp).delete()


@try_except_func
def get_count_bundle_sold(date1, date2):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=date1) & Q(payment_date__lte=date2))
    return consumer_models.UserBundleLog.objects(query_set).count()


@try_except_func
def get_user_bundle_logs(date_from, date_to):
    query_set = Q(datetime__gte=date_from) & Q(datetime__lte=date_to)

    return consumer_models.UserBundleLog.objects(query_set)


@try_except_func
def get_allocated_bundles(date1, date2):
    query_set = Q(allocated_unit__gt=0)
    query_set = query_set & (Q(create_datetime__gte=date1) & Q(create_datetime__lte=date2))
    return consumer_models.Bundles.objects(query_set)


@try_except_func
def get_user_bundle_logs_for_sales_volume(date_from, date_to):
    query_set = Q(payment_status=True) | Q(payment_topup=True)
    query_set = query_set & (Q(payment_date__gte=date_from) & Q(payment_date__lte=date_to))
    return consumer_models.UserBundleLog.objects(query_set).sum('amount')


@try_except_func
def update_number_of_consumed(tenant, bundle_code,number_of_consumed,topup=False):
    bundle = get_bundle(bundle_code, tenant)
    if bundle:
        if topup:
            print("line 1056 ", bundle_code, tenant, bundle.consumed_unit_subscriber, number_of_consumed)
            bundle.consumed_unit_subscriber+= int(number_of_consumed)
        else:
            bundle.consumed_unit_subscriber= int(number_of_consumed)
        bundle.save()
        bundle = get_bundle(bundle_code, tenant)



@try_except_func
def get_user_bundle_logs_paid(tenant, topup=False):
    match_query ={
                '$match': {
                    'payment_status': True
                }
                }
    group_query = {
                '$group': {
                    '_id': {
                        'bundle_code': '$bundle_code'
                    },
                    'bundle_code': {
                        '$first': '$bundle_code'
                    },
                    'number_of_consumed': {
                        '$sum': 1
                    }
                }
            }
    if topup:
         match_query ={
                '$match': {
                    'payment_topup': True
                }}
         group_query={
                '$group': {
                    '_id': {
                        'topup_code': '$topup_code'
                    },
                    'topup_code': {
                        '$first': '$topup_code'
                    },
                    'number_of_consumed': {
                        '$sum': 1
                    }
                }
            }

    pipeline=[

            match_query
           ,  group_query
        ]
    print("pipeline ", pipeline)
    with switch_db(consumer_models.UserBundleLog, tenant) as CustomModel:
        return CustomModel.objects().aggregate(*pipeline)
    return False




@try_except_func
def get_profiles_used_count(date_from, date_to):
    query_set = Q(availability__ne="Free")
    query_set = query_set & (Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to))
    profiles_used_count = consumer_models.Profiles.objects(query_set).count()
    return profiles_used_count


@try_except_func
def get_profiles_exist_count(date_from, date_to):
    query_set = Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to)
    profile_exist = consumer_models.Profiles.objects(query_set).count()
    return profile_exist


@try_except_func
def get_profiles_exist(date_from, date_to):
    query_set = Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to)
    profile_exist = consumer_models.Profiles.objects(query_set)
    return profile_exist


@try_except_func
def get_bundles_exist_count(date_from, date_to):
    query_set = Q(create_datetime__gte=date_from) & Q(create_datetime__lte=date_to)
    bundle_exist = consumer_models.Bundles.objects(query_set).count()
    return bundle_exist

@try_except_func
def get_profile_from_vendor(vendor_name,db_name=None):
     if db_name != None:
            with switch_db(consumer_models.Profiles, db_name) as CustomModel:
                return CustomModel.objects(vendor_name=vendor_name)
     else:
        return consumer_models.Profiles.objects(vendor_name=vendor_name)



@try_except_func
def get_bundle_code_from_iccid(iccid):
    return consumer_models.UserBundle.objects(iccid=iccid).first().bundle_code


@try_except_func
def get_user_email_from_iccid(iccid):
    return consumer_models.UserBundle.objects(iccid=iccid).first().email


@try_except_func
def get_user_bundles(date_from, date_to):
    query_set = (Q(datetime__gte=date_from) & Q(datetime__lte=date_to))
    return consumer_models.UserBundle.objects(query_set)


@try_except_func
def get_bundle_name_from_bundle_code(bundle_code):
    return consumer_models.Bundles.objects(bundle_code=bundle_code).first().bundle_name


@try_except_func
def get_status_from_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid).first().status


@try_except_func
def get_msisdn_from_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid).first().msisdn


@try_except_func
def get_country_name_from_country_code(country_code):
    return consumer_models.Countries.objects(iso3_code=country_code).first().country_name


@try_except_func
def get_paid_user_bundle_log(country_code, bundle_code, data_code=0, topup_code=0, otp=None, email=None):
    query_set = (Q(country_code=country_code)
                 & Q(bundle_code=bundle_code))
    if email is not None:
        query_set = query_set & Q(email=email)
    if data_code > 0:
        query_set = query_set & Q(data_code__in=[data_code]) & Q(topup_code__in=[topup_code])
    else:
        query_set = query_set & Q(payment_status=True)
    if otp is not None:
        query_set = query_set & Q(otp=otp)
    return consumer_models.UserBundle.objects(query_set).first()


@try_except_func
def get_paid_bunlde_count(email):
    return consumer_models.UserBundle.objects(email=email).count()


@try_except_func
def get_paid_bunlde_info(email):
    return consumer_models.UserBundle.objects(email=email)


@try_except_func
def save_user_bundle(bundle_doc):
    new_doc = {"email": bundle_doc.email,
               "otp": bundle_doc.otp,
               "country_code": bundle_doc.country_code,
               "payment_status": True,
               "bundle_code": bundle_doc.bundle_code,
               "data_code": [],
               "currency_code": bundle_doc.currency_code,
               "amount": bundle_doc.amount,
               "payment_date": bundle_doc.payment_date,
               "validy_date": bundle_doc.validy_date}

    return consumer_models.UserBundle(**new_doc).save()


@try_except_func
def get_paid_user_bundle(bundle_code, email):
    query_set = (Q(bundle_code=bundle_code) & Q(email=email) & Q(payment_status=True))
    return consumer_models.UserBundle.objects(query_set).first()


@try_except_func
def expire_profile(iccid):
    consumer_models.Profiles.objects(iccid=iccid).update(
        set__availability="Expired",
        set__status=False
    )


@try_except_func
def get_vendor(vendor_name):
    return consumer_models.Vendors.objects(vendor_name=vendor_name).first()


def add_vendors():
    for vendor in ['Flexiroam', 'eSIMGo']:
        exist_vendor = get_vendor(vendor)
        if not exist_vendor:
            new_doc = {'vendor_name': vendor}
            consumer_models.Vendors(**new_doc).save()

    return True


@try_except_func
def get_vendors():
    return consumer_models.Vendors.objects()


@try_except_func
def get_reseller():
    reseller = reseller_models.Reseller.objects()
    return reseller


@try_except_func
def update_paid_user_bundle(bundle_doc):
    query_set = (Q(country_code=bundle_doc.country_code) & Q(email=bundle_doc.email)
                 & Q(bundle_code=bundle_doc.bundle_code) & Q(payment_status=True))
    user_bundle = consumer_models.UserBundle.objects(query_set).first()
    if user_bundle:
        user_bundle.amount = round(float(user_bundle.amount) + float(bundle_doc.amount), 3)
        new_date = user_bundle.validy_date + timedelta(bundle_doc.validity_days)
        user_bundle.validy_date = new_date
        user_bundle.data_code.append(bundle_doc.data_code)
        user_bundle.topup_code.append(bundle_doc.topup_code)
        user_bundle.save()
        return True
    return False


@try_except_func
def add_user_iccid(doc):
    return consumer_models.UserIccid(**doc).save()


@try_except_func
def get_user_iccid(bundle_code, otp=None, email=None, status='unused'):
    query_set = Q(bundle_code=bundle_code) & Q(status=status)
    if email is not None:
        query_set = query_set & Q(email=email)
    if otp is not None:
        query_set = query_set & Q(payment_otp=otp)
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def get_used_iccid(email=None, bundle_code=None):
    query_set = Q(email=email) & Q(status='used')
    if bundle_code is not None:
        query_set = query_set & Q(bundle_code=str(bundle_code))

    return consumer_models.UserIccid.objects(query_set).distinct('iccid')


@try_except_func
def get_all_used_iccid():
    return consumer_models.UserIccid.objects(status='used')


@try_except_func
def get_unused_iccid(otp=None):
    query_set = Q(payment_otp=otp) & Q(status='unused')
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def check_used_iccid(iccid):
    return consumer_models.UserIccid.objects(iccid=iccid, status="used").count()


@try_except_func
def add_user_bundle_log(doc):
    return consumer_models.UserBundleLog(**doc).save()



@try_except_func
def add_user_bundle(doc):
    return consumer_models.UserBundle(**doc).save()


def update_user_bundle(otp, email, topup_code):
    query_set = Q(otp=otp) & Q(email=email)
    consumer_models.UserBundle.objects(query_set).update(push__topup_code=topup_code, set__payment_topup=True)


@try_except_func
def add_topup_bundle(doc):
    return consumer_models.TopupBundles(**doc).save()


@try_except_func
def reset_free_profile(iccid):
    consumer_models.Profiles.objects(iccid=str(iccid)).update(set__availability="Free")


@try_except_func
def get_default_language():
    lan = consumer_models.Languages.objects.filter_by(default_lang=True).first()
    return lan.language_code


@try_except_func
def get_faq(language_code):
    faq = consumer_models.FAQ.objects(language_code=language_code)
    return faq


@try_except_func
def get_page(language_code, page_title_id):
    page = consumer_models.Page.objects(language_code=language_code, page_title_id=page_title_id).first()
    return page


@try_except_func
def get_setting():
    return main_models.Settings.objects().first()


@try_except_func
def keycloack_settings(op_name):
    return main_models.KeycloackSettings.objects(op_name=op_name).first()


@try_except_func
def get_all_objects_from_version(default_response, version_token) -> object:
    version_object = get_version_from_token(version_token=version_token)
    if version_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "version not found or not active"
        return None, None, None, default_response
    default_response["data"]["version_number"] = version_object.version_number
    version_doc = version_object
    app_object = get_app_from_id(version_doc.app_id)
    if app_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "application not found or not active"
        return None, None, None, default_response
    operator_object = get_operator_from_name(op_name=app_object.op_name)
    if operator_object is None:
        default_response['responseCode'] = 2
        default_response["message"] = "operator not found or not active"
        return None, None, None, default_response

    return version_object, app_object, operator_object, default_response


@try_except_func
def save_history_logs(result, log):
    with switch_db(main_models.HistoryLogs, result) as CustomModel:
        return CustomModel(**log).save()


@try_except_func
def get_user_from_user_iccid_(otp, email):
    return consumer_models.UserIccid.objects(email=email, payment_otp=otp).first()


@try_except_func
def get_bundle_log(otp):
    return consumer_models.UserBundleLog.objects(otp=otp).first()


@try_except_func
def get_bundle_by_code(bundle_code, not_deleted=True,db_name=None):
    if not_deleted:
        return consumer_models.Bundles.objects(bundle_code=bundle_code, deleted=False).first()
    else:
        return consumer_models.Bundles.objects(bundle_code=bundle_code).first()


@try_except_func
def get_history_log(order_number):
    return main_models.HistoryLogs.objects(order_number=order_number).first()


@try_except_func
def check_history(result, order_number, email, iccid, bundle_code, topup_code=""):
    query_set = (Q(order_number=order_number) & Q(email=email) & Q(iccid=str(iccid)) & Q(bundle_code=str(bundle_code)))
    if topup_code != "":
        query_set = query_set & (Q(topup_code=topup_code))
    with switch_db(main_models.HistoryLogs, result) as CustomModel:
        res = CustomModel.objects(query_set).count()
    if res > 0:
        return True
    return False


def update_history_log(history_log_id, topup_code):
    main_models.HistoryLogs.objects(history_log_id=history_log_id).update(set__topup_code=topup_code,
                                                                          set__transaction="BuyTopup")


@try_except_func
def get_profile_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid)).first()
    if profile:
        return profile
    return None


def get_transaction_logs(order_number):
    query_set = (Q(order_number=order_number) & Q(transaction_status=True))
    transaction_logs = consumer_models.TransactionLogs.objects(query_set).first()
    if transaction_logs:
        return transaction_logs
    return None


def get_payment_status_from_user_bundle_log(order_number):
    query_set = (Q(order_number=order_number) & Q(payment_status=False))
    user_bundle_log = consumer_models.UserBundleLog.objects(query_set).first()
    if user_bundle_log:
        return user_bundle_log
    return None


def get_topup_status_from_user_bundle_log(order_number):
    query_set = (Q(order_number=order_number) & Q(payment_topup=False))
    user_bundle_log = consumer_models.UserBundleLog.objects(query_set).first()
    if user_bundle_log:
        return user_bundle_log
    return None


def get_user_bundle_log(order_number):
    query_set = Q(order_number=order_number)
    user_bundle_log = consumer_models.UserBundleLog.objects(query_set).first()
    if user_bundle_log:
        return user_bundle_log
    return None


def update_payment_status(order_number):
    consumer_models.UserBundleLog.objects(order_number=order_number).update(set__payment_status=True)


def update_topup_status(order_number):
    x = consumer_models.UserBundleLog.objects(order_number=order_number).update(set__payment_topup=True)
    return x


@try_except_func
def update_used_iccid(otp, email, iccid, plan_uid):
    consumer_models.UserIccid.objects(email=email, payment_otp=otp).update(set__status="used", set__plan_uid=plan_uid,
                                                                           set__iccid=iccid)


def get_user_iccid_from_otp_email(otp, email):
    query_set = (Q(payment_otp=otp) & Q(email=email))
    return consumer_models.UserIccid.objects(query_set).first()


@try_except_func
def get_sku_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid)).first()
    if profile:
        return profile.sku
    return None


@try_except_func
def save_profile(data):
    data = data.replace('ICCID,Matching ID,RSP URL,Bundle,Reference', '')
    data = data.replace('\n', '')
    new_str = data.split(",")
    qr_code_value = 'LPA:1${}${}'.format(new_str[2], new_str[1])
    new_data = {'vendor_name': 'eSIMGo', 'iccid': new_str[0], 'sku': new_str[0], 'availability': 'Assigned',
                'status': True,
                'smdp_address': new_str[2], 'matching_id': new_str[1], 'has_lpa': True, 'qr_code_value': qr_code_value}
    if consumer_models.Profiles(**new_data).save():
        return new_str[0], new_str[2], new_str[1]
    return False, False, False


@try_except_func
def get_user_history_bundle_pipeline(tenant, otp, bundle_code, email):
    pipeline = [
        {
            '$match': {
                '$and': [

                    {
                        'payment_status': True
                    }, {
                        'payment_topup': False
                    }, {
                        'bundle_code': bundle_code
                    }, {
                        'otp': otp,
                    }, {
                        'email': email
                    }
                ]
            }
        }, {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'otp',
                'foreignField': 'payment_otp',
                'as': 'user_iccid'
            }
        }, {
            '$unwind': {
                'path': '$user_iccid',
                'includeArrayIndex': 'payment_otp',
                'preserveNullAndEmptyArrays': False
            }
        }, {
            '$lookup': {
                'from': 'profiles',
                'localField': 'user_iccid.iccid',
                'foreignField': 'iccid',
                'as': 'profiles'
            }
        }, {
            '$unwind': {
                'path': '$profiles'
            }
        }, {
            '$lookup': {
                'from': 'user_bundle_log',
                'localField': 'otp',
                'foreignField': 'otp',
                'as': 'user_bundle_log'
            }
        }, {
            '$unwind': {
                'path': '$user_bundle_log'
            }
        }, {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$bundle_code'
                },
                'bundle_code': {
                    '$first': '$bundle_code'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'currency_code': {
                    '$first': '$currency_code'
                },
                'amount': {
                    '$first': '$bundles.retail_price'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },
                'bundle_category': {
                    '$first': '$bundles.bundle_category'
                },
                'region_name': {
                    '$first': '$bundles.region_name'
                },
                'country_code_list': {
                    '$first': '$bundles.country_code_list'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'email': {
                    '$first': '$email'
                },
                'otp': {
                    '$first': '$otp'
                },
                'order_number': {
                    '$first': '$user_bundle_log.order_number'
                },
                'data_amount': {
                    '$first': '$bundles.data_amount'
                },
                'data_unit': {
                    '$first': '$bundles.data_unit'
                },
                'bundle_duration': {
                    '$first': '$bundles.bundle_duration'
                },
                'activation_code': {
                    '$first': '$profiles.qr_code_value'
                },
                'smdp_address': {
                    '$first': '$profiles.smdp_address'
                },
                'matching_id': {
                    '$first': '$profiles.matching_id'
                },
                'has_lpa': {
                    '$first': '$profiles.has_lpa'
                },
                'payment_date': {
                    '$first': '$payment_date'
                }
            }
        }
    ]
    with switch_db(consumer_models.UserBundle, tenant) as CustomModel:
        return CustomModel.objects().aggregate(*pipeline)
    return False


@try_except_func
def get_user_bundle_pipeline(from_date, to_date):
    date_from = datetime.datetime.strptime(from_date, '%Y-%m-%d %H:%M:%S')
    date_to = datetime.datetime.strptime(to_date, '%Y-%m-%d %H:%M:%S')
    pipeline = [
        {
            '$match': {
                '$and': [
                    {"payment_date": {"$gte": date_from,
                                      "$lte": date_to}},
                    {"payment_status": True},
                ]
            }
        }, {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$lookup': {
                'from': 'user_iccid',
                'localField': 'otp',
                'foreignField': 'payment_otp',
                'as': 'user_iccid'
            }
        }, {
            '$unwind': {
                'path': '$user_iccid',
                'includeArrayIndex': 'otp',
                'preserveNullAndEmptyArrays': True
            }
        }, {
            '$lookup': {
                'from': 'profiles',
                'localField': 'user_iccid.iccid',
                'foreignField': 'iccid',
                'as': 'profiles'
            }
        }, {
            '$unwind': {
                'path': '$profiles',
                'includeArrayIndex': 'iccid',
                'preserveNullAndEmptyArrays': True
            }
        }, {
            '$lookup': {
                'from': 'bundles',
                'localField': 'bundle_code',
                'foreignField': 'bundle_code',
                'as': 'bundles'
            }
        }, {
            '$unwind': {
                'path': '$bundles'
            }
        }, {
            '$group': {
                '_id': {
                    'otp': '$otp',
                    'email': '$email',
                    'bundle_code': '$bundle_code'
                },
                'bundle_code': {
                    '$first': '$bundle_code'
                },
                'iccid': {
                    '$first': '$user_iccid.iccid'
                },
                'currency_code': {
                    '$first': '$currency_code'
                },
                'amount': {
                    '$first': '$amount'
                },
                'bundle_name': {
                    '$first': '$bundles.bundle_name'
                },
                'bundle_marketing_name': {
                    '$first': '$bundles.bundle_marketing_name'
                },
                'bundle_category': {
                    '$first': '$bundles.bundle_category'
                },
                'region_name': {
                    '$first': '$bundles.region_name'
                },
                'country_code_list': {
                    '$first': '$bundles.country_code_list'
                },
                'country_list': {
                    '$first': '$bundles.country_list'
                },
                'email': {
                    '$first': '$email'
                },
                'otp': {
                    '$first': '$otp'
                },
                'order_number': {
                    '$first': '$user_bundle_log.order_number'
                },
                'data_amount': {
                    '$first': '$bundles.data_amount'
                },
                'data_unit': {
                    '$first': '$bundles.data_unit'
                },
                'bundle_duration': {
                    '$first': '$bundles.bundle_duration'
                }
            }
        }
    ]

    return consumer_models.UserBundle.objects().aggregate(*pipeline)

@try_except_func
def get_bundle_details(category):
    return consumer_models.BundleDetails.objects(default_message=True, category=category).order_by('-detail_code').first()

def update_bundle_details_type(value,language_code='en'):
     try:
        updated=True
        # Update the documents
        res = consumer_models.BundleDetails.objects(detail_code=str(value),  language_code=language_code).first()
        if res :
            res.detail_code = int(value)
            res.save(validate=False)

     except ValueError as  e:
          print(f'ValueError on updating value of  detail code {value} : {e}')
          updated=False
     return updated


def get_vendor_zones(vendor: str):
    if not isinstance(vendor, str):
        raise Exception("must pass str value")
    zone_list = list(consumer_models.Zones.objects(vendor_name=vendor).order_by('zone_name').values_list('zone_name'))
    return zone_list



def get_countries_by_zone(zone: str):
    if not isinstance(zone, str):
        raise Exception("must pass str value")
    zone: consumer_models.Zones = consumer_models.Zones.objects(zone_name=zone).first()
    if not zone:
        return []
    return zone.country_list

def get_reseller_with_webhook_notification() -> list:
    """
        Retrieve all resellers that are configured to receive webhook notifications.

        This function filters and returns all resellers whose notification type is set to "webhook"
        and who have a valid (non-null, existing) callback URL.
    """
    reseller = reseller_models.Reseller.objects(notification_type="webhook",
                                                callback_url__ne=None,
                                                callback_url__exists=True)
    return list(reseller)

def get_notification_histories_by_reseller(reseller_id: str, bundle_code: str, status: bool,
                                           require_unsent_and_unreceived: bool = False) -> NotificationHistory:
    """
    Retrieve NotificationHistory documents for a given reseller and bundle code,
    filtered by status and optionally by whether the notification was sent or received.

    :param reseller_id: The ID of the reseller.
    :param bundle_code: The code of the bundle associated with the notification.
    :param status: The status of the notification (True for success, False for failure).
    :param require_unsent_and_unreceived: If True, also filter out any notifications that have
            request_sent_time, response_payload, or response_received_time set.
    """
    filters = {
        "reseller_id": reseller_id,
        "bundle_code": bundle_code,
        "status": status,
    }

    if require_unsent_and_unreceived:
        filters.update({
            "request_sent_time": None,
            "response_payload": None,
            "response_received_time": None,
        })

    return reseller_models.NotificationHistory.objects(**filters).order_by('-created_at').first()

import smtplib
import json
import requests
from flask import render_template
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import hashlib
import secrets
import string
from instance import consumer_config as instance_config
from b2c_helpers.vendors import ESIMGo

def generate_hashed_otp(otp):
    hashed = hashlib.sha512(str(otp).encode('ascii')).hexdigest()
    return hashed


def generate_hash(to_encryprt):
    md5_encrypt = hashlib.md5(to_encryprt.upper().encode('utf-8')).hexdigest()
    final = hashlib.sha1(md5_encrypt.encode('utf-8')).hexdigest()
    return final


def generate_card_hash(to_encryprt):
    print("uppercase ", to_encryprt.upper())
    final = hashlib.md5(to_encryprt.upper().encode('utf-8')).hexdigest()
    print("md5 ", final)
    return final


def get_token_esimgo(username, password):
    try:
        headers = {}
        payload = {'email': username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post('{}/login'.format(instance_config.flexiroam_url), headers=headers,
                          data=payload)
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"


def get_token_flexiroam(username, password):
    try:
        headers = {}
        payload = {'email': username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post('{}/user/login/v1'.format(instance_config.flexiroam_url), headers=headers,
                          data=payload)
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"


def generate_temp_otp(n):
    new_rand = ''.join(secrets.choice(string.digits) for i in range(0, n))
    return new_rand


def get_token_dynamic_vendor(vendor_name):
    try:
        vendor_url = getattr(instance_config, vendor_name.lower() + "_url")
        username = getattr(instance_config, vendor_name.lower() + "_username")
        password = getattr(instance_config, vendor_name.lower() + "_password")
        headers = {}
        payload = {'email': username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        r = requests.post('{}/user/login/v1'.format(vendor_url), files=payload)

        response = r.json()
        return response, ""
    except Exception as e:
        print("Exception at get_token_dynamic_vendor:")
        print(str(e))
        return False, "Couldn't get token!"


def get_token_super_admin_reseller(username, password):
    try:
        payload = json.dumps({
                "username": username,
                "password": password
            })
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        r = requests.request("POST",'{}/Agent/login'.format(instance_config.reseller_url), headers=headers,
                          data=payload)
        response = r.json()

        return response, ""

    except Exception:
        return False, "couldn`t login"


def get_bundle_info(bundle_code):
    r = requests.get('https://10.110.26.33:6003/getbundlesfilteredbycode/{}'.format(bundle_code))
    bundles = r.json()
    bundle_dic = {}
    if bundles:
        for bundle in bundles:
            print("bundle ", bundle)
            country_list = bundle['bundle_country_list'].split(", ")

            bundle_dic = {'bundle_name': bundle['bundle_name'],
                          'country_list': bundle['bundle_country_list'],
                          'price': bundle['subscriber_charge'],
                          'currency_code': bundle['currency_code'],
                          'validity': bundle['subscription_days'], 'gprs_limit': bundle['package'],
                          'activation_policy': bundle['activation_policy'],
                          'available_top_up': bundle['available_top_up'],
                          'eKYC': bundle['eKYC'],
                          'plan_type': bundle['plan_type'],

                          }
            bundle_dic['topup_types'] = None
            bundle_dic['country_list'] = country_list
            bundle_dic['topup_code'] = 0
            if 'topup_types' in bundle:
                bundle_dic['topup_types'] = bundle['topup_types']
                bundle_dic['topup_code'] = bundle['topup_code']
    return bundle_dic


def send_checkout_api(merchant_key, order_number, price, order_currency, order_description, cancel_url,
                      success_url, card_holder_name, billing_address, email, hashed):
    url = "{}/api/v1/session".format('https://checkout.montypay.com')
    payload = {
        "merchant_key": merchant_key,
        "operation": "purchase",
        "methods": [
            "card"
        ],
        "order": {
            "number": order_number,
            "amount": str(price),
            "currency": order_currency,
            "description": order_description
        },
        "billing_address": {
            "country": billing_address['country'],
            "state": billing_address['state'],
            "city": billing_address['city'],
            "address": billing_address['address'],
            "zip": billing_address['zip_code'],
            "phone": billing_address['msisdn']
        },
        "cancel_url": cancel_url,
        "success_url": success_url,
        "customer": {
            "name": card_holder_name,
            "email": email
        },
        "hash": hashed
    }
    headers = {'Content-Type': 'application/json'}
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    new_resp = response.json()
    return new_resp


def send_notification(fcm_token, registration_ids, status, data):
    try:

        registration_ids = 'key=' + str(registration_ids)
        url = "https://fcm.googleapis.com/fcm/send"
        payload = {
            "registration_ids": [
                fcm_token

            ],
            "notification": {

                "content-available": True,
                "priority": "high"
            },
            "data": data
        }
        if status == "success":
            payload['purchased'] = True
            payload['notification']['title'] = "Bundle added"
            payload['notification']['body'] = "Bundle added"
            payload['data']['purchased'] = True
        if status == "failed":
            payload['purchased'] = False
            payload['notification']['title'] = "Payment canceled or failed"
            payload['notification']['body'] = "Payment canceled or failed"
            payload['data']['purchased'] = False
        headers = {'Content-Type': 'application/json', 'Authorization': registration_ids}
        response = requests.request("POST", url, headers=headers, data=json.dumps(payload))

        new_resp = response.json()
        return new_resp
    except Exception as e:
        print("error ", str(e))


def send_billing_address(user):
    billing_address = {'country': 'LB', 'state': 'payer_state', 'city': 'Esim customer city',
                       'address': 'Esim customer address',
                       'zip_code': '123456'}
    billing_address['msisdn'] = user.msisdn

    return billing_address


def send_otp_email(user, pwd, sender, stmp_server, smtp_port, recipient, data, template_file, use_gmail=True):
    message = MIMEMultipart("alternative")
    message["Subject"] = 'New OTP'
    message["From"] = sender
    message["To"] = recipient
    msg_text = render_template(template_file, data=data, email=recipient)
    msg_html = MIMEText(msg_text, 'html')
    message.attach(msg_html)
    # send your email
    sending_email(user, pwd, sender, stmp_server, smtp_port,
                  message.as_string(), recipient, use_gmail)


def send_contact_email(user, pwd, sender, stmp_server, smtp_port, recipient, data, template_file, contact_email,
                       use_gmail=True):
    message = MIMEMultipart("alternative")
    message["Subject"] = 'New message '
    message["From"] = sender
    message["To"] = sender
    msg_text = render_template(template_file, data=data, email=recipient)
    msg_html = MIMEText(msg_text, 'html')
    message.attach(msg_html)
    # send your email
    sending_email(user, pwd, sender, stmp_server, smtp_port,
                  message.as_string(), contact_email, use_gmail)
    return True


def sending_email(user, pwd, sender_email, stmp_server, smtp_port, message, recipient, use_gmail=True):
    with smtplib.SMTP(stmp_server, smtp_port) as server:
        try:
            if use_gmail:
                server.ehlo()
                server.starttls()

            server.login(user, pwd)
            server.sendmail(sender_email,
                            recipient, message)
        except Exception as e:
            # Print any error messages to stdout
            print(e, flush=True)
        finally:
            print("email sent ")
            server.quit()


def send_error_email(list_of_users, email_subject, msg, email_settings ):

    send_exception_email(email_settings, list_of_users, email_subject, msg)

def send_exception_email(email_settings, list_of_users, subject, body):
    for to_user in list_of_users:

            smtp_server = smtplib.SMTP(email_settings.smtp_server, email_settings.smtp_port)
            smtp_server.ehlo()
            smtp_server.starttls()
            smtp_server.login(email_settings.username, email_settings.password)
            header = 'To:' + to_user + '\n' + 'From: ' + email_settings.username + '\n' + 'Subject:' + subject + ' \n'
            msg = header + '\n' + body + '\n\n'
            smtp_server.sendmail(email_settings.username, to_user, msg)
            smtp_server.close()


def get_esimgo_balance():
    balance = ESIMGo().get_balance()
    return balance


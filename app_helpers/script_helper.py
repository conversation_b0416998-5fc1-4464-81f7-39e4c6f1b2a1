import logging
import smtplib
import json
import requests
from flask import render_template
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import hashlib
import secrets
import string
from instance import consumer_config as instance_config
from app_helpers import db_helper
from datetime import datetime, timedelta
from app_helpers.db_helper import get_profile_from_iccid, get_user_iccid
from app_helpers.main_helper import try_except_func
from app_models import mobiles, consumer_models, reseller_models
from b2c_helpers.vendors import (
    MontyMobile,
    Indosat,
    Flexiroam,
    ESIMGo,
    Vodafone,
    Orange,
    FlexiroamAPI,
    Bayobab
)
from b2c_helpers.db_helper import get_profile_status_for_each_vendor
from app_helpers.db_helper import get_vendor
from b2c_helpers.db_helper import accumulate_vendor_data
BAYOBAB_VENDOR = "Bayobab"

logger = logging.getLogger(__name__)

not_valid_json = "Response content is not valid JSON"
allocation_exception = "couldn`t allocate bundle"
app_json = "application/json"


def generate_hashed_otp(otp):
    hashed = hashlib.sha512(str(otp).encode("ascii")).hexdigest()
    return hashed


def generate_hash(to_encryprt):
    md5_encrypt = hashlib.md5(to_encryprt.upper().encode("utf-8")).hexdigest()
    final = hashlib.sha1(md5_encrypt.encode("utf-8")).hexdigest()
    return final


def generate_card_hash(to_encryprt):
    print("uppercase ", to_encryprt.upper())
    final = hashlib.md5(to_encryprt.upper().encode("utf-8")).hexdigest()
    print("md5 ", final)
    return final


def get_token_esimgo(username, password):
    try:
        headers = {}
        payload = {"email": username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post(
            "{}/login".format(instance_config.flexiroam_url),
            headers=headers,
            data=payload,
        )
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"


def get_token_flexiroam(username, password):
    try:
        headers = {}
        payload = {"email": username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"
        r = requests.post(
            "{}/user/login/v1".format(instance_config.flexiroam_url),
            headers=headers,
            data=payload,
        )
        response = r.json()

        return response, ""
    except Exception:
        return False, "couldn`t allocate bundle"


def generate_temp_otp(n):
    new_rand = "".join(secrets.choice(string.digits) for i in range(0, n))
    return new_rand


def get_bundle_info(bundle_code):
    r = requests.get("https://10.110.26.33:6003/getbundlesfilteredbycode/{}".format(bundle_code))
    bundles = r.json()
    bundle_dic = {}
    if bundles:
        for bundle in bundles:
            print("bundle ", bundle)
            country_list = bundle["bundle_country_list"].split(", ")

            bundle_dic = {
                "bundle_name": bundle["bundle_name"],
                "country_list": bundle["bundle_country_list"],
                "price": bundle["subscriber_charge"],
                "currency_code": bundle["currency_code"],
                "validity": bundle["subscription_days"],
                "gprs_limit": bundle["package"],
                "activation_policy": bundle["activation_policy"],
                "available_top_up": bundle["available_top_up"],
                "eKYC": bundle["eKYC"],
                "plan_type": bundle["plan_type"],
            }
            bundle_dic["topup_types"] = None
            bundle_dic["country_list"] = country_list
            bundle_dic["topup_code"] = 0
            if "topup_types" in bundle:
                bundle_dic["topup_types"] = bundle["topup_types"]
                bundle_dic["topup_code"] = bundle["topup_code"]
    return bundle_dic


def send_checkout_api(
        merchant_key,
        order_number,
        price,
        order_currency,
        order_description,
        cancel_url,
        success_url,
        card_holder_name,
        billing_address,
        email,
        hashed,
):
    url = "{}/api/v1/session".format("https://checkout.montypay.com")
    payload = {
        "merchant_key": merchant_key,
        "operation": "purchase",
        "methods": ["card"],
        "order": {
            "number": order_number,
            "amount": str(price),
            "currency": order_currency,
            "description": order_description,
        },
        "billing_address": {
            "country": billing_address["country"],
            "state": billing_address["state"],
            "city": billing_address["city"],
            "address": billing_address["address"],
            "zip": billing_address["zip_code"],
            "phone": billing_address["msisdn"],
        },
        "cancel_url": cancel_url,
        "success_url": success_url,
        "customer": {"name": card_holder_name, "email": email},
        "hash": hashed,
    }
    headers = {"Content-Type": "application/json"}
    response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
    new_resp = response.json()
    return new_resp


def send_notification(fcm_token, registration_ids, status, data):
    try:

        registration_ids = "key=" + str(registration_ids)
        url = "https://fcm.googleapis.com/fcm/send"
        payload = {
            "registration_ids": [fcm_token],
            "notification": {"content-available": True, "priority": "high"},
            "data": data,
        }
        if status == "success":
            payload["purchased"] = True
            payload["notification"]["title"] = "Bundle added"
            payload["notification"]["body"] = "Bundle added"
            payload["data"]["purchased"] = True
        if status == "failed":
            payload["purchased"] = False
            payload["notification"]["title"] = "Payment canceled or failed"
            payload["notification"]["body"] = "Payment canceled or failed"
            payload["data"]["purchased"] = False
        headers = {
            "Content-Type": "application/json",
            "Authorization": registration_ids,
        }
        response = requests.request("POST", url, headers=headers, data=json.dumps(payload))

        new_resp = response.json()
        return new_resp
    except Exception as e:
        print("error ", str(e))


def send_billing_address(user):
    billing_address = {
        "country": "LB",
        "state": "payer_state",
        "city": "Esim customer city",
        "address": "Esim customer address",
        "zip_code": "123456",
    }
    billing_address["msisdn"] = user.msisdn

    return billing_address


def send_otp_email(
        user,
        pwd,
        sender,
        stmp_server,
        smtp_port,
        recipient,
        data,
        template_file,
        use_gmail=True,
):
    message = MIMEMultipart("alternative")
    message["Subject"] = "New OTP"
    message["From"] = sender
    message["To"] = recipient
    msg_text = render_template(template_file, data=data, email=recipient)
    msg_html = MIMEText(msg_text, "html")
    message.attach(msg_html)
    # send your email
    sending_email(
        user,
        pwd,
        sender,
        stmp_server,
        smtp_port,
        message.as_string(),
        recipient,
        use_gmail,
    )


def send_contact_email(
        user,
        pwd,
        sender,
        stmp_server,
        smtp_port,
        recipient,
        data,
        template_file,
        contact_email,
        use_gmail=True,
):
    message = MIMEMultipart("alternative")
    message["Subject"] = "New message "
    message["From"] = sender
    message["To"] = sender
    msg_text = render_template(template_file, data=data, email=recipient)
    msg_html = MIMEText(msg_text, "html")
    message.attach(msg_html)
    # send your email
    sending_email(
        user,
        pwd,
        sender,
        stmp_server,
        smtp_port,
        message.as_string(),
        contact_email,
        use_gmail,
    )
    return True


def sending_email(user, pwd, sender_email, stmp_server, smtp_port, message, recipient, use_gmail=True):
    with smtplib.SMTP(stmp_server, smtp_port) as server:
        try:
            if use_gmail:
                server.ehlo()
                server.starttls()

            server.login(user, pwd)
            server.sendmail(sender_email, recipient, message)
        except Exception as e:
            # Print any error messages to stdout
            print(e, flush=True)
        finally:
            print("email sent ")
            server.quit()


def allocate_per_vendor(vendor_name, user_iccid, bundle_vendor_code, bundle_log, bundle_info, email):
    """
    allocate bundle for user based on vendor.
    """
    allocate, exception, plan_uid, iccid = None, None, None, None
    try:
        is_topup = True if bundle_log.topup_code != "" else False
        if vendor_name == "Flexiroam":
            if bundle_info.bundle_vendor_code == bundle_vendor_code:
                allocate, exception = allocate_flexi_bundle(user_iccid, str(bundle_vendor_code))
                allocate["send_email"] = True
                iccid = user_iccid.iccid
                plan_uid = None

                if allocate["data"] is not None and "plan_uid" in allocate["data"]:
                    plan_uid = allocate["data"]["plan_uid"]
                    sku = db_helper.get_profile_from_iccid(iccid)
                    plan_data = get_user_consumption(sku, None)
                    print("plan_data ", plan_data)
                    expiry_date = plan_data["data"][0]["end_date"]
                    user_iccid.expiry_date = expiry_date
                    user_iccid.save()
        elif vendor_name == "Vodafone":
            if is_topup:
                allocate, exception = topup_vodafone_bundle(bundle_vendor_code, user_iccid, bundle_log.order_number)
            else:
                allocate, exception = allocate_vodafone_bundle(bundle_vendor_code, bundle_log.order_number)
            if allocate["success"]:
                plan_uid = allocate["order_reference"]
        elif vendor_name == "eSIMGo":
            is_topup = True if db_helper.get_bundle_log(str(bundle_log.otp)).topup_code != "" else False
            exception = ""
            if is_topup:
                allocate, exception = topup_esimgo_bundle(bundle_vendor_code=bundle_vendor_code, user_iccid=user_iccid)
            else:
                allocate, exception = allocate_esimgo_bundle(bundle_vendor_code)
                print("exception if exist ", exception)
                print("New eSIM will be assigned for bundle {}".format(bundle_vendor_code))
            if allocate["success"]:
                print(
                    " line 448 str(bundle_log.bundle_code), str(bundle_log.otp), email ",
                    str(bundle_log.bundle_code),
                    str(bundle_log.otp),
                    email,
                )
                user_iccid = get_user_iccid(str(bundle_log.bundle_code), str(bundle_log.otp), email)
                print("user iccid unused ")
                if user_iccid is None:
                    print("user iccid used ")
                    user_iccid = get_user_iccid(
                        str(bundle_log.bundle_code),
                        str(bundle_log.otp),
                        email,
                        status="used",
                    )

                print(
                    "bundle_info.vendor_code==bundle_vendor_code ",
                    bundle_info.bundle_vendor_code,
                    bundle_vendor_code,
                )
                if bundle_info.bundle_vendor_code == bundle_vendor_code and user_iccid:
                    user_iccid.iccid = allocate["iccid"]
                    user_iccid.activation_code = allocate["matching_id"]
                    user_iccid.expiry_date = datetime.utcnow() + timedelta(days=365)
                    user_iccid.save()
                    iccid = allocate["iccid"]
                    plan_uid = allocate["order_reference"]
                    user_iccid = db_helper.get_user_iccid(str(bundle_info.bundle_code), bundle_log.otp, email)

        elif vendor_name == "eSIMGoMock":

            order_reference = generate_temp_otp(7)
            iccid = user_iccid.iccid
            profile = get_profile_from_iccid(iccid)
            if profile is not None:
                user_iccid.smdp_address = profile.smdp_address
                user_iccid.matching_id = profile.matching_id

                user_iccid.save()
                user_iccid = db_helper.get_user_iccid(str(bundle_info.bundle_code), bundle_log.otp, email)
                plan_uid = order_reference
                allocate = {
                    "success": True,
                    "order_reference": order_reference,
                    "iccid": profile.iccid,
                    "smdp_address": profile.smdp_address,
                    "matching_id": profile.matching_id,
                    "send_email": True,
                }
                exception = ""
            else:
                allocate = {
                    "success": False,
                    "order_reference": order_reference,
                    "iccid": user_iccid,
                    "smdp_address": "",
                    "matching_id": "",
                    "send_email": False,
                }
                exception = "Couldn't allocate"

    except Exception as e:
        print("exception , ", str(e))
        allocate = {"success": False, "send_email": False}
        plan_uid = None
        iccid = None
        user_iccid = None

    return allocate, exception, plan_uid, iccid, user_iccid


def allocate_esimgo_bundle(bundle_code):
    try:
        url = f"{instance_config.esimgo_url}/v2.2/orders"
        headers = {"X-API-Key": instance_config.esim_go_token}
        payload = {
            "assign": True,
            "order": [{"item": bundle_code, "quantity": 1, "type": "bundle"}],
            "type": "transaction",
        }
        print("Buying eSIMGo bundle {}".format(bundle_code))
        response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            print("Bundle {} successfully bought".format(bundle_code))
            order_reference = response.json()["orderReference"]
            url = "{}/v2.2/esims/assignments/{}".format(instance_config.esimgo_url, order_reference)
            get_response = requests.request("GET", url, headers=headers)
            if get_response.status_code == 200:
                iccid, smdp_address, matching_id = db_helper.save_profile(get_response.text)
                return {
                           "success": True,
                           "order_reference": order_reference,
                           "iccid": iccid,
                           "smdp_address": smdp_address,
                           "matching_id": matching_id,
                       }, ""
        return {
                   "order_reference": "",
                   "iccid": "",
                   "smdp_address": "",
                   "matching_id": "",
               }, "Couldn't allocate"
    except Exception as e:
        print("exception in allocation ", e)
        return {
                   "order_reference": "",
                   "iccid": "",
                   "smdp_address": "",
                   "matching_id": "",
               }, "Couldn't allocate"


def topup_esimgo_bundle(bundle_vendor_code, user_iccid):
    url = f"{instance_config.esimgo_url}/v2.2/orders"
    payload = {
        "type": "transaction",
        "assign": False,
        "Order": [{"type": "bundle", "quantity": 1, "item": bundle_vendor_code}],
    }
    headers = {"X-API-Key": instance_config.esim_go_token}
    response = requests.request("POST", url, headers=headers, json=payload)
    print("Buying eSIMGo bundle {}".format(bundle_vendor_code))
    if response.status_code == 200:
        url = f"{instance_config.esimgo_url}/v2.2/esims/apply"
        payload = {"iccid": user_iccid.iccid, "name": bundle_vendor_code, "repeat": 1}
        response = requests.request("POST", url, headers=headers, json=payload)
        print("applying eSIMGo bundle {} to ICCID {}".format(bundle_vendor_code, user_iccid.iccid))
        if response.status_code == 200:
            print("Topup Success for ICCID {}".format(user_iccid.iccid))
            response_json = response.json()
            order_reference = response_json["applyReference"]
            profile = db_helper.get_profile_from_iccid(user_iccid.iccid)
            return {
                       "success": True,
                       "order_reference": order_reference,
                       "iccid": user_iccid.iccid,
                       "smdp_address": profile.smdp_address,
                       "matching_id": profile.matching_id,
                       "send_email": True,
                   }, ""
        else:
            print("Topup Failed for ICCID {}".format(user_iccid.iccid))
            print("Reason: {}".format(str(response.json()["message"])))
            return {
                       "success": False,
                       "order_reference": None,
                       "iccid": None,
                       "send_email": False,
                   }, "Bad Request"


def allocate_flexi_bundle(user_iccid, plan_code):
    try:
        bundles = False

        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)
        if access_token:
            headers = {"token": access_token["data"]["token"], "Content-Type": app_json}
            sku = db_helper.get_sku_from_iccid(user_iccid.iccid)
            lst_sku = "[" + sku + "]"
            payload = {
                "sku": lst_sku,
                "plan_code": plan_code,
                "plan_start_type_id": "1",
                "discount": "",
            }
            r = requests.post(
                "{}/plan/load/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=json.dumps(payload),
                timeout=125.5,
            )
            bundles = r.json()
        return bundles, ""
    except Exception as e:
        return False, allocation_exception


@try_except_func
def allocate_vodafone_bundle(bundle_code):
    try:

        url = "{}/things/consumer-profile/{}".format(instance_config.vodafone_url, bundle_code)
        headers = {
            "ResponseURLs": instance_config.callback_get_iccid,
            "Authorization": "Bearer " + instance_config.esim_go_token,
        }
        response = requests.request("POST", url, headers=headers, data={})
        if response.status_code == 200:
            res = response.json()
            order_reference = res["acknowledgement"]["id"]
            print("order_reference ", order_reference)
            return {
                       "success": True,
                       "order_reference": order_reference,
                       "iccid": "",
                       "smdp_address": "",
                       "matching_id": "",
                   }, "Waiting reply"
        return {
                   "success": False,
                   "order_reference": "",
                   "iccid": "",
                   "smdp_address": "",
                   "matching_id": "",
               }, "Couldn't allocate"
    except Exception as e:
        print("exception in allocation ", e)
        return {
                   "success": False,
                   "order_reference": "",
                   "iccid": "",
                   "smdp_address": "",
                   "matching_id": "",
               }, "Couldn't allocate"


@try_except_func
def topup_vodafone_bundle(bundle_vendor_code, user_iccid, order_number):
    url = "{}/network/top-up/iccids/{}".format(instance_config.vodafone_url, user_iccid.iccid)
    headers = {
        "ResponseURLs": instance_config.callback_get_iccid,
        "Authorization": "Bearer " + str(instance_config.vodafone_token),
    }
    payload = {"customReference": order_number, "bundleID": bundle_vendor_code}
    response = requests.request("PUT", url, headers=headers, json=payload)
    print("Buying Vodafone bundle {}".format(bundle_vendor_code))
    print(response.status_code)
    print(response.json())
    if response.status_code == 202:
        print("Topup Success for ICCID {}".format(user_iccid.iccid))
        res = response.json()
        order_reference = res["acknowledgement"]["id"]
        print("order_reference ", order_reference)
        return {
                   "success": True,
                   "order_reference": order_reference,
                   "iccid": "",
                   "smdp_address": "",
                   "matching_id": "",
                   "send_email": False,
               }, "Waiting reply"
    return {
               "success": False,
               "order_reference": "",
               "iccid": "",
               "smdp_address": "",
               "matching_id": "",
               "send_email": False,
           }, "Couldn't allocate"


def get_user_consumption(sku, bundle_info):
    print("sku.vendor_name ", sku.vendor_name)
    bundles = {}
    if sku.vendor_name == "Flexiroam":

        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)
        if access_token:
            headers = {"token": access_token["data"]["token"]}
        payload = {"sku": str(sku.sku)}
        print("payload ", payload)
        url = "{}/plan/simplan/v1".format(instance_config.flexiroam_url)
        r = requests.post(url, headers=headers, data=payload, timeout=1.5)
        if r.status_code == 200:
            bundles = r.json()
            print("bundles ")
            print(bundles)
        return bundles
    if sku.vendor_name == "eSIMGo":
        print("iccid: ", sku.iccid)
        print("vendor_bundle_name: ", bundle_info.bundle_vendor_code)
        url = "{}/v2.2/esims/{}/bundles/{}".format(instance_config.esimgo_url, sku.iccid,
                                                   bundle_info.bundle_vendor_code)
        headers = {"X-API-Key": instance_config.esim_go_token}
        response = requests.request("GET", url, headers=headers)
        print(response.json())
        if response.status_code == 200:
            bundles = {
                "data": [
                    {
                        "data_allocated": 0,
                        "data_used": 0,
                        "fullspeed_data_amount": 0,
                        "start_date": "",
                        "end_date": "",
                        "daily_usage": {},
                    }
                ]
            }

            data_allocated = response.json()["assignments"][0]["initialQuantity"]
            remaining_quantity = response.json()["assignments"][0]["remainingQuantity"]
            data_used = data_allocated - remaining_quantity
            fullspeed_data_amount = bundle_info.fullspeed_data_amount
            print("fullspeed_data_amount:", fullspeed_data_amount)
            bundles["data"][0]["data_allocated"] = data_allocated
            print("bundle", bundles)
            bundles["data"][0]["data_used"] = data_used
            bundles["data"][0]["fullspeed_data_amount"] = fullspeed_data_amount
            bundles["data"][0]["start_date"] = str(datetime.now())
            bundles["data"][0]["end_date"] = str(datetime.now() + timedelta(days=7))
            bundles["data"][0]["daily_usage"] = {}
        return bundles
    return bundles


def get_all_topups_balance(iccid, instance_config):
    try:
        url = f"{instance_config.vodafone_url}/network/top-up/history/iccids/{iccid}/90"
        headers = {"Authorization": "Bearer " + instance_config.vodafone_token}
        response = requests.request("GET", url, headers=headers)
        cumulative_topup_balance = 0
        if response.status_code == 200:
            for bundle in response.json():
                if bundle.get("topUp"):
                    cumulative_topup_balance += int(bundle["topUp"]["value"])
                elif bundle.get("initial"):
                    cumulative_topup_balance += int(bundle["initial"]["value"])
        return round((float(cumulative_topup_balance)), 2)
    except Exception as e:
        print("User cannot view his total bundles", e)
        return 10


def get_full_consumption(sku: consumer_models.Profiles):
    print("sku.vendor_name ", sku.vendor_name)
    bundles = {"status": False, "data": [], "message": ""}
    profile = {"profile_status": ""}
    esim = {"esim_status": ""}
    final_resp = {}

    if sku.vendor_name == "eSIMGo":
        esimgo = ESIMGo()
        consumptions = esimgo.get_profile_consumption(sku.sku)
        bundles_data = []
        print("consumptions for esimgo:", consumptions)

        if not consumptions:
            bundles["message"] = "No Data to view"

        else:
            bundles = consumptions["bundles"]
            for bundle in bundles:
                for data in bundle["assignments"]:
                    plan_status = ""
                    order_history = reseller_models.Order_history.objects(iccid=sku.iccid).first()
                    if order_history:
                        plan_status = order_history.plan_status

                    bundles_data_entry = {
                        "data_allocated": data["initialQuantity"],
                        "data_balance": data["remainingQuantity"],
                        "data_used": data["initialQuantity"] - data["remainingQuantity"],
                        "plan_code": bundle["name"],
                        "plan_status": plan_status,
                    }

                    if "startTime" in data:
                        start_date = data["startTime"]
                        bundles_data_entry["start_date"] = start_date

                    if "endTime" in data:
                        end_date = data["endTime"]
                        bundles_data_entry["end_date"] = end_date

                    bundles_data.append(bundles_data_entry)

            bundles = {"status": True, "data": bundles_data, "message": ""}

        esim_status = get_profile_status_for_each_vendor(vendor_name="eSIMGo", sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status
        return bundles, profile, esim, consumptions

    if sku.vendor_name == "Vodafone":
        vodafone = Vodafone()
        consumptions = vodafone.get_profile_consumption(sku.sku)
        print("consumptions for vodafone:", consumptions)
        if not consumptions:
            bundles["message"] = "No Data to view"

        else:
            bundles_data = []
            bundles_json = consumptions["bundle"]
            for json_obj in bundles_json:
                # Extracting each field for the current object
                bundle_type = json_obj["bundleType"]
                bundle_balance = json_obj["bundleBalance"]
                bundle_expiry = json_obj["bundleExpiry"]
                is_bundle_depleted = json_obj["isBundleDepleted"]
                is_bundle_expired = json_obj["isBundleExpired"]
                remaining_data = round((float(bundle_balance)), 2)
                end_date = bundle_expiry if "bundleExpiry" in json_obj else None

                data_allocated = get_all_topups_balance(sku.iccid, instance_config)

                start_date = ""
                expiry_date = ""
                plan_status = ""
                plan_uid = ""

                # user_iccid = consumer_models.UserIccid.objects(iccid=sku.iccid).first()
                #
                # if user_iccid is None:
                #     print("The user is not in user iccid")
                order_history = reseller_models.Order_history.objects(iccid=sku.iccid).first()

                if order_history:
                    plan_status = order_history.plan_status
                    start_date = order_history.date_created
                    plan_uid = order_history.plan_uid

                    # else:
                    #     plan_status = user_iccid.status
                    #     start_date = ""
                    #     plan_uid = user_iccid.plan_uid
                if end_date != None:
                    expiry_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S.%fZ")
                    # else:
                    #     expiry_date = user_iccid.expiry_date

                if data_allocated == 0:
                    data_allocated = remaining_data

                data_used = data_allocated - remaining_data

                if data_allocated < data_used or data_allocated < 0:
                    return []

                bundles_data.append(
                    {
                        "data_allocated": data_allocated,
                        "data_balance": data_used,
                        "data_used": data_used,
                        "start_date": start_date,
                        "end_date": expiry_date,
                        "plan_code": plan_uid,
                        "plan_status": plan_status,
                    }
                )

            bundles = {
                "status": True,
                "data": bundles_data,
                "message": bundles["message"],
            }

        url = "{}/network/things/iccids/{}".format(instance_config.vodafone_url, sku.iccid)
        headers = {"Authorization": "Bearer " + instance_config.vodafone_token}
        response_ = requests.request("GET", url, headers=headers)
        if response_.status_code == 400:
            profile["profile_status"] = ""

        if response_.status_code == 200:
            profile_json = response_.json()["thing"]["profiles"]
            for json_obj in profile_json:
                profile_status = json_obj["status"]
                profile["profile_status"] = profile_status
                print("profile", profile)

        esim_status = get_profile_status_for_each_vendor(vendor_name="Vodafone", sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status
        return bundles, profile, esim, consumptions

    if sku.vendor_name == "Monty Mobile":
        monty_mobile = MontyMobile()
        json_response = monty_mobile.get_profile_consumption(str(sku.sku))
        print("consumptions for monty mobile:", json_response)

        if not json_response:
            print("An error occured while get_profile_consumption for MontyMobile")
        else:
            bundles["data"] = json_response.get("data", {}).get("plans", [])
            bundles["status"] = True
            print(bundles)
        esim_status = get_profile_status_for_each_vendor(vendor_name="Monty Mobile", sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status
        return bundles, profile, esim, json_response

    if sku.vendor_name == BAYOBAB_VENDOR:
        logger.info("Fetching consumption for bayobab vendor with profile %s", sku.iccid)

        bayobab = Bayobab()
        json_response = bayobab.fetch_consumption(sku.iccid)
        if not json_response:
            bundles["message"] = f"Error occurred while fetching consumption for the profile: {sku.iccid}"
            return bundles, profile, esim, json_response

        if json_response.get("errorCode"):
            bundles["message"] = f'Error while fetching consumption for profile {sku.iccid}, error: {json_response.get("errorMessage")}'
            return bundles, profile, esim, json_response
        bundles["status"] = True
        for content in json_response.get("content", []):
            order = reseller_models.Order_history.objects(plan_uid=content.get("id")).first()

            if not order:
                continue
            bundle = consumer_models.Bundles.objects(bundle_code=order.bundle_code).first()

            if not bundle:
                continue

            data_amount = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
            for balance in content.get("balance", []):
                if balance.get("service") == "DATA":
                    bundles["data"].append(
                        {
                            "plan_code": content.get("id"),
                            "plan_status": order.plan_status,
                            "data_allocated": data_amount,
                            "data_balance": balance["value"],
                            "data_used": data_amount - balance["value"],
                            "start_date": content.get("attachTimestamp"),
                            "end_date": content.get("expirationDate"),
                        }
                    )

        esim_status = get_profile_status_for_each_vendor(vendor_name=BAYOBAB_VENDOR, sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status
        return bundles, profile, esim, json_response

    if sku.vendor_name == "Indosat":
        bundles_data = []
        indosat = Indosat()
        data_remaining = 0
        data_used = 0
        remaining_data: float = 0
        plan_status = ""
        consumptions = indosat.get_terminal_rating(sku.iccid)

        total_data_amount, _, _, queued_bundles = accumulate_vendor_data(sku.iccid)
        print("consumptions response in Indosat", consumptions)

        if consumptions:
            consumptions_length = len(consumptions)
            queued_bundles_length = len(queued_bundles)
            if consumptions_length < queued_bundles_length:
                expired_plans = queued_bundles[0: queued_bundles_length - consumptions_length]

                missing_data = [{"ratePlanName": plan["bundle_vendor_code"], "dataRemaining": 0} for plan in
                                expired_plans]

                consumptions = missing_data + consumptions
            total_data_remaining = 0
            for plan in consumptions:
                data_remaining = plan.get("dataRemaining", None)
                if data_remaining is not None:
                    data_remaining = float(data_remaining)
                    print("data_remaining converted to float", data_remaining)

                else:
                    # check ctd usage and overage limit reached and status
                    current_consumption = indosat.get_device_consumption(iccid=sku.iccid)
                    current_status = current_consumption.get("status")
                    current_overage_limit_reached = current_consumption.get("overageLimitReached")
                    if (current_status == "DEACTIVATED") or current_overage_limit_reached:
                        data_remaining = 0
                        data_used = total_data_amount * 1024
                        continue

                    print("data_remaining line 3276", data_remaining)
                    bundle = consumer_models.Bundles.objects(bundle_vendor_code=plan["ratePlanName"]).first()

                    if bundle:
                        data_remaining = bundle.data_amount * 1024
                        print("data_remaining in if bundle line 3275", data_remaining)

                total_data_remaining += float(data_remaining)

            data_allocated = total_data_amount * 1024
            if total_data_remaining > 0 and total_data_amount != 0:
                data_used = data_allocated - total_data_remaining

            order_history = reseller_models.Order_history.objects(iccid=sku.iccid).first()
            if order_history:
                plan_status = order_history.plan_status
            bundles_data.append(
                {
                    "data_allocated": data_allocated,
                    "data_balance": total_data_remaining,
                    "data_used": data_used,
                    "start_date": "",
                    "end_date": "",
                    "plan_code": "",
                    "plan_status": plan_status,
                }
            )

        esim_status = get_profile_status_for_each_vendor(vendor_name="Indosat", sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status

        bundles = {"status": True, "data": bundles_data, "message": ""}
        profile = ""
        return bundles, profile, esim, consumptions

    if sku.vendor_name == "Orange":
        vendor_orange = Orange()
        bundles_data = []

        # Fetch consumption details for the given MSISDN
        consumptions = vendor_orange.get_profile_consumption(str(sku.msisdn))
        print("consumptions for orange:", consumptions)

        # Initialize bundles dictionary
        bundles = {"status": False, "data": [], "message": "No Data to view"}

        if consumptions:
            consumption_orange = consumptions["buckets"]
            remaining_data = 0

            # Calculate the total remaining data balance
            for bundle in consumption_orange:
                for data in bundle['bucketBalances']:
                    remaining_data += float(data["remainingValue"])

            # Fetch order history for the given profile
            order_history = reseller_models.Order_history.objects(iccid=sku.iccid, order_status="Successful").order_by(
                "-date_created")

            for history in order_history:
                # Fetch bundle details for the order
                bundle = consumer_models.Bundles.objects(bundle_code=history.bundle_code).first()
                if bundle:
                    # Convert data amount to MB if the unit is GB
                    data_amount = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
                    balance_data = data_amount

                    # Adjust balance based on remaining data and plan status
                    if history.plan_status == "Active":
                        if remaining_data <= 0:
                            balance_data = 0
                        elif remaining_data >= data_amount:
                            balance_data = data_amount
                        else:
                            balance_data = remaining_data
                        remaining_data -= data_amount

                    bundles_data_entry = {
                        "data_allocated": data_amount,
                        "data_balance": balance_data,
                        "data_used": data_amount - balance_data,
                        "plan_code": history.plan_uid,
                        "plan_status": history.plan_status,
                        "start_date": history.date_created,
                        "end_date": history.expiry_date
                    }

                    bundles_data.append(bundles_data_entry)

                # Update the bundles dictionary with the processed data
                bundles.update({"status": True, "data": bundles_data, "message": ""})

            # Map profile status from French to English
            status_mapping = {
                "En cours": "Released",
                "Alerte": "Alert",
                "Fair-usé": "Installed",
                "Bloqué": "Blocked",
            }

            profile_status = status_mapping.get(consumption_orange[0]["status"], "Unknown")
            print(f"Profile Status: {profile_status}")
            esim["esim_status"] = profile_status
        return bundles, profile, esim, consumptions

    if sku.vendor_name == "Flexiroam":
        flexiroam = Flexiroam()
        consumptions = flexiroam.get_profile_consumption(sku.sku)
        print("consumptions for flexiroam:", consumptions)

        if not consumptions:
            bundles["message"] = "No Data to view"

        else:
            bundles["data"] = consumptions["data"]
            bundles["status"] = True
            print(bundles)

        esim_status = get_profile_status_for_each_vendor(vendor_name="Flexiroam", sku=sku.sku, iccid=sku.iccid)
        esim["esim_status"] = esim_status
        return bundles, profile, esim, consumptions

    if sku.vendor_name == "FlexiroamV2":
        flexiroam = FlexiroamAPI()
        consumptions = flexiroam.get_sim_details(sku.sku)
        bundles_data = []
        esim_status = get_profile_status_for_each_vendor(
            vendor_name="FlexiroamV2",
            sku=sku.sku,
            iccid=sku.iccid
        )
        esim["esim_status"] = esim_status

        if not consumptions:
            bundles["message"] = "No Data to view"
            return bundles, profile, esim, consumptions

        # Process active plans
        for plan in consumptions["active_plans"]:

            if sku.availability == "Assigned":

                # Get bundle details from Bundles collection
                order: reseller_models.Order_history = reseller_models.Order_history.objects(plan_uid=plan["plan_uuid"]).first()
                if not order:
                    data_allocated = plan["balance"]
                else:
                    bundle: consumer_models.Bundles = consumer_models.Bundles.objects(bundle_code=order.bundle_code).first()
                    # Calculate data allocated in MB
                    if not bundle:
                        logging.error("Couldn't find order with plan_uid %s", plan["plan_uuid"])
                        continue
                    data_allocated = bundle.data_amount * 1024 if bundle.data_unit == "GB" else bundle.data_amount
            else:
                data_allocated = plan["balance"]

            # Current balance from the plan
            data_balance = plan["balance"]

            # Calculate data used
            data_used = data_allocated - data_balance

            # Convert assigned_on to match expiration_date format
            try:
                # Parse the GMT format date
                start_date = datetime.strptime(plan["assigned_on"], "%a, %d %b %Y %H:%M:%S GMT")
                # Convert to the same format as expiration_date
                formatted_start_date = start_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            except Exception as e:
                logging.error("Error formatting start date: %s", e)
                formatted_start_date = plan["assigned_on"]

            bundles_data_entry = {
                "data_allocated": data_allocated,
                "data_balance": data_balance,
                "data_used": data_used,
                "plan_code": plan["plan_uuid"],
                "plan_status": plan["status"],
                "start_date": formatted_start_date,
                "end_date": plan["expiration_date"]
            }
            bundles_data.append(bundles_data_entry)

        bundles = {
            "status": True,
            "data": bundles_data,
            "message": ""
        }
        return bundles, profile, esim, consumptions
    return bundles, profile, esim, None

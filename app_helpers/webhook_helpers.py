from app_helpers import db_helper, main_helper
from app_ext_models.power_bi_models import SaleVolume, postgress
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

monty_profit = 3
email_subject_ = "eSIM b2c"


def send_sales_volume(postgres_db):
    postgres_db.app_context().push()
    date1, date2, date_time = main_helper.get_pday_time_string_format(1)
    count = SaleVolume.query.count()
    profit_bundle = db_helper.get_daily_profit_pipeline(date1, date2)
    for prof in profit_bundle:
        # print(type(prof['country_list']))
        for index in range(len(prof['country_list'])) and range(len(prof['country_code_list'])):
            value_country_list = prof['country_list'][index]
            value_country_code_list = prof['country_code_list'][index]
            sale_volume_values = SaleVolume(
                id=count + 1,
                date=date_time,
                consumed_unit=prof['consumed_unit'],
                country_name=value_country_list,
                country_code=value_country_code_list,
                bundle_name=prof['bundle_name'],
                bundle_marketing_name=prof['bundle_marketing_name'],
                bundle_code=prof['bundle_code'],
                unit_price=prof['unit_price'],
                retail_price=prof['retail_price'],
                currency_code=prof['currency_code'],
                profit_cost=prof['profit'],
            )
            count = count + 1
            postgress.session.add(sale_volume_values)
            postgress.session.commit()
    cleanup(postgress.session)

    profit_topup = db_helper.get_daily_profit_topup_pipeline(date1, date2)
    for proff in profit_topup:
        for index in range(len(proff['country_list'])) and range(len(proff['country_code_list'])):
            value_country_list = proff['country_list'][index]
            value_country_code_list = prof['country_code_list'][index]
            sale_volume_values = SaleVolume(
                id=count + 1,
                date=date_time,
                consumed_unit=proff['consumed_unit'],
                country_name=value_country_list,
                country_code=value_country_code_list,
                bundle_name=proff['bundle_name'],
                bundle_marketing_name=proff['bundle_marketing_name'],
                bundle_code=proff['bundle_code'],
                unit_price=proff['unit_price'],
                retail_price=proff['retail_price'],
                currency_code=proff['currency_code'],
                profit_cost=proff['profit'],
            )
            count = count + 1
            postgress.session.add(sale_volume_values)
            postgress.session.commit()
            postgress.session.close()


def sending_email(user, pwd, sender_email, stmp_server, smtp_port, message, recipient, use_gmail=True):
    with smtplib.SMTP(stmp_server, smtp_port) as server:
        try:
            if use_gmail:
                server.ehlo()
                server.starttls()

            server.login(user, pwd)
            server.sendmail(sender_email,
                            recipient, message)
        except Exception as e:
            # Print any error messages to stdout
            print(e, flush=True)
        finally:
            print("email sent ")
            server.quit()


def send_email(user, pwd, sender, stmp_server, smtp_port, recipient, data,
               use_gmail=True):
    message = MIMEMultipart("alternative")
    message["Subject"] = 'Error handling '
    message["From"] = sender
    message["To"] = recipient
    msg_text = "errorr "
    msg_html = MIMEText(msg_text, 'html')
    message.attach(msg_html)
    for user_ in recipient:
        message["To"] = user_
        # send your email
        sending_email(user, pwd, sender, stmp_server, smtp_port,
                      message.as_string(), recipient, use_gmail)
    return True


def cleanup(session, engine_container):
    """
    This method cleans up the session object and also closes the connection pool using the dispose method.
    """
    session.close()
    engine_container.dispose()

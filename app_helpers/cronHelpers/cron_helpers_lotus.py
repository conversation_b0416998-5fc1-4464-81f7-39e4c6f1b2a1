import datetime
import logging
from typing import List, Dict, Tuple

from dateutil.relativedelta import relativedelta

from app_helpers import db_helper
from app_helpers.db_helper import (
    add_bundle,
    check_bundle,
    add_profiles,
    check_iccid,
    add_to_update_bundle_version,
)
from app_models import consumer_models
from app_models.consumer_models import Profiles
from b2c_helpers.constaints import LOTUS_FLARE_VENDOR
from b2c_helpers.support_helper import get_vendor_info
from b2c_helpers.vendors_integrations.lotus_flare_vendor import LotusFlare
from instance import consumer_config as instance_config

logger = logging.getLogger(__name__)


def lotusflare_save_profiles():
    try:
        logger.info("Start Saving Profiles From LotusFlare")
        save_profile_lotusflare_vendor_call()
    except Exception as e:
        logger.error("Error on lotusflare_save_profiles, %s", str(e))


def save_profile_lotusflare_vendor_call():
    vendor = db_helper.get_vendor(LOTUS_FLARE_VENDOR)
    profile_threshold = vendor.profiles_threshold_count or 10

    current_profiles = db_helper.get_profiles_by_vendor(
        vendor_name=LOTUS_FLARE_VENDOR, availability="Free", profile_status={"$ne": "Delete"}
    )
    if len(current_profiles) > profile_threshold:
        logger.info("No need to load more profiles for LotusFlare. Available = %s", len(current_profiles))
        return

    lotusflare_vendor = LotusFlare()

    # Ask about profile retrieval number
    profiles = lotusflare_vendor.allocate_esims(profile_threshold - len(current_profiles))
    logger.info("lotus vendor allocation profiles.")

    if not profiles:
        logger.warning("No Profiles Returned From LotusFlare")
        return

    save_lotusflare_profile_in_db(profiles)


def save_lotusflare_profile_in_db(profiles):
    for profile in profiles:
        iccid = profile["iccid"]
        existing_profile = check_iccid(iccid)
        if existing_profile and existing_profile.profile_status != profile.status:
            logger.info("Updating existing profile with iccid %s to status %s", iccid, profile.status)
            existing_profile.status = profile.status
            existing_profile.save()
            continue

        qr_code = profile["esim_qr"]
        qr_code_value = qr_code.replace("LPA:1", "")
        qr_value_splited = qr_code_value.split("$")

        new_profile = {
            "vendor_name": LOTUS_FLARE_VENDOR,
            "iccid": iccid,
            "imsi": profile["imsi"],
            "sku": profile["ki"],
            "qr_code_value": f"LPA:1${qr_value_splited[1]}${qr_value_splited[2]}",
            "profile_status": profile["status"],
            "smdp_address": qr_value_splited[1],
            "matching_id": qr_value_splited[2],
            "status": False,
            "profile_names": "L"
        }

        add_profiles(new_profile)
        logger.info("Added LotusFlare profile ICCID: %s", iccid)


def lotusflare_save_bundles():
    try:
        logger.info("Start Saving Bundles From LotusFlare")
        save_bundle_lotusflare_vendor_call()
    except Exception as e:
        logger.error("Error on lotusflare_save_bundles, %s", str(e))


def save_bundle_lotusflare_vendor_call():
    lotusflare_vendor = LotusFlare()
    bundles = lotusflare_vendor.get_products()
    logger.info("LotusFlare Get Bundles Response: %s", bundles)
    if not bundles:
        logger.warning("No Bundles Returned From LotusFlare")
        return

    save_lotusflare_bundle_in_db(bundles)


def normalize_duration(bundle: dict) -> Tuple[int, str]:
    """Convert duration to days based on unit and return duration and its string form."""
    duration = int(bundle["duration"])
    unit = bundle.get("duration_unit", "").lower()

    if unit == "month":
        duration_days = duration * 30
    elif unit == "year":
        duration_days = duration * 365
    elif unit == "week":
        duration_days = duration * 7
    else:
        duration_days = duration

    return duration_days, str(duration_days)


def build_bundle_document(
    bundle: dict,
    vendor,
    bundle_detail: dict,
    profile_name: str,
    list_countries_code: List[str],
    rate_revenue: float
) -> Dict:
    """Construct a new bundle dictionary ready to be saved."""
    duration_days, duration_str = normalize_duration(bundle)
    refill_group = f"{profile_name}{vendor.vendor_code}".replace(" ", "")

    new_bundle = {
        "vendor_name": LOTUS_FLARE_VENDOR,
        "bundle_name": bundle["name"],
        "bundle_marketing_name": bundle["name"],
        "bundle_code": datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%S%f") + "_" + bundle["id"],
        "bundle_vendor_name": bundle["name"],
        "bundle_vendor_code": bundle["id"],
        "data_amount": bundle["amount"],
        "data_unit": bundle["amount_unit"],
        "validity_amount": duration_str,
        "bundle_duration": duration_days,
        "fullspeed_data_amount": bundle["networks"][0]["speed"].replace("G", ""),
        "rate_revenue": float(rate_revenue),
        "retail_price": 1.0,
        "profile_names": profile_name,
        "allocated_unit": 1,
        "deleted": False,
        "refill_group": refill_group,
        "supplier_vendor": str(vendor.vendor_code),
        "bundle_category": "country",
        "is_region": False,
        "plan_type_code": bundle_detail["plan_type_code"],
        "plan_type": bundle_detail["plan_type"],
        "activity_policy_code": bundle_detail["activity_policy_code"],
        "activity_policy": bundle_detail["activity_policy"],
        "available_netwok_code": bundle_detail["available_netwok_code"],
        "available_netwok": bundle_detail["available_netwok"],
        "top_up_plan_code": bundle_detail["top_up_plan_code"],
        "top_up_plan": bundle_detail["top_up_plan"],
        "ekyc_code": bundle_detail["ekyc_code"],
        "ekyc": bundle_detail["ekyc"],
        "country_code_list": list_countries_code,
        "support_topup": vendor.support_topup,
    }

    if len(list_countries_code) == 2:
        new_bundle["bundle_category"] = "region"
        new_bundle["is_region"] = True
    elif len(list_countries_code) > 2:
        new_bundle["bundle_category"] = "global"
        new_bundle["is_region"] = True

    return new_bundle


def persist_bundle(new_bundle: dict, new_bundle_list: List[str]):
    """Save the bundle and update logs."""
    logger.info("Attempting to add LotusFlare bundle %s", new_bundle["bundle_vendor_code"])
    add_bundle(new_bundle)
    logger.info("New LotusFlare bundle added with bundle_code:%s", new_bundle["bundle_code"])
    new_bundle_list.append(new_bundle["bundle_code"])
    add_to_update_bundle_version([f"Save LotusFlare Bundles (New One): {new_bundle_list}"])


def save_lotusflare_bundle_in_db(bundles: List[Dict]):
    logger.info("Started save LotusFlare bundles")

    profile_name = "L"
    new_bundle_list = []

    vendor = db_helper.get_vendor(LOTUS_FLARE_VENDOR)
    if not vendor:
        logger.warning("No vendor found with vendor name %s ... hence skipping save_bundles", LOTUS_FLARE_VENDOR)
        return

    rate_revenue = vendor.rate_revenue or 25.0

    bundle_details = db_helper.get_bundle_details(
        ["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"])
    bundle_detail = {}
    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]

    for bundle in bundles:
        list_countries_code = bundle["coverage"]
        bundle_exist = check_bundle(bundle["id"], LOTUS_FLARE_VENDOR)
        if bundle_exist:
            missing_country_code = bundle_exist.missing_country_code
            list_countries_code = [c for c in list_countries_code if c not in missing_country_code]

            if not bundle_exist.deleted:
                logger.info("LotusFlare updating existing bundle with bundle id %s", bundle["id"])
                bundle_exist.update(
                    country_code_list=list_countries_code,
                    supplier_vendor=str(vendor.vendor_code),
                )
                continue

        logger.info("LotusFlare saving new bundle with bundle id %s", bundle["id"])
        new_bundle = build_bundle_document(bundle, vendor, bundle_detail, profile_name, list_countries_code, rate_revenue)
        persist_bundle(new_bundle, new_bundle_list)


def lotusflare_allocate_profiles(bundle_code=None, daily_used=0) -> Tuple[int, str]:
    profile_added = 0
    try:
        logger.info("Starting LotusFlare profile allocation for bundle code: %s", bundle_code)

        lotusflare_vendor = LotusFlare()
        vendor_info = get_vendor_info(LOTUS_FLARE_VENDOR)
        if not vendor_info.is_active:
            logger.warning("Vendor %s is not active", LOTUS_FLARE_VENDOR)
            return 0, "Vendor is not active. Cannot allocate profiles."

        bundle = consumer_models.Bundles.objects(
            vendor_name=LOTUS_FLARE_VENDOR, bundle_code=bundle_code, allocate_profiles=True
        ).first()

        if not bundle:
            logger.error("No active bundle found for bundle code: %s", bundle_code)
            return 0, f"No active bundle found for bundle code: {bundle_code}."

        profile_names = db_helper.remove_space_profiles(bundle.profile_names)
        available_inventory_profiles = Profiles.objects(
            vendor_name=LOTUS_FLARE_VENDOR,
            availability="Free",
            status=True,
            plan_uid__ne="",
            bundle_code=bundle.bundle_code,
            profile_names=profile_names,
        ).count()

        profiles_to_be_added = daily_used - available_inventory_profiles
        if profiles_to_be_added <= 0:
            logger.info("LotusFlare sufficient inventory available for bundle code: %s. No additional profiles needed.",
                        bundle_code)
            return 0, f"LotusFlare sufficient inventory available for bundle code: {bundle_code}. No additional profiles needed."

        logger.info("Allocating %d profiles for bundle code: %s", profiles_to_be_added, bundle_code)

        for _ in range(profiles_to_be_added):
            free_profile = Profiles.objects(
                vendor_name=LOTUS_FLARE_VENDOR,
                availability="Free",
                profile_names=profile_names,
                bundle_code__in=["", None],
                plan_uid="",
            ).first()

            if not free_profile:
                logger.warning("No free profiles available for allocation under bundle code: %s", bundle_code)
                break

            resp = lotusflare_vendor.create_order(
                operation_type="NEW",
                product_id=bundle.bundle_vendor_code,  # vendor’s product id, not Mongo _id
                activation_date=datetime.datetime.now(datetime.timezone.utc).isoformat(),
                iccid=free_profile.iccid,
                auto_allocate_esim=True,
            )
            if not resp:
                logger.error("LotusFlare profile allocation failed for ICCID: %s, bundle code: %s", free_profile.iccid,
                             bundle.bundle_code)
                continue

            plan_uid = resp["esim"]["ki"]
            free_profile.update(
                **{
                    "bundle_code": bundle.bundle_code,
                    "allocate_date": datetime.datetime.now(),
                    "status": True,
                    "plan_uid": plan_uid,
                    "expiry_date": datetime.datetime.now() + relativedelta(days=365),
                }
            )
            profile_added += 1
            bundle.update(inc__allocated_unit=1, is_active=True)
            logger.info("Successfully allocated ICCID: %s to bundle code: %s", free_profile.iccid, bundle.bundle_code)

        if profile_added > 0:
            bundle.update(set__daily_used=0, set__previous_daily_used=bundle.daily_used)
            logger.info("LotusFlare successfully allocated %d profiles for bundle code: %s", profile_added, bundle_code)
            return profile_added, f"Successfully allocated {profile_added} profiles for bundle code: {bundle_code}."

        logger.warning("LotusFlare no profiles were allocated for bundle code: %s", bundle_code)
        return 0, "No profiles allocated due to insufficient free profiles."

    except Exception as e:
        logger.exception("LotusFlare inventory profile allocation failed due to exception: %s", str(e))
        return profile_added, f"Error during profile allocation: {str(e)}"


def lotusflare_bulk_allocate_profiles():
    """Function to allocate inventory profiles for all active LotusFlare bundles."""

    try:
        logger.info("Starting bulk allocation for LotusFlare profiles...")

        bundles = db_helper.get_bundles_by_vendor(
            LOTUS_FLARE_VENDOR, is_active=True, deleted=False, bundle_code=None, allocate_profiles=True
        )

        for bundle in bundles:
            daily_used = bundle.daily_used
            if daily_used == 0:
                profile_count = Profiles.objects(
                    vendor_name=LOTUS_FLARE_VENDOR, bundle_code=bundle.bundle_code
                ).count()

                if profile_count == 0:
                    daily_used = instance_config.nb_profiles
                    logger.info("No existing profiles found. Using default daily allocation: %d", daily_used)

            if daily_used == 0:
                logger.info("Skipping bundle %s as daily_used is 0", bundle.bundle_code)
                continue

            profile_added, failed_reason = lotusflare_allocate_profiles(bundle.bundle_code, daily_used)
            db_helper.run_runnable_scripts(
                script="AllocateProfiles",
                vendor_name=LOTUS_FLARE_VENDOR,
                bundle_code=bundle.bundle_code,
                daily_used=daily_used,
                profile_added=profile_added,
                state="Finished",
                failure_reason=failed_reason,
            )
    except Exception as e:
        logger.exception("Bundle %s allocation crashed : %s", str(e))

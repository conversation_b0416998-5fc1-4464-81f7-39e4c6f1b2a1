import sys
import requests
from app_helpers.script_helper import get_token_flexiroam, generate_temp_otp
from app_helpers.db_helper import add_bundle, check_bundle, add_profiles, check_iccid, get_all_used_iccid, \
    activate_bundle, get_vendors
from app_helpers import db_helper
from datetime import datetime, timedelta
import mongoengine
from flask import make_response
from instance import consumer_config as instance_config


def return_health_check():
    vendors = get_vendors()
    if vendors is None:
        return make_response("Some error in db", 503)
    else:
        test_login = True
        for vendor in vendors:
            test_specific = test_login_vendor(vendor.vendor_name)
            if not test_specific:
                test_login = False

        if test_login:
            return make_response("Everything working", 200)
    return make_response("Some error in vendors info", 503)


def get_esim_go_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.utcnow()  # current date and time

    hashed_otp = generate_temp_otp(2)
    if len(list_countries) > 1:
        title = bundle["description"]
        print("title ", title)
        title = title.replace(",", "_")
        for to_replace in ['gb', 'days', 'Unthrottled']:
            title = title.replace(to_replace, "")
            print("title ", title)

        bundle_name = title + " " + str(bundle["dataAmount"]) + " GB"
        print("bundle_name ", bundle_name)
        code_bundle = ''.join(title.split()).lower()
        bundle_code = code_bundle + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)

    else:

        bundle_name = list_countries[0] + " " + str(bundle["dataAmount"]) + " GB"
        print("bundle_name ", bundle_name)
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
        print("bundle_code ", bundle_code)
    return bundle_name, bundle_code


def get_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.utcnow()  # current date and time
    amount = ('%f' % float(bundle["plan"][0]["data_amount"])).rstrip('0').rstrip('.')
    hashed_otp = generate_temp_otp(2)
    if len(list_countries) > 1:
        if bundle["plan"][0]['category_id'] == "2":
            bundle_name = "Global_bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global_bundle" + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
        else:

            title = bundle["plan"][0]["title"]

            for to_replace in ['1Month', 'MB', 'GX', 'JX', 'B - ', 'I - ']:
                title = title.replace(to_replace, "")

            bundle_name = title + " " + str(amount) + bundle["plan"][0][
                "data_unit"]
            code_bundle = ''.join(title.split()).lower()
            bundle_code = code_bundle + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)

    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
    return bundle_name, bundle_code


def get_country_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.now()  # current date and time
    amount = ('%f' % float(bundle["plan"][0]["data_amount"])).rstrip('0').rstrip('.')
    if len(list_countries) > 1:
        if bundle["plan"][0]['category_id'] == "2":
            bundle_name = "Global bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global bundle" + "_" + now.strftime("%m%d%Y%H%M%S")
        else:

            bundle_name = bundle["plan"][0]["policy"][0]["policy_name"] + " " + str(amount) + bundle["plan"][0][
                "data_unit"]
            bundle_code = bundle["plan"][0]["policy"][0]["policy_name"] + "_" + now.strftime("%m%d%Y%H%M%S")
    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S")
    return bundle_name, bundle_code


def test_login_vendor(vendor_name):
    if vendor_name == "Flexiroam":

        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)
        if access_token:
            return True
    else:
        return True

    return False


def get_page_number(bundles_count):
    return round(bundles_count / 100)


def get_esim_go_bundles(esimgo_token, i):
    payload = {}
    headers = {
        'X-API-Key': esimgo_token
    }
    url = '{}/v2.2/catalogue?orderBy=price&page={}&perPage=100'.format(instance_config.esimgo_url, i)

    response = requests.request("GET", url, headers=headers, data=payload)
    bundles = response.json()
    if bundles:
        for bundle in bundles['bundles']:

            list_countries = []
            list_countries_code = []
            print("bundle['name'] ", bundle['name'])
            bundle_exist = check_bundle(bundle['name'], instance_config.esimgo_vendor)
            print("bundle_exist ", bundle_exist)
            if not bundle_exist:
                vendor = db_helper.get_vendor(instance_config.esimgo_vendor)
                rate_revenue = 25.0
                if vendor:
                    rate_revenue = vendor.rate_revenue

                retail_price = float(bundle["price"]) + (
                        float(bundle["price"]) * float(rate_revenue) / 100)

                new_bundle = {
                    "vendor_name": "eSIMGo",
                    "bundle_name": bundle['name'],
                    "bundle_marketing_name": bundle['description'],
                    "bundle_code": bundle['description'],
                    "bundle_vendor_name": bundle['description'],
                    "bundle_vendor_code": bundle['name'],
                    "unit_price": bundle["price"],
                    "data_amount": bundle["dataAmount"],
                    "fullspeed_data_amount": bundle["dataAmount"],
                    "allocated_unit": 1,
                    "data_unit": "GB",
                    "validity_amount": str(bundle["duration"]),
                    "rate_revenue": float(rate_revenue),
                    "retail_price": round(float(retail_price), 2),
                }

                bundle_duration = int(bundle["duration"])
                list_countries = []
                list_countries_code = []

                if len(bundle['roamingEnabled']) > 1:
                    print("bundle['roamingEnabled'] ", bundle['roamingEnabled'])
                    for country in bundle['roamingEnabled']:
                        print("country['iso'] ", country['iso'])
                        country_code = db_helper.get_iso3(country['iso'])
                        print("country_code ", country_code)
                        if country_code and country_code != 'ISR':
                            list_countries.append(country['name'])

                            list_countries_code.append(country_code)

                    new_bundle['bundle_duration'] = bundle_duration
                    new_bundle['is_region'] = False
                    new_bundle["bundle_category"] = "global"
                else:
                    new_bundle['is_region'] = True
                    new_bundle["category_name"] = "country_" + list_countries_code[0]
                    new_bundle["bundle_category"] = "country"
                bundle_name, bundle_code = get_esim_go_bundle_name(list_countries, list_countries_code, bundle)
                print("bundle_name, bundle_code  ", bundle_name, bundle_code)
                new_bundle['bundle_code'] = bundle_code
                new_bundle['bundle_name'] = bundle_name

                new_bundle['is_active'] = True
                new_bundle['country_list'] = list_countries
                new_bundle['country_code_list'] = list_countries_code
                result = add_bundle(new_bundle)
                print("result ", result)


def esimgo_save_bundles():
    try:
        vendor = db_helper.get_vendor("eSIMGo")
        if vendor:
            pages = get_page_number(vendor.bundles_count)

            for i in range(1, pages + 1):
                print("i ", i)
                get_esim_go_bundles(instance_config.esimgo_token, i)

            print("token ", instance_config.esimgo_token)


    except Exception as e:
        print("exception esimgo_save_bundles ", str(e))


def save_flexiroam_bundles(token, i):
    headers = {
        'token': token
    }
    payload = {'group_by_offering': 'yes', 'limit': "100", "page": i + 1}
    print("payload ", payload)

    r = requests.post('{}/plan/inventory/view/v1'.format(instance_config.flexiroam_url), headers=headers,
                      data=payload)
    bundles = r.json()
    if bundles:
        print("bundles ", bundles)
        list_countries_code = []

        for bundle in bundles['data']:
            bundle_exist = check_bundle(bundle['plan'][0]['plan_code'], 'Flexiroam')

            if bundle_exist:
                bundle_exist.allocated_unit = bundle['allocated_unit']
                bundle_exist.consumed_unit = bundle['consumed_unit']
                if bundle_exist.allocated_unit > bundle_exist.consumed_unit and not bundle_exist.deleted:
                    bundle_exist.is_active = True
                bundle_exist.save()

            else:

                vendor = db_helper.get_vendor('Flexiroam')
                rate_revenue = 25.0
                if vendor:
                    rate_revenue = vendor.rate_revenue
                list_countries = []
                retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)

                if int(bundle['allocated_unit']) > 0:

                    new_bundle = {
                        "vendor_name": "Flexiroam",
                        "bundle_name": bundle['title'],
                        "bundle_marketing_name": bundle['title'],
                        "bundle_code": bundle['plan'][0]['plan_code'],
                        "bundle_vendor_name": bundle['title'],
                        "bundle_vendor_code": bundle['plan'][0]['plan_code'],

                        "unit_price": bundle["unit_price"],
                        "data_amount": bundle["plan"][0]["data_amount"],
                        "fullspeed_data_amount": bundle["plan"][0]["fullspeed_data_amount"],
                        "data_unit": bundle["plan"][0]["data_unit"],
                        "validity_amount": bundle["plan"][0]["validity_amount"],
                        "rate_revenue": float(rate_revenue),
                        "retail_price": round(float(retail_price), 2),
                        "profile_names": bundle['profile_names'],
                        "allocated_unit": bundle['allocated_unit'],

                    }
                    if 'vendor_code' in bundle:
                        new_bundle["supplier_vendor"]= bundle['vendor_code']

                    bundle_duration = int(bundle["plan"][0]['validity_amount'])

                    if bundle["plan"][0]["validity_unit"] == "MONTH":
                        new_bundle["validity_amount"] = str(int(bundle["plan"][0]["validity_amount"])* 30)
                        bundle_duration = int(bundle["plan"][0]["validity_amount"])* 30

                    list_countries_code = []
                    list_countries = []
                    for policy_attributes in bundle["plan"][0]['policy']:

                        policy_attribute_check = policy_attributes['policy_attributes'][0]
                        if 'Country' in policy_attribute_check:

                            for policy in policy_attribute_check['Country']:
                                list_countries.append(policy['name'])
                                list_countries_code.append(policy['value'])

                    bundle_name, bundle_code = get_bundle_name(list_countries, list_countries_code, bundle)

                    new_bundle['bundle_name'] = bundle_name

                    new_bundle['bundle_code'] = bundle_code
                    new_bundle['bundle_duration'] = bundle_duration
                    new_bundle['is_region'] = False

                    if int(bundle["plan"][0]["category_id"]) == 2:
                        new_bundle['is_region'] = True
                        new_bundle["bundle_category"] = "global"
                        new_bundle["category_name"] = "global"
                    elif int(bundle["plan"][0]["category_id"]) == 4:
                        new_bundle["category_name"] = "region"
                        new_bundle['is_region'] = True
                        new_bundle["bundle_category"] = "region"

                    elif int(bundle["plan"][0]["category_id"]) == 3:

                        attributes = bundle["plan"][0]['policy'][0]['policy_attributes']

                        if len(list_countries_code) > 2:

                            new_bundle["category_name"] = "region"
                            new_bundle['is_region'] = True
                            new_bundle["bundle_category"] = "region"
                        else:
                            new_bundle['is_region'] = True
                            new_bundle["category_name"] = "country_" + list_countries_code[0]
                            new_bundle["bundle_category"] = "country"

                    new_bundle['is_active'] = True
                    new_bundle['country_list'] = list_countries
                    new_bundle['country_code_list'] = list_countries_code

                    result = add_bundle(new_bundle)


def flexi_save_bundles():
    try:

        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)

        if access_token:

            vendor = db_helper.get_vendor("Flexiroam")
            if vendor:
                pages = get_page_number(vendor.bundles_count, )
                for i in range(0, pages):
                    save_flexiroam_bundles(access_token['data']['token'], i)


    except Exception as e:
        print("exception flexi_save_bundles ", str(e))


def update_profile(response):
    for profile in response:
        found_iccid = check_iccid(profile["iccid"])

        if found_iccid:
            if profile['installation_status'] is not None:
                found_iccid.installation_status = profile['installation_status']['status']
                found_iccid.save()
                print("profile['installation_status'] ", profile['installation_status']['status'])

    return True


def save_profile(response):
    for profile in response:
        found_iccid = check_iccid(profile["iccid"])

        if not found_iccid:
            qr_code_value = profile["qr_code_value"]
            qr_code_value = qr_code_value.replace("LPA:1", "")

            qr_value_splited = qr_code_value.split("$")

            new_profile = {
                "vendor_name": "Flexiroam",
                "sku": profile["sku"],
                "iccid": profile["iccid"],
                "qr_code_value": profile["qr_code_value"],
                "profile_names": profile["profile_names"],
                "last_connection": profile["last_connection"],
                "installation_status": profile["installation_status"],
                "smdp_address": qr_value_splited[1],
                "matching_id": qr_value_splited[2]
            }

            res = add_profiles(new_profile)
            activate_bundle(profile["profile_names"])

    return True


def update_prices_vendor(vendor_name):
    vendor = db_helper.get_vendor(vendor_name)
    affected_rows = 0
    success_rows = 0
    if vendor:
        rate_revenue = vendor.rate_revenue
        bundles = db_helper.get_bundles_by_vendor(vendor_name)
        for bundle in bundles:

            retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)
            bundle["retail_price"] = retail_price
            bundle["rate_revenue"] = rate_revenue
            if bundle.save():
                success_rows = success_rows + 1
            else:
                affected_rows = affected_rows + 1
    return affected_rows, success_rows


def flexi_update_profiles():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)

        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            payload = {'availability': 1, "sim_type": "eSIM", "limit": "10000"}
            r = requests.post('{}/product/inventory/view/v1'.format(instance_config.flexiroam_url), headers=headers,
                              data=payload)

            if r.status_code == 200:
                response = r.json()
                if response:
                    update_profile(response['data'])



    except Exception as e:
        print("exception flexi_update_profiles ", str(e))


def flexi_save_profiles():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username,
                                                      instance_config.flexiroam_password)

        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            payload = {'availability': 0, "sim_type": "eSIM", "limit": "10000"}
            r = requests.post('{}/product/inventory/view/v1'.format(instance_config.flexiroam_url), headers=headers,
                              data=payload)

            if r.status_code == 200:
                response = r.json()
                if response:
                    save_profile(response['data'])



    except Exception as e:
        print("exception flexi_save_profiles ", str(e))


def get_date_before(days):
    today = datetime.today()
    return today - timedelta(days=days)


def rest_profiles():
    date_before = get_date_before(1)
    unpaid_logs = db_helper.get_unpaid_bundle_log(date_before)

    for log in unpaid_logs:
        bundle_code = log.bundle_code
        if log.topup_code != "":
            bundle_code = log.topup_code

        unused_iccid = db_helper.get_unused_iccid(str(log))
        if unused_iccid:
            used_iccid = db_helper.check_used_iccid(unused_iccid.iccid)
            if used_iccid == 0:
                bundle_info = db_helper.get_bundle(bundle_code)
                if bundle_info:
                    bundle_info.consumed_unit = bundle_info.consumed_unit - 1
                    bundle_info.save()
                db_helper.reset_free_profile(unused_iccid.iccid)
                unused_iccid.delete()
                db_helper.delete_bundle_log_per_otp(log)


def flexi_expire_profiles():
    try:
        iccid_list = get_all_used_iccid()

        if iccid_list:
            for user_iccid in iccid_list:
                user_bundle = db_helper.get_paid_user_bundle(user_iccid.bundle_code, user_iccid.email)

                if user_bundle and user_bundle.validy_date < datetime.today():
                    user_iccid.status = "expired"
                    user_iccid.save()
                    db_helper.expire_profile(user_iccid.iccid)
    except Exception as e:
        print("exception flexi_expire_profiles", str(e))


def expire_scripts():
    date_now = datetime.now()
    db_helper.reset_user_iccid(date_now)
    db_helper.reset_email_verification(date_now)
    db_helper.reset_password_request(date_now)
    db_helper.reset_contact_us(date_now)

import asyncio
import copy
from typing import List

import mongoengine
import requests
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor as ThreadPool
from app_models.reseller_models import Order_history
from b2c_helpers.graph import Graph
from dateutil.relativedelta import relativedelta
from uuid import uuid4
from app_models.consumer_models import Profiles, Vendors, Bundles
from pymongo.collection import Collection

from services.notify_reseller_service import notify_reseller
from services.vendor_common_services import enough_vendor_balance
from services.vodafone_services import get_vodafone_bundles
from .encrypt_helper import Crypt, create_esimgo_signature
from app_helpers.script_helper import (
    get_token_flexiroam,
    get_token_dynamic_vendor,
    generate_temp_otp,
    send_error_email,
    get_esimgo_balance,
)
from app_helpers.db_helper import (
    add_bundle,
    check_bundle,
    add_profiles,
    check_iccid,
    get_all_used_iccid,
    activate_bundle,
    save_history_logs,
    enough_organization_balance,
    add_to_update_bundle_version,
    reset_free_profile, get_failed_notifications, get_first_time_notifications,
)
from urllib3.exceptions import NewConnectionError
import simplejson as json
import os
from app_helpers import db_helper
from datetime import timedelta
from flask import make_response, render_template
from instance import consumer_config as instance_config
from app_models import main_models
import re
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app_models.main_models import NotificationLogs, EmailSettings, Settings
from email.header import Header
import datetime
from pymongo import UpdateOne
from mongoengine.queryset.visitor import Q
from app_helpers.main_helper import encrypt_data, send_custom_dev_email
import time
from b2c_helpers.vendors import (
    montymobile_save_token,
    get_token_montymobile,
    MontyMobile,
    Indosat,
    Flexiroam,
    ESIMGo,
    Orange,
    FlexiroamAPI,
    MontyReseller,
)
from app_models import mobiles, consumer_models
from b2c_helpers.support_helper import send_custom_monitor_email
from b2c_helpers.reseller import ResellerHelper
import threading
import logging

logger = logging.getLogger(__name__)

VODAFONE_VENDOR = "Vodafone"
app_json = "application/json"
FLEXIROAM_VENDOR = "Flexiroam"
FLEXIROAM_VENDOR_V2 = "FlexiroamV2"
MONTY_MOBILE_VENDOR = "Monty Mobile"
MONTY_RESELLER_VENDOR = "Monty Reseller"
allocation_exception = "couldn`t allocate bundle"
message_update_bundle_version = "Bundle re-activated (number of free profiles >= limit)"
ESIM_GO_VENDOR = "eSIMGo"
INDOSAT_VENDOR = "Indosat"
ORANGE_VENDOR = "Orange"
max_indosat_profile_pages: int = 500


def return_health_check():
    output = {"test": "yes"}
    response = json.dumps(output)

    response_http = make_response(response)
    response_http.headers.add("Access-Control-Allow-Origin", "*")
    response_http.headers["Content-Type"] = "application/json"

    return response_http


def get_esim_go_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.datetime.utcnow()  # current date and time
    hashed_otp = generate_temp_otp(2)
    if len(list_countries) > 1:
        title = bundle["description"]
        title = title.replace(",", "_")
        for to_replace in ["gb", "days", "Unthrottled"]:
            title = title.replace(to_replace, "")
        code_bundle = "".join(title.split()).lower()
        bundle_code = code_bundle + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)

    else:
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
    return bundle_code


def get_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.datetime.utcnow()  # current date and time
    amount = ("%f" % float(bundle["plan"][0]["data_amount"])).rstrip("0").rstrip(".")
    hashed_otp = generate_temp_otp(2)
    if len(list_countries) > 1:
        if bundle["plan"][0]["category_id"] == "2":
            bundle_name = "Global_bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global_bundle" + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
        else:

            title = bundle["plan"][0]["title"]

            for to_replace in ["1Month", "MB", "GX", "JX", "B - ", "I - "]:
                title = title.replace(to_replace, "")

            bundle_name = title + " " + str(amount) + bundle["plan"][0]["data_unit"]
            code_bundle = "".join(title.split()).lower()
            bundle_code = code_bundle + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)

    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
    return bundle_name, bundle_code


def get_bundle_name_montymobile(list_countries, list_countries_code, bundle):
    now = datetime.datetime.utcnow()  # current date and time
    amount = ("%f" % float(bundle["data_amount"])).rstrip("0").rstrip(".")
    hashed_otp = generate_temp_otp(2)
    if len(list_countries) > 1:
        if bundle["category"] == "GLOBAL":
            bundle_name = "Global bundle " + " " + str(amount) + bundle["data_unit"]
            bundle_code = "Global_bundle" + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
        else:

            title = bundle["bundle_name"]

            for to_replace in ["1Month", "MB", "GX", "JX", "B - ", "I - "]:
                title = title.replace(to_replace, "")

            bundle_name = title + " " + str(amount) + bundle["data_unit"]
            code_bundle = "".join(title.split()).lower()
            bundle_code = code_bundle + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)

    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S") + str(hashed_otp)
    return bundle_name, bundle_code


def get_country_bundle_name(list_countries, list_countries_code, bundle):
    now = datetime.datetime.now()  # current date and time
    amount = ("%f" % float(bundle["plan"][0]["data_amount"])).rstrip("0").rstrip(".")
    if len(list_countries) > 1:
        if bundle["plan"][0]["category_id"] == "2":
            bundle_name = "Global bundle " + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = "Global bundle" + "_" + now.strftime("%m%d%Y%H%M%S")
        else:

            bundle_name = bundle["plan"][0]["policy"][0]["policy_name"] + " " + str(amount) + bundle["plan"][0]["data_unit"]
            bundle_code = bundle["plan"][0]["policy"][0]["policy_name"] + "_" + now.strftime("%m%d%Y%H%M%S")
    else:

        bundle_name = list_countries[0] + " " + str(amount) + bundle["plan"][0]["data_unit"]
        bundle_code = list_countries_code[0] + "_" + now.strftime("%m%d%Y%H%M%S")
    return bundle_name, bundle_code


def test_login_vendor(vendor_name):
    if vendor_name == FLEXIROAM_VENDOR:

        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)
        if access_token:
            return True
    elif vendor_name == ESIM_GO_VENDOR:
        return True
    return False


def get_page_number(bundles_count):
    return round(bundles_count / 100)


def update_country(
    country,
    list_countries,
    list_countries_code,
    country_name_dic,
    country_code_dic,
    missing_values=[],
):
    country_code = country["iso"]
    if len(country["iso"]) == 2:
        country_code = get_iso3_code_by_name(country_code_dic, country["iso"])
    if country_code in country_code_dic and country_code not in missing_values:
        list_countries.append(country_name_dic[country["iso"]])
        list_countries_code.append(country_code_dic[country["iso"]])
    else:
        country_code = db_helper.get_iso3(country["iso"])
        if country_code and country_code.iso3_code != "ISR":
            if country_code.iso3_code not in missing_values:
                list_countries.append(country_code["country_name"].strip())
                list_countries_code.append(country_code.iso3_code.strip())
            country_name_dic[country["iso"]] = country_code["country_name"].strip()
            country_code_dic[country["iso"]] = country_code.iso3_code.strip()
    return list_countries, list_countries_code, country_name_dic, country_code_dic


def get_iso3_code_by_name(country_name_dic, country):
    for country_code, country_name in country_name_dic.items():
        if country == country_name:
            return country_code
    return None  # Return None if the value is not found


def get_esim_go_bundles(esimgo_token, i, country_code_dic, country_name_dic):
    payload = {}
    headers = {"X-API-Key": esimgo_token}
    url = "{}/v2.3/catalogue?page={}&perPage=100&api_key={}".format(instance_config.esimgo_url, i, esimgo_token)
    bundle_details = db_helper.get_bundle_details(["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"])
    vendor = db_helper.get_vendor(instance_config.esimgo_vendor)
    rate_revenue = 25.0
    bundle_detail = {}
    bundle_exist_list = []
    new_bundle_list = []
    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]
    lst_db = instance_config.lst_db_
    response = requests.request("GET", url, headers=headers, data=payload)
    bundles = response.json()
    if response.status_code == 200:

        if bundles:
            for bundle in bundles["bundles"]:

                for db in lst_db:
                    bundle_exist = check_bundle(bundle["name"], instance_config.esimgo_vendor)
                    print("bundle['name'] ", bundle["name"])

                    if vendor:
                        rate_revenue = vendor.rate_revenue
                    group_id_refill = bundle["groups"][0]
                    if bundle["dataAmount"] < 0.0:
                        group_id_refill = bundle["groups"][0] + "_unlimited"

                    refill_group = str(group_id_refill) + str(vendor.vendor_code)
                    refill_group = refill_group.replace(" ", "")

                    if not bundle_exist:
                        print("new bundle")
                        retail_price = float(bundle["price"]) + (float(bundle["price"]) * float(rate_revenue) / 100)

                        original_bundle_code = bundle["description"]
                        updated_bundle_code = original_bundle_code.replace("+", "")
                        bundle["description"] = updated_bundle_code

                        new_bundle = {
                            "vendor_name": ESIM_GO_VENDOR,
                            "bundle_name": bundle["name"],
                            "bundle_marketing_name": bundle["description"],
                            "bundle_code": bundle["description"],
                            "bundle_vendor_name": bundle["description"],
                            "bundle_vendor_code": bundle["name"],
                            "unit_price": bundle["price"],
                            "data_amount": int(bundle["dataAmount"]) / 1000,
                            "fullspeed_data_amount": int(bundle["dataAmount"]) / 1000,
                            "allocated_unit": 1,
                            "data_unit": "GB",
                            "validity_amount": str(bundle["duration"]),
                            "rate_revenue": float(rate_revenue),
                            "retail_price": round(float(retail_price), 2),
                            "plan_type_code": bundle_detail["plan_type_code"],
                            "plan_type": bundle_detail["plan_type"],
                            "activity_policy": bundle_detail["activity_policy"],
                            "activity_policy_code": bundle_detail["activity_policy_code"],
                            "available_netwok_code": bundle_detail["available_netwok_code"],
                            "available_netwok": bundle_detail["available_netwok"],
                            "top_up_plan_code": bundle_detail["top_up_plan_code"],
                            "top_up_plan": bundle_detail["top_up_plan"],
                            "profile_names": "",
                            "group_id": bundle["groups"][0],
                            "deleted": True,
                            "refill_group": refill_group,
                            "supplier_vendor": str(vendor.vendor_code),
                        }
                        bundle_duration = int(bundle["duration"])

                        if new_bundle["data_amount"] < 0.0:
                            unlimited = True
                        else:
                            unlimited = False

                        new_bundle["unlimited"] = unlimited

                        list_countries = []
                        list_countries_code = []
                        for country in bundle["countries"]:
                            (
                                list_countries,
                                list_countries_code,
                                country_name_dic,
                                country_code_dic,
                            ) = update_country(
                                country,
                                list_countries,
                                list_countries_code,
                                country_name_dic,
                                country_code_dic,
                            )
                        if len(bundle["roamingEnabled"]) > 1:
                            for country in bundle["roamingEnabled"]:
                                (
                                    list_countries,
                                    list_countries_code,
                                    country_name_dic,
                                    country_code_dic,
                                ) = update_country(
                                    country,
                                    list_countries,
                                    list_countries_code,
                                    country_name_dic,
                                    country_code_dic,
                                )

                            new_bundle["bundle_duration"] = bundle_duration
                            new_bundle["is_region"] = False
                            new_bundle["bundle_category"] = "global"
                            new_bundle["bundle_name"] = "Global {}GB {}Days".format(int(bundle["dataAmount"] / 1000), bundle_duration)
                            new_bundle["bundle_marketing_name"] = "The World is Yours!"

                            bundle_code = get_esim_go_bundle_name(list_countries, list_countries_code, bundle)
                            new_bundle["bundle_code"] = bundle_code
                            new_bundle["is_active"] = False
                            new_bundle["country_list"] = list_countries
                            new_bundle["country_code_list"] = list_countries_code

                            new_bundle_list.append(new_bundle["bundle_code"])
                            result = add_bundle(new_bundle, db.get("ALIAS"))
                        else:
                            if len(list_countries) > 0:
                                new_bundle["bundle_duration"] = bundle_duration
                                new_bundle["is_region"] = False
                                new_bundle["bundle_category"] = "country"
                                country_name = list_countries[0].replace(" ", "")
                                new_bundle["bundle_name"] = "{}{}GB {}Days".format(
                                    country_name,
                                    int(bundle["dataAmount"] / 1000),
                                    bundle_duration,
                                )
                                new_bundle["bundle_marketing_name"] = "The World is Yours!"

                                bundle_code = get_esim_go_bundle_name(list_countries, list_countries_code, bundle)

                                new_bundle["bundle_code"] = bundle_code
                                new_bundle["is_active"] = False
                                new_bundle["country_list"] = list_countries
                                new_bundle["country_code_list"] = list_countries_code
                                new_bundle_list.append(new_bundle["bundle_code"])

                                result = add_bundle(new_bundle, db.get("ALIAS"))
                    else:
                        if bundle["name"] == "esim_10GB_30D_AX_U":
                            list_countries_code = []
                            list_countries = []
                            for country in bundle["countries"]:
                                (
                                    list_countries,
                                    list_countries_code,
                                    country_name_dic,
                                    country_code_dic,
                                ) = update_country(
                                    country,
                                    list_countries,
                                    list_countries_code,
                                    country_name_dic,
                                    country_code_dic,
                                    bundle_exist.missing_country_code,
                                )
                            if len(bundle["roamingEnabled"]) > 1:
                                for country in bundle["roamingEnabled"]:
                                    (
                                        list_countries,
                                        list_countries_code,
                                        country_name_dic,
                                        country_code_dic,
                                    ) = update_country(
                                        country,
                                        list_countries,
                                        list_countries_code,
                                        country_name_dic,
                                        country_code_dic,
                                        bundle_exist.missing_country_code,
                                    )

                            # retail_price = float(bundle["price"]) + (float(bundle["price"]) * float(rate_revenue) / 100)
                            bundle_exist_list.append(bundle_exist.bundle_code)
                            db_helper.update_bundle(
                                bundle_exist.bundle_code,
                                list_countries,
                                list_countries_code,
                                bundle["price"],
                                db.get("ALIAS"),
                            )

                        if bundle["dataAmount"] < 0.0:
                            group_id = bundle["groups"][0] + "_unlimited"
                        else:
                            group_id = bundle["groups"][0]

                        bundle_exist.update(
                            set__group_id=group_id,
                            set__refill_group=refill_group,
                            set__supplier_vendor=str(vendor.vendor_code),
                        )

                add_to_update_bundle_version([f"Save eSIMGo Bundles (New One): {new_bundle_list}"])

    return country_code_dic, country_name_dic


def esimgo_save_bundles():
    try:
        vendor = db_helper.get_vendor(ESIM_GO_VENDOR)
        country_code_dic = {}
        country_name_dic = {}
        if vendor:
            pages = get_page_number(vendor.bundles_count)
            for i in range(1, pages + 1):
                country_code_dic, country_name_dic = get_esim_go_bundles(
                    instance_config.esim_go_token, i, country_code_dic, country_name_dic
                )

    except Exception as e:
        print("exception esimgo_save_bundles ", str(e))


def indosat_save_bundles():
    vendor = db_helper.get_vendor(INDOSAT_VENDOR)
    if not vendor:
        logging.error("Indosat vendor not found")
        return
    if vendor and not vendor.is_active:
        logging.error("Indosat vendor not active")
        return
    indosat = Indosat()
    last_page = False
    bundles = []
    new_bundles = []
    new_bundle_list = []

    page = 1
    rate_revenue = indosat.rate_revenue if indosat.rate_revenue else 25
    exchange_rate = indosat.currency_exchange_rate if indosat.currency_exchange_rate else 15405.35
    bundle_detail = {}
    bundle_details = db_helper.get_bundle_details(["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"])
    countries = consumer_models.Countries.objects()
    country_objects = list(countries)
    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]
    while not last_page:
        response = indosat.get_bundles(pageSize=50, pageNumber=page)
        last_page = response.get("lastPage", False)
        page += 1
        bundles.extend(response.get("ratePlans", []))

    for bundle in bundles:
        try:
            refill_group = str(indosat.vendor.vendor_code)
            refill_group = refill_group.replace(" ", "")
            bundle_exist = check_bundle(bundle["name"], vendor_name="Indosat")
            if not bundle_exist:
                unit_price = round(float(bundle["subscriptionCharge"] / exchange_rate), 2)
                retail_price = round(float(unit_price * (1 + (rate_revenue / 100))), 2)
                bundle_duration = bundle["lengthOfTerm"]
                new_bundle = {
                    "vendor_name": INDOSAT_VENDOR,
                    "bundle_name": bundle["name"],
                    "bundle_marketing_name": bundle["name"],
                    "bundle_code": bundle["versionId"],
                    "bundle_vendor_name": bundle["name"],
                    "bundle_vendor_code": bundle["name"],
                    "unit_price": unit_price,
                    "allocated_unit": 0,
                    "validity_amount": str(bundle_duration),
                    "bundle_duration": bundle_duration,
                    "rate_revenue": float(rate_revenue),
                    "retail_price": retail_price,
                    "plan_type_code": bundle_detail["plan_type_code"],
                    "plan_type": bundle_detail["plan_type"],
                    "activity_policy": bundle_detail["activity_policy"],
                    "activity_policy_code": bundle_detail["activity_policy_code"],
                    "available_netwok_code": bundle_detail["available_netwok_code"],
                    "available_netwok": bundle_detail["available_netwok"],
                    "top_up_plan_code": bundle_detail["top_up_plan_code"],
                    "top_up_plan": bundle_detail["top_up_plan"],
                    "is_active": False,
                    "deleted": True,
                    "profile_names": "",
                    "refill_group": refill_group,
                    "supplier_vendor": str(indosat.vendor.vendor_code),
                }
                fullspeed_data_amount = data_amount = 0
                if "usageLimit" in bundle["dataUsage"]:
                    fullspeed_data_amount = data_amount = int(bundle["dataUsage"]["usageLimit"])

                data_unit = bundle["dataUsage"]["usageLimitUnit"].strip(" per cycles")
                if data_unit == "MB" and data_amount > 1000:
                    data_unit = "GB"
                    fullspeed_data_amount = data_amount = round(float(data_amount / 1024), 2)

                new_bundle["fullspeed_data_amount"] = fullspeed_data_amount
                new_bundle["data_amount"] = data_amount
                new_bundle["data_unit"] = data_unit

                # TODO Add country list and country code list to the code based on zone sheets.
                try:
                    pattern = r"Prepaid\s+(?P<country_name>.+?)\s+\d"
                    match = re.search(pattern, bundle["name"])
                    if match:
                        country_name = match.group("country_name").lower()
                        country = next(
                            (country for country in country_objects if country.iso3_code.lower() == country_name),
                            None,
                        )
                        list_countries = [country.country_name]
                        list_countries_code = [country.iso3_code]
                    else:
                        list_countries = []
                        list_countries_code = []

                except:
                    list_countries = []
                    list_countries_code = []
                version_id = bundle["versionId"]
                if len(list_countries) == 0:
                    bundle_category = "country"
                    bundle_name = "NEEDS MANUAL INTERVENTION"
                    new_bundle["bundle_marketing_name"] = bundle_name
                    bundle_code = "{}_{}_{}days_{}".format(int(data_amount), data_unit, bundle_duration, version_id)
                elif len(list_countries) == 1:
                    bundle_category = "country"
                    bundle_name = "{} {}{} {}Days".format(list_countries[0], data_amount, data_unit, bundle_duration)
                    bundle_code = "{}_{}{}_{}days_{}".format(
                        list_countries[0],
                        int(data_amount),
                        data_unit,
                        bundle_duration,
                        version_id,
                    )

                elif 1 < len(list_countries) <= 2:
                    bundle_category = "region"
                    bundle_name = "Regional {}{} {}Days".format(data_amount, data_unit, bundle_duration)
                    bundle_code = "{}_{}{}_{}days_{}".format(
                        "regional",
                        int(data_amount),
                        data_unit,
                        bundle_duration,
                        version_id,
                    )
                elif len(list_countries) > 2:
                    bundle_category = "global"
                    bundle_name = "Global {}{} {}Days".format(data_amount, data_unit, bundle_duration)
                    new_bundle["bundle_marketing_name"] = "The World is Yours!"
                    bundle_code = "{}_{}{}_{}days_{}".format(
                        "global",
                        int(data_amount),
                        data_unit,
                        bundle_duration,
                        version_id,
                    )
                else:
                    print("Error while saving bundle", bundle)
                    bundle_category = ""
                    continue

                updated_bundle_code = bundle_code.replace(" ", "_")
                new_bundle["bundle_name"] = bundle_name
                new_bundle["bundle_category"] = bundle_category
                new_bundle["country_list"] = list_countries
                new_bundle["country_code_list"] = list_countries_code
                new_bundle["bundle_code"] = updated_bundle_code

                new_bundle_list.append(new_bundle["bundle_code"])
                add_bundle(new_bundle)
                new_bundles.append(new_bundle)
            else:
                bundle_exist.update(
                    set__refill_group=refill_group,
                    set__supplier_vendor=str(indosat.vendor.vendor_code),
                )
        except Exception as e:
            print(str(e))
            continue


def indosat_save_profiles():
    logger.info("Initiating Indosat Save Profiles")
    indosat = Indosat()
    last_page = False
    profiles = []
    iccids_list = []
    all_terminals = []
    batch_size = 50
    page = 1

    # Getting all Profiles Available
    while page < max_indosat_profile_pages and not last_page:
        response = indosat.get_profiles(
            pageSize=50,
            pageNumber=page,
            status="TEST_READY",
        )
        last_page = response.get("lastPage", False)
        page += 1
        profiles.extend(response.get("devices", []))

    if page >= max_indosat_profile_pages:
        print("Terminated because of timeout.")
    for profile in profiles:
        iccids_list.append(profile.get("iccid"))

    # Checking Diff between our database and indosat

    query = {
        "$and": [
            {"vendor_name": INDOSAT_VENDOR},
            {"iccid": {"$exists": True, "$ne": None}},
        ]
    }
    db_iccids = consumer_models.Profiles._get_collection().distinct("iccid", query)
    iccids_list = list(set(iccids_list) - set(db_iccids))

    bundle_codes_map_pipeline = [
        {"$match": {"vendor_name": "Indosat"}},
        {"$project": {"bundle_code": 1, "bundle_vendor_code": 1}},
    ]

    aggregation_result = consumer_models.Bundles._get_collection().aggregate(bundle_codes_map_pipeline)
    bundles_newly_allocated_amount = {}
    bundle_codes_map = {}
    for item in aggregation_result:
        bundle_codes_map[item["bundle_vendor_code"]] = item["bundle_code"]
        bundles_newly_allocated_amount[item["bundle_vendor_code"]] = 0

    # Iterate over the iccids_list in batches of batch_size
    for i in range(0, len(iccids_list), batch_size):
        # Get the current batch
        batch = iccids_list[i : i + batch_size]

        # Call the get_terminal_details function with the current batch
        terminals = indosat.get_terminal_details(batch)

        # Store the results
        all_terminals.extend(terminals)

    for profile in all_terminals:

        qr_code_value = profile.get("custom2", None)
        if qr_code_value:
            qr_code_value_tuple = qr_code_value.replace("LPA:1", "")

            qr_value_splited = qr_code_value_tuple.split("$")
        else:
            continue

        ratePlan = profile["ratePlan"]
        bundle_code = bundle_codes_map.get(ratePlan)
        if not bundle_code:
            continue
        new_profile = {
            "vendor_name": INDOSAT_VENDOR,
            "sku": profile["iccid"],
            "iccid": profile["iccid"],
            "qr_code_value": qr_code_value,
            "profile_names": None,
            "last_connection": None,
            "installation_status": None,
            "smdp_address": qr_value_splited[1],
            "matching_id": qr_value_splited[2],
            "status": True,
            "bundle_code": bundle_code,
            "expiry_date": datetime.datetime.now() + relativedelta(days=363),
            "plan_uid": str(uuid4()),
        }
        bundles_newly_allocated_amount[ratePlan] = bundles_newly_allocated_amount[ratePlan] + 1
        res = add_profiles(new_profile)
    bundles_collection: Collection = consumer_models.Bundles._get_collection()
    bundles_collection.update_many(
        filter={
            "vendor_name": "Indosat"
        },
        update={
            "$set": {"daily_used": 0}
        }
    )
    for k, v in bundles_newly_allocated_amount.items():
        bundles_collection.update_one(
            {"bundle_vendor_code": k},
            {
                "$inc": {"allocated_unit": v}
            }
        )
    print("Terminating Indosat Save Profiles")
    return True


def update_orange_availibity():
    ending_date = get_hours_before(7)
    return consumer_models.Profiles.objects(
        plan_uid__ne="",
        availability="Provisioning",
        status=True,
        create_datetime__lt=ending_date,
    ).update(availability="Free")


def orange_save_profiles(fetch_suspended_only=False, profiles_to_save: int = -1):
    orange_helper: Orange = Orange()
    # Temporary fix for script taking too long:
    number_of_existing_profiles: int = Profiles.objects(vendor_name="Orange").count()

    # TODO: set custom value to function arguments and send it in get_profiles function
    #  for example: (profilePicked/profileSaved)

    get_profiles_response: list = orange_helper.get_profiles(fetch_suspended=fetch_suspended_only, range_start=number_of_existing_profiles)
    if not get_profiles_response:
        logging.error("Failed to fetch orange profiles")

    logging.info("Fetched orange profiles successfully")

    profiles = sum((response_dict.get("connectivityDirectory", []) for response_dict in get_profiles_response), [])
    if profiles_to_save != -1:
        logging.info("Saving %s orange profiles", len(profiles))
        profiles = profiles[0:profiles_to_save]

    logging.info("Saving all available orange profiles")
    for profile in profiles:
        try:
            iccid = profile["iccid"]
            if profile["sim"]["profileStatus"] != "RELEASED":
                logging.info("will not save profile %s with status %s", iccid, profile["sim"]["status"])
                continue

            profile_already_exists = check_iccid(iccid)
            if profile_already_exists:
                logging.info("Profile %s already found in database", iccid)
                continue

            qr_code_value = profile["device"]["serialNumber"]
            qr_value_split = qr_code_value.split("$")

            new_profile = {
                "vendor_name": orange_helper.vendor_name,
                "sku": profile["sim"]["serialNumber"],
                "iccid": iccid,
                "qr_code_value": qr_code_value,
                "last_connection": None,
                "installation_status": None,
                "smdp_address": qr_value_split[1],
                "matching_id": qr_value_split[2],
                "status": True,
                "msisdn": profile["subscription"]["msisdnVoice"]["cc"] + profile["subscription"]["msisdnVoice"]["sn"],
                "subscription_id": profile["subscription"]["identifier"],
                "availability": "Provisioning",
            }

            add_profiles(new_profile)

        except Exception as e:
            logging.error("Exception while saving orange profiles %s", e)


def orange_allocate_profiles(bundle_code=None, daily_used=0):
    profile_added = 0
    try:
        message = ""
        orange_helper: Orange = Orange()
        original_daily_used = daily_used
        vendor_info: Vendors = get_vendor_info(ORANGE_VENDOR)

        if not vendor_info.is_active or not vendor_info.apply_inventory:
            logging.error("Vendor %s is not active", vendor_info.vendor_name)
            return 0, "Vendor is not active or inventory disabled"

        bundles = db_helper.get_bundles_by_vendor(
            ORANGE_VENDOR, is_active=True, deleted=False, bundle_code=bundle_code, allocate_profiles=True
        )
        bundle: Bundles
        for bundle in bundles:
            number_profiles_free = Profiles.objects(
                vendor_name=ORANGE_VENDOR,
                availability="Free",
                status=True,
                profile_names=bundle.zone_name,
                bundle_code=bundle.bundle_code,
            ).count()
            logging.info("bundle %s has %s profiles", bundle.bundle_code, number_profiles_free)
            if bundle.maximum_profiles_number == -1 or (
                bundle.maximum_profiles_number != -1 and number_profiles_free < int(bundle.maximum_profiles_number)
            ):
                if daily_used == 0:
                    daily_used = bundle.daily_used
                if daily_used == 0 and not Profiles.objects(vendor_name=ORANGE_VENDOR, bundle_code=bundle.bundle_code):
                    daily_used = instance_config.nb_profiles

                if bundle.maximum_profiles_number != -1 and daily_used > bundle.maximum_profiles_number:
                    daily_used = bundle.maximum_profiles_number

                if daily_used > number_profiles_free and daily_used > 0:
                    profile_to_add = daily_used - number_profiles_free

                    profiles_to_be_updated = Profiles.objects(vendor_name=ORANGE_VENDOR, availability="Provisioning", status=True).limit(
                        profile_to_add
                    )
                    logging.info("provisioning %s profiles for bundle %s", profile_to_add, bundle.bundle_code)
                    try:
                        for profile in profiles_to_be_updated:
                            data_amount = bundle.data_amount
                            data_unit = bundle.data_unit

                            if data_unit == "GB":
                                data_amount = str(data_amount * 1024)

                            allocate_response = orange_helper.provision_iccid(
                                iccid=profile.iccid,
                                bundle_duration=bundle.bundle_duration,
                                data_amount=data_amount,
                                zone_name=bundle.zone_name,
                            )
                            if not allocate_response:
                                logging.error("Couldn't provision profile %s with bundle %s", profile.iccid, bundle.bundle_code)
                                continue
                            profile.update(
                                bundle_code=bundle.bundle_code,
                                profile_names=bundle.profile_names,
                                allocate_date=datetime.datetime.now(),
                                plan_uid=str(uuid4()),
                                expiry_date=datetime.datetime.now() + relativedelta(days=365),
                                availability="Free",
                            )
                            profile_added += 1
                            bundle.update(inc__allocated_unit=1, is_active=True)
                            db_helper.ignore_runnable_script(bundle_code, original_daily_used)

                    except Exception as e:
                        logging.error("Error while allocate orange profiles ", e)
                        return 0, "Error while allocate orange profiles"
                    if original_daily_used == 0:
                        db_helper.run_runnable_scripts(
                            script="AllocateProfiles",
                            vendor_name=ORANGE_VENDOR,
                            bundle_code=bundle.bundle_code,
                            daily_used=daily_used,
                            profile_added=profile_added,
                            state="Finished",
                            failure_reason=message,
                        )

            else:
                logging.info("%s bundle has enough profiles no need to get more", bundle.bundle_code)
                message = f"{bundle.bundle_code} bundle has enough profiles no need to get more"
            if not bundle.is_active and profile_added > 0:
                bundle.update(set__is_active=True)
                add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])
            if bundle_code is None:
                bundles.update(set__daily_used=0, set__previous_daily_used=bundle.daily_used)
        if not bundles:
            print("No Orange bundles for the specific conditions")

    except Exception as e:
        logger.exception(f"<orange_allocate_profiles> error: {e}")
        return 0, allocation_exception
    return profile_added, message


def save_vodafone_bundles():
    try:
        get_vodafone_bundles(instance_config.vodafone_url, instance_config.vodafone_token)
    except Exception as e:
        print("-------------", str(e))


def dynamic_protocol_save_bundles(vendor_name):
    try:
        vendor = db_helper.get_vendor(vendor_name)
        if vendor:
            access_token, exception = get_token_dynamic_vendor(vendor_name=vendor_name)

            if access_token:
                pages = get_page_number(
                    vendor.bundles_count,
                )
                for i in range(0, pages):
                    save_dynamic_protocol_bundles(
                        token=access_token["data"]["token"],
                        i=i,
                        vendor_name=vendor_name,
                    )

    except Exception as e:
        print("exception flexi_save_bundles ", str(e))


def save_dynamic_protocol_bundles(token, i, vendor_name):
    headers = {"token": token}
    payload = {"group_by_offering": "yes", "limit": "100", "page": i + 1}

    r = requests.post(
        "{}/plan/inventory/view/v1".format(getattr(instance_config, vendor_name.lower() + "_url")),
        headers=headers,
        data=payload,
    )
    bundles = r.json()
    if bundles and "data" in bundles:
        list_countries_code = []

        for bundle in bundles["data"]:
            bundle_exist = check_bundle(bundle["plan"][0]["plan_code"], vendor_name)

            if bundle_exist:
                bundle_exist.allocated_unit = bundle["allocated_unit"]
                bundle_exist.consumed_unit = bundle["consumed_unit"]
                if bundle_exist.allocated_unit > bundle_exist.consumed_unit and not bundle_exist.deleted:
                    bundle_exist.is_active = True

                if float(bundle_exist.unit_price) == 0.0:
                    bundle_exist.is_active = False

                list_countries_code = []
                list_countries = []
                for policy_attributes in bundle["plan"][0]["policy"]:

                    policy_attribute_check = policy_attributes["policy_attributes"][0]
                    if "Country" in policy_attribute_check:

                        for policy in policy_attribute_check["Country"]:
                            if policy["value"] != "ISR" and policy["value"] not in bundle_exist.missing_country_code:
                                list_countries.append(policy["name"])
                                list_countries_code.append(policy["value"])

                bundle_exist.save()

            else:

                vendor = db_helper.get_vendor(vendor_name)
                rate_revenue = 25.0
                if vendor:
                    rate_revenue = vendor.rate_revenue
                list_countries = []
                retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)
                if int(bundle["allocated_unit"]) > 0:

                    new_bundle = {
                        "vendor_name": vendor_name,
                        "bundle_name": bundle["title"],
                        "bundle_marketing_name": bundle["title"],
                        "bundle_code": bundle["plan"][0]["plan_code"],
                        "bundle_vendor_name": bundle["title"],
                        "bundle_vendor_code": bundle["plan"][0]["plan_code"],
                        "unit_price": bundle["unit_price"],
                        "data_amount": bundle["plan"][0]["data_amount"],
                        "fullspeed_data_amount": bundle["plan"][0]["fullspeed_data_amount"],
                        "data_unit": bundle["plan"][0]["data_unit"],
                        "validity_amount": bundle["plan"][0]["validity_amount"],
                        "rate_revenue": float(rate_revenue),
                        "retail_price": round(float(retail_price), 2),
                        "profile_names": bundle["profile_names"],
                        "allocated_unit": bundle["allocated_unit"],
                    }

                    bundle_duration = int(bundle["plan"][0]["validity_amount"])

                    if bundle["plan"][0]["validity_unit"] == "MONTH":
                        new_bundle["validity_amount"] = str(int(bundle["plan"][0]["validity_amount"]) * 30)
                        bundle_duration = int(bundle["plan"][0]["validity_amount"]) * 30

                    list_countries_code = []
                    list_countries = []
                    for policy_attributes in bundle["plan"][0]["policy"]:

                        policy_attribute_check = policy_attributes["policy_attributes"][0]
                        if "Country" in policy_attribute_check:

                            for policy in policy_attribute_check["Country"]:
                                list_countries.append(policy["name"])
                                list_countries_code.append(policy["value"])

                    bundle_name, bundle_code = get_bundle_name(list_countries, list_countries_code, bundle)

                    # check if the string contains &, $, ", or '
                    if re.search("[@#%+__&$\"']", bundle_name):
                        # if so, replace them with a underscore
                        bundle_name = re.sub("[@#%+__&$\"']", "_", bundle_name)

                    if re.search("[@#%+__&$\"']", bundle_code):
                        # if so, replace them with a underscore
                        bundle_code = re.sub("[@#%+__&$\"']", "_", bundle_code)

                    new_bundle["bundle_name"] = bundle_name
                    new_bundle["bundle_code"] = bundle_code
                    new_bundle["bundle_duration"] = bundle_duration
                    new_bundle["is_region"] = False

                    if int(bundle["plan"][0]["category_id"]) == 2:
                        new_bundle["is_region"] = True
                        new_bundle["bundle_category"] = "global"
                        new_bundle["category_name"] = "global"
                    elif int(bundle["plan"][0]["category_id"]) == 4:
                        new_bundle["category_name"] = "region"
                        new_bundle["is_region"] = True
                        new_bundle["bundle_category"] = "region"

                    elif int(bundle["plan"][0]["category_id"]) == 3:

                        attributes = bundle["plan"][0]["policy"][0]["policy_attributes"]

                        if len(list_countries_code) > 2:

                            new_bundle["category_name"] = "region"
                            new_bundle["is_region"] = True
                            new_bundle["bundle_category"] = "region"
                        else:
                            new_bundle["is_region"] = True
                            new_bundle["category_name"] = "country_" + list_countries_code[0]
                            new_bundle["bundle_category"] = "country"

                    if float(bundle["unit_price"]) == 0.0:
                        new_bundle["is_active"] = False
                    else:
                        new_bundle["is_active"] = True

                    new_bundle["country_list"] = list_countries
                    new_bundle["country_code_list"] = list_countries_code
                    result = add_bundle(new_bundle)


def save_montymobile_bundles(token, i):
    print("Started Monty Mobile Save Bundles")
    vendor = db_helper.get_vendor(MONTY_MOBILE_VENDOR)
    rate_revenue = 25.0
    headers = {"Authorization": token}
    payload = {"group_by_offering": "yes", "limit": 100, "page": i + 1}

    r = requests.post(
        "{}/plan/inventory/view/v1".format(instance_config.montymobile_url),
        headers=headers,
        json=payload,
    )

    bundles = r.json()
    bundle_details = db_helper.get_bundle_details(["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"])
    bundle_detail = {}
    bundle_exist_list = []
    new_bundle_list = []
    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]
    if bundles and "data" in bundles:
        print("Found data and bundles in monty mobile save bundles")
        for bundle in bundles["data"]:
            bundle_exist = check_bundle(bundle["bundle_code"], MONTY_MOBILE_VENDOR)

            if bundle_exist:
                print(f"{MONTY_MOBILE_VENDOR} bundle exists - with bundle_vendor_code: {bundle['bundle_code']}")

                if bundle["allocated_unit"] > bundle_exist.allocated_unit:
                    bundle_exist.allocated_unit = bundle["allocated_unit"]

                if not bundle_exist.deleted:
                    profile_names = db_helper.remove_space_profiles(bundle["profile_names"])
                    refill_group = str(profile_names) + str(vendor.vendor_code)
                    refill_group = refill_group.replace(" ", "")
                    bundle_exist.refill_group = refill_group
                    bundle_exist.supplier_vendor = str(vendor.vendor_code)
                    bundle_exist.profile_names = profile_names
                    profiles = consumer_models.Profiles.objects(
                        vendor_name=MONTY_MOBILE_VENDOR,
                        availability="Free",
                        status=False,
                        allocate_date=None,
                        profile_names=profile_names,
                    ).count()
                    if profiles > 0:
                        bundle_exist.is_active = True

                list_countries_code = []
                list_countries = []
                if bundle["country_code"] is not None and bundle["country_name"] is not None:
                    for index, country_code in enumerate(bundle["country_code"]):
                        if country_code not in bundle_exist.missing_country_code:
                            list_countries_code.append(country_code)
                            list_countries.append(bundle["country_name"][index])

                bundle_exist.country_list = list_countries
                bundle_exist.country_code_list = list_countries_code
                bundle_exist.bundle_category = bundle["category"]
                bundle_exist.region_name = bundle["region_name"]
                bundle_exist.region_code = bundle["region_code"]
                if bundle["category"] == "global":
                    bundle_exist.category_name = "global"
                    bundle_exist.is_region = True
                elif bundle["category"] == "region":
                    bundle_exist.category_name = "region"
                    bundle_exist.is_region = True
                elif bundle["category"] == "country":
                    bundle_exist.is_region = False
                    bundle_exist.category_name = (
                            "country_" + list_countries_code[0]
                    )
                bundle_exist_list.append(bundle_exist.bundle_code)
                bundle_exist.save()
                print(f"{MONTY_MOBILE_VENDOR} bundle updated - bundle_vendor_code: {bundle['bundle_code']}")

            else:
                print(f"{MONTY_MOBILE_VENDOR} New bundle with bundle_vendor_code: {bundle['bundle_code']}")

                if vendor:
                    rate_revenue = vendor.rate_revenue
                retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)
                refill_group = str(bundle["profile_names"]) + str(vendor.vendor_code)
                refill_group = refill_group.replace(" ", "")

                new_bundle = {
                    "vendor_name": MONTY_MOBILE_VENDOR,
                    "bundle_name": bundle["bundle_name"],
                    "bundle_marketing_name": bundle["bundle_name"],
                    "bundle_code": bundle["bundle_code"],
                    "bundle_vendor_name": bundle["bundle_name"],
                    "bundle_vendor_code": bundle["bundle_code"],
                    "unit_price": bundle["unit_price"],
                    "data_amount": bundle["data_amount"],
                    "fullspeed_data_amount": bundle["full_speed_data_amount"],
                    "data_unit": bundle["data_unit"],
                    "validity_amount": str(bundle["validity_amount"]),
                    "rate_revenue": float(rate_revenue),
                    "retail_price": round(float(retail_price), 2),
                    "profile_names": db_helper.remove_space_profiles(bundle["profile_names"]),
                    "allocated_unit": 1,
                    "deleted": True,
                    "refill_group": refill_group,
                    "supplier_vendor": str(vendor.vendor_code),
                    "bundle_category": bundle["category"],
                    "region_name":  bundle["region_name"],
                    "region_code": bundle["region_code"]
                }

                if (new_bundle["data_amount"] > 1024) and new_bundle["data_unit"].lower() == "mb":
                    new_bundle["data_amount"] = new_bundle.get("data_amount") / 1024
                    new_bundle["data_unit"] = "GB"
                    new_bundle["fullspeed_data_amount"] = new_bundle.get("data_amount")

                print(bundle["bundle_name"])

                bundle_duration = int(bundle["validity_amount"])

                if bundle.get("validity_unit", "") == "MONTH":
                    new_bundle["validity_amount"] = str(int(bundle["validity_amount"]) * 30)
                    bundle_duration = int(bundle["validity_amount"]) * 30

                list_countries_code = []
                list_countries = []
                if bundle["country_code"] is None or bundle["country_name"] is None:
                    continue
                for index, country_code in enumerate(bundle["country_code"]):
                    list_countries_code.append(country_code)
                    list_countries.append(bundle["country_name"][index])

                bundle_name, bundle_code = get_bundle_name_montymobile(list_countries, list_countries_code, bundle)

                # check if the string contains &, $, ", or '
                if re.search("[@#%+__&$\"']", bundle_name):
                    # if so, replace them with a underscore
                    bundle_name = re.sub("[@#%+__&$\"']", "_", bundle_name)

                if re.search("[@#%+__&$\"']", bundle_code):
                    # if so, replace them with a underscore
                    bundle_code = re.sub("[@#%+__&$\"']", "_", bundle_code)

                new_bundle["bundle_name"] = bundle_name
                new_bundle["bundle_code"] = bundle_code
                new_bundle["bundle_duration"] = bundle_duration
                new_bundle["is_region"] = False

                if bundle["category"] == "global":
                    new_bundle["is_region"] = True
                    new_bundle["category_name"] = "global"
                elif bundle["category"] == "region":
                    new_bundle["category_name"] = "region"
                    new_bundle["is_region"] = True
                elif bundle["category"] == "country":
                    new_bundle["is_region"] = False
                    new_bundle["category_name"] = ("country_" + list_countries_code[0])

                if float(bundle["unit_price"]) == 0.0:
                    new_bundle["is_active"] = False
                    new_bundle["deleted"] = True

                    subject = f"------URGENT------ Vendor: Monty Mobile new bundle"
                    body = f"[{datetime.datetime.utcnow()} new bundle  {new_bundle['bundle_vendor_code']}  with unit price is zero"
                    send_custom_monitor_email(subject, body)

                else:
                    new_bundle["is_active"] = True

                new_bundle["country_list"] = list_countries
                new_bundle["country_code_list"] = list_countries_code
                new_bundle["plan_type_code"] = bundle_detail["plan_type_code"]
                new_bundle["plan_type"] = bundle_detail["plan_type"]
                new_bundle["activity_policy_code"] = bundle_detail["activity_policy_code"]
                new_bundle["activity_policy"] = bundle_detail["activity_policy"]
                new_bundle["available_netwok_code"] = bundle_detail["available_netwok_code"]
                new_bundle["available_netwok"] = bundle_detail["available_netwok"]
                new_bundle["top_up_plan_code"] = bundle_detail["top_up_plan_code"]
                new_bundle["top_up_plan"] = bundle_detail["top_up_plan"]
                new_bundle["ekyc_code"] = bundle_detail["ekyc_code"]
                new_bundle["ekyc"] = bundle_detail["ekyc"]

                new_bundle_list.append(new_bundle["bundle_code"])
                result = add_bundle(new_bundle)
                print(f" {MONTY_MOBILE_VENDOR} New bundle added with bundle_vendor_code: {bundle['bundle_code']}")

        add_to_update_bundle_version([f"Save Monty-Mobile Bundles (New One): {new_bundle_list}"])


def save_flexiroam_bundles(token, i):
    vendor = db_helper.get_vendor(FLEXIROAM_VENDOR)
    rate_revenue = 25.0
    headers = {"token": token}
    # add latest_offering to get latest bundles only
    payload = {
        "group_by_offering": "yes",
        "limit": "100",
        "page": i + 1,
        "latest_offering": 1,
    }

    r = requests.post(
        "{}/plan/inventory/view/v1".format(instance_config.flexiroam_url),
        headers=headers,
        data=payload,
    )
    bundles = r.json()
    bundle_details = db_helper.get_bundle_details(["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"])
    bundle_detail = {}
    bundle_exist_list = []
    new_bundle_list = []
    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]

    if bundles and "data" in bundles:
        list_countries_code = []

        for bundle in bundles["data"]:

            bundle_exist = check_bundle(bundle["plan"][0]["plan_code"], FLEXIROAM_VENDOR)

            if bundle_exist:

                bundle_exist.allocated_unit = bundle["allocated_unit"]
                bundle_exist.consumed_unit = bundle["consumed_unit"]
                profile_names = db_helper.remove_space_profiles(bundle_exist.profile_names)
                refill_group = profile_names + str(vendor.vendor_code)
                refill_group = refill_group.replace(" ", "")
                if bundle_exist.allocated_unit > bundle_exist.consumed_unit and not bundle_exist.deleted:

                    profiles = consumer_models.Profiles.objects(
                        vendor_name=FLEXIROAM_VENDOR,
                        availability="Free",
                        status=False,
                        allocate_date=None,
                        profile_names=profile_names,
                    ).count()
                    if profiles > 0:
                        bundle_exist.is_active = True
                bundle_exist.refill_group = refill_group
                bundle_exist.supplier_vendor = str(vendor.vendor_code)
                if not hasattr(bundle_exist, "plan_type_code"):
                    bundle_exist.plan_type_code = bundle_detail["plan_type_code"]
                    bundle_exist.plan_type = bundle_detail["plan_type"]

                if not hasattr(bundle_exist, "activity_policy_code"):
                    bundle_exist.activity_policy_code = bundle_detail["activity_policy_code"]
                    bundle_exist.activity_policy = bundle_detail["activity_policy"]

                if not hasattr(bundle_exist, "available_netwok_code"):
                    bundle_exist.available_netwok_code = bundle_detail["available_netwok_code"]
                    bundle_exist.available_netwok = bundle_detail["available_netwok"]

                if not hasattr(bundle_exist, "top_up_plan_code"):
                    bundle_exist.top_up_plan_code = bundle_detail["top_up_plan_code"]
                    bundle_exist.top_up_plan = bundle_detail["top_up_plan"]

                if not hasattr(bundle_exist, "ekyc_code"):
                    bundle_exist.ekyc_code = bundle_detail["ekyc_code"]
                    bundle_exist.ekyc = bundle_detail["ekyc"]

                list_countries_code = []
                list_countries = []
                for policy_attributes in bundle["plan"][0]["policy"]:

                    policy_attribute_check = policy_attributes["policy_attributes"][0]
                    if "Country" in policy_attribute_check:

                        for policy in policy_attribute_check["Country"]:
                            if policy["value"] != "ISR" and policy["value"] not in bundle_exist.missing_country_code:
                                list_countries.append(policy["name"])
                                list_countries_code.append(policy["value"])

                bundle_exist_list.append(bundle_exist.bundle_code)
                bundle_exist.save()

            else:

                if vendor:
                    rate_revenue = vendor.rate_revenue
                list_countries = []
                retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)
                if int(bundle["allocated_unit"]) > 0:
                    refill_group = bundle["profile_names"] + str(vendor.vendor_code)
                    refill_group = refill_group.replace(" ", "")
                    new_bundle = {
                        "vendor_name": FLEXIROAM_VENDOR,
                        "bundle_name": bundle["title"],
                        "bundle_marketing_name": bundle["title"],
                        "bundle_code": bundle["plan"][0]["plan_code"],
                        "bundle_vendor_name": bundle["title"],
                        "bundle_vendor_code": bundle["plan"][0]["plan_code"],
                        "unit_price": bundle["unit_price"],
                        "data_amount": bundle["plan"][0]["data_amount"],
                        "fullspeed_data_amount": bundle["plan"][0]["fullspeed_data_amount"],
                        "data_unit": bundle["plan"][0]["data_unit"],
                        "validity_amount": bundle["plan"][0]["validity_amount"],
                        "rate_revenue": float(rate_revenue),
                        "retail_price": round(float(retail_price), 2),
                        "profile_names": bundle["profile_names"],
                        "allocated_unit": bundle["allocated_unit"],
                        "deleted": True,
                        "refill_group": refill_group,
                        "supplier_vendor": str(vendor.vendor_code),
                    }

                    bundle_duration = int(bundle["plan"][0]["validity_amount"])

                    if bundle["plan"][0]["validity_unit"] == "MONTH":
                        new_bundle["validity_amount"] = str(int(bundle["plan"][0]["validity_amount"]) * 30)
                        bundle_duration = int(bundle["plan"][0]["validity_amount"]) * 30

                    list_countries_code = []
                    list_countries = []
                    for policy_attributes in bundle["plan"][0]["policy"]:

                        policy_attribute_check = policy_attributes["policy_attributes"][0]
                        if "Country" in policy_attribute_check:

                            for policy in policy_attribute_check["Country"]:
                                list_countries.append(policy["name"])
                                list_countries_code.append(policy["value"])

                    bundle_name, bundle_code = get_bundle_name(list_countries, list_countries_code, bundle)

                    # check if the string contains &, $, ", or '
                    if re.search("[@#%+__&$\"']", bundle_name):
                        # if so, replace them with a underscore
                        bundle_name = re.sub("[@#%+__&$\"']", "_", bundle_name)

                    if re.search("[@#%+__&$\"']", bundle_code):
                        # if so, replace them with a underscore
                        bundle_code = re.sub("[@#%+__&$\"']", "_", bundle_code)

                    new_bundle["bundle_name"] = bundle_name
                    new_bundle["bundle_code"] = bundle_code
                    new_bundle["bundle_duration"] = bundle_duration
                    new_bundle["is_region"] = False

                    if int(bundle["plan"][0]["category_id"]) == 2:
                        new_bundle["is_region"] = True
                        new_bundle["bundle_category"] = "global"
                        new_bundle["category_name"] = "global"
                    elif int(bundle["plan"][0]["category_id"]) == 4:
                        new_bundle["category_name"] = "region"
                        new_bundle["is_region"] = True
                        new_bundle["bundle_category"] = "region"

                    elif int(bundle["plan"][0]["category_id"]) == 3:

                        attributes = bundle["plan"][0]["policy"][0]["policy_attributes"]

                        if len(list_countries_code) > 2:

                            new_bundle["category_name"] = "region"
                            new_bundle["is_region"] = True
                            new_bundle["bundle_category"] = "region"
                        else:
                            new_bundle["is_region"] = True
                            new_bundle["category_name"] = "country_" + list_countries_code[0]
                            new_bundle["bundle_category"] = "country"

                    if float(bundle["unit_price"]) == 0.0:
                        new_bundle["is_active"] = False
                        new_bundle["deleted"] = True

                        subject = f"------URGENT------ Vendor: Flexiroam new bundle"
                        body = f"[{datetime.datetime.utcnow()} new bundle  {new_bundle['bundle_vendor_code']}  with unit price is zero"
                        send_custom_monitor_email(subject, body)

                    else:
                        new_bundle["is_active"] = True

                    new_bundle["country_list"] = list_countries
                    new_bundle["country_code_list"] = list_countries_code
                    new_bundle_list.append(new_bundle["bundle_code"])
                    result = add_bundle(new_bundle)

        add_to_update_bundle_version([f"Save Flexiroam Bundles (New One): {new_bundle_list}"])


def flexiroamv2_save_bundles():
    try:
        vendor = db_helper.get_vendor(FLEXIROAM_VENDOR_V2)
        if not vendor:
            return
        last_update = datetime.datetime.utcnow()
        vendor.last_update_bundles_date = last_update
        vendor.save()
        bundle_details = db_helper.get_bundle_details(
            [
                "plan_type",
                "activity_policy",
                "available_netwok",
                "top_up_plan",
                "ekyc",
            ]
        )
        bundle_detail = {}
        new_bundle_list = []
        for detail in bundle_details:
            bundle_detail[detail["category"]] = detail["detail_msg"]
            bundle_detail[detail["category"] + "_code"] = detail["detail_code"]

        flexiroam_vendor = FlexiroamAPI()

        result = flexiroam_vendor.list_plans()

        for res in result:
            bundle_already_exists = db_helper.check_bundle(res["bundle_vendor_code"], FLEXIROAM_VENDOR_V2)
            if bundle_already_exists:
                bundle_already_exists.update(set__update_at=datetime.datetime.utcnow())
            else:
                new_bundle = {
                    "vendor_name": FLEXIROAM_VENDOR_V2,
                    "bundle_name": res["bundle_name"],
                    "bundle_marketing_name": res["bundle_name"],
                    "bundle_code": res["bundle_code"],
                    "bundle_vendor_name": res["bundle_name"],
                    "bundle_vendor_code": res["bundle_vendor_code"],
                    "unit_price": res["price"],
                    "data_amount": res["data_amount"],
                    "fullspeed_data_amount": res["data_amount"],
                    "data_unit": res["data_unit"],
                    "validity_amount": str(res["bundle_duration"]),
                    "bundle_duration": int(res["bundle_duration"]),
                    "country_list": res["country_list"],
                    "country_code_list": res["country_code_list"],
                    "rate_revenue": float(vendor.rate_revenue),
                    "retail_price": round(float(res["price"]), 2),
                    "profile_names": res["profile_names"],
                    "deleted": True,
                    "supplier_vendor": str(vendor.vendor_code),
                    "bundle_category": res["bundle_category"],
                    "allocated_unit": 1,
                    "consumed_unit": 0,
                    "refill_group": str(vendor.vendor_code) + res["profile_names"],
                    "support_topup": vendor.support_topup,
                }
                result = add_bundle(new_bundle)
                new_bundle_list.append(res["bundle_vendor_code"])

        bundle_not_updated = Bundles.objects(vendor_name=FLEXIROAM_VENDOR_V2, update_at__lte=last_update).distinct("bundle_code")

        Bundles.objects(vendor_name=FLEXIROAM_VENDOR_V2, update_at__lte=last_update).update(set__deleted=True)
        if len(new_bundle_list) > 0 or len(bundle_not_updated) > 0:
            subject = f"{FLEXIROAM_VENDOR_V2} update bundles"
            body = ""
            if len(new_bundle_list) > 0:
                body = f"[{datetime.datetime.utcnow()} new bundle  {new_bundle['bundle_vendor_code']}  with unit price is zero"

                add_to_update_bundle_version([f"Save Flexiroam Bundles (New One): {new_bundle_list}"])
            if len(bundle_not_updated) > 0:
                body += body + f"{datetime.datetime.utcnow()}  bundles deleted {bundle_not_updated}"

                add_to_update_bundle_version([f"Delete Flexiroam Bundles : {bundle_not_updated}"])
            send_custom_monitor_email(subject, body)
    except Exception as e:
        logging.warning("Exception on  flexiroamv2_save_bundles %s", e)


def flexi_save_bundles():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)

        if access_token:
            vendor = db_helper.get_vendor(FLEXIROAM_VENDOR)
            if vendor:
                pages = get_page_number(vendor.bundles_count)
                for i in range(0, pages):
                    save_flexiroam_bundles(access_token["data"]["token"], i)

    except Exception as e:
        print("exception flexi_save_bundles ", str(e))


def montymobile_save_bundles():
    try:
        print("Entered montymobile_save_bundles")
        access_token, exception = get_token_montymobile()
        print("access_token")
        if access_token:
            vendor = db_helper.get_vendor(MONTY_MOBILE_VENDOR)
            if vendor:

                pages = get_page_number(vendor.bundles_count)
                for i in range(0, pages):
                    save_montymobile_bundles(access_token["accessToken"], i)

        else:
            print("EXCEPTION mmsb")
            print(exception)
            print("could not attain access_token MMSB")
    except Exception as e:
        print("exception montymobile_save_bundles ", str(e))


def flexi_save_token():
    print("env ", os.getenv("ENV"))
    email_subject = "Flexiroam token for {} env".format(os.getenv("ENV"))
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)
        if access_token:
            flexiroam_token = access_token["data"]["token"]
            consumer_models.Vendors.objects(vendor_name=FLEXIROAM_VENDOR).update(set__temp_token=flexiroam_token)
        else:
            email_settings = EmailSettings.objects().first()
            send_error_email(
                instance_config.list_of_users_,
                email_subject,
                " error function in saving new flexiroam token: No access token from flexi",
                email_settings,
            )
    except Exception as e:
        msg = " error function in saving new flexiroam token:   the error message : is {}".format(str(e))
        email_settings = EmailSettings.objects().first()
        send_error_email(instance_config.list_of_users_, email_subject, msg, email_settings)

        print("exception flexi_save_token ", str(e))


def montymobile_save_token_():
    try:
        montymobile_save_token()

    except Exception as e:
        print("Error in montymobile save token as", e)


def dynamic_vendor_save_token(vendor_name):
    print("env ", os.getenv("ENV"))
    email_subject = "{} token for {} env".format(vendor_name, os.getenv("ENV"))
    try:
        access_token, exception = get_token_dynamic_vendor(vendor_name=vendor_name)
        if access_token:
            vendor_token = access_token["data"]["token"]
            consumer_models.Vendors.objects(vendor_name=vendor_name).update(set__temp_token=vendor_token)
        else:
            email_settings = EmailSettings.objects().first()
            send_error_email(
                instance_config.list_of_users_,
                email_subject,
                f" error function in saving new vendor token: No access token from {vendor_name}",
                email_settings,
            )
    except Exception as e:
        msg = " error function in saving new {} token:   the error message : is {}".format(vendor_name, str(e))
        email_settings = EmailSettings.objects().first()
        send_error_email(instance_config.list_of_users_, email_subject, msg, email_settings)

        print("exception dynamic_vendor_save_token ", str(e))


def update_profile(response):
    for profile in response:
        found_iccid = check_iccid(profile["iccid"])

        if found_iccid:

            if profile["installation_status"] is not None:
                found_iccid.installation_status = profile["installation_status"]["status"]
                found_iccid.save()

    return True


def save_profile(response):
    for profile in response:
        found_iccid = check_iccid(profile["iccid"])
        if not found_iccid:
            qr_code_value = profile["qr_code_value"]
            qr_code_value = qr_code_value.replace("LPA:1", "")

            qr_value_splited = qr_code_value.split("$")

            new_profile = {
                "vendor_name": FLEXIROAM_VENDOR,
                "sku": profile["sku"],
                "iccid": profile["iccid"],
                "qr_code_value": profile["qr_code_value"],
                "profile_names": profile["profile_names"],
                "last_connection": profile["last_connection"],
                "installation_status": profile["installation_status"],
                "smdp_address": qr_value_splited[1],
                "matching_id": qr_value_splited[2],
                "status": False,
            }

            res = add_profiles(new_profile)
            activate_bundle(profile["profile_names"])
        else:
            if found_iccid.availability != "Expired":
                found_iccid.availability = "Free"
                found_iccid.save()
    return True


def save_profile_dynamic(vendor_name, response):
    for profile in response:
        found_iccid = check_iccid(profile["iccid"])
        if not found_iccid:
            qr_code_value = profile["qr_code_value"]
            qr_code_value = qr_code_value.replace("LPA:1", "")

            qr_value_splited = qr_code_value.split("$")

            new_profile = {
                "vendor_name": vendor_name,
                "sku": profile["sku"],
                "iccid": profile["iccid"],
                "qr_code_value": profile["qr_code_value"],
                "profile_names": profile["profile_names"],
                "last_connection": profile["last_connection"],
                "installation_status": profile["installation_status"],
                "smdp_address": qr_value_splited[1],
                "matching_id": qr_value_splited[2],
            }

            res = add_profiles(new_profile)
            activate_bundle(profile["profile_names"])
        else:
            print("found iccid ", found_iccid.iccid)
            if found_iccid.availability != "Expired":
                found_iccid.availability = "Free"
                found_iccid.save()
    return True


def round_up(n):
    return round(n * 2) / 2


def update_prices_vendor(vendor_name):
    vendor = db_helper.get_vendor(vendor_name)
    affected_rows = 0
    success_rows = 0
    if vendor:
        rate_revenue = vendor.rate_revenue
        bundles = db_helper.get_bundles_by_vendor(vendor_name)
        for bundle in bundles:
            retail_price = float(bundle["unit_price"]) + (float(bundle["unit_price"]) * float(rate_revenue) / 100)
            retail_price_rounded = round(retail_price, 1)
            bundle["retail_price"] = retail_price_rounded
            bundle["rate_revenue"] = rate_revenue

            if bundle.save():
                success_rows = success_rows + 1
            else:
                affected_rows = affected_rows + 1
    return affected_rows, success_rows


def flexi_update_profiles():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)

        if access_token:
            headers = {"token": access_token["data"]["token"]}

            payload = {"availability": 1, "sim_type": "eSIM", "limit": "10000"}
            r = requests.post(
                "{}/product/inventory/view/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=payload,
            )

            if r.status_code == 200:
                response = r.json()
                if response:
                    update_profile(response["data"])

    except Exception as e:
        print("exception flexi_update_profiles ", str(e))


def flexiroamv2_allocate_profiles(bundle_code=None, daily_used=0):
    profile_added = 0
    try:
        vendor_info = get_vendor_info(FLEXIROAM_VENDOR_V2)
        if not vendor_info.is_active:
            return 0, "Vendor is not active"

        bundles = db_helper.get_bundles_by_vendor(
            FLEXIROAM_VENDOR_V2, is_active=True, deleted=False, bundle_code=bundle_code, allocate_profiles=True
        )
        if not bundles:
            logging.info(f"No active {FLEXIROAM_VENDOR_V2} bundles found for allocation.")
            return profile_added, "No active bundles found"

        for bundle in bundles:
            daily_used = determine_daily_used(bundle, daily_used)

            profiles_needed = calculate_profiles_needed_allocation(bundle, daily_used, FLEXIROAM_VENDOR_V2)
            if profiles_needed > 0:
                profile_added += allocate_profiles_flexi2(bundle, profiles_needed, daily_used, profile_added)

            handle_maximum_profiles(bundle)
            update_bundle_status(bundle, daily_used, profiles_needed)
        if bundle_code is None:
            Bundles.objects(vendor_name=FLEXIROAM_VENDOR_V2).update(set__daily_used=0)
            add_to_update_bundle_version([f"Update Inventory {FLEXIROAM_VENDOR_V2}"])
    except Exception as e:
        logging.exception(f"<flexiroamv2_allocate_profiles> error: {str(e)}")
        return 0, "Error occurred during profile allocation"

    return profile_added, "Profile allocation completed successfully"


def determine_daily_used(bundle, daily_used):
    if daily_used == 0:
        # Use nb_profiles from instance_config if daily_used is zero
        daily_used = bundle.daily_used

    # Ensure daily_used does not exceed maximum_profiles_number
    return min(daily_used, bundle.maximum_profiles_number) if bundle.maximum_profiles_number != -1 else daily_used


def calculate_profiles_needed_allocation(bundle: Bundles, daily_used: int, vendor_name: str):
    number_profiles_free = Profiles.objects(
        vendor_name=vendor_name,
        availability="Free",
        status=True,
        plan_uid__ne="",
        bundle_code=bundle.bundle_code,
        profile_names=bundle.profile_names,
        expiry_date__gte=datetime.datetime.utcnow(),
    ).count()

    # Determine profiles needed based on number of free profiles and daily usage
    if daily_used == 0 and number_profiles_free == 0:
        daily_used = instance_config.nb_profiles

    print(
        "daily_used - number_profiles_free ",
        number_profiles_free,
        daily_used - number_profiles_free,
    )
    return max(0, daily_used - number_profiles_free)


def allocate_profiles_flexi2(bundle, profiles_needed, daily_used, profile_added):
    flexi_helper = FlexiroamAPI()

    for _ in range(profiles_needed):
        try:
            allocation = flexi_helper.buy_plan(bundle.bundle_vendor_code, True)
            if isinstance(allocation, dict) and allocation.get("error"):
                error_message = allocation["error"]

                body = (
                    f"[{datetime.datetime.utcnow()}] profiles can't be saved "
                    f"for bundle_vendor_code {bundle.bundle_vendor_code} for vendor {FLEXIROAM_VENDOR_V2} --- calling allocate_profiles_flexi2: exception: {error_message}"
                )
                send_custom_monitor_email(subject="PROFILES CAN'T BE SAVED", body=body)

                return 0
            else:
                profile = create_profile(allocation, bundle)
                if profile:

                    profile_added += 1

                    bundle.update(inc__allocated_unit=1, is_active=True)
                    db_helper.ignore_runnable_script(bundle.bundle_code, daily_used)
                else:
                    logging.error(f"Failed to allocate profile for bundle {bundle.bundle_code}.")
        except Exception as e:
            logging.exception(f"Error allocating profile for {bundle.bundle_code}: {str(e)}")
            return 0

    return profile_added


def create_profile(allocation, bundle):
    sim_details = FlexiroamAPI().get_sim_details(allocation.get("sku"))
    if not sim_details:
        logging.error(f"Failed to retrieve SIM details for SKU {allocation.get('sku')}")
        return None
    profile = Profiles.objects(sku=str(allocation.get("sku"))).first()
    parts = allocation.get("esim_lpa").split("$")
    active_plans = sim_details.get("active_plans", [])

    # Retrieve plan_uuid from the first plan in the active_plans array, if it exists
    plan_uid = active_plans[0].get("plan_uuid") if active_plans else ""

    if profile and getattr(profile, "bundle_code", None):
        if profile.bundle_code.strip() != "":
            body = (
                f"[{datetime.datetime.utcnow()}] profiles can't be saved as already taken "
                f'for sku {str(allocation.get("sku"))} already taken for vendor {FLEXIROAM_VENDOR_V2} '
            )

            send_custom_monitor_email(subject="PROFILES CAN'T BE SAVED", body=body)
            return False
        else:
            profile.update(
                set__bundle_code=bundle.bundle_code,
                set__sku=str(allocation.get("sku")),
                set__smdp_address=parts[1],
                set__matching_id=parts[2],
                set__qr_code_value=allocation.get("esim_lpa"),
                set__allocate_date=datetime.datetime.utcnow(),
                set__status=True,
                set__iccid=sim_details.get("details", {}).get("iccid", ""),
                set__profile_names=bundle.profile_names,
                set__plan_uid=plan_uid,
                set__expiry_date=datetime.datetime.utcnow() + relativedelta(months=3),
            )
            return True
    return Profiles(
        vendor_name=FLEXIROAM_VENDOR_V2,
        bundle_code=bundle.bundle_code,
        sku=str(allocation.get("sku")),
        smdp_address=parts[1],
        matching_id=parts[2],
        qr_code_value=allocation.get("esim_lpa"),
        allocate_date=datetime.datetime.utcnow(),
        status=True,
        iccid=sim_details.get("details", {}).get("iccid", ""),
        profile_names=bundle.profile_names,
        plan_uid=plan_uid,
        expiry_date=datetime.datetime.utcnow() + relativedelta(months=3),
    ).save()


def handle_maximum_profiles(bundle):
    if bundle.maximum_profiles_number != -1:
        logging.info(f"{bundle.bundle_code} has reached the maximum number of profiles.")


def update_bundle_status(bundle, daily_used, profile_added=0):
    if not bundle.is_active:
        bundle.update(set__is_active=True)
        add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])


def save_profile_flexi(token, i):
    headers = {"token": token}
    payload = {"availability": 0, "sim_type": "eSIM", "limit": "10000", "page": i}

    r = requests.post(
        "{}/product/inventory/view/v1".format(instance_config.flexiroam_url),
        headers=headers,
        data=payload,
    )
    print("save_profile_flexi ", r.status_code)
    if r.status_code == 200:

        response = r.json()
        if response:
            save_profile(response["data"])


def vodafone_save_profiles(bundle_code=None, daily_used=0):
    vendor_info = get_vendor_info(VODAFONE_VENDOR)
    profile_added = 0
    message = ""
    original_daily_used = daily_used

    if not vendor_info.is_active:
        return profile_added, "Vendor is Inactive"

    bundles = db_helper.get_bundles_by_vendor(
        VODAFONE_VENDOR,
        is_active=True,
        deleted=False,
        bundle_code=bundle_code,
        allocate_profiles=True,
    )
    bundle: Bundles
    for bundle in bundles:
        profile_added = 0

        profile_names = bundle.profile_names.replace(" ", "")
        restriction_on_bundle_count = bundle.maximum_profiles_number != -1
        profiles = Profiles.objects(
            vendor_name=VODAFONE_VENDOR,
            availability="Free",
            profile_names=profile_names,
            bundle_code=bundle.bundle_code,
        )
        free_profile_count = profiles.filter(status=True).count()
        one_minute_ago = datetime.datetime.utcnow() - datetime.timedelta(minutes=1)
        free_unallocated_profile_count = profiles.filter(status=False, create_datetime__gt=one_minute_ago).count()
        total_free_profiles = free_profile_count + free_unallocated_profile_count

        reached_max_num_profiles_allowed = int(total_free_profiles) < int(bundle.maximum_profiles_number)

        if not restriction_on_bundle_count or reached_max_num_profiles_allowed:
            if daily_used == 0:
                daily_used = bundle.daily_used
            if daily_used == 0:
                if consumer_models.Profiles.objects(Q(vendor_name=VODAFONE_VENDOR) & Q(bundle_code=bundle.bundle_code)).count() == 0:
                    daily_used = instance_config.nb_profiles

            if restriction_on_bundle_count and daily_used > bundle.maximum_profiles_number:
                daily_used = bundle.maximum_profiles_number
            try:
                status, profile_added, message = save_vodafone_profiles(
                    bundle.bundle_code,
                    bundle.bundle_vendor_code,
                    daily_used,
                    total_free_profiles,
                    original_daily_used,
                )

            except Exception as error:
                print(f"Couldn't save profiles for exception {error} in Vodafone")

            if bundle_code is None:
                bundle.update(set__previous_daily_used=bundle.daily_used, set__daily_used=0)
                bundle.reload()
            daily_used = original_daily_used

        else:
            print(f"{bundle.bundle_code} Vodafone didnt need profiles. Reached the maximum number of profiles")
            if not bundle.is_active:
                bundle.update(set__is_active=True)
                add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])

    if bundle_code is None:

        bundles = db_helper.get_bundles_by_vendor(VODAFONE_VENDOR)
        for bundle in bundles:
            bundle.update(set__daily_used=0)

    return profile_added, message


def save_vodafone_profiles(
    bundle_code,
    bundle_vendor_code,
    daily_used,
    total_free_profiles,
    original_daily_used=0,
):
    profile_added = 0
    message = ""
    if daily_used > total_free_profiles and daily_used > 0:
        profile_to_add = daily_used - total_free_profiles
        for i in range(0, profile_to_add):
            try:
                order_number = str(uuid4())
                profile_doc = {
                    "vendor_name": VODAFONE_VENDOR,
                    "iccid": order_number,
                    "bundle_code": bundle_code,
                    "status": False,
                }

                consumer_models.Profiles(**(profile_doc)).save()

                profile = db_helper.check_iccid(order_number)
                url = "{}/network/things/consumer-profile/{}".format(instance_config.vodafone_url, bundle_vendor_code)
                headers = {
                    "ResponseURLs": instance_config.callback_get_iccid,
                    "Authorization": "Bearer " + str(instance_config.vodafone_token),
                }

                payload = {"customReference": order_number}

                response = requests.request("POST", url, headers=headers, json=payload)
                if response.status_code == 400:
                    res = response.json()
                    exception = res["error"]["description"]
                    message = exception
                    body = (
                        f"[{datetime.datetime.utcnow()}] profiles can't be saved "
                        f"for bundle_vendor_code {bundle_vendor_code} for vendor {VODAFONE_VENDOR} --- contact devs: exception: {exception}"
                    )
                    send_custom_monitor_email(subject="PROFILES CAN'T BE SAVED", body=body)

                elif response.status_code == 200:
                    res = response.json()
                    profile.plan_uid = res["acknowledgement"]["id"]
                    profile.save()
                    profile_added = profile_added + 1
                    db_helper.ignore_runnable_script(bundle_code, original_daily_used)

                    consumer_models.Bundles.objects(Q(bundle_code=bundle_code)).update(inc__allocated_unit=1)

            except (
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                NewConnectionError,
            ) as error:
                print(f"<allocate_vodafone_bundle> remote error happened: {error} while buying bundle: {bundle_code}")

                return False, profile_added, message
            except ValueError as error:
                print(f"<allocate_vodafone_bundle> remote error happened: {error} while buying bundle: {bundle_code}")

                return False, profile_added, message
            except Exception as error:
                print(f"<allocate_vodafone_bundle> error happened: {error} while buying bundle: {bundle_code}")

                return False, profile_added, message

    else:
        print("Cannot save profiles. Exceeded maximum allowed profiles.")
        message = "Cannot save profiles. Exceeded maximum allowed profiles."
    if original_daily_used == 0:
        db_helper.run_runnable_scripts(
            script="AllocateProfiles",
            vendor_name=VODAFONE_VENDOR,
            bundle_code=bundle_code,
            daily_used=daily_used,
            profile_added=profile_added,
            state="Finished",
        )

    return True, profile_added, message


def save_profile_dynamic_vendor(vendor_name, token, i):
    headers = {"token": token}
    payload = {"availability": 0, "sim_type": "eSIM", "limit": "10000", "page": i}
    vendor_url = getattr(instance_config, vendor_name.lower() + "_url")

    r = requests.post("{}/product/inventory/view/v1".format(vendor_url), headers=headers, data=payload)
    print("save_profile_dynamic_vendor ", r.status_code)
    if r.status_code == 200:

        response = r.json()
        if response:
            save_profile_dynamic(vendor_name, response["data"])


def flexi_save_profiles():
    try:
        access_token, exception = get_token_flexiroam(instance_config.flexiroam_username, instance_config.flexiroam_password)

        if access_token:

            for i in range(0, 4):
                save_profile_flexi(access_token["data"]["token"], i)

    except Exception as e:
        print("exception flexi_save_profiles ", str(e))


def montymobile_save_profiles():
    logger.info("Starting Monty Mobile save profiles")

    monty_mobile = MontyMobile()

    max_profiles = 1000
    page = 1
    profiles_added = 0

    while profiles_added < max_profiles:
        try:
            resp = monty_mobile.get_profiles(page)
            profiles = resp.get("data") if resp else None

            if not profiles:
                logger.warning("No profiles received on page %s", page)
                break

            for profile in profiles:
                if check_iccid(profile["iccid"]):
                    continue

                qr_code_value = profile["qr_code_value"].replace("LPA:1", "")
                qr_value_splited = qr_code_value.split("$")
                if len(qr_value_splited) < 3:
                    logger.warning("Invalid QR code format for ICCID: %s", profile["iccid"])
                    continue

                new_profile = {
                    "vendor_name": MONTY_MOBILE_VENDOR,
                    "sku": profile["iccid"],
                    "iccid": profile["iccid"],
                    "qr_code_value": profile["qr_code_value"],
                    "profile_names": profile["profile_names"],
                    "last_connection": profile["last_connection"],
                    "installation_status": profile["installation_status"],
                    "smdp_address": qr_value_splited[1],
                    "matching_id": qr_value_splited[2],
                    "status": False,
                    "expiry_date": profile["expiry_date"] if profile["expiry_date"] else None,
                }

                add_profiles(new_profile)
                profiles_added += 1
                activate_bundle(profile["profile_names"])
            page += 1
        except Exception as e:
            logger.exception("Exception while montymobile_save_profiles: %s", e)
            break
    logger.info("montymobile_save_profiles executed and %d profiles has been added successfully", profiles_added)


def get_hours_before(hours):
    now = datetime.datetime.utcnow()
    d = now - datetime.timedelta(hours=hours)
    date_final = d.strftime("%Y-%m-%d %H:%M:%S")
    return date_final


def dyanmic_vendor_save_profiles(vendor_name):
    try:
        vendor = db_helper.get_vendor(vendor_name)
        if vendor:
            access_token, exception = get_token_dynamic_vendor(vendor_name=vendor_name)

            if access_token:

                for i in range(0, 4):
                    save_profile_dynamic_vendor(vendor_name, access_token["data"]["token"], i)

    except Exception as e:
        print("exception dynamic_vendor_save_profiles ", str(e))


def get_date_before(days):
    today = datetime.datetime.today()
    d = today - datetime.timedelta(days=days)
    date_final = d.strftime("%Y-%m-%d 00:00:00")

    return date_final


def rest_profiles(starting_hours: int = 6, ending_hours: int = 3):
    """

        :param starting_hours: starting time-range (in hours) before UTC time.
        :param ending_hours: ending time-range (in hours) before UTC time.

        Updates batches of profiles in a specified time-range (from parameters) which are not linked
        to any order owned by any party.
        Sets profiles' availability to Free and reactivates bundles linked to these profiles if
        they were inactive

    """
    # TODO: rename this function and reflect the naming on all usages.

    try:
        ending_date = get_hours_before(ending_hours)
        start_date = get_hours_before(starting_hours)
        logger.info("Freeing profiles from %s to %s", start_date, ending_date)
        orders = db_helper.get_unpaid_orders(start_date, ending_date)
        orders_unique_iccid_list = set(map(lambda order: order.get("iccid"), list(orders)))

        # TODO: get all data from this function instead of querying order_history again on this iccid to get its order statuses
        reactivated_bundles = []
        freed_profiles = []
        for iccid in orders_unique_iccid_list:
            current_profile_order_statuses: set = set(Order_history.objects(iccid=iccid).distinct('order_status'))
            non_refundable_order_statuses: set = {"Successful", "Refunded"}
            cannot_free_profile: bool = bool(current_profile_order_statuses & non_refundable_order_statuses)
            if cannot_free_profile:
                logger.debug("Skipping freeing this profile, it has succesfull/refunded orders")
                continue
            result, reactivated_bundles = reset_free_profile(iccid, start_date, ending_date, [], reactivated_bundles)

            if result:
                freed_profiles.append(iccid)

        if freed_profiles or reactivated_bundles:
            # compose an email notifying the team with changes
            subject = f"List of Freed Profiles and Activated Bundles"

            freed_profiles_text = ",\n".join(freed_profiles)
            reactivated_bundles_text = ",\n".join(reactivated_bundles)

            body = f"""
                Freed Profiles: 
                ICCID: {freed_profiles_text}
                ------------------------------------------------------
                Re-Activated Bundles 
                Bundles: {reactivated_bundles_text}
            """
            logger.debug("activated bundles %s", reactivated_bundles)
            send_custom_monitor_email(subject, body)
            logger.info("Freed profiles and sent email")
    except Exception as e:
        logger.error(e)
        return



def flexi_expire_profiles():
    try:
        iccid_list = get_all_used_iccid()

        if iccid_list:
            for user_iccid in iccid_list:
                user_bundle = db_helper.get_paid_user_bundle(user_iccid.bundle_code, user_iccid.email)

                if user_bundle and user_bundle.validy_date < datetime.today():
                    user_iccid.status = "expired"
                    user_iccid.save()
                    db_helper.expire_profile(user_iccid.iccid)
    except Exception as e:
        print("exception flexi_expire_profiles", str(e))


def expire_esimgo_bundles():
    headers = {}
    payload = {
        "alertType": "Utilisation",
        "iccid": None,
        "bundle": {"initialQuantity": 100, "remainingQuantity": 0, "endTime": None},
    }
    esimgo_profile_list: list = Profiles.objects(vendor_name=ESIM_GO_VENDOR, availability="Assigned").distinct("iccid")
    utcnow = datetime.datetime.utcnow()

    orders = Order_history.objects(
        order_status="Successful",
        expiry_date__lte=utcnow,
        iccid__in=esimgo_profile_list,
        plan_status__ne="Expired",
    )

    logging.info("Fixing order data for %s orders", len(orders))
    for order in orders:
        formatted_time = utcnow.strftime("%Y-%m-%dT%H:%M:%SZ")
        this_payload = copy.deepcopy(payload)
        this_payload.update({"iccid": order.iccid, "endTime": formatted_time})
        signature = create_esimgo_signature(json.dumps(this_payload), instance_config.esim_go_token)
        these_headers = headers.copy()
        these_headers.update({"X-Signature-Sha256": signature})
        resp = requests.post(
            url=instance_config.esimgo_webhook_url,
            json=this_payload,
            headers=these_headers,
        )
        if resp.status_code != 200:
            logging.info(
                "error happened when sending expiry notification for iccid %s",
                order.iccid,
            )


def expire_bundles():
    # Send expiry notification for esimgo  order bundles if it reached expiry_date.
    expire_esimgo_bundles()

    date_now = datetime.datetime.now()

    # Update subscriber order if it reached expiry date.
    Order_history.objects(
        order_status="Successful",
        expiry_date__lte=date_now,
        plan_status__ne="Expired",
        reseller_type="subscriber",
    ).update(set__plan_status="Expired")

    # Update reseller order if it reached expiry date.
    expired_reseller_order_histories = Order_history.objects(
        order_status="Successful",
        expiry_date__lte=date_now,
        plan_status__ne="Expired",
        reseller_type="reseller",
    )
    expired_reseller_order_histories.update(set__plan_status="Expired")

    thread = threading.Thread(
        target=send_multiple_reseller_push_notifications,
        args=(expired_reseller_order_histories,),
    )
    thread.start()


def send_multiple_reseller_push_notifications(expired_reseller_order_histories):
    reseller_helper = ResellerHelper(instance_config=instance_config)
    for order_history in expired_reseller_order_histories:
        reseller_helper.send_reseller_push_notification(
            order_history.iccid,
            str(order_history.id),
            "Expired",
            str(order_history.reseller_id.id),
        )


def expire_scripts():
    flexi_save_token()
    date_now = datetime.datetime.now()
    db_helper.reset_email_verification(date_now)
    db_helper.reset_password_request(date_now)
    db_helper.reset_contact_us(date_now)


def send_user_bundle_history(date1, date2):
    user_bundle_hist = db_helper.get_user_history_bundle_pipeline(date1, date2)
    for bundle in user_bundle_hist:
        if "bundle_code" in bundle:
            found_history = db_helper.check_history(
                bundle["payment_date"],
                bundle["email"],
                bundle["iccid"],
                bundle["bundle_code"],
            )
            if not found_history:
                coverage = "global"
                if bundle["bundle_category"] == "region":
                    coverage = bundle["region_name"]
                elif bundle["bundle_category"] == "country":
                    if len(bundle["country_list"]) > 0:
                        coverage = bundle["country_list"][0]
                order_number = "order" + generate_temp_otp(12)
                qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
                    instance_config.decrypted_backend_url,
                    bundle["matching_id"],
                    bundle["smdp_address"],
                    bundle["has_lpa"],
                )
                history_log = {
                    "history_log_id": "hist_" + generate_temp_otp(12),
                    "datetime": bundle["payment_date"],
                    "email": bundle["email"],
                    "iccid": bundle["iccid"],
                    "bundle_name": bundle["bundle_name"],
                    "bundle_marketing_name": bundle["bundle_marketing_name"],
                    "coverage": coverage,
                    "price": bundle["amount"],
                    "currency_code": bundle["currency_code"],
                    "bundle_code": bundle["bundle_code"],
                    "data_amount": bundle["data_amount"],
                    "data_unit": bundle["data_unit"],
                    "bundle_duration": bundle["bundle_duration"],
                    "order_number": order_number,
                    "smdp_address": bundle["smdp_address"],
                    "activation_code": bundle["activation_code"],
                    "qr_code_link": qr_code_link,
                    "sent_using": "Email",
                    "transaction": "BuyBundle",
                }
                save_history_logs(history_log)


def send_order_history_topup(date1, date2):
    try:
        user_topup_pipeline = db_helper.get_user_topup_history_log_pipeline(date1, date2)

        for user in user_topup_pipeline:
            if "bundle_code" in user:
                found_history = db_helper.check_history(
                    user["datetime"],
                    user["email"],
                    user["iccid"],
                    user["bundle_code"],
                    user["topup_code"],
                )
                if not found_history:
                    coverage = "global"
                    if user["bundle_category"] == "region":
                        coverage = user["region_name"]
                    elif user["bundle_category"] == "country":
                        if len(user["country_list"]) > 0:
                            coverage = user["country_list"][0]
                    qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
                        instance_config.decrypted_backend_url,
                        user["matching_id"],
                        user["smdp_address"],
                        user["has_lpa"],
                    )
                    user_bundle_values = {
                        "history_log_id": "hist_" + generate_temp_otp(12),
                        "datetime": user["datetime"],
                        "iccid": user["iccid"],
                        "email": user["email"],
                        "amount": user["amount"],
                        "bundle_marketing_name": user["bundle_marketing_name"],
                        "bundle_name": user["bundle_name"],
                        "bundle_code": user["bundle_code"],
                        "topup_code": user["topup_code"],
                        "order_number": user["order_number"],
                        "bundle_duration": user["bundle_duration"],
                        "data_amount": user["data_amount"],
                        "data_unit": user["data_unit"],
                        "coverage": coverage,
                        "smdp_address": user["smdp_address"],
                        "activation_code": user["activation_code"],
                        "currency_code": user["currency_code"],
                        "otp": user["otp"],
                        "qr_code_link": qr_code_link,
                        "transaction": user["transaction"],
                        "sent_using": user["sent_using"],
                        "transaction_status": user["transaction_status"],
                    }
                    save_history_logs(user_bundle_values)

    except Exception as e:
        print("exception send_order_history_topup ", str(e))

def customer_feedback_send_email(testing=False):
    from app_main import app
    with app.app_context():
        query = {"email__ne": "", "customer_feedback_sent": False}
        current_time = datetime.datetime.now()
        query["customer_feedback_date__lte"] = current_time - datetime.timedelta(days=1)
        if testing:
            query["email"] = "<EMAIL>"
        notifications = NotificationLogs.objects(**query).limit(50)
        for notification in notifications:
            send_email(str(notification.email))
            notification.customer_feedback_sent = True
            notification.save()
        print("successful send email")


def add_missing_keys(email_template_keys, email_json):
    for key in email_template_keys:

        # Check if the key exists in any dictionary in A
        key_exists = any(key in d for d in email_json)
        # If the key doesn't exist in any dictionary, add it with an empty string value
        if not key_exists:
            email_json[key] = ""
    return email_json


def send_email(user_email):
    email_setting = EmailSettings.objects().first()
    setting = Settings.objects().first()
    smtp_port = 587
    sender_email = email_setting.email
    sender_password = email_setting.password
    recipient_email = user_email
    token = encrypt_data(recipient_email)
    url = instance_config.FEEDBACK_URL + str(token)
    user = mobiles.AppUserDetails.objects(user_email=user_email).first()
    data = {}
    language = user.language
    if user.first_name != "":
        data["user"] = user.first_name.capitalize()
    msg = MIMEMultipart("alternative")

    try:
        template_mapping = instance_config.feedback_mapping
        email_feedback_keys = ["subject", "text", "contact_us"]
        translation = template_mapping.get(language, template_mapping["en"])
        translation = add_missing_keys(email_feedback_keys, translation)
        dir_language = consumer_models.Languages.objects(language_code=language).first()
        if not dir_language["is_ltr"]:
            msg["Subject"] = Header(translation.get("subject"), "utf-8")
            html_template = render_template(
                "monty_eSIM_Request_for_Feedback_rtl.html",
                data=data,
                url=url,
                translation=translation,
                montyesim_msisdn=setting.whatsapp_misisdn,
            )
            msg.attach(MIMEText(html_template, "html"))
        else:
            msg["Subject"] = Header(translation.get("subject"), "utf-8")
            html_template = render_template(
                "monty_eSIM_Request_for_Feedback.html",
                url=url,
                data=data,
                translation=translation,
                montyesim_msisdn=setting.whatsapp_misisdn,
            )

            msg.attach(MIMEText(html_template, "html"))

    except Exception as e:
        print("Exception in send_email as", e)

    msg["From"] = sender_email
    msg["To"] = recipient_email
    smtp_server = smtplib.SMTP(email_setting.smtp_server, smtp_port)
    smtp_server.starttls()
    smtp_server.login(sender_email, sender_password)

    smtp_server.sendmail(sender_email, recipient_email, msg.as_string())
    smtp_server.quit()
    return "Email sent successfully!"


def check_profile_expiry():
    # Expire all profiles whose expiry_date is less than or equal to the current timestamp
    threshold = datetime.datetime.now()
    db_helper.expire_profiles(threshold)


def notify_profile_before_expiry(testing=False):
    setting = db_helper.get_setting()
    duration = setting.notify_duration
    now = datetime.datetime.utcnow()
    pipeline = [
        {"$addFields": {"daysUntilDate": {"$divide": [{"$subtract": ["$expiry_date", now]}, 86400000]}}},
        {"$match": {"$expr": {"$lte": ["$daysUntilDate", duration]}}},
        {"$match": {"daysUntilDate": {"$nin": ["", None]}}},
        {
            "$lookup": {
                "from": "order_history",
                "let": {"iccid": "$iccid"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$iccid", "$$iccid"]},
                                    {"$eq": ["$reseller_type", "subscriber"]},
                                    {"$eq": ["$order_status", "Successful"]},
                                ]
                            }
                        }
                    }
                ],
                "as": "result",
            }
        },
        {"$unwind": {"path": "$result"}},
        {
            "$group": {
                "_id": {"bundle_code": "$bundle_code", "iccid": "$iccid"},
                "iccid": {"$first": "$iccid"},
                "email": {"$first": "$result.client_email"},
                "bundle_name": {"$first": "$result.bundle_data.bundle_name"},
                "bundle_code": {"$first": "$result.bundle_data.bundle_code"},
            }
        },
    ]
    if testing:
        query_set = Q(iccid="23209591494665390114")
    else:
        query_set = Q(availability="Assigned") & Q(expiry_date__nin=["", None]) & Q(expiry_notified__ne=True)
    bundle_result = list(consumer_models.Profiles.objects(query_set).aggregate(pipeline))
    bulk_updates = []
    for doc in bundle_result:
        bulk_updates.append(UpdateOne({"iccid": doc["iccid"]}, {"$set": {"expiry_notified": True}}))
    if bulk_updates:
        consumer_models.Profiles.objects._collection.bulk_write(bulk_updates)
    notification_logs = []
    for doc in bundle_result:
        label = consumer_models.Labels.objects(iccid=doc["iccid"], client_email=doc["email"]).first()
        if label:
            bundle_label_name = label["label_name"]
        else:
            bundle_label_name = doc["bundle_name"]
        notification_logs.append(
            NotificationLogs(
                **{
                    "notification_id": "notf_" + str(generate_temp_otp(12)),
                    "datetime": datetime.datetime.utcnow(),
                    "email": doc["email"],
                    "iccid": doc["iccid"],
                    "transaction_message": f"Attention! Your eSIM {bundle_label_name} is expiring soon. Top up now to avoid service interruption.",
                    "transaction": "ExpiryBundle",
                    "transaction_status": False,
                    "bundle_code": doc["bundle_code"],
                    "reseller_type": "subscriber"
                }
            )
        )
        if notification_logs:
            NotificationLogs.objects.insert(notification_logs)

        print("Notify Successed")


def esimgo_save_profiles(bundle_code=None, daily_used=0):
    if not enough_vendor_balance(vendor_name=ESIM_GO_VENDOR):
        return 0, f"Insufficient balance for vendor {ESIM_GO_VENDOR}"

    original_daily_used = daily_used
    message = ""
    vendor_info = get_vendor_info(ESIM_GO_VENDOR)

    if not vendor_info.is_active:
        return 0, "Vendor is not active"

    profile_added = 0
    bundles = db_helper.get_bundles_by_vendor(
        ESIM_GO_VENDOR,
        is_active=True,
        deleted=False,
        bundle_code=bundle_code,
        allocate_profiles=True,
    )
    for bundle in bundles:
        profile_names = db_helper.remove_space_profiles(bundle.profile_names)
        if bundle.maximum_profiles_number == -1 or (
            bundle.maximum_profiles_number != -1
            and Profiles.objects(
                vendor_name=ESIM_GO_VENDOR,
                availability="Free",
                status=True,
                profile_names=profile_names,
                bundle_code=bundle.bundle_code,
            ).count()
            < int(bundle.maximum_profiles_number)
        ):
            if daily_used == 0:
                daily_used = bundle.daily_used
            if daily_used == 0 and not Profiles.objects(vendor_name=ESIM_GO_VENDOR, bundle_code=bundle.bundle_code):
                daily_used = instance_config.nb_profiles

            if not enough_organization_balance(bundle):
                logging.info(f"balance is less than limit eSIMGo, vendor balance is {get_esimgo_balance()}")
                print("balance is less than limit eSIMGo")
                subject = "LOW ESIMGO BALANCE"
                body = (
                    f"[{datetime.datetime.utcnow()}] detected low balance while allocating profiles, "
                    f"Low balance for vendor {bundle.vendor_name}, balance is {get_esimgo_balance()}"
                )
                send_custom_monitor_email(subject, body)
                return 0, "No balance enough to get profiles"

            print("bundle.bundle_code ", bundle.bundle_code, "daily used ", daily_used)

            if bundle.maximum_profiles_number != -1 and daily_used > bundle.maximum_profiles_number:
                daily_used = bundle.maximum_profiles_number

            profile_added = save_esimgo_profiles(
                bundle.bundle_code,
                daily_used,
                bundle.bundle_vendor_code,
                original_daily_used,
            )
            if not bundle_code:
                bundle.update(set__previous_daily_used=bundle.daily_used, set__daily_used=0)
                bundle.reload()
            daily_used = original_daily_used
            print("profile added mafroud ", profile_added)

        else:
            message = f"eSIMGo {bundle.bundle_code} has sufficient profiles. Reached the maximum number of profiles"
            print(message)
            if not bundle.is_active:
                bundle.update(set__is_active=True)
                add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])

    if not bundles:
        message = "No bundles found to be allocated"
        print(message)
    if not bundle_code:
        bundles = db_helper.get_bundles_by_vendor(ESIM_GO_VENDOR)
        bundles.update(set__daily_used=0)
    return profile_added, message


def save_esimgo_profiles(bundle_code, daily_used, bundle_vendor_code, original_daily_used=0):
    try:
        profile_added = 0
        esimgo_helper = ESIMGo()
        number_profiles_free = Profiles.objects(
            Q(vendor_name=ESIM_GO_VENDOR) & Q(availability="Free") & Q(status=True) & Q(plan_uid__ne="") & Q(bundle_code=bundle_code)
        ).count()
        expiry_date = datetime.datetime.now() + relativedelta(days=365)
        if daily_used <= number_profiles_free or daily_used <= 0:
            return
        profile_to_add = daily_used - number_profiles_free
        for i in range(profile_to_add):
            esimgo_create_order = esimgo_helper.assign_profiles(bundle_vendor_code=bundle_vendor_code)
            order_reference = esimgo_create_order.get("orderReference")
            profile_info = esimgo_create_order.get("order", [{}])[0].get("esims", [{}])[0]
            iccid, matching_id, smdp_address = (
                profile_info.get("iccid"),
                profile_info.get("matchingId"),
                profile_info.get("smdpAddress"),
            )
            qr_code_value = f"LPA:1${smdp_address}${matching_id}"
            unit_price = esimgo_create_order.get("total", 0)
            Profiles(
                **{
                    "vendor_name": "eSIMGo",
                    "iccid": iccid,
                    "sku": iccid,
                    "status": True,
                    "smdp_address": smdp_address,
                    "matching_id": matching_id,
                    "qr_code_value": qr_code_value,
                    "plan_uid": order_reference,
                    "bundle_code": bundle_code,
                    "expiry_date": expiry_date,
                    "profile_names": "",
                    "unit_price": unit_price,
                }
            ).save()
            db_helper.ignore_runnable_script(bundle_code, original_daily_used)
            Bundles.objects(bundle_code=bundle_code).update(inc__allocated_unit=1, is_active=True)
            profile_added = profile_added + 1
        if original_daily_used == 0:
            db_helper.run_runnable_scripts(
                script="AllocateProfiles",
                vendor_name="eSIMGo",
                bundle_code=bundle_code,
                daily_used=daily_used,
                profile_added=profile_added,
                state="Finished",
            )
        return profile_added
    except Exception as e:
        logger.exception(f"<save_esimgo_profiles> error: {e} when allocating new profile for  {bundle_code} ")
        return 0


def get_vendor_info(vendor_name):
    vendor = consumer_models.Vendors.objects(vendor_name=vendor_name).first()
    return vendor


def get_sku_from_iccid(iccid):
    profile = consumer_models.Profiles.objects(iccid=str(iccid)).first()
    if profile:
        return profile.sku
    return None


def allocate_flexiroam_bundles(instance_config, plan_code, sku):
    try:
        bundles = False
        access_token = get_vendor_info(FLEXIROAM_VENDOR).temp_token
        if access_token:

            headers = {
                "token": access_token,
                "Content-Type": "application/x-www-form-urlencoded",
            }

            payload = f"sku={sku}&plan_code={plan_code}&plan_start_type_id=1&discount="

            response = requests.post(
                "{}/user/order/sim/plan/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=payload,
                timeout=125.5,
            )

            bundles = response.json()
            print("status code ", response.status_code)

            if response.status_code != 200:
                res = response.json()
                exception = res["message"]
                body = (
                    f"[{datetime.datetime.now()}] flexi allocatiobn error"
                    f"for vendor {FLEXIROAM_VENDOR} for plan code {plan_code} for sku {sku} --- contact devs: exception: {exception}"
                )
                send_custom_monitor_email(
                    subject="------URGENT------ ALLOCATION FLEXI BUNDLES ERROR:",
                    body=body,
                )
                return False, body
            elif response.status_code == 200:
                res = response.json()
                return True, res

    except Exception as e:
        logger.exception(f"<allocate_flexiroam_bundles> error: {e} when allocating the sku {sku} to bundle code {plan_code} ")
        return False, e


def allocate_montymobile_bundles(plan_code, sku):
    try:
        monty_mobile = MontyMobile()

        return monty_mobile.load_bundle_to_profile(iccid=sku, sku=sku, bundle_vendor_code=plan_code)

    except Exception as e:
        logger.exception(f"<allocate_flexiroam_bundles> error: {e} ")
        return {}, e


def flexi_pre_allocate_profile_check(number_of_profiles, plan_code):
    try:
        access_token = get_vendor_info(FLEXIROAM_VENDOR).temp_token
        if access_token:
            headers = {
                "token": access_token,
                "Content-Type": "application/x-www-form-urlencoded",
            }

            payload = {
                "availability": 0,
                "sim_type": "eSIM",
                "limit": number_of_profiles,
                "plan_code": plan_code,
            }

            r = requests.post(
                "{}/product/inventory/available/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=payload,
                timeout=25,
            )

            if r.status_code == 200:
                response = r.json()
                if response:
                    return response["data"]

    except Exception as e:
        return []


def flexi_allocate_profiles(bundle_code=None, daily_used=0):
    profile_added = 0
    try:
        message = ""
        flexi_helper = Flexiroam()
        original_daily_used = daily_used
        vendor_info = get_vendor_info(FLEXIROAM_VENDOR)
        if not vendor_info.is_active:
            return 0, "Vendor is not active"
        bundles = db_helper.get_bundles_by_vendor(
            FLEXIROAM_VENDOR, is_active=True, deleted=False, bundle_code=bundle_code, allocate_profiles=True
        )

        for bundle in bundles:
            profile_names = db_helper.remove_space_profiles(bundle.profile_names)

            if bundle.maximum_profiles_number == -1 or (
                bundle.maximum_profiles_number != -1
                and Profiles.objects(
                    vendor_name=FLEXIROAM_VENDOR,
                    availability="Free",
                    status=True,
                    profile_names=profile_names,
                    bundle_code=bundle.bundle_code,
                ).count()
                < int(bundle.maximum_profiles_number)
            ):
                if daily_used == 0:
                    daily_used = bundle.daily_used
                if daily_used == 0:
                    if Profiles.objects(vendor_name=FLEXIROAM_VENDOR, bundle_code=bundle.bundle_code).count() == 0:
                        daily_used = instance_config.nb_profiles
                if daily_used == 0:
                    continue

                if bundle.maximum_profiles_number != -1 and daily_used > bundle.maximum_profiles_number:
                    print("daily_used > bundle.maximum_profiles_number")
                    daily_used = bundle.maximum_profiles_number

                number_profiles_free = Profiles.objects(
                    Q(vendor_name=FLEXIROAM_VENDOR)
                    & Q(availability="Free")
                    & Q(status=True)
                    & Q(plan_uid__ne="")
                    & Q(bundle_code=bundle.bundle_code)
                    & Q(profile_names=bundle.profile_names)
                    & Q(expiry_date__gte=datetime.datetime.utcnow())
                ).count()

                if daily_used > number_profiles_free and daily_used > 0:
                    profile_to_add = daily_used - number_profiles_free
                    try:
                        # for profile in profiles:
                        profiles = flexi_helper.get_next_available_profiles(
                            plan_code=bundle.bundle_vendor_code,
                            number_of_profiles=profile_to_add,
                        )

                        for item in profiles.get("data", []):
                            sku = item.get("sku")
                            profile = Profiles.objects(
                                vendor_name=FLEXIROAM_VENDOR,
                                sku=sku,
                                availability="Free",
                                status=False,
                                allocate_date=None,
                                profile_names=profile_names,
                            ).first()

                            allocation, message = flexi_helper.load_bundle_to_profile(
                                sku=profile.sku,
                                iccid=profile.iccid,
                                bundle_vendor_code=bundle.bundle_vendor_code,
                            )

                            if allocation:
                                profile.update(
                                    **{
                                        "bundle_code": bundle.bundle_code,
                                        "allocate_date": datetime.datetime.now(),
                                        "status": True,
                                        "plan_uid": allocation.get("data", {}).get("plan_uid"),
                                        "expiry_date": datetime.datetime.now() + relativedelta(months=4),
                                    }
                                )
                                profile_added += 1
                                bundle.update(inc__allocated_unit=1, is_active=True)
                                db_helper.ignore_runnable_script(bundle_code, original_daily_used)

                            else:
                                print(f"Error while allocating bundle {bundle.bundle_code} to sku {profile.sku} {message}")
                                db_helper.run_runnable_scripts(
                                    script="AllocateProfiles",
                                    vendor_name="Flexiroam",
                                    bundle_code=bundle.bundle_code,
                                    daily_used=original_daily_used,
                                )

                    except Exception as e:
                        print("Error while allocate flexiroam profiles ", e)
                        return 0, "Error while allocate flexiroam profiles"
                if original_daily_used == 0:
                    db_helper.run_runnable_scripts(
                        script="AllocateProfiles",
                        vendor_name="Flexiroam",
                        bundle_code=bundle.bundle_code,
                        daily_used=daily_used,
                        profile_added=profile_added,
                        state="Finished",
                        failure_reason=message,
                    )

            else:
                print(f"{bundle.bundle_code} Flexiroam didnt need profiles. Reached maximum number of profiles")
                message = f"{bundle.bundle_code} Flexiroam didnt need profiles. Reached maximum number of profiles"
            if not bundle.is_active:
                bundle.update(set__is_active=True)
                add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])
            if bundle_code is None:
                bundles.update(set__daily_used=0, set__previous_daily_used=bundle.daily_used)
        if not bundles:
            print("No flexiroam bundles for the specific conditions")

    except Exception as e:
        logger.exception(f"<flexi_allocate_profiles> error: {e}")
        return 0, allocation_exception
    return profile_added, message


def calculate_profiles_needed(bundle_code, skip_initialized=False):
    bundle = consumer_models.Bundles.objects(bundle_code=bundle_code).first()
    if skip_initialized and bundle.initialized:
        return None, None
    country_code_list = bundle.country_code_list
    bundle_duration = bundle.bundle_duration
    data_amount = bundle.data_amount
    one_day_ago = datetime.datetime.utcnow() - datetime.timedelta(days=1)
    pipeline = [
        {
            "$match": {
                "reseller_type": "subscriber",
                "order_status": "Successful",
                "date_created": {"$gte": one_day_ago},
                "bundle_data.data_amount": data_amount,
                "bundle_data.bundle_duration": bundle_duration,
                "searched_code_list": {"$ne": []},
                "$expr": {"$setIsSubset": ["$searched_code_list", country_code_list]},
            }
        },
        {
            "$group": {
                "_id": {
                    "searched_code_list": "$searched_code_list",
                    "bundle_duration": "$bundle_data.bundle_duration",
                    "data_amount": "$bundle_data.data_amount",
                },
                "demand_count": {"$sum": 1},
                "searched_code_list": {"$first": "$searched_code_list"},
                "bundle_duration": {"$first": "$bundle_data.bundle_duration"},
                "data_amount": {"$first": "$bundle_data.data_amount"},
            }
        },
    ]

    past_24_hour_orders = Order_history._get_collection().aggregate(pipeline)
    profile_amount_to_be_added = 0

    for order_set in past_24_hour_orders:
        past_24_hour_demand = order_set.get("demand_count", 0)
        profile_amount_to_be_added += past_24_hour_demand

    supply_and_demand_record = consumer_models.BundlesSupplyAndDemand.objects(
        country_code_list=country_code_list,
        data_amount=data_amount,
        bundle_duration=bundle_duration,
        update_at__gte=one_day_ago,
    ).first()

    supplied_count = 0

    if supply_and_demand_record:
        supplied_count = supply_and_demand_record.supply_count
        supply_and_demand_record.update(
            set__demand_count=profile_amount_to_be_added,
            set__update_at=datetime.datetime.utcnow(),
        )
        supply_and_demand_record.reload()

    else:
        new_supply_and_demand_record = consumer_models.BundlesSupplyAndDemand(
            country_code_list=country_code_list,
            data_amount=data_amount,
            bundle_duration=bundle_duration,
            demand_count=profile_amount_to_be_added,
        )
        new_supply_and_demand_record.save()

    profile_amount_to_be_added = profile_amount_to_be_added - supplied_count

    if profile_amount_to_be_added < 5:
        profile_amount_to_be_added = 5

    return profile_amount_to_be_added, bundle


def montymobile_allocate_profiles(bundle_code=None, daily_used=0):
    try:
        profile_added = 0
        allocation_exception = ""
        vendor_info = get_vendor_info(MONTY_MOBILE_VENDOR)
        if vendor_info.is_active and vendor_info.apply_inventory:
            bundles = db_helper.get_bundles_by_vendor(
                MONTY_MOBILE_VENDOR,
                is_active=True,
                deleted=False,
                bundle_code=bundle_code,
            )
            print("bundle_code ", bundle_code)
            if bundles:
                for bundle in bundles:
                    profile_names = db_helper.remove_space_profiles(bundle.profile_names)

                    if bundle.maximum_profiles_number == -1 or (
                        bundle.maximum_profiles_number != -1
                        and consumer_models.Profiles.objects(
                            vendor_name=MONTY_MOBILE_VENDOR,
                            availability="Free",
                            status=True,
                            allocate_date=None,
                            profile_names=profile_names,
                            bundle_code=bundle.bundle_code,
                        ).count()
                        < int(bundle.maximum_profiles_number)
                    ):
                        if daily_used == 0:
                            daily_used = bundle.daily_used
                        if daily_used == 0:
                            if (
                                    consumer_models.Profiles.objects(
                                        Q(vendor_name=MONTY_MOBILE_VENDOR) & Q(bundle_code=bundle.bundle_code)
                                    ).count()
                                    == 0
                            ):
                                daily_used = instance_config.nb_profiles
                        if daily_used == 0:
                            continue

                        if bundle.maximum_profiles_number != -1 and daily_used > bundle.maximum_profiles_number:
                            daily_used = bundle.maximum_profiles_number
                        print("daily_used ", daily_used)
                        profiles = consumer_models.Profiles.objects(
                            vendor_name=MONTY_MOBILE_VENDOR,
                            availability="Free",
                            status=False,
                            allocate_date=None,
                            profile_names=profile_names,
                        ).limit(daily_used)

                        for profile in profiles:
                            sku = profile.sku
                            print("sku ", sku)
                            (
                                allocation,
                                allocation_status,
                            ) = allocate_montymobile_bundles(bundle.bundle_vendor_code, sku)
                            print(
                                "allocation, allocation_status ",
                                allocation,
                                allocation_status,
                            )
                            if allocation_status:
                                profile_added += 1
                                print(f"allocation {allocation}")
                                profile.update(
                                    **{
                                        "bundle_code": bundle.bundle_code,
                                        "allocate_date": datetime.datetime.now(),
                                        "status": True,
                                        "plan_uid": allocation["data"]["simplan"]["plan_uid"],
                                        "expiry_date": datetime.datetime.now() + relativedelta(months=12),
                                    }
                                )
                                bundle.update(inc__allocated_unit=1, is_active=True)

                            else:
                                print("Allocation didnt happened", allocation)
                    else:
                        print(
                            f"The bundle {bundle.bundle_code} in Monty Mobile didnt need profiles. We have reached the maximum number of profiles"
                        )
                        if bundle.is_active == False:
                            bundle.update(set__is_active=True)
                            add_to_update_bundle_version([f"{message_update_bundle_version}:{bundle.bundle_code}"])

            if bundle_code is None:
                bundles.update(set__daily_used=0, set__previous_daily_used=daily_used)
    except Exception as e:
        allocation_exception = str(e)
        logger.exception(f"<montymobile_allocate_profiles> error: {allocation_exception}")

        return False, str(e)
    return profile_added, allocation_exception


def indosat_allocate_profiles(destination_bundle_code, source_bundle_code=None, amount=0):
    """
    destination_bundle_code: Bundle we need profiles for
    source_bundle_code: Bundle we can move profiles from
    amount: amount to reallocate
    """
    # Step1 Check if source_bundle_code and amount exist
    profile_added = 0
    profiles_safety_limit = 5
    iccids_list = []
    successful_iccids_changed = []
    modified_bundles = {}
    message = ""
    indosat_helper = Indosat()
    destination_bundle = Bundles.objects(bundle_code=destination_bundle_code).first()
    destination_bundle_vendor_code = destination_bundle.bundle_vendor_code
    if source_bundle_code and amount:
        profiles = Profiles.objects(
            bundle_code=source_bundle_code,
            availability="Free",
            status=True,
            expiry_date__gte=datetime.datetime.utcnow(),
        ).limit(amount)
    else:
        if not amount:
            amount = destination_bundle.daily_used
        amount_counter = amount
        pipeline = [
            {
                "$match": {
                    "vendor_name": "Indosat",
                    "availability": "Free",
                    "status": True,
                    "bundle_code": {"$ne": destination_bundle_code},
                    "$expr": {
                        "$or": [
                            {"$gte": ["$expiry_date", datetime.datetime.utcnow()]},
                            {"$eq": ["$expiry_date", None]},
                        ]
                    },
                }
            },
            {
                "$group": {
                    "_id": "$bundle_code",
                    "iccids": {"$addToSet": "$iccid"},
                    "count": {"$sum": 1},
                    "bundle_code": {"$first": "$bundle_code"},
                }
            },
            {"$match": {"$expr": {"$gt": [{"$size": "$iccids"}, profiles_safety_limit]}}},
            {"$sort": {"count": -1}},
        ]
        profiles = Profiles._get_collection().aggregate(pipeline)
        profiles = list(profiles)
        available_iccids = []
        for profile in profiles:
            while profile["count"] > profiles_safety_limit and amount_counter > 0:
                # Remove an ICCID from the current dictionary
                iccid = profile["iccids"].pop()
                available_iccids.append(iccid)

                # Update the count in the dictionary
                profile["count"] -= 1
                amount_counter -= 1

                if amount_counter == 0:
                    break
        profiles = Profiles.objects(iccid__in=available_iccids)
    for profile in profiles:
        profile.status = False
        modified_bundles[profile.bundle_code] = modified_bundles.get(profile.bundle_code, 0) + 1
        iccids_list.append(profile.iccid)
        profile.save()

    for bundle_code, count in modified_bundles.items():
        bundle = Bundles.objects(bundle_code=bundle_code).first()
        if (bundle.allocated_unit - count) <= bundle.consumed_unit:
            bundle.update(is_active=False)
        bundle.update(dec__allocated_unit=count)

    for iccid in iccids_list:
        response = indosat_helper.edit_profile_details(iccid=iccid, ratePlan=destination_bundle_vendor_code)
        if response:
            successful_iccids_changed.append(iccid)
            profile_added = profile_added + 1

    successfully_changed_profiles = Profiles.objects(iccid__in=successful_iccids_changed)
    added_profiles_count = len(successful_iccids_changed)
    for profile in successfully_changed_profiles:
        profile.status = True
        profile.bundle_code = destination_bundle_code
        profile.save()
    if successfully_changed_profiles:
        Bundles.objects(bundle_code=destination_bundle_code).update(inc__allocated_unit=added_profiles_count, is_active=True)
    remaining_profiles_needed = amount - added_profiles_count
    if remaining_profiles_needed > 0:
        send_custom_monitor_email(
            subject="---URGENT---Short On ICCIDS for Indosat",
            body=f"Insufficient ICCIDS for Indosat bundle: {destination_bundle_code}, Only added {added_profiles_count} profiles but still need {remaining_profiles_needed}",
        )
    return profile_added, message


def revoke_and_refill_profile(profile: Profiles):
    try:
        RETRY_COUNT = 2
        temporary_token = Vendors.objects(vendor_name=FLEXIROAM_VENDOR).first().temp_token
        headers = {"token": temporary_token}

        sku = profile.sku
        plan_uid = profile.plan_uid
        bundle_code = profile.bundle_code
        bundle: Bundles = Bundles.objects(bundle_code=bundle_code).first()
        bundle_vendor_code = bundle.bundle_vendor_code
        #   Revoke the plan from the profile
        revoke_url = f"{instance_config.flexiroam_url}/plan/unload/v1"
        revoke_payload = {"plan_uid": plan_uid}
        revoke_response = requests.post(url=revoke_url, headers=headers, json=revoke_payload)
        i = 0
        while revoke_response.status_code != 200 and i < RETRY_COUNT:
            revoke_response = requests.post(url=revoke_url, headers=headers, json=revoke_payload)
            i += 1
        if revoke_response.status_code != 200:
            raise ValueError(f"Error revoking after retrying {RETRY_COUNT} times")

        #   refill the profile again with the same plan
        refill_url = f"{instance_config.flexiroam_url}/plan/load/v1"
        refill_payload = {
            "sku": f"[{int(sku)}]",
            "plan_code": bundle_vendor_code,
            "plan_start_type_id": "1",
        }
        refill_response = requests.post(url=refill_url, headers=headers, data=refill_payload)
        i = 0
        while refill_response.status_code != 200 and i < RETRY_COUNT:
            refill_response = requests.post(url=refill_url, headers=headers, json=refill_payload)
            i += 1
        if refill_response.status_code != 200:
            raise ValueError(f"Error refilling after retrying {RETRY_COUNT} times")

        #   Update the profile with the new values
        new_plan_uid = refill_response.json()["data"]["plan_uid"]
        new_expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=120)
        profile.update(plan_uid=new_plan_uid, expiry_date=new_expiry_date)
        print(f"[{datetime.datetime.utcnow()}] profile {sku} successfully refilled")

    except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
    ) as error:
        print(f"<revoke_and_refill_profile> remote error happened: {error} while revoking/refilling bundle")
        subject = f"------URGENT------ Vendor: Flexiroam remote script error"
        body = f"[{datetime.datetime.utcnow()} {error} happened while revoking/refilling bundle"
        send_custom_monitor_email(subject, body)
        send_custom_dev_email(subject, body)

    except Exception as error:
        print(f"<revoke_and_refill_profile> error happened: {error} while revoking/refilling bundle")
        subject, body = (
            f"------URGENT------ Vendor: Flexiroam exception script error",
            f"[{datetime.datetime.utcnow()} {error} occurred while trying to revoke bundle]",
        )
        send_custom_monitor_email(subject, body)
        send_custom_dev_email(subject, body)


def revoke_and_refill_profile_flexiroam_v2(profile: Profiles):
    """
    this approach is taken for flexiroam plan due to their policy which prevents profiles to be
    refunded except within the same month of their allocation. (CDR logs enforce this policy)
    """
    logging.info("starting revoke and refill profile process")
    flexi_helper = FlexiroamAPI()
    bundle: Bundles = Bundles.objects(bundle_code=profile.bundle_code).first()
    try:
        #   refund plan
        flexi_helper.refund_plan(purchased_plan_id=profile.plan_uid, sku=profile.sku)
        #   load the same profile with the refunded plan
        flexi_helper.buy_topup(plan_code=bundle.bundle_vendor_code, sku=profile.sku)
        profile_info = flexi_helper.get_sim_details(sku=profile.sku)
        active_plans = profile_info.get("active_plans")
        if not active_plans:
            raise Exception("plan was not loaded successfully")
        plan_uid = active_plans[-1].get("plan_uuid")
        string_expiry_date = active_plans[-1].get("expiration_date")
        expiry_date = datetime.datetime.strptime(profile_info.get("expiry_date"), "%Y-%m-%d")
        profile.update(plan_uid=plan_uid, expiry_date=expiry_date)
        logging.info(f"profile with sku %s successfully refilled", profile.sku)

    except (
        requests.exceptions.ConnectionError,
        requests.exceptions.Timeout,
        NewConnectionError,
    ) as error:
        logging.error(f"{error} while revoking/refilling bundle")
        subject = f"------URGENT------ Vendor: Flexiroam remote script error"
        body = f"[{datetime.datetime.utcnow()} {error} happened while revoking/refilling bundle"
        send_custom_monitor_email(subject, body)
        send_custom_dev_email(subject, body)

    except Exception as error:
        logging.error(f"{error} while revoking/refilling bundle")
        subject, body = (
            f"------URGENT------ Vendor: Flexiroam exception script error",
            f"[{datetime.datetime.utcnow()} {error} occurred while trying to revoke bundle]",
        )
        send_custom_monitor_email(subject, body)
        send_custom_dev_email(subject, body)


def refill_old_bundles_flexiroamv2():
    _BATCH_SIZE = 100
    vendor: Vendors = Vendors.objects(vendor_name="FlexiroamV2").first()
    if not vendor:
        logging.error("Vendor %s not found", FLEXIROAM_VENDOR)
        return False

    def loop_profile_batch(batch: List[Profiles]):
        for profile in batch:
            revoke_and_refill_profile_flexiroam_v2(profile)

    profiles = list(
        Profiles.objects(
            vendor_name=FLEXIROAM_VENDOR,
            availability="Free",
            plan_uid__nin=[None, ""],
            status=True,
            expiry_date__lte=datetime.datetime.utcnow() + datetime.timedelta(days=100),
        )
    )

    with ThreadPool(thread_name_prefix="revoke_and_refill") as pool:
        for i in range(0, len(profiles), _BATCH_SIZE):
            pool.submit(loop_profile_batch, profiles[i : i + _BATCH_SIZE])
    return True

def refill_old_bundles_flexiroam():
    _BATCH_SIZE = 100
    vendor: Vendors = Vendors.objects(vendor_name=FLEXIROAM_VENDOR).first()
    if not vendor:
        logging.error("Vendor %s not found", FLEXIROAM_VENDOR)
        return False

    def loop_profile_batch(batch: List[Profiles]):
        for profile in batch:
            revoke_and_refill_profile(profile)

    profiles = list(
        Profiles.objects(
            vendor_name=FLEXIROAM_VENDOR,
            availability="Free",
            plan_uid__nin=[None, ""],
            status=True,
            expiry_date__lte=datetime.datetime.utcnow() + datetime.timedelta(days=100),
        )
    )

    with ThreadPool(thread_name_prefix="revoke_and_refill") as pool:
        for i in range(0, len(profiles), _BATCH_SIZE):
            pool.submit(loop_profile_batch, profiles[i : i + _BATCH_SIZE])
    return True

def flexi_get_client_id():
    try:
        access_token = get_vendor_info(FLEXIROAM_VENDOR).temp_token
        if access_token:
            headers = {"token": access_token, "Content-Type": app_json}
            payload = {}

            response = requests.post(
                "{}/user/type/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=json.dumps(payload),
                timeout=125.5,
            )

            if response.status_code == 400:
                res = response.json()
                exception = res["message"]
                logger.exception(f"<flexi_get_client_id> error: {exception} ")

            elif response.status_code == 200:
                res = response.json()
                client_id = res["data"]["user_id"]
                return client_id

    except Exception as e:
        logger.exception(f"<flexi_get_client_id> error: {e} ")
        return False


def flexi_get_inventory_details(client_id):
    try:
        access_token = get_vendor_info(FLEXIROAM_VENDOR).temp_token
        if access_token:
            headers = {"token": access_token, "Content-Type": app_json}
            payload = {"client_id": client_id}

            response = requests.post(
                "{}/product/inventory/balance/v1".format(instance_config.flexiroam_url),
                headers=headers,
                data=json.dumps(payload),
                timeout=125.5,
            )

            if response.status_code == 400:
                res = response.json()
                exception = res["message"]
                logger.exception(f"<flexi_get_inventory_details> error: {exception} ")

            elif response.status_code == 200:
                res = response.json()
                return res

    except Exception as e:
        logger.exception(f"<flexi_get_inventory_details> error: {e} ")
        return False


def flexi_check_available_profiles():
    try:
        client_id = flexi_get_client_id()
        inventory_details = flexi_get_inventory_details(client_id)
        if inventory_details:
            profiles_data = []
            for data in inventory_details["data"]:
                profile_name = data["profile_name"]
                total_allocated = data["total_allocated"]
                total_used = data["total_used"]
                data_remaining = int(total_allocated) - int(total_used)

                if data_remaining < 200:
                    subject = f"------URGENT------ Vendor: Flexiroam Available Profiles"
                    body = f"[{datetime.datetime.utcnow()}] The remaining number of profiles for profile_name {profile_name} is {data_remaining} less than 200"
                    send_custom_monitor_email(subject, body)

                profile_data = {
                    "profile_name": profile_name,
                    "total_allocated": total_allocated,
                    "total_used": total_used,
                    "data_remaining": data_remaining,
                }

                profiles_data.append(profile_data)

            return profiles_data
        else:
            logger.error("Error in flexi_get_inventory_details")
            return False

    except Exception as e:
        logger.exception(f"<flexi_check_available_profiles> error: {e} ")
        return False


def get_vodafone_msisdn_by_iccid(iccid):
    try:
        url = f"{instance_config.vodafone_url}/network/things/iccids/{iccid}"
        headers = {"Authorization": f"Bearer {instance_config.vodafone_token}"}
        msisdn = f"NOT-FOUND-{iccid}"
        for _ in range(3):
            profile_response = requests.request("GET", url, headers=headers)
            if profile_response.status_code == 200:
                msisdn = profile_response.json()["thing"]["profiles"][0]["msisdn"]
                break
            time.sleep(2 * _)
        return msisdn
    except Exception as e:
        logger.exception(f"<get_vodafone_msisdn_by_iccid> error: {e} ")
        return False


def unload_all_flexi_profiles():
    affected_rows = 0
    success_rows = 0
    try:
        RETRY_COUNT = 2
        temporary_token = Vendors.objects(vendor_name=FLEXIROAM_VENDOR).first().temp_token
        headers = {"token": temporary_token}
        revoke_url = f"{instance_config.flexiroam_url}/plan/unload/v1"

        profiles = consumer_models.Profiles.objects(
            vendor_name=FLEXIROAM_VENDOR,
            availability="Free",
            status=True,
            plan_uid__nin=[None, ""],
            bundle_code__nin=[None, ""],
        )
        affected_rows = len(profiles)

        for profile in profiles:
            iccid = profile.iccid
            plan_uid = profile.plan_uid
            bundle_code = profile.bundle_code

            print(f"{datetime.datetime.utcnow()} trying to revoke plan {plan_uid} from flexiroam profile {iccid}")
            #   Revoke the plan from the profile
            revoke_payload = {"plan_uid": plan_uid}
            revoke_response = requests.post(url=revoke_url, headers=headers, json=revoke_payload)
            i = 0
            while revoke_response.status_code != 200 and i < RETRY_COUNT:
                revoke_response = requests.post(url=revoke_url, headers=headers, json=revoke_payload)
                i += 1
                print(f"{datetime.datetime.utcnow()} {revoke_response.status_code} ---- {revoke_response.json()}")
                time.sleep(i)  # wait a bit

            if revoke_response.status_code != 200:
                print(f"Error revoking plan {plan_uid} from profile {iccid} after retrying {RETRY_COUNT}" f" times")
                continue

            print(f"{datetime.datetime.utcnow()} success revoking plan {plan_uid} from flexiroam profile {iccid}")
            profile.update(
                **{
                    "bundle_code": None,
                    "allocate_date": None,
                    "status": False,
                    "plan_uid": None,
                    "expiry_date": None,
                }
            )
            Bundles.objects(bundle_code=bundle_code).update(dec__allocated_unit=1)
            success_rows += 1
        return affected_rows, success_rows
    except Exception as e:
        logger.exception(f"<unload_all_flexi_profiles> Exception: {e}")
        return affected_rows, success_rows


def unload_all_flexiv2_profiles():
    affected_rows = 0
    success_rows = 0
    try:
        flexi_helper: FlexiroamAPI = FlexiroamAPI()
        RETRY_COUNT = 2

        profiles = consumer_models.Profiles.objects(
            vendor_name=FLEXIROAM_VENDOR_V2,
            availability="Free",
            status=True,
            plan_uid__nin=[None, ""],
            bundle_code__nin=[None, ""],
        )
        affected_rows = len(profiles)

        for profile in profiles:

            iccid = profile.iccid
            plan_uid = profile.plan_uid
            bundle_code = profile.bundle_code
            try:
                print(f"{datetime.datetime.utcnow()} trying to revoke plan {plan_uid} from flexiroam profile {iccid}")
                #   Revoke the plan from the profile
                refund_response = flexi_helper.refund_plan(purchased_plan_id=profile.plan_uid, sku=profile.sku)
                print(f"{datetime.datetime.utcnow()} success revoking plan {plan_uid} from flexiroam profile {iccid}")
                profile.update(
                    **{
                        "bundle_code": None,
                        "allocate_date": None,
                        "status": False,
                        "plan_uid": None,
                        "expiry_date": None,
                    }
                )
                Bundles.objects(bundle_code=bundle_code).update(dec__allocated_unit=1)
                success_rows += 1
            except Exception as e:
                raise ValueError(f"Error revoking plan from profile {iccid} after retrying {RETRY_COUNT}" f" times") from e
        return affected_rows, success_rows
    except Exception as e:
        logger.exception(f"<unload_all_flexi_profiles> Exception: {e}")
        return affected_rows, success_rows


def delete_expiry_dates(starting=3, ending=1, vendors: list = None):
    if not vendors:
        vendors = [ESIM_GO_VENDOR, FLEXIROAM_VENDOR]
    ending_date = get_date_before(ending)
    start_date = get_date_before(starting)
    paid_logs = db_helper.get_paid_bundle_log(start_date, ending_date)

    user_iccid_filter_kwargs = {
        "payment_otp__in": [],
        "bundle_code__in": [],
    }

    for log in list(paid_logs):
        user_iccid_filter_kwargs.update(
            {
                "payment_otp__in": [
                    *user_iccid_filter_kwargs.get("payment_otp__in"),
                    log["otp"],
                ],
                "bundle_code__in": [
                    *user_iccid_filter_kwargs.get("bundle_code__in"),
                    log["bundle_code"],
                ],
            }
        )

    used_user_iccid_list = consumer_models.UserIccid.objects(**{**user_iccid_filter_kwargs}).distinct("iccid")
    Profiles.objects(
        iccid__in=used_user_iccid_list,
        availability__ne="Free",
        vendor_name__in=vendors,
        expiry_date__ne=None,
    ).update(set__expiry_date=None)


def vodafone_sync_profiles_count(bundle_code=None):
    try:
        now = datetime.datetime.utcnow()
        vendor_info = get_vendor_info(VODAFONE_VENDOR)
        vodafone_free_profiles_count = 0
        body = ""
        if vendor_info.is_active:
            bundles = db_helper.get_bundles_by_vendor(VODAFONE_VENDOR, is_active=True, deleted=False, bundle_code=bundle_code)
            if bundles:
                for bundle in bundles:
                    vodafone_free_profiles_count = consumer_models.Profiles.objects(
                        Q(vendor_name=VODAFONE_VENDOR)
                        & Q(bundle_code=bundle.bundle_code)
                        & Q(availability="Free")
                        & Q(plan_uid__nin=[None, ""])
                        & Q(status=True)
                        & Q(expiry_date__gt=now)
                    ).count()

                    body += f"The total remaining number of free profiles for bundle_code: '{bundle.bundle_code}' is {vodafone_free_profiles_count}\n"

            vodafone_all_profiles = vendor_info.total_profiles_bought

            vodafone_all_profiles_in_our_inventory = consumer_models.Profiles.objects(Q(vendor_name=VODAFONE_VENDOR)).count()
            total_number_of_needed_profiles = vodafone_all_profiles - vodafone_all_profiles_in_our_inventory
            subject = f"------URGENT------ For Vendor Vodafone:"
            body += f"for [{datetime.datetime.utcnow()}], The total number of needed Profiles is {total_number_of_needed_profiles}"
            send_custom_monitor_email(subject, body)

            return (
                "Emails sent successfully",
                vodafone_free_profiles_count,
                total_number_of_needed_profiles,
            )

    except Exception as e:
        print("exception in sync_profiles_count as", e)


def notify_system_with_inventory_details():
    """
        Generates and sends an inventory status report for all vendors, including
        bundle-wise profile counts and availability metrics.
    """
    try:
        utc_now = datetime.datetime.utcnow()

        # Get bundle-wise inventory details for all vendors
        inventory_details = list(Bundles._get_collection().aggregate([
            {
                '$match': {
                    'is_active': True,
                    'deleted': False,
                    'allocate_profiles': True
                }
            },
            {
                '$lookup': {
                    'from': 'profiles',
                    'let': {'bundle_code': '$bundle_code'},
                    'pipeline': [{
                        '$match': {
                            '$expr': {
                                '$and': [
                                    {'$eq': ['$bundle_code', '$$bundle_code']},
                                    {'$eq': ['$availability', 'Free']},
                                    {'$eq': ['$status', True]},
                                    {'$ne': ['$plan_uid', None]},
                                    {'$ne': ['$plan_uid', '']}
                                ]
                            }
                        }
                    }],
                    'as': 'profiles'
                }
            },
            {
                '$project': {
                    'bundle_code': 1,
                    'profiles_count': {'$size': '$profiles'},
                    'vendor_names': '$profiles.vendor_name',
                    'data_amount': '$data_amount',
                    'data_unit': '$data_unit',
                    'bundle_duration': '$bundle_duration'
                }
            },
            {'$unwind': '$vendor_names'},
            {
                '$group': {
                    '_id': {
                        'bundle_code': '$bundle_code',
                        'vendor_name': '$vendor_names'
                    },
                    'bundle_info': {'$first': '$$ROOT'},
                    'count': {'$sum': 1}
                }
            },
            {'$sort': {'count': -1}},
            {
                '$group': {
                    '_id': '$_id.vendor_name',
                    'bundles': {
                        '$push': {
                            'bundle_code': '$_id.bundle_code',
                            'count': '$count',
                            'bundle_info': '$bundle_info'
                        }
                    }
                }
            }
        ]))

        # Construct email content
        email_body = [
            f"Inventory Status Report - {utc_now.strftime('%Y-%m-%d %H:%M')}",
            "\nFree Profiles Available Per Vendor:",
        ]

        for vendor_data in inventory_details:
            vendor_name = vendor_data['_id']
            total_free_profiles = sum(bundle['count'] for bundle in vendor_data['bundles'])

            email_body.append(f"\n{vendor_name}:")

            email_body.append(f"Total Free Profiles: {total_free_profiles}")
            email_body.append("Bundle Breakdown:")
            if len(vendor_data['bundles']) > 100:
                email_body.append("Too many bundles to list here. Please check the dashboard for details.")
                continue

            for bundle in vendor_data['bundles']:
                percentage = (bundle['count'] / total_free_profiles * 100) if total_free_profiles else 0
                email_body.append(
                    f"  - {bundle['bundle_code']}: {bundle['count']} profiles "
                    f"({percentage:.1f}% of vendor total)   [{bundle['bundle_info']['data_amount']}{bundle['bundle_info']['data_unit']}] [{bundle['bundle_info']['bundle_duration']} days]"
                )

        # Send email
        send_custom_monitor_email(subject="Daily Inventory Status Report", body="\n".join(email_body))

        return "Inventory report generated and sent successfully"

    except Exception as e:
        logging.error(f"Failed to generate inventory report: {str(e)}", exc_info=True)
        raise


def get_token_play_integrity():
    try:
        play_integrity_url = instance_config.play_integrity_url
        username = instance_config.play_integrity_username
        password = instance_config.play_integrity_password
        payload = json.dumps({"username": username, "password": password})
        headers = {"Content-Type": app_json}
        r = requests.request(
            "POST",
            "{}/api/Authorize/GenerateToken".format(play_integrity_url),
            headers=headers,
            data=payload,
        )
        response = r.json()
        return response, ""

    except Exception as e:
        print("Exception at get_token_play_integrity as :", str(e))
        return False, "Couldn't get token!"


def save_play_integrity_token():
    email_subject = "Play Integrity token"
    try:
        response, exception = get_token_play_integrity()
        if response["result"] == "Success":
            play_integrity_token = response["tokenString"]
            str_key = instance_config.token_key
            encrypt_helper = Crypt()
            encrypted_token = encrypt_helper.encrypt(play_integrity_token, str_key)
            main_models.Settings.objects().update(set__encrypted_integrity_token=encrypted_token)
            return True
        else:
            send_custom_dev_email(
                subject=email_subject,
                body=f"------URGENT------Play Integrity token: get token failed at {datetime.datetime.utcnow()}. exception: {exception}",
            )
    except Exception as e:
        send_custom_dev_email(
            subject=email_subject,
            body=f"------URGENT------Play Integrity token: get token failed at {datetime.datetime.utcnow()}. exception: {exception}",
        )
        print("exception in save_play_integrity_token ", str(e))


def monty_reseller_fetch_and_save_bundles() -> (int, datetime.datetime):
    now: datetime.datetime = datetime.datetime.utcnow()

    vendor: Vendors = Vendors.objects(vendor_name=MONTY_RESELLER_VENDOR, is_active=True).first()
    if not vendor:
        raise ValueError("Vendor not active")
    vendor.update(set__last_update_bundles_date=now)
    monty_reseller: MontyReseller = MontyReseller()

    page_size: int = 100
    page_number: int = 1
    available_bundles_from_vendor: list = []
    while True:
        data: dict = monty_reseller.get_bundles(page_size=page_size, page_number=page_number)
        all_bundles: list = data.get("bundles", [])
        if not all_bundles:
            break

        available_bundles_from_vendor.extend(all_bundles)
        page_number += 1

    if not available_bundles_from_vendor:
        return 0, now

    available_bundles_from_vendor_bvc_list: list = [bundle.get("bundle_code") for bundle in available_bundles_from_vendor]

    #   get bundles that we already have and are available from vendor
    already_existing_bvc_list: list = Bundles.objects().distinct("bundle_vendor_code")

    new_bundle_vendor_code_list: list = list(set(available_bundles_from_vendor_bvc_list) - set(already_existing_bvc_list))

    new_bundle_list: list = [bundle for bundle in available_bundles_from_vendor if bundle.get("bundle_code") in new_bundle_vendor_code_list]

    for bundle_data in new_bundle_list:

        _, local_bundle_code = db_helper.set_bundle_name_code(bundle_data)

        new_bundle = {
            "vendor_name": MONTY_RESELLER_VENDOR,
            "bundle_code": local_bundle_code,
            "allocated_unit": 1,
            "consumed_unit": 0,
            "bundle_vendor_code": bundle_data.get("bundle_code"),
            "bundle_name": bundle_data.get("bundle_name"),
            "region_code": bundle_data.get("region_code"),
            "region_name": bundle_data.get("region_name"),
            "bundle_marketing_name": bundle_data.get("bundle_marketing_name"),
            "bundle_category": bundle_data.get("bundle_category"),
            "create_datetime": datetime.datetime.utcnow(),
            "validity_amount": str(bundle_data.get("validity")),
            "bundle_duration": int(bundle_data.get("validity")),
            "data_amount": bundle_data.get("gprs_limit"),
            "data_unit": bundle_data.get("data_unit"),
            "retail_price": bundle_data.get("subscriber_price"),
            "currency_code": bundle_data.get("currency_code_list", ["USD"])[0],
            "country_list": bundle_data.get("country_name", []),
            "country_code_list": bundle_data.get("country_code", []),
            "unlimited": bundle_data.get("unlimited", False),
            "is_active": False,
            "deleted": True,
        }
        try:
            Bundles(**new_bundle).save()
            logging.info("saved new bundle %s", local_bundle_code)
        except Exception as e:
            logging.error("Couldn't save bundle %s due to %s", local_bundle_code, e)

    logging.info("Processed %s bundles", len(available_bundles_from_vendor))
    if len(new_bundle_list) > 0:
        add_to_update_bundle_version([f"Saved new bundles from monty reseller: {new_bundle_vendor_code_list}"])

    bundles_that_should_be_soft_deleted_list: list = list(set(already_existing_bvc_list) - set(available_bundles_from_vendor_bvc_list))
    bundles_to_soft_delete: mongoengine.QuerySet = Bundles.objects(bundle_vendor_code__in=bundles_that_should_be_soft_deleted_list)
    logging.info("Soft deleting %s bundles", len(bundles_to_soft_delete))
    bundles_to_soft_delete.update(deleted=True)
    if bundles_to_soft_delete:
        add_to_update_bundle_version([f"CRITICAL: Soft deleted bundles: {bundles_to_soft_delete.distinct('bundle_code')}"])
        send_custom_monitor_email(
            subject=f"CRITICAL: Soft deleted bundles",
            body=f"These bundles were soft deleted and are not available to purchase anymore: \n {bundles_to_soft_delete.distinct('bundle_code')}",
        )
    logging.info("Soft deleted %s bundles", len(bundles_to_soft_delete))
    return len(all_bundles), now


def get_all_monty_reseller_bundles():
    return monty_reseller_fetch_and_save_bundles()


def run_manage_subscriptions():
    asyncio.run(Graph(instance_config).manage_subscriptions())


def retry_failed_reseller_notification() -> None:
    """
    Retry sending failed reseller webhook notifications, grouped by reseller.

    This function retrieves all previously failed notifications grouped by reseller ID.
    For each reseller, it attempts to resend their failed notifications using the 'retry' type.
    If 5 consecutive notification attempts fail for a reseller, the process skips further retries
    for that reseller to prevent overload or blacklisting.
    """
    # get list of notifications per reseller
    logger.info("Retry Failed Reseller Notification Scheduler Started")
    failed_notifications = get_failed_notifications()
    logger.info("Start Retrying Failed Reseller Notification on %s affected notifications", len(failed_notifications))
    for reseller_id, notifications in failed_notifications.items():
        failed_cnt = 0
        logger.info("Start notification process for reseller %s", reseller_id)
        for notification in notifications:
            # start notifying reseller
            status = notify_reseller(notification_type="retry", notification=notification)
            # check if count of notification status is false for 5 times so we stop notifying him
            if not status:
                failed_cnt = failed_cnt + 1
            if failed_cnt == 5:
                logger.warning("Skipping notifying reseller with id %s due to %s consecutive failed notification response", reseller_id, failed_cnt)
                break

def try_first_time_notification() -> None:
    """
    Attempt to send first-time reseller webhook notifications, grouped by reseller.

    This function retrieves all unsent notifications (first-time) grouped by reseller ID.
    It sends each notification using the 'first_time' type. If 5 consecutive notifications fail
    for a reseller, further attempts are skipped to avoid unnecessary load.
    """
    # get list of notifications per reseller
    logger.info("First Time Notification Scheduler Started")
    first_time_notifications = get_first_time_notifications()
    logger.info("Start First Time Notification on %s affected notifications", len(first_time_notifications))
    for reseller_id, notifications in first_time_notifications.items():
        failed_cnt = 0
        logger.info("Start notification process for reseller %s", reseller_id)
        for notification in notifications:
            # start notifying reseller
            status = notify_reseller(notification_type="first_time", notification=notification)
            # check if count of notification status is false for 5 times so we stop notifying him
            if not status:
                failed_cnt = failed_cnt + 1
            if failed_cnt == 5:
                logger.warning("Skipping notifying reseller with id %s due to %s consecutive failed notification response", reseller_id, failed_cnt)
                break

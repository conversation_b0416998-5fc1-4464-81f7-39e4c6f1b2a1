import datetime
from flask import flash
from flask import render_template, session
from instance import consumer_config as instance_config
from app_helpers.script_helper import allocate_flexi_bundle, get_user_consumption
from app_helpers import db_helper
def get_tenant():
    try:
        if 'tenant' in session:
            result = session['tenant']
        else:
            result = 'default'
        return result
    except Exception as e:
        print("Exception as : ", str(e))
        return "unknown"


def validate_new_pass(pass1, pass2):
    return pass1 == pass2


def password_check(passwd):
    special_sm_list = ['$', '!', '@', '#', '%']
    val = True

    if len(passwd) < 12:
        flash('length should be at least 12')
        val = False

    if len(passwd) > 20:
        flash('length should be not be greater than 20')
        val = False

    if not any(char.isdigit() for char in passwd):
        flash('Password should have at least one numeral')
        val = False

    if not any(char.isupper() for char in passwd):
        flash('Password should have at least one uppercase letter')
        val = False

    if not any(char.islower() for char in passwd):
        flash('Password should have at least one lowercase letter')
        val = False

    if not any(char in special_sm_list for char in passwd):
        flash('Password should have at least one of the symbols $@#')
        val = False
    if val:
        return val


def update_expiration_date():
    now = datetime.datetime.utcnow()
    return now + datetime.timedelta(hours=24 * 180)


def get_model(model_list, index):
    new_list = [item for item in model_list if item[1] == index]
    model_name, selected_index = new_list[0]
    return model_name


def show_count(model, ids, status):
    count = 0
    for id_ in ids:
        update_model = model.objects(id=id_).first()
        update_model.show_count = status
        if update_model.save():
            count = count + 1
    return count


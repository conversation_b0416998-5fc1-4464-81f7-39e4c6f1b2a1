import datetime
from flask import flash

def validate_new_pass(pass1, pass2):
    return pass1 == pass2


def password_check(passwd):
    special_sm_list = ['$', '!', '@', '#', '%']
    val = True
      
    if len(passwd) < 12:
        flash('length should be at least 12')
        val = False
          
    if len(passwd) > 20: 
        flash('length should be not be greater than 20')
        val = False
          
    if not any(char.isdigit() for char in passwd): 
        flash('Password should have at least one numeral') 
        val = False
          
    if not any(char.isupper() for char in passwd): 
        flash('Password should have at least one uppercase letter') 
        val = False
          
    if not any(char.islower() for char in passwd): 
        flash('Password should have at least one lowercase letter') 
        val = False
          
    if not any(char in special_sm_list for char in passwd):
        flash('Password should have at least one of the symbols $@#') 
        val = False
    if val: 
        return val


def update_expiration_date():
    now = datetime.datetime.utcnow()
    return now + datetime.timedelta(hours=24*180)


def get_model(model_list, index):
    new_list = [item for item in model_list if item[1] == index]
    model_name, selected_index = new_list[0]
    return model_name


def show_count(model, ids, status):
    count = 0
    for id_ in ids:
        update_model = model.objects(id=id_).first()
        update_model.show_count = status
        if update_model.save():
            count = count + 1
    return count


def depend_on_tenant(model, ids, status):
    count = 0
    for id_ in ids:
        update_model = model.objects(id=id_).first()
        update_model.depend_on_tenant = status
        if update_model.save():
            count = count + 1
    return count

import pprint
import datetime
import connexion
import pymongo
from bson import ObjectId
from instance import consumer_config

database_name = consumer_config.decrypted_db_name_alias
mongo_db_url = consumer_config.new_host_

def insert_one(
    collection,
    *args,
    apply_Tenancy: int = 2,
    apply_Date=True,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    collection_name = collection
    database = myclient[database_name]
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Date:
        args[0]["date_created"] = datetime.datetime.utcnow()
    if (
        apply_Tenancy == 1
        and not connexion.context.get("token_info", {})
        .get("test_key", {})
        .get("api_caller", "")
        == "internal"
    ):
        args[0]["added_by"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("user_id", "")
        )

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if (
        apply_Tenancy == 2
        and not connexion.context.get("token_info", {})
        .get("test_key", {})
        .get("api_caller", "")
        == "internal"
    ):
        args[0]["added_by"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("user_id", "")
        )

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.insert_one(*args, **kwargs)


def insert_many(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    collection_name = collection
    database = myclient[database_name]
    collection = database[collection]
    for item in args[0]:
        if collection_name == "order_history":
            item["reseller_type"] = "reseller"
        item["date_created"] = datetime.datetime.utcnow()
        item["added_by"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("user_id", "")
        )
        if apply_Tenancy == 1:
            item["reseller_id"] = ObjectId(
                connexion.context.get("token_info", {})
                .get("test_key", {})
                .get("reseller_id", "")
            )

        if apply_Tenancy == 2:

            item["reseller_id"] = ObjectId(
                connexion.context.get("token_info", {})
                .get("test_key", {})
                .get("reseller_id", "")
            )
            item["branch_id"] = ObjectId(
                connexion.context.get("token_info", {})
                .get("test_key", {})
                .get("branch_id", "")
            )

    return collection.insert_many(*args, **kwargs)


def find_one(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
    if apply_Tenancy == 2:

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.find_one(*args, **kwargs)


def find(
    collection,
    *args,
    skip: int = 0,
    limit: int = 0,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if apply_Tenancy == 2:

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.find(*args, **kwargs).skip(skip).limit(limit)

def find_order_history(
    collection,
    *args,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection = database[collection]

    return collection.find(*args, **kwargs)


def update_one(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if apply_Tenancy == 2:

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.update_one(*args, **kwargs)


def update_many(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if apply_Tenancy == 2:

        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.update_many(*args, **kwargs)


def aggregate(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0][0]["$match"]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0][0]["$match"]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if apply_Tenancy == 2:

        args[0][0]["$match"]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0][0]["$match"]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.aggregate(*args, **kwargs)


def delete_one(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
    if apply_Tenancy == 2:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )
    return collection.delete_one(*args, **kwargs)


def delete_many(
    collection, *args, apply_Tenancy: int = 2, database_name=database_name, **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
    if apply_Tenancy == 2:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )
    return collection.delete_many(*args, **kwargs)


def count_documents(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]
    if collection_name == "order_history":
        args[0]["reseller_type"] = "reseller"
    if apply_Tenancy == 1:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )

    if apply_Tenancy == 2:
        args[0]["reseller_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("reseller_id", "")
        )
        args[0]["branch_id"] = ObjectId(
            connexion.context.get("token_info", {})
            .get("test_key", {})
            .get("branch_id", "")
        )

    return collection.count_documents(*args, **kwargs)


def distinct(
    collection,
    *args,
    apply_Tenancy: int = 2,
    mongo_db_url=mongo_db_url,
    database_name=database_name,
    **kwargs
):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection_name = collection
    collection = database[collection]

    return collection.distinct(*args, **kwargs)


def WARNING_DROP_COLLECTION(collection, mongo_db_url=mongo_db_url):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection = database[collection]

    return collection.drop()


def CREATE_INDEXES(collection, *args, mongo_db_url=mongo_db_url, **kwargs):
    myclient = pymongo.MongoClient(mongo_db_url)
    database = myclient[database_name]
    collection = database[collection]
    collection.create_index(*args, **kwargs)


def create_indexes(collection, *args, mongo_url=mongo_db_url, **kwargs):
    myclient = pymongo.MongoClient(mongo_url)
    database = myclient[database_name]
    collection = database[collection]
    collection.create_index(*args, **kwargs)


if __name__ == "__main__":
    request = {"name": "John", "address": "Highway 37"}

    inserted_id = insert_one("hello", "there", request)

    requests = [
        {"name": "1", "address": "Highway 1"},
        {"name": "2", "address": "Highway 2"},
        {"name": "3", "address": "Highway 3"},
    ]

    inserted_ids = insert_many("hello", "there", requests)

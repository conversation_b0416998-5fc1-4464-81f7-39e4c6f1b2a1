import datetime
import sys

sys.path.append('../')
from app_helpers.db_helper import get_one_runnable_csv
from app_helpers import webhook_helpers, db_helper, main_helper
from app_models.main_models import generate_csv_

REPORT_PROMOCODE = 'app_main/exported_files/report_promo_code.csv'


def run_cron_jobs_runnable_csv(app=None):
    print('*********************************************************')
    print('Start: Running CronJobs Runnable at: ', datetime.datetime.utcnow())
    print('*********************************************************')
    for script_name in generate_csv_:
        script_object = Script(script_name=script_name)
        script_object.batch_run(app=None)


class Script:
    def __init__(self, script_name=None):
        """
        script_name: values: GenrateVoucherCode, GenratePromoCode
        """
        self.script_name = script_name
        self.runnable_object = self.init_runnable_object() if script_name is not None else None
        self.amount = self.runnable_object.amount if self.runnable_object is not None else 0
        self.quantity = self.runnable_object.quantity if self.runnable_object is not None else 0
        self.currency_code = self.runnable_object.currency_code if self.runnable_object is not None else 0
        self.generate_qr_code = self.runnable_object.generate_qr_code if self.runnable_object is not None else 0
        self.reseller_code = self.runnable_object.reseller_code if self.runnable_object is not None else 0
        self.expiry_datetime = self.runnable_object.expiry_datetime if self.runnable_object is not None else 0
        self.reason = self.runnable_object.reason if self.runnable_object is not None else 0
        self.redeem_name=self.runnable_object.redeem_name if self.runnable_object is not None else 0
        self.status=self.runnable_object.status if self.runnable_object is not None else 0
        self.reseller_id = self.runnable_object.reseller_id if self.runnable_object is not None else 0
        self.export_file_format = None
        self.start_date = datetime.datetime.now()
        self.end_date = None
        self.duration = 0
        self.affected_rows = 0
        self.success_rows = 0

    def set_script_name(self, script_name):
        self.script_name = script_name

    def init_runnable_object(self):
        self.runnable_object = get_one_runnable_csv(self.script_name)
        return self.runnable_object

    def init_duration(self):
        self.end_date = datetime.datetime.now()
        self.duration = (self.end_date - self.start_date).total_seconds()
        return self.duration

    def batch_run(self, app=None):
        try:
            if self.runnable_object is None:
                return None
            self.runnable_object.failure_reason = ""
            self.runnable_object.state = "Running"
            self.runnable_object.save()

            if self.script_name == "GeneratePromoCode":
                self.save_promo_code(self.start_date, self.redeem_name, self.amount, self.quantity, self.currency_code,
                                     self.status,
                                     self.expiry_datetime)

            elif self.script_name == "GenerateVoucherCode":

                self.save_voucher_code(self.start_date, self.reseller_code, self.redeem_name, self.amount, self.quantity,
                                       self.currency_code, self.status,  self.expiry_datetime, self.generate_qr_code, self.reason, self.reseller_id)

            self.init_duration()
            self.runnable_object.end_date = self.end_date
            self.runnable_object.duration = self.duration
            self.runnable_object.affected_rows = self.affected_rows
            self.runnable_object.success_rows = self.success_rows
            self.runnable_object.save()

        except Exception as e:
            self.runnable_object.state = "Error"
            self.runnable_object.failure_reason = "Exception in batch_run due to " + str(e)
            self.runnable_object.save()

    def save_promo_code(self, start_date,redeem_name,  amount, quantity, currency_code, is_active, expiry_datetime):
        try:
            self.runnable_object.status = "starting"
            self.runnable_object.status_date = datetime.datetime.now()
            main_helper.generate_csv_file_for_promo_code(start_date, redeem_name, amount, quantity, currency_code, is_active, expiry_datetime)
            self.affected_rows = quantity
            self.success_rows = quantity
            self.runnable_object.state = "Finished"

        except Exception as e:
            self.runnable_object.state = "Error"
            self.runnable_object.failure_reason =  " Exception in save_promo_codes due to " + str(e)

    def save_voucher_code(self, start_date, reseller_code,redeem_name, amount, quantity, currency_code, status,expiry_datetime,
                          generate_qr_code, reason, reseller_id):
        try:
            self.runnable_object.status = "starting"
            self.runnable_object.status_date = datetime.datetime.now()
            self.runnable_object.save()

            res = main_helper.generate_csv_file_for_voucher_code(start_date, reseller_code, redeem_name,  amount, quantity, currency_code, status, expiry_datetime,
                                       generate_qr_code, reason, reseller_id)
            self.affected_rows = 0
            self.success_rows = 0
            self.runnable_object.state = "Finished"

        except Exception as e:
            print("exception save_voucher_code ", str(e))
            self.runnable_object.state = "Error"
            self.runnable_object.failure_reason = "Exception in save_voucher_codes due to " + str(e)

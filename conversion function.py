from app_helpers import mongodb
from connexion.exceptions import ProblemException


def convert_currency(currency_code, prices_in_usd_dict):
    if currency_code:
        getQuery = {'currency_code': currency_code, "currency_rate": {"$exists": True}}
        currency = mongodb.find_one("currency_codes", getQuery, apply_Tenancy=0)
        try:
            currency_rate = currency["currency_rate"]
            if currency_rate == 0:
                raise ValueError
            converted_dict = {"additional_currency_code": currency_code}

            # Iterate through the original dictionary
            for key, value in prices_in_usd_dict.items():
                # Modify the key by adding 'in_additional_currencies'
                new_key = f'{key}_in_additional_currency'

                new_value = value * currency_rate

                # Add the modified key and converted value to the new dictionary
                converted_dict[new_key] = new_value

            return converted_dict

        except:
            raise ProblemException(status=501, title="Error",
                                   detail="Contact Administration! Currency not Fully Configured!")


if __name__ == '__main__':
    print(convert_currency("SAR", {"a": 1, 'b': 2}))

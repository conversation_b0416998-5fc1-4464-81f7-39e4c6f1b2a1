# DB Connections Configuration

This directory contains JSON configuration files for database connections. Each file specifies a single database configuration as follows:

```json
{
  "mongo_dbs": {
    "alias": "main",
    "db": "rsp_stage",
    "host": "localhost",
    "port": 27017,
    "default": false,
    "main": true
  }
}
```

## Description of Properties

- `default`: Indicates if this is the currently active tenant-specific database. There should only be one `default` database set to `true` at any time.
- `main`: Indicates if this is the main, shared, non-tenant-specific database. This database should also be active as a secondary database alongside the default database. There should only be one `main` database set to `true` at any time.

## Constraints

- A property cannot be both `default` and `main`. Ensure that these properties are mutually exclusive across all configuration files.
- Always ensure that there is exactly one `default` and one `main` database configuration marked as `true` at any given time.

## Naming Convention

- The files inside the `db_connections` directory should always start with the environment name followed by an underscore, formatted as `{ENV}_`. For example, `DEV_mongoConfig.json`, `PROD_mongoConfig.json`, etc. This helps in identifying the environment-specific configuration easily.

Please ensure that each JSON file follows this structure and adheres to the naming conventions and constraints specified above.

[2025-04-23 10:25:16 +0300] [256001] [INFO] <root>: Logging configuration loaded
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable_csv" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_token_" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "save_vodafone_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "indosat_save_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "indosat_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "flexi_save_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "flexi_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "flexiroamv2_allocate_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "bayobab_bulk_allocate_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "vodafone_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "expire_scripts" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "rest_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "expire_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "create_profile_expiry" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "customer_feedback_send_email" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "notify_profile_before_expiry" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "esimgo_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "flexi_check_available_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "notify_system_with_inventory_details" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "save_play_integrity_token" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "refill_old_bundles_flexiroam" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "run_manage_subscriptions" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "expire_bayobab_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Scheduler started
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_profiles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_bundles" to job store "default"
[2025-04-23 10:25:16 +0300] [256001] [INFO] <apscheduler.scheduler>: Added job "telkomsel_bulk_allocate_profiles" to job store "default"
[2025-04-23 10:25:18 +0300] [256001] [INFO] <root>: method get_vendor_info
[2025-04-23 10:26:00 +0300] [256001] [INFO] <apscheduler.executors.default>: Running job "telkomsel_bulk_allocate_profiles (trigger: cron[minute='*/13'], next run at: 2025-04-23 10:39:00 EEST)" (scheduled at 2025-04-23 10:26:00+03:00)
[2025-04-23 10:26:00 +0300] [256001] [INFO] <app_helpers.cron_helpers>: Starting bulk allocation for Telkomsel profiles...
[2025-04-23 10:26:00 +0300] [256001] [INFO] <root>: method get_bundles_by_vendor
[2025-04-23 10:26:00 +0300] [256001] [INFO] <apscheduler.executors.default>: Job "telkomsel_bulk_allocate_profiles (trigger: cron[minute='*/13'], next run at: 2025-04-23 10:39:00 EEST)" executed successfully
[2025-04-23 10:28:00 +0300] [256001] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-04-23 10:35:00 EEST)" (scheduled at 2025-04-23 10:28:00+03:00)
[2025-04-23 10:28:00 +0300] [256001] [INFO] <app_helpers.cron_helpers>: Start Saving Profiles From Telkomsel
[2025-04-23 10:28:00 +0300] [256001] [INFO] <root>: method get_vendor
[2025-04-23 10:28:00 +0300] [256001] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_profiles, 'NoneType' object has no attribute 'profiles_threshold_count' 
[2025-04-23 10:28:00 +0300] [256001] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-04-23 10:35:00 EEST)" executed successfully
[2025-04-23 10:28:23 +0300] [256001] [INFO] <apscheduler.scheduler>: Scheduler has been shut down
[2025-04-23 10:28:27 +0300] [256867] [INFO] <root>: Logging configuration loaded
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable_csv" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_token_" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "save_vodafone_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "indosat_save_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "indosat_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "flexi_save_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "flexi_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "flexiroamv2_allocate_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "bayobab_bulk_allocate_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "vodafone_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "expire_scripts" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "rest_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "expire_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "create_profile_expiry" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "customer_feedback_send_email" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "notify_profile_before_expiry" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "esimgo_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "flexi_check_available_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "notify_system_with_inventory_details" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "save_play_integrity_token" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "refill_old_bundles_flexiroam" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "run_manage_subscriptions" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "expire_bayobab_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Scheduler started
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_profiles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_bundles" to job store "default"
[2025-04-23 10:28:27 +0300] [256867] [INFO] <apscheduler.scheduler>: Added job "telkomsel_bulk_allocate_profiles" to job store "default"
[2025-04-23 10:28:28 +0300] [256867] [INFO] <__main__>: Initiating Indosat Save Profiles with concurrency
[2025-04-23 10:28:29 +0300] [256867] [INFO] <root>: method get_vendor_info
[2025-04-23 10:29:48 +0300] [256867] [INFO] <__main__>: Total profiles: 23705, Total pages: 250
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Running job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-04-23 10:40:00 EEST)" (scheduled at 2025-04-23 10:30:00+03:00)
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-04-23 10:35:00 EEST)" (scheduled at 2025-04-23 10:30:00+03:00)
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable_csv (trigger: cron[minute='*/15'], next run at: 2025-04-23 10:30:00 EEST)" (scheduled at 2025-04-23 10:30:00+03:00)
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-04-23 10:30:00 EEST)" (scheduled at 2025-04-23 10:30:00+03:00)
[2025-04-23 10:30:00 +0300] [256867] [INFO] <app_helpers.cron_helpers>: Start Saving Bundles From Telkomsel
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: method get_vendor_info
[2025-04-23 10:30:00 +0300] [256867] [ERROR] <apscheduler.executors.default>: Job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-04-23 10:40:00 EEST)" raised an exception
Traceback (most recent call last):
  File "/home/<USER>/PycharmProjects/esim-b2c-mng-py/.venv/lib/python3.13/site-packages/apscheduler/executors/base.py", line 125, in run_job
    retval = job.func(*job.args, **job.kwargs)
  File "/home/<USER>/PycharmProjects/esim-b2c-mng-py/app_helpers/cron_helpers.py", line 4246, in get_all_monty_reseller_bundles
    return monty_reseller_fetch_and_save_bundles()
  File "/home/<USER>/PycharmProjects/esim-b2c-mng-py/app_helpers/cron_helpers.py", line 4165, in monty_reseller_fetch_and_save_bundles
    raise ValueError("Vendor not active")
ValueError: Vendor not active
[2025-04-23 10:30:00 +0300] [256867] [ERROR] <root>: Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-04-23 10:30:00 +0300] [256867] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_bundles, Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-04-23 10:40:00 EEST)" executed successfully
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable_csv (trigger: cron[minute='*/15'], next run at: 2025-04-23 10:45:00 EEST)" executed successfully
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: running 16 scripts
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.01615s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.019953s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.011301s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.016647s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.019847s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.017631s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.014758s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.010758s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.012377s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.018755s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.011919s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.013032s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.011194s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.011628s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Vodafone, bundle: 03142023150845_01032_2
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Vodafone. Added: 0, Duration: 0.01s, Reason: Vendor is Inactive
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.01136s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting script execution: AllocateProfiles
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Starting profile allocation for vendor: Monty Mobile, bundle: NOR_0115202512400130
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile allocation completed for Monty Mobile. Added: 0, Duration: 0.01s, Reason: 
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Profile added: 0
[2025-04-23 10:30:00 +0300] [256867] [INFO] <root>: Script AllocateProfiles completed successfully. Duration: 0.011192s
[2025-04-23 10:30:00 +0300] [256867] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-04-23 10:35:00 EEST)" executed successfully
[2025-04-23 10:30:32 +0300] [256867] [INFO] <__main__>: Fetching page 2 with size 50
[2025-04-23 10:30:33 +0300] [256867] [INFO] <__main__>: Fetching page 3 with size 50
[2025-04-23 10:30:33 +0300] [256867] [INFO] <__main__>: Fetching page 4 with size 50
[2025-04-23 10:30:34 +0300] [256867] [INFO] <__main__>: Fetching page 5 with size 50
[2025-04-23 10:30:34 +0300] [256867] [INFO] <__main__>: Fetching page 6 with size 50
[2025-04-23 10:30:34 +0300] [256867] [INFO] <__main__>: Fetching page 7 with size 50
[2025-04-23 10:30:35 +0300] [256867] [INFO] <__main__>: Fetching page 8 with size 50
[2025-04-23 10:30:35 +0300] [256867] [INFO] <__main__>: Fetching page 9 with size 50
[2025-04-23 10:30:36 +0300] [256867] [INFO] <__main__>: Fetching page 10 with size 50
[2025-04-23 10:30:36 +0300] [256867] [INFO] <__main__>: Fetching page 11 with size 50
[2025-04-23 10:30:36 +0300] [256867] [INFO] <__main__>: Fetching page 12 with size 50
[2025-04-23 10:30:37 +0300] [256867] [INFO] <__main__>: Fetching page 13 with size 50
[2025-04-23 10:30:37 +0300] [256867] [INFO] <__main__>: Fetching page 14 with size 50
[2025-04-23 10:30:37 +0300] [256867] [INFO] <__main__>: Fetching page 15 with size 50
[2025-04-23 10:30:38 +0300] [256867] [INFO] <__main__>: Fetching page 16 with size 50
[2025-04-23 10:30:38 +0300] [256867] [INFO] <__main__>: Fetching page 17 with size 50
[2025-04-23 10:30:38 +0300] [256867] [INFO] <__main__>: Fetching page 18 with size 50
[2025-04-23 10:30:39 +0300] [256867] [INFO] <__main__>: Fetching page 19 with size 50
[2025-04-23 10:30:39 +0300] [256867] [INFO] <__main__>: Fetching page 20 with size 50
[2025-04-23 10:30:40 +0300] [256867] [INFO] <__main__>: Fetching page 21 with size 50
[2025-04-23 10:30:41 +0300] [256867] [INFO] <__main__>: Fetching page 24 with size 50
[2025-04-23 10:30:41 +0300] [256867] [INFO] <__main__>: Fetching page 22 with size 50
[2025-04-23 10:30:41 +0300] [256867] [INFO] <__main__>: Fetching page 23 with size 50
[2025-04-23 10:30:42 +0300] [256867] [INFO] <__main__>: Fetching page 25 with size 50
[2025-04-23 10:30:42 +0300] [256867] [INFO] <__main__>: Fetching page 26 with size 50
[2025-04-23 10:30:42 +0300] [256867] [INFO] <__main__>: Fetching page 27 with size 50
[2025-04-23 10:30:44 +0300] [256867] [INFO] <__main__>: Fetching page 28 with size 50
[2025-04-23 10:30:44 +0300] [256867] [INFO] <__main__>: Fetching page 29 with size 50
[2025-04-23 10:30:44 +0300] [256867] [INFO] <__main__>: Fetching page 30 with size 50
[2025-04-23 10:30:45 +0300] [256867] [INFO] <__main__>: Fetching page 31 with size 50
[2025-04-23 10:30:45 +0300] [256867] [INFO] <__main__>: Fetching page 32 with size 50
[2025-04-23 10:30:45 +0300] [256867] [INFO] <__main__>: Fetching page 33 with size 50
[2025-04-23 10:30:46 +0300] [256867] [INFO] <__main__>: Fetching page 34 with size 50
[2025-04-23 10:30:46 +0300] [256867] [INFO] <__main__>: Fetching page 35 with size 50
[2025-04-23 10:30:46 +0300] [256867] [INFO] <__main__>: Fetching page 36 with size 50
[2025-04-23 10:30:47 +0300] [256867] [INFO] <__main__>: Fetching page 37 with size 50
[2025-04-23 10:30:47 +0300] [256867] [INFO] <__main__>: Fetching page 38 with size 50
[2025-04-23 10:30:47 +0300] [256867] [INFO] <__main__>: Fetching page 39 with size 50
[2025-04-23 10:30:47 +0300] [256867] [INFO] <__main__>: Fetching page 40 with size 50
[2025-04-23 10:30:48 +0300] [256867] [INFO] <__main__>: Fetching page 41 with size 50
[2025-04-23 10:30:49 +0300] [256867] [INFO] <__main__>: Fetching page 42 with size 50
[2025-04-23 10:30:49 +0300] [256867] [INFO] <__main__>: Fetching page 43 with size 50
[2025-04-23 10:30:49 +0300] [256867] [INFO] <__main__>: Fetching page 44 with size 50
[2025-04-23 10:30:49 +0300] [256867] [INFO] <__main__>: Fetching page 45 with size 50
[2025-04-23 10:30:50 +0300] [256867] [INFO] <__main__>: Fetching page 46 with size 50
[2025-04-23 10:30:50 +0300] [256867] [INFO] <__main__>: Fetching page 47 with size 50
[2025-04-23 10:30:50 +0300] [256867] [INFO] <__main__>: Fetching page 48 with size 50
[2025-04-23 10:30:50 +0300] [256867] [INFO] <__main__>: Fetching page 49 with size 50
[2025-04-23 10:30:50 +0300] [256867] [INFO] <__main__>: Fetching page 50 with size 50
[2025-04-23 10:30:51 +0300] [256867] [INFO] <__main__>: Fetching page 51 with size 50
[2025-04-23 10:30:51 +0300] [256867] [INFO] <__main__>: Fetching page 52 with size 50
[2025-04-23 10:30:51 +0300] [256867] [INFO] <__main__>: Fetching page 53 with size 50
[2025-04-23 10:30:51 +0300] [256867] [INFO] <__main__>: Fetching page 54 with size 50
[2025-04-23 10:30:51 +0300] [256867] [INFO] <__main__>: Fetching page 55 with size 50
[2025-04-23 10:30:52 +0300] [256867] [INFO] <__main__>: Fetching page 56 with size 50
[2025-04-23 10:30:53 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 15
[2025-04-23 10:30:53 +0300] [256867] [INFO] <__main__>: Fetching page 57 with size 50
[2025-04-23 10:30:53 +0300] [256867] [INFO] <__main__>: Fetching page 58 with size 50
[2025-04-23 10:30:53 +0300] [256867] [INFO] <__main__>: Fetching page 59 with size 50
[2025-04-23 10:30:53 +0300] [256867] [INFO] <__main__>: Fetching page 60 with size 50
[2025-04-23 10:30:54 +0300] [256867] [INFO] <__main__>: Fetching page 61 with size 50
[2025-04-23 10:30:54 +0300] [256867] [INFO] <__main__>: Fetching page 62 with size 50
[2025-04-23 10:30:54 +0300] [256867] [INFO] <__main__>: Fetching page 63 with size 50
[2025-04-23 10:30:55 +0300] [256867] [INFO] <__main__>: Fetching page 64 with size 50
[2025-04-23 10:30:55 +0300] [256867] [INFO] <__main__>: Fetching page 65 with size 50
[2025-04-23 10:30:55 +0300] [256867] [INFO] <__main__>: Fetching page 66 with size 50
[2025-04-23 10:30:55 +0300] [256867] [INFO] <__main__>: Fetching page 67 with size 50
[2025-04-23 10:30:56 +0300] [256867] [INFO] <__main__>: Fetching page 68 with size 50
[2025-04-23 10:30:56 +0300] [256867] [INFO] <__main__>: Fetching page 69 with size 50
[2025-04-23 10:30:56 +0300] [256867] [INFO] <__main__>: Fetching page 70 with size 50
[2025-04-23 10:30:58 +0300] [256867] [INFO] <__main__>: Fetching page 71 with size 50
[2025-04-23 10:31:01 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 2
[2025-04-23 10:31:02 +0300] [256867] [INFO] <__main__>: Fetching page 72 with size 50
[2025-04-23 10:31:02 +0300] [256867] [INFO] <__main__>: Fetching page 73 with size 50
[2025-04-23 10:31:04 +0300] [256867] [INFO] <__main__>: Fetching page 74 with size 50
[2025-04-23 10:31:04 +0300] [256867] [INFO] <__main__>: Fetching page 75 with size 50
[2025-04-23 10:31:05 +0300] [256867] [INFO] <__main__>: Fetching page 76 with size 50
[2025-04-23 10:31:05 +0300] [256867] [INFO] <__main__>: Fetching page 77 with size 50
[2025-04-23 10:31:07 +0300] [256867] [INFO] <__main__>: Fetching page 78 with size 50
[2025-04-23 10:31:07 +0300] [256867] [INFO] <__main__>: Fetching page 79 with size 50
[2025-04-23 10:31:07 +0300] [256867] [INFO] <__main__>: Fetching page 80 with size 50
[2025-04-23 10:31:08 +0300] [256867] [INFO] <__main__>: Fetching page 81 with size 50
[2025-04-23 10:31:08 +0300] [256867] [INFO] <__main__>: Fetching page 82 with size 50
[2025-04-23 10:31:08 +0300] [256867] [INFO] <__main__>: Fetching page 83 with size 50
[2025-04-23 10:31:08 +0300] [256867] [INFO] <__main__>: Fetching page 84 with size 50
[2025-04-23 10:31:10 +0300] [256867] [INFO] <__main__>: Fetching page 85 with size 50
[2025-04-23 10:31:10 +0300] [256867] [INFO] <__main__>: Fetching page 86 with size 50
[2025-04-23 10:31:10 +0300] [256867] [INFO] <__main__>: Fetching page 87 with size 50
[2025-04-23 10:31:11 +0300] [256867] [INFO] <__main__>: Fetching page 88 with size 50
[2025-04-23 10:31:11 +0300] [256867] [INFO] <__main__>: Fetching page 89 with size 50
[2025-04-23 10:31:11 +0300] [256867] [INFO] <__main__>: Fetching page 90 with size 50
[2025-04-23 10:31:12 +0300] [256867] [INFO] <__main__>: Fetching page 91 with size 50
[2025-04-23 10:31:13 +0300] [256867] [INFO] <__main__>: Fetching page 92 with size 50
[2025-04-23 10:31:14 +0300] [256867] [INFO] <__main__>: Fetching page 93 with size 50
[2025-04-23 10:31:14 +0300] [256867] [INFO] <__main__>: Fetching page 94 with size 50
[2025-04-23 10:31:15 +0300] [256867] [INFO] <__main__>: Fetching page 95 with size 50
[2025-04-23 10:31:16 +0300] [256867] [INFO] <__main__>: Fetching page 96 with size 50
[2025-04-23 10:31:16 +0300] [256867] [INFO] <__main__>: Fetching page 97 with size 50
[2025-04-23 10:31:16 +0300] [256867] [INFO] <__main__>: Fetching page 98 with size 50
[2025-04-23 10:31:16 +0300] [256867] [INFO] <__main__>: Fetching page 99 with size 50
[2025-04-23 10:31:17 +0300] [256867] [INFO] <__main__>: Fetching page 100 with size 50
[2025-04-23 10:31:17 +0300] [256867] [INFO] <__main__>: Fetching page 101 with size 50
[2025-04-23 10:31:17 +0300] [256867] [INFO] <__main__>: Fetching page 102 with size 50
[2025-04-23 10:31:18 +0300] [256867] [INFO] <__main__>: Fetching page 103 with size 50
[2025-04-23 10:31:19 +0300] [256867] [INFO] <__main__>: Fetching page 104 with size 50
[2025-04-23 10:31:19 +0300] [256867] [INFO] <__main__>: Fetching page 105 with size 50
[2025-04-23 10:31:19 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 16
[2025-04-23 10:31:19 +0300] [256867] [INFO] <__main__>: Fetching page 106 with size 50
[2025-04-23 10:31:19 +0300] [256867] [INFO] <__main__>: Fetching page 107 with size 50
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Fetching page 108 with size 50
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Fetching page 109 with size 50
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Fetching page 110 with size 50
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 20
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 14
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 22
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 9
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 12
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 19
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 26
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 7
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 5
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 29
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 11
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 10
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 18
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 17
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 28
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 4
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 8
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 23
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 6
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 3
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 25
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 13
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 24
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 30
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 21
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 32
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 31
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 33
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 34
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 36
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 35
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 37
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 27
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 40
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 39
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 38
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 41
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 44
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 42
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 43
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 45
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 46
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 49
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 48
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 50
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 47
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 51
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 52
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 55
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 54
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 53
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 58
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 59
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 57
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 56
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 60
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 63
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 62
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 65
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 64
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 61
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 70
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 68
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 69
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 73
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 71
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 74
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 75
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 67
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 77
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 66
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 78
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 79
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 72
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 80
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 81
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 84
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 82
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 83
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 87
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 76
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 88
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 89
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 91
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 92
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 93
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 94
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 85
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 86
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 95
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 96
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 97
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 98
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 90
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 100
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 101
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 102
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 103
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 106
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 104
[2025-04-23 10:31:20 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 105
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Fetching page 111 with size 50
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 110
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Fetching page 112 with size 50
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 108
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Fetching page 113 with size 50
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 109
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Fetching page 114 with size 50
[2025-04-23 10:31:22 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 99
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Fetching page 115 with size 50
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 111
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Fetching page 116 with size 50
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 112
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Fetching page 117 with size 50
[2025-04-23 10:31:23 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 113
[2025-04-23 10:31:24 +0300] [256867] [INFO] <__main__>: Fetching page 118 with size 50
[2025-04-23 10:31:24 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 114
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Fetching page 119 with size 50
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 117
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Fetching page 120 with size 50
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 116
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Fetching page 121 with size 50
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 118
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Fetching page 122 with size 50
[2025-04-23 10:31:25 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 115
[2025-04-23 10:31:26 +0300] [256867] [INFO] <__main__>: Fetching page 123 with size 50
[2025-04-23 10:31:26 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 107
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Fetching page 124 with size 50
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 121
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Fetching page 125 with size 50
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 123
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Fetching page 126 with size 50
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 120
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Fetching page 127 with size 50
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Fetching page 128 with size 50
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 119
[2025-04-23 10:31:27 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 122
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Fetching page 129 with size 50
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 124
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Fetching page 130 with size 50
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 128
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Fetching page 131 with size 50
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 125
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Fetching page 132 with size 50
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 126
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Fetching page 133 with size 50
[2025-04-23 10:31:28 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 127
[2025-04-23 10:31:29 +0300] [256867] [INFO] <__main__>: Fetching page 134 with size 50
[2025-04-23 10:31:29 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 129
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Fetching page 135 with size 50
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 131
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Fetching page 136 with size 50
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 132
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Fetching page 137 with size 50
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 130
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Fetching page 138 with size 50
[2025-04-23 10:31:30 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 133
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Fetching page 139 with size 50
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 134
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Fetching page 140 with size 50
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 136
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Fetching page 141 with size 50
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 135
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Fetching page 142 with size 50
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 137
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Fetching page 143 with size 50
[2025-04-23 10:31:31 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 138
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Fetching page 144 with size 50
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Fetching page 145 with size 50
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 140
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 141
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Fetching page 146 with size 50
[2025-04-23 10:31:32 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 139
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Fetching page 147 with size 50
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 144
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Fetching page 148 with size 50
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 145
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Fetching page 149 with size 50
[2025-04-23 10:31:34 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 146
[2025-04-23 10:31:35 +0300] [256867] [INFO] <__main__>: Fetching page 150 with size 50
[2025-04-23 10:31:35 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 148
[2025-04-23 10:31:36 +0300] [256867] [INFO] <__main__>: Fetching page 151 with size 50
[2025-04-23 10:31:36 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 149
[2025-04-23 10:31:36 +0300] [256867] [INFO] <__main__>: Fetching page 152 with size 50
[2025-04-23 10:31:36 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 147
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Fetching page 153 with size 50
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 150
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Fetching page 154 with size 50
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 152
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Fetching page 155 with size 50
[2025-04-23 10:31:37 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 151
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Fetching page 156 with size 50
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 142
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Fetching page 157 with size 50
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 143
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Fetching page 158 with size 50
[2025-04-23 10:31:38 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 153
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Fetching page 159 with size 50
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 156
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 158
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Fetching page 160 with size 50
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Fetching page 161 with size 50
[2025-04-23 10:31:39 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 157
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Fetching page 162 with size 50
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Fetching page 163 with size 50
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 159
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Fetching page 164 with size 50
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 160
[2025-04-23 10:31:41 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 161
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Fetching page 165 with size 50
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 162
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Fetching page 166 with size 50
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 164
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Fetching page 167 with size 50
[2025-04-23 10:31:42 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 163
[2025-04-23 10:31:43 +0300] [256867] [INFO] <__main__>: Fetching page 168 with size 50
[2025-04-23 10:31:43 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 165
[2025-04-23 10:31:44 +0300] [256867] [INFO] <__main__>: Fetching page 169 with size 50
[2025-04-23 10:31:44 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 155
[2025-04-23 10:31:44 +0300] [256867] [INFO] <__main__>: Fetching page 170 with size 50
[2025-04-23 10:31:44 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 154
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Fetching page 171 with size 50
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 168
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Fetching page 172 with size 50
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 169
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Fetching page 173 with size 50
[2025-04-23 10:31:45 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 170
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Fetching page 174 with size 50
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 171
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Fetching page 175 with size 50
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Fetching page 176 with size 50
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 173
[2025-04-23 10:31:47 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 172
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Fetching page 177 with size 50
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 174
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Fetching page 178 with size 50
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 166
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Fetching page 179 with size 50
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 176
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Fetching page 180 with size 50
[2025-04-23 10:31:48 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 167
[2025-04-23 10:31:49 +0300] [256867] [INFO] <__main__>: Fetching page 181 with size 50
[2025-04-23 10:31:49 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 177
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Fetching page 182 with size 50
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 180
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Fetching page 183 with size 50
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 179
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Fetching page 184 with size 50
[2025-04-23 10:31:50 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 178
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Fetching page 185 with size 50
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 181
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Fetching page 186 with size 50
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 182
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Fetching page 187 with size 50
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 183
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Fetching page 188 with size 50
[2025-04-23 10:31:51 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 184
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Fetching page 189 with size 50
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 185
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Fetching page 190 with size 50
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 187
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Fetching page 191 with size 50
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 188
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Fetching page 192 with size 50
[2025-04-23 10:31:52 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 186
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Fetching page 193 with size 50
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 189
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Fetching page 194 with size 50
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 190
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Fetching page 195 with size 50
[2025-04-23 10:31:53 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 175
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Fetching page 196 with size 50
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 191
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Fetching page 197 with size 50
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 192
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Fetching page 198 with size 50
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 193
[2025-04-23 10:31:54 +0300] [256867] [INFO] <__main__>: Fetching page 199 with size 50
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 194
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Fetching page 200 with size 50
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 195
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Fetching page 201 with size 50
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Fetching page 202 with size 50
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 196
[2025-04-23 10:31:55 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 197
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Fetching page 203 with size 50
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 200
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Fetching page 204 with size 50
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Fetching page 205 with size 50
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 199
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 198
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Fetching page 206 with size 50
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 202
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Fetching page 207 with size 50
[2025-04-23 10:31:56 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 201
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Fetching page 208 with size 50
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 205
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Fetching page 209 with size 50
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 203
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Fetching page 210 with size 50
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 204
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Fetching page 211 with size 50
[2025-04-23 10:31:57 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 207
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Fetching page 212 with size 50
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 208
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Fetching page 213 with size 50
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 209
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Fetching page 214 with size 50
[2025-04-23 10:31:58 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 210
[2025-04-23 10:31:59 +0300] [256867] [INFO] <__main__>: Fetching page 215 with size 50
[2025-04-23 10:31:59 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 211
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Fetching page 216 with size 50
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 214
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Fetching page 217 with size 50
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 213
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Fetching page 218 with size 50
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 212
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Fetching page 219 with size 50
[2025-04-23 10:32:00 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 215
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Fetching page 220 with size 50
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 217
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Fetching page 221 with size 50
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 216
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Fetching page 222 with size 50
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 218
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Fetching page 223 with size 50
[2025-04-23 10:32:01 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 219
[2025-04-23 10:32:02 +0300] [256867] [INFO] <__main__>: Fetching page 224 with size 50
[2025-04-23 10:32:02 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 206
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Fetching page 225 with size 50
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 222
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Fetching page 226 with size 50
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 221
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Fetching page 227 with size 50
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 220
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Fetching page 228 with size 50
[2025-04-23 10:32:03 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 224
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Fetching page 229 with size 50
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Fetching page 230 with size 50
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 226
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 225
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Fetching page 231 with size 50
[2025-04-23 10:32:04 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 227
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Fetching page 232 with size 50
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 228
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Fetching page 233 with size 50
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Fetching page 234 with size 50
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 231
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Fetching page 235 with size 50
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 230
[2025-04-23 10:32:05 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 229
[2025-04-23 10:32:06 +0300] [256867] [INFO] <__main__>: Fetching page 236 with size 50
[2025-04-23 10:32:06 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 232
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Fetching page 237 with size 50
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 234
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Fetching page 238 with size 50
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 233
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Fetching page 239 with size 50
[2025-04-23 10:32:07 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 235
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Fetching page 240 with size 50
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Fetching page 241 with size 50
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 236
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 223
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Fetching page 242 with size 50
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 239
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Fetching page 243 with size 50
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 238
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Fetching page 244 with size 50
[2025-04-23 10:32:08 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 237
[2025-04-23 10:32:09 +0300] [256867] [INFO] <__main__>: Fetching page 245 with size 50
[2025-04-23 10:32:09 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 241
[2025-04-23 10:32:09 +0300] [256867] [INFO] <__main__>: Fetching page 246 with size 50
[2025-04-23 10:32:09 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 240
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Fetching page 247 with size 50
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Fetching page 248 with size 50
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 244
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 243
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Fetching page 249 with size 50
[2025-04-23 10:32:10 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 242
[2025-04-23 10:32:11 +0300] [256867] [INFO] <__main__>: Fetching page 250 with size 50
[2025-04-23 10:32:11 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 245
[2025-04-23 10:32:11 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 247
[2025-04-23 10:32:11 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 249
[2025-04-23 10:32:11 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 248
[2025-04-23 10:32:15 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 246
[2025-04-23 10:32:17 +0300] [256867] [INFO] <__main__>: Received 50 profiles from page 250
[2025-04-23 10:32:30 +0300] [256867] [INFO] <__main__>: Saving 12500 profiles to indosat_profiles.csv
[2025-04-23 10:32:41 +0300] [256867] [INFO] <__main__>: Successfully saved profiles to indosat_profiles.csv
[2025-04-23 10:32:44 +0300] [256867] [INFO] <__main__>: Total profiles fetched: 12500
[2025-04-23 10:32:44 +0300] [256867] [INFO] <apscheduler.scheduler>: Scheduler has been shut down
[2025-05-26 11:28:07 +0300] [53858] [INFO] <root>: Logging configuration loaded
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Adding job tentatively -- it will be properly scheduled when the scheduler starts
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "run_cron_jobs_runnable_csv" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_token_" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "save_vodafone_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "indosat_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "indosat_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "flexi_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "montymobile_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "bayobab_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "flexi_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "flexiroamv2_allocate_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "bayobab_bulk_allocate_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "vodafone_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "expire_scripts" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "rest_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "expire_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "check_profile_expiry" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "customer_feedback_send_email" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "notify_profile_before_expiry" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "esimgo_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "flexi_check_available_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "notify_system_with_inventory_details" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "save_play_integrity_token" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "refill_old_bundles_flexiroam" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "run_manage_subscriptions" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "expire_bayobab_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Scheduler started
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "get_all_monty_reseller_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "telkomsel_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "telkomsel_bulk_allocate_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "retry_failed_reseller_notification" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "try_first_time_notification" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "lotusflare_save_profiles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "lotusflare_save_bundles" to job store "default"
[2025-05-26 11:28:07 +0300] [53858] [INFO] <apscheduler.scheduler>: Added job "lotusflare_bulk_allocate_profiles" to job store "default"
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:30:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "retry_failed_reseller_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:30:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Retry Failed Reseller Notification Scheduler Started
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:30:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <root>: method get_failed_notifications
[2025-05-26 11:30:00 +0300] [53858] [WARNING] <app_helpers.cron_helpers>: No vendor found with vendor name Monty Reseller ... hence skipping save_bundles
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable_csv (trigger: cron[minute='*/15'], next run at: 2025-05-26 11:45:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" executed successfully
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "try_first_time_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" (scheduled at 2025-05-26 11:30:00+03:00)
[2025-05-26 11:30:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Saving Bundles From Telkomsel
[2025-05-26 11:30:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: First Time Notification Scheduler Started
[2025-05-26 11:30:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Retrying Failed Reseller Notification on 0 affected notifications
[2025-05-26 11:30:00 +0300] [53858] [INFO] <root>: method get_vendor_info
[2025-05-26 11:30:00 +0300] [53858] [INFO] <root>: method get_first_time_notifications
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "retry_failed_reseller_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" executed successfully
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable_csv (trigger: cron[minute='*/15'], next run at: 2025-05-26 11:45:00 EEST)" executed successfully
[2025-05-26 11:30:00 +0300] [53858] [ERROR] <root>: Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:30:00 +0300] [53858] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_bundles, Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" executed successfully
[2025-05-26 11:30:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start First Time Notification on 0 affected notifications
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "try_first_time_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:40:00 EEST)" executed successfully
[2025-05-26 11:30:00 +0300] [53858] [INFO] <root>: running 0 scripts
[2025-05-26 11:30:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:35:00 EEST)" executed successfully
[2025-05-26 11:35:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:40:00 EEST)" (scheduled at 2025-05-26 11:35:00+03:00)
[2025-05-26 11:35:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-05-26 11:42:00 EEST)" (scheduled at 2025-05-26 11:35:00+03:00)
[2025-05-26 11:35:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Saving Profiles From Telkomsel
[2025-05-26 11:35:00 +0300] [53858] [INFO] <root>: method get_vendor
[2025-05-26 11:35:00 +0300] [53858] [INFO] <root>: method get_profiles_by_vendor
[2025-05-26 11:35:00 +0300] [53858] [INFO] <root>: running 0 scripts
[2025-05-26 11:35:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:40:00 EEST)" executed successfully
[2025-05-26 11:35:00 +0300] [53858] [INFO] <root>: method get_vendor_info
[2025-05-26 11:35:00 +0300] [53858] [ERROR] <root>: Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:35:00 +0300] [53858] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_profiles, Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor 
[2025-05-26 11:35:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-05-26 11:42:00 EEST)" executed successfully
[2025-05-26 11:39:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "telkomsel_bulk_allocate_profiles (trigger: cron[minute='*/13'], next run at: 2025-05-26 11:52:00 EEST)" (scheduled at 2025-05-26 11:39:00+03:00)
[2025-05-26 11:39:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Starting bulk allocation for Telkomsel profiles...
[2025-05-26 11:39:00 +0300] [53858] [INFO] <root>: method get_bundles_by_vendor
[2025-05-26 11:39:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "telkomsel_bulk_allocate_profiles (trigger: cron[minute='*/13'], next run at: 2025-05-26 11:52:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "expire_bayobab_bundles (trigger: cron[minute='*/20'], next run at: 2025-05-26 12:00:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "retry_failed_reseller_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:45:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "try_first_time_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" (scheduled at 2025-05-26 11:40:00+03:00)
[2025-05-26 11:40:00 +0300] [53858] [WARNING] <app_helpers.cron_helpers>: No vendor found with vendor name Monty Reseller ... hence skipping save_bundles
[2025-05-26 11:40:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Retry Failed Reseller Notification Scheduler Started
[2025-05-26 11:40:00 +0300] [53858] [INFO] <root>: method get_failed_notifications
[2025-05-26 11:40:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: First Time Notification Scheduler Started
[2025-05-26 11:40:00 +0300] [53858] [INFO] <root>: running 0 scripts
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "get_all_monty_reseller_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Saving Bundles From Telkomsel
[2025-05-26 11:40:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Retrying Failed Reseller Notification on 0 affected notifications
[2025-05-26 11:40:00 +0300] [53858] [INFO] <root>: method get_first_time_notifications
[2025-05-26 11:40:00 +0300] [53858] [INFO] <root>: method get_vendor_info
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "run_cron_jobs_runnable (trigger: cron[minute='*/5'], next run at: 2025-05-26 11:45:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "retry_failed_reseller_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start First Time Notification on 0 affected notifications
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "expire_bayobab_bundles (trigger: cron[minute='*/20'], next run at: 2025-05-26 12:00:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "try_first_time_notification (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" executed successfully
[2025-05-26 11:40:00 +0300] [53858] [ERROR] <root>: Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:40:00 +0300] [53858] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_bundles, Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:40:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_bundles (trigger: cron[minute='*/10'], next run at: 2025-05-26 11:50:00 EEST)" executed successfully
[2025-05-26 11:42:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Running job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-05-26 11:49:00 EEST)" (scheduled at 2025-05-26 11:42:00+03:00)
[2025-05-26 11:42:00 +0300] [53858] [INFO] <app_helpers.cron_helpers>: Start Saving Profiles From Telkomsel
[2025-05-26 11:42:00 +0300] [53858] [INFO] <root>: method get_vendor
[2025-05-26 11:42:00 +0300] [53858] [INFO] <root>: method get_profiles_by_vendor
[2025-05-26 11:42:00 +0300] [53858] [INFO] <root>: method get_vendor_info
[2025-05-26 11:42:00 +0300] [53858] [ERROR] <root>: Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor
[2025-05-26 11:42:00 +0300] [53858] [ERROR] <app_helpers.cron_helpers>: Error on telkomsel_save_profiles, Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor 
[2025-05-26 11:42:00 +0300] [53858] [INFO] <apscheduler.executors.default>: Job "telkomsel_save_profiles (trigger: cron[minute='*/7'], next run at: 2025-05-26 11:49:00 EEST)" executed successfully

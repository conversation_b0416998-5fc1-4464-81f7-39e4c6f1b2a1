[2025-04-24 10:42:35 +0300] [28575] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Configuring MongoDB connection for environment: test
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:42:35 +0300] [28575] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:42:36 +0300] [28575] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "[32mGET / HTTP/1.1[0m" 302 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /login/ HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 10:42:40 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:40] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:42:42 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:42] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-04-24 10:42:52 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:52] "GET / HTTP/1.1" 200 -
[2025-04-24 10:42:52 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:52] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 10:42:53 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 10:42:55 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:55] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:42:55 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:55] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 10:42:55 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:55] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:42:55 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:55] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 10:42:55 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:42:55] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 10:43:04 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:04] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 10:43:05 +0300] [28575] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:05] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-24 10:43:16 +0300] [28803] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Configuring MongoDB connection for environment: test
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:43:16 +0300] [28803] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:43:16 +0300] [28803] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:43:26 +0300] [28803] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:26] "[35m[1mGET /promo_code/ HTTP/1.1[0m" 500 -
[2025-04-24 10:43:26 +0300] [28803] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:26] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:43:36 +0300] [28803] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:36] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:43:46 +0300] [28803] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:43:46] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:44:03 +0300] [28979] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:44:03 +0300] [28979] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:44:03 +0300] [28979] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:44:06 +0300] [28979] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:44:06] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:44:13 +0300] [28979] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:44:13] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:44:34 +0300] [29186] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:44:34 +0300] [29186] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:44:35 +0300] [29186] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:46:08 +0300] [29464] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:46:08 +0300] [29464] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:46:09 +0300] [29464] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:46:14 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:46:14] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:46:18 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:46:18] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:46:28 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:46:28] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:46:28 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:46:28] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:46:38 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:46:38] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:47:18 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:18] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:47:18 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:18] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:47:18 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:18] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:47:28 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:28] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:47:28 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:28] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:47:28 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:28] "[35m[1mGET / HTTP/1.1[0m" 500 -
[2025-04-24 10:47:38 +0300] [29464] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:47:38] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:47:49 +0300] [30060] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:47:49 +0300] [30060] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:47:50 +0300] [30060] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:48:31 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:48:31] "GET / HTTP/1.1" 200 -
[2025-04-24 10:48:59 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:48:59] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 10:49:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:10] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 10:49:17 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:17] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:36 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:36] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-24 10:49:37 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:49:37] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 10:50:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:50:10] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 10:50:10 +0300] [30060] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:50:10] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:51:31 +0300] [31506] [INFO] <root>: Logging configuration loaded
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 10:51:31 +0300] [31506] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 10:51:32 +0300] [31506] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 10:51:53 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:51:53] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 10:51:53 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:51:53] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:57:12 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:57:12] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 10:57:27 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:57:27] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 10:57:27 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:57:27] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 10:57:30 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 10:57:30] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:00:27 +0300] [31506] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:00:27] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:00:52 +0300] [33887] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:00:52 +0300] [33887] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:00:58 +0300] [33887] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:01:36 +0300] [33887] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:01:36] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:01:36 +0300] [33887] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:01:36] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:01:59 +0300] [34296] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:01:59 +0300] [34296] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:02:03 +0300] [34296] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:02:08 +0300] [34296] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:02:08] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:02:08 +0300] [34296] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:02:08] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:02:43 +0300] [34502] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:02:43 +0300] [34502] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:02:44 +0300] [34502] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:02:48 +0300] [34502] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:02:48] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:02:48 +0300] [34502] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:02:48] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:04:11 +0300] [34502] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:04:11] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:04:11 +0300] [34502] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:04:11] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:10:02 +0300] [36122] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:10:02 +0300] [36122] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:10:02 +0300] [36122] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET / HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:10:03 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:03] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /faq/ HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 11:10:06 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:06] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "GET /faq/new/?url=%2Ffaq%2F HTTP/1.1" 200 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:10:07 +0300] [36122] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:10:07] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:11:29 +0300] [36441] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:11:29 +0300] [36441] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:11:35 +0300] [36441] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:12:53 +0300] [36743] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:12:53 +0300] [36743] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:13:02 +0300] [36743] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET / HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 11:13:52 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:52] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:13:53 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:13:53] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-24 11:14:10 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:10] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 11:14:11 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:11] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:14:22 +0300] [36743] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:14:22] "GET /faq/new/?url=%2Ffaq%2F HTTP/1.1" 200 -
[2025-04-24 11:19:13 +0300] [38108] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:19:13 +0300] [38108] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:19:14 +0300] [38108] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:20:37 +0300] [38512] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:20:37 +0300] [38512] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:20:38 +0300] [38512] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:20:40 +0300] [38512] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:40] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:20:40 +0300] [38512] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:40] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:20:50 +0300] [38601] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:20:50 +0300] [38601] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:20:50 +0300] [38601] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:20:51 +0300] [38601] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:51] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:20:51 +0300] [38601] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:51] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:20:52 +0300] [38601] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:52] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:20:52 +0300] [38601] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:20:52] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:20:59 +0300] [38654] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:20:59 +0300] [38654] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:21:00 +0300] [38654] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:21:01 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:01] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:21:01 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:01] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:21:04 +0300] [38654] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:21:04] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:21:39 +0300] [38795] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:21:39 +0300] [38795] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:21:40 +0300] [38795] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:22:02 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:02] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:22:02 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:02] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET / HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:22:06 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:06] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /faq/ HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 11:22:09 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:09] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "GET /faq/new/?url=%2Ffaq%2F HTTP/1.1" 200 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:44 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:44] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 11:22:51 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:51] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:22:53 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:53] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:22:53 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:22:53] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:23:25 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:23:25] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:23:25 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:23:25] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:23:36 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:23:36] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:23:36 +0300] [38795] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:23:36] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:23:41 +0300] [39267] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:23:41 +0300] [39267] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:23:42 +0300] [39267] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:32:21 +0300] [42313] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:32:21 +0300] [42313] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:32:21 +0300] [42313] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET / HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:32:23 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:23] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /voucher_code/ HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:32:26 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:26] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 11:32:27 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:27] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:32:28 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:28] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:32:28 +0300] [42313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:32:28] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:38:47 +0300] [44653] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:38:47 +0300] [44653] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:38:56 +0300] [44715] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:38:56 +0300] [44715] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:39:34 +0300] [44851] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:39:34 +0300] [44851] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:39:42 +0300] [44893] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:39:42 +0300] [44893] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:39:47 +0300] [44925] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:39:47 +0300] [44925] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:39:47 +0300] [44925] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:39:48 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:48] "GET / HTTP/1.1" 200 -
[2025-04-24 11:39:48 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:48] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:39:49 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:49] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 11:39:51 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:51] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 11:39:53 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:53] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:39:53 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:39:53] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:40:23 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:23] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:40:23 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:23] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "GET /voucher_code_logs/ HTTP/1.1" 200 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 11:40:35 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:35] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-24 11:40:36 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:36] "[35m[1mGET /voucher_code_logs/new/?url=%2Fvoucher_code_logs%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:40:36 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:36] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 11:40:38 +0300] [44925] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:40:38] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:40:59 +0300] [45313] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:40:59 +0300] [45313] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:41:00 +0300] [45313] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:41:40 +0300] [45313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:41:40] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:41:40 +0300] [45313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:41:40] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:41:40 +0300] [45313] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:41:40] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:43:51 +0300] [45851] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:43:51 +0300] [45851] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:43:52 +0300] [45851] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 11:43:57 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:43:57] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 11:44:03 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:44:03] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:44:03 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:44:03] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:51:00 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:51:00] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:51:00 +0300] [45851] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:51:00] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 11:53:09 +0300] [47359] [INFO] <root>: Logging configuration loaded
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 11:53:09 +0300] [47359] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 11:53:10 +0300] [47359] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 11:55:30 +0300] [47359] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:55:30] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 11:55:30 +0300] [47359] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 11:55:30] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 12:03:44 +0300] [51690] [INFO] <root>: Logging configuration loaded
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 12:03:44 +0300] [51690] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 12:03:44 +0300] [51690] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET / HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 12:03:45 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:45] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 12:03:48 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:48] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 12:03:49 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:49] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 12:03:49 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:03:49] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[32mGET / HTTP/1.1[0m" 302 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "GET /login/ HTTP/1.1" 200 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:52 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:52] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "GET / HTTP/1.1" 200 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:53 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:53] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 12:24:55 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:55] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:24:56 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:56] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 12:24:56 +0300] [51690] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:24:56] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 12:25:39 +0300] [55298] [INFO] <root>: Logging configuration loaded
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 12:25:39 +0300] [55298] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 12:25:40 +0300] [55298] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:42 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:42] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-24 12:25:43 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:43] "GET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1" 200 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "GET /static/js/jquery.richtext.js HTTP/1.1" 200 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "GET /static/admin/vendor/select2/select2.png HTTP/1.1" 200 -
[2025-04-24 12:25:44 +0300] [55298] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:25:44] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-24 12:30:49 +0300] [57300] [INFO] <root>: Logging configuration loaded
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 12:30:49 +0300] [57300] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 12:30:50 +0300] [57300] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET / HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 12:30:51 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:51] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 12:30:53 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:53] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 12:30:55 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:55] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 12:30:55 +0300] [57300] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 12:30:55] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 16:15:50 +0300] [110167] [INFO] <root>: Logging configuration loaded
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 16:15:50 +0300] [110167] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 16:15:51 +0300] [110167] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "[32mGET / HTTP/1.1[0m" 302 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /login/ HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 16:15:52 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:52] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET / HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:53 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:53] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-24 16:15:55 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:55] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-24 16:15:56 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:56] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 16:15:56 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:56] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 16:15:59 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:15:59] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 16:16:00 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:16:00] "[35m[1mGET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1[0m" 500 -
[2025-04-24 16:16:00 +0300] [110167] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:16:00] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-24 16:17:52 +0300] [116594] [INFO] <root>: Logging configuration loaded
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Configuring login manager settings
[2025-04-24 16:17:52 +0300] [116594] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-24 16:17:52 +0300] [116594] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /promo_code/new/?url=%2Fpromo_code%2F HTTP/1.1" 200 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /static/js/jquery.richtext.js HTTP/1.1" 200 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /static/admin/vendor/select2/select2.png HTTP/1.1" 200 -
[2025-04-24 16:17:57 +0300] [116594] [INFO] <werkzeug>: 127.0.0.1 - - [24/Apr/2025 16:17:57] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-25 15:58:31 +0300] [271176] [INFO] <root>: Logging configuration loaded
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 15:58:31 +0300] [271176] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 15:58:32 +0300] [271176] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "[32mGET / HTTP/1.1[0m" 302 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /login/ HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 15:58:33 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET / HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-25 15:58:34 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:34] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-25 15:58:38 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:38] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "GET /static/admin/vendor/select2/select2.png HTTP/1.1" 200 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "GET /promo_code/?flt1_8=1 HTTP/1.1" 200 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-25 15:58:40 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:40] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 15:58:41 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:41] "[35m[1mGET /promo_code/generate_link/?id=671f4036809a7acb6a04f39f&url=%2Fpromo_code%2F%3Fflt0_8%3D1 HTTP/1.1[0m" 500 -
[2025-04-25 15:58:41 +0300] [271176] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:58:41] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-04-25 15:59:36 +0300] [272418] [INFO] <root>: Logging configuration loaded
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 15:59:36 +0300] [272418] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 15:59:36 +0300] [272418] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET / HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 15:59:37 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:37] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "GET /promo_code/generate_link/?id=671f4036809a7acb6a04f39f&url=%2Fpromo_code%2F%3Fflt0_8%3D1 HTTP/1.1" 200 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 15:59:39 +0300] [272418] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 15:59:39] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 16:02:16 +0300] [273048] [INFO] <root>: Logging configuration loaded
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 16:02:16 +0300] [273048] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 16:02:17 +0300] [273048] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 16:05:18 +0300] [274401] [INFO] <root>: Logging configuration loaded
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 16:05:18 +0300] [274401] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 16:05:19 +0300] [274401] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /promo_code/ HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-04-25 16:05:27 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:27] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-25 16:05:29 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:29] "GET /static/admin/vendor/select2/select2.png HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /promo_code/?flt1_8=1 HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:30 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:30] "[36mGET /static/admin/vendor/select2/select2.png HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "GET /promo_code/generate_link/?id=671f4036809a7acb6a04f39f&url=%2Fpromo_code%2F%3Fflt0_8%3D1 HTTP/1.1" 200 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-04-25 16:05:44 +0300] [274401] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:05:44] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-04-25 16:10:24 +0300] [276013] [INFO] <root>: Logging configuration loaded
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 16:10:24 +0300] [276013] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 16:10:25 +0300] [276013] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /promo_code/generate_link/?id=671f4036809a7acb6a04f39f&url=%2Fpromo_code%2F%3Fflt0_8%3D1 HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-04-25 16:10:32 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:32] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-04-25 16:10:33 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:33] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-04-25 16:10:33 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:33] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-04-25 16:10:33 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:33] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-04-25 16:10:33 +0300] [276013] [INFO] <werkzeug>: 127.0.0.1 - - [25/Apr/2025 16:10:33] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-04-25 16:15:18 +0300] [276938] [INFO] <root>: Logging configuration loaded
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 16:15:18 +0300] [276938] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 16:15:19 +0300] [276938] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-25 16:18:21 +0300] [277982] [INFO] <root>: Logging configuration loaded
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Configuring login manager settings
[2025-04-25 16:18:21 +0300] [277982] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-04-25 16:18:22 +0300] [277982] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-04-29 11:17:08 +0300] [464829] [INFO] <root>: Logging configuration loaded
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Initialized Flask application and CSRF protection
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Configuring MongoDB connection for environment: local
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Applied non-production MongoDB settings
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Initializing MongoDB connection.
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Successfully established MongoDB connection
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Configuring login manager settings
[2025-04-29 11:17:08 +0300] [464829] [INFO] <app_extra>: Successfully configured login manager and CSRF protection
[2025-05-13 16:36:49 +0300] [123818] [INFO] <root>: Logging configuration loaded
[2025-05-13 16:36:49 +0300] [123818] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-13 17:31:01 +0300] [137571] [INFO] <root>: Logging configuration loaded
[2025-05-13 17:31:01 +0300] [137571] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-13 17:31:01 +0300] [137571] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "[32mGET / HTTP/1.1[0m" 302 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /login/ HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-13 17:31:03 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:03] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET / HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-13 17:31:05 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:05] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-05-13 17:31:07 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:07] "[35m[1mGET /unlimited-bundle-metadata/ HTTP/1.1[0m" 500 -
[2025-05-13 17:31:08 +0300] [137571] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:31:08] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-13 17:34:54 +0300] [140379] [INFO] <root>: Logging configuration loaded
[2025-05-13 17:34:54 +0300] [140379] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-13 17:34:55 +0300] [140379] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-13 17:34:57 +0300] [140379] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:34:57] "[35m[1mGET /unlimited-bundle-metadata/ HTTP/1.1[0m" 500 -
[2025-05-13 17:34:57 +0300] [140379] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:34:57] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-13 17:35:50 +0300] [140580] [INFO] <root>: Logging configuration loaded
[2025-05-13 17:35:50 +0300] [140580] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-13 17:35:50 +0300] [140580] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /unlimited-bundle-metadata/ HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:51 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:51] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "GET /unlimited-bundle-metadata/new/?url=%2Funlimited-bundle-metadata%2F HTTP/1.1" 200 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "GET /static/js/jquery.richtext.js HTTP/1.1" 200 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-13 17:35:53 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:53] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-13 17:35:58 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:35:58] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[32mPOST /unlimited-bundle-metadata/new/?url=%2Funlimited-bundle-metadata%2F HTTP/1.1[0m" 302 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "GET /unlimited-bundle-metadata/ HTTP/1.1" 200 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-13 17:36:14 +0300] [140580] [INFO] <werkzeug>: 127.0.0.1 - - [13/May/2025 17:36:14] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-05-14 13:22:47 +0300] [161204] [INFO] <root>: Logging configuration loaded
[2025-05-14 13:22:47 +0300] [161204] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-14 13:22:47 +0300] [161204] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "[32mGET / HTTP/1.1[0m" 302 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /login/ HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-14 13:22:49 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:49] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET / HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:50 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:50] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "GET /create_bundle/ HTTP/1.1" 200 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:54 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:54] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:22:57 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:57] "GET /get_zones_by_vendor?vendor=Orange HTTP/1.1" 200 -
[2025-05-14 13:22:57 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:22:57] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-14 13:23:14 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:23:14] "GET /countries-by-zone?zone=WorldWithoutEuropeZone2 HTTP/1.1" 200 -
[2025-05-14 13:23:16 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:23:16] "GET /countries-by-zone?zone=Europe HTTP/1.1" 200 -
[2025-05-14 13:23:17 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:23:17] "GET /countries-by-zone?zone=World HTTP/1.1" 200 -
[2025-05-14 13:24:36 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:36] "GET /get_zones_by_vendor?vendor= HTTP/1.1" 200 -
[2025-05-14 13:24:36 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:36] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-14 13:24:37 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:37] "GET /get_zones_by_vendor?vendor=Orange HTTP/1.1" 200 -
[2025-05-14 13:24:37 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:37] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-14 13:24:39 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:39] "GET /countries-by-zone?zone=WorldWithoutEuropeZone1 HTTP/1.1" 200 -
[2025-05-14 13:24:40 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:40] "GET /countries-by-zone?zone=WorldWithoutEuropeZone2 HTTP/1.1" 200 -
[2025-05-14 13:24:48 +0300] [161204] [INFO] <root>: method get_vendor_info
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "POST /create_bundle/ HTTP/1.1" 200 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:24:51 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:24:51] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:25:39 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:25:39] "GET /get_zones_by_vendor?vendor= HTTP/1.1" 200 -
[2025-05-14 13:25:39 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:25:39] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-14 13:25:40 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:25:40] "GET /get_zones_by_vendor?vendor=Orange HTTP/1.1" 200 -
[2025-05-14 13:25:40 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:25:40] "POST /get_bundles HTTP/1.1" 200 -
[2025-05-14 13:25:43 +0300] [161204] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:25:43] "GET /countries-by-zone?zone=WorldWithoutEuropeZone2 HTTP/1.1" 200 -
[2025-05-14 13:25:44 +0300] [161204] [INFO] <root>: method get_vendor_info
[2025-05-14 13:25:49 +0300] [162073] [INFO] <root>: Logging configuration loaded
[2025-05-14 13:25:49 +0300] [162073] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-14 13:25:51 +0300] [162073] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-14 13:25:52 +0300] [162073] [INFO] <root>: method get_vendor_info
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET / HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-14 13:26:44 +0300] [162073] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:44] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-05-14 13:26:50 +0300] [162406] [INFO] <root>: Logging configuration loaded
[2025-05-14 13:26:50 +0300] [162406] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-14 13:26:50 +0300] [162406] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "GET / HTTP/1.1" 200 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "GET / HTTP/1.1" 200 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:52 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:52] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /global-bundles/ HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-05-14 13:26:58 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:58] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "GET /global-bundles/?sort=0 HTTP/1.1" 200 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:26:59 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:26:59] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "GET /global-bundles/?sort=0&desc=1 HTTP/1.1" 200 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:00 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:00] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "GET /global-bundles/edit/?id=68246fb08e1dd50463836bb7&url=%2Fglobal-bundles%2F%3Fsort%3D0%26desc%3D1 HTTP/1.1" 200 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "GET /static/js/jquery.richtext.js HTTP/1.1" 200 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:06 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:06] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:07 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:07] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:07 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:07] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:07 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:07] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:07 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:07] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:07 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:07] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[32mPOST /global-bundles/edit/?id=68246fb08e1dd50463836bb7&url=%2Fglobal-bundles%2F%3Fsort%3D0%26desc%3D1 HTTP/1.1[0m" 302 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "GET /global-bundles/?sort=0&desc=1 HTTP/1.1" 200 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:15 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:15] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-05-14 13:27:16 +0300] [162406] [INFO] <werkzeug>: 127.0.0.1 - - [14/May/2025 13:27:16] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:34:26 +0300] [105786] [INFO] <root>: Logging configuration loaded
[2025-05-22 16:34:26 +0300] [105786] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-22 16:34:27 +0300] [105786] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-22 16:34:39 +0300] [105838] [INFO] <root>: Logging configuration loaded
[2025-05-22 16:34:39 +0300] [105838] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-22 16:34:40 +0300] [105838] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-22 16:39:33 +0300] [107897] [INFO] <root>: Logging configuration loaded
[2025-05-22 16:39:33 +0300] [107897] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-22 16:39:33 +0300] [107897] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "[32mGET / HTTP/1.1[0m" 302 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /login/ HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-22 16:39:37 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:37] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET / HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-22 16:39:38 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:38] "GET /static/favicon.ico HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /cruise-bundles/ HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/fonts/icomoon.ttf?ryfr0a HTTP/1.1" 200 -
[2025-05-22 16:39:41 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:41] "GET /static/fonts/feather-webfont.woff?t=1501841394106 HTTP/1.1" 200 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "GET /cruise-bundles/edit/?id=67dabb22b9d1ffe67ccfde50&url=%2Fcruise-bundles%2F HTTP/1.1" 200 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "GET /static/css/richtext.min.css HTTP/1.1" 200 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "GET /static/js/jquery.richtext.js HTTP/1.1" 200 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:43 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:43] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[32mPOST /cruise-bundles/edit/?id=67dabb22b9d1ffe67ccfde50&url=%2Fcruise-bundles%2F HTTP/1.1[0m" 302 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "GET /cruise-bundles/ HTTP/1.1" 200 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/js/filters.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/js/form.js HTTP/1.1[0m" 304 -
[2025-05-22 16:39:46 +0300] [107897] [INFO] <werkzeug>: 127.0.0.1 - - [22/May/2025 16:39:46] "[36mGET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-22 16:42:08 +0300] [108516] [INFO] <root>: Logging configuration loaded
[2025-05-22 16:42:08 +0300] [108516] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-22 16:42:09 +0300] [108516] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-22 16:53:42 +0300] [112197] [INFO] <root>: Logging configuration loaded
[2025-05-22 16:53:42 +0300] [112197] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-22 16:53:42 +0300] [112197] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-23 15:55:34 +0300] [249702] [INFO] <root>: Logging configuration loaded
[2025-05-23 15:55:34 +0300] [249702] [INFO] <app_extra>: Connecting to mongo using MongoEngine
[2025-05-23 15:55:34 +0300] [249702] [INFO] <werkzeug>:  * Running on http://127.0.0.1:5000 (Press CTRL+C to quit)
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "[32mGET / HTTP/1.1[0m" 302 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /login/ HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "[35m[1mGET /static/logo2.png HTTP/1.1[0m" 500 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/css/dashboard.css HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/bootstrap/bootstrap3/js/bootstrap.min.js?v=3.3.5 HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-23 15:55:35 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:35] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[32mPOST /login/ HTTP/1.1[0m" 302 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET / HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:36 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:36] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /notificationhistory/ HTTP/1.1" 200 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/css/bootstrap.min.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/css/bootstrap.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/css/icons.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/js/popper.min.js HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/js/jquery-3.5.1.min.js HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/js/bootstrap.js HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "[36mGET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1[0m" 304 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-23 15:55:38 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:55:38] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /notificationhistory/ HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/css/bootstrap.min.css HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/css/icons.css HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/css/bootstrap.css HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/select2/select2.css?v=3.5.2 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/admin/css/bootstrap3/admin.css?v=1.1.1 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/select2/select2-bootstrap3.css?v=1.4.6 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/admin/css/bootstrap3/submenu.css HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/js/jquery-3.5.1.min.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker-bs3.css?v=1.3.22 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/js/popper.min.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/js/bootstrap.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/moment.min.js?v=2.22.2 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/admin/js/helpers.js?v=1.0.0 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/select2/select2.min.js?v=3.5.2 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "[35m[1mGET /static/admin/js/icon.js HTTP/1.1[0m" 500 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/vendor/bootstrap-daterangepicker/daterangepicker.js?v=1.3.22 HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/js/form.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/js/filters.js HTTP/1.1" 200 -
[2025-05-23 15:56:19 +0300] [249702] [INFO] <werkzeug>: 127.0.0.1 - - [23/May/2025 15:56:19] "GET /static/admin/admin/js/actions.js?v=1.0.0 HTTP/1.1" 200 -

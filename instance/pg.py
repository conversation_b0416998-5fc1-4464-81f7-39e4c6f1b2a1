import base64
import glob
import os

import yaml
from dotenv import load_dotenv

# def load_env_file(env_file):
#     """Load the .env file and return the variables as a dictionary."""
#     load_dotenv(env_file)
#     return {key: os.getenv(key) for key in os.environ if os.getenv(key) is not None}


def load_env_file(env_file):
    """
    Parse the .env file and return the variables as a dictionary.
    """
    env_vars = {}
    try:
        with open(env_file, "r") as file:
            for line in file:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key.strip()] = value.strip()
    except FileNotFoundError:
        print(f"Error: File {env_file} not found.")
    except Exception as e:
        print(f"Error reading {env_file}: {e}")
    return env_vars


def encode_base64(value):
    """Encode a string value to Base64."""
    return base64.b64encode(value.encode()).decode()


# def replace_yaml_values(yaml_file, env_vars, output_file):
#     """Replace the values in the YAML file using the provided environment variables and create a new file."""
#     with open(yaml_file, "r") as file:
#         yaml_content = yaml.safe_load(file)
#
#     if "data" in yaml_content:
#         for key, value in yaml_content["data"].items():
#             if key in env_vars:
#                 yaml_content["data"][key] = encode_base64(env_vars[key])
#
#     with open(output_file, "w") as file:
#         yaml.dump(yaml_content, file, default_flow_style=False)

def replace_yaml_values(yaml_file, env_vars, output_file, project_name):
    """
    Replace the values in the YAML file using the provided environment variables
    and create a new file, removing non-matching variables from the YAML.
    Update the metadata.name field to match the project name.
    """
    try:
        # Load the YAML content
        with open(yaml_file, "r") as file:
            yaml_content = yaml.safe_load(file)
        if project_name == "esimResellerMicroservice":
            print("in reseller now")
        # Filter and update the YAML content
        if "data" in yaml_content:
            # yaml_content["data"] = {
            #     key: encode_base64(env_vars[key]) for key in env_vars if key in yaml_content["data"]
            # }
            yaml_content["data"] = {
                key: encode_base64(str(env_vars[key]).strip('"')) for key in env_vars
            }

        # Update the metadata name to match the project name
        if "metadata" in yaml_content and "name" in yaml_content["metadata"]:
            yaml_content["metadata"]["name"] = f"{project_name}-secret"

        # Write the updated YAML to the output file
        with open(output_file, "w") as file:
            yaml.dump(yaml_content, file, default_flow_style=False)

        print(f"Successfully updated {output_file} for project {project_name}")
    except Exception as e:
        print(f"Error updating {yaml_file}: {e}")

def process_environment(env_file, yaml_file, environment, output_dir, project_name):
    """
    Process the environment by loading the env file, replacing YAML values,
    and removing environment variables after use.
    """
    # Ensure the output directory exists
    os.makedirs(output_dir, exist_ok=True)

    output_file = os.path.join(output_dir, f"{environment}_secrets.yaml")

    # Load environment variables from the env file
    env_vars = load_env_file(env_file)

    # Replace YAML values using the loaded environment variables
    replace_yaml_values(yaml_file, env_vars, output_file, project_name)


    # Print success message
    print(f"Created new file {output_file} with updated values using {env_file}")


def main():
    base_path = "/home/<USER>/PycharmProjects"
    yaml_file = "/home/<USER>/Desktop/secret.yaml"

    # Find all projects starting with "esim" (case-insensitive)
    projects = [project for project in glob.glob(f"{base_path}/esim*", recursive=False) if os.path.isdir(project)]

    environments = {
        "dev": ".env.dev.j2",
        "qa": ".env.stage.j2",
        "uat": ".env.uat.j2",
        "production": ".env.production.j2",
        "lux_stage": ".env.lux_stage.j2",
        "lux_dev": ".env.lux_dev.j2",
    }

    for project in projects:
        project_name = os.path.basename(project)
        project_output_dir = os.path.join(base_path, f"{project_name}_secrets")
        templates_path = os.path.join(project, "Deployment/Ansible/templates")

        for environment, env_file_name in environments.items():
            env_file = os.path.join(templates_path, env_file_name)

            # Check if the environment file exists before processing
            if os.path.exists(env_file):
                process_environment(env_file, yaml_file, environment, project_output_dir, project_name)
            else:
                print(f"Skipped: {env_file} does not exist.")


if __name__ == "__main__":
    main()

import json
import logging
import os
from pathlib import Path
from dotenv import load_dotenv

from src.cipher import Crypt
from email_validator import caching_resolver

controllers_with_rate_limit = [
    "LoginRegisterEmail",
    "VerifyEmailCiba",
    "LimitedLogin",
    "RefreshToken",
    "Logout",
    "AssignStripe",
    "ProfileSharing",
    "ValidatePromoAndReferral",
    "TranslatedMessages",
    "QrCodeEmailMobile",
    "QrCodeEmail",
    "CancelStripe",
    "BundlesByCountries",
]

current_file_directory_path = os.path.dirname(os.path.abspath(__file__))
firebase_config_directory = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../Deployment/Configs")
ENVIRONMENT = os.getenv("ENV", "test")

qa_tester_emails_list = []
tester_domain = "@husain.com"
firebase_config = {}
default_rate_limit = os.getenv("RATE_LIMIT", "5/15 minutes")

mongo_db_name_alias = os.getenv("MONGO_DBNAME_ALIAS")
mongo_host = os.getenv("MONGO_DEFAULT_HOST")
mongo_port = os.getenv("MONGO_DEFAULT_PORT")
operator_name = os.getenv("OPERATOR_NAME", "OPERATOR_NAME")
decrypted_ciba_callback_url = os.getenv("CIBA_CALLBACK_URL")
mongo_username = os.getenv("MONGODB_USERNAME")
mongo_password = os.getenv("MONGODB_PASSWORD")
default_limit = f"{os.getenv('DEFAULT_RATE_LIMIT', 30)} per minute"

print(f"ENV: {ENVIRONMENT}")

if ENVIRONMENT == "test":
    aes_key = os.getenv("ENCRPYPT_UKEY")
else:
    aes_key = os.getenv("ENCRPYPT_KEY")

cipher = Crypt(aes_key=aes_key)

if ENVIRONMENT == "stage":

    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )
    qa_tester_emails_list = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_qa.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)

elif ENVIRONMENT == "lux_stage":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )
    qa_tester_emails_list = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ]

    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_lux_qa.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)


elif ENVIRONMENT == "dev":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )

    firebase_config_path = os.path.join(firebase_config_directory, 'firebase_config_qa.json')
    with open(firebase_config_path, 'r') as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)

elif ENVIRONMENT == "lux_dev":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )

    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_qa.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)

elif ENVIRONMENT == "test":
    controllers_with_rate_limit = []
    default_limit = ""
    new_host_ = "mongodb://{0}/{1}?authSource=admin".format(mongo_host, mongo_db_name_alias)

    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_qa.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = value


elif ENVIRONMENT == "production":
    new_host_ = "mongodb+srv://{0}:{1}@{2}/{3}".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )
    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_prod.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)

    qa_tester_emails_list = ["<EMAIL>"]

elif ENVIRONMENT == "uat":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username, mongo_password, mongo_host, mongo_db_name_alias
    )
    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_uat.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)
    qa_tester_emails_list = ["<EMAIL>"]


else:
    new_host_ = "mongodb://{}:{}/{}".format(mongo_host, mongo_port, mongo_db_name_alias)
    firebase_config_path = os.path.join(firebase_config_directory, "firebase_config_qa.json")
    with open(firebase_config_path, "r") as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = cipher.decrypt(value)


signature_key = os.getenv("SIGNATURE_KEY")
front_end_url = os.getenv("FRONTEND_URL")
shop_plans_front_end_url = os.getenv("SHOP_PLANS_FRONT_END_URL")
decrypted_wp_qr_code = os.getenv("BACKEND_URL_WHATSAPP_QR_CODE")
wp_link = os.getenv("WP_URL")
promo_code_key = os.getenv("PROMO_CODE_KEY")
port = os.getenv("PORT")
ENC_FEEDBACK_KEY = os.getenv("ENC_FEEDBACK_KEY")

#   region montypay
merchant_key = os.getenv("MERCHANT_KEY")
merchant_pass = os.getenv("PASSWORD_KEY")
success_url_ = os.getenv("SUCCESS_URL")
cancel_url_ = os.getenv("CANCEL_URL")
#   endregion

#   region keycloak
keycloack_url = os.getenv("KEYCLOACK_URL")
keycloack_admin_url = os.getenv("KEYCLOACK_ADMIN_URL")
keycloack_client_id = os.getenv("KEYCLOACK_CLIENT_ID")
decrypted_login_ciba_url = os.getenv("EMAIL_CIBA_LOGIN")
keycloack_realm_name = os.getenv("KEYCLOACK_RELEAM_NAME")
keycloack_client_key = os.getenv("KEYCLOACK_CLIENT_KEY")
keycloack_user = os.getenv("KEYCLOACK_USER")
keycloack_password = os.getenv("KEYCLOACK_PASSWORD")
keycloack_admin_user = os.getenv("KEYCLOACK_ADMIN_USER")
keycloack_admin_password = os.getenv("KEYCLOACK_ADMIN_PASSWORD")
keycloack_admin_releam = os.getenv("KEYCLOACK_ADMIN_RELEAM")
keycloack_admin_key = os.getenv("KEYCLOACK_ADMIN_KEY")
keycloack_client_id_temporary = os.getenv("KEYCLOACK_CLIENT_ID_TEMP")
keycloack_client_key_temporary = os.getenv("KEYCLOACK_CLIENT_KEY_TEMP")
facebook_callback = os.getenv("FB_REDIRECT_URL")
gmail_callback = os.getenv("GMAIL_REDIRECT_URL")
#   endregion

#   region vodafone
vodafone_url = os.getenv("VODAFONE_URL")
vodafone_token = os.getenv("VODAFONE_TOKEN")
callback_url = os.getenv("CALLBACK_URL")
callback_get_iccid = callback_url + "/v2/get-profile"
#   endregion

#   region stripe
stripe_superadmin_username = os.getenv("STRIPE_USER_USERNAME")
stripe_superadmin_password = os.getenv("STRIPE_USER_PASSWORD")
stripe_url = os.getenv("STRIPE_URL")
api_key = os.getenv("STRIPE_API_KEY")
#   endregion

#   region reseller
reseller_url = os.getenv("RESELLER_URL")
reseller_super_admin_username = os.getenv("RESELLER_ADMIN_USERNAME")
reseller_super_admin_password = os.getenv("RESELLER_ADMIN_PASSWORD")
#   endregion


#   region esimgo
esimgo_url = os.getenv("ESIMGO_URL")
esim_go_token = os.getenv("ESIMGO_TOKEN")
#   endregion

#   region flexiroam
flexiroam_url = os.getenv("FLEXIROAM_URL")
flexiroam_username = os.getenv("FLEXIROAM_USERNAME")
flexiroam_password = os.getenv("FLEXIROAM_PASSWORD")
#   endregion

#   region montymobile
montymobile_url = os.getenv("MONTYMOBILE_URL")
montymobile_username = os.getenv("MONTYMOBILE_USERNAME")
montymobile_password = os.getenv("MONTYMOBILE_PASSWORD")
#   endregion
token_key = os.getenv("TOKEN_KEY")
internal_api_key = os.getenv("INTERNAL_API_KEY")


play_integrity_username = os.getenv("PLAYINTEGRITY_USERNAME")
play_integrity_password = os.getenv("PLAYINTEGRITY_PASSWORD")
play_integrity_url = os.getenv("PLAYINTEGRITY_URL")
application_name = os.getenv("APPLICATION_NAME")
package_name = os.getenv("PACKAGE_NAME")


# for all json files
def load_json_mapping(path):
    try:
        with open(path, "r", encoding="utf-8") as file_to_open:
            return json.loads(file_to_open.read())
    except FileNotFoundError:
        logging.error(" %s not found", path)
    except Exception as unexpected_error:
        logging.error("Error loading %s: %s", path, unexpected_error)
    return {}  # Return an empty dictionary if the file cannot be loaded


json_files = [
    f"{current_file_directory_path}/notif.json",
    f"{current_file_directory_path}/translations/translations_send_invoice_email.json",
    f"{current_file_directory_path}/translations/translations_send_topup_email.json",
    f"{current_file_directory_path}/translations/translations_consumption_email.json",
    f"{current_file_directory_path}/translations/translations_consumption_low_usage_email.json",
    f"{current_file_directory_path}/translations/translations_verification_email.json",
    f"{current_file_directory_path}/translations/translations_transaction_history.json",
]

mappings = {}

for file_path in json_files:
    mappings[file_path] = load_json_mapping(file_path)

notification_mapping = mappings.get(f"{current_file_directory_path}/notif.json")
verification_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_verification_email.json")
consumption_low_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_consumption_low_usage_email.json")
consumption_expiry_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_consumption_email.json")
send_topup_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_send_topup_email.json")
send_invoice_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_send_invoice_email.json")
transaction_history_mapping = mappings.get(f"{current_file_directory_path}/translations/translations_transaction_history.json")

firebase_ios_file = f"{firebase_config_directory}/{os.getenv('IOS_FIREBASE')}"


html_templates_path = "html_templates"


android_google_client_id = os.getenv("ANDROID_GOOGLE_CLIENT_ID", "")
android_google_client_key = os.getenv("ANDROID_GOOGLE_CLIENT_KEY", "")

indosat_username = os.getenv("INDOSAT_USERNAME", "")
indosat_password = os.getenv("INDOSAT_PASSWORD", "")
indosat_liscense_key = os.getenv("INDOSAT_LISCENSE_KEY", "")

indosat_url = os.getenv("INDOSAT_URL", "")


website_secret_key = os.getenv("WEBSITE_CAPTCHA", "")
public_key_path = f"{current_file_directory_path}/{os.getenv('PATH_PUBLIC')}"
private_key_path = f"{current_file_directory_path}/{os.getenv('PATH_PRIVATE')}"


recaptcha_secret_key = website_secret_key
email_dns_caching_resolver = caching_resolver(timeout=10)
debug_level = os.getenv("DEBUG_LEVEL", "INFO")


orange_username = os.getenv("ORANGE_USERNAME")
orange_password = os.getenv("ORANGE_PASSWORD")
orange_login_username = os.getenv("ORANGE_LOGIN_USERNAME")
orange_login_password = os.getenv("ORANGE_LOGIN_PASSWORD")

monty_reseller_url = os.getenv("MONTY_RESELLER_URL", "https://reseller.localrsp.com/api/v0")
monty_reseller_agent_username = os.getenv("MONTY_RESELLER_AGENT_USERNAME")
monty_reseller_agent_password = os.getenv("MONTY_RESELLER_AGENT_PASSWORD")

# branch_key = os.getenv('BRANCH_KEY')
flexi_api_key = os.getenv("FLEXIROAM_API_KEY")
flexiroam2_url = os.getenv("FLEXIROAM_URL_V2")

ms_graph_tenant_id = os.getenv("MS_GRAPH_TENANT_ID")
ms_graph_client_id = os.getenv("MS_GRAPH_CLIENT_ID")
ms_graph_client_secret = os.getenv("MS_GRAPH_CLIENT_SECRET")
outlook_admin_user_id = os.getenv("OUTLOOK_ADMIN_USER_ID")
outlook_webhook_notification_url = os.getenv("OUTLOOK_WEBHOOK_NOTIFICATION_URL")
outlook_webhook_lifecycle_notification_url = os.getenv("OUTLOOK_WEBHOOK_LIFECYCLE_NOTIFICATION_URL")
outlook_subscription_email = os.getenv("OUTLOOK_SUBSCRIPTION_EMAIL")
queue_max_size_webhook = os.getenv("QUEUE_MAX_SIZE_WEBHOOK", 1000)
max_workers_webhook = os.getenv("MAX_WORKERS_WEBHOOK", 5)
http_connection_timeout = os.getenv("HTTP_CONNECTION_TIMEOUT", 10)

bayobab_url = os.getenv("BAYOBAB_URL")
bayobab_username = os.getenv("BAYOBAB_USERNAME")
bayobab_password = os.getenv("BAYOBAB_PASSWORD")

es2plus_url = os.getenv("ES2PLUS_URL")
es2plus_fri = os.getenv("ES2PLUS_FRI")
es2plus_fci = os.getenv("ES2PLUS_FCI")

auth_email_otp_duration = int(os.getenv("AUTH_EMAIL_OTP_DURATION", 10))     # in minutes
bruteforce_email_otp_duration = int(os.getenv("BRUTEFORCE_EMAIL_OTP_DURATION", 24))     # in hours

telkomsel_authentication_url = os.getenv("TELKOMSEL_AUTHENTICATION_URL")
telkomsel_base_url = os.getenv("TELKOMSEL_BASE_URL")
telkomsel_username = os.getenv("TELKOMSEL_USERNAME")
telkomsel_password = os.getenv("TELKOMSEL_PASSWORD")
telkomsel_client_id = os.getenv("TELKOMSEL_CLIENT_ID")
telkomsel_client_secret = os.getenv("TELKOMSEL_CLIENT_SECRET")


#Lotus Flare configuration
lotus_base_url = os.getenv("LOTUS_FLARE_BASE_URL")
lotus_client_id = os.getenv("LOTUS_FLARE_CLIENT_ID")
lotus_client_key = os.getenv("LOTUS_FLARE_CLIENT_KEY")
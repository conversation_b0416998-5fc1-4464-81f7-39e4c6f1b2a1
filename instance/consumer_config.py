import json
import os
import logging

from dotenv import load_dotenv
from pathlib import Path

from app_helpers.encrypt_helper import Crypt
logger = logging.getLogger(__name__)

decrypt_helper = Crypt()

logging_path = "/home/<USER>/app/logs/activity.log"
ENVIRONMENT = os.getenv('ENV', 'test')

current_file_directory_path = os.path.dirname(os.path.abspath(__file__))
firebase_config_directory = os.path.join(current_file_directory_path, "../Deployment/Configs")

logger.info(f'ENV: {ENVIRONMENT}')

mongo_port = os.getenv('MONGO_DEFAULT_PORT', 27017)
mongo_dbname_alias = os.getenv('MONGO_DBNAME_ALIAS', "esim-b2c")
mongo_host = os.getenv('MONGO_DEFAULT_HOST', "127.0.0.1")
mongo_default_alias = os.getenv('MONGO_DEFAULT_ALIAS', 'default')
mongo_username = os.getenv('MONGODB_USERNAME')
mongo_password = os.getenv('MONGODB_PASSWORD')

firebase_config: dict = {}
firebase_config_path = os.path.join(firebase_config_directory, 'firebase_config.json')

try:
    aes_key = os.getenv('ENCRPYPT_KEY')
    with open(firebase_config_path, 'r') as f:
        encrypted_config: dict = json.loads(f.read())
        for key, value in encrypted_config.items():
            firebase_config[key] = decrypt_helper.decrypt(value, aes_key)
except FileNotFoundError as e:
    logger.warning("Could not find firebase config file at %s", firebase_config_path)

if ENVIRONMENT in ["dev", "stage", "uat"]:
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(mongo_username, mongo_password,
                                                                    mongo_host, mongo_dbname_alias)

elif ENVIRONMENT == 'production':
    new_host_ = "mongodb+srv://{0}:{1}@{2}/{3}".format(mongo_username, mongo_password,
                                                       mongo_host, mongo_dbname_alias)
else:
    new_host_ = "mongodb://{}:{}/{}".format(mongo_host, mongo_port, mongo_dbname_alias)

http_connection_timeout = os.getenv("HTTP_CONNECTION_TIMEOUT")
logging_path = "logs/"
debug_ = os.getenv('DEBUG')
lst_db_ = [{
    "ALIAS": mongo_default_alias,
    "DB": mongo_dbname_alias,
    "HOST": mongo_host,
    "PORT": int(mongo_port),
    "USERNAME": mongo_username,
    "PASSWORD": mongo_password
}]

flexiroam_url = os.getenv('FLEXIROAM_URL')
flexiroam_username = os.getenv('FLEXIROAM_USERNAME')
flexiroam_password = os.getenv('FLEXIROAM_PASSWORD')

esimgo_url = os.getenv('ESIMGO_URL')
esim_go_token = os.getenv('ESIMGO_TOKEN')
esimgo_vendor = os.getenv('ESIMGO_VENDOR')

decrypted_backend_url = os.getenv('DECRYPTED_BACKEND_URL')

vodafone_url = os.getenv('VODAFONE_URL')
vodafone_token = os.getenv('VODAFONE_TOKEN')
promo_code_key = os.getenv('PROMO_CODE_KEY')
critical_data_key = os.getenv('DATA_KEY')

front_end_url = os.getenv('FRONT_END_URL')

montymobile_url = os.getenv('MONTYMOBILE_URL')
montymobile_username = os.getenv('MONTYMOBILE_USERNAME')
montymobile_password = os.getenv('MONTYMOBILE_PASSWORD')
token_key = os.getenv('TOKEN_KEY')
internal_api_key = os.getenv('INTERNAL_API_KEY')

indosat_username = os.getenv('INDOSAT_USERNAME')
indosat_password = os.getenv('INDOSAT_PASSWORD')
indosat_liscense_key = os.getenv('INDOSAT_LISCENSE_KEY')
indosat_url = os.getenv('INDOSAT_URL')

callback_url = os.getenv('CALLBACK_URL')
callback_get_iccid = callback_url + "/v2/get-cron-profile"

flexi_api_key = os.getenv("FLEXIROAM_API_KEY")
flexiroam2_url = os.getenv("FLEXIROAM_URL_V2")
firebase_ios_file = f"{firebase_config_directory}/{os.getenv('IOS_FIREBASE')}"

outlook_subscription_email = os.getenv("OUTLOOK_SUBSCRIPTION_EMAIL")
orange_username = os.getenv('ORANGE_USERNAME')
orange_password = os.getenv('ORANGE_PASSWORD')
orange_login_username = os.getenv('ORANGE_LOGIN_USERNAME')
orange_login_password = os.getenv('ORANGE_LOGIN_PASSWORD')

bayobab_url = os.getenv("BAYOBAB_URL")
bayobab_username = os.getenv("BAYOBAB_USERNAME")
bayobab_password = os.getenv("BAYOBAB_PASSWORD")

es2plus_url = os.getenv("ES2PLUS_URL")
es2plus_fri = os.getenv("ES2PLUS_FRI")
es2plus_fci = os.getenv("ES2PLUS_FCI")

import os
from app_helpers.encrypt_helper import Crypt
import logging

decrypt_helper = Crypt()
current_file_directory_path = os.path.dirname(os.path.abspath(__file__))

ENVIRONMENT = os.getenv("ENV", "test")
print("Environment:", ENVIRONMENT)

if ENVIRONMENT == "test":
    encrypt_key = os.getenv("ENCRPYPT_UKEY")
else:
    encrypt_key = os.getenv("ENCRPYPT_KEY")

esim_go_transaction = "validate"
decrypted_db_name_alias = os.getenv("MONGO_DBNAME_ALIAS")
decrypted_host_alias = os.getenv("MONGO_DEFAULT_HOST")
decrypted_mongo_username = os.getenv("MONGODB_USERNAME")
decrypted_mongo_password = os.getenv("MONGODB_PASSWORD")
mongo_port = os.getenv("MONGO_DEFAULT_PORT")

if ENVIRONMENT == "stage":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        decrypted_mongo_username,
        decrypted_mongo_password,
        decrypted_host_alias,
        decrypted_db_name_alias,
    )
    os.system("mkdir -p /home/<USER>/files/logos")
    logos_directory = "/home/<USER>/files/logos/"


elif ENVIRONMENT == "dev":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        decrypted_mongo_username,
        decrypted_mongo_password,
        decrypted_host_alias,
        decrypted_db_name_alias,
    )
    os.system("mkdir -p /home/<USER>/files/logos")
    logos_directory = "/home/<USER>/files/logos/"


elif ENVIRONMENT == "production":
    new_host_ = "mongodb+srv://{0}:{1}@{2}/{3}".format(
        decrypted_mongo_username,
        decrypted_mongo_password,
        decrypted_host_alias,
        decrypted_db_name_alias,
    )
    os.system("mkdir -p /home/<USER>/files/logos")
    logos_directory = "/home/<USER>/files/logos/"


elif ENVIRONMENT == "uat":
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        decrypted_mongo_username,
        decrypted_mongo_password,
        decrypted_host_alias,
        decrypted_db_name_alias,
    )
    os.system("mkdir -p /home/<USER>/files/logos")
    logos_directory = "/home/<USER>/files/logos/"


elif ENVIRONMENT == "test":
    decrypted_mongo_default_host = os.getenv("MONGO_DEFAULT_HOST")
    new_host_ = "mongodb://{0}:{1}/{2}".format(
        decrypted_mongo_default_host,
        mongo_port,
        decrypted_db_name_alias,
    )

else:
    decrypted_mongo_default_host = os.getenv("MONGO_DEFAULT_HOST")
    decrypted_mongo_default_name_alias = os.getenv("MONGO_DEFAULT_ALIAS")
    new_host_ = "mongodb://{}:{}/{}".format(
        decrypted_mongo_default_host,
        mongo_port,
        decrypted_db_name_alias,
    )
    logos_directory = "files/logos/"
    debug_ = os.getenv("DEBUG")

keycloack_url = os.getenv("KEYCLOACK_URL")
keycloack_admin_url = os.getenv("KEYCLOACK_ADMIN_URL")
keycloack_client_id = os.getenv("KEYCLOAK_CLIENT_ID")
keycloack_realm_name = os.getenv("REALM_NAME")
keycloack_client_key = os.getenv("KEYCLOAK_CLIENT_KEY")
keycloack_user = os.getenv("KEYCLOACK_ADMIN_USER")
keycloack_password = os.getenv("KEYCLOACK_ADMIN_PASSWORD")
keycloack_admin_releam = os.getenv("ADMIN_REALM")
keycloack_admin_key = os.getenv("ADMIN_KEY")
keycloack_client_id_numeral = os.getenv("KEYCLOAK_CLIENT_ID_NUMERAL")

flexiroam_username = os.getenv("FLEXIROAM_USERNAME")
flexiroam_password = os.getenv("FLEXIROAM_PASSWORD")
flexiroam_url = os.getenv("FLEXIROAM_URL")

esimgo_url = os.getenv("ESIMGO_URL")
esim_go_token = os.getenv("ESIMGO_TOKEN")

vodafone_url = os.getenv("VODAFONE_URL")
vodafone_token = os.getenv("VODAFONE_TOKEN")

montymobile_url = os.getenv("MONTYMOBILE_URL")
montymobile_username = os.getenv("MONTYMOBILE_USERNAME")
montymobile_password = os.getenv("MONTYMOBILE_PASSWORD")

flexiroam2_url = os.getenv("FLEXIROAM_URL_V2")
flexi_api_key = os.getenv("FLEXIROAM_API_KEY")

indosat_url = os.getenv("INDOSAT_URL")
indosat_password = os.getenv("INDOSAT_PASSWORD")
indosat_username = os.getenv("INDOSAT_USERNAME")
indosat_liscense_key = os.getenv("INDOSAT_LISCENSE_KEY")

orange_username = os.getenv("ORANGE_USERNAME")
orange_password = os.getenv("ORANGE_PASSWORD")
orange_login_username = os.getenv("ORANGE_LOGIN_USERNAME")
orange_login_password = os.getenv("ORANGE_LOGIN_PASSWORD")

email_login_ = os.getenv("EMAIL_LOGIN")

email_username = os.getenv("EMAIL_USERNAME")
email_password = os.getenv("EMAIL_PASSWORD")
smtp_port = os.getenv("SMTP_PORT")
smtp_server = os.getenv("SMTP_SERVER")

password_key = os.getenv("PASSWORD_KEY")

callback_get_iccid = os.getenv("CALLBACK_GET_ICCID")
user_pass = os.getenv("ADMIN_PASS")
front_end_url = os.getenv("RESELLER_FRONT_END_URL")
promo_code_key = os.getenv("PROMO_CODE_KEY")

token_key = os.getenv("TOKEN_KEY")
support_email = os.getenv("SUPPORT_EMAIL", "<EMAIL>")
bucket_url = os.getenv("BUCKET_URL", "https://thereisnostagingbucket.com/esimapp/")

outlook_subscription_email = os.getenv("OUTLOOK_SUBSCRIPTION_EMAIL")

reseller_url = os.getenv("RESELLER_URL")
reseller_super_admin_username = os.getenv("RESELLER_ADMIN_USERNAME")
reseller_super_admin_password = os.getenv("RESELLER_ADMIN_PASSWORD")

bayobab_url = os.getenv("BAYOBAB_URL")
bayobab_username = os.getenv("BAYOBAB_USERNAME")
bayobab_password = os.getenv("BAYOBAB_PASSWORD")

es2plus_url = os.getenv("ES2PLUS_URL")
es2plus_fri = os.getenv("ES2PLUS_FRI")
es2plus_fci = os.getenv("ES2PLUS_FCI")

http_connection_timeout = int(os.getenv("HTTP_CONNECTION_TIMEOUT", 10))

telkomsel_authentication_url = os.getenv("TELKOMSEL_AUTHENTICATION_URL")
telkomsel_base_url = os.getenv("TELKOMSEL_BASE_URL")
telkomsel_username = os.getenv("TELKOMSEL_USERNAME")
telkomsel_password = os.getenv("TELKOMSEL_PASSWORD")
telkomsel_client_id = os.getenv("TELKOMSEL_CLIENT_ID")
telkomsel_client_secret = os.getenv("TELKOMSEL_CLIENT_SECRET")
captcha_secret_key = os.getenv("CAPTCHA_SECRET_KEY")
logging.debug("Consumer config initialized")

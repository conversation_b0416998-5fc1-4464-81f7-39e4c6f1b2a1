import os
from app_helpers.encrypt_helper import Crypt
from dotenv import load_dotenv
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)
decrypt_helper = Crypt()

ENVIRONMENT = os.getenv("ENV", "local")
logger.info("Environment : %s" , format(ENVIRONMENT))
mongo_host_alias = mongo_host = os.getenv("MONGO_DEFAULT_HOST")
mongo_db_name_alias = mongo_alias = os.getenv("MONGO_DBNAME_ALIAS")
mongo_port = os.getenv("MONGO_DEFAULT_PORT")
mongo_password = os.getenv("MONGODB_PASSWORD")
mongo_username = os.getenv("MONGODB_USERNAME")
encrypted_key = os.getenv("ENCRPYPT_KEY")

if ENVIRONMENT in ["stage", "lux_stage"]:
    new_host_ = "mongodb://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username,
        mongo_password,
        mongo_host_alias,
        mongo_db_name_alias,
    )

elif ENVIRONMENT in ["uat", "lux_uat"]:
    new_host_ = "mongodb+srv://{0}:{1}@{2}/{3}?authSource=admin".format(
        mongo_username,
        mongo_password,
        mongo_host_alias,
        mongo_db_name_alias,
    )

elif ENVIRONMENT == "production":
    new_host_ = "mongodb+srv://{0}:{1}@{2}/{3}".format(
        mongo_username,
        mongo_password,
        mongo_host_alias,
        mongo_db_name_alias,
    )

elif ENVIRONMENT == "test":
    new_host_ = "mongodb://{}:{}/{}?authSource=admin".format(
        mongo_host,
        mongo_port,
        mongo_db_name_alias,
    )

else:
    new_host_ = "mongodb://{}:{}/{}".format(
        os.getenv("MONGO_DEFAULT_HOST"),
        os.getenv("MONGO_DEFAULT_PORT"),
        os.getenv("MONGO_DBNAME_ALIAS"),
    )

debug_ = os.getenv("DEBUG")
lst_db_ = []
lst_db_.append(
    {
        "ALIAS": os.getenv("MONGO_DEFAULT_ALIAS"),
        "DB": mongo_db_name_alias if mongo_db_name_alias else os.getenv("MONGO_DBNAME_ALIAS"),
        "HOST": mongo_host_alias if mongo_host_alias else os.getenv("MONGO_DEFAULT_HOST"),
        "PORT": int(os.getenv("MONGO_DEFAULT_PORT")) if os.getenv("MONGO_DEFAULT_PORT") else None,
        "USERNAME": mongo_username if mongo_username else None,
        "PASSWORD": mongo_password if mongo_password else None,
    }
)


decrypted_postgres_username = os.getenv("POSTGRES_USERNAME")
decrypted_postgres_password = os.getenv("POSTGRES_PASSWORD")
decrypted_postgres_host = os.getenv("POSTGRES_HOST")
decrypted_postgres_db = os.getenv("POSTGRES_DB")


flexiroam_url = os.getenv("FLEXIROAM_URL")
flexiroam_username = decrypted_flexiroam_username = os.getenv("FLEXIROAM_USERNAME")
flexiroam_password = decrypted_flexiroam_password = os.getenv("FLEXIROAM_PASSWORD")


esimgo_vendor = "eSIMGo"
esimgo_url = os.getenv("ESIMGO_URL")
esim_go_token = os.getenv("ESIMGO_TOKEN")

vodafone_url = os.getenv("VODAFONE_URL")
vodafone_token = os.getenv("VODAFONE_TOKEN")

callback_url = os.getenv("CALLBACK_URL")
callback_get_iccid = callback_url + "/v2/get-cron-profile"


email_login_ = os.getenv("EMAIL_LOGIN")
postgress_schema_ = os.getenv("POSTGRES_SCHEMA")
decrypted_backend_url = os.getenv("BACKEND_URL")

promo_code_key = os.getenv("PROMO_CODE_KEY")

reseller_url = os.getenv("RESELLER_URL")
reseller_super_admin_username = os.getenv("RESELLER_ADMIN_USERNAME")
reseller_super_admin_password = os.getenv("RESELLER_ADMIN_PASSWORD")

list_of_users_ = ["<EMAIL>"]

ENC_FEEDBACK_KEY = os.getenv("ENC_FEEDBACK_KEY")
FEEDBACK_URL = os.getenv("FEEDBACK_URL")


if ENVIRONMENT != "production" and os.getenv("LST_DB_") is not None:
    lst_db = os.getenv("LST_DB_").split(",")
    for db in lst_db:
        alias = db
        alias = alias
        lst_db_.append(
            {
                "ALIAS": alias,
                "DB": alias,
                "HOST": mongo_host_alias if mongo_host_alias else os.getenv("MONGO_DEFAULT_HOST"),
                "PORT": int(os.getenv("MONGO_DEFAULT_PORT")) if os.getenv("MONGO_DEFAULT_PORT") else None,
                "USERNAME": mongo_username if mongo_username else None,
                "PASSWORD": mongo_password if mongo_password else None,
            }
        )

nb_profiles = int(os.getenv("PROFILE_COUNT"))
esim_go_token = os.getenv("ESIMGO_TOKEN")

montymobile_url = os.getenv("MONTYMOBILE_URL")
montymobile_username = os.getenv("MONTYMOBILE_USERNAME")
montymobile_password = os.getenv("MONTYMOBILE_PASSWORD")

token_key = os.getenv("TOKEN_KEY")
server_url = os.getenv("SERVER_URL", "http://localhost:6009")
esimgo_webhook_url = server_url + os.getenv("ESIMGO_WEBHOOK", "/v2/healthcheck")

play_integrity_username = os.getenv("PLAY_INTEGRITY_USERNAME")
play_integrity_password = os.getenv("PLAY_INTEGRITY_PASSWORD")
play_integrity_url = os.getenv("PLAY_INTEGRITY_URL")
decrypted_wp_qr_code = os.getenv("BACKEND_URL_WHATSAPP_QR_CODE")

orange_username = os.getenv("ORANGE_USERNAME")
orange_password = os.getenv("ORANGE_PASSWORD")
orange_login_username = os.getenv("ORANGE_LOGIN_USERNAME")
orange_login_password = os.getenv("ORANGE_LOGIN_PASSWORD")


try:
    with open("instance/translations/translation_feedback_email.json", "r", encoding="utf-8") as f:
        feedback_mapping = json.loads(f.read())
except Exception as e:
    logging.info(f"translation_feedback_email.json not found {e}")

monty_reseller_url = os.getenv("MONTY_RESELLER_URL", "https://reseller.localrsp.com/api/v0")
monty_reseller_agent_username = os.getenv("MONTY_RESELLER_AGENT_USERNAME")
monty_reseller_agent_password = os.getenv("MONTY_RESELLER_AGENT_PASSWORD")

indosat_url = os.getenv("INDOSAT_URL")
indosat_password = os.getenv("INDOSAT_PASSWORD")
indosat_username = os.getenv("INDOSAT_USERNAME")
indosat_liscense_key = os.getenv("INDOSAT_LISCENSE_KEY")

flexiroam2_url = os.getenv("FLEXIROAM_URL_V2", "https://solutions-api.flexiroam.com/v1/public-v2")
flexi_api_key = os.getenv("FLEXIROAM_API_KEY")

ms_graph_tenant_id = os.getenv("MS_GRAPH_TENANT_ID")
ms_graph_client_id = os.getenv("MS_GRAPH_CLIENT_ID")
ms_graph_client_secret = os.getenv("MS_GRAPH_CLIENT_SECRET")
outlook_admin_user_id = os.getenv("OUTLOOK_ADMIN_USER_ID")
outlook_webhook_notification_url = os.getenv("OUTLOOK_WEBHOOK_NOTIFICATION_URL")
outlook_webhook_lifecycle_notification_url = os.getenv("OUTLOOK_WEBHOOK_LIFECYCLE_NOTIFICATION_URL")

http_connection_timeout = os.getenv("HTTP_CONNECTION_TIMEOUT", 10)

bayobab_url = os.getenv("BAYOBAB_URL")
bayobab_username = os.getenv("BAYOBAB_USERNAME")
bayobab_password = os.getenv("BAYOBAB_PASSWORD")


es2plus_url = os.getenv("ES2PLUS_URL")
es2plus_fri = os.getenv("ES2PLUS_FRI")
es2plus_fci = os.getenv("ES2PLUS_FCI")

telkomsel_authentication_url = os.getenv("TELKOMSEL_AUTHENTICATION_URL")
telkomsel_base_url = os.getenv("TELKOMSEL_BASE_URL")
telkomsel_username = os.getenv("TELKOMSEL_USERNAME")
telkomsel_password = os.getenv("TELKOMSEL_PASSWORD")
telkomsel_client_id = os.getenv("TELKOMSEL_CLIENT_ID")

telkomsel_client_secret = os.getenv("TELKOMSEL_CLIENT_SECRET")

lotus_base_url = os.getenv("LOTUS_BASE_URL")
lotus_client_id = os.getenv("LOTUS_CLIENT_ID")
lotus_client_key = os.getenv("LOTUS_CLIENT_KEY")
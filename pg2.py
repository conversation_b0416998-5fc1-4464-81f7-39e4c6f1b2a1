from app_models import consumer_models, reseller_models as rm, main_models, ai_assistant_models

from Services.authorization_services.keyCloak import <PERSON><PERSON>loakHelper
from swagger_server.__main__ import app_flask
from keycloak import KeycloakAdmin
import concurrent.futures


def setup_keycloak_admin():
    return KeycloakHelper().get_admin_client()


def enable_user(user_id, keycloak_admin, username):
    try:
        keycloak_admin.update_user(
            user_id=user_id,
            payload={"enabled": True},
        )
        print(f"Enabled user {user_id}")
        return f"Successfully enabled user {user_id}"
    except Exception as e:
        return f"Failed to enable user {user_id}: {str(e)}"


def bulk_enable_users():
    keycloak_admin = setup_keycloak_admin()

    # Get all users
    users = keycloak_admin.get_users()

    # Using ThreadPoolExecutor for parallel processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(enable_user, user["id"], keycloak_admin, user["username"]) for user in users]

        for future in concurrent.futures.as_completed(futures):
            print(future.result())


if __name__ == "__main__":
    bulk_enable_users()

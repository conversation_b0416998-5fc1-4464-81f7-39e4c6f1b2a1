import csv
import logging
from typing import List, Dict, Set

logger = logging.getLogger(__name__)


def compare_profiles(original_csv: str, exported_csv: str, output_csv: str) -> None:
    """
    Compare two CSV files of Indosat profiles and output profiles that exist in the original
    but not in the exported file.

    Args:
        original_csv: Path to the CSV file with original profiles
        exported_csv: Path to the exported_indosat_profiles.csv file
        output_csv: Path to save the output CSV with missing profiles
    """
    logger.info(f"Comparing profiles between {original_csv} and {exported_csv}")

    # Read ICCIDs from exported profiles
    exported_iccids = set()
    try:
        with open(exported_csv, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Using the Iccid column from the exported CSV
                iccid = row.get('Iccid')
                if iccid:
                    exported_iccids.add(iccid)

        logger.info(f"Found {len(exported_iccids)} unique ICCIDs in exported profiles")
    except Exception as e:
        logger.error(f"Error reading exported profiles CSV: {str(e)}")
        return

    # Read original profiles and find those not in exported profiles
    missing_profiles = []
    try:
        with open(original_csv, 'r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            fieldnames = reader.fieldnames

            for row in reader:
                # Using the iccid field from the original CSV
                iccid = row.get('iccid')
                if iccid and iccid not in exported_iccids:
                    missing_profiles.append(row)

        logger.info(
            f"Found {len(missing_profiles)} profiles that exist in original but not in exported file")
    except Exception as e:
        logger.error(f"Error reading original profiles CSV: {str(e)}")
        return

    # Write missing profiles to output file
    try:
        if missing_profiles:
            with open(output_csv, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(missing_profiles)
            logger.info(
                f"Successfully wrote {len(missing_profiles)} missing profiles to {output_csv}")
        else:
            logger.info("No missing profiles found")
    except Exception as e:
        logger.error(f"Error writing output CSV: {str(e)}")


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Example usage
    compare_profiles(
        original_csv="indosat_profiles.csv",
        exported_csv="/home/<USER>/Desktop/expored_indosat_profiles.csv",
        output_csv="missing_profiles.csv"
    )
import logging
from b2c_helpers.vendors import MontyMobile
from app_helpers.db_helper import check_iccid
from main import app

logger = logging.getLogger(__name__)


def update_profile_expiry(profiles):
    """
    Update expiry date of profiles if they exist in the local DB.
    """
    for profile in profiles:
        profile_found = check_iccid(profile["iccid"])
        if not profile_found:
            continue
        expiry_date = profile["expiry_date"] if profile["expiry_date"] else None
        if expiry_date:
            profile_found.expiry_date = expiry_date
            profile_found.save()


def fetch_profiles_from_vendor(availability, status):
    """
    Fetch profiles page-wise from MontyMobile and update expiry dates.
    """
    page = 1
    while True:
        logger.info("Fetching profiles from vendor [availability=%s, status=%s, page=%s]", availability, status, page)
        response = monty_mobile.get_profiles(page=page, availability=availability, status=status)
        profiles = response.get("data") if response else None

        if not profiles:
            logger.info("No more profiles received for page %s. Ending fetch loop.", page)
            break

        logger.debug("Fetched %d profiles on page %s", len(profiles), page)
        update_profile_expiry(profiles)
        page += 1


with app.app_context():
    """
    This script updates expiry dates of MontyMobile profiles
    based on different availability and status combinations.
    """
    monty_mobile = MontyMobile()

    # Availability: FREE(0), ASSIGNED(1) | Status: ACTIVE(1), EXPIRED(3)
    scenarios = [
        (0, 1),
        (1, 1),
        (0, 3),
        (1, 3),
    ]

    for availability, status in scenarios:
        logger.info("Starting expiry update for availability= %s and status= %s", availability, status)
        fetch_profiles_from_vendor(availability, status)
        logger.info("Completed expiry update for availability= %s and status= %s", availability, status)

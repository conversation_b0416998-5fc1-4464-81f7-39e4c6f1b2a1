#!/usr//venv3.7/bin/python
import sys

sys.path.append('/')
from main import app
from app_models import consumer_models, reseller_models

with app.app_context():
    sys.path.append('/')
    pipeline = [
        {
            '$match': {
                'bundle_code': {
                    '$exists': True,
                    '$ne': ''
                }
            }
        }, {
            '$group': {
                '_id': '$bundle_code',
                'free_profiles_count': {
                    '$sum': {
                        '$cond': [
                            {
                                '$and': [
                                    {
                                        '$eq': [
                                            '$availability', 'Free'
                                        ]
                                    }, {
                                        '$eq': [
                                            '$status', True
                                        ]
                                    }
                                ]
                            }, 1, 0
                        ]
                    }
                },
                'vendor_name': {'$first': '$vendor_name'},
                'consumed': {
                    '$sum': {
                        '$cond': [
                            {
                                '$eq': [
                                    '$availability', 'Assigned'
                                ]
                            }, 1, 0
                        ]
                    }
                }
            }
        }, {
            '$addFields': {
                'allocated': {
                    '$sum': [
                        '$free_profiles_count', '$consumed'
                    ]
                }
            }
        }, {
            '$project': {
                '_id': 0,
                'bundle_code': '$_id',
                'allocated': 1,
                'consumed': 1,
                'vendor_name': 1
            }
        }
    ]
    
    bundles = consumer_models.Profiles._get_collection().aggregate(pipeline)

    for bundle in bundles:
        consumed_unit = bundle.get("consumed", None)
        allocated_unit = bundle.get("allocated", None)
        vendor_name = bundle.get("vendor_name", None)
        bundle_code = bundle.get("bundle_code", None)
        if vendor_name in ["eSIMGo", "Vodafone"] and allocated_unit:
            allocated_unit += 1
        if allocated_unit is not None and consumed_unit is not None:
            consumer_models.Bundles.objects(bundle_code=bundle_code).update(set__consumed_unit=consumed_unit,
                                                                            set__allocated_unit=allocated_unit)

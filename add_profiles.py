import random
import string
import secrets
from app_models import consumer_models
import uuid
import sys
from app_models.consumer_models import Bundles, Vendors
sys.path.append('/')
from app_main import app


def generate_matching_id():
    groups = []
    for i in range(4):
        group = ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))
        groups.append(group)
    return '-'.join(groups)


def generate_temp_otp(n):
    new_rand = ''.join(secrets.choice(string.digits) for i in range(0, n))
    return new_rand


def generate_dummy_profile(vendor_name, profile_names="", bundle_code=""):
    if bundle_code and (bundle := Bundles.objects(bundle_code=bundle_code).first()):
        if bundle.profile_names != profile_names:
            return
    matching_id = f"{generate_matching_id()}"
    iccid = f"no_started_{generate_temp_otp(8)}"
    smdp_address = "rsp.truphone.com"
    qr_code_value = f"LPA:1${smdp_address}${matching_id}"
    plan_uid = ""
    if bundle_code:
        plan_uid = f"{uuid.uuid4()}"
        profile_names = bundle.profile_names
    consumer_models.Profiles(
        vendor_name=vendor_name,
        iccid=iccid,
        sku=iccid,
        smdp_address=smdp_address,
        matching_id=matching_id,
        qr_code_value=qr_code_value,
        profile_name=profile_names,
        plan_uid=plan_uid,
        bundle_code=bundle_code,
        expiry_date=Vendors.objects(vendor_name=vendor_name).first().expiry_date
    ).save()


with app.app_context():
    sys.path.append('/')

    bundles_dict = {
        "Flexiroam": Bundles.objects(is_active=True, deleted=False, vendor_name="Flexiroam"),
        "eSIMGo": Bundles.objects(is_active=True, deleted=False, vendor_name="eSIMGo"),
        "Vodafone": Bundles.objects(is_active=True, deleted=False, vendor_name="Vodafone")
    }

    for i in range(1):
        generate_dummy_profile("Monty Mobile", "A")

        for vendor_name, bundles in bundles_dict.items():
            for bundle in bundles:
                generate_dummy_profile(vendor_name=vendor_name, profile_names=bundle.profile_names, bundle_code=bundle.bundle_code)

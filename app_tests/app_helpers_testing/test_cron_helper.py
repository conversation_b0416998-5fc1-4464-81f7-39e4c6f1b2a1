import unittest
import requests
from flask import Flask
from flask_mongoengine import MongoEngine
from app_helpers.script_helper import get_token_flexiroam
from ..instance import consumer_config as instance_config
from app_helpers.cron_helpers import flexi_save_bundles, get_bundle_name, save_profile, get_country_bundle_name, \
    flexi_save_profiles, flexi_expire_profiles
from app_helpers.db_helper import check_bundle, add_bundle, check_iccid, get_all_used_iccid, get_paid_user_bundle, \
    reset_email_verification, reset_password_request
from app_helpers.db_helper import get_vendor, expire_profile, reset_user_iccid
from datetime import datetime
import datetime

app = Flask(__name__)
db = MongoEngine()
app.config["MONGODB_SETTINGS"] = instance_config.lst_db_
db.init_app(app)

list_countries = ['Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia', 'Myanmar', 'Philippines', 'Singapore',
                  'Thailand', 'Vietnam']
list_countries_code = ['BRN', 'KHM', 'IDN', 'LAO', 'MYS', 'MMR', 'PHL', 'SGP', 'THA', 'VNM']
bundle = {'title': 'Monty Mobile JX-Global 200MB 1Month (Trial)', 'currency': 'USD', 'retail_price': '0.00',
          'unit_price': '1.00', 'start_date': None, 'end_date': None, 'plan_start_duration': '3',
          'plan_start_duration_unit': 'MONTH', 'plan_expiry_date': '0000-00-00 00:00:00', 'vendor_code': 'JT',
          'profile_names': 'J', 'plan_status': 'Active', 'allocated_unit': 0, 'consumed_unit': 0, 'plan': [
        {'plan_code': 'JX-200MB-1M-GDP', 'vendor_code': 'JT', 'title': 'JX Global Data Plan 200MB 1Month',
         'description': 'JX Global Data Plan 200MB 1Month', 'unit_price': '0.00', 'data_amount': '200.0000',
         'fullspeed_data_amount': '200.0000', 'data_unit': 'MB', 'validity_amount': '1', 'validity_unit': 'MONTH',
         'category_id': '2', 'category': 'Global Plans', 'timezone': 'GMT', 'status': 'Active',
         'profile_names': [{'profile_name': 'J'}], 'policy': [
            {'policy_name': 'Global Coverage', 'policy_status': 'Active', 'policy_type': 'Plan coverage',
             'policy_attributes': [{'Country': [{'name': 'Albania', 'value': 'ALB', 'status': 'Active'},

                                                {'name': 'Zimbabwe', 'value': 'ZWE', 'status': 'Active'}]}]},
            {'policy_name': 'Plan Start - Auto only', 'policy_status': 'Active', 'policy_type': 'Plan Start',
             'policy_attributes': [
                 {'Start': [{'name': 'Auto Start Upon Connection', 'value': '1', 'status': 'Active'}]}]}]}]}
bundle2 = {'vendor_name': 'Flexiroam', 'bundle_name': 'Monty Mobile JX-Global 200MB 1Month (Trial)',
           'bundle_marketing_name': 'Monty Mobile JX-Global 200MB 1Month (Trial)', 'bundle_code': 'JX-200MB-1M-GDP',
           'bundle_vendor_name': 'Monty Mobile JX-Global 200MB 1Month (Trial)', 'bundle_vendor_code': 'JX-200MB-1M-GDP',
           'supplier_vendor': 'JT', 'unit_price': '1.00', 'data_amount': '200.0000',
           'fullspeed_data_amount': '200.0000', 'data_unit': 'MB', 'validity_amount': '1', 'rate_revenue': 25.0,
           'retail_price': 1.25, 'profile_names': 'J', 'allocated_unit': 0}

response = {'success': True, 'message': 'SIM Inventory', 'total_records': 27, 'used_inventory': 6,
            'data': [

                {'product_code': 'GX-ESIM', 'sku': '3022030010021', 'sku_type': 'eSIM', 'iccid': '8910300000003657007',
                 'qr_code_value': 'LPA:1$rsp-0026.oberthur.net$AEP4D-DVPQ9-CSRJP-GIO3G', 'profile_names': 'F',
                 'assigned_date': '2022-07-22 06:48:41', 'name_tag': None, 'email_tag': None, 'availability': 'Free',
                 'order_no': 'ORD-62da48482d607', 'sim_status': 'Active', 'last_connection': None,
                 'installation_status': None},
                {'product_code': 'GX-ESIM', 'sku': '3022030010904', 'sku_type': 'eSIM', 'iccid': '8910300000003657890',
                 'qr_code_value': 'LPA:1$rsp-0026.oberthur.net$8IGY2-LRTF9-CCO4K-7JH0Q', 'profile_names': 'F',
                 'assigned_date': '2022-07-21 08:53:20', 'name_tag': None, 'email_tag': None, 'availability': 'Free',
                 'order_no': 'ORD-62d913fe0bb8b', 'sim_status': 'Active', 'last_connection': None,
                 'installation_status': None}]}

response2 = {'success': False, 'message': 'SIM Inventory', 'total_records': 27, 'used_inventory': 6,
             'data': [
                 {'product_code': 'GX-ESIM', 'sku': '3022030010022', 'sku_type': 'eSIM', 'iccid': '8910300000003657008',
                  'qr_code_value': 'LPA:1$rsp-0026.oberthur.net$PRRMT-UJ7MY-QTAAS-61FAW', 'profile_names': 'F',
                  'assigned_date': '2022-07-25 08:42:10', 'name_tag': None, 'email_tag': None, 'availability': 'Free',
                  'order_no': 'ORD-62de5760c69a5', 'sim_status': 'Active', 'last_connection': None,
                  'installation_status': None}]}

response3 = {'data': [{'product_code': 'GX-ESIM', 'sku': '3022030010022'}]}


class TestCronHelper(unittest.TestCase):

    def test_flexi_save_bundles(self):
        access_token, exception = get_token_flexiroam(instance_config.FLEXIROAM_USERNAME,
                                                      instance_config.FLEXIROAM_PASSWORD)
        self.assertEqual('Authentication successful', access_token['message'])
        self.assertEqual('Rachad', access_token['data']['first_name'])
        print("access_token['data']['token']", access_token)
        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            self.assertEqual(headers['token'], access_token['data']['token'])

            payload = {'group_by_offering': 'yes'}
            r = requests.post('{}/plan/inventory/view/v1'.format(instance_config.FLEXIROAM_URL), headers=headers,
                              data=payload)
            bundles = r.json()
            self.assertEqual(len(bundles), 4)  # json response has 2

            if bundles:
                list_countries_code = []

                for bundle in bundles['data']:
                    print("BUNDLES", bundle)
                    # bundle1 = instance_config.db.bundles.find_one({"vendor_name": "Flexiroam"})
                    bundle_exist = check_bundle(bundle['plan'][0]['plan_code'], 'Flexiroam')

                    bundle_exist2 = check_bundle(bundle['plan'][0]['plan_code'], 'Flex')  # not exist

                    if bundle_exist:
                        print("bundle_exist", bundle_exist)
                        bundle_exist.allocated_unit = bundle['allocated_unit']
                        print("bundle_exist.allocated_unit", bundle_exist.allocated_unit)

                        bundle_exist.consumed_unit = bundle['consumed_unit']
                        print("bundle_exist.consumed_unit", bundle_exist.consumed_unit)

                        if bundle_exist.allocated_unit > bundle_exist.consumed_unit:
                            bundle_exist.is_active = True
                            self.assertEqual(bundle_exist.is_active, True)
                        bundle_exist.save()

                    if bundle_exist2:
                        print("Exist!")

                    else:
                        vendor = get_vendor('Flexiroam')
                        print("Vendor", vendor)
                        rate_revenue = 25.0
                        if vendor:
                            rate_revenue = vendor.rate_revenue  # =25
                            self.assertEqual(rate_revenue, 25)
                        list_countries = []
                        retail_price = float(bundle["unit_price"]) + (
                                float(bundle["unit_price"]) * float(rate_revenue) / 100)
                        self.assertEqual(1.25, retail_price)

                    new_bundle = {
                        "vendor_name": "Flexiroam",
                        "bundle_name": bundle['title'],
                        "bundle_marketing_name": bundle['title'],
                        "bundle_code": bundle['plan'][0]['plan_code'],
                        "bundle_vendor_name": bundle['title'],
                        "bundle_vendor_code": bundle['plan'][0]['plan_code'],
                        "supplier_vendor": bundle['vendor_code'],
                        "unit_price": bundle["unit_price"],
                        "data_amount": bundle["plan"][0]["data_amount"],
                        "fullspeed_data_amount": bundle["plan"][0]["fullspeed_data_amount"],
                        "data_unit": bundle["plan"][0]["data_unit"],
                        "validity_amount": bundle["plan"][0]["validity_amount"],
                        "rate_revenue": float(rate_revenue),
                        "retail_price": round(float(retail_price), 2),
                        "profile_names": bundle['profile_names'],
                        "allocated_unit": bundle['allocated_unit'],

                    }

                    print("New Bundle", new_bundle)
                    self.assertEqual(new_bundle['rate_revenue'], 25)
                    self.assertEqual(new_bundle['data_unit'], "MB")
                    self.assertEqual(new_bundle['vendor_name'], "Flexiroam")
                    self.assertEqual(new_bundle['fullspeed_data_amount'], "200.0000")

                    bundle_duration = 1

                    if bundle['plan_start_duration_unit'] == 'MONTH':
                        bundle_duration = 30

                    list_countries_code = []
                    if len(bundle["plan"][0]['policy']) > 2:
                        countries = []
                        for policy_attributes in bundle["plan"][0]['policy']:
                            policy_attribute_check = policy_attributes['policy_attributes'][0]
                            print("policy_attribute_check", policy_attribute_check)
                            if 'Country' in policy_attribute_check:
                                list_countries.append(
                                    policy_attributes['policy_attributes'][0]['Country'][0]['name'])

                                print("list_countries", list_countries)
                                list_countries_code.append(
                                    policy_attributes['policy_attributes'][0]['Country'][0]['value'])
                                print("list_countries_code", list_countries_code)
                                # self.assertEqual(list_countries_code[0], "BRN")
                        # self.assertEqual(len(list_countries_code),12)

                    else:
                        countries = bundle["plan"][0]['policy'][0]['policy_attributes'][0]['Country']
                        list_countries = [d['name'] for d in countries if 'name' in d]
                        print("list_countries", list_countries)
                        list_countries_code = [d['value'] for d in countries if 'value' in d]

                    bundle_name, bundle_code = get_bundle_name(list_countries, list_countries_code, bundle)
                    new_bundle['bundle_name'] = bundle_name

                    new_bundle['bundle_code'] = bundle_code
                    new_bundle['bundle_duration'] = bundle_duration
                    new_bundle['is_region'] = False
                    new_bundle["bundle_category"] = "region"

                    if int(bundle["plan"][0]["category_id"]) == 1:
                        self.assertNotEqual(bundle["plan"][0]["fullspeed_data_amount"], '200.0000')

                        new_bundle["category_name"] = "region_"
                        new_bundle['is_region'] = True
                        new_bundle["bundle_category"] = "region"

                    elif int(bundle["plan"][0]["category_id"]) == 2:
                        new_bundle['is_region'] = True
                        new_bundle["bundle_category"] = "global"
                        new_bundle["category_name"] = "global"
                        self.assertEqual(new_bundle["category_name"], "global")

                    elif int(bundle["plan"][0]["category_id"]) == 3:

                        self.assertEqual(bundle["plan"][0]["fullspeed_data_amount"], '200.0000')

                        if len(bundle["plan"][0]['policy']) > 2:
                            self.assertEqual(new_bundle["is_region"], False)

                            new_bundle["category_name"] = "region"
                            new_bundle['is_region'] = True
                            self.assertEqual(new_bundle["is_region"], True)
                            self.assertEqual(new_bundle["bundle_category"], "region")
                            # self.assertEqual(new_bundle["category_name"], "region")


                        else:
                            self.assertEqual(new_bundle["bundle_category"], "region")

                            new_bundle['is_region'] = True
                            self.assertEqual(new_bundle['is_region'], True)
                            new_bundle["category_name"] = "country_" + list_countries_code[0]
                            self.assertEqual(len(new_bundle["category_name"]), 11)
                            new_bundle["bundle_category"] = "country"
                            self.assertEqual(new_bundle["bundle_category"], "country")

                    new_bundle['is_active'] = True
                    new_bundle['country_list'] = list_countries
                    new_bundle['country_code_list'] = list_countries_code

                    self.assertEqual(new_bundle['is_active'], True)
                    result = add_bundle(new_bundle)

    def test_save_profiles(self):
        q = save_profile(response['data'])
        self.assertEqual(q, True)

        r = save_profile(response2['data'])
        self.assertEqual(r, True)

        try:
            s = save_profile(response3['data'])  # wrong form of response
        except Exception as e:
            print("Exception as ", e)

    def test_flexi_save_profiles(self):

        access_token, exception = get_token_flexiroam(instance_config.FLEXIROAM_USERNAME,
                                                      instance_config.FLEXIROAM_PASSWORD)
        if access_token:
            headers = {
                'token': access_token['data']['token']
            }

            payload = {'availability': 0, "sim_type": "eSIM"}
            r = requests.post('{}/product/inventory/view/v1'.format(instance_config.FLEXIROAM_URL), headers=headers,
                              data=payload)

            self.assertEqual(r.status_code, 200)
            response = r.json()
            self.assertEqual(response['success'], True)
            resp_data = save_profile(response['data'])
            self.assertEqual(True, resp_data)

    def test_get_bundle_name(self):
        bundle_name = get_bundle_name(list_countries=list_countries, list_countries_code=list_countries_code,
                                      bundle=bundle)

        # not fix but it worked
        # self.assertEqual(bundle_name, ('Global_bundle  200MB', 'Global_bundle_07272022120727'))
        self.assertEqual(bundle_name[0], ('Global_bundle  200MB'))

        try:
            bundle_name2 = get_bundle_name(list_countries=list_countries, list_countries_code=list_countries_code,
                                           bundle=bundle2)
            print(bundle_name2)

        except Exception as e:
            print("Exception as", e)

    def test_flexi_expire_profiles(self):
        # status:'used'
        iccid_list = get_all_used_iccid()
        print("iccid_list", iccid_list)
        user_bundle = get_paid_user_bundle("1004", "<EMAIL>")
        self.assertEqual(user_bundle.email, "<EMAIL>")
        for user_iccid in iccid_list:
            print("hkjkjk")
            self.assertEqual(user_iccid.status, "used")

            print(user_bundle.validy_date)
            self.assertEqual(user_bundle.validy_date, datetime(2022, 4, 28, 16, 28, 55, 923000))
            # date<today
            user_iccid.status = "expired"
            self.assertEqual(user_iccid.status, "expired")

            self.assertEqual(user_iccid.iccid, "89970284323186080835")
            expire_profile("231")
            self.assertEqual(user_iccid.status, False)

            user_bundle2 = get_paid_user_bundle("1003", "<EMAIL>")
            self.assertEqual(user_bundle2.email, "<EMAIL>")
            self.assertEqual(user_bundle2.validy_date, datetime(2022, 8, 28, 21, 25, 43))
            # date>today
            self.assertNotEqual(user_iccid.status, "expired")

    def test_expire_scripts(self):
        date_now = datetime.datetime.now()
        print(date_now)
        reset_user_iccid_1 = instance_config.db.user_iccid.find_one({"bundle_code": "1010"})
        print(reset_user_iccid_1['counter'])

        #self.assertEqual(reset_user_iccid_1['counter'],0)
        print("reset_user_iccid_1['expiry_date']",reset_user_iccid_1['expiry_date'])

        reset_user_iccid(date_now)
        self.assertEqual(reset_user_iccid_1['counter'],0)

        reset_email_verification_1 = instance_config.db.app_email_verification.find_one({"user_email": "<EMAIL>"})

        #after recalling the function(reset_email_verification), it will be 0
        #self.assertEqual(reset_email_verification_1['count'],5)
        self.assertEqual(reset_email_verification_1['is_verified'],False)

        reset_email_verification(date_now)
        self.assertEqual(reset_email_verification_1['count'],0)

        reset_password_request_1 = instance_config.db.app_user_password_reset.find_one({"user_email": "<EMAIL>"})
        print(reset_password_request_1['expiry_create_date'])


        #after recalling the function(reset_password_request), expiry_create_date will be changed
        #self.assertEqual(reset_password_request_1['expiry_create_date'],datetime.datetime(2022, 6, 27, 10, 46, 1, 939000))
        #expiry_create_date<today
        reset_password_request(date_now)

        #count =5 will be changed to 0
        self.assertEqual(reset_password_request_1['count'],0)

        try:
            reset_password_request_2 = instance_config.db.app_user_password_reset.find_one({"user_email": "sara.zakaria"})
            print("reset_password_request_2['expiry_date']", reset_password_request_2['expiry_date'])

            print("Not found")
        except Exception as e:
            print("Exception as ", e)


from app_extra import app
import datetime
import unittest
import logging
import json
from flask import Flask
from flask_mongoengine import MongoEngine
from ..instance import consumer_config as instance_config
from app_helpers import db_helper
from app_extra.views.main_views import send_qr_email, generate_temp_otp
import os
from app_helpers import script_helper

logging.basicConfig(filename="log.txt", filemode="w", level=logging.DEBUG)
logging.info(datetime.datetime.now())

app = Flask(__name__)
db = MongoEngine()
app.config["MONGODB_SETTINGS"] = instance_config.lst_db_
db.init_app(app)

logging.basicConfig(filename="log.txt", level=logging.DEBUG, filemode="w", force=True)
logging.info(datetime.datetime.now())
logging.info("----------Start logging...")

# -------------------- Get path of test cases json file
current_dir = os.path.dirname(os.path.abspath(__file__))
test_cases_path = os.path.join(current_dir, "test_app_extra.json")

file = open(test_cases_path, "r")
file_data = file.read()
data_dictionary = json.loads(file_data)
file.close()

# result = get_tenant()
result = 'default'


class UnitTestCases(unittest.TestCase):
    def __init__(self, methodName='runTest'):
        super().__init__(methodName=methodName)
        self.app = app

    def setUp(self) -> None:
        logging.info("----------Start logging...")

    def test01_send_qr_code_by_email(self):
        with app.app_context():
            if result:
                user = data_dictionary['user1']
                userr = db_helper.get_user_from_user_iccid_(user['otp'],user['email'])
                sent = send_qr_email(userr, user['otp'])
                self.assertEqual(sent, True)

    def test02_add_history_log_manually(self):
        with app.app_context():
            result = 'default'
            if result:

                # history log found
                test_case1 = data_dictionary['user1']

                # history log not found
                test_case2 = data_dictionary['user2']
                # user_bundle_hist = db_helper.get_user_history_bundle_pipeline(result, test_case1['otp'], test_case1['bundle_code'], test_case1['email'])

                user_bundle_hist = db_helper.get_user_history_bundle_pipeline(result, test_case2['otp'],
                                                                              test_case2['bundle_code'],
                                                                              test_case2['email'])

                for bundle in user_bundle_hist:
                    if 'bundle_code' in bundle:
                        found_history = db_helper.check_history(result, bundle['order_number'], bundle['email'],
                                                                bundle['iccid'],
                                                                bundle['bundle_code'])

                        # second time it will be True, exist
                        self.assertEqual(found_history, False)

                        if not found_history:
                            coverage = "global"
                            if bundle['bundle_category'] == "region":
                                coverage = bundle['region_name']
                            elif bundle['bundle_category'] == "country":
                                if len(bundle['country_list']) > 0:
                                    coverage = bundle['country_list'][0]
                            qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
                                instance_config.decrypted_backend_url,
                                bundle['matching_id'],
                                bundle['smdp_address'],
                                bundle['has_lpa'])
                            history_log = {
                                "history_log_id": "hist_" + generate_temp_otp(12),
                                # "datetime": bundle['payment_date'],
                                "email": bundle['email'],
                                "iccid": bundle['iccid'],
                                "bundle_name": bundle['bundle_name'],
                                "bundle_marketing_name": bundle['bundle_marketing_name'],
                                "coverage": coverage,
                                "price": bundle['amount'],
                                "currency_code": bundle['currency_code'],
                                "bundle_code": bundle['bundle_code'],
                                "data_amount": bundle['data_amount'],
                                "data_unit": bundle['data_unit'],
                                "bundle_duration": bundle['bundle_duration'],
                                "order_number": bundle['order_number'],
                                "smdp_address": bundle['smdp_address'],
                                "activation_code": bundle['activation_code'],
                                "qr_code_link": qr_code_link,
                                "sent_using": "Email",
                                "transaction": 'BuyBundle'
                            }


                            self.assertEqual(history_log, data_dictionary['new_history_log_temp'])
                            self.assertEqual(history_log['bundle_code'],
                                             data_dictionary['new_history_log_temp']['bundle_code'])
                            self.assertEqual(history_log['email'], data_dictionary['new_history_log_temp']['email'])
                            self.assertEqual(history_log['sent_using'],
                                             data_dictionary['new_history_log_temp']['sent_using'])
                            self.assertEqual(history_log['data_amount'],
                                             data_dictionary['new_history_log_temp']['data_amount'])
                            self.assertEqual(history_log['iccid'], data_dictionary['new_history_log_temp']['iccid'])

                            db_helper.save_history_logs(result, history_log)

    def test03_edit_payment(self):
        with app.app_context():
            result = 'default'
            if result:
                test_case2 = data_dictionary['user2']

                transaction_logs = db_helper.get_transaction_logs(order_number=test_case2['order_number'])
                if transaction_logs:
                    order_number = transaction_logs["order_number"]

                    # check if payment status :False
                    user_bundle_log = db_helper.get_payment_status_from_user_bundle_log(order_number)
                    if user_bundle_log:
                        # 1 set paymenet status : true
                        db_helper.update_payment_status(order_number)

                        # 2  set status in user iccid : "used"
                        userr = db_helper.get_user_from_user_iccid_(test_case2['otp'], test_case2['email'])
                        self.assertEqual(userr['iccid'], "8910300000003657004")
                        status_used = db_helper.update_used_iccid(userr['iccid'])
                        self.assertEqual(status_used["status"], "used")
                        self.assertEqual(status_used['iccid'], "8910300000003657004")

                        # to get plan_uid and iccid in case of esimgo for user-iccid
                        bundle = db_helper.get_bundle_by_code(test_case2['bundle_code'])

                        allocate, exception, plan_uid, iccid, user_iccid = script_helper.allocate_per_vendor(
                            bundle.vendor_name,
                            userr,
                            bundle.bundle_vendor_code,test_case2['otp'],
                            test_case2['bundle_code'],test_case2['email'])
                        db_helper.update_used_iccid(test_case2['otp'], test_case2['email'], iccid, plan_uid)



                        # 3 add new row to user bundle
                        doc = {
                            'email': userr.email,
                            'iccid': userr.iccid,
                            'qr_code_pin': user_bundle_log.qr_code_pin,
                            'wp_code_pin': user_bundle_log.wp_code_pin,
                            'otp': user_bundle_log.otp,
                            'cancel_otp': user_bundle_log.cancel_otp,
                            'payment_status': True,
                            'payment_topup': user_bundle_log.payment_topup,
                            'country_code': user_bundle_log.country_code,
                            'bundle': user_bundle_log.bundle,
                            'bundle_code': user_bundle_log.bundle_code,
                            'data_code': user_bundle_log.data_code,
                            'topup_code': [],
                            'currency_code': user_bundle_log.currency_code,
                            'id_card': user_bundle_log.id_card,
                            'amount': user_bundle_log.amount,
                            'datetime': datetime.datetime.utcnow(),
                            'payment_date': user_bundle_log.payment_date,
                            'validy_date': user_bundle_log.validy_date
                        }
                        test_case3 = data_dictionary["user_bundle"]

                        db_helper.add_user_bundle(doc)
                        self.assertEqual(test_case3["iccid"], "8910300000003657004")
                        self.assertEqual(test_case3["email"], "<EMAIL>")
                        self.assertEqual(test_case3["payment_status"], True)

                        # 4 send email
                        email_sent = send_qr_email(userr, test_case3["otp"])
                        self.assertEqual(email_sent, True)

    def test04_edit_topup_status(self):
        with app.app_context():
            result = 'default'
            if result:
                test_case2 = data_dictionary['user2']

                transaction_logs = db_helper.get_transaction_logs(order_number=test_case2['order_number'])
                if transaction_logs:
                    order_number = transaction_logs["order_number"]

                    # check if topup status :False
                    user_bundle_log = db_helper.get_topup_status_from_user_bundle_log(order_number)
                    if user_bundle_log:
                        # 1 set topup status : true
                        db_helper.update_topup_status(order_number)


                        # 2  set status in user iccid : "used"
                        userr = db_helper.get_user_from_user_iccid_(test_case2['otp'], test_case2['email'])
                        self.assertEqual(userr['iccid'], "8910300000003657004")
                        status_used = db_helper.update_used_iccid(userr['iccid'])
                        self.assertEqual(userr["status"], "used")
                        # to get plan_uid and iccid in case of esimgo for user-iccid
                        bundle = db_helper.get_bundle_by_code(test_case2['bundle_code'])
                        allocate, exception, plan_uid, iccid, user_iccid = script_helper.allocate_per_vendor(
                            bundle.vendor_name,
                            userr,
                            bundle.bundle_vendor_code, test_case2['otp'],
                            test_case2['bundle_code'], test_case2['email'])
                        db_helper.update_used_iccid(test_case2['otp'], test_case2['email'], iccid, plan_uid)
                        user_bundle_log_updated = db_helper.get_user_bundle_log(order_number)

                        # 3 add new row to topup bundles
                        profile = db_helper.get_profile_from_iccid(user_iccid.iccid)
                        doc = {
                            "vendor_name": profile.vendor_name,
                            'iccid': userr.iccid,
                            'email': userr.email,
                            'bundle_code': user_bundle_log_updated.bundle_code,
                            'popup_code': user_bundle_log_updated.topup_code,
                            'plan_uid': userr.plan_uid,
                            "datetime": datetime.datetime.utcnow(),
                            'payment_date': user_bundle_log_updated.payment_date,
                            'status': userr.status,
                            'validy_date': user_bundle_log_updated.validy_date,
                            'payment_otp': userr.payment_otp,
                        }
                        db_helper.add_topup_bundle(doc)

                        # Add topup_code in user_bundle
                        db_helper.update_user_bundle(test_case2['otp'], test_case2['email'], user_bundle_log_updated.topup_code)

                        test_case3 = data_dictionary["topup_bundles"]
                        self.assertEqual(doc["vendor_name"], test_case3["vendor_name"])

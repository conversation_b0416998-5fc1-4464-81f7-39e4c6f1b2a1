import pymongo
from instance import consumer_config

# Set up the MongoDB connection details
database_name = consumer_config.decrypted_db_name_alias
mongo_db_url = consumer_config.new_host_
myclient = pymongo.MongoClient(mongo_db_url)

# Access the database and collections
database = myclient[database_name]
notification_logs_collection = database["notification_logs"]
order_history_collection = database["order_history"]

# Perform aggregation to match `notification_logs` with `order_history` on `iccid`
pipeline = [
    {
        "$lookup": {
            "from": "order_history",
            "localField": "iccid",
            "foreignField": "iccid",
            "as": "order_history_info"
        }
    },
    {
        "$unwind": {
            "path": "$order_history_info",
            "preserveNullAndEmptyArrays": True
        }
    },
    {
        "$match": {
            "order_history_info.order_status": "Successful"
        }
    },
    {
        "$group": {
            "_id": "$_id",
            "reseller_type": {"$first": "$order_history_info.reseller_type"}
        }
    },
    {
        "$project": {
            "reseller_type": 1,
            "_id": 1
        }
    }
]


# Iterate through the results and update `notification_logs`
for notification in notification_logs_collection.aggregate(pipeline):
    # Check if a match was found in `order_history`
    if notification["reseller_type"]:
        notification_logs_collection.update_one(
            {"_id": notification["_id"]},
            {"$set": {"reseller_type": notification["reseller_type"]}}
        )

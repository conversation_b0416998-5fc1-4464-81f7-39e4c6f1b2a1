apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "11"
  creationTimestamp: "2025-01-31T14:25:06Z"
  generation: 14
  labels:
    app: esim-b2c-reseller-api
    kustomize.toolkit.fluxcd.io/name: esim-b2c-reseller-api
    kustomize.toolkit.fluxcd.io/namespace: b2c
    version: v-0.15
  name: esim-b2c-reseller-api
  namespace: b2c
  resourceVersion: "69118955"
  uid: 3825bd9a-19cf-4caa-887c-c7616bcabb29
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: esim-b2c-reseller-api
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        cattle.io/timestamp: "2025-03-11T09:45:21Z"
      creationTimestamp: null
      labels:
        app: esim-b2c-reseller-api
        version: v-0.15
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - gunicorn --workers=3 --threads=10 --worker-class=gthread wsgi:app -b 0.0.0.0:9005
        command:
        - /bin/bash
        - -c
        env:
        - name: ENV
          value: dev
        envFrom:
        - secretRef:
            name: esim-b2c-reseller-secret
        image: registry.gitlab.com/monty-mobile1/esim/b2c/esim-b2c-reseller-py0.1:v-0.15
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /api/v0/HealthCheck
            port: 9005
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
        name: esim-b2c-reseller-api
        ports:
        - containerPort: 9005
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /api/v0/HealthCheck
            port: 9005
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 3
        resources:
          limits:
            cpu: "2"
            memory: 1Gi
          requests:
            cpu: "1"
            memory: 512Mi
        startupProbe:
          failureThreshold: 60
          httpGet:
            path: /api/v0/HealthCheck
            port: 9005
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: b2c-registry-secret
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 10001
      serviceAccount: esim-b2c-reseller-api-sa
      serviceAccountName: esim-b2c-reseller-api-sa
      terminationGracePeriodSeconds: 30
status:
  availableReplicas: 1
  conditions:
  - lastTransitionTime: "2025-03-20T11:00:27Z"
    lastUpdateTime: "2025-03-20T11:00:27Z"
    message: Deployment has minimum availability.
    reason: MinimumReplicasAvailable
    status: "True"
    type: Available
  - lastTransitionTime: "2025-02-07T14:54:56Z"
    lastUpdateTime: "2025-04-09T12:21:54Z"
    message: ReplicaSet "esim-b2c-reseller-api-65fd8676b4" is progressing.
    reason: ReplicaSetUpdated
    status: "True"
    type: Progressing
  observedGeneration: 14
  readyReplicas: 1
  replicas: 2
  unavailableReplicas: 1
  updatedReplicas: 1

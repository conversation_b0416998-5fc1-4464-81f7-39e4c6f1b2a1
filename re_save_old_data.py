#!/usr//venv3.7/bin/python
from app_models.consumer_models import UserBundleLog, UserIccid, Bundles, Profiles
from app_models.reseller_models import Order_history, BundleData
from app_models.main_models import HistoryLogs
from main import app
from instance import consumer_config as instance

# Create dictionaries to store data
bundles_dict = {bundle.bundle_code: bundle for bundle in Bundles.objects()}
profiles_dict = {profile.iccid: profile for profile in Profiles.objects()}
user_iccid_dict = {user_iccid.payment_otp: user_iccid for user_iccid in UserIccid.objects()}
history_logs_dict = {history_log.history_log_id: history_log for history_log in HistoryLogs.objects()}


with app.app_context():
    count = 0
    old_data = UserBundleLog.objects()
    for user_bundle_log in old_data:
        if user_bundle_log["migrated"] == True:
            continue

        if user_bundle_log.payment_status:
            bundle_details = bundles_dict.get(user_bundle_log.bundle_code)
            user_iccid = user_iccid_dict.get(user_bundle_log.otp)
            history_log = history_logs_dict.get(user_bundle_log.history_log)

            if user_iccid:
                if user_iccid.iccid == None:
                    continue

            if user_iccid:
                profile = profiles_dict.get(user_iccid.iccid)
                if user_iccid.status == 'unused':
                    user_iccid.status = 'Pending'
                elif user_iccid.status == 'used':
                    user_iccid.status = 'Active'
                elif user_iccid.status == 'expired':
                    user_iccid.status = 'Expired'

            if bundle_details:
                if bundle_details["bundle_category"] == "region":
                    bundle_details['coverage'] = bundle_details["region_code"]
                    bundle_details['display_name'] = str(bundle_details["region_name"]).capitalize()
                elif bundle_details["bundle_category"] == "country":
                    bundle_details['coverage'] = str(bundle_details["country_code_list"][0])
                    bundle_details['display_name'] = str(bundle_details["country_list"][0]).capitalize()
                else:
                    bundle_details['coverage'] = "Global"
                    bundle_details['display_name'] = "Global"

                bundle_data = BundleData(
                    bundle_code=bundle_details.bundle_code,
                    bundle_name=bundle_details.bundle_name,
                    bundle_marketing_name=bundle_details.bundle_marketing_name,
                    bundle_duration=bundle_details.bundle_duration,
                    retail_price=bundle_details.retail_price,
                    currency_code=bundle_details.currency_code,
                    data_amount=bundle_details.data_amount,
                    data_unit=bundle_details.data_unit,
                    country_list=bundle_details.country_list,
                    country_code_list=bundle_details.country_code_list,
                    coverage= bundle_details['coverage'],
                    display_name = bundle_details['display_name'],
                )

            if user_iccid and bundle_details :
                order_history = Order_history(
                    order_status="Successful",
                    bundle_price=user_bundle_log.amount,
                    bundle_code=user_bundle_log.bundle_code,
                    client_email=user_bundle_log.email,
                    country_code=user_bundle_log.country_code,
                    date_created=user_bundle_log.datetime,
                    reseller_type="subscriber",
                    topup_code=user_bundle_log.topup_code,
                    currency_code=user_bundle_log.currency_code,
                    version_token=user_bundle_log.version_token,
                    order_number=user_bundle_log.order_number,
                    paid_amount_credit_card=user_bundle_log.paid_amount_credit_card,
                    paid_amount_wallet=user_bundle_log.paid_amount_wallet,
                    promo_code=user_bundle_log.promo_code,
                    stripe_client_secret=user_bundle_log.stripe_client_secret,
                    pid_hash=user_bundle_log.pidencrypted,
                    ipv4_address=user_bundle_log.ipv4_address,
                    payment_category=user_bundle_log.payment_category,

                    qr_code_link= '{}/generate-qr-code/{}/{}/{}/qr_code.jpg'.format(
                        instance.decrypted_wp_qr_code,
                        profile['matching_id'],
                        profile['smdp_address'],
                        profile['has_lpa']
                        ),
                    order_type= "BuyBundle",
                    smdp_address=profile.smdp_address,
                    activation_code= user_iccid.activation_code,
                    payment_date= user_bundle_log.payment_date,

                    iccid=user_iccid.iccid,
                    plan_uid=user_iccid.plan_uid,
                    vendor_msisdn=user_iccid.vendor_msisdn,
                    expiry_notified=user_iccid.expiry_notified,
                    plan_status=user_iccid.status,
                    expiry_date=user_iccid.expiry_date,
                    resend_counter=user_iccid.counter,
                    searched_countries=user_iccid.searched_countries,
                    searched_code_list=user_iccid.searched_code_list,
                    plan_started=user_iccid.plan_started,
                    bundle_data=bundle_data,

                    qr_code_value=profile.qr_code_value if profile else None,

                    migrated= True
                )
                order_history.save()
                count += 1
                user_bundle_log.update(set__migrated=True)

        elif user_bundle_log.payment_topup:
                bundle_details = bundles_dict.get(user_bundle_log.topup_code)
                user_iccid = user_iccid_dict.get(user_bundle_log.otp)
                history_log = history_logs_dict.get(user_bundle_log.history_log)

                if user_iccid:
                    if user_iccid.iccid == None:
                        continue

                if user_iccid:
                    profile = profiles_dict.get(user_iccid.iccid)
                    if user_iccid.status == 'unused':
                        user_iccid.status = 'Pending'
                    elif user_iccid.status == 'used':
                        user_iccid.status = 'Active'
                    elif user_iccid.status == 'expired':
                        user_iccid.status = 'Expired'

                if bundle_details:
                    if bundle_details["bundle_category"] == "region":
                        bundle_details['coverage'] = bundle_details["region_code"]
                        bundle_details['display_name'] = str(bundle_details["region_name"]).capitalize()
                    elif bundle_details["bundle_category"] == "country":
                        bundle_details['coverage'] = str(bundle_details["country_code_list"][0])
                        bundle_details['display_name'] = str(bundle_details["country_list"][0]).capitalize()
                    else:
                        bundle_details['coverage'] = "Global"
                        bundle_details['display_name'] = "Global"

                    bundle_data = BundleData(
                        bundle_code=bundle_details.bundle_code,
                        bundle_name=bundle_details.bundle_name,
                        bundle_marketing_name=bundle_details.bundle_marketing_name,
                        bundle_duration=bundle_details.bundle_duration,
                        retail_price=bundle_details.retail_price,
                        currency_code=bundle_details.currency_code,
                        data_amount=bundle_details.data_amount,
                        data_unit=bundle_details.data_unit,
                        country_list=bundle_details.country_list,
                        country_code_list=bundle_details.country_code_list,
                        coverage=bundle_details['coverage'],
                        display_name=bundle_details['display_name'],
                    )

                if user_iccid and bundle_details:
                    order_history = Order_history(
                        order_status="Successful",
                        bundle_price=user_bundle_log.amount,
                        bundle_code=user_bundle_log.topup_code,
                        client_email=user_bundle_log.email,
                        country_code=user_bundle_log.country_code,
                        date_created=user_bundle_log.datetime,
                        reseller_type="subscriber",
                        topup_code=user_bundle_log.topup_code,
                        currency_code=user_bundle_log.currency_code,
                        version_token=user_bundle_log.version_token,
                        order_number=user_bundle_log.order_number,
                        paid_amount_credit_card=user_bundle_log.paid_amount_credit_card,
                        paid_amount_wallet=user_bundle_log.paid_amount_wallet,
                        promo_code=user_bundle_log.promo_code,
                        stripe_client_secret=user_bundle_log.stripe_client_secret,
                        pid_hash=user_bundle_log.pidencrypted,
                        ipv4_address=user_bundle_log.ipv4_address,
                        payment_category=user_bundle_log.payment_category,

                        qr_code_link= '{}/generate-qr-code/{}/{}/{}/qr_code.jpg'.format(instance.decrypted_wp_qr_code,
                                                                     profile['matching_id'],
                                                                     profile['smdp_address'],
                                                                     profile['has_lpa']
                                                                     ),
                        order_type= "BuyTopup",
                        smdp_address= profile.smdp_address,
                        activation_code= user_iccid.activation_code,
                        payment_date= user_bundle_log.payment_date,

                        iccid=user_iccid.iccid,
                        plan_uid=user_iccid.plan_uid,
                        vendor_msisdn=user_iccid.vendor_msisdn,
                        expiry_notified=user_iccid.expiry_notified,
                        plan_status=user_iccid.status,
                        expiry_date=user_iccid.expiry_date,
                        resend_counter=user_iccid.counter,
                        searched_countries=user_iccid.searched_countries,
                        searched_code_list=user_iccid.searched_code_list,
                        plan_started=user_iccid.plan_started,
                        bundle_data=bundle_data,

                        qr_code_value=profile.qr_code_value if profile else None,

                        migrated= True

                    )
                    order_history.save()
                    count += 1
                    user_bundle_log.update(set__migrated=True)

    print("Data migration completed successfully!")
    print("Total records of userBundleLog", old_data.count())
    print("Total records migrated successfully", count)
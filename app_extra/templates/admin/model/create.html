
{% extends 'admin/master.html' %}
{% import
  'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
  <link href="{{ url_for('static',filename='css/richtext.min.css')}}" rel="stylesheet">
      <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
      <!--<link href="{{ url_for('static',filename='css/fontawesome.css')}}" rel="stylesheet">-->
{% endblock %}

{% block body %}
{{ model_layout.messages() }}
  {% block navlinks %}
  <ul class="nav nav-tabs">
    <li class="nav-item ">
        <a href="{{ return_url }}"  class="nav-link">{{ _gettext('List') }}</a>
    </li>
     <li class="nav-item ">
        <a class="nav-link active" href="javascript:void(0)"  data-toggle="tab">{{ _gettext('Create') }}</a>
    </li>
  </ul>
  {% endblock %}

  {% block create_form %}
    {{ lib.render_form(form, return_url, extra(), form_opts) }}
  {% endblock %}
{% endblock %}

{% block tail %}
  {{ super() }}
       <script src="{{ url_for('static',filename='js/jquery.richtext.js')}}"></script>
     <script>
     	$(document).ready(function() {
     	    $('.txtEditor').each(function(i, obj) {
                $("#"+obj.id).richText();
            });
     	       $('#language_code').on('change', function() {
                  alert( this.value );
                  if (this.value=='ar'){
                    $(".language_selected").addClass("rtl-class");
                    }
                  else{
                   $(".language_selected").removeClass("rtl-class");
                   }
                });


			});
    $(function () {
          $('.public_tooltip').tooltip({
      template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner tooltip-key-full"></div></div>'
  })
    }
    )
      function countChar(val) {
        var len = val.value.length;
        $('#'+val.id+'_onkeyup').text( len+' chars entred');
      };
 $(".keyup").map(function() {
     countChar(this)
    return this.innerHTML;
})

    </script>
  {{ lib.form_js() }}
{% endblock %}

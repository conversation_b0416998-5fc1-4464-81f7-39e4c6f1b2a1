{% extends 'admin/master.html' %}
{% import 'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
  <link href="{{ url_for('static', filename='css/richtext.min.css') }}" rel="stylesheet">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <!--<link href="{{ url_for('static', filename='css/fontawesome.css') }}" rel="stylesheet">-->
  <style>
    /* Custom table colors */
    .table-bordered th,
    .table-bordered td {
      border-color: #ccc;
    }

    .table-bordered thead th {
      background-color: #f2f2f2;
    }

    .table-bordered tbody td {
      background-color: #fff;
    }
  </style>
{% endblock %}

{% block body %}
  <h6>Profile Status</h6>

  <table class="table table-bordered">
    <tr>
      <th>Vendor Name</th>
      <td>{{ model.vendor_name }}</td>
    </tr>
    <tr>
      <th>ICCID</th>
      <td>
        {{ encrypted_iccid }}
      </td>
    </tr>
    <tr>
      <th>Profile Names</th>
      <td>{{ model.profile_names }}</td>
    </tr>
    <tr>
      <th>Availability</th>
      <td>{{ model.availability }}</td>
    </tr>
    <tr>
      <th>Status</th>
      <td>{{ model.status }}</td>
    </tr>
    <tr>
      <th>Profile Status</th>
      <td>
        {{ profile.profile_status }}
      </td>
    </tr>
    <tr>
      <th>eSIM Status</th>
      <td>
        {{ esim.esim_status }}
      </td>
    </tr>
     <tr>
      <th>Vendor Json Response</th>
      <td>
        <button onclick="viewJsonResponse()">View Json Response</button>
      </td>
    </tr>
  </table>

  <script>
    function viewJsonResponse() {
      var jsonResponse = {{ json_response | tojson }};
      alert(JSON.stringify(jsonResponse, null, 2));
    }
  </script>
  <br>
  <br>
  <h6>User Consumption Information - From Vendor</h6>
  {% if model.vendor_name == 'Vodafone' %}
    <div class="signal-shape">
      <p><span class="warning-sign">&#9888;</span>These data are for accumulated bundles!</p>
      <p><span class="warning-sign">&#9888;</span>The response doesn't return startDate </p>
    </div>
  {% endif %}

  {% if model.vendor_name == 'Bayobab' %}
    <div class="signal-shape">
      <p><span class="warning-sign">&#9888;</span>Vendor is showing only the active plans!</p>
    </div>
  {% endif %}

  {% if consumption.status %}

    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Plan Code</th>
          <th>Activation Status</th>
          <th>Data Allocated</th>
          <th>Data Balance</th>
          <th>Data Used</th>
          <th>Start Date</th>
          <th>End Date</th>
        </tr>
      </thead>
      <tbody>
        {% for bundle in consumption.data %}
          <tr>
            {% if model.vendor_name == 'Monty Mobile' %}
              <td>{{ bundle.get("plan_uid", "") }}</td>
            {% else %}
              <td>{{ bundle.get("plan_code", "") }}</td>
            {% endif %}
            <td>{{ bundle.get("plan_status", "") }}</td>
            <td>{{ bundle.get("data_allocated", "") }}</td>
            <td>{{ bundle.get("data_balance", "") }}</td>
            <td>{{ bundle.get("data_used", "") }}</td>
            <td>{{ bundle.get("start_date", "")}}</td>
            <td>{{ bundle.get("end_date", "") }}</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {% else %}
    <p>{{consumption.message}}</p>
  {% endif %}
  <br>
  <br>
  <h6>User Consumption Information - From Cache</h6>
  {% if  model and model.consumption_cache%}
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Data Allocated</th>
          <th>Data Used</th>
          <th>Data Unit</th>
          <th>Cached At</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ model.consumption_cache.data_allocated }}</td>
          <td>{{ model.consumption_cache.data_used }}</td>
          <td>{{ model.consumption_cache.data_unit }}</td>
          <td>{{ model.consumption_cache.cached_at }}</td>
        </tr>
      </tbody>
    </table>
  {% else %}
    <p>Empty consumption cache</p>
  {% endif %}
  {% if not model %}
    <p>Record does not exist.</p>
  {% endif %}
  <a href="{{ return_url }}" class="btn btn-default">Back</a>
{% endblock %}

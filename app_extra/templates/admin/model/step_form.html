
{% extends 'admin/master.html' %}
{% import
  'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
     <link href="{{ url_for('static',filename='css/step-form.css')}}" rel="stylesheet">
{% endblock %}

{% block body %}
{{ model_layout.messages() }}
  {% block navlinks %}
  <ul class="nav nav-tabs">
    <li class="nav-item ">
        <a href="{{ return_url }}"  class="nav-link">{{ _gettext('List') }}</a>
    </li>
     <li class="nav-item ">
        <a class="nav-link active" href="javascript:void(0)"  data-toggle="tab">{{ _gettext('Import') }}</a>
    </li>
  </ul>
  {% endblock %}

<form id="myForm" class="admin-form form-horizontal"
      action="{{ action or '' }}"


        method="POST"
          enctype="multipart/form-data"

>

        <fieldset class="top-fieldset">
            <div class="form-group">
              <div class="col-md-10">
                <ul id="progressbar">
                <li class="active step">Upload Data</li>
                <li class="step">Confirm Data</li>
                <li class="step d-none op-check">Upload Op</li>
                <li class="step d-none op-check">Confirm Op</li>
                </ul>
              </div>
            </div>

            </fieldset>

        <!-- One "tab" for each step in the form: -->
    	<fieldset class="tab">
            <div class="form-group">
              <label for="data_to_decode" class="col-md-2 control-label">Encrypted data
                &nbsp;
              </label>
              <div class="col-md-10">
                    <input type="file" name="data_to_decode" id="data_to_decode" class="form-control"/>
                    <input name="csrf_token" type="hidden" value="{{ csrf_token() }}"/>

              </div>

            </div>
              {% if has_op %}
            <div class="form-group ">
              <label for="has_encrypted_op" class="col-md-2 control-label">Has op
                &nbsp;
              </label>
              <div class="col-md-10">

                <input class="form-control" id="has_encrypted_op" name="has_encrypted_op" type="checkbox" >

              </div>

                </div>
            {% endif %}

            </fieldset>

        <fieldset class="tab">
            <div class="decrypted_data"></div>
              <input class="form-control" type="hidden" name="encoded_data" id="encoded_data">
        </fieldset>
        <fieldset class="tab d-none has-op">
           <div class="form-group">
              <label for="encoded_op" class="col-md-2 control-label">Encrypted op
                &nbsp;
              </label>
              <div class="col-md-10">

                <textarea class="form-control"  name="encoded_op" id="encoded_op">
                </textarea>

              </div>

            </div>
        </fieldset>

       <fieldset class="tab d-none has-op">
           <div class="form-group">
              <label for="encoded_op" class="col-md-2 control-label">Real op
                &nbsp;
              </label>
              <div class="col-md-10">

                <textarea class="form-control" readonly name="decoded_op" id="decoded_op">
                </textarea>

              </div>

            </div>
        </fieldset>


        <fieldset style="overflow:auto;">
            <div style="float:right; margin-top: 5px;">
                <button type="button" class="previous btn ">Previous</button>
                <button type="button" class="next btn btn-primary ">Next</button>
                <button type="button" class="submit btn btn-primary">Confirm</button>
            </div>
        </fieldset>
        <!-- Circles which indicates the steps of the form: -->

    </form>
{% endblock %}

{% block tail %}
  {{ super() }}
  {{ lib.form_js() }}
    <script src="{{ url_for('static',filename='js/jquery.validate.min.js')}}"></script>
     <script src="{{ url_for('static',filename='js/step-form.js')}}"></script>
    <script src="{{ url_for('static',filename='js/custom-steps.js')}}"></script>
{% endblock %}

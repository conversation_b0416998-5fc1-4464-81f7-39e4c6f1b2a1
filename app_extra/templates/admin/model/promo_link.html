{% extends 'admin/master.html' %}
{% import
  'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
  <link href="{{ url_for('static', filename='css/richtext.min.css') }}" rel="stylesheet">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <!--<link href="{{ url_for('static', filename='css/fontawesome.css') }}" rel="stylesheet">-->
  <style>
    /* Custom table colors */
    .table-bordered th,
    .table-bordered td {
      border-color: #ccc;
    }

    .table-bordered thead th {
      background-color: #f2f2f2;
    }

    .table-bordered tbody td {
      background-color: #fff;
    }
  </style>
{% endblock %}


{% block body %}

  <h6>Promo link</h6>


  {% if promo_link %}

    {{ promo_link }}
  {% endif %}

  {% if not model %}
    <p>Record does not exist.</p>
  {% endif %}

  <a href="{{ return_url }}" class="btn btn-default">Back</a>
{% endblock %}

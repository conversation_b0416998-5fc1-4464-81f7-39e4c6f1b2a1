{% import 'admin/layout.html' as layout with context -%}
{% import 'admin/static.html' as admin_static with context %}
<!DOCTYPE html>
<html>
  <head>
    <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}{{ admin_view.name }}{% endblock %}</title>
    {% block head_meta %}
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <meta name="author" content="">
    {% endblock %}
    {% block head_css %}
        <link href="{{ url_for('static',filename='css/bootstrap.min.css')}}" rel="stylesheet">
        <link href="{{ url_for('static',filename='css/bootstrap.css')}}" rel="stylesheet">

        <link href="{{ url_for('static',filename='css/icons.css')}}" rel="stylesheet">
        <link href="{{ admin_static.url(filename='admin/css/bootstrap3/admin.css', v='1.1.1') }}" rel="stylesheet">
        <link
          href="{{ admin_static.url(filename='vendor/select2/select2.css', v='3.5.2') }}"
          rel="stylesheet"
        />
                <link
          href="{{ admin_static.url(filename='vendor/select2/select2-bootstrap3.css', v='1.4.6') }}"
          rel="stylesheet"
        />
    	<link href="{{ admin_static.url(filename='admin/css/bootstrap3/submenu.css') }}" rel="stylesheet">
        {% if admin_view.extra_css %}
          {% for css_url in admin_view.extra_css %}
            <link href="{{ css_url }}" rel="stylesheet">
          {% endfor %}
        {% endif %}
        <style>
        body {
            padding-top: 4px;
        }
        .select2-container--default .select2-selection--single{
             border: 0px !important;
             border-bottom: 1px solid #aaa !important;
             border-radius: 0px !important;
        }

        </style>
    {% endblock %}
    {% block head %}
    {% endblock %}
    {% block head_tail %}
    {% endblock %}
  </head>
  <body>
    {% block page_body %}
    <div class="container{%if config.get('FLASK_ADMIN_FLUID_LAYOUT', False) %}-fluid{% endif %}">

      <nav class="navbar navbar-expand-lg navbar-light bg-light" role="navigation">
        <!-- Brand and toggle get grouped for better mobile display -->

      {% block brand %}
          <a class="navbar-brand" href="{{ admin_view.admin.url }}">{{ admin_view.admin.name }}</a>
          {% endblock %}
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#admin-navbar-collapse" aria-controls="admin-navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>

        <!-- navbar content -->
        <div class="collapse navbar-collapse" id="admin-navbar-collapse">
          {% block main_menu %}
          <ul class="navbar-nav mr-auto">
            {{ layout.menu() }}
          </ul>
          {% endblock %}

          {% block menu_links %}
          <ul class="nav navbar-nav navbar-right">
            {{ layout.menu_links() }}
          </ul>
          {% endblock %}
          {% block access_control %}
          {% endblock %}
        </div>
      </nav>

      {% block messages %}
      {{ layout.messages() }}
      {% endblock %}

      {# store the jinja2 context for form_rules rendering logic #}
      {% set render_ctx = h.resolve_ctx() %}

      {% block body %}{% endblock %}
    </div>
    {% endblock %}

    {% block tail_js %}
    <script src="{{ url_for('static',filename='js/jquery-3.5.1.min.js')}}"  type="text/javascript"></script>
    <script src="{{ url_for('static',filename='js/popper.min.js')}}"  type="text/javascript"></script>
    <script src="{{ url_for('static',filename='js/bootstrap.js')}}"  type="text/javascript"></script>
    <script src="{{ admin_static.url(filename='vendor/moment.min.js', v='2.22.2') }}" type="text/javascript"></script>
   <!--<script src="{{ url_for('static',filename='js/select2.full.js') }}" type="text/javascript"></script>
     -->
     <script
            src="{{ admin_static.url(filename='vendor/select2/select2.min.js', v='3.5.2') }}"
            type="text/javascript"
    ></script>
        <script src="{{ admin_static.url(filename='admin/js/helpers.js', v='1.0.0') }}" type="text/javascript"></script>
    <script src="{{ url_for('static',filename='admin/js/icon.js') }}" type="text/javascript"></script>
    <script type="text/javascript">
    $( document ).ready(function() {
        $(".js-select2").select2()

    $(".js-select2").on('change', function(e) {
        console.log($(this).val())
            var csrftoken = "{{ csrf_token() }}"
                 $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", csrftoken)
                    }
                },
                url: "/change_tenant",
                data:{
                    'tenant': $(this).val()
                },
                 method: "POST",
                datatype: "json",
                success: function(result){
                   window.location =window.location.href
                }
                });
             $.ajax();
        });
        })

$(document).ready(function() {
     $('#from_bundle').select2();
     $('#to_bundle').select2();
    $('#vendor_name').change(function() {
        var vendorName = $(this).val();
        var csrfToken = $('input[name=csrf_token]').val();
        // Make AJAX request to fetch bundles based on selected vendor name
        $.ajax({
            url: '/get_bundles', // URL to Flask route that returns bundles
            method: 'POST',
            data: { vendor_name: vendorName ,     csrf_token: csrfToken },
            success: function(response) {
                // Clear existing options and add new options
                $('#from_bundle').empty();
                $('#to_bundle').empty();
                console.log(response.bundles)
                response.bundles.forEach(function(bundle) {
                    $('#from_bundle').append($('<option>', {
                        value: bundle,
                        text: bundle
                    }));
                    $('#to_bundle').append($('<option>', {
                        value: bundle,
                        text: bundle
                    }));
                });
            }
        });
    });
    $('#from_bundle').change(function() {
        var bundle_code = $(this).val();
        var csrfToken = $('input[name=csrf_token]').val();
        // Make AJAX request to fetch bundles based on selected vendor name
        $.ajax({
            url: '/free_profiles', // URL to Flask route that returns bundles
            method: 'POST',
            data: { bundle_code: bundle_code ,     csrf_token: csrfToken },
            success: function(response) {
                // Clear existing options and add new options

                $("#free_profiles").val(response.data)


            }
        });
    });
});


       </script>
    {% if admin_view.extra_js %}
      {% for js_url in admin_view.extra_js %}
        <script src="{{ js_url }}" type="text/javascript"></script>
      {% endfor %}
    {% endif %}
    {% endblock %}

    {% block tail %}
    {% endblock %}
  </body>
</html>

{% extends 'admin/master.html' %}
{% import 'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
  <link href="{{ url_for('static', filename='css/richtext.min.css') }}" rel="stylesheet">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <style>
    /* Custom table colors */
    .table-bordered th,
    .table-bordered td {
      border-color: #ccc;
    }

    .table-bordered thead th {
      background-color: #f2f2f2;
    }

    .table-bordered tbody td {
      background-color: #fff;
    }
  </style>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    // Function to apply sorting and filtering when the buttons are clicked
    function applySortingAndFiltering() {
      var sortField = $('#sort-field').val();
      var filterText = $('#filter-text').val();

      // Redirect to the current page with query parameters for sorting and filtering
      var queryParams = {
        sort: sortField,
        filter: filterText
      };
      var currentUrl = window.location.pathname;
      var queryString = $.param(queryParams);
      var newUrl = currentUrl + '?' + queryString;

      window.location.href = newUrl;
    }

    // Attach a click event handler to the Apply button
    $(document).ready(function () {
      $('#apply-button').click(function () {
        applySortingAndFiltering();
      });
    });
  </script>
</head>

{% endblock %}

{% block body %}

<h6>Inventory Information</h6>

{% if data and column_list %}
<table class="table table-bordered">
  <thead>
    <tr>
      {% for key in column_list %}
        <th>{{ key }}</th>
      {% endfor %}
    </tr>
  </thead>
  <tbody>
    {% for bundle in data %}
      <tr>
        {% for key in column_list %}
          <td><b>{{ bundle[key] }}</b></td>
        {% endfor %}
      </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<a href="{{ return_url }}" class="btn btn-default">Back</a>
</div>

{% endblock %}

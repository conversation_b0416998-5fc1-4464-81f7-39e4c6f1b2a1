{# templates/admin/user_email_templates_edit_create_page.html #}
{% extends 'admin/model/edit.html' %}

{% block body %}
    {{ super() }}

    <style>
        /* Style for the preview button */
        .preview-button {
            margin-left: 1%; /* Align with the form fields */
            padding: 10px 20px; /* Button padding */
            font-size: 14px; /* Font size */
            background-color: #28a745; /* Button color */
            color: white; /* Text color */
            border: none; /* Remove border */
            border-radius: 4px; /* Rounded corners */
            cursor: pointer; /* Pointer cursor on hover */
        }

        /* Hover effect for the preview button */
        .preview-button:hover {
            background-color: #218838; /* Darker green on hover */
        }

        /* Disabled state for the button */
        .preview-button:disabled {
            background-color: #6c757d; /* Gray color for disabled button */
            cursor: not-allowed; /* Cursor to indicate it's not clickable */
        }

        /* Extra margin for proper spacing */
        .form-group.preview-button-group {
            margin-bottom: 20px;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function () {

/*
            // Function to populate language select options
            function populateLanguageSelect(selectElement) {
                // Clear existing options
                selectElement.innerHTML = '';

                // Find the index of the select element in the list of select elements
                var selectIndex = Array.from(document.querySelectorAll('select[name$="-language_code"]')).indexOf(selectElement);

                // Create and append the default option based on current_languages
                var defaultOption = document.createElement('option');

                // Check if the select element has a corresponding entry in current_languages
                var currentLang = langData["current_languages"].find(function (lang) {
                    return lang.index === selectIndex;
                });

                if (currentLang) {
                    // Find the language details from the languages list
                    var language = langData["languages"].find(function (lang) {
                        return lang.code === currentLang.code;
                    });

                    if (language) {
                        defaultOption.textContent = language.display_name; // Use the display_name from languages
                        defaultOption.value = language.code; // Use the code from languages
                    } else {
                        // Fallback to default text if language details are not found
                        defaultOption.textContent = 'Select Language';
                        defaultOption.value = '';
                    }
                } else {
                    // No currentLanguages entry found, set the default option
                    defaultOption.textContent = 'Select Language';
                    defaultOption.value = '';
                }

                selectElement.appendChild(defaultOption);

                // Add options from langData
                langData["languages"].forEach(function (lang) {
                    var option = document.createElement('option');
                    option.value = lang.code; // Use the code as the value
                    option.textContent = lang.display_name; // Use the display_name as the text
                    selectElement.appendChild(option);
                });
            }
*/
            // Function to add preview buttons under the 'content' fields in all contents-X divs
            function addPreviewButtons() {
                // Select all divs that match the pattern contents-X
                var contentDivs = document.querySelectorAll('.inline-field[id^="contents-"]');

                // Loop through each of these divs
                contentDivs.forEach(function (contentDiv) {
                    // Find the select element within the current contentDiv
                    var selectElement = contentDiv.querySelector('select[name$="-language_code"]');

                    // Populate the select element with language options
                    if (selectElement) {
                        populateLanguageSelect(selectElement);
                    }

                    // Find the form-group div that contains the content field within the current contentDiv
                    var contentFormGroup = contentDiv.querySelector('.form-group textarea[id$="-content"]').closest('.form-group');

                    // Check if the preview button div already exists
                    if (contentFormGroup && !contentDiv.querySelector('.preview-button-group')) {
                        // Create the preview button
                        var previewButton = document.createElement('button');
                        previewButton.type = 'button';
                        previewButton.className = 'btn btn-secondary preview-button';
                        previewButton.innerText = 'Preview HTML Content';
                        previewButton.disabled = true; // Initially disable the button

                        // Add an event listener to the button
                        previewButton.addEventListener('click', function () {
                            // Get the content from the textarea
                            var content = contentDiv.querySelector('textarea[name$="-content"]').value;

                            // Open a new window
                            var previewWindow = window.open('', '_blank');

                            // Write the HTML content to the new window
                            previewWindow.document.open();
                            previewWindow.document.write(`
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <title>HTML Preview</title>
                            </head>
                            <body>
                                ${content}
                            </body>
                            </html>
                        `);
                            previewWindow.document.close();
                        });

                        // Create a new div with class form-group to wrap the button
                        var buttonWrapper = document.createElement('div');
                        buttonWrapper.className = 'form-group preview-button-group';

                        // Append the button to the buttonWrapper
                        buttonWrapper.appendChild(previewButton);

                        // Insert the buttonWrapper after the contentFormGroup div
                        contentFormGroup.parentNode.insertBefore(buttonWrapper, contentFormGroup.nextSibling);

                        // Function to update the button's disabled state
                        function updateButtonState() {
                            var content = contentDiv.querySelector('textarea[name$="-content"]').value;
                            previewButton.disabled = !content.trim(); // Enable if content is not empty
                        }

                        // Add event listener to update button state on input changes
                        contentDiv.querySelector('textarea[name$="-content"]').addEventListener('input', updateButtonState);

                        // Initial check to set the button state
                        updateButtonState();
                    }
                });
            }

            // Initially add preview buttons to existing content divs
            addPreviewButtons();

            // Monitor for dynamically added content divs and select elements
            const observer = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.addedNodes.length > 0) {
                        // Check for newly added content divs
                        mutation.addedNodes.forEach(function (node) {
                            if (node.nodeType === 1 && node.matches('.inline-field[id^="contents-"]')) {
                                addPreviewButtons(); // Re-run the function to handle new divs
                            }
                        });

/*
                        // Check for newly added select elements
                        var newSelects = document.querySelectorAll('select[name$="-language_code"]');
                        newSelects.forEach(function (selectElement) {
                            if (selectElement.querySelector('option') === null) { // Check if it's not yet populated
                                populateLanguageSelect(selectElement);
                            } else {
                            // Add a change event listener to already populated selects
                            selectElement.addEventListener('change', function () {
                                console.log('selectElement.textContent:', selectElement.textContent);
                                console.log('selectElement.value:', selectElement.value);
                            });
                        }
                        });*/
                    }
                });
            });

            // Observe the entire document for changes
            observer.observe(document.body, {childList: true, subtree: true});
        });
    </script>
{% endblock %}


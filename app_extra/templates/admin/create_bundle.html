{% extends 'admin/master.html' %}
{% import 'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
  <link href="{{ url_for('static', filename='css/richtext.min.css') }}" rel="stylesheet">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
     <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
 </head>

{% endblock %}

{% block body %}

    {% with messages = get_flashed_messages() %}
          {% if messages %}
            <ul class="flash-messages">
              {% for message in messages %}
                <li>{{ message }}</li>
              {% endfor %}
            </ul>
          {% endif %}
    {% endwith %}
    <div class="col-md-10">


    <form method="post" action="">
        {{ form.hidden_tag() }}
        {{ form.csrf_token }}
        <div>
            <label for="vendor_name">{{ form.vendor_name.label }}</label>
            {{ form.vendor_name(id="vendor_name", class="form-control", onchange="fetchZones(this.value)") }}
        </div>
        <div>
            <label for="bundle_prefix">{{ form.bundle_prefix.label }}</label>
            {{ form.bundle_prefix(class="form-control") }}
        </div>
        <div>
            <label for="bundle_name">{{ form.bundle_name.label }}</label>
            {{ form.bundle_name(class="form-control") }}
        </div>
        <div>
            <label for="bundle_marketing_name">{{ form.bundle_marketing_name.label }}</label>
            {{ form.bundle_marketing_name(class="form-control") }}
        </div>
        <div>
            <label for="data_amount">{{ form.data_amount.label }}</label>
            {{ form.data_amount(class="form-control") }}
        </div>

        <div>
            <label for="data_unit">{{ form.data_unit.label }}</label>
            {{ form.data_unit(id="data_unit", class="form-control") }}
        </div>
        <div>
            <label for="validity">{{ form.validity.label }}</label>
            {{ form.validity(class="form-control") }}
        </div>
        <div>
            <label for="zone_name">{{ form.zone_name.label }}</label>
            {{ form.zone_name(id="zone_name", class="form-control", onchange="fetchCountries(this.value)") }}
        </div>
        <div id="countries-list">  </div>
        <div>
            <label for="retail_price">{{ form.retail_price.label }}</label>
            {{ form.retail_price(class="form-control") }}
        </div>
        <div>
            <label for="unit_price">{{ form.unit_price.label }}</label>
            {{ form.unit_price(class="form-control") }}
        </div>
        <button type="submit" name="submit" class="btn btn-primary">Create Bundle</button>
    </form>
  </div>
    <script type="text/javascript">
        const zoneSelect = document.getElementById('zone_name');
        const countriesList = document.getElementById('countries-list');
        function fetchCountries(zone) {
        axios.get(`/countries-by-zone?zone=${zone}`).then(function(response) {
            countriesList.innerHTML = ''; // Clear previous entries
            const countries = response.data;
            if (countries.length === 0) {
                countriesList.innerHTML = '<p>No countries available for this zone.</p>';
            } else {
                const ul = document.createElement('ul');
                countries.forEach(function(country) {
                    const li = document.createElement('li');
                    li.textContent = country;
                    ul.appendChild(li);
                });
                countriesList.appendChild(ul);
            }
        }).catch(function(error) {
            console.error('Error fetching the countries:', error);
            document.getElementById('countries-list').innerHTML = '<p>Error loading countries.</p>';
        });
    }
        function fetchZones(vendor) {
            countriesList.innerHTML = '';
                axios.get(`/get_zones_by_vendor?vendor=${vendor}`).then(function(response) {
                    zoneSelect.innerHTML = '';
                    const zones = response.data;
                    if (zones.length === 0) {
                        zoneSelect.disabled = true;
                        zoneSelect.appendChild(new Option("No countries available", ""));
                    }
                    else {
                        zoneSelect.disabled = false;
                        zones.forEach(function(zone) {
                            zoneSelect.appendChild(new Option(zone, zone));
                        });
                    }
                    if(zones.length === 1){
                        fetchCountries(zones[0])
                    }
                }).catch(function(error) {
                    console.error('Error fetching the countries:', error);
                });
            }

    </script>
{% endblock %}


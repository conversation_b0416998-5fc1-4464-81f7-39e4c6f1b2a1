{% extends 'admin/model/list.html' %}

{% block body %}
    {{ super() }}
    {% set view_name = admin_view.name + '.import_csv' %}
    <form action="{{ url_for(view_name|lower|escape|replace(" ", "")) }}"
          method="POST"
          enctype="multipart/form-data"
          class="admin-form form-inline">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <input type="hidden" name="filter" value="{{ return_url }}">
        <div class="form-group">
            <input type="file" name="file" class="form-control"/>
            <button type="submit" class="button button2">Import</button>
        </div>
    </form>
<h6><strong style="color:red;">Note:</strong> Format of import should be same as the export file</h6>

{% endblock %}
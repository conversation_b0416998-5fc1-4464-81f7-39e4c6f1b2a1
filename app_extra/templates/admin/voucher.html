{% extends 'admin/master.html' %} {% import 'admin/lib.html' as lib with context
%} {% import 'admin/static.html' as admin_static with context%} {% import
'admin/model/layout.html' as model_layout with context %} {% import
'admin/actions.html' as actionlib with context %} {% import
'admin/model/row_actions.html' as row_actions with context %}
{% block head_css %}
    {{ super() }}
    <style>
        .navbar {
            border-color: black;
            border-radius: 10px;
            border-top-left-radius: 25px;
            border-top-right-radius: 25px;
            border-bottom-left-radius: 25px;
            border-bottom-right-radius: 25px;
        }
        .navbar{
            font-weight: bold;
        }
    </style>
{% endblock head_css %}
{% block brand %}
     <a class="navbar-brand" href="#">{{ admin_view.admin.name }}</a>
 {% endblock %}

{% block page_body %}
    {{ super() }}

{% endblock %}
{% block body %}
{{ layout.messages() }}
<div class="admin-form form-horizontal" >
            <div class="card-title text-center">
            </div>
      <form class="card" action="" method="post">
<!--          <div class="row">-->
<!--              <div class="col-sm-2 col-md-5">-->
<!--              <div class="card-body p-10">-->
<!--                <div class="card-title text-center">-->
<!--                        </div>-->
            {{ form.hidden_tag() }}
            {{ lib.render_form_fields(form, form_opts=form_opts) }}

<!--            {% for field in form %}-->
<!--                <div class="form-group">-->
<!--                    {{ field(class_="form-control") }}-->
<!--                </div>-->
<!--            {% endfor %}-->



             <div class="form-group">
                    <div class="col-md-offset-2 col-md-10 submit-row">
                        <button type="submit" id="submit" name="submit" class="btn btn-primary ">Generate Voucher</button>

                    </div>
 </div>

</form>
    </div>

{% endblock %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ settings.title }}</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.12.1/css/jquery.dataTables.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        table {
            width: 100%;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        button {
            margin-bottom: 10px;
            padding: 8px 16px;
            font-size: 16px;
            cursor: pointer;
        }

        .deprecated {
            background-color: #f8d7da;
        }

        /* Light red background for deprecated rows */

        /* Custom column colors */
        td:nth-child(3) {
            background-color: #ffcccc;
        }

        /* Light red for Parent */
        td:nth-child(5) {
            background-color: #ccffcc;
        }

        /* Light green for Document */
        td:nth-child(6) {
            background-color: #ccccff;
        }

        /* Light blue for Subdocuments */
        td:nth-child(4) {
            background-color: #fff0b3;
        }

        /* Light yellow for Active */
        td:nth-child(2) {
            background-color: #ffb3b3;
        }

        td:nth-child(1) {
            background-color: #b3d9ff;
        }

        /* Light sky blue for Tenant Specific */
        td:nth-child(7) {
            background-color: #ffcc99;
        }

        /* Peach for Shared */
    </style>
</head>
<body>
<h1>{{ settings.title }}</h1>
<button onclick="generateCSVFormat()">Generate CSV</button>
<select id="parentFilter" onchange="filterParent()">
    <option value="">Filter by Error...</option>
    <!-- Options will be dynamically added based on the data -->
</select>
<br/>
<br/>
<table id="modelsTable" class="display">
    <thead>
    <tr>
        <th style="width:40%;">{{ settings.identifier_title }}</th>
        <th style="width:60%;">Error</th>
    </tr>
    </thead>
    <tbody>
    <!-- Data rows will be added here -->
    </tbody>
</table>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.12.1/js/jquery.dataTables.min.js"></script>
<script>
    const headers = ["{{ settings.identifier_title }}", "Error"];
    const error_table_index = 1
    const total_columns = 2

    $(document).ready(function () {

        window.data = {{ data | tojson }}
        var table = $('#modelsTable').DataTable({
            data: window.data,
            columns: headers.map(header => ({ title: header })),
            initComplete: function () {
                this.api().columns(error_table_index).every(function () {
                    var column = this;
                    var select = $('#parentFilter');
                    column.data().unique().sort().each(function (d, j) {

                        select.append('<option value="' + d + '">' + d + '</option>');

                    });
                });
            }
        });

        $.fn.dataTable.ext.search.push(
            function (settings, data, dataIndex) {
                var searchTerm = $('.dataTables_filter input').val();
                if (!searchTerm) {
                    return true;
                }
                var terms = searchTerm.split(',').map(term => term.trim().toLowerCase());
                return terms.every(term =>
                    data.slice(0, total_columns).some(column => column.toLowerCase().indexOf(term) > -1)
                );
            }
        );
    });

    function generateCSVFormat() {

        const csvData = [headers, ...data];

        // Convert the list of lists to CSV format
        const csvContent = csvData.map(row => row.join(",")).join("\n");

        // Create a blob with the CSV content
        const blob = new Blob([csvContent], {type: 'text/csv;charset=utf-8;'});

        // Create a link element
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "data.csv";

        // Append the link to the body and trigger the download
        document.body.appendChild(link);
        link.click();

        // Remove the link from the document
        document.body.removeChild(link);
    }

    function filterParent() {
        var selectedParent = $('#parentFilter').val();
        var table = $('#modelsTable').DataTable();
        if (selectedParent) {
            table.column(error_table_index).search(selectedParent).draw();
        } else {
            table.column(error_table_index).search('').draw();
        }
    }
</script>
</body>
</html>
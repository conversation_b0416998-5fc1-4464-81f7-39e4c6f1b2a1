{% extends "admin/master.html" %}

{% import'admin/layout.html' as layout with context %} 

{% block head_css %}
    {{ super() }}
    <style>
        .navbar {
            border-color: black;
            border-radius: 5px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
        }
        .navbar{
            font-weight: bold;
        }
    </style>
{% endblock head_css %}
{% block brand %}
     <a class="navbar-brand" href="#">{{ admin_view.admin.name }}</a>
 {% endblock %}

{% block page_body %}
    {{ super() }}

{% endblock %}
{% block body %}
{{ layout.messages() }}
<div class="row">
    <div class="col-lg-4 col-md-12">
      <form class="card" action="" method="post">
          <div class="card-body p-6">
            <div class="card-title text-center">

            </div>
            {{ form.hidden_tag() }}
            {% for field in form %}
                <div class="form-group">
                    {{ field(class_="form-control") }}
                </div>
            {% endfor %}

            <div class="form-footer">
              <button type="submit" id="submit" name="submit" class="btn btn-primary btn-block">Submit</button>
            </div>
          </div>
        </form>
    </div>
</div>
{% endblock %}
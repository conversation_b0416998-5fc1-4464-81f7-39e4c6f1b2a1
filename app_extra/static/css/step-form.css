* {
    box-sizing: border-box;
}
.tab{
    display: none;
    width: 100%;
    margin: 0px auto;
}
.current{
    display: block;
}
#myForm fieldset {
    background: white;
    border: 0 none;
    border-radius: 0px;
    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.4);
    padding: 20px 30px;
    box-sizing: border-box;
    width: 80%;
    margin: 0 10%;
    position: relative;
}
#myForm input, #myForm textarea {
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 0px;
    margin-bottom: 10px;
    width: 100%;
    box-sizing: border-box;
    color: #2C3E50;
    font-size: 13px;
}
#myForm input[type="file"]{
    box-sizing: content-box;
    border: 0px;
}
.fs-title {
    font-size: 18px;
    text-transform: uppercase;
    color: #2C3E50;
    margin-bottom: 10px;
    letter-spacing: 2px;
    font-weight: bold;
}
.fs-subtitle {
    font-weight: normal;
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
}


h1 {
    text-align: center;
}

input {
    padding: 10px;
    width: 100%;
    font-size: 17px;
    border: 1px solid #aaaaaa;
}

button {
    background-color: #4CAF50;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    font-size: 17px;
    cursor: pointer;
}
.top-fieldset {
    box-shadow: none !important;
    border: 0px !important;
}
#progressbar {
    margin-bottom: 30px;
    overflow: hidden;
    counter-reset: step;
}

#progressbar li {
    list-style-type: none;
    text-transform: uppercase;
    font-size: 9px;
    width: 50%;
    float: left;
    position: relative;
    letter-spacing: 1px;
    text-align: center;

}
#progressbar.five-steps li{
    width: 25%;
}
#progressbar li:after {
    content: '';
    width: 100%;
    height: 2px;
    background: #83a9df;
    position: absolute;
    left: -50%;
    top: 9px;
    z-index: 1; /*put it behind the numbers*/
}

#progressbar li:first-child:after {
    /*connector not needed before the first step*/
    content: none;
}

/*marking active/completed steps green*/
/*The number of the step and the connector before it = green*/
#progressbar li.active:before, #progressbar li.active:after {
    background: #467fcf;
    color: white;
}

#progressbar li.active:before, #progressbar li.active:after {
    background: #467fcf;
    color: white;
}
#progressbar li:before {
    content: counter(step);
    counter-increment: step;
    width: 24px;
    height: 24px;
    line-height: 26px;
    display: block;
    font-size: 12px;
    color: #333;
    background: white;
    border-radius: 25px;
    margin: 0 auto 10px auto;
    text-align: center;
    border: 1px solid #467fcf;
    z-index: 2;
    position: relative;
}

#progressbar li.active {
    color: #467fcf;
}

button:hover {
    opacity: 0.8;
}

.previous {
    background-color: #bbbbbb;
}

/* Make circles that indicate the steps of the form: */


.step.finish {
    background-color: #4CAF50;
}

.error {
    color: #f00;
    }
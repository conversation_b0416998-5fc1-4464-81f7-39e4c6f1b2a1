@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoon.eot?ryfr0a');
  src:  url('../fonts/icomoon.eot?ryfr0a#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?ryfr0a') format('truetype'),
    url('../fonts/icomoon.woff?ryfr0a') format('woff'),
    url('../fonts/icomoon.svg?ryfr0a#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
@font-face {
  font-family: "feather";
  src: url("../fonts/feather-webfont.eot?t=1501841394106");
  /* IE9*/
  src: url("../fonts/feather-webfont.eot?t=1501841394106#iefix") format("embedded-opentype"), url("../fonts/feather-webfont.woff?t=1501841394106") format("woff"), url("../fonts/feather-webfont.ttf?t=1501841394106") format("truetype"), url("../fonts/feather-webfont.svg?t=1501841394106#feather") format("svg");
  /* iOS 4.1- */
}


[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.fa-circle-o {
    font-family: 'feather' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.fa-circle-o:before {
    content: "\e995" !important;
}

.fa-check-circle{
font-family: 'feather' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

}
.fa-check-circle:before{
  content: "\e92b" !important;
}
.fa-minus-circle{
  font-family: 'feather' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.fa-minus-circle:before{
  content: "\e995" !important;
}

.icon-edit:before {
  content: "\e916";
}
.icon-delete:before {
  content: "\e917";
}
.icon-play-button:before {
  content: "\e915";
}
.icon-duplicate:before {
  content: "\e900";
}
.icon-off:before {
  content: "\e901";
}
.icon-on .path1:before {
  content: "\e902";
  color: rgb(211, 65, 120);
}
.icon-on .path2:before {
  content: "\e903";
  margin-left: -1em;
  color: rgb(232, 78, 137);
}
.icon-on .path3:before {
  content: "\e904";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.icon-on .path4:before {
  content: "\e905";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.icon-on .path5:before {
  content: "\e906";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.icon-on .path6:before {
  content: "\e907";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.icon-power-button:before {
  content: "\e908";
}
.icon-power-off:before {
  content: "\e909";
}
.icon-power .path1:before {
  content: "\e90a";
  color: rgb(225, 230, 233);
}
.icon-power .path2:before {
  content: "\e90b";
  margin-left: -1em;
  color: rgb(62, 166, 155);
}
.icon-power .path3:before {
  content: "\e90c";
  margin-left: -1em;
  color: rgb(100, 121, 138);
}
.icon-power .path4:before {
  content: "\e90d";
  margin-left: -1em;
  color: rgb(100, 121, 138);
}
.icon-power .path5:before {
  content: "\e90e";
  margin-left: -1em;
  color: rgb(68, 196, 161);
}
.icon-power .path6:before {
  content: "\e90f";
  margin-left: -1em;
  color: rgb(62, 166, 155);
}
.icon-power .path7:before {
  content: "\e910";
  margin-left: -1em;
  color: rgb(62, 166, 155);
}
.icon-power .path8:before {
  content: "\e911";
  margin-left: -1em;
  color: rgb(62, 166, 155);
}
.icon-turn-off:before {
  content: "\e912";
}
.icon-update-arrow:before {
  content: "\e913";
}
.icon-video-play-button:before {
  content: "\e914";
}

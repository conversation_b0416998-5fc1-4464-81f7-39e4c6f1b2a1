function updateCategoryCode(categoryCodeOptions) {
    var categorySelect = document.getElementById("category");
    var categoryCodeSelect = document.getElementById("category_code");
    var categoryValue = categorySelect.value;

    // Clear the existing options
    categoryCodeSelect.innerHTML = "";

    // Populate the category_code select field based on the selected category
    var selectedCategory = categoryCodeOptions[categoryValue];
    var selectedCategoryOptions = selectedCategory ? selectedCategory.subcategories : {};

    Object.keys(selectedCategoryOptions).forEach(function (key) {
        var codeStatus = selectedCategoryOptions[key];
        var option = document.createElement("option");
        option.value = codeStatus.name;
        option.text = codeStatus.name;
        categoryCodeSelect.add(option);
    });

    // Retain the current value if it exists
    if (categoryCodeSelect.dataset.currentValue) {
        categoryCodeSelect.value = categoryCodeSelect.dataset.currentValue;
    }

    updateCategoryCodeFields(selectedCategoryOptions);
}

function updateCategoryCodeFields(selectedCategoryOptions) {
    var categoryCodeSelect = document.getElementById("category_code");
    var categoryCodeValue = categoryCodeSelect.value;
    var selectedCodeStatus = Object.values(selectedCategoryOptions).find(function (option) {
        return option.name === categoryCodeValue;
    });

    var rangedCheckbox = document.getElementById("ranged");
    var minField = document.getElementById("min");
    var maxField = document.getElementById("max");
    var categoryValueField = document.getElementById("category_value");
    var displayName = document.getElementById("display_name");

    if (selectedCodeStatus) {
        rangedCheckbox.checked = selectedCodeStatus.is_ranged;
        minField.disabled = !selectedCodeStatus.is_ranged;
        maxField.disabled = !selectedCodeStatus.is_ranged;
        categoryValueField.disabled = !selectedCodeStatus.is_value;
        displayName.disabled = !selectedCodeStatus.has_display_name;
    }
}

function updateMinMaxFields() {
    var rangedCheckbox = document.getElementById("ranged");
    var minField = document.getElementById("min");
    var maxField = document.getElementById("max");

    if (rangedCheckbox.checked) {
        minField.disabled = false;
        maxField.disabled = false;
    } else {
        minField.disabled = true;
        maxField.disabled = true;
    }
}

// Initial update based on the default value
document.addEventListener("DOMContentLoaded", function () {
    var categoryCodeOptions = JSON.parse(document.getElementById("categoryCodeOptions").textContent);

    var categoryCodeSelect = document.getElementById("category_code");

    categoryCodeSelect.dataset.currentValue = categoryCodeSelect.value;  // Save the current value

    updateCategoryCode(categoryCodeOptions);

    document.getElementById("category").addEventListener("change", function () {
        updateCategoryCode(categoryCodeOptions);
    });

    categoryCodeSelect.addEventListener("change", function () {
        updateCategoryCodeFields(categoryCodeOptions[document.getElementById("category").value].subcategories);
    });

    updateMinMaxFields();

    document.getElementById("ranged").addEventListener("change", updateMinMaxFields);
});

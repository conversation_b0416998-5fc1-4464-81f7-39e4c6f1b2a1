  $(document).ready(function(){
        var val = {
            // Specify validation rules
            rules: {
                data_to_decode: "required",
            },
            // Specify validation error messages
            messages: {
                data_to_decode:      "Please enter data",

            }
        }
        $("#myForm").multiStepForm(
        {
            defaultStep:0,
            beforeSubmit : function(form, submit){
                console.log("called before submiting the form");
                console.log(form);
                console.log(submit);
            },
            hideBackButton : true,
            validations:val,
        }
        ).navigateTo(0);
    });
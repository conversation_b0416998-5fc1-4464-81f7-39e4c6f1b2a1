function updateSubCategoryCode(subcategoryOptions) {

    var categorySelect = document.getElementById("category");
    var subcategorySelect = document.getElementById("subcategory");
    var categoryValue = categorySelect.value;

    // Clear the existing options
    subcategorySelect.innerHTML = "";

    // Populate the category_code select field based on the selected category
    var selectedCategory = subcategoryOptions[categoryValue];
    var selectedCategoryOptions = selectedCategory ? selectedCategory.subcategories : {};

    Object.keys(selectedCategoryOptions).forEach(function (key) {
        var codeStatus = selectedCategoryOptions[key];
        var option = document.createElement("option");
        option.value = codeStatus;
        option.text = codeStatus;
        subcategorySelect.add(option);
    });

    if (categoryValue === subcategorySelect.dataset.originalCategory){
            subcategorySelect.dataset.currentValue= subcategorySelect.dataset.originalValue;
    }

    // Retain the current value if it exists
    if (subcategorySelect.dataset.currentValue && subcategorySelect.dataset.currentValue !== "") {
        subcategorySelect.value = subcategorySelect.dataset.currentValue;
        subcategorySelect.dataset.currentValue = ""
    }

}

// Initial update based on the default value
document.addEventListener("DOMContentLoaded", function () {
    var subcategoryOptions = JSON.parse(document.getElementById("subcategoryOptions").textContent);

    var subcategorySelect = document.getElementById("subcategory");

    subcategorySelect.dataset.originalCategory = document.getElementById("category").value;

    // Store the original default value
    if (!subcategorySelect.dataset.originalValue) {
        subcategorySelect.dataset.originalValue = subcategorySelect.value;
    }

    subcategorySelect.dataset.currentValue = subcategorySelect.value;  // Save the current value

    updateSubCategoryCode(subcategoryOptions);

    document.getElementById("category").addEventListener("change", function () {
        updateSubCategoryCode(subcategoryOptions);
    });
});

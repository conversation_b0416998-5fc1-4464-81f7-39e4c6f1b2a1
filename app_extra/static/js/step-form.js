(function ( $ ) {
    $.fn.multiStepForm = function(args) {
        if(args === null || typeof args !== 'object' || $.isArray(args))
            throw  " : Called with Invalid argument";
            var form = this;
            var tabs = form.find('.tab');
            var steps = form.find('.step');
            steps.each(function(i, e){
                $(e).on('click', function(ev){
                });
            });
            form.navigateTo = function (i) {/*index*/

            var tabs = form.find('.tab');
            /*Mark the current section with the class 'current'*/
            tabs.removeClass('current').eq(i).addClass('current');
            // Show only the navigation buttons that make sense for the current section:
            form.find('.previous').toggle(i > 0);
            atTheEnd = i >= tabs.length - 1;
            form.find('.next').toggle(!atTheEnd);
            // console.log('atTheEnd='+atTheEnd);
            form.find('.submit').toggle(atTheEnd);
            fixStepIndicator(curIndex());
            return form;
        }
        function curIndex() {
            var tabs = form.find('.tab');
            /*Return the current index by looking at which section has the class 'current'*/
            return tabs.index(tabs.filter('.current'));
        }
        function fixStepIndicator(n) {
            steps.each(function(i, e){
                i == n ? $(e).addClass('active') : $(e).removeClass('active');
            });
        }
        /* Previous button is easy, just go back */
        form.find('.previous').click(function() {
            form.navigateTo(curIndex() - 1);
        });

        /* Next button goes forward iff current block validates */
        form.find('.next').click(function() {
            if('validations' in args && typeof args.validations === 'object' && !$.isArray(args.validations)){
                if(!('noValidate' in args) || (typeof args.noValidate === 'boolean' && !args.noValidate)){
                    form.validate(args.validations);

                    if(form.valid() == true){
                        if (curIndex() == 0){
                            if($("#has_encrypted_op").is(':checked')){
                                $(".op-check").removeClass('d-none')
                                $(".has-op").removeClass('d-none').addClass('tab')
                                var tabs = form.find('.tab');

                                $("#progressbar").addClass('five-steps')
                            }
                            else{
                                 $(".op-check").addClass('d-none')
                                 $(".has-op").addClass('d-none').removeClass('tab')
                                 $("#progressbar").removeClass('five-steps')
                                  var tabs = form.find('.tab');
                            }
                            decrypt_data()
                        }
                   form.navigateTo(curIndex() + 1);


                        return true;
                    }
                    return false;
                }
            }
            i = curIndex() + 1
            form.navigateTo(i);
        });
        form.find('.submit').on('click', function(e){
            if(typeof args.beforeSubmit !== 'undefined' && typeof args.beforeSubmit !== 'function')
                args.beforeSubmit(form, this);
                /*check if args.submit is set false if not then form.submit is not gonna run, if not set then will run by default*/
            if(typeof args.submit === 'undefined' || (typeof args.submit === 'boolean' && args.submit)){
                form.submit();
            }
            return form;
        });
        /*By default navigate to the tab 0, if it is being set using defaultStep property*/
        typeof args.defaultStep === 'number' ? form.navigateTo(args.defaultStep) : null;
        form.noValidate = function() {

        }
        return form;
    };
    function decrypt_data(data_to_decode){
         var csrf_token = $("input[name=csrf_token]").val();
         var file_data = $('#data_to_decode').prop('files')[0];
         var form_data = new FormData();
         form_data.append('file', file_data);

    $.ajax({
        url: 'decode/', // point to server-side PHP script
        cache: false,
        contentType: false,
        processData: false,
        headers: {
            "X-CSRFToken": csrf_token,
        },
        data: form_data,
        type: 'post',
        success: function(data){
             $('#encoded_data').text(JSON.stringify(JSON.parse(data)));
             fill_form(JSON.parse(data))
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            console.log("errorThrown",errorThrown)
        }
     });

    }
    function fill_form(data){

            $.each(data.results, function(id, obj) {

                $(".decrypted_data").append('<div class="item new_item_'+id+' my-3 p-3 bg-white rounded shadow-sm">\n' +
                    '    <h5 class="border-bottom pb-2 mb-0">New Item</h5>\n' +
                    '                     </div>')
                $.each(obj, function (propName, value) {
                      $(".new_item_" + id).append('<div class="d-flex pt-3">\n' +
                    '      <h6 class="my-0">\n' +
                    '        <strong class="d-block text-gray-dark">' + propName + ' : </strong>\n' +

                    '      </h6>\n' +
                    '<h6 class="my-0"> ' + value + '</h6>' +
                    '    </div>')
                });
             });

    }
}( jQuery ));
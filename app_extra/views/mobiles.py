from app_models.main_models import OperatorList
from app_models.mobiles import App<PERSON>ist as AppListModel
from app_models.mobiles import AppVersionList as AppVersionListModel
from b2c_helpers.support_helper import generate_unique_ref_code
from flask import flash, Markup
from flask_admin.actions import action
from flask_admin.babel import gettext
from mongoengine.context_managers import switch_db
from wtforms import <PERSON><PERSON>ield

from app_helpers import main_helper
from app_helpers.admin_helper import get_tenant
from .main_views import ModelView1, ModelView2

method_was_changed = 'Method was changed'
change_method = 'Couldn`t change method'


def prepare_form_app_list(form):
    form.op_name.choices = [(obj.op_name, obj.op_name) for obj in OperatorList.objects()]
    return form


def prepare_form_app_version_list(form):
    form.app_id.choices = [(obj._id, "{}-{}-{}".format(obj.app_name, obj.op_name, obj.app_platform)) for obj in
                           AppListModel.objects()]
    return form


class AppUserDetails(ModelView1):
    form_excluded_columns = ('user_image', 'image_extension')
    column_filters = ('referral_code', 'user_email', 'user_token', 'version_token', 'create_date', 'language','fcm_token', 'block_transactions', 'unblock_counter')

    # Maps the document field name to the new display name
    column_labels = {
        'verify_count': 'Verify Counter'
    }

    @action('Generate Referral Code In bulk', 'Generate referral code for selected users',
            'Are you sure you want to generate a referral code for selected users?')
    def generate_referral_codes(self, ids):
        try:
            result = get_tenant()

            if result:

                succeeded_code_generation_total = 0
                failed_code_generation_total = 0
                failed_attempts = []

                with switch_db(self.model, result) as CustomModel:
                    for id_ in ids:
                        app_user = CustomModel.objects(id=id_).first()

                        new_referral_code = generate_unique_ref_code(length=8, total_number_of_retries=5)

                        if new_referral_code is not None:
                            app_user.referral_code = new_referral_code
                            app_user.save()

                            succeeded_code_generation_total += 1
                        else:
                            failed_attempts.append(app_user.user_token)
                            failed_code_generation_total += 1

                # -----------------------------------------------------------------------------------------------------
                # Error Display preparations
                # -----------------------------------------------------------------------------------------------------
                failed_attempts_string = ', '.join(failed_attempts)  # Prepare string for the "Copy" button

                failed_generation_user_identifier_message = "User Token"

                code_generation_message = ""
                if succeeded_code_generation_total > 0:
                    code_generation_message = code_generation_message + f"[ {succeeded_code_generation_total} successful"

                if failed_code_generation_total > 0:
                    if succeeded_code_generation_total > 0:
                        code_generation_message = code_generation_message + ", "
                    else:
                        code_generation_message = f"[ {code_generation_message}"

                    code_generation_message = code_generation_message + f"{failed_code_generation_total} failed"

                code_generation_message = code_generation_message + f" ] / {succeeded_code_generation_total + failed_code_generation_total}"


                # Check if there are failed attempts and conditionally prepare the button HTML
                copy_failed_button_html = f"""
                <button style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; border-radius: 5px; background-color: #f0f0f0;" onclick="var textArea = document.createElement('textarea'); textArea.value = '{failed_attempts_string}'; document.body.appendChild(textArea); textArea.select(); document.execCommand('Copy'); textArea.remove(); showToast();">Copy Failed</button>
                """ if failed_code_generation_total > 0 else ""

                flash_message = Markup(f"""
                <div>
                    <strong>Referral Code Generation Summary:</strong> {code_generation_message}
                    {copy_failed_button_html}
                </div>
                <div id="toast" style="visibility: hidden; min-width: 250px; margin-left: -125px; background-color: #333; color: #fff; text-align: center; border-radius: 2px; padding: 16px; position: fixed; z-index: 1; left: 50%; bottom: 30px; font-size: 17px;">
                    Copied {failed_generation_user_identifier_message} to clipboard!
                </div>
                <script>
                """"""
                function showToast() {
                    var toast = document.getElementById("toast");
                    toast.style.visibility = "visible";
                    setTimeout(function()
                            {toast.style.visibility = "hidden";}, 
                            3000
                        );
                }
                </script>
                """)

                flash(flash_message)

        except Exception as e:
            print("Exception in referral code generation as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to generate referral code for selected users. %(error)s', error=str(e)),
                  'error')

    @action('Unblock User', 'Unblock User', 'Are you sure you want to unblock?')
    def unblock_user(self, ids):
        try:
            for id_ in ids:
                user = self.model.objects(id=id_).first()
                user.block_transactions = False
                user.unblock_counter = 1
                user.permanent_block = False
                user.save()
                flash(Markup('The user' + user.user_email + ' has been unblocked'))

        except Exception as e:
            print("Exception in unblocking user as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to unblock user. %(error)s', error=str(e)),'error')

class AppUserDetailsBackup(ModelView1):
    form_excluded_columns = ('user_image', 'image_extension')
    column_filters = ('referral_code', 'user_email', 'user_token', 'version_token', 'create_date', 'language','fcm_token', 'block_transactions', 'unblock_counter')

class NotificationHistory(ModelView1):
    column_filters = ('reseller_id', 'status')


class AppContactUs(ModelView1):
    pass


class AppList(ModelView1):
    column_filters = ('op_name', 'app_name', 'app_platform', 'last_version')

    form_overrides = {'op_name': SelectField}

    def create_form(self, obj=None):
        form = super().create_form(obj)
        return prepare_form_app_list(form)

    def edit_form(self, obj=None):
        form = super().edit_form(obj)
        return prepare_form_app_list(form)

    @action('Create a new Version', 'Create a new Version from the first select app', '')
    def create_new_version(self, ids):
        id_ = ids[0]
        selected_obj = self.model.objects(id_app_list=id_).first()
        last_version = selected_obj.last_version
        next_version = last_version + 1
        selected_obj.last_version = next_version
        selected_obj.save()
        version_doc = {"app_id": id_,
                       'version_number': next_version,
                       'version_token': main_helper.random_url_token(),
                       'still_valid': True}
        AppVersionListModel(**version_doc).save()


class AppVersionList(ModelView2):

    def on_model_change(self, form, model, is_created):
        if not is_created:
            return
        result = get_tenant()
        if not result:
            return
        model.switch_db(result)
        if form.version_token.data != '':
            model.version_token = form.version_token.data
        else:
            model.version_token = main_helper.random_url_token()
        model.save()


class AppRedeemCodeRecord(ModelView1):
    pass


class AppUserRequest(ModelView1):
    pass

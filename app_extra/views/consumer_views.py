from app_extra.views.main_views import ModelView1, ModelViewWithImport, ModelViewWithImportExcel, \
    ModelViewWithImportData
from app_models.consumer_models import Languages as LangCode
from wtforms import SelectField


def prepare_form_language_list(form):
    form.language_code.choices = [(obj.language_code, obj.language_code) for obj in LangCode.objects()]
    return form


def prepare_form_titles_list(form):
    form.page_title_id.choices = [('about_us', 'about_us'), ('terms_condition', 'terms_conditions'),
                                  ('privacy_policy', 'privacy_policy'), ('payment_policy', 'payment_policy')]
    form.language_code.choices = [(obj.language_code, obj.language_code) for obj in LangCode.objects()]
    return form


class Bundles(ModelView1):
    column_filters = ('bundle', 'country', 'package', 'price')


class FAQ(ModelViewWithImportData):
    form_overrides = {'language_code': SelectField}
    column_to_import = (
        "question_text_en", "answer_en", "question_text_ar", "answer_ar", "question_text_de", "answer_de",
        "question_text_fr", "answer_fr",
        "question_text_es", "answer_es", "question_text_pt", "answer_pt", "question_text_zh", "answer_zh"
    )
    model_type = 'faq'

    def create_form(self, obj=None):
        form = super().create_form(obj)
        return prepare_form_language_list(form)

    def edit_form(self, obj=None):
        form = super().edit_form(obj)
        return prepare_form_language_list(form)


class Countries(ModelViewWithImport):
    column_list = ('country_name', 'iso2_code', 'iso3_code', 'alternative_country')
    column_to_import = ('country_name', 'iso2_code', 'iso3_code', 'alternative_country')
    column_filters = ('country_name', 'iso2_code', 'iso3_code')
    pass


class Zones(ModelViewWithImport):
    column_list = ("vendor_name", "zone_name", "country_list", "country_code_list", "roaming_zones")
    column_to_import = ("vendor_name", "zone_name", "country_list", "country_code_list", "roaming_zones")
    column_filters = ("vendor_name", "zone_name", "roaming_zones")
    pass


class CoveredAreas(ModelViewWithImportExcel):
    column_list = ('country_name', 'current_zone', 'region_code', 'region_name')
    column_filters = column_list


class Page(ModelViewWithImportData):
    column_list = ('page_title_id', 'page_title', 'page_intro', 'page_content', 'language_code')
    column_filters = ('page_title_id', 'page_title', 'page_intro', 'page_content', 'language_code')
    column_to_import = ('page_title_id',
                        'page_title_en', 'page_intro_en', 'page_content_en', 'language_code',
                        'page_title_es', 'page_intro_es', 'page_content_es',
                        'page_title_de', 'page_intro_de', 'page_content_de',
                        'page_title_ar', 'page_intro_ar', 'page_content_ar',
                        'page_title_fr', 'page_intro_fr', 'page_content_fr',
                        'page_title_zh', 'page_intro_zh', 'page_content_zh',
                        'page_title_pt', 'page_intro_pt', 'page_content_pt')

    form_overrides = {'page_title_id': SelectField, 'language_code': SelectField}
    model_type = 'page'

    def create_form(self, obj=None):
        form = super().create_form(obj)
        return prepare_form_titles_list(form)

    def edit_form(self, obj=None):
        form = super().edit_form(obj)
        return prepare_form_titles_list(form)


class Languages(ModelView1):
    pass


class MCCMNC(ModelViewWithImport):
    column_list = ('mcc', 'mnc', 'iso2_code', 'country', 'country_code')
    column_to_import = ('mcc', 'mnc', 'iso2_code', 'country', 'country_code')
    column_filters = column_list
    pass

class Regions(ModelViewWithImport):
    column_list = ('region_code', 'region_name', 'zone_name')
    column_to_import = ('region_code', 'region_name', 'zone_name')
    column_filters = ('region_code', 'region_name', 'zone_name')
    pass
import datetime

import flask_admin as admin
from flask import jsonify, make_response
#from app_helpers import admin_logging

import flask_login as login
from flask import (flash, redirect, url_for,
                   request)
from flask_admin import expose
from flask_bcrypt import check_password_hash, generate_password_hash
from flask_login import (login_user, logout_user,
                         current_user)

from app_extra.views import admin_forms as forms
from app_helpers.admin_helper import update_expiration_date, validate_new_pass, password_check
from app_helpers.cron_helpers import return_health_check
from app_models import main_models, consumer_models

def redirect_to_login_view():
    return redirect(url_for('admin.login_view'))


class AppAdminView(admin.AdminIndexView):
    @expose('/')
    def index(self):
        if not login.current_user.is_authenticated:
            print("User is_authenticated is False" ,  flush=True)
            return redirect_to_login_view()
        return self.render('admin/index.html')

    @expose('/get_bundles', methods=['POST', 'GET'])
    def get_bundles_view(self):

        vendor_name = request.form.get('vendor_name')
        # Fetch bundles based on vendor name (replace this with your actual logic)
        bundles = consumer_models.Bundles.objects(vendor_name=vendor_name).only('bundle_code')
        bundle_codes = [bundle.bundle_code for bundle in bundles]
        return jsonify({'bundles': bundle_codes})

    @expose('/free_profiles', methods=['POST', 'GET'])
    def get_free_profiles(self):

        bundle_code = request.form.get('bundle_code')
        profiles = consumer_models.Profiles.objects(bundle_code=bundle_code, availability="Free").count()

        return jsonify({'data': profiles})


    @expose('/login_factor/', methods=('GET', 'POST'))
    def login_factor_view(self):
        """User login factor route."""
        if current_user.is_authenticated:
            # if user is logged in we get out of here
            return redirect(url_for('admin.index'))
        form = forms.LoginFactorForm()
        if form.validate_on_submit():
            user = main_models.User.objects(email=form.email.data).first()
            if user is None:
                flash("Your email or password doesn't match!!!")
                return redirect(url_for('admin.login_factor_view'))
            if user.has_salt:
                flash("Already has otp")
                return redirect_to_login_view()

            # check password and redirect
            if check_password_hash(user.password, form.password.data):
                user.has_salt = True
                user.save()
                flash("You've been logged in!", "success")
                login_user(user)
                return redirect(url_for('admin.two_factor_setup'))

            return redirect(url_for('admin.login_factor_view'))
        return self.render('admin/register.html', form=form)

    @expose('/set_password/', methods=('GET', 'POST'))
    def set_password_view(self):
        form = forms.SetForm()
        print("you are here in login_view")
        if form.validate_on_submit() and password_check(form.new_password.data):

                admin_user = main_models.User.objects(email=form.email.data).first()
                if validate_new_pass(form.new_password.data, form.confirm_password.data):
                    try:
                        new_date = update_expiration_date()
                        admin_user.expiration_date = new_date
                        admin_user.is_expired = False
                        admin_user.password = generate_password_hash(form.new_password.data).decode('utf-8')
                        admin_user.save()
                        return redirect_to_login_view()
                    except Exception as e:
                        print("exception "+str(e))
                        flash('Couldn`t change your password')

                else:
                    flash("Please confirm your new password")
        return self.render('admin/login.html', form=form, form_title='Set Password')

    @expose('/login/', methods=('GET', 'POST'))
    def login_view(self):
        print("you are here in login_view")
        next_var = request.args.get('next')
        form = forms.LoginForm()
        if form.validate_on_submit():
            admin_user = main_models.User.objects(email=form.email.data).first()
            if admin_user is None:
                flash("Your email or password doesn't match.")
            else:
                if check_password_hash(admin_user.password, form.password.data):
                    if not admin_user.is_expired:
                        flash("You've been logged in!", "success")
                        login_user(admin_user)
                        admin_user.last_login = datetime.datetime.utcnow()
                        admin_user.save()
                        #postgress.session.commit()
                        return redirect(next_var or url_for('admin.index'))
                    return redirect(url_for('admin.set_password_view'))
                flash("Your email or password doesn't match.")
        return self.render('admin/login.html', form=form, form_title='Login')

    @expose('/logout/')
    def logout_view(self):
        logout_user()
        flash("You've been logged out! Come back soon!", "success")
        return redirect_to_login_view()

    @expose('/healthcheck')
    def health_check(self):
        return make_response("Everything working", 200)


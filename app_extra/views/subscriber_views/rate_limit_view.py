from app_models import main_models
from flask_admin.contrib.mongoengine import ModelView
from flask_login import current_user
import logging

logger = logging.getLogger(__name__)


class RateLimitLogs(ModelView):
    page_size = 100
    column_list = ('datetime', 'id_types', 'ip_address', 'device_id', 'block_type', 'blocked_accounts', 'email_list')
    column_filters = ('datetime', 'ip_address', 'device_id', 'block_type', 'blocked_accounts')
    column_default_sort = '-datetime'

    def is_accessible(self):
        try:
            current = main_models.User.objects(id=current_user.get_id()).first()
            if not current.is_admin:
                self.can_export = current.can_export
                self.can_create = 0
                self.can_edit = 0
                self.can_delete = 0
                self.can_set_page_size = 0
            return current.is_admin
        except Exception as e:
            logger.error(e)
            return False

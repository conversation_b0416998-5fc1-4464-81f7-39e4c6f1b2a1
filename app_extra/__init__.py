import base64
import pickle
import zlib
from datetime import timedelta
import flask_admin
from app_models.consumer_models import AuxClass, Zones
from app_models.user_email_template_models import UserEmailTemplates
from flask import Flask, redirect, url_for, flash, request, jsonify, render_template
from bson import ObjectId
from flask import session, json
from flask_login import LoginManager
from flask_login import current_user
from flask_mongoengine import MongoEngine
from flask_wtf.csrf import CSRFProtect
from app_extra.views import (
    admin_views,
    reseller_views,
    main_views,
    consumer_views,
    mobiles as mobiles_views,
)
from app_models import (
    main_models,
    mobiles,
    consumer_models,
    stripe_models,
    reseller_models,
    ai_assistant_models,
    user_email_template_models,
)

from app_extra.views.subscriber_views import rate_limit_view
from app_helpers.db_helper import get_vendor_zones, get_countries_by_zone
from instance import consumer_config as instance_config
import datetime
from logging_config import logging

logger = logging.getLogger(__name__)


class JSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        if isinstance(o, datetime.datetime):
            return str(o)
        return json.JSONEncoder.default(self, o)


app = Flask(__name__)
csrf = CSRFProtect()

app.config["FLASK_ADMIN_FLUID_LAYOUT"] = True
app.config["SECRET_KEY"] = "asdfkljasdfasdnfaslkdjf10293ruaposijdfljkAJADFasdflkjasdflkajsdlfkjasdflkjasdfnasdf"
app.config["MONGODB_HOST"] = instance_config.new_host_
if instance_config.ENVIRONMENT != "production":
    app.config["MONGODB_SETTINGS"] = instance_config.lst_db_

if app.json_encoder is None:
    app.json_encoder = JSONEncoder

try:
    logger.info("Connecting to mongo using MongoEngine")
    mongo_db = MongoEngine()
    mongo_db.init_app(app=app)
except Exception as exception:
    logger.exception(f"Faced exception while connecting to mongo {exception}")
    raise exception

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = "login"
login_manager.refresh_view = "login"
login_manager.needs_refresh_message = "Session timeout, please re-login"
login_manager.needs_refresh_message_category = "info"
csrf.init_app(app)


@login_manager.user_loader
def load_user(user_id):
    try:
        res = main_models.User.objects(id=user_id).first()
        if "tenant" in session:
            session.pop("tenant")
        if res and res.tenant != "" and res.tenant is not None:
            session["tenant"] = res.tenant
            print("session ", session["tenant"])
        return res
    except Exception as e:
        print("exception ", e)
    return False


@app.after_request
def after_request_func(response):
    try:
        if response.status_code == 403:
            flash("You don't have access to this link!", "success")
            return redirect(url_for("admin.login_view", next=request.full_path))
        session.permanent = True
        app.permanent_session_lifetime = timedelta(minutes=20)
        if current_user.is_authenticated:
            user = main_models.User.objects(id=current_user.get_id()).first()
            if not user or user.is_expired:
                return redirect(url_for("admin.logout_view"))
        return response
    except Exception as e:
        print("exception ,", e)
        return response


# @app.route("/error_report")
def error_report():
    compressed_data = request.args.get("compressed_data", None)

    if compressed_data and compressed_data.replace(" ", "") != "":
        # Decode from Base64 to get the compressed bytes
        compressed_data_bytes = base64.b64decode(compressed_data)

        # Decompress the data using zlib
        serialized_data = zlib.decompress(compressed_data_bytes)

        # Deserialize the data using pickle
        error_data = pickle.loads(serialized_data)

        data = error_data.get("data", [])
        settings = error_data.get("settings", {})

        return render_template("admin/error_report.html", data=data, settings=settings)
    return "You should not be here"


@app.route("/render-html/<template_id>/<int:content_index>")
def render_html_content(template_id, content_index):
    # Fetch the template by ID using MongoEngine
    template = UserEmailTemplates.objects.get(id=template_id)

    # Ensure the index is within the bounds of the contents list
    if content_index < len(template.contents):
        tmp_content = template.contents[content_index].content

        base64_bytes = tmp_content.encode("utf-8")

        original_bytes = base64.b64decode(base64_bytes)

        original_text = original_bytes.decode("utf-8")

        content = original_text

    else:
        content = "<p>No content available.</p>"

    # Render the content directly
    return content  # This will render raw HTML


@app.errorhandler(Exception)
def server_error(err):
    return "Error.. Please send this error to Support or DevTeam: " + str(err), 500


admin = flask_admin.Admin(
    app=app, name="B2C Admin Portal", index_view=admin_views.AppAdminView(url="/", name="Dashboard"), template_mode="bootstrap4"
)


manage_app_users_category = "Manage App Users"
subscriber_category = "Subscriber"
reseller_category = "Reseller"
inventory_category = "Inventory"
admin_category = "Admin"
notification_category = "Notifications"
pages_data_category = "Pages Data"
bundles_category = "Bundles"
app_category = "App"
monitoring_system_category = "Monitoring System Management"

admin.add_category(manage_app_users_category)
admin.add_category(subscriber_category)
admin.add_category(reseller_category)
admin.add_category(inventory_category)
admin.add_category(admin_category)
admin.add_category(notification_category)
admin.add_category(pages_data_category)
admin.add_category(bundles_category)
admin.add_category(app_category)
admin.add_category(monitoring_system_category)


admin.add_view(
    main_views.SubscriberOrders(
        reseller_models.Order_history,
        category=subscriber_category,
        name="Subscriber Orders",
        endpoint="subscriber_orders",
    )
)
admin.add_view(view=rate_limit_view.RateLimitLogs(model=mobiles.RateLimitLogs, category=subscriber_category, name="Subscriber Rate Limit"))
admin.add_view(mobiles_views.AppUserDetails(mobiles.AppUserDetails, category=subscriber_category, name="Subscriber User List"))
admin.add_view(mobiles_views.AppUserDetailsBackup(mobiles.AppUserDetailsBackup, category=subscriber_category, name="Subscriber User Backup List"))
admin.add_view(mobiles_views.NotificationHistory(reseller_models.NotificationHistory, category=reseller_category, name="Notification History"))
admin.add_view(mobiles_views.AppRedeemCodeRecord(mobiles.AppRedeemCodeRecord, category=subscriber_category, name="Subscriber Redeem Logs"))
admin.add_view(main_views.TransactionLogs(consumer_models.TransactionLogs, category=subscriber_category))
admin.add_view(main_views.RewardHistoryLogs(consumer_models.RewardHistory, category=subscriber_category))
admin.add_view(main_views.CashbackHistory(consumer_models.CashbackHistory, category=subscriber_category))
admin.add_view(main_views.UserEmailTemplates(user_email_template_models.UserEmailTemplates, category=subscriber_category))
admin.add_view(main_views.FeedbackQuestions(main_models.Question, category=subscriber_category))
admin.add_view(main_views.FeedbackAnswers(main_models.Answer, category=subscriber_category))
admin.add_view(main_views.BlockedEmails(main_models.BlockedEmails, category=subscriber_category))
admin.add_view(main_views.ProfileSharing(reseller_models.SharedProfiles, category=subscriber_category, name="Shared Profiles"))
admin.add_view(main_views.Labels(consumer_models.Labels, category=subscriber_category))
admin.add_view(consumer_views.Languages(consumer_models.Languages, category=subscriber_category, name="All Languages"))
admin.add_view(main_views.Messages(consumer_models.Messages, category=subscriber_category))
admin.add_view(main_views.BundleDetails(consumer_models.BundleDetails, category=subscriber_category))
admin.add_view(main_views.BundleMessageTranslationData(consumer_models.BundleMessageTranslation, category=subscriber_category))

admin.add_view(mobiles_views.AppContactUs(mobiles.AppContactUs, category=app_category, name="Contact Us"))
admin.add_view(mobiles_views.AppList(mobiles.AppList, category=app_category, name="Subscriber App List"))
admin.add_view(mobiles_views.AppVersionList(mobiles.AppVersionList, category=app_category))
admin.add_view(main_views.UserLoginSession(mobiles.UserLoginSession, category=app_category, name="User Session"))

admin.add_view(
    main_views.VendorUnlimitedBundleProfile(
        consumer_models.VendorUnlimitedBundleProfile,
        category=bundles_category,
        name="Unlimited Bundle Metadata",
        endpoint="unlimited-bundle-metadata",
    )
)

admin.add_view(consumer_views.FAQ(consumer_models.FAQ, category=pages_data_category))
admin.add_view(consumer_views.Page(consumer_models.Page, category=pages_data_category))

admin.add_view(consumer_views.MCCMNC(mobiles.MCCMNC, category=admin_category))
admin.add_view(main_views.OperatorList(main_models.OperatorList, category=admin_category))
admin.add_view(main_views.Vendors(consumer_models.Vendors, category=admin_category))
admin.add_view(main_views.RunnableScripts(main_models.RunnableScripts, category=admin_category))
admin.add_view(consumer_views.Countries(consumer_models.Countries, category=admin_category))
admin.add_view(consumer_views.Zones(consumer_models.Zones, category=admin_category))
admin.add_view(consumer_views.Regions(consumer_models.Regions, category=admin_category))
admin.add_view(consumer_views.CoveredAreas(consumer_models.CoveredAreas, category=admin_category))
admin.add_view(main_views.Profiles(consumer_models.Profiles, category=admin_category))
admin.add_view(main_views.User(main_models.User, category=admin_category))
admin.add_view(main_views.Settings(main_models.Settings, category=admin_category))
admin.add_view(main_views.UpdateBundleVersion(main_models.UpdateBundleVersion, category=admin_category))
admin.add_view(main_views.KeycloackSettings(main_models.KeycloackSettings, category=admin_category))
admin.add_view(main_views.EmailSettings(main_models.EmailSettings, category=admin_category))

admin.add_view(main_views.CallbackHistory(consumer_models.CallbackHistory, category=admin_category))

admin.add_view(main_views.AiAssistantAdvancedSearch(ai_assistant_models.AdvancedSearch, category=admin_category))

admin.add_view(
    main_views.PaymentLogsTransactions(
        consumer_models.PaymentLogsTransactions,
        category=admin_category,
        name="Error logs",
        endpoint="error_logs",
    )
)
admin.add_view(main_views.DeviceData(mobiles.DeviceData, category=admin_category))
admin.add_view(main_views.NetworkList(mobiles.NetworkList, category=admin_category, name="Network List"))

admin.add_view(main_views.NotificationLogs(main_models.NotificationLogs, category="Notifications"))


admin.add_view(
    main_views.RegionBundles(
        consumer_models.Bundles,
        category=bundles_category,
        name="Region Bundles",
        endpoint="region-bundle",
    )
)
admin.add_view(
    main_views.GlobalBundles(
        consumer_models.Bundles,
        category=bundles_category,
        name="Global bundles",
        endpoint="global-bundles",
    )
)
admin.add_view(
    main_views.CountryBundles(
        consumer_models.Bundles,
        category=bundles_category,
        name="Country bundles",
        endpoint="country-bundles",
    )
)
admin.add_view(
    main_views.CruiseBundles(
        consumer_models.Bundles,
        category=bundles_category,
        name="Cruise bundles",
        endpoint="cruise-bundles",
    )
)
admin.add_view(main_views.BundleCreation(category=bundles_category, name="Create new bundle", endpoint="create_bundle"))



admin.add_view(
    main_views.ModifyUserBalance(
        mobiles.AppUserDetails,
        category=manage_app_users_category,
        name="Modify Balance",
        endpoint="modify-user-balance",
    )
)

admin.add_view(
    main_views.PurchaseBlockedUsers(
        mobiles.AppUserDetails,
        category=manage_app_users_category,
        name="Purchase Blocked Users",
        endpoint="purchase-blocked-users",
    )
)
admin.add_view(
    main_views.LoginCooldown(
        mobiles.AppUserDetails,
        category=manage_app_users_category,
        name="Login Cooldown",
        endpoint="login-cooldown-users",
    )
)
admin.add_view(
    main_views.VerifyCooldown(
        mobiles.AppUserDetails,
        category=manage_app_users_category,
        name="Verify Cooldown",
        endpoint="verify-cooldown-users",
    )
)
admin.add_view(
    main_views.ApplyPromoCooldown(
        mobiles.AppRedeemCodeRecord,
        category=manage_app_users_category,
        name="Apply Promo Cooldown",
        endpoint="apply-promo-cooldown",
    )
)

admin.add_view(reseller_views.Reseller(reseller_models.Reseller, name="Reseller", category=reseller_category))
admin.add_view(
    reseller_views.ImportVoucherCode(
        mobiles.RedeemCodes,
        category=reseller_category,
        name="Voucher_Code",
        endpoint="voucher_code",
    )
)
admin.add_view(
    reseller_views.ImportPromoCode(
        mobiles.RedeemCodes,
        category=reseller_category,
        name="Promo_Code",
        endpoint="promo_code",
    )
)
admin.add_view(
    reseller_views.VoucherRedeemLogs(
        mobiles.VoucherRedeemLogs,
        category=reseller_category,
        name="Voucher Code Logs",
        endpoint="voucher_code_logs",
    )
)
admin.add_view(
    reseller_views.PromoRedeemLogs(
        mobiles.PromoRedeemLogs,
        category=reseller_category,
        name="Promo Code Logs",
        endpoint="promo_code_logs",
    )
)


admin.add_view(
    main_views.ResellerOrders(
        reseller_models.Order_history,
        category=reseller_category,
        name="Reseller Orders",
        endpoint="reseller_orders",
    )
)
admin.add_view(main_views.Agent(reseller_models.Agent, category=reseller_category))

admin.add_view(main_views.InventoryModelView(AuxClass, name="Inventory Details", endpoint="inventory", category=inventory_category))


admin.add_view(main_views.Allocateprofiles(category=inventory_category, name="Allocate profiles", endpoint="allocate_profiles"))
admin.add_view(
    main_views.BundlesSupplyAndDemand(
        consumer_models.BundlesSupplyAndDemand,
        category=inventory_category,
        name="Bundles Supply And Demand",
    )
)


@app.route("/countries-by-zone")
def update_countries():
    zone = request.args.get("zone", "01", type=str)  # Default or error handling can be adjusted
    return jsonify(get_countries_by_zone(zone))


@app.route("/get_zones_by_vendor")
def get_zones_by_vendor():
    vendor = request.args.get("vendor", "01", type=str)  # Default or error handling can be adjusted
    return jsonify(get_vendor_zones(vendor))



from app_models.consumer_models import Vendors
from flask_wtf import FlaskForm
from wtforms import TextAreaField, IntegerField, FloatField, SelectField
from wtforms.validators import DataRequired


class BundleCreationForm(FlaskForm):
    def __init__(self, *args, **kwargs):
        super(BundleCreationForm, self).__init__(*args, **kwargs)
        self.vendor_name.choices.extend([(vendor.vendor_name, vendor.vendor_name) for vendor in Vendors.objects()])
    vendor_name = SelectField(u'Vendor Name', choices=[('', 'Select a Vendor')], validators=[DataRequired()])
    bundle_prefix = TextAreaField('Bundle code prefix', validators=[DataRequired()], render_kw={"placeholder": "Bundle Prefix"})
    bundle_name = TextAreaField('Bundle Name', validators=[DataRequired()], render_kw={"placeholder": "Bundle Name"})
    bundle_marketing_name = TextAreaField('Bundle Marketing Name', validators=[DataRequired()], render_kw={"placeholder": "Bundle Prefix"})
    data_amount = IntegerField(label='Bundle data amount', validators=[DataRequired()], render_kw={"placeholder": "Data amount"})
    data_unit = SelectField(label='Select Data Unit', validators=[DataRequired()], render_kw={"placeholder": "Data Unit"}, choices=["GB", "MB"])
    validity = IntegerField(label='Validity', validators=[DataRequired()], render_kw={"placeholder": "Validity in days"})
    zone_name = SelectField(label='Select zone of countries', render_kw={"placeholder": "Supported Countries/Zone"}, choices=[], validate_choice=False)
    retail_price = FloatField(label='Retail price', validators=[DataRequired()])
    unit_price = FloatField(label='Unit Price', validators=[DataRequired()])

import app_main
import datetime
import time
import os
import types
import urllib

import requests
import base64
import re
import uuid
import xml.etree.ElementTree as ET
import logging
import json

from enum import Enum

from b2c_helpers import exception_handler
from urllib3.exceptions import NewConnectionError
from requests.exceptions import Timeout, ConnectionError
from typing import TypedDict, Optional, List
from decimal import Decimal
from app_models.consumer_models import Vendors
from app_models.consumer_models import Countries

from b2c_helpers.constaints import (
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR,
    CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE,
    CUSTOMER_SUPPORT_MESSAGE_FOR_TOKEN_VENDOR,
    MONTYMOBILE_VENDOR,
)
from b2c_helpers.encrypt_helper import Crypt
from b2c_helpers.support_helper import (
    deactivate_vendor,
    deactivate_bundle,
    get_vendor_info,
    send_custom_monitor_email,
)

from app_models.reseller_models import Order_history


from app_helpers.encrypt_helper import Crypt
from instance import consumer_config as instance_config

logger = logging.getLogger(__name__)

def get_bundle_name(list_countries, list_countries_code, bundle):
    amount = f"{float(bundle['data_amount']):.2f}".rstrip("0").rstrip(".")

    if len(list_countries) > 1:
        if bundle["bundle_category"] == "global":
            bundle_name = f"Global_bundle {amount}{bundle['data_unit']}"
            bundle_code = f"Global_bundle_{uuid.uuid4()}"
        else:
            title = bundle["bundle_name"]
            title = re.sub(r"1Month|MB|GX|JX|B - |I - ", "", title).strip()
            bundle_name = f"{title} {amount}{bundle['data_unit']}"
            bundle_code = f"{title.replace(' ', '').lower()}-{uuid.uuid4()}"
    else:
        bundle_name = f"{list_countries[0]} {amount}{bundle['data_unit']}"
        bundle_code = f"{list_countries_code[0]}_{uuid.uuid4()}"

    # Replace any special characters in bundle_name and bundle_code with underscores
    bundle_name = re.sub(r"[@#%+__&$\"']", "-", bundle_name)
    bundle_code = re.sub(r"[@#%+__&$\"']", "-", bundle_code)

    return bundle_name, bundle_code


class UnauthorizedError(Exception):
    pass


def raise_if_401_403(response: requests.Response):
    if response.status_code in [401, 403]:
        raise UnauthorizedError


def raise_if_not_200(response: requests.Response):
    if response.status_code != 200:
        raise UnauthorizedError


class VendorMetaClass(type):
    def __new__(cls, name, bases, dct):
        # Configure http timeout parameter
        dct["timeout"] = int(getattr(instance_config, "http_connection_timeout", 10))

        # Modify methods before class creation
        for attr, value in dct.items():
            if (
                callable(value)
                and not attr.startswith("_login")
                and not isinstance(value, type)
            ):

                def wrapper(func):
                    def inner(self, *args, **kwargs):
                        try:
                            return func(self, *args, **kwargs)
                        except UnauthorizedError:
                            if self.retry:
                                print(
                                    "Handling UnauthorizedError. Trying to login again."
                                )
                                self._login_api_request()
                                self.retry = False
                                return func(self, *args, **kwargs)
                            raise
                        except (ConnectionError, Timeout, NewConnectionError):
                            if self.retry:
                                print(
                                    "Handling Connection Error. Trying again after 2 seconds."
                                )
                                time.sleep(2)
                                self.retry = False
                                return func(self, *args, **kwargs)

                    return inner
                dct[attr] = wrapper(value)
        return super().__new__(cls, name, bases, dct)


class MontyMobile(metaclass=VendorMetaClass):
    """
    class for API calls to montymobile(gambia) services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Monty Mobile"
    headers = {"Authorization": ""}
    montymobile_url = "https://mobileapp.localrsp.com/mock"

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)

        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "montymobile_username")
            and getattr(instance_config, "montymobile_password")
            and getattr(instance_config, "montymobile_url")
            and vendor
            and getattr(vendor, "temp_token")
        ):
            raise ValueError(
                "required module instance_config: module containing montymobile_username,"
                " montymobile_password and montymobile_url; "
                "required object vendor: object(temp_token='access_token')"
            )

        self.vendor = vendor
        self.instance_config = instance_config
        self.user_name = instance_config.montymobile_username.replace(' ', '')
        self.password = instance_config.montymobile_password.replace(' ', '')
        self.montymobile_url = self.instance_config.montymobile_url.replace(' ', '')
        self.montymobile_login_url = self.montymobile_url + '/user/login/v1'
        self.montymobile_load_url = self.montymobile_url + '/plan/load/v1'
        self.montymobile_unload_url = self.montymobile_url + '/plan/unload/v1'
        self.montymobile_consumption_url = self.montymobile_url + '/v1/plan/subscriber-plans'
        self.montymobile_get_bundles_url = self.montymobile_url + '/plan/inventory/view/v1'
        self.montymobile_get_profiles_url = self.montymobile_url + '/product/inventory/view/v1'
        self.montymobile_get_profile_status_url = self.montymobile_url + '/v1/product/installation-status'

    def _login_api_request(self):
        try:
            json = {"email": self.user_name, "password": self.password}
            response = requests.request("POST", self.montymobile_login_url, json=json, timeout=self.timeout)
            if response.status_code != 200:
                raise ValueError(response.text)
            access_token = response.json()["accessToken"]
            self.headers["Authorization"] = access_token
            encrypted_access_token = Crypt().encrypt(
                access_token, self.instance_config.token_key
            )
            self.vendor.update(set__temp_token=encrypted_access_token)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile._login_api_request> Exception {exception}"
            )
            raise exception

    def _login(self):
        try:
            try:
                if access_token := decrypt_vendor_token(self.vendor_name):
                    self.headers["Authorization"] = access_token
                    return
            except:
                pass
            self._login_api_request()

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile._login> Exception: {exception}"
            )

    def load_bundle_to_profile(self, sku: str, iccid: str, bundle_vendor_code: str):
        try:
            self._login()
            json = {
                "sku": sku,
                "iccid": iccid,
                "plan_code": bundle_vendor_code,
                "plan_start_type_id": 1,
                "quantity": 1,
            }
            response = requests.request(
                "POST", self.montymobile_load_url, headers=self.headers, json=json, timeout=self.timeout
            )
            raise_if_401_403(response)
            if response.status_code != 200 or not response.json()["success"]:
                raise ValueError(response.text)
            return response.json(), "Success"

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<MontyMobile.load_bundle_to_iccid> remote error happened: {error} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}, str(error)

        except ValueError as error:
            print(
                f"<MontyMobile.load_bundle_to_iccid> remote error happened: {error} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
            )
            deactivate_bundle(bundle_vendor_code, vendor_name=self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Bundle: {bundle_vendor_code} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(
                    datetime.datetime.utcnow(),
                    bundle_vendor_code,
                    self.vendor_name,
                    error,
                ),
            )
        except Exception as exception:
            print(
                f"<MontyMobile.load_bundle_to_iccid> error happened: {exception} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
                # handled this well for this vendor,
                # but if we get two unauthorized errors for other vendors, this will cause the fallback to fail
                # reference ticket https://montyholding.atlassian.net/browse/ESB2C-8607
            return {}, str(exception)

        return {}, ""

    def get_profile_consumption(self, sku):
        try:
            self._login()
            json = {"sku": sku}
            url = self.montymobile_consumption_url
            response = requests.request(
                "POST", url=url, headers=self.headers, json=json, timeout=self.timeout
            )
            raise_if_401_403(response)
            if response.status_code != 200:
                raise ValueError(response.text)
            return response.json()
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile.get_profile_consumption> "
                f"error happened: {exception} while getting consumption for sku: {sku}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def unload_plan(self, plan_uid):
        try:
            self._login()
            json = {"plan_uid": plan_uid}
            response = requests.request(
                "POST", self.montymobile_unload_url, headers=self.headers, json=json, timeout=self.timeout
            )
            raise_if_401_403(response)
            if response.status_code != 200:
                raise ValueError(response.text)
            return response.json()
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile.unload_plan> "
                f"error happened: {exception} while unloading plan with plan_uid: {plan_uid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def get_bundles(self, page=1):
        try:
            payload = {"group_by_offering": "yes", "limit": 100, "page": page}

            response = requests.post(
                self.montymobile_get_bundles_url, headers=self.headers, json=payload, timeout=self.timeout
            )
            raise_if_401_403(response)

            bundles = response.json()
            return bundles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile.get_bundles> "
                f"error happened: {exception} while getting bundles for page: {page}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_profiles(self, page=1):
        try:

            payload = {
                "availability": 0,
                "sim_type": "eSIM",
                "limit": 10000,
                "page": page,
            }
            # do you need application/json header?
            response = requests.post(
                self.montymobile_get_profiles_url, headers=self.headers, json=payload, timeout=self.timeout
            )

            raise_if_401_403(response)

            profiles = response.json()
            return profiles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile.get_profiles> "
                f"error happened: {exception} while getting profiles for page: {page}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_profile_status(self, iccid):
        try:
            payload = {"iccid": iccid}
            response = requests.post(
                self.montymobile_get_profile_status_url,
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )

            raise_if_401_403(response)

            profiles = response.json()
            return profiles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <MontyMobile.get_profile_statuss> "
                f"error happened: {exception} while getting profile_status for iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []


class Flexiroam(metaclass=VendorMetaClass):
    """
    class for API calls to Flexiroam services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Flexiroam"
    headers = {"Authorization": ""}

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)

        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "flexiroam_username")
            and getattr(instance_config, "flexiroam_password")
            and getattr(instance_config, "flexiroam_url")
            and vendor
            and getattr(vendor, "temp_token")
        ):
            raise ValueError(
                "required module instance_config: module containing flexiroam_username,"
                " flexiroam_password and flexiroam_url; "
                "required object vendor: object(temp_token='access_token')"
            )

        self.vendor = vendor
        self.instance_config = instance_config
        self.user_name = instance_config.flexiroam_username.replace(" ", "")
        self.password = instance_config.flexiroam_password.replace(" ", "")
        self.flexiroam_url = self.instance_config.flexiroam_url.replace(" ", "")
        self.flexiroam_login_url = self.flexiroam_url + "/user/login/v1"
        self.flexiroam_load_url = self.flexiroam_url + "/plan/load/v1"
        self.flexiroam_unload_url = self.flexiroam_url + "/plan/unload/v1"
        self.flexiroam_consumption_url = self.flexiroam_url + "/plan/simplan/v1"
        self.flexiroam_get_bundles_url = self.flexiroam_url + "/plan/inventory/view/v1"
        self.flexiroam_get_profiles_url = (
            self.flexiroam_url + "/product/inventory/view/v1"
        )
        self.flexiroam_get_available_profile_url = (
            self.flexiroam_url + "/product/inventory/available/v1"
        )

    def _login_api_request(self):
        try:
            headers = {}
            payload = {"email": self.user_name, "password": self.password}
            response = requests.request(
                "POST", self.flexiroam_login_url, headers=headers, data=payload, timeout=self.timeout
            )
            if response.status_code != 200:
                raise ValueError(response.text)
            access_token = response.json()["data"]["token"]
            self.headers["token"] = access_token
            encrypted_access_token = Crypt().encrypt(
                access_token, self.instance_config.token_key
            )
            self.vendor.update(set__temp_token=encrypted_access_token)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam._login_api_request> Exception {exception}"
            )
            raise exception

    def _login(self):
        try:
            try:
                if access_token := decrypt_vendor_token(self.vendor_name):
                    self.headers["token"] = access_token
                    return
            except:
                pass
            self._login_api_request()

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam._login> Exception: {exception}"
            )

    def load_bundle_to_profile(self, sku: str, iccid: str, bundle_vendor_code: str):
        MAX_RETRIES = 3
        SLEEP_TIME = 2  # Time to wait before retrying

        for attempt in range(MAX_RETRIES):
            try:
                self._login()
                lst_sku = "[" + sku + "]"
                json = {
                    "sku": lst_sku,
                    "plan_code": bundle_vendor_code,
                    "plan_start_type_id": 1,
                    "discount": "",
                }
                response = requests.request(
                    "POST",
                    self.flexiroam_load_url,
                    headers=self.headers,
                    json=json,
                    timeout=125.5,
                )
                raise_if_401_403(response)
                if response.status_code != 200 or not response.json()["success"]:
                    raise ValueError(response.text)
                return response.json(), "Success"
            except (
                requests.exceptions.ConnectionError,
                requests.exceptions.Timeout,
                NewConnectionError,
            ) as error:
                print(
                    f"<Flexiroam.load_bundle_to_iccid> remote error happened: {error} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
                )
                # vendor is not available, so we should deactivate all bundles related to this vendor
                deactivate_vendor(self.vendor_name)
                send_custom_monitor_email(
                    subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                    body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                        datetime.datetime.utcnow(), self.vendor_name, error
                    ),
                )
                return {}, str(error)
            except ValueError as error:
                print(
                    f"<Flexiroam.load_bundle_to_iccid> remote error happened: {error} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
                )
                deactivate_bundle(bundle_vendor_code)
                send_custom_monitor_email(
                    subject=f"------URGENT------ Bundle: {bundle_vendor_code} deactivated",
                    body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(
                        datetime.datetime.utcnow(),
                        bundle_vendor_code,
                        self.vendor_name,
                        error,
                    ),
                )
            except Exception as exception:
                print(
                    f"<Flexiroam.load_bundle_to_iccid> error happened: {exception} while buying bundle: {bundle_vendor_code} iccid: {iccid}"
                )
                if isinstance(exception, UnauthorizedError):
                    raise exception
                return {}, str(exception)

            # If reached here, it means an exception was thrown. Wait and retry
            print(f"Attempt {attempt + 1} failed, retrying in {SLEEP_TIME} seconds...")
            time.sleep(SLEEP_TIME)

        # If reached here, all attempts failed. Return an error
        return {}, "All attempts failed"

    def get_profile_consumption(self, sku):
        try:
            self._login()
            payload = {
                "sku": sku,
            }
            url = self.flexiroam_consumption_url
            response = requests.request(
                "POST", url=url, headers=self.headers, data=payload, timeout=self.timeout
            )
            raise_if_401_403(response)
            if response.status_code != 200:
                raise ValueError(response.text)
            return response.json()
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam.get_profile_consumption> "
                f"error happened: {exception} while getting consumption for sku: {sku}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def unload_plan(self, plan_uid):
        try:
            self._login()
            payload = {"plan_uid": plan_uid}
            response = requests.request(
                "POST", self.flexiroam_unload_url, headers=self.headers, data=payload, timeout=self.timeout
            )
            raise_if_401_403(response)
            if response.status_code != 200:
                raise ValueError(response.text)
            return response.json()
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam.unload_plan> "
                f"error happened: {exception} while unloading plan with plan_uid: {plan_uid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def get_bundles(self, page=1):
        try:
            self._login()

            payload = {"group_by_offering": "yes", "limit": 100, "page": page}

            response = requests.post(
                self.flexiroam_get_bundles_url, headers=self.headers, data=payload, timeout=self.timeout
            )
            raise_if_401_403(response)

            bundles = response.json()
            return bundles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam.get_bundles> "
                f"error happened: {exception} while getting bundles for page: {page}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_profiles(self, page=1, sku=""):
        try:
            self._login()

            payload = {
                "availability": 0,
                "sim_type": "eSIM",
                "limit": 10000,
                "page": page,
            }
            if sku != "":
                payload = {"sku": sku}
            response = requests.post(
                self.flexiroam_get_profiles_url, headers=self.headers, data=payload, timeout=self.timeout
            )

            raise_if_401_403(response)

            profiles = response.json()
            return profiles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Flexiroam.get_profiles> "
                f"error happened: {exception} while getting profiles for page: {page}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_next_available_profiles(self, plan_code, number_of_profiles=1000, sku=None):
        try:
            self._login()

            payload = {
                "availability": 0,
                "sim_type": "eSIM",
                "limit": number_of_profiles,
                "plan_code": plan_code,
            }
            if sku:
                payload["sku"] = sku

            response = requests.post(
                self.flexiroam_get_available_profile_url,
                headers=self.headers,
                data=payload,
                timeout=25,
            )
            raise_if_401_403(response)
            profiles = response.json()
            return profiles

        except Exception as exception:
            logging.info(
                "<Flexiroam.get_profiles> error happened: %s while getting pre_allocate_profiles for plan_code: %s",
                exception,
                plan_code,
            )
            if isinstance(
                exception,
                (
                    requests.exceptions.ConnectionError,
                    requests.exceptions.Timeout,
                    NewConnectionError,
                    UnauthorizedError,
                ),
            ):
                raise exception
            return []


class ESIMGo(metaclass=VendorMetaClass):
    """
    class for API calls to eSIMGo services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "eSIMGo"
    headers = {"X-API-Key": ""}

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)
        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "esimgo_url")
            and getattr(instance_config, "esim_go_token")
            and vendor
        ):
            raise ValueError(
                "required module instance_config: module containing esimgo_url,"
                " esim_go_token; "
                "required object vendor:"
            )

        self.vendor = vendor
        self.instance_config = instance_config
        self.esimgo_url = self.instance_config.esimgo_url.replace(" ", "")
        self.esimgo_load_url = self.esimgo_url + "/v2.3/orders"
        self.esimgo_consumption_url = self.esimgo_url + "/v2.3/esims/"
        self.esimgo_get_bundles_url = self.esimgo_url + "/v2.3/catalogue"
        self.esimgo_assign_profiles_url = self.esimgo_url + "/v2.3/orders"
        self.esimgo_get_order_url = self.esimgo_url + "/v2.3/esims/assignments/"
        self.esimgo_apply_topup = self.esimgo_url + "/v2.3/esims/apply"
        self.esimgo_get_country_networks_url = (
            self.esimgo_url + "/v2.3/networks?returnAll=true"
        )
        self.esimgo_get_profile_history_url = (
            self.esimgo_url + "/v2.3/esims/{iccid}/history"
        )
        self.esimgo_get_esim_details_and_status_url = (
            self.esimgo_url + "/v2.3/esims/{iccid}"
        )
        self.esimgo_refund_to_balance = (
            self.esimgo_url
            + "/v2.3/esims/{iccid}/bundles/{bundle_vendor_code}?refundToBalance=true&offerId={offerId}"
        )
        self.esimgo_get_the_status_of_a_bundle_applied_to_an_esim: str = (
            self.esimgo_url + "/v2.3/esims/{iccid}/bundles/{name}"
        )

        self.headers = {"X-API-Key": self.instance_config.esim_go_token}

    def _login_api_request(self):
        # define this function as other vendors and retur true for now.
        return True

    def get_profile_consumption(self, iccid):
        try:

            url = self.esimgo_consumption_url + iccid + "/bundles?includeUsed=true"
            response = requests.request("GET", url, headers=self.headers, timeout=self.timeout)

            if response.status_code == 200:
                return response.json()
            else:
                raise ValueError(response.text)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_profile_consumption> "
                f"error happened: {exception} while getting consumption for sku: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def get_bundles(self, page=1):
        try:
            payload = {}
            url = "{}?page={}&perPage=100".format(self.esimgo_get_bundles_url, page)
            response = requests.get(url, headers=self.headers, data=payload, timeout=self.timeout)
            raise_if_401_403(response)
            bundles = response.json()
            return bundles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_bundles> "
                f"error happened: {exception} while getting bundles for page: {page}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def assign_profiles(self, bundle_vendor_code, buy_bundle=True):
        # buy bundle will true in case of buying a bundle and false in case of paying a topup
        try:
            RETRY_COUNT = 3

            payload = {
                "assign": buy_bundle,
                "order": [
                    {"item": bundle_vendor_code, "quantity": 1, "type": "bundle"}
                ],
                "type": "transaction",
            }
            response = requests.post(
                self.esimgo_assign_profiles_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=self.timeout
            )

            raise_if_401_403(response)

            profiles = response.json()
            return profiles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.assign_profiles> "
                f"error happened: {exception} while assigning profiles for bundle_vendor_code: {bundle_vendor_code}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_order(self, order_reference):
        try:
            get_order_url = f"{self.esimgo_get_order_url}{order_reference}"
            response = requests.request("GET", get_order_url, headers=self.headers, timeout=self.timeout)
            raise_if_401_403(response)
            if response.status_code != 200:
                raise Exception(response.text)
            response.raise_for_status()
            order = response.json()
            return order

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_order> "
                f"error happened: {exception} while getting order for order_reference: {order_reference}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def apply_topup(self, bundle_vendor_code, iccid):
        try:

            payload = {"iccid": iccid, "name": bundle_vendor_code, "repeat": 1}
            response = requests.post(
                self.esimgo_apply_topup, headers=self.headers, data=json.dumps(payload), timeout=self.timeout
            )
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.apply_topup> "
                f"response status code {response.status_code}  "
            )

            raise_if_401_403(response)
            # in case of incompatible topup from esimgo side
            if response.status_code == 400:
                return False
            if response.status_code != 200:
                raise Exception(response.text)
            response.raise_for_status()
            order = response.json()["applyReference"]
            return order

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.apply_topup> "
                f"error happened: {exception} while getting apply topup bundle {bundle_vendor_code}  for iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_balance(self):
        try:
            url = f"{instance_config.esimgo_url}/v2.3/organisation"
            response = requests.request("GET", url, headers=self.headers, timeout=self.timeout)
            balance = response.json()["organisations"][0]["balance"]
            return balance
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_balance> "
                f"error happened: {exception} while getting balance for esimgo"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return 0

    def get_country_networks(self):
        try:
            payload = {}
            url = self.esimgo_get_country_networks_url
            response = requests.get(url, headers=self.headers, data=payload, timeout=self.timeout)
            raise_if_401_403(response)
            networks = response.json()
            return networks

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_country_networks> "
                f"error happened: {exception} while getting country networks"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_profile_history(self, iccid):
        try:
            payload = {}
            url = self.esimgo_get_profile_history_url.format(iccid=iccid)
            response = requests.get(url, headers=self.headers, data=payload, timeout=self.timeout)
            raise_if_401_403(response)
            return response.json()

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_profile_history> "
                f"error happened: {exception} while get_profile_history"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def refund_profile(self, iccid, bundle_vendor_code):
        try:
            payload = {}

            # ----------------------------------------------------
            # GET information from external eSIMGo GET API
            # ----------------------------------------------------
            get_bundle_status_applied_to_an_esim_url = (
                self.esimgo_get_the_status_of_a_bundle_applied_to_an_esim.format(
                    iccid=iccid, name=bundle_vendor_code
                )
            )
            with requests.Session() as session:
                bundle_status_request_response = session.get(
                    get_bundle_status_applied_to_an_esim_url,
                    headers=self.headers,
                    data=payload,
                    timeout=self.timeout
                )
                if bundle_status_request_response.status_code != 200:
                    raise_if_401_403(bundle_status_request_response)
                    logging.info(
                        f"[{datetime.datetime.utcnow()}] <response.json()> {bundle_status_request_response.json()}"
                    )
                    return False

            # Extract data from bundle_status_request_response
            bundle_status_data: list = bundle_status_request_response.json().get(
                "assignments", []
            )
            if not bundle_status_data:
                return False

            first_bundle_status: dict = bundle_status_data[0]

            # ------------------------------------------------------
            # Revokes profile bundle via external eSIMGo DELETE API
            # ------------------------------------------------------
            bundle_state: str = first_bundle_status.get("bundleState", "")
            offer_id: str = first_bundle_status.get("id", "")
            if bundle_state != "queued" or not offer_id:
                return False

            url = self.esimgo_refund_to_balance.format(
                iccid=iccid, bundle_vendor_code=bundle_vendor_code, offerId=offer_id
            )
            with requests.Session() as session:
                response = session.delete(url, headers=self.headers, data=payload, timeout=self.timeout)
                if response.status_code != 200:
                    raise_if_401_403(response)
                    logging.info(
                        f"[{datetime.datetime.utcnow()}] <response.json()> {response.json()}"
                    )
                    return False

            return True
        except Exception as exception:
            logging.error(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.refund_profile> "
                f"error happened: {exception} while refund_profile"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return False

    def get_esim_details_and_status(self, iccid):
        try:
            payload = {}
            url = self.esimgo_get_esim_details_and_status_url.format(iccid=iccid)
            response = requests.get(url, headers=self.headers, data=payload, timeout=self.timeout)
            raise_if_401_403(response)
            return response.json()

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <ESIMGo.get_esim_details_and_status> "
                f"error happened: {exception} while get_esim_details_and_status"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []


class Vodafone(metaclass=VendorMetaClass):
    """
    class for API calls to Vodafone services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Vodafone"
    headers = {"Authorization": "Bearer "}

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)
        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "vodafone_url")
            and getattr(instance_config, "vodafone_token")
            and vendor
        ):
            raise ValueError(
                "required module instance_config: module containing vodafone_url,"
                " vodafone_token; "
                "required object vendor:"
            )

        self.vendor = vendor
        self.instance_config = instance_config
        self.vodafone_url = self.instance_config.vodafone_url.replace(" ", "")

        self.vodafone_consumption_url = self.vodafone_url + "/network/things/iccids/"
        self.vodafone_profile_with_topups_balance_url = (
            self.vodafone_url + "/network/top-up/history/iccids/"
        )
        self.vodafone_get_bundles_url = self.vodafone_url + "/network/account/products"
        self.vodafone_assign_profiles_url = (
            self.vodafone_url + "/network/things/consumer-profile/"
        )
        self.vodafone_assign_topup_url = (
            self.vodafone_url + "/network/top-up/iccids/{iccid}"
        )
        self.vodafone_country_networks_url = (
            self.vodafone_url + "/network/account/countryAndNetwork"
        )
        self.vodafone_get_order_url = self.vodafone_url + "/v2.3/esims/assignments/"

        self.callback_url = self.instance_config.callback_get_iccid
        self.suspend_url = self.vodafone_url + "/network/things/iccids/{iccid}/suspends"
        self.profile_status_url = (
            self.vodafone_url + "/network/things/consumer-profile-codes/iccids/{iccid}"
        )

        self.headers = {
            "Authorization": f"Bearer {self.instance_config.vodafone_token}"
        }

    def get_profile_consumption(self, iccid):
        try:

            url = self.vodafone_consumption_url + iccid + "/bundle"
            response = requests.request("GET", url, headers=self.headers, timeout=self.timeout)

            if response.status_code == 200:
                return response.json()
            else:
                raise ValueError(response.text)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.get_profile_consumption> "
                f"error happened: {exception} while getting consumption for iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def get_profile_with_topups_balance(self, iccid):
        try:

            url = self.vodafone_profile_with_topups_balance_url + iccid + "/90"
            response = requests.request("GET", url, headers=self.headers, timeout=self.timeout)

            if response.status_code == 200:
                return response.json()
            else:
                raise ValueError(response.text)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.get_profile_with_topups_balance> "
                f"error happened: {exception} while getting balance for iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def get_bundles(self):
        try:

            response = requests.get(
                url=self.vodafone_get_bundles_url, headers=self.headers, timeout=self.timeout
            )
            raise_if_401_403(response)
            bundles = response.json()
            return bundles

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.get_bundles> "
                f"error happened: {exception} while getting bundles: "
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def assign_profiles(self, bundle_vendor_code, order_number):
        try:

            payload = {"customReference": order_number}
            url = self.vodafone_assign_profiles_url + bundle_vendor_code
            headers = self.headers.copy()
            headers["ResponseURLs"] = instance_config.callback_get_iccid
            response = requests.request("POST", url, headers=headers, json=payload, timeout=self.timeout)

            raise_if_401_403(response)
            response_json = response.json()
            if response.status_code == 200:
                return response_json
            else:
                exception = response_json["error"]["description"]
                raise ValueError(exception)

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.assign_profiles> "
                f"error happened: {exception} while assigning profiles for bundle_vendor_code: {bundle_vendor_code} and order_number: {order_number}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def _add_callback_url(self):
        self.headers["ResponseURLs"] = self.callback_url

    def suspend_profile(self, iccid, custom_reference=None):
        payload = {}
        if custom_reference:
            payload["customReference"] = custom_reference
        self._add_callback_url()
        response = requests.patch(
            self.suspend_url.format(iccid=iccid),
            headers=self.headers,
            data=json.dumps(payload),
            timeout=self.timeout
        )
        print(response.text)
        if response.status_code != 200:
            return (
                False,
                response.json().get("error", {}).get("description", response.text),
                None,
            )
        return True, "Success", response.json().get("acknowledgement", {}).get("id", "")

    def assign_topup(self, bundle_vendor_code, order_number, iccid):
        try:

            payload = {"customReference": order_number, "bundleID": bundle_vendor_code}
            url = self.vodafone_assign_topup_url.format(iccid=iccid)
            headers = self.headers.copy()
            headers["ResponseURLs"] = instance_config.callback_get_iccid

            response = requests.request("PUT", url, headers=headers, json=payload, timeout=self.timeout)

            raise_if_401_403(response)
            response_json = response.json()
            if response.status_code == 202 or response.status_code == 200:
                return response_json
            else:
                exception = response_json["error"]["description"]
                raise ValueError(exception)

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.assign_profiles> "
                f"error happened: {exception} while assigning profiles for bundle_vendor_code: {bundle_vendor_code} and order_number: {order_number}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_country_network(self):
        try:

            response = requests.get(
                url=self.vodafone_country_networks_url, headers=self.headers, timeout=self.timeout
            )
            raise_if_401_403(response)
            networks = response.json()
            return networks

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.get_country_network> "
                f"error happened: {exception} while getting country networks: "
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []

    def get_profile_status(self, iccid):
        try:

            response = requests.get(
                url=self.profile_status_url.format(iccid=iccid), headers=self.headers, timeout=self.timeout
            )
            raise_if_401_403(response)
            networks = response.json()
            return networks

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Vodafone.get_profile_status> "
                f"error happened: {exception} while get_profile_status: "
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return []


class Indosat(metaclass=VendorMetaClass):
    """
    class for API calls to Indosat services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Indosat"
    headers = {"Authorization": "Basic "}

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)

        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "indosat_username")
            and getattr(instance_config, "indosat_password")
            and getattr(instance_config, "indosat_url")
            and vendor
        ):
            raise ValueError(
                "required module instance_config: module containing indosat_username,"
                " indosat_password and indosat_url; "
            )

        self.vendor = vendor
        self.instance_config = instance_config
        self.user_name = instance_config.indosat_username.replace(" ", "")
        self.password = instance_config.indosat_password.replace(" ", "")
        self.liscense_key = instance_config.indosat_liscense_key.replace(" ", "")

        self.indosat_url = self.instance_config.indosat_url.replace(" ", "")
        self.credentials = f"{self.user_name}:{self.password}"
        self.encoded_credentials = base64.b64encode(self.credentials.encode()).decode()
        self.headers = {"Authorization": f"Basic {self.encoded_credentials}"}
        self.indosat_get_profiles_url = self.indosat_url + "/devices?"
        self.indosat_get_bundles_url = self.indosat_url + "/rateplans"
        self.indosat_get_profile_detailed_url = self.indosat_url + "/devices/"
        self.indosat_get_iccid_consumption_url = self.indosat_url + "/devices/"
        self.indosat_edit_profile_details_url = self.indosat_url + "/devices/"
        self.rate_revenue = self.vendor.rate_revenue
        self.currency_exchange_rate = self.vendor.currency_exchange_rate
        self.indosat_get_terminal_details_url = (
            "https://indosatooredoo.jasper.com/ws/service/terminal"
        )
        self.indosat_topup_iccid_url = (
            "https://indosatooredoo.jasper.com/ws/service/terminal"
        )

    def generate_modified_since(self):
        """
        Generate a modifiedSince parameter
        Returns:
            str: The modifiedSince parameter in the format 'modifiedSince=YYYY-MM-DDTHH:MM:SSZ'.
        """
        # Get the current date and time
        return (datetime.datetime.utcnow()-datetime.timedelta(days=180)).strftime("%Y-%m-%dT%H:%M:%SZ")

    def get_profiles(
        self,
        status=None,
        accountId: str = None,
        pageSize=None,
        pageNumber=None,
        modifiedSince: str = None,
    ):
        if modifiedSince is None:
            modifiedSince = self.generate_modified_since()
        endquery = f"modifiedSince={modifiedSince}"
        try:

            if status:
                endquery = endquery + f"&status={status}"
            if accountId:
                endquery = endquery + f"&accountId={accountId}"
            if pageSize:
                endquery = endquery + f"&pageSize={pageSize}"
            if pageNumber:
                endquery = endquery + f"&pageNumber={pageNumber}"
            url = self.indosat_get_profiles_url + endquery
            payload = {}

            response = requests.request("GET", url, headers=self.headers, data=payload, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_profiles> remote error happened: {error} while getting profiles query: {endquery}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_profiles> remote error happened: {error} while getting profiles query: {endquery}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_profiles> remote error happened: {exception} while getting profiles query: {endquery}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_bundles(
        self, shared=None, accountId: str = None, pageSize=None, pageNumber=None
    ):

        try:
            url = self.indosat_get_bundles_url
            query_params = []

            if shared:
                query_params.append(f"shared={shared}")
            if accountId:
                query_params.append(f"accountId={accountId}")
            if pageSize:
                query_params.append(f"pageSize={pageSize}")
            if pageNumber:
                query_params.append(f"pageNumber={pageNumber}")

            if query_params:
                url += "?" + "&".join(query_params)

            payload = {}

            response = requests.request("GET", url, headers=self.headers, data=payload, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_bundles> remote error happened: {error} while getting bundles query: {url}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )
            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_bundles> remote error happened: {error} while getting bundles query: {url}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_bundles> remote error happened: {exception} while getting bundles query: {url}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_profile_detailed(self, iccid: str):

        try:

            url = self.indosat_get_profile_detailed_url + iccid
            payload = {}

            response = requests.request("GET", url, headers=self.headers, data=payload, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )
            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {exception} while getting profiles query: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_device_consumption_in_zone(
        self,
        iccid: str,
        cycleStartDate: str = None,
        ratePlan: str = None,
        zone: str = None,
    ):
        # Warning, data is in b, divide by 1024 twice (1,048,576)

        try:

            url = self.indosat_get_iccid_consumption_url + iccid + "/usageInZone"

            # List to hold query parameters
            query_params = []

            # Add each parameter if it's not None
            if cycleStartDate:
                query_params.append(f"cycleStartDate={cycleStartDate}")
            if zone:
                query_params.append(f"zone={zone}")
            if ratePlan:
                query_params.append(f"ratePlan={ratePlan}")

            # Concatenate all query parameters with '&'
            if query_params:
                url += "?" + "&".join(query_params)

            payload = {}

            response = requests.request("GET", url, headers=self.headers, data=payload, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )
            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {exception} while getting profiles query: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_device_consumption(self, iccid: str, ratePlan: str = None):
        # Warning, data is in b, divide by 1024 twice (1,048,576)
        try:

            url = self.indosat_get_iccid_consumption_url + iccid + "/ctdUsages"
            query_params = []
            if ratePlan:
                query_params.append(f"ratePlan={ratePlan}")

            if query_params:
                url += "?" + "&".join(query_params)
            payload = {}

            response = requests.request("GET", url, headers=self.headers, data=payload, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )
            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {exception} while getting profiles query: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def edit_profile_details(
        self, iccid: str, ratePlan: str = None, status: str = None
    ):
        # Warning, data is in b, divide by 1024 twice (1,048,576)
        try:

            url = self.indosat_edit_profile_details_url + iccid
            headers = self.headers
            headers["Content-Type"] = "application/json"
            payload = {}
            if ratePlan:
                payload["ratePlan"] = ratePlan
            if status:
                payload["status"] = status
            if payload:
                response = requests.request(
                    "PUT", url, headers=headers, data=json.dumps(payload), timeout=self.timeout
                )
                if response.status_code == 200:
                    return response.json()
                else:
                    raise Exception(response.text)

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )
            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {error} while getting profiles query: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_profile_detailed> remote error happened: {exception} while getting profiles query: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def register_namespaces(self, namespaces):
        for prefix, uri in namespaces.items():
            ET.register_namespace(prefix, uri)

    def get_terminal_details(self, iccids: list):
        try:
            url = self.indosat_get_terminal_details_url

            xml_string = (
                xml_string
            ) = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
          <sch:GetTerminalDetailsRequest>
             <sch:messageId>0001</sch:messageId>
             <sch:version>1</sch:version>
             <sch:licenseKey>{self.liscense_key}</sch:licenseKey>
             <sch:iccids>
             </sch:iccids>
          </sch:GetTerminalDetailsRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            # Define and register the namespace map
            namespaces = {
                "soapenv": "http://schemas.xmlsoap.org/soap/envelope/",
                "sch": "http://api.jasperwireless.com/ws/schema",
                "wsse": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd",
            }
            self.register_namespaces(namespaces)

            request = ET.fromstring(xml_string)

            # Locate the <sch:iccids> element
            iccids_element = request.find(".//sch:iccids", namespaces)

            # Create and append new <sch:iccid> elements
            for iccid in iccids:
                new_iccid_element = ET.SubElement(
                    iccids_element, "{http://api.jasperwireless.com/ws/schema}iccid"
                )
                new_iccid_element.text = iccid

            # Convert the modified XML back to a string, with proper namespaces
            payload = ET.tostring(request, encoding="unicode")

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/GetTerminalDetails"',
            }
            response = requests.request("POST", url, headers=headers, data=payload, timeout=self.timeout)
            if response.status_code != 200:
                raise ValueError()
            response = ET.fromstring(response.text)

            # Define the namespace map
            namespaces = {
                "ns2": "http://api.jasperwireless.com/ws/schema"
            }  # Add other namespaces if necessary

            # Find all terminal elements
            terminals = response.findall(".//ns2:terminal", namespaces)

            # Initialize a list to hold dictionaries
            terminals_list = []

            # Extract data for each terminal
            for terminal in terminals:
                terminal_dict = {}
                for child in terminal:
                    # Strip the namespace from the tag
                    tag = child.tag[child.tag.find("}") + 1 :]
                    terminal_dict[tag] = child.text
                terminals_list.append(terminal_dict)

            return terminals_list
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_terminal_details> remote error happened: {error} while getting terminals "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_terminal_details> remote error happened: {error} while getting terminals"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_terminal_details> remote error happened: {exception} while getting terminals"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_terminal_rating(self, iccid):
        try:
            url = self.indosat_get_terminal_details_url

            xml_string = (
                xml_string
            ) = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
          <sch:GetTerminalRatingRequest>
             <sch:messageId>0001</sch:messageId>
             <sch:version>1</sch:version>
             <sch:licenseKey>{self.liscense_key}</sch:licenseKey>

          </sch:GetTerminalRatingRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            # Define and register the namespace map
            namespaces = {
                "soapenv": "http://schemas.xmlsoap.org/soap/envelope/",
                "sch": "http://api.jasperwireless.com/ws/schema",
                "wsse": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd",
            }
            self.register_namespaces(namespaces)

            request = ET.fromstring(xml_string)

            # Locate the <sch:GetTerminalRatingRequest> element
            iccids_element = request.find(".//sch:GetTerminalRatingRequest", namespaces)

            # Create and append new <sch:iccid> elements
            new_iccid_element = ET.SubElement(
                iccids_element, "{http://api.jasperwireless.com/ws/schema}iccid"
            )
            new_iccid_element.text = iccid

            # Convert the modified XML back to a string, with proper namespaces
            payload = ET.tostring(request, encoding="unicode")

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/GetTerminalRating"',
            }
            response = requests.request("POST", url, headers=headers, data=payload, timeout=self.timeout)
            if response.status_code != 200:
                raise ValueError()
            response = ET.fromstring(response.text)

            # Define the namespace map
            namespaces = {
                "ns2": "http://api.jasperwireless.com/ws/schema"
            }  # Add other namespaces if necessary

            # Find all terminal elements
            terminals = response.findall(".//ns2:terminalRating", namespaces)

            # Initialize a list to hold dictionaries
            terminals_list = []

            # Extract data for each terminal
            for terminal in terminals:
                terminal_dict = {}
                for child in terminal:
                    # Strip the namespace from the tag
                    tag = child.tag[child.tag.find("}") + 1 :]
                    terminal_dict[tag] = child.text
                terminals_list.append(terminal_dict)

            return terminals_list
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.get_terminal_rating> remote error happened: {error} while getting terminals "
            )
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} couldnt get consumption",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.get_terminal_rating> remote error happened: {error} while getting terminals"
            )

        except Exception as exception:
            print(
                f"<Indosat.get_terminal_rating> remote error happened: {exception} while getting terminals"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def topup_iccid(self, iccid, rate_plan):
        try:
            url = self.indosat_topup_iccid_url

            data = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
      <sch:QueueTerminalRatePlanRequest>
         <sch:messageId>0001</sch:messageId>
         <sch:version>1</sch:version>
         <sch:licenseKey>{self.liscense_key}</sch:licenseKey>
         <sch:iccid>{iccid}</sch:iccid>
         <sch:renewalRatePlan>{rate_plan}</sch:renewalRatePlan>
      </sch:QueueTerminalRatePlanRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/QueueTerminalRatePlan"',
            }
            response = requests.request("POST", url, headers=headers, data=data, timeout=self.timeout)
            if response.status_code != 200:
                response = ET.fromstring(response.text)
                namespaces = {"jws": "http://api.jasperwireless.com/ws/schema"}
                error = response.find(".//jws:error", namespaces).text
                raise ValueError(error)

            response = ET.fromstring(response.text)

            # Define the namespace map
            namespaces = {
                "ns2": "http://api.jasperwireless.com/ws/schema"
            }  # Add other namespaces if necessary

            # Find all terminal elements
            status = response.find(".//ns2:status", namespaces).text

            # Initialize a list to hold dictionaries
            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.topup_iccid> remote error happened: {error} while adding topup "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.topup_iccid> remote error happened: {error} while topping up for iccid: {iccid} for rate plan {rate_plan}"
            )

        except Exception as exception:
            print(
                f"<Indosat.topup_iccid> remote error happened: {exception} while topping up for iccid: {iccid} for rate plan {rate_plan}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def change_base_rate_plan(self, iccid, rate_plan):
        try:
            url = self.indosat_topup_iccid_url

            data = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
      <sch:EditTerminalRequest>
         <sch:messageId>0001</sch:messageId>
         <sch:version>1</sch:version>
         <sch:licenseKey>{self.liscense_key}</sch:licenseKey>
         <sch:iccid>{iccid}</sch:iccid>
         <sch:targetValue>{rate_plan}</sch:targetValue>
         <sch:changeType>4</sch:changeType>
      </sch:EditTerminalRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/EditTerminal"',
            }
            response = requests.request("POST", url, headers=headers, data=data, timeout=self.timeout)
            if response.status_code != 200:
                response = ET.fromstring(response.text)
                namespaces = {"jws": "http://api.jasperwireless.com/ws/schema"}
                error = response.find(".//jws:error", namespaces).text
                raise ValueError(error)

            response = ET.fromstring(response.text)

            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.change_base_rate_plan> remote error happened: {error} while changing ratePlan "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.change_base_rate_plan> remote error happened: {error} while changing rate Plan for iccid: {iccid} for rate plan {rate_plan}"
            )

        except Exception as exception:
            print(
                f"<Indosat.change_base_rate_plan> remote error happened: {exception} while changing rate Plan    for iccid: {iccid} for rate plan {rate_plan}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def activate_sim_status(self, iccid):
        try:
            url = self.indosat_topup_iccid_url

            data = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
      <sch:EditTerminalRequest>
         <sch:messageId>0001</sch:messageId>
         <sch:version>1</sch:version>
         <sch:licenseKey>{self.liscense_key}</sch:licenseKey>
         <sch:iccid>{iccid}</sch:iccid>
         <sch:targetValue>ACTIVATED_NAME</sch:targetValue>
         <sch:changeType>3</sch:changeType>
      </sch:EditTerminalRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/EditTerminal"',
            }
            response = requests.request("POST", url, headers=headers, data=data, timeout=self.timeout)
            if response.status_code != 200:
                response = ET.fromstring(response.text)
                namespaces = {"jws": "http://api.jasperwireless.com/ws/schema"}
                error = response.find(".//jws:error", namespaces).text
                raise ValueError(error)

            response = ET.fromstring(response.text)

            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.activate_sim_status> remote error happened: {error} while changing ratePlan "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.activate_sim_status> remote error happened: {error} while activating iccid: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.activate_sim_status> remote error happened: {exception} while activating iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def set_renewal_mode_to_named_plan(self, iccid):
        try:
            url = self.indosat_topup_iccid_url

            data = f"""<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://api.jasperwireless.com/ws/schema">
       <soapenv:Header> 
      <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"> 
        <wsse:UsernameToken> 
          <wsse:Username>{self.user_name}</wsse:Username> 
          <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password> 
        </wsse:UsernameToken> 
      </wsse:Security> 
    </soapenv:Header>
       <soapenv:Body>
      <sch:EditTerminalRatingRequest>
         <sch:messageId>0001</sch:messageId>
         <sch:version>1</sch:version>
         <sch:licenseKey>{self.liscense_key}</sch:licenseKey>
         <sch:iccid>{iccid}</sch:iccid>
         <sch:renewalMode>N</sch:renewalMode>
      </sch:EditTerminalRatingRequest>
       </soapenv:Body>
    </soapenv:Envelope>"""

            headers = {
                "Content-Type": "text/xml",
                "SOAPAction": '"http://api.jasperwireless.com/ws/service/terminal/EditTerminalRating"',
            }
            response = requests.request("POST", url, headers=headers, data=data, timeout=self.timeout)
            if response.status_code != 200:
                response = ET.fromstring(response.text)
                namespaces = {"jws": "http://api.jasperwireless.com/ws/schema"}
                error = response.find(".//jws:error", namespaces).text
                raise ValueError(error)

            response = ET.fromstring(response.text)

            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Indosat.set_renewal_mode_to_named_plan> remote error happened: {error} while setting renewal mode to Named ratePlan for iccid"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Indosat.set_renewal_mode_to_named_plan> remote error happened: {error} while while setting renewal mode to Named ratePlan for iccid: {iccid}"
            )

        except Exception as exception:
            print(
                f"<Indosat.set_renewal_mode_to_named_plan> remote error happened: {exception} while while setting renewal mode to Named ratePlan for iccid: {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}


class BayobabAccessCred:
    def __init__(self, customer_id="", access_token="", refresh_token="", expire_at=None):
        self.customer_id = customer_id
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.expire_at = expire_at

class Bayobab(metaclass=VendorMetaClass):
    """
    class for API calls to bayobab services, import and use in your controllers
    """
    retry = False
    vendor_name: str = "Bayobab"
    bayobab_plan_limit = 4
    access_cred: BayobabAccessCred = None

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)
        required_attrs = [
            "bayobab_url",
            "bayobab_username",
            "bayobab_password",
            "es2plus_url",
            "es2plus_fri",
            "es2plus_fci",
        ]

        if not (instance_config and isinstance(instance_config, types.ModuleType) and vendor):
            raise ValueError("Required Bayobab module instance_config and vendor object are missing.")

        if not all(hasattr(instance_config, attr) for attr in required_attrs):
            raise ValueError("Bayobab instance_config module must contain required attributes: " f"{', '.join(required_attrs)}")

        self.vendor = vendor
        self.instance_config = instance_config

        self.bayobab_username = instance_config.bayobab_username
        self.bayobab_password = instance_config.bayobab_password
        self.bayobab_url = self.instance_config.bayobab_url
        self.es2plus_url = instance_config.es2plus_url
        self.es2plus_fri = instance_config.es2plus_fri
        self.es2plus_fci = instance_config.es2plus_fci

        self.auth_url = self.bayobab_url + "/api/v2/auth/token"
        self.get_bundles_url = self.bayobab_url + "/api/v3/customer/{customer_id}/offer/created-offers"
        self.get_profiles_url = self.bayobab_url + "/api/v2/customer/{customer_id}/subscriber"
        self.get_profile_detail_url = self.es2plus_url + "/gsma/rsp2/es2plus/confirmOrder"
        self.download_profile_url = self.es2plus_url + "/gsma/rsp2/es2plus/downloadOrder"
        self.get_install_status_url = self.es2plus_url + "/gsma/rsp2/es2plus/tacInfo"
        self.get_zone_detail_url = self.bayobab_url + "/api/v2/customer/{customer_id}/availability-zone/{zone_id}"
        self.attach_offer_url = self.bayobab_url + "/api/v2/subscriber/iccid/{iccid}/offer/{offer_id}"
        self.detach_offer_url = self.bayobab_url + "/api/v2/subscriber/iccid/{iccid}/offer/{offer_id}"
        self.fetch_consumption_url = self.bayobab_url + "/api/v2/subscriber/iccid/{iccid}/offer"

    def _login_api_request(self):
        try:
            # Check if the current token is valid
            if self.access_cred and self.access_cred.expire_at > datetime.datetime.utcnow():
                return

            payload = {}

            # If access_cred is empty, we need to get new credentials
            if not self.access_cred:
                payload = {
                    "username": self.bayobab_username,
                    "password": self.bayobab_password,
                }
                response = requests.post(self.auth_url, json=payload, timeout=self.timeout)
            # If access token has expired, refresh the access token
            else:
                payload = {
                    "token": self.access_cred.access_token,
                    "refreshToken": self.access_cred.refresh_token,
                }
                response = requests.put(self.auth_url, json=payload, timeout=self.timeout)

            if response.status_code == 200:
                resp_dict = response.json()
                if resp_dict.get("errorCode") is None and resp_dict.get("content"):
                    content = resp_dict["content"][0]
                    self.access_cred = BayobabAccessCred(
                        customer_id=content["customerId"],
                        access_token=content["token"],
                        refresh_token=content["refreshToken"],
                        expire_at=datetime.datetime.utcnow() + datetime.timedelta(seconds=content["validityTime"] - 120),
                    )
                else:
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error(
                        "Bayobab refresh token has failed with error message: %s",
                        error_message,
                    )
            else:
                logger.error(
                    "Bayobab refresh token has failed: %s with status code %d",
                    response.text,
                    response.status_code,
                )
        except Exception as exception:
            logger.exception("Bayobab refresh token exception occured %s", exception)

    def _check_auth(self):
        self._login_api_request()
        if not self.access_cred or datetime.datetime.utcnow() > self.access_cred.expire_at:
            raise UnauthorizedError("Bayobab access token expired or not available.")

    def get_bundles(self, page: int = 0, size: int = 100):
        """Fetch customer bundles from Bayobab API."""
        self._check_auth()
        url = self.get_bundles_url.format(customer_id=self.access_cred.customer_id)
        params = {"page": page, "size": size}
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}

        try:
            response = requests.get(url=url, headers=headers, params=params, timeout=self.timeout)
            if response.status_code == 200:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error("Bayobab get_bundles failed with error: %s", error_message)
                    return None

                return resp_dict
            else:
                logger.error(
                    "Bayobab get_bundles has failed: %s with status code %d",
                    response.text,
                    response.status_code,
                )
                return None
        except Exception as exception:
            logger.exception("Bayobab get_bundles exception occured %s", exception)

    def get_profiles(self, page: int = 0, size: int = 100):
        """Fetch customer profiles from Bayobab API."""
        self._check_auth()
        url = self.get_profiles_url.format(customer_id=self.access_cred.customer_id)
        params = {"page": page, "size": size}
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}

        try:
            response = requests.get(url, headers=headers, params=params, timeout=self.timeout)
            if response.status_code == 200:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error("Bayobab profiles failed with error: %s", error_message)
                    return None

                return resp_dict
            else:
                logger.error(
                    "Bayobab get profiles has failed: %s with status code %d",
                    response.text,
                    response.status_code,
                )
                return None
        except Exception as exception:
            logger.exception("Bayobab profiles exception occured %s", exception)
            return None

    def get_profile_activation_code(self, iccid: str, releaseFlag: bool = True):
        "Get profile activation code from ES2+"
        headers = {"Content-Type": "application/json"}
        payload = {
            "header": {
                "functionRequesterIdentifier": self.es2plus_fri,
                "functionCallIdentifier": self.es2plus_fci,
            },
            "iccid": iccid,
            "releaseFlag": releaseFlag,
        }
        try:
            response = requests.post(
                url=self.get_profile_detail_url,
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as req_err:
            logger.error(
                "Bayobab es2plaus get_profile_activation_code request failed: %s",
                req_err,
            )
            return None
        except Exception as exception:
            logger.exception(
                "Bayobab es2plaus get_profile_activation_code unexpected error occurred,: %s",
                exception,
            )
            return None

    def download_from_rsp(self, iccid: str):
        "Download profile from RSP in order to call confirm order(to get activation code)"
        headers = {"Content-Type": "application/json"}
        payload = {
            "header": {
                "functionRequesterIdentifier": self.es2plus_fri,
                "functionCallIdentifier": self.es2plus_fci,
            },
            "iccid": iccid,
        }
        try:
            response = requests.post(
                url=self.download_profile_url,
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as req_err:
            logger.error(
                "Bayobab es2+ download_from_rsp request failed: %s",
                str(req_err),
            )
            return None
        except Exception as exception:
            logger.exception(
                "Bayobab es2+ download_from_rsp unexpected error occurred,: %s",
                str(exception),
            )
            return None

    def get_profile_install_status(self, iccid: str):
        "Get profile profile installation status of specific iccid from ES2+"
        headers = {"Content-Type": "application/json"}
        payload = {
            "header": {
                "functionRequesterIdentifier": self.es2plus_fri,
                "functionCallIdentifier": self.es2plus_fci,
            },
            "iccid": iccid,
        }
        try:
            response = requests.post(
                url=self.get_install_status_url,
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )
            if response.status_code == 200:
                return response.json()
            else:
                logger.error("Bayobab error occured while fetching installaion status for profile %s, status_code %d", iccid, response.status_code)
                return None
        except requests.RequestException as req_err:
            logger.error(
                "Bayobab es2plus get_profile_install_status request failed: %s",
                req_err,
            )
            return None
        except Exception as exception:
            logger.exception(
                "Bayobab es2plus get_profile_install_status unexpected error occurred %s",
                exception,
            )
            return None

    def get_availability_zone_detail(self, zone_id: str):
        """Get availability zone detail of a specific zone"""
        self._check_auth()
        url = self.get_zone_detail_url.format(customer_id=self.access_cred.customer_id, zone_id=zone_id)
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}

        try:
            response = requests.get(url, headers=headers, timeout=self.timeout)
            if response.status_code == 200:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error(
                        "Bayobab get_availability_zone_detail failed with error: %s",
                        error_message,
                    )
                    return None

                return resp_dict
            else:
                logger.error(
                    "Bayobab get_availability_zone_detail failed: %s with status code %d",
                    response.text,
                    response.status_code,
                )
                return None
        except Exception as exception:
            logger.exception("Bayobab get_availability_zone_detail exception occured %s", exception)
            return None

    def attach_offer(self, iccid: str, offer_id: str, my_offer: bool = False):
        """Attach an offer to a profile"""
        self._check_auth()
        if self.check_plan_limit_reached(iccid, my_offer):
            logger.error("Bayobab profile %s has reached maximum attached profile limit %s", iccid, self.bayobab_plan_limit)
            return {"errorCode": "LimitReached", "errorMessage": "This bundle cannot be topped up"}
        url = self.attach_offer_url.format(iccid=iccid, offer_id=offer_id)
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}
        payload = {"myOffer": my_offer}
        try:
            response = requests.post(url=url, headers=headers, json=payload, timeout=self.timeout)

            if response.status_code == 202:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error("Bayobab attach offer to iccid %s with offer_id %s has  failed with resp: %s", iccid, offer_id,
                                 resp_dict)
                    return None

                return resp_dict
            else:
                logger.error(
                    "Bayobab attach offer to iccid %s with offer_id %s has failed: %s with status code %d", iccid, offer_id,
                    response.text, response.status_code)
                return None
        except Exception as exception:
            logger.exception("Bayobab attach offer to iccid %s with offer_id %s, exception occured %s", iccid, offer_id, exception)
            return None

    def detach_offer(self, iccid: str, offer_id: str, my_offer: bool = False):
        """Detach an offer from the profile"""
        self._check_auth()
        url = self.detach_offer_url.format(iccid=iccid, offer_id=offer_id)
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}
        payload = {"myOffer": my_offer}
        try:
            response = requests.delete(url=url, headers=headers, json=payload, timeout=self.timeout)

            if response.status_code == 202:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    logger.error(
                        "Bayobab detach offer failed: ICCID %s, Offer ID %s, Error: %s",
                        iccid,
                        offer_id,
                        resp_dict.get("errorMessage", "Unknown error"),
                    )
                    return None
                return resp_dict
            logger.error(
                "Bayobab detach offer failed: ICCID %s, Offer ID %s, Response: %s, Status Code: %d",
                iccid,
                offer_id,
                response.text,
                response.status_code,
            )
            return None
        except Exception as exception:
            logger.exception("Exception in Bayobab detach offer: ICCID %s, Offer ID %s, Error: %s", iccid, offer_id, str(exception))
            return None

    def check_plan_limit_reached(self, iccid: str, my_offer=False) -> bool:
        """Check if the plan limit is reached and try to detach expired offers if necessary."""
        successful_orders_count = Order_history.objects(iccid=iccid, order_status="Successful").count()

        if successful_orders_count < self.bayobab_plan_limit:
            logger.info("Bayobab profile %s can accept more offers.", iccid)
            return False

        # Fetch expired orders only if limit is reached
        expired_orders = Order_history.objects(iccid=iccid, order_status="Successful", plan_status="Expired")

        if not expired_orders:
            logger.error("Bayobab profile %s has no expired offers to detach.", iccid)
            return True

        for order in expired_orders:

            resp = self.detach_offer(iccid, order.plan_uid, my_offer)
            if resp:
                logger.info(
                    "Bayobab plan %s successfully detached from profile %s. You can now load new offers.", order.plan_uid, iccid
                )
                return False  # Stop after successful detachment

            logger.error("Failed to detach plan %s from profile %s. Trying another expired offer.", order.plan_uid, iccid)

        return True  # If all expired offers failed to detach


    def fetch_consumption(self, iccid: str):
        """Fetch attached offers of a single profile"""
        self._check_auth()
        url = self.fetch_consumption_url.format(iccid=iccid)
        headers = {"Authorization": f"Bearer {self.access_cred.access_token}"}

        try:
            response = requests.get(url, headers=headers, timeout=self.timeout)
            if response.status_code == 200:
                resp_dict = response.json()
                if error_code := resp_dict.get("errorCode"):
                    error_message = resp_dict.get("errorMessage", "Unknown error")
                    logger.error(
                        "Bayobab fetch consumption for iccid: %s failed with error: %s",
                        iccid,
                        error_message,
                    )
                    return None

                return resp_dict
            else:
                logger.error(
                    "Bayobab fetch consumption for iccid: %s failed: %s with status code %d",
                    iccid,
                    response.text,
                    response.status_code,
                )
                return None
        except Exception as exception:
            logger.exception("Bayobab fetch consumption for iccid: %s exception occured %s", iccid, exception)
            return None


class Orange(metaclass=VendorMetaClass):
    """
    class for API calls to Orange services, import and use in your controllers
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Orange"
    headers = {"Authorization": "Bearer "}

    class TriggerType(Enum):
        TRAFFIC_BEARER = "TRAFFIC_BEARER"
        TRAFFIC_LOCATION = "TRAFFIC_LOCATION"
        TRAFFIC_UNITARY_THRESHOLD = "TRAFFIC_UNITARY_THRESHOLD"
        TRAFFIC_GLOBAL_THRESHOLD = "TRAFFIC_GLOBAL_THRESHOLD"
        TRAFFIC_DYNAMIC_GLOBAL_THRESHOLD = "TRAFFIC_DYNAMIC_GLOBAL_THRESHOLD"
        UPDATE_IMEI = "UPDATE_IMEI"
        TRAFFIC_SILENT_MACHINE = "TRAFFIC_SILENT_MACHINE"
        TRAFFIC_CRAZY_MACHINE = "TRAFFIC_CRAZY_MACHINE"
        M2M_SUBSCRIPTION_STATUS_TRIGGER = "M2M_SUBSCRIPTION_STATUS_TRIGGER"

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)

        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "orange_username")
            and getattr(instance_config, "orange_password")
            and vendor
        ):
            raise ValueError(
                "required module instance_config: module containing orange_username,"
                " orange_password"
            )

        self.vendor = vendor
        self.retry = True

        self.instance_config = instance_config
        self.user_name = instance_config.orange_username.replace(" ", "")
        self.password = instance_config.orange_password.replace(" ", "")

        self.credentials = f"{self.user_name}:{self.password}"
        self.encoded_credentials = base64.b64encode(self.credentials.encode()).decode()
        self.soap_headers = {
            "Content-Type": "application/xml",
            "Authorization": f"Basic {self.encoded_credentials}",
        }

        self.connectivity_directory_url = (
            "https://iosw-pbe-ba.orange.com:443/MLM_EXT_IMC/ConnectivityDirectory-1"
        )
        self.orange_consumption_url = "https://api.orange.com/dub/v2/usageReport"

        self.orange_provision_iccid_url = (
            "https://api.orange.com/iot-wholesale-data-supervision/v1/productOrders"
        )

        self.login_user_name = instance_config.orange_login_username.replace(" ", "")
        self.login_password = instance_config.orange_login_password.replace(" ", "")
        self.login_credentials = f"{self.login_user_name}:{self.login_password}"
        self.encoded_login_credentials = base64.b64encode(
            self.login_credentials.encode()
        ).decode()

        self.login_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
            "Authorization": f"Basic {self.encoded_login_credentials}",
        }

        self.orange_login_url = "https://api.orange.com/oauth/v3/token"

        self.orange_consumption_url = "https://api.orange.com/dub/v2/usageReport"

        # self.liscense_key = instance_config.indosat_liscense_key.replace(' ', '')
        #
        # self.indosat_url = self.instance_config.indosat_url.replace(' ', '')
        # self.credentials = f"{self.user_name}:{self.password}"
        # self.encoded_credentials = base64.b64encode(self.credentials.encode()).decode()
        # self.headers = {'Authorization': f"Basic {self.encoded_credentials}"}
        # self.indosat_get_profiles_url = self.indosat_url + '/devices?'
        # self.indosat_get_bundles_url = self.indosat_url + '/rateplans'
        # self.indosat_get_profile_detailed_url = self.indosat_url + '/devices/'
        # self.indosat_get_iccid_consumption_url = self.indosat_url + '/devices/'
        # self.indosat_edit_profile_details_url = self.indosat_url + '/devices/'
        # self.rate_revenue = self.vendor.rate_revenue
        # self.currency_exchange_rate = self.vendor.currency_exchange_rate
        # self.indosat_get_terminal_details_url = "https://indosatooredoo.jasper.com/ws/service/terminal"
        # self.indosat_topup_iccid_url = "https://indosatooredoo.jasper.com/ws/service/terminal"

    def _login_api_request(self):
        try:
            headers = self.login_headers
            payload = "grant_type=client_credentials"

            response = requests.request(
                "POST", self.orange_login_url, headers=headers, data=payload, timeout=self.timeout
            )
            if response.status_code != 200:
                raise ValueError(response.text)
            access_token = response.json()["access_token"]
            self.headers["Authorization"] = f"Bearer {access_token}"
            encrypted_access_token = Crypt().encrypt(
                access_token, self.instance_config.token_key
            )
            self.vendor.update(set__temp_token=encrypted_access_token)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Orange._login_api_request> Exception {exception}"
            )
            raise exception

    def _login(self):
        try:
            try:
                if access_token := decrypt_vendor_token(self.vendor_name):
                    self.headers["Authorization"] = f"Bearer {access_token}"
                    return
            except:
                pass
            self._login_api_request()

        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Orange._login> Exception: {exception}"
            )

    def recursive_element_to_dict(self, element):
        # This function recursively converts an element and its children into a dictionary.
        result = {}
        if element.text and element.text.strip():
            result = element.text.strip()

        for subelem in element:
            tag = subelem.tag[subelem.tag.find("}") + 1 :]  # Strip the namespace
            if tag not in result:
                result[tag] = self.recursive_element_to_dict(subelem)
            else:
                # If the tag is already in the dictionary, we need to turn it into a list
                if not isinstance(result[tag], list):
                    result[tag] = [
                        result[tag]
                    ]  # Convert the existing entry into a list
                result[tag].append(self.recursive_element_to_dict(subelem))
        return result

    def parse_xml_response(self, xml_data, namespaces, level_name):
        response = ET.fromstring(xml_data)

        # Define the namespace map relevant to your XML structure

        # Find all elements under <ns4:connectivityDirectory>
        directories = response.findall(level_name, namespaces)

        directories_list = [
            self.recursive_element_to_dict(directory) for directory in directories
        ]
        return directories_list

    def get_profiles(self, fetch_suspended: bool = True, iccid: str = ""):
        """
        Gets all the iccids in our fleet.
        """

        try:
            if fetch_suspended:
                fetch_suspended_query = "\n<con:simStatus>SUSPENDED</con:simStatus>\n"
            else:
                fetch_suspended_query = ""

            if iccid:
                iccid_query = f"\n<con:iccid>{iccid}</con:iccid>\n"
            else:
                iccid_query = ""

            url = self.connectivity_directory_url

            payload = (
                '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">\n<soap:Body>\n<ns3:searchInConnectivityDirectory xmlns:ns2="http://common.types.malima.francetelecom.com" xmlns:ns3="http://webservice.malima.francetelecom.com/v1" xmlns:con="http://connectivityDirectory.types.malima.francetelecom.com">\n<con:version>1.25</con:version>\n<con:searchCriterion>'
                + fetch_suspended_query
                + iccid_query
                + "</con:searchCriterion>\n</ns3:searchInConnectivityDirectory>\n</soap:Body>\n</soap:Envelope>"
            )
            headers = self.soap_headers

            # TODO it is unsure if there is a default max range size that they will get us, so if we have 1000 lines and the api only returns 100, we will have to inforce rangeSize and rangeStart, and check on resultsNumberExceeded param in the response
            response = requests.request("POST", url, headers=headers, data=payload, timeout=self.timeout)
            if response.status_code != 200:
                raise ValueError(response.text)

            # Define the namespace map
            namespaces = {
                "soap": "http://schemas.xmlsoap.org/soap/envelope/",
                "ns2": "http://common.types.malima.francetelecom.com",
                "ns3": "http://webservice.malima.francetelecom.com/v1",
                "ns4": "http://connectivityDirectory.types.malima.francetelecom.com",
                "ns5": "http://exception.malima.francetelecom.com",
            }
            response_dict = self.parse_xml_response(
                response.text,
                namespaces,
                ".//ns3:searchInConnectivityDirectoryResponse",
            )

            if response_dict:

                return response_dict[0]
            else:
                return {}

        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Orange.get_profiles> remote error happened: {error} while getting profiles"
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return {}

        except ValueError as error:
            print(
                f"<Orange.get_profiles> remote error happened: {error} while getting profiles"
            )

        except Exception as exception:
            print(
                f"<Orange.get_profiles> remote error happened: {exception} while getting profiles"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

        return {}

    def get_profile_consumption(self, msisdn):
        try:
            self._login()
            url = self.orange_consumption_url
            print("msisdn, type(msisdn)", msisdn, type(msisdn))
            payload = json.dumps({"msisdn": str(msisdn)})
            response = requests.request("POST", url, headers=self.headers, data=payload, timeout=self.timeout)
            raise_if_401_403(response)
            if response.status_code == 200:
                return response.json()
            else:
                raise ValueError(response.text)
        except Exception as exception:
            print(
                f"[{datetime.datetime.utcnow()}] <Orange.get_profile_consumption> "
                f"error happened: {exception} while getting consumption for msisdn: {msisdn}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return {}

    def provision_iccid(
        self,
        iccid,
        zone_name=None,
        data_amount: str = "",
        bundle_duration: str = "",
        initial_provisioning_mode: bool = False,
        action="add",
    ):
        try:
            self._login()
            if initial_provisioning_mode:
                data_amount = "1"
                bundle_duration = "1"
            elif not all([zone_name, data_amount, bundle_duration]):
                raise ValueError(
                    "zone_name, data_amount, and bundle_duration are required parameters if not in initial provision mode!"
                )
            url = self.orange_provision_iccid_url
            current_date = datetime.datetime.utcnow()
            today_date = current_date.strftime("%Y-%m-%d")
            end_date = (current_date + datetime.timedelta(days=365)).strftime(
                "%Y-%m-%d"
            )
            payload = {
                "orderItem": [
                    {
                        "action": action,
                        "offerCode": "",
                        "product": {
                            "productCharacteristic": {
                                "startDate": today_date,
                                "endDate": end_date,
                                "realLifeDuration": bundle_duration,
                                "realLifeDurationUnit": "Day",
                                "roamingZones": "World",
                                "data": {
                                    "fairUseVolume": "CO",
                                    "addedBucket": [
                                        {
                                            "zone": "World",
                                            "volume": data_amount,
                                            "type": "current",
                                        }
                                    ],
                                },
                                "voice": {"usingVoice": "false"},
                                "sms": {"usingSms": "false"},
                            },
                            "reliesOnProduct": {"iccid": iccid},
                        },
                    }
                ],
            }

            headers = self.headers

            response = requests.request("POST", url, headers=headers, json=payload, timeout=self.timeout)
            raise_if_401_403(response)
            if response.status_code != 201:
                raise ValueError(response.text)
            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Orange.provision_iccid> remote error happened: {error} while provisioning iccid {iccid} "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return False

        except ValueError as error:
            print(
                f"<Orange.provision_iccid> remote error happened: {error} while provisioning iccid {iccid}"
            )

        except Exception as exception:
            print(
                f"<Orange.provision_iccid> remote error happened: {exception} while while provisioning iccid {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return False

        return False

    def topup_iccid(
        self,
        iccid,
        zone_name: str,
        data_amount: str,
        end_date: str,
        create_trigger: bool = True,
        emails: List[str] = None,
        trigger_data_amount: Optional[str] = None,
        subscription_id: Optional[str] = None,
    ):
        """
        :param data_amount: In Megabytes
        :param iccid:
        :param zone_name:
        :param end_date:
        :param create_trigger:
        :param emails:
        :param trigger_data_amount:
        :param subscription_id:
        """
        try:
            if (
                create_trigger
                and not all([emails, trigger_data_amount, end_date, subscription_id])
                and not all(
                    [
                        isinstance(emails, list),
                        isinstance(trigger_data_amount, str),
                        isinstance(end_date, str),
                        isinstance(subscription_id, str),
                    ]
                )
            ):
                raise ValueError(
                    "emails, trigger_data_amount, trigger_bundle_duration, subscription_id one of them is missing or empty"
                )

            self._login()
            url = self.orange_provision_iccid_url
            payload = {
                "orderItem": [
                    {
                        "action": "modify",
                        "product": {
                            "productCharacteristic": {
                                "endDate": end_date,
                                "data": {
                                    "addedBucket": [
                                        {
                                            "type": "current",
                                            "zone": zone_name,
                                            "volume": data_amount,
                                            "endDate": end_date,
                                        }
                                    ]
                                },
                            },
                            "reliesOnProduct": {"iccid": iccid},
                        },
                    }
                ]
            }

            headers = self.headers
            response = requests.request("POST", url, headers=headers, json=payload, timeout=self.timeout)

            raise_if_401_403(response)
            if response.status_code != 200:
                raise ValueError(response.text)
            if create_trigger:
                self.create_trigger(
                    iccid=iccid,
                    subscription_id=subscription_id,
                    trigger_type=Orange.TriggerType.TRAFFIC_UNITARY_THRESHOLD.value,
                    emails=emails,
                    trigger_data_amount=trigger_data_amount,
                )
                self.create_trigger(
                    iccid=iccid,
                    subscription_id=subscription_id,
                    trigger_type=Orange.TriggerType.TRAFFIC_UNITARY_THRESHOLD.value,
                    emails=emails,
                    trigger_data_amount=str(float(trigger_data_amount) * 0.8),
                )
            return True
        except (
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
            NewConnectionError,
        ) as error:
            print(
                f"<Orange.topup_iccid> remote error happened: {error} while topping up iccid {iccid} "
            )
            # vendor is not available, so we should deactivate all bundles related to this vendor
            deactivate_vendor(self.vendor_name)
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {self.vendor_name} deactivated",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_VENDOR.format(
                    datetime.datetime.utcnow(), self.vendor_name, error
                ),
            )

            return False

        except ValueError as error:
            print(
                f"<Orange.topup_iccid> remote error happened: {error} while topping up iccid {iccid}"
            )

        except Exception as exception:
            print(
                f"<Orange.topup_iccid> remote error happened: {exception} while while topping up iccid {iccid}"
            )
            if isinstance(exception, UnauthorizedError):
                raise exception
            return False

        return False

    def create_trigger(
        self,
        emails: List[str],
        iccid: str = None,
        subscription_id: str = None,
        trigger_type: str = "TRAFFIC_UNITARY_THRESHOLD",
        trigger_data_amount: Optional[str] = None,
    ):
        headers = self.soap_headers
        headers["Content-Type"] = "application/json"
        headers["Accept"] = "application/json"
        url = "https://iosw-pbe-ba-rest.orange.com:443/MLM_EXT_IMC/CustomerAlarmAPI-1/v1/triggers"
        trigger_name = f"{float(trigger_data_amount)}GB-trigger-"
        if float(trigger_data_amount) * 1024 < 1000:
            trigger_name = f"{int(float(trigger_data_amount) * 1024)}MB-trigger-"
        payload = {
            "level": "HIGH",
            "trigger_type": trigger_type,
            "criteria": {
                "traffic_unitary_threshold": {
                    "bearer_type": "DATA",
                    "threshold_value": float(trigger_data_amount) * 1024 * 1024 * 1024,
                    "call_origin": "ALL_ORIGIN",
                    "communication_way": "INOUT",
                }
            },
            "scope": {"perimeter": "SUBSCRIPTION", "subscription_id": subscription_id},
            "name": f"{trigger_name}for-{iccid}",
            "notification": {"by_mail": True},
            "notification_emails": emails,
        }
        if not iccid or not subscription_id:
            payload["scope"] = {"perimeter": "FLEET"}
            payload["name"] = trigger_name
        response = requests.request("POST", url, headers=headers, json=payload, timeout=self.timeout)
        raise_if_401_403(response)
        if response.status_code != 201:
            raise ValueError(response.text)


def dynamic_vendor_save_token(vendor_name):
    email_subject = "{} token for {} env".format(vendor_name, os.getenv("ENV"))
    try:
        access_token, _ = get_token_dynamic_vendor(vendor_name=vendor_name)
        if access_token:
            vendor_token = access_token["data"]["token"]
            str_key = instance_config.token_key
            encrypt_helper = Crypt()
            encrypted_vendor_token = encrypt_helper.encrypt(vendor_token, str_key)
            Vendors.objects(vendor_name=vendor_name).update(
                set__temp_token=encrypted_vendor_token
            )
        else:
            send_custom_monitor_email(
                subject=f"------URGENT------ Vendor: {vendor_name}",
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_TOKEN_VENDOR.format(
                    datetime.datetime.utcnow()
                ),
            )
            return False

    except Exception as e:
        print("exception dynamic_vendor_save_token ", str(e))
        send_custom_monitor_email(
            subject=email_subject,
            body=CUSTOMER_SUPPORT_MESSAGE_FOR_TOKEN_VENDOR.format(
                datetime.datetime.utcnow()
            ),
        )


# Get token dynamic
def get_token_dynamic_vendor(vendor_name):
    try:
        vendor_url = getattr(instance_config, vendor_name.lower() + "_url")
        username = getattr(instance_config, vendor_name.lower() + "_username")
        password = getattr(instance_config, vendor_name.lower() + "_password")
        headers = {}
        payload = {"email": username, "password": password}
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        timeout = int(getattr(instance_config, "http_connection_timeout", 10))
        r = requests.post("{}/user/login/v1".format(vendor_url), json=payload, timeout=timeout)

        response = r.json()
        return response, ""
    except Exception as e:
        print("Exception at get_token_dynamic_vendor as ", str(e))
        return False, "Couldn't get token!"


def montymobile_save_token():
    email_subject = "MontyMobile token"
    try:
        access_token, exception = get_token_montymobile()
        if access_token:
            montymobile_token = access_token["accessToken"]
            str_key = instance_config.token_key
            encrypt_helper = Crypt()
            encrypted_token = encrypt_helper.encrypt(montymobile_token, str_key)
            Vendors.objects(vendor_name=MONTYMOBILE_VENDOR).update(
                set__temp_token=encrypted_token
            )
        else:
            send_custom_monitor_email(
                subject=email_subject,
                body=CUSTOMER_SUPPORT_MESSAGE_FOR_TOKEN_VENDOR.format(
                    datetime.datetime.utcnow()
                ),
            )
    except Exception as e:
        send_custom_monitor_email(
            subject=email_subject,
            body=CUSTOMER_SUPPORT_MESSAGE_FOR_TOKEN_VENDOR.format(
                datetime.datetime.utcnow()
            ),
        )
        print("exception montymobile_save_token ", str(e))


##MontyMobile Integration
def get_token_montymobile():
    try:
        vendor_url = instance_config.montymobile_url
        username = instance_config.montymobile_username
        password = instance_config.montymobile_password
        payload = {"email": username, "password": password}

        timeout = int(getattr(instance_config, "http_connection_timeout", 10))
        r = requests.post("{}/user/login/v1".format(vendor_url), json=payload, timeout=timeout)
        response = r.json()
        return response, ""

    except Exception as e:
        print("Exception at get_token_dynamic_vendor as :", str(e))
        return False, "Couldn't get token!"


def decrypt_vendor_token(vendor_name):
    encrypted_vendor_token = get_vendor_info(vendor_name).temp_token
    encrypt_helper = Crypt()
    str_key = instance_config.token_key
    decrypted_value = encrypt_helper.decrypt(encrypted_vendor_token, str_key)
    return decrypted_value


class MontyReseller(metaclass=VendorMetaClass):
    """
    Helper class to integrate with reseller service APIs
    """

    retry = True
    user_name: str
    password: str
    vendor_name: str = "Monty Reseller"
    headers = {"Access-Token": ""}
    monty_reseller_url = "https://resellerapi.montyesim.com/api/v0"
    login_endpoint = "/Agent/login"
    load_endpoint = "/Bundles"
    topup_endpoint = "/Bundles/Topup"
    consumption_endpoint = "/Orders/Consumption"
    get_bundles_endpoint = "/Bundles"
    get_bundle_networks_endpoint = "/Bundles/Networks"
    get_available_regions_endpoint = "/AvailableRegions"
    get_available_countries_endpoint = "/AvailableCountries"
    reserve_profile_endpint = "/Bundles/Reserve"
    complete_transaction_endpoint = "/Bundles/Complete"
    cancel_transaction_endpoint = "/Bundles/Cancel"

    def __init__(self):
        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "monty_reseller_agent_username")
            and getattr(instance_config, "monty_reseller_agent_password")
            and getattr(instance_config, "monty_reseller_url")
        ):
            raise ValueError(
                "Required module instance_config: module containing monty_reseller_agent_username,"
                " monty_reseller_agent_password and monty_reseller_url"
            )

        self._instance_config = instance_config
        self._user_name = instance_config.monty_reseller_agent_username.replace(" ", "")
        self._password = instance_config.monty_reseller_agent_password.replace(" ", "")
        self._base_url = self._instance_config.monty_reseller_url.replace(" ", "")
        self._login_url = self._base_url + MontyReseller.login_endpoint
        self._load_url = self._base_url + MontyReseller.load_endpoint
        self._topup_url = self._base_url + MontyReseller.topup_endpoint
        self._consumption_url = self._base_url + MontyReseller.consumption_endpoint
        self._get_bundles_url = self._base_url + MontyReseller.get_bundles_endpoint
        self._get_bundle_networks_url = (
            self._base_url + MontyReseller.get_bundle_networks_endpoint
        )
        self._get_available_regions_url = (
            self._base_url + MontyReseller.get_available_regions_endpoint
        )
        self._get_available_countries_url = (
            self._base_url + MontyReseller.get_available_countries_endpoint
        )
        self._reserve_profile_url = (
            self._base_url + MontyReseller.reserve_profile_endpint
        )
        self._complete_transaction_url = (
            self._base_url + MontyReseller.complete_transaction_endpoint
        )
        self._cancel_transaction_url = (
            self._base_url + MontyReseller.cancel_transaction_endpoint
        )

    def _login_api_request(self):
        try:
            login_payload = {"username": self._user_name, "password": self._password}
            response = requests.request(
                "POST", url=self._login_url, json=login_payload, timeout=self.timeout
            )
            response.raise_for_status()
            self.headers = {"Access-Token": response.json()["access_token"]}
        except (requests.exceptions.RequestException, ValueError) as e:
            logging.error(
                "Couldn't authenticate with reseller service due to %s", str(e)
            )
            send_custom_monitor_email(
                subject="Monty Reseller Auth Error",
                body=f"Couldn't authenticate with reseller service: {str(e)}",
            )
            raise ValueError("Couldn't authenticate with Monty Reseller service") from e

    def _make_request(self, method, url, **kwargs):
        try:
            response = requests.request(
                method, url, headers=self.headers, timeout=self.timeout, **kwargs
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error("Request to %s failed: %s", url, str(e))
            if isinstance(
                e, requests.exceptions.HTTPError
            ) and e.response.status_code in [401, 403]:
                raise UnauthorizedError(
                    "Unauthorized access to Monty Reseller API"
                ) from e
            raise ValueError(f"Request to Monty Reseller API failed: {str(e)}") from e

    def get_bundles(self, **params):
        return self._make_request("GET", self._get_bundles_url, params=params)

    def get_consumption(self, order_id: str = None):
        params = {"order_id": order_id} if order_id else {}
        return self._make_request("GET", self._consumption_url, params=params)

    def create_order(self, bundle_code: str):
        payload = {"bundle_code": bundle_code}
        return self._make_request("POST", self._load_url, json=payload)

    def topup_order(self, previous_order_reference: str, bundle_code: str):
        payload = {"previous_order_reference": previous_order_reference, "bundle_code": bundle_code}
        return self._make_request("POST", self._topup_url, json=payload)

    def get_networks_of_bundle(self, bundle_code: str):
        params = {"bundle_code": bundle_code}
        return self._make_request("GET", self._get_bundle_networks_url, params=params)

    def get_regions(self):
        return self._make_request("GET", self._get_available_regions_url)

    def get_countries(self):
        return self._make_request("GET", self._get_available_countries_url)

    def reserve_profile(self, bundle_code: str, order_reference: str = None):
        payload = {"bundle_code": bundle_code}
        if order_reference:
            payload["order_reference"] = order_reference
        return self._make_request("POST", self._reserve_profile_url, json=payload)

    def complete_order(self, order_reference: str):
        payload = {"order_reference": order_reference}
        return self._make_request("POST", self._complete_transaction_url, json=payload)

    def cancel_transaction(self, order_reference: str):
        payload = {"order_reference": order_reference}
        return self._make_request("POST", self._cancel_transaction_url, json=payload)


class FlexiroamAPI(metaclass=VendorMetaClass):
    """
    Helper class to integrate with reseller service APIs
    """

    retry = False
    api_key: str
    country_cache = {}  # Cache to store MCC -> (ISO3 code, country name) mapping

    def __init__(self) -> None:
        self._base_url = "https://solutions-api.flexiroam.com/v1/public-v2"

        self.country_cache = (
            {}
        )  # Cache to store MCC -> (ISO3 code, country name) mapping

        if not (
            instance_config
            and isinstance(instance_config, types.ModuleType)
            and getattr(instance_config, "flexi_api_key")
        ):
            raise ValueError(
                "Required module instance_config: module containing flexi_api_key"
            )
        self._api_key = (
            instance_config.flexi_api_key
        )  # getting from environment variable
        self._base_url = instance_config.flexiroam2_url

    def _get_headers(
        self,
    ):
        headers = {"x-api-key": self._api_key, "Content-Type": "application/json"}

        return headers

    def _make_request(self, method, url, **kwargs):
        try:
            response = requests.request(
                method, url, headers=self._get_headers(), timeout=self.timeout, **kwargs
            )
            response.raise_for_status()
            try:

                return response.json()
            except json.JSONDecodeError:

                # If JSON parsing fails, return raw text response
                return response.text
        except requests.exceptions.RequestException as e:

            logging.error("Request to %s failed: %s", url, str(e))

            # Extract error details from the response if available
            error_message = "Unknown error"
            try:
                if e.response is not None:

                    error_message = e.response.text  # Raw error message from response
                    try:
                        # Attempt to parse error message as JSON
                        response_data = json.loads(error_message)
                        error_message = response_data.get("error", "Unknown error")
                        if "buy-plan" in url or "refund-plan" in url:
                            return response_data
                    except json.JSONDecodeError:

                        pass  # Use raw error message if JSON parsing fails
            except Exception as ex:
                logging.error("Error extracting error details: %s", str(ex))

                raise ValueError(
                    f"Request to FlexiroamAPI API failed: {error_message}"
                ) from e

    def buy_plan(self, plan_code, use_free_esim):
        url = f"{self._base_url}/buy-plan"

        data = {"plan_code": plan_code, "use_free_esim": use_free_esim}
        try:
            response = self._make_request("POST", url, json=data)
        except ValueError as e:
            # Extract error details from the exception
            error_message = str(e)
            # Check if the error_message contains JSON data
            try:
                response_data = json.loads(error_message)
                error_detail = response_data.get("error", "Unknown error")

            except (json.JSONDecodeError, TypeError):
                # If error_message is not JSON, use the raw message
                error_detail = error_message
                return {"error": error_detail}

        return response

    def buy_topup(self, plan_code, sku):
        url = f"{self._base_url}/buy-plan"
        data = {"plan_code": plan_code, "sku": sku}
        return self._make_request("POST", url, json=data)

    def list_inventory(
        self,
        chip_type="esim",
        search_by="availability",
        search="free",
        age_key=None,
        page_key=None,
        all_profiles=None,
    ):
        if all_profiles is None:
            all_profiles = []

        url = self._prepare_url(f"{self._base_url}/list-inventory", page_key)
        params = {"chip_type": chip_type, "search_by": search_by, "search": search}
        response = self._make_request("GET", url, params=params)
        profiles = response.get("items", [])

        all_profiles.extend(profiles)

        next_page_key = response.get("next_page_key")

        if next_page_key:
            return self.list_inventory(
                chip_type="esim",
                search_by="availability",
                search="free",
                age_key=None,
                page_key=next_page_key,
                all_profiles=all_profiles,
            )

        return all_profiles

    def get_sim_details(self, sku):
        url = f"{self._base_url}/sim-details"
        params = {"sku": sku}
        return self._make_request("GET", url, params=params)

    def suspend_inventory(self, sku):
        url = f"{self._base_url}/client/inventory/{sku}/suspend"
        return self._make_request("POST", url)

    def activate_inventory(self, sku):
        url = f"{self._base_url}/client/inventory/{sku}/active"
        return self._make_request("POST", url)

    def refund_plan(self, purchased_plan_id, sku):
        url = f"{self._base_url}/refund-plan"
        data = {"purchased_plan_id": purchased_plan_id, "sku": sku}
        return self._make_request("POST", url, json=data)

    def list_plans(self, page_key=None, all_bundles=None):
        if all_bundles is None:
            all_bundles = []

        url = self._prepare_url(page_key)
        response = self._make_request("GET", url)
        plans = response.get("items", [])

        bundles = [self._create_bundle(plan) for plan in plans]

        all_bundles.extend(bundles)
        next_page_key = response.get("next_page_key")

        if next_page_key:
            return self.list_plans(page_key=next_page_key, all_bundles=all_bundles)

        return all_bundles

    def _prepare_url(self, page_key):
        url = f"{self._base_url}/list-plans"
        if page_key is not None:
            url += f"?page_key={page_key}"
        return url

    def _create_bundle(self, plan):
        data_amount, data_unit = self._get_plan_data_details(plan)
        mcc_or_area_code = plan.get("mcc_or_area_code")

        if mcc_or_area_code:
            country_list, iso3_country_codes = self._get_country_details(
                mcc_or_area_code
            )
        else:
            country_list, iso3_country_codes = [], []

        bundle_category = self._get_bundle_category(iso3_country_codes)

        bundle_name, bundle_code = get_bundle_name(
            country_list,
            iso3_country_codes,
            {
                "bundle_category": bundle_category,
                "data_amount": data_amount,
                "bundle_name": plan.get("title"),
                "data_unit": data_unit,
            },
        )

        return self._build_bundle(
            plan,
            data_amount,
            data_unit,
            country_list,
            iso3_country_codes,
            bundle_name,
            bundle_code,
            bundle_category,
        )

    def _get_plan_data_details(self, plan):
        data_amount = (
            plan.get("settings", {}).get("balances", {}).get("main_data").get("plan")
        )
        data_unit = plan.get("settings", {}).get("data_unit_per")
        megabytes_per_giga = plan.get("settings", {}).get("data_unit")
        if data_amount and data_unit == "MB" and data_amount >= megabytes_per_giga:
            data_amount /= megabytes_per_giga
            data_unit = "GB"

        return data_amount, data_unit

    def _get_country_details(self, mcc_or_area_code):
        mcc_codes = mcc_or_area_code.split("_")
        iso3_country_codes = []
        country_list = []

        for mcc in mcc_codes:
            iso3_code, country_name = self._retrieve_country(mcc)

            if iso3_code and country_name:
                iso3_country_codes.append(iso3_code)
                country_list.append(country_name)

        return country_list, iso3_country_codes

    def _retrieve_country(self, mcc):
        if mcc in self.country_cache:
            return self.country_cache[mcc]

        country = Countries.objects(mcc=mcc).first()
        if country:
            iso3_code = country.iso3_code
            country_name = country.country_name
            if iso3_code != "ISR":
                self.country_cache[mcc] = (iso3_code, country_name)
                return iso3_code, country_name
        return None, None

    def _get_bundle_category(self, iso3_country_codes):
        if len(iso3_country_codes) > 1:
            return "global"
        return "country"

    def _build_bundle(
        self,
        plan,
        data_amount,
        data_unit,
        country_list,
        iso3_country_codes,
        bundle_name,
        bundle_code,
        bundle_category,
    ):
        bundle = {
            "bundle_duration": plan.get("settings", {}).get("validity_in_days"),
            "data_amount": data_amount,
            "data_unit": data_unit,
            "country_list": country_list,
            "country_code_list": iso3_country_codes,
            "bundle_vendor_code": plan.get("plan_code"),
            "price": plan.get("price", 0),
            "bundle_name": bundle_name,
            "bundle_code": bundle_code,
            "profile_names": plan.get("imsi_profile", ""),
            "bundle_category": bundle_category,
        }

        return bundle

class ChangeBillingStatusOperation(Enum):
    CUSTOMER_INVENTORY = "CUSTOMER-INVENTORY"
    IN_TESTING = "IN-TESTING"
    IN_BILLING = "IN-BILLING"
    READY_FOR_BILLING = "READY-FOR-BILLING"
    SUSPENDED = "SUSPENDED"
    RETIRED = "RETIRED"
    def is_valid_operation(action: str) -> bool:
        try:
            ChangeBillingStatusOperation(action)
            return True
        except ValueError:
            return False

class ChangePricePlanOperation(Enum):
    BC_ONLY = "BC-ONLY"
    PERMANENT = "PERMANENT"
    def is_valid_operation(action: str) -> bool:
        try:
            ChangePricePlanOperation(action)
            return True
        except ValueError:
            return False

class TelkomselAccessCred:
    def __init__(self, access_token="", expire_at=None):
        self.access_token = access_token
        self.expire_at = expire_at

class TelkomselVendor(metaclass=VendorMetaClass):
    """
        class for API calls to Telkomsel services, import and use in your controllers
    """
    vendor_name: str = "TELKOMSEL"
    headers = {"Content-Type": "application/vnd.api+json", "Authorization" : "Bearer {token}"}
    access_cred: TelkomselAccessCred = None

    def __init__(self):
        vendor = get_vendor_info(self.vendor_name)
        if not (instance_config and isinstance(instance_config, types.ModuleType) and getattr(instance_config, "telkomsel_base_url") and vendor):
            logging.error("Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor")
            raise ValueError("Required module instance_config: module containing TELKOMSEL_BASE_URL and object vendor")

        self.vendor = vendor
        self.instance_config = instance_config
        self.telkomsel_authentication_url = self.instance_config.telkomsel_authentication_url
        self.telkomsel_find_sim_card_url = self.instance_config.telkomsel_base_url + '/simCard/basic'
        self.telkomsel_change_status_sim_card_url = self.instance_config.telkomsel_base_url + '/simCard/{SIM_ID}/billingStatus'
        self.telkomsel_available_price_plan_sim_card_url = self.instance_config.telkomsel_base_url + '/simCard/{SIM_ID}/availableTariff'
        self.telkomsel_fetch_tariff_url = self.instance_config.telkomsel_base_url + '/tariff'
        self.telkomsel_change_price_plan_sim_card_url = self.instance_config.telkomsel_base_url + '/simCard/{SIM_ID}/changePriceplan'
        self.telkomsel_data_usage_daily_url = self.instance_config.telkomsel_base_url + '/dataUsageDaily'
        self.telkomsel_sim_card_usage_url = self.instance_config.telkomsel_base_url + '/simCard/msisdn/{MSISDN}/ctdUsage'
        self.username = self.instance_config.telkomsel_username
        self.password = self.instance_config.telkomsel_password
        self.client_id = self.instance_config.telkomsel_client_id
        self.client_secret = self.instance_config.telkomsel_client_secret

        self.es2plus_url = self.instance_config.es2plus_url
        self.es2plus_fri = self.instance_config.es2plus_fri
        self.es2plus_fci = self.instance_config.es2plus_fci
        self.download_profile_url = self.es2plus_url + '/gsma/rsp2/es2plus/downloadOrder'
        self.get_profile_detail_url = self.es2plus_url + '/gsma/rsp2/es2plus/confirmOrder'

    def login(self):
        """
        Handles authentication for the Telkomsel API by ensuring a valid access token is set.

        If the current access token is missing or expired, attempts to re-authenticate
        using the `login_api()` method. On success, updates the request headers with
        a valid `Authorization` token.

        Logs a warning if the token is missing or expired. Logs and handles any unexpected
        exceptions during the login process.

        Returns:
            None or result from `exception_handler.handle_exception` if an error occurs.
        """
        try:
            if not self.access_cred or datetime.datetime.utcnow() > self.access_cred.expire_at:
                logging.warning("Telkomsel access token expired or not available.")
                self.login_api()
                return
            self.headers = self.headers.copy()
            self.headers.update({"Authorization": f"Bearer {self.access_cred.access_token}"})
        except Exception as e:
            logging.error("Error on login, %s", str(e))
            return exception_handler.handle_exception(e)

    def login_api(self) -> (object, str):
        """
        Authenticates with the Telkomsel API and retrieves an access token.

        If a valid (non-expired) access token already exists in `self.access_cred`,
        the method exits early without making an API request.

        Otherwise, it sends a POST request to the Telkomsel authentication endpoint
        using client and user credentials. On successful authentication, it updates
        the authorization headers and stores the new access token with its adjusted
        expiry time (buffered by 120 seconds).

        Logs request and response details for debugging purposes.

        Returns:
            tuple: A tuple containing the full response JSON (dict) and a success message string.

        Raises:
            ValueError: If the authentication request fails or an exception occurs during the process.
        """
        try:
            if self.access_cred and self.access_cred.expire_at > datetime.datetime.utcnow():
                return

            url = self.telkomsel_authentication_url
            headers = {"Content-Type": "application/x-www-form-urlencoded"}
            payload = {
                "grant_type": "password",
                "username": self.username,
                "password": self.password,
                "client_id": self.client_id,
                "client_secret": self.client_secret
            }
            payload_encoded = urllib.parse.urlencode(payload)

            logger.info("Sending login request with url - %s and payload - %s", url, payload_encoded)
            response = requests.post(url=url, headers=headers, data=payload_encoded, timeout=self.timeout)
            logger.info("Receiving login response - Status Code: %s, Response Body: %s", response.status_code,
                        response.text)

            if response.status_code != 200:
                raise ValueError(response.json()["error_description"])

            access_token = response.json()['access_token']
            expires_in = response.json()['expires_in']
            self.headers = self.headers.copy()
            self.headers.update({"Authorization": f"Bearer {access_token}"})
            self.access_cred = TelkomselAccessCred(
                access_token=access_token,
                expire_at=datetime.datetime.utcnow() + datetime.timedelta(seconds=expires_in - 120),
            )
            return response.json(), "Telkomsel Account Logged In Successfully"
        except Exception as e:
            logging.error("Error on login_api, %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def get_profiles(self, msisdn: str = None) -> (object, str):
        """
        Retrieves SIM card profile(s) from the Telkomsel API.

        Authenticates using the `login()` method before making a GET request to the
        Telkomsel SIM card lookup endpoint. If `msisdn` is provided, it filters the
        results to return data only for the specified MSISDN.

        Logs both the request and response for visibility and debugging.

        Args:
            msisdn (str, optional): The mobile subscriber number to filter the results by.

        Returns:
            tuple: A tuple containing the response JSON (dict) and a success message (str).

        Raises:
            ValueError: If the request fails or an exception occurs during the process.
        """
        try:
            logging.info(f"Receiving request find sim card - Msisdn {msisdn if msisdn else 'Not Provided'}")
            self.login()
            url = self.telkomsel_find_sim_card_url
            if msisdn is not None:
                url = url + "?filter=(EQ msisdn " + msisdn + ")"

            logging.info("Sending Find Sim Card request with url - %s", url)
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            logging.info("Receiving Find Sim Card response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 200:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Getting Sim Card {msisdn if msisdn else ''} Successfully"
        except Exception as e:
            logging.error("Error on get_profiles, %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def update_profile_status(self, sim_id, billing_status) -> (object, str):
        """
        Updates the billing status of a SIM card profile via the Telkomsel API.

        Validates the `sim_id` and `billing_status`, then sends a PATCH request to
        update the SIM card's billing status. Logs the request and response details
        throughout the process.

        Args:
            sim_id (str): The unique identifier of the SIM card whose status is to be updated.
            billing_status (str): The new billing status to apply. Must be valid as per
                               `ChangeBillingStatusOperation.is_valid_operation()`.

        Returns:
            tuple: A tuple containing the response JSON (dict) and a success message (str).

        Raises:
            ValueError: If `sim_id` is `None`, `billing_status` is invalid, or if the API call fails.
        """
        try:
            if sim_id is None:
                raise ValueError("SimId cannot be None")
            if not ChangeBillingStatusOperation.is_valid_operation(billing_status):
                raise ValueError("Billing Status Not Found")
            logging.info("Receiving request change status sim card - SimId %s, BillingStatus %s", sim_id, billing_status)

            self.login()
            url = self.telkomsel_change_status_sim_card_url
            url = url.replace("{SIM_ID}", sim_id)
            payload = {
                    "data": {
                        "type": "ChangeBillingStatusOperation",
                        "attributes": {
                            "status": billing_status
                        },
                        "relationships": {
                            "instance": {
                                "data": {
                                    "type": "SimCard",
                                    "id": sim_id
                                }
                            }
                        }
                    }
                }

            logging.info("Sending Change Status Sim Card request with url - %s and payload %s", url, json.dumps(payload))
            response = requests.patch(url, headers=self.headers, data=json.dumps(payload), timeout=self.timeout)
            logging.info("Receiving Change Status Sim Card response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 202:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Change Status Sim Card {sim_id} Successfully"
        except Exception as e:
            logging.error("Error on update_profile_status, %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def get_bundles(self, sim_id: str = None) -> (object, str):
        """
        Retrieves available price plans (tariffs) for a given SIM card or fetches general tariffs.

        If `sim_id` is provided, fetches available price plans specific to that SIM card.
        Otherwise, retrieves general tariff information. Authenticates with the Telkomsel API
        before making the request.

        Logs all request and response details for traceability.

        Args:
            sim_id (str, optional): The ID of the SIM card for which to fetch available bundles.
                                    If not provided, general tariffs will be returned.

        Returns:
            tuple: A tuple containing the response JSON (dict) and a success message (str).

        Raises:
            ValueError: If the API request fails or an exception occurs during the process.
        """
        try:
            logging.info(f"Receiving request available price plan sim card - SimId {sim_id if sim_id else 'Not Provided'}")

            self.login()
            if sim_id is not None:
                url = self.telkomsel_available_price_plan_sim_card_url
                url = url.replace("{SIM_ID}", sim_id)
            else:
                url = self.telkomsel_fetch_tariff_url

            logging.info("Sending Get Tariffs  with url - %s", url)
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            logging.info("Receiving Get Tariffs response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 200:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Getting Get Tariffs Successfully"
        except Exception as e:
            logging.error("Error on get_bundles, %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def load_plan(self, sim_id, bundle_id, execution_type) -> (object, str):
        """
        Changes the price plan of a SIM card via the Telkomsel API.

        Sends a POST request to update the price plan for a given SIM card using the specified
        execution type. Validates the input parameters before making the request.

        Args:
            sim_id (str): The ID of the SIM card to be updated.
            bundle_id (str): The ID of the new price plan to assign.
            execution_type (str): The type of execution (e.g., immediate or scheduled). Must be valid
                as per `ChangePricePlanOperation.is_valid_operation()`.

        Returns:
            Tuple[object, str]: A tuple containing the JSON response from the API and a success message string.

        Raises:
            ValueError: If required parameters are missing, the execution type is invalid, or the API returns an error.
        """
        try:
            if not sim_id or not bundle_id:
                raise ValueError("SimId and BundleId cannot be None")
            if not ChangePricePlanOperation.is_valid_operation(execution_type):
                raise ValueError("Execution Type Not Found")

            logging.info("Receiving request change price plan sim card - SimId %s - PricePlanId %s - ExecutionType %s",
                         sim_id, bundle_id, execution_type)
            self.login()
            url = self.telkomsel_change_price_plan_sim_card_url
            url = url.replace("{SIM_ID}", sim_id)

            payload = {
                "data": {
                    "type": "ChangePriceplanOperation",
                    "attributes": {
                        "executionType": execution_type
                    },
                    "relationships": {
                        "instance": {
                            "data": {
                                "type": "SimCard",
                                "id": sim_id
                            }
                        },
                        "priceplan": {
                            "data": {
                                "type": "Priceplan",
                                "id": bundle_id
                            }
                        }
                    }
                }
            }

            logging.info("Sending Change Price Plan request with url: %s - payload: %s", url, json.dumps(payload))
            response = requests.post(url, headers=self.headers, json=json.dumps(payload), timeout=self.timeout)
            logging.info("Receiving Change Price Plan response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 202:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Changing Price Plan Sim Card {sim_id} to {bundle_id} Successfully"
        except Exception as e:
            logging.error("Error on load_plan: %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def get_daily_consumption(self, msisdn) -> (object, str):
        """
        Retrieves daily data usage information for a given MSISDN via the Telkomsel API.

        Sends a GET request to the Telkomsel daily data usage endpoint with a filter applied
        for the specified MSISDN.

        Args:
            msisdn (str): The mobile number (MSISDN) for which to retrieve daily data usage.

        Returns:
            Tuple[object, str]: A tuple containing the JSON response from the API and a success message string.

        Raises:
            ValueError: If `msisdn` is None or the API responds with an error.
        """
        try:
            if msisdn is None:
                raise ValueError("Msisdn cannot be None")

            logging.info("Receiving request data usage daily - Msisdn %s", msisdn)
            self.login()
            url = self.telkomsel_data_usage_daily_url
            url = url + "?filter=(EQ msisdn " + msisdn + ")"

            logging.info("Sending Data Usage Daily request with url - %s", url)
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            logging.info("Receiving Data Usage Daily response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 200:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Getting Data Usage Daily {msisdn} Successfully"
        except Exception as e:
            logging.error("Error on get_daily_consumption, %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def get_profile_history(self, msisdn) -> (object, str):
        """
        Retrieves the usage cycle-to-date for a specified SIM card.

        Validates the provided `msisdn`, then makes an authenticated request to the
        Telkomsel API to fetch the usage data for that SIM card. Logs all request and
        response details.

        Args:
            msisdn (str): The mobile subscriber number (MSISDN) for which to retrieve the usage history.

        Returns:
            tuple: A tuple containing the response JSON (dict) and a success message (str).

        Raises:
            ValueError: If `msisdn` is `None` or if the API request fails or returns an error.
        """
        try:
            if msisdn is None:
                raise ValueError("Msisdn cannot be None")
            logging.info("Receiving sim card usage cycle to date request - Msisdn %s", msisdn)

            self.login()
            url = self.telkomsel_sim_card_usage_url
            url = url.replace("{MSISDN}", msisdn)

            logging.info("Sending SIM Card Usage Cycle-to-Date request with url: %s", url)
            response = requests.get(url, headers=self.headers, timeout=self.timeout)
            logging.info("Receiving SIM Card Usage Cycle-to-Date response - Status Code %s, Response Body: %s", response.status_code,
                         response.text)

            if response.status_code != 200:
                raise ValueError(response.json()["errors"][0]["detail"])

            return response.json(), f"Getting Sim Card {msisdn} Usage Cycle To Date Successfully"
        except Exception as e:
            logging.error("Error on get_profile_history: %s", str(e))
            raise ValueError(exception_handler.handle_exception(e))

    def download_from_rsp(self, iccid: str):
        "Download profile from RSP in order to call confirm order(to get activation code)"
        headers = {"Content-Type": "application/json"}
        payload = {
            "header": {
                "functionRequesterIdentifier": self.es2plus_fri,
                "functionCallIdentifier": self.es2plus_fci,
            },
            "iccid": iccid,
        }
        try:
            response = requests.post(
                url=self.download_profile_url,
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as req_err:
            logger.error(
                "Telkomsel es2+ download_from_rsp request failed: %s",
                str(req_err),
            )
            return None
        except Exception as exception:
            logger.exception(
                "Telkomsel es2+ download_from_rsp unexpected error occurred,: %s",
                str(exception),
            )
            return None

    def get_profile_activation_code(self, iccid: str, releaseFlag: bool = True):
        "Get profile activation code from ES2+"
        headers = {"Content-Type": "application/json"}
        payload = {
            "header": {
                "functionRequesterIdentifier": self.es2plus_fri,
                "functionCallIdentifier": self.es2plus_fci,
            },
            "iccid": iccid,
            "releaseFlag": releaseFlag,
        }
        try:
            response = requests.post(
                url=self.get_profile_detail_url,
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as req_err:
            logger.error(
                "Telkomsel es2plaus get_profile_activation_code request failed: %s",
                req_err,
            )
            return None
        except Exception as exception:
            logger.exception(
                "Telkomsel es2plaus get_profile_activation_code unexpected error occurred,: %s",
                exception,
            )
            return None
# indosat = Indosat()
# last_page = False
# profiles = []
# iccids_list = []
# all_terminals = []
# batch_size = 50
# page = 1
#
# # Getting all Profiles Available
# while page < 200 and not last_page:
#     response = indosat.get_profiles(
#         pageSize=50,
#         pageNumber=page,
#         status="TEST_READY",
#     )
#     last_page = response.get("lastPage", False)
#     page += 1
#     profiles.extend(response.get("devices", []))

import csv
import logging
import threading
import concurrent.futures
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


def fetch_batch(indosat: Indosat, page_number: int, page_size: int, status: str) -> Dict[str, Any]:
    """Fetch a single batch of profiles."""
    logger.info(f"Fetching page {page_number} with size {page_size}")
    return indosat.get_profiles(pageSize=page_size, pageNumber=page_number, status=status)


def fetch_profiles_concurrent(max_pages: int = 100, batch_size: int = 50,
                              status: str = "TEST_READY", output_file: str = "indosat_profiles.csv",
                              max_workers: int = 5) -> List[Dict[str, Any]]:
    """
    Fetch Indosat profiles in batches using concurrent programming and save to CSV.

    Args:
        max_pages: Maximum number of pages to fetch
        batch_size: Number of records per page
        status: Filter profiles by status
        output_file: Path to save the CSV output
        max_workers: Maximum number of concurrent workers

    Returns:
        List of profile dictionaries
    """
    logger.info("Initiating Indosat Save Profiles with concurrency")
    indosat = Indosat()
    all_profiles = []

    # First fetch to get total pages info
    first_response = indosat.get_profiles(pageSize=batch_size, pageNumber=1, status=status)
    all_profiles.extend(first_response.get("devices", []))

    total_count = first_response.get("totalCount", 0)
    total_pages = min(max_pages, (total_count + batch_size - 1) // batch_size)

    logger.info(f"Total profiles: {total_count}, Total pages: {total_pages}")

    # Skip page 1 as we already fetched it
    pages_to_fetch = list(range(2, total_pages + 1))

    # Use ThreadPoolExecutor for concurrent fetching
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_page = {
            executor.submit(fetch_batch, indosat, page, batch_size, status): page
            for page in pages_to_fetch
        }

        # Process completed tasks
        for future in concurrent.futures.as_completed(future_to_page):
            page = future_to_page[future]
            try:
                response = future.result()
                batch_profiles = response.get("devices", [])
                logger.info(f"Received {len(batch_profiles)} profiles from page {page}")
                all_profiles.extend(batch_profiles)
            except Exception as e:
                logger.error(f"Error fetching page {page}: {str(e)}")

    # Save profiles to CSV
    save_profiles_to_csv(all_profiles, output_file)

    return all_profiles


def save_profiles_to_csv(profiles: List[Dict[str, Any]], output_file: str) -> None:
    """Save profiles to a CSV file."""
    if not profiles:
        logger.warning("No profiles to save")
        return

    # Get fieldnames from the first profile
    fieldnames = list(profiles[0].keys())

    logger.info(f"Saving {len(profiles)} profiles to {output_file}")

    try:
        with open(output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(profiles)
        logger.info(f"Successfully saved profiles to {output_file}")
    except Exception as e:
        logger.error(f"Error saving profiles to CSV: {str(e)}")


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Example usage
    profiles = fetch_profiles_concurrent(
        max_pages=250,
        batch_size=50,
        status="TEST_READY",
        output_file="indosat_profiles.csv",
        max_workers=5
    )

    logger.info(f"Total profiles fetched: {len(profiles)}")
import datetime
import logging

from app_models import consumer_models as cm
from b2c_helpers.support_helper import send_custom_monitor_email, deactivate_vendor
from b2c_helpers.vendors import ESIMGo


def enough_vendor_balance(bundle_info: cm.Bundles = None, vendor_name: str = None):
    """
        checks vendor balance if vendor.has_balance is enabled.
        for now only works for eSIMGo
    """
    if bundle_info:
        vendor_name = bundle_info.vendor_name
    if not vendor_name:
        raise ValueError('params bundle_info or vendor_name must be provided')
    vendor: cm.Vendors = cm.Vendors.objects(vendor_name=vendor_name).first()
    if not vendor.has_balance:
        logging.info("vendor has no balance, ignoring balance check")
        return True

    minimal_balance: int = vendor.minimal_balance
    organisation_balance: int = ESIMGo().get_balance()
    enough_balance: bool = organisation_balance > minimal_balance
    if not enough_balance:

        subject: str = "URGENT---INSUFFICIENT BALANCE WHILE ALLOCATING PROFILES"
        body: str = (f"[{datetime.datetime.utcnow()}] Insufficient balance for {vendor.vendor_name} "
                     f"we wont allocate extra profiles")
        send_custom_monitor_email(subject, body)
    return enough_balance
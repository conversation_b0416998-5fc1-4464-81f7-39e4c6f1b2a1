import ast
import hashlib
import hmac
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Literal

import requests

from app_helpers import db_helper
from app_helpers.db_helper import logger
from instance import consumer_config

CONSUMPTION_NOTIFICATION = [
    "StartBundle", "limit_80", "limit_100",
    "PLAN-STARTED", "PLAN-EXPIRED", "PLAN-100", "PLAN-80",
    "thing activated", "BundleLowUsage", "BundleDepletion", "BundleValidityExpiryWarning", "BundleValidityExpiry",
    "Plan Started and Selected", "Data Utilization", "Plan Expired",
    "SESSION_START", "CTD_USAGE", "PREPAID_PLAN_COMPLETION", "EXPIRATION","DATA_LIMIT",
    "Expired", "Started", "Eighty"
]

def notify_reseller(notification_type: Literal["first_time", "retry"], notification) -> bool:
    """
        Sends a webhook notification to a reseller and updates the notification record accordingly.

        This function:
        - Retrieves the reseller and bundle associated with the notification.
        - Validates the reseller's eligibility (callback URL, active vendor, etc.).
        - Sends the payload with an HMAC signature to the reseller's callback URL.
        - Updates the notification record with request/response metadata and retry tracking.

        :param notification_type (Literal["first_time", "retry"]): Type of notification being sent.
                "first_time" indicates this is the initial attempt, "retry" for subsequent retries.
        :param notification: The NotificationHistory object representing the notification to be sent.
    """
    try:
        logger.info("Trying to notify reseller %s with new updated_fields %s on bundle_code %s",
                    notification.reseller_id, notification.request_payload, notification.bundle_code)
        timeout = int(consumer_config.http_connection_timeout)
        # get reseller by id
        reseller_id = notification.reseller_id
        reseller = db_helper.get_reseller_by_id(reseller_id)
        if not reseller:
            logger.warning("no reseller found with id %s to be notified", reseller_id)
            notification.delete()
            return False
        if not reseller.callback_url and notification.notified_on in ("update_bundle", "new_bundle") :
            logger.warning("no reseller callback url found with id %s to be notified", reseller_id)
            return False
        if not reseller.consumption_url and notification.notified_on in CONSUMPTION_NOTIFICATION:
            logger.warning("no reseller consumption url found with id %s to be notified", reseller_id)
            return False

        url = reseller.consumption_url
        if notification.notified_on in ("update_bundle", "new_bundle"):
            url = reseller.callback_url

        # retrieve the bundle to be notified
        if notification.bundle_code:
            bundle = db_helper.get_bundle_by_bundle_code(notification.bundle_code)
            # check if bundle exist or no
            if not bundle:
                logger.warning("no bundle found with code %s", notification.bundle_code)
                notification.delete()
                return False
            # check if bundle of vendor is active or no for this reseller, if no then delete the notification
            if bundle.vendor_name not in reseller.active_vendors_list:
                logger.warning("bundle with code %s not active for reseller %s", notification.bundle_code, reseller_id)
                notification.delete()
                logger.warning(
                    "notification with bundle code %s for reseller id %s is removed due inactive vendor existence",
                    notification.bundle_code, reseller_id)
                return False

        headers = {"Content-Type": "application/json"}
        # get the header of reseller so we can replace the specific header key with their values
        reseller_headers = reseller.headers
        if reseller_headers:
            if reseller.api_key:
                reseller_headers = reseller_headers.replace("{{apiKey}}", reseller.api_key)
            if reseller.tenant:
                reseller_headers = reseller_headers.replace("{{tenantKey}}", reseller.tenant)

            # Ensure proper quotes for JSON parsing
            try:
                reseller_headers_dict = json.loads(reseller_headers)
                logger.info("Header of reseller %s is %s", reseller_id, reseller_headers_dict)
            except json.JSONDecodeError as e:
                logger.error("error occurred while getting header for reseller %s, %s", reseller_id, str(e))
                raise ValueError(f"Invalid JSON in reseller headers: {reseller_headers}") from e

            headers.update(reseller_headers_dict)

        payload = json.loads(notification.request_payload)
        # hashing payload with secret key retrieved from reseller
        payload_hashed = generate_signature(payload, reseller.secret_key)
        payload["signature"] = payload_hashed

        request_sent_time = datetime.utcnow()
        logger.info("Sending notification to reseller %s with payload %s", reseller_id, payload)
        reseller_webhook_response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        logger.info("Receiving notification response from reseller %s: %s %s", reseller_id, reseller_webhook_response.status_code,
                    reseller_webhook_response.text)

        # saving history for logging purpose
        retry_on_failed_at = None
        response_success_time = None
        # if response status code is not 200 then retry the same notification after X minutes
        if reseller_webhook_response.status_code != 200:
            retry_on_failed_at = datetime.utcnow() + timedelta(minutes=reseller.retry_on_failed_after)
        else:
            response_success_time = datetime.utcnow()

        update_fields = {
            "set__request_sent_time": request_sent_time,
            "set__response_received_time": datetime.utcnow(),
            "set__status": reseller_webhook_response.status_code == 200,
            "set__retry_on_failed_at": retry_on_failed_at,
            "set__response_success_time": response_success_time,
            "set__updated_at": datetime.utcnow()
        }

        if notification_type == "first_time":
            update_fields.update({
                "set__response_payload": reseller_webhook_response.text,
                "set__retry_number": 0
            })
        else:
            current_retry_number = notification.retry_number or 0
            next_retry_number = current_retry_number + 1

            update_fields.update({
                "set__retry_number": next_retry_number,
                "set__response_payload_retry": reseller_webhook_response.text
            })

        notification.update(**update_fields)
        logger.info("Updating notification %s for reseller %s", notification, reseller_id)

        return reseller_webhook_response.status_code == 200
    except ConnectionError as e:
        logger.error("Connection error occurred on notify_reseller : %s", str(e))
        return False
    except Exception as e:
        logger.error("Error occurred on notify_reseller : %s", str(e))
        return False

def generate_signature(payload: Dict[str, Any], secret_key: str) -> str:
    """
        Generate an HMAC-SHA256 signature for a given payload using the provided secret key.

        :param payload: The data to sign. Keys will be sorted to ensure consistent output.
        :param secret_key: The shared secret key used to generate the HMAC.
    """
    payload_str = json.dumps(payload, sort_keys=True)  # Ensure sorted keys for consistency
    signature = hmac.new(secret_key.encode(), payload_str.encode(), hashlib.sha256).hexdigest()
    return signature

import datetime
import logging
import mongoengine
import requests
from app_models import consumer_models as cm
from app_helpers import db_helper
from app_helpers.db_helper import add_bundle, add_to_update_bundle_version

VODAFONE_VENDOR = "Vodafone"

logger = logging.getLogger(__name__)


def get_iso3_code_by_name(country_name_dic, country):
    for country_code, country_name in country_name_dic.items():
        if country == country_name:
            return country_code
    return None  # Return None if the value is not found


def get_vodafone_bundles(vodafone_url, vodafone_token):
    print("----------------------GETTING VODAFONE BUNDLES------------------")
    headers = {"Authorization": f"Bearer {vodafone_token}"}
    url = "{}/network/account/products".format(vodafone_url)
    bundle_details = db_helper.get_bundle_details(
        ["plan_type", "activity_policy", "available_netwok", "top_up_plan", "ekyc"]
    )
    vendor = cm.Vendors.objects(vendor_name=VODAFONE_VENDOR).first()
    if not vendor:
        logger.warning("No vendor found with vendor name %s ... hence skipping save_bundles", VODAFONE_VENDOR)
        return
    bundle_detail = {}
    new_bundle_list = []

    for detail in bundle_details:
        bundle_detail[detail["category"]] = detail["detail_msg"]
        bundle_detail[detail["category"] + "_code"] = detail["detail_code"]

    country_code_dic = {}
    country_name_dic = {}
    response = requests.request("GET", url, headers=headers)
    bundles_resp: list = response.json()
    if not bundles_resp or not isinstance(bundles_resp, list):
        return
    for group in bundles_resp:
        group_id = group.get("id", None)
        vendor_bundles = group.get("bundles", [])
        current_zone = group.get("roaming control profiles", [{}])[0].get("id", None)
        for vendor_bundle in vendor_bundles:

            bundle: cm.Bundles = cm.Bundles.objects(
                vendor_name=VODAFONE_VENDOR,
                bundle_vendor_code=vendor_bundle.get("id", None),
            ).first()
            refill_group = str(group_id) + str(vendor.vendor_code)
            refill_group = refill_group.replace(" ", "")

            found = True
            if not bundle:
                found = False
                missing_country_code = []
            else:
                missing_country_code = bundle.missing_country_code

            country_list = []
            country_code_list = []
            covered_areas: mongoengine.QuerySet = db_helper.get_convered_area_from_current_zone(
                current_zone
            )
            if not covered_areas:
                raise ValueError(
                    f"Must add areas in collection for zone {current_zone} before proceeding"
                )
            for area in covered_areas:
                country_name = area.country_name
                (
                    country_list,
                    country_code_list,
                    country_name_dic,
                    country_code_dic,
                ) = update_country_per_name(
                    country_name,
                    country_list,
                    country_code_list,
                    country_name_dic,
                    country_code_dic,
                    missing_country_code,
                )

            if found:
                bundle.update(
                    country_list=country_list,
                    country_code_list=country_code_list,
                    group_id=group_id,
                    refill_group=refill_group,
                    supplier_vendor=str(vendor.vendor_code),
                )
                continue

            rate_revenue: float = float(vendor.rate_revenue) or 25.0
            retail_price: float = 0
            data_amount = round(int(vendor_bundle["value"]) / 1024)

            bundle_category, region_code, region_name = get_bc_rn_rc_by_covered_areas(
                covered_areas
            )

            new_bundle = {
                "vendor_name": VODAFONE_VENDOR,
                "bundle_code": datetime.datetime.now().strftime("%m%d%Y%H%M%S")
                + "_"
                + vendor_bundle["id"],
                "bundle_name": f"{region_name} {data_amount}GB {vendor_bundle['validity period']} days",
                "bundle_marketing_name": "The World is Yours!",
                "bundle_category": bundle_category,
                "category_name": bundle_category,
                "region_code": region_code,
                "region_name": region_name,
                "bundle_vendor_code": vendor_bundle["id"],
                "bundle_vendor_name": vendor_bundle["name"],
                "bundle_duration": str(vendor_bundle["validity period"]),
                "unit_price": 0,
                "rate_revenue": rate_revenue,
                "retail_price": round(float(retail_price), 2),
                "data_amount": data_amount,
                "fullspeed_data_amount": data_amount,
                "data_unit": "GB",
                "validity_amount": str(vendor_bundle["validity period"]),
                "allocated_unit": 1,
                "is_region": True,
                "is_active": False,
                "deleted": True,
                "country_list": country_list,
                "country_code_list": country_code_list,
                "plan_type_code": bundle_detail["plan_type_code"],
                "plan_type": bundle_detail["plan_type"],
                "activity_policy": bundle_detail["activity_policy"],
                "activity_policy_code": bundle_detail["activity_policy_code"],
                "available_netwok_code": bundle_detail["available_netwok_code"],
                "available_netwok": bundle_detail["available_netwok"],
                "top_up_plan_code": bundle_detail["top_up_plan_code"],
                "top_up_plan": bundle_detail["top_up_plan"],
                "ekyc_code": bundle_detail["ekyc_code"],
                "ekyc": bundle_detail["ekyc"],
                "group_id": group_id,
            }
            add_bundle(new_bundle)
            new_bundle_list.append(new_bundle["bundle_code"])

        add_to_update_bundle_version(
            [f"Save Vodafone Bundles (New One): {new_bundle_list}"]
        )


def get_bc_rn_rc_by_covered_areas(
    covered_areas: mongoengine.QuerySet,
) -> [str, str, str]:
    """
        returns bundle category, region code and region name based on the covered areas of this zone
        :param: covered_areas: mongoengine.QuerySet
        :return: bundle category, region code, region name
    """
    if not isinstance(covered_areas.first(), cm.CoveredAreas):
        raise ValueError("Must provide queryset for covered_areas")
    if covered_areas.count() == 1:
        return "country", "", ""
    region_codes = covered_areas.distinct("region_code")
    region_names = covered_areas.distinct("region_name")
    if len(region_codes) == 1 and len(region_names) == 1:
        return "region", region_codes[0], region_names[0]
    return "global", "", ""


def update_country_per_name(
    country_name,
    list_countries,
    list_countries_code,
    country_name_dic,
    country_code_dic,
    missing_country_code=[],
):
    country_code = get_iso3_code_by_name(country_name_dic, country_name)
    found = False
    if country_code is not None:
        if (
            country_name in country_name_dic
            and country_code.iso3_code not in missing_country_code
        ):
            found = True
            list_countries.append(country_name_dic[country_code.iso3_code.strip()])
            list_countries_code.append(country_code_dic[country_code.iso3_code.strip()])
    if not found:
        country_code = db_helper.get_country_code_from_country_name(country_name)

        if country_code and country_code.iso3_code != "ISR":
            if country_code.iso3_code not in missing_country_code:
                list_countries.append(country_code["country_name"].strip())
                list_countries_code.append(country_code.iso3_code.strip())
            country_name_dic[country_code.iso3_code.strip()] = country_code[
                "country_name"
            ].strip()
            country_code_dic[
                country_code.iso3_code.strip()
            ] = country_code.iso3_code.strip()
    return list_countries, list_countries_code, country_name_dic, country_code_dic

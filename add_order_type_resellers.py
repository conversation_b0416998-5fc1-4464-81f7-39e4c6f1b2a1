#!/usr//venv3.7/bin/python
import sys
from main import app
from app_models import reseller_models

with app.app_context():
    sys.path.append('/')
    count_bundles = 0
    count_topups= 0
    count_no_iccid =0

    pipeline = [
        {
            '$match': {
                'reseller_type': 'reseller',
                'order_status': 'Successful',
                'iccid': {
                    '$exists': True
                }
            }
        },

     {
        '$sort': {
            'date_created': 1
        }
    }, {
        '$group': {
            '_id': {
                'iccid': '$iccid',
                'client_email': '$client_email',
                'client_name': '$client_name'
            },
            'order_ids': {
                '$push': '$_id'
            }
        }
    }, {
        '$addFields': {
            'BuyBundle': {
                '$slice': [
                    '$order_ids', 1
                ]
            },
            'BuyTopup': {
                '$cond': {
                    'if': {
                        '$gt': [
                            {
                                '$size': '$order_ids'
                            }, 1
                        ]
                    },
                    'then': {
                        '$slice': [
                            '$order_ids', 1, {
                                '$subtract': [
                                    {
                                        '$size': '$order_ids'
                                    }, 1
                                ]
                            }
                        ]
                    },
                    'else': []
                }
            }
        }
    }, {
        '$group': {
            '_id': None,
            'AllBuyBundle': {
                '$push': '$BuyBundle'
            },
            'AllBuyTopup': {
                '$push': '$BuyTopup'
            }
        }
    }, {
        '$project': {
            '_id': 0,
            'AllBuyBundle': {
                '$reduce': {
                    'input': '$AllBuyBundle',
                    'initialValue': [],
                    'in': {
                        '$concatArrays': [
                            '$$value', '$$this'
                        ]
                    }
                }
            },
            'AllBuyTopup': {
                '$reduce': {
                    'input': '$AllBuyTopup',
                    'initialValue': [],
                    'in': {
                        '$concatArrays': [
                            '$$value', '$$this'
                        ]
                    }
                }
            }
        }
    }
]
    result = list(reseller_models.Order_history.objects(order_type_added__ne=True).aggregate(pipeline))
    # Process the results
    if result:
        doc = result[0]
        buy_bundle_ids = doc['AllBuyBundle']
        buy_topup_ids = doc['AllBuyTopup']

        for order_id in buy_bundle_ids:
            reseller_models.Order_history.objects(id=order_id).update(set__order_type='BuyBundle',set__order_type_added=True)
            count_bundles= count_bundles + 1

        for order_id_ in buy_topup_ids:
            reseller_models.Order_history.objects(id=order_id_).update(set__order_type='BuyTopup',set__order_type_added=True)
            count_topups= count_topups + 1

        print("Total records updated for Successful bundle  orders where iccid exists:",count_bundles)
        print("Total records updated for Successful topup orders where iccid exists:",count_topups)

        reseller_models.Order_history.objects(reseller_type="reseller", iccid__exists=False).update(set__order_type='BuyBundle',set__order_type_added=True)


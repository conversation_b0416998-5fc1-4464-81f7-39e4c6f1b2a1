import json
import os
import logging
from logging import config

log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
log_file_path = os.path.join(log_dir, "app.log")

print(f"log_file_path : {log_file_path}")

if not os.path.exists(log_dir):
    os.makedirs(log_dir)

if not os.path.exists(log_file_path):
    open(log_file_path, 'w').close()

# Get log level from environment variable, default to INFO
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
if log_level not in valid_levels:
    print(f"Invalid log level: {log_level}, defaulting to INFO")
    log_level = "INFO"

print(f"Using log level: {log_level}")

try:
    logging_config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logging_config.json')
    if os.path.exists(logging_config_path):
        print(f"Loading logging configuration from: {logging_config_path}")
        with open(logging_config_path, "r") as f:
            logging_config = json.load(f)

        # Update log levels in the configuration
        for handler in logging_config.get("handlers", {}).values():
            if "level" in handler:
                handler["level"] = log_level

        if "root" in logging_config and "level" in logging_config["root"]:
            logging_config["root"]["level"] = log_level

        logging.config.dictConfig(logging_config)
    else:
        print(f"{logging_config_path} not found, using default configuration.")
        raise FileNotFoundError

except FileNotFoundError:
    print("Using default logging configuration")
    # Convert string level to logging level
    numeric_level = getattr(logging, log_level)

    logging.basicConfig(
        level=numeric_level,
        datefmt="%Y-%m-%d %H:%M:%S %z",
        format="[%(asctime)s] [%(process)d] [%(levelname)s] <%(name)s>: %(message)s",
        handlers=[
            logging.FileHandler(log_file_path, encoding="utf-8"),
            logging.StreamHandler()
        ]
    )
except Exception as e:
    print(f"Error in logging setup: {e}")
    # Fall back to INFO level in case of errors
    logging.basicConfig(
        level=logging.INFO,
        datefmt="%Y-%m-%d %H:%M:%S %z",
        format="[%(asctime)s] [%(process)d] [%(levelname)s] <%(name)s>: %(message)s",
        handlers=[
            logging.FileHandler(log_file_path, encoding="utf-8"),
            logging.StreamHandler()
        ]
    )
    print("Falling back to INFO log level due to error")

logger = logging.getLogger(__name__)
logger.info("Application started.")
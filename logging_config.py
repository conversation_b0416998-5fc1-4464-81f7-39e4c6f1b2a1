import json
import os
import logging
from logging import config
from typing import Optional


def setup_logging(app_root: Optional[str] = None) -> None:
    """
    Setup logging configuration and lock it to prevent third-party overwrites.

    :param app_root: Application root directory. If None, uses caller's directory.
    """
    # Use caller's directory if no root provided
    if app_root is None:
        app_root = os.path.dirname(os.path.abspath(__file__))

    log_dir: str = os.path.join(app_root, "logs")
    log_file_path: str = os.path.join(log_dir, "app.log")

    print(f"log_file_path : {log_file_path}")

    # Create log directory and file if they don't exist
    os.makedirs(log_dir, exist_ok=True)
    if not os.path.exists(log_file_path):
        open(log_file_path, 'w').close()

    # Try to load custom logging config, fallback to default
    _configure_logging(app_root, log_file_path)

    # Prevent third-party modules from overwriting logging config
    logging.getLogger().manager.disable = logging.CRITICAL + 1

    logging.info("Logging setup completed.")


def _configure_logging(app_root: str, log_file_path: str) -> None:
    """
    Load logging configuration from JSON file or use default.

    :param app_root: Application root directory
    :param log_file_path: Path to the log file
    """
    logging_config_path: str = os.path.join(app_root, 'logging_config.json')

    try:
        if os.path.exists(logging_config_path):
            print(f"Loading logging configuration from: {logging_config_path}")
            with open(logging_config_path, "r") as f:
                logging_config: dict = json.load(f)
                # Replace template path with actual log file path
                logging_config["handlers"]["file"]["filename"] = log_file_path
            logging.config.dictConfig(logging_config)
        else:
            raise FileNotFoundError("Config file not found")

    except (FileNotFoundError, Exception) as e:
        print(f"Using default logging configuration: {e}")
        _setup_default_logging(log_file_path)


def _setup_default_logging(log_file_path: str) -> None:
    """
    Configure default logging with file and console handlers.

    :param log_file_path: Path to the log file
    """
    logging.basicConfig(
        level=logging.INFO,
        datefmt="%Y-%m-%d %H:%M:%S %z",
        format="[%(levelname)s] <%(name)s>: %(message)s",
        handlers=[
            logging.StreamHandler()
        ]
    )
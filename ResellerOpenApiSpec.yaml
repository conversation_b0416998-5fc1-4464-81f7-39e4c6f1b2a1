openapi: 3.0.0
info:
  description: "This is an OpenAPI for the Reseller Microservice"
  version: "0.0"
  title: Reseller API

servers:
  - url: http://0.0.0.0:9005/api/v0

tags:

  - name: Reseller
    description: Reseller Related Operations


  - name: Branch
    description: Branch Related Operations

  - name: Agent
    description: Agent Related Operations

  - name: Bundles
    description: Bundles Related Operations

  - name: Orders
    description: Orders Related Operations

  - name: Role
    description: Role Related Operations

  - name: Token
    description: Token Related Operations

  - name: IssueReport
    description: IssueReport Related Operations

  - name: NetworkList
    description: NetworkList Related Operations
components:

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Access-Token

    InternalApiKeyAuth:
      type: apiKey
      in: header
      name: Internal-Token




  responses:

    '200':
      description: Ok
      headers:
        Cache-Control:
          schema:
            type: string
            default: no-store
            enum:
              - no-store
    '201':
      description: Created
      headers:
        Cache-Control:
          schema:
            type: string
            default: no-store
            enum:
              - no-store

    '400':
      description: Bad Request
      headers:
        Cache-Control:
          schema:
            type: string
            default: no-store
            enum:
              - no-store
    '403':
      description: Forbidden
      headers:
        Cache-Control:
          schema:
            type: string
            default: no-store
            enum:
              - no-store
    '204':
      description: Not Found'
      headers:
        Cache-Control:
          schema:
            type: string
            default: no-store
            enum:
              - no-store
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            properties:
              status:
                type: string
              title:
                type: string
              detail:
                type: string




  schemas:

    ResponseEntity: &ResponseEntity
      type: object
      additionalProperties: false
      properties: &ResponseEntityProperties
        response_code:
          type: string
          format: hex
          minLength: 4
          maxLength: 4
          pattern: '^[0-9a-fA-F]{4}$'
          example: 0001
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success

    ObjectID: &ObjectID
      type: string
      format: hex
      minLength: 24
      maxLength: 24
      example: '507f191e810c19729de860ea'
      pattern: '^[0-9a-fA-F]{24}$'

    Role: &Role
      type: object
      additionalProperties: false
      required:
        - role_id
        - name
      properties:
        role_id:
          <<: *ObjectID
        name:
          type: string
          minLength: 0
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
        description:
          type: string
          minLength: 0
          maxLength: 2000
          example: Has Full Authority Over Reseller
          pattern: "^[a-zA-Z .,']+$"

    DeleteRoleResponse:
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: This is a message
        <<: *ResponseEntityProperties

    CreateRoleRequest:
      type: object
      additionalProperties: false
      required:
        - name
        - description
        - permissions
        - permission_level
        - access_level
      properties:
        name:
          type: string
          minLength: 0
          maxLength: 45
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"

        permission_level:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
        access_level:
          type: string
          enum:
            - basic
            - medium
            - sensitive
          example: basic
        description:
          type: string
          minLength: 0
          maxLength: 2000
          example: Has Full Authority Over Reseller
          pattern: "^[a-zA-Z .,']+$"
        permissions:
          type: array
          uniqueItems: true
          items:
            type: object
            properties:
              api_name:
                type: string
                example: topupBundle
              permission_type:
                type: string
                example: Self

    CreateRoleResponse:
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: This is a message
        role_id:
          <<: *ObjectID
        <<: *ResponseEntityProperties

    GetRoleResponse: &GetRoleResponse
      type: object
      additionalProperties: false
      required:
        - role_id
        - name
        - permissions
        - access_level
      properties:
        role_id:
          <<: *ObjectID
        name:
          type: string
          minLength: 0
          maxLength: 20
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"

        permission_level:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
        access_level:
          type: string
          enum:
            - basic
            - medium
            - sensitive
          example: basic
        description:
          type: string
          minLength: 0
          maxLength: 2000
          example: Has Full Authority Over Reseller
          pattern: "^[a-zA-Z .,']+$"
        permissions:
          type: array
          items:
            type: object
            properties:
              api_name:
                type: string
                example: topupBundle
              permission_type:
                type: string
                example: Self

    GetAllRolesResponse: &GetAllRolesResponse
      type: array
      minItems: 0
      items:
        <<: *GetRoleResponse

    EditRoleRequest: &EditRoleRequest
      type: object
      additionalProperties: false
      properties:

        name:
          type: string
          minLength: 0
          maxLength: 45
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"

        permission_level:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
        access_level:
          type: string
          enum:
            - basic
            - medium
            - sensitive
          example: basic
        description:
          type: string
          minLength: 0
          maxLength: 2000
          example: Has Full Authority Over Reseller
          pattern: "^[a-zA-Z .,']+$"
        permissions:
          type: array
          items:
            type: object
            properties:
              api_name:
                type: string
                example: topupBundle
              permission_type:
                type: string
                example: Self

    EditRoleResponse:
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Role Edited Successfully

        <<: *ResponseEntityProperties

    UpdateAdminPermissionsResponse: &UpdateAdminPermissionsResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Monty Admin Role Updated Successfully.
        role_id:
          <<: *ObjectID

        <<: *ResponseEntityProperties

    LoginRequest: &ValidateCredsRequest
      type: object
      additionalProperties: false
      required:
      - username
      - password
      properties:
        username:
          type: string
          #pattern: '^(?=.{8,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$'
          #example: kay_1
          example: admin
        password:
          writeOnly: true
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})'
          example: $3343JcS24
          maxLength: 30

    ForgotPasswordRequest: &ForgotPasswordRequest
      type: object
      additionalProperties: false
      required:
      - email
      properties:
        email:
          type: string
          pattern: '^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$'
          example: <EMAIL>
        captcha_token:
          type: string
          example: 03Aeyuqwe978qwy7e7wqyeiouy21ey710ewquiey8712

    ForgotPasswordResponse: &ForgotPasswordResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Password Reset Link Sent to email

        <<: *ResponseEntityProperties

    PasswordResetRequest: &PasswordResetRequest
      type: object
      additionalProperties: false
      required:
      - password_reset_identifier
      - password
      properties:
        password_reset_identifier:
          type: string
          pattern: '^\d{25}$'
          example: 1129037324695099832474129
        password:
          writeOnly: true
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
          example: $3343JcS2412345
          maxLength: 30
        password_confirmation:
          writeOnly: true
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
          example: $3343JcS2412345
          maxLength: 30

    PasswordResetResponse: &PasswordResetResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Password Reset Successfully, please login to your account.

        <<: *ResponseEntityProperties

    LoginResponse: &LoginResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Credentials Valid, User Logged In
        access_token:
          type: string
          example: "aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw"
        refresh_token:
          type: string
          example: "eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog"
        agent_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        token_type:
          type: string
          enum:
          - ApiKey
        expires_in:
          type: integer
          minimum: 300
        refresh_expires_in:
          type: integer
          minimum: 1800
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        role_name:
          type: string
          pattern: "^[a-zA-Z ']+$"
          example: ResellerAdmin
        permission_level:
          type: integer
          format: int32
          minimum: 1
          maximum: 3
        reseller_currency_code:
          type: string
          maxLength: 15


        <<: *ResponseEntityProperties

    RefreshTokenResponse:
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Credentials Valid, Access Token Refreshed
        access_token:
          type: string
          example: "aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw"

        refresh_token:
          type: string
          example: "eyJhhsghdjxSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJLdGN5U1BUYndSSzk0UWU4Rlg0VEtKeU1hQ3pMdkRacWxackhuLUNRdjA0In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Jszo9pMjIv9EyGAPDENNgpG0sJnGaVIh4WIsDLnHCvS2AeyRgWQbVXTYyh_SeeoXU4B77v3U4pHSCiS3VprsL79iS4QR_vgspTngBvFsIbKirsxSdY7bQUuU39uJiLXx3eQnWEX4SUf5vH7bH11pGX_0m17lWo5THd_mL8MguSBY2zkkPRsRjFjfwHU-IuMY0qTt14ty6kMYSMdDURlWyA-XmXSUMLVtvU1dm1ig6j2disRdz13kj4wwFkw71IXsPQHXGLWoTdqXPxE-KIxiHZgNV580_IUlZwY-dVRp4ZA2QC3kOOD4ivrOb7VYqikvbihF90eugYv9S2BkVOVdog"


        supports_promo:
          type: boolean

        supports_vouchers:
          type: boolean

        token_type:
          type: string
          enum:
          - ApiKey
        expires_in:
          type: integer
          minimum: 300

        refresh_expires_in:
          type: integer
          minimum: 1800

        role_name:
          type: string
          pattern: "^[a-zA-Z ']+$"
          example: ResellerAdmin

        permission_level:
          type: integer
          format: int32
          minimum: 1
          maximum: 3

        reseller_currency_code:
          type: string
          maxLength: 15

        <<: *ResponseEntityProperties

    LogoutResponse: &LogoutResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Log Out Successful

        <<: *ResponseEntityProperties

    RefreshTokenRequest: &RefreshTokenRequest
      type: object
      required:
        - refresh_token
      additionalProperties: false
      properties:
        refresh_token:
          type: string
          nullable: false
          minLength: 20
          maxLength: 2048
          pattern: "^[A-Za-z0-9-_=.]+$"
          example: "aZlDaRa6Ijp7ImJyYW5jaF9pZCI6IjYyOGUxNWVhNjJhNTAwODU1Y2UwYjMwYSIsInJvbGVfaWQiOiJmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmYiLCJzdWIiOiIzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMiLCJ0ZW5hbnRfaWQiOiI1NTU1NjY2NmZmZmYwMDAwZmZmZjAwMDIifSwiZXhwIjoiNDk0NS0wMy0wMlQxMTo1NTowNiswMjowMCJ9kmqQxGGJzbXrTDMqr16Ojk89dUhf4z5nZWfExW66oSZAWdBcE_7WPbgAQ5xh4vr6hLvvueL0BHfUqv93rqYxBw"

    CheckTokenResponse: &CheckTokenResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Token Valid
        allowed_microservices:
          type: object

        <<: *ResponseEntityProperties

    GetBundlesResponse: &GetBundlesResponse
      type: object
      additionalProperties: false
      properties:
        bundles:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              bundle_tag:
                type: array
                items:
                  type: string
                description: will exist only if value inserted by reseller
              is_active:
                type: boolean
                description: Indicates whether the bundle is currently active and available for purchase.
              is_active_corp:
                type: boolean
              bundle_name:
                type: string
                example: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
              bundle_code:
                type: string
                example: esim_10gb_30days_unitedstates__0905202216214562
                description: A unique identifier for the bundle
              bundle_marketing_name:
                type: string
                example: eSIM, 10GB, 30 Days, United States, Unthrottled
              bundle_category:
                type: string
                description: Classification of the bundle
              country_code:
                type: array
                uniqueItems: true
                items:
                  type: string
                  example: USA
                  minLength: 3
                  maxLength: 3
              country_name:
                type: array
                uniqueItems: true
                items:
                  type: string
                  example: United States
              region_code:
                nullable: true
                type: string
                example: AS
              region_name:
                nullable: true
                type: string
                example: Asia
              currency_code_list:
                type: array
                uniqueItems: true
                items:
                  type: string
                  enum:
                  - USD
              additional_currency_code:
                type: string
                maxLength: 15

                example: SAR
              data_unit:
                type: string
                enum:
                - MB
                - GB
                - TB
                description: The unit of measurement for data.
              gprs_limit:
                type: number
                format: float
                example: 10
                description: The maximum data usage limit.
              subscriber_price:
                type: number
                format: float
                example: 16.7
              subscriber_price_in_additional_currency:
                type: number
                format: float
                example: 16.7
              reseller_retail_price:
                type: number
                format: float
                example: 20.7
                description: The retail price set by the reseller in the primary currency.
              reseller_retail_price_in_additional_currency:
                type: number
                format: float
                example: 20.7
                description: The reseller retail price converted into the `additional_currency_code`.
              validity:
                type: integer
                example: 30
                description: The validity period of the package in days.
              unlimited:
                type: boolean
                description: Indicates whether the package has unlimited data usage.
              refill_group:
                type: string
              support_topup:
                type: boolean
                description: Indicates whether the package supports top-ups (additional data purchases).
              supplier_vendor:
                type: string
                description: The name of the vendor or supplier providing the data package.

        total_bundles_count:
          type: integer
          example: 1000

        <<: *ResponseEntityProperties

    GetCountriesResponse: &GetCountriesResponse
      type: object
      additionalProperties: false
      properties:
        countries:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              iso3_code:
                type: string
                example: FRA
                minLength: 3
                maxLength: 3
              iso2_code:
                type: string
                example: FR
                minLength: 2
                maxLength: 2
              country_name:
                type: string
                example: France



        <<: *ResponseEntityProperties

    GetCurrenciesResponse: &GetCurrenciesResponse
      type: object
      additionalProperties: false
      properties:
        currencies:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              currency_code:
                type: string
                maxLength: 15
                example: SAR
              currency name:
                type: string
                example: Saudi Arabian Rial
              currency_rate:
                type: number
                format: float
                minimum: 0
              is_available:
                type: boolean
              last_modified:
                readOnly: true
                type: string
                format: date

        total_currencies_count:
          type: integer
          minimum: 0


        <<: *ResponseEntityProperties

    GetRegionsResponse: &GetRegionsResponse
      type: object
      additionalProperties: false
      properties:
        regions:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              region_code:
                type: string
                example: FRA
              region_name:
                type: string
                example: France
        <<: *ResponseEntityProperties

    GetBundleConsumptionResponse: &GetBundleConsumptionResponse
      type: object
      additionalProperties: false
      properties:
        status:
          type: boolean
        data_allocated:
          type: number
          format: float
          example: 5000
        data_used:
          type: number
          format: float
          example: 5000
        data_remaining:
          type: number
          format: float
          example: 5000
        data_unit:
          type: string
          example: MB
          enum:
          - MB
          - GB
          - TB
        policy_status:
          type: string
        plan_status:
          type: string
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        cached_at:
          type: string
        profile_status:
          type: string
        unlimited:
          type: boolean
          default: false
        <<: *ResponseEntityProperties

    GetMyBundleConsumptionResponse: &GetMyBundleConsumptionResponse
      type: object
      additionalProperties: false
      properties:
        status:
          type: boolean
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        data_allocated:
          type: number
          format: float
          example: 5000
        iccid:
          type: string
          pattern: '^\d{18,20}$'
        logo_uri:
          type: string
        data_used:
          type: number
          format: float
          example: 5000
        data_remaining:
          type: number
          format: float
          example: 5000
        data_unit:
          type: string
          example: MB
          enum:
          - MB
          - GB
          - TB
        policy_status:
          type: string
        plan_status:
          type: string
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        profile_status:
          type: string
        unlimited:
          type: boolean
          default: false
        cached_at:
          type: string

        <<: *ResponseEntityProperties

    AssignBundleRequest: &AssignBundleRequest
      type: object
      additionalProperties: false
      properties: &AssignBundleProperties
        order_reference:
          type: string
          maxLength: 30
        bundle_code:
          type: string
          example: USA3GB_3102938429
        whatsapp_number:
          type: string
        email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        name:
          type: string
          example: John Wick

    AssignBundleResponse: &AssignBundleResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          example: Bundle Assigned Successfully
        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        remaining_wallet_balance:
          type: number
          format: float
          example: 30
          description: in dollars
        iccid:
          type: string
          pattern: '^\d{18,20}$'
        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 30
          description: in additional currency
        additional_currency_code:
          type: string
          maxLength: 15
          example: SAR

        <<: *ResponseEntityProperties

    ReserveBundleRequest: &ReserveBundleRequest
      type: object
      properties:
        order_reference:
          maxLength: 100
          type: string
        bundle_code:
          type: string
          example: USA3GB_3102938429
        whatsapp_number:
          type: string
        email:
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          type: string
          example: <EMAIL>
        name:
          maxLength: 20
          minLength: 1
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: John Wick
      additionalProperties: false

    ReserveBundleResponse:
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Bundle Reserved Successfully
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 30
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional currency
          format: float
          example: 30
        additional_currency_code:
          maxLength: 15
          type: string
          example: SAR
      additionalProperties: false

    CancelBundleRequest: &CancelBundleRequest
      type: object
      properties:
        order_reference:
          type: string
      additionalProperties: false

    CancelBundleResponse: &CancelBundleResponse
      type: object
      properties:
        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Order Canceled Successfully
      additionalProperties: false
      example:
        response_code: "1"
        Message: Order Canceled Successfully
        developer_message: Operation Succcessful
        title: Success

    CompleteTransactionRequest: &CompleteTransactionRequest
      type: object
      properties:
        order_reference:
          maxLength: 100
          type: string
      additionalProperties: false

    CompleteTransactionResponse: &CompleteTransactionResponse
      type: object
      additionalProperties: false
      properties:

        response_code:
          maxLength: 4
          minLength: 4
          pattern: "^[0-9a-fA-F]{4}$"
          type: string
          format: hex
          example: "1"
        developer_message:
          type: string
          example: Operation Succcessful
        title:
          type: string
          example: Success
        message:
          type: string
          example: Transaction Completed Successfully

        total_orders_count:
          type: integer
          minimum: 0

        orders:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/CompleteTransactionSuccessfulResponse'
              - $ref: '#/components/schemas/CompleteTransactionFailedResponse'

    CompleteTransactionSuccessfulResponse: &CompleteTransactionSuccessfulResponse
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
          type: string
          example: 6380d4efa3fb4
        plan_status:
          type: string
        plan_started:
          type: boolean
        expiry_date:
          type: string
          format: date-time
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          pattern: "^\\d{18,20}$"
          type: string
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        matching_id:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string
        client_name:
          type: string
        client_email:
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in additional
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string
        order_type:
          type: string
          example: BuyBundle
        vendor_expiry_date_profile:
          type: number
        vendor_expiry_days:
          type: number
        vendor_start_bundle:
          type: boolean
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_active_topups:
          type: boolean

      additionalProperties: false

    CompleteTransactionFailedResponse: &CompleteTransactionFailedResponse
      type: object
      properties:
        order_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        reseller_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        branch_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        order_status:
          type: string
          enum:
          - Failed
        bundle_code:
          type: string
          example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: "eSIM, 10GB, 30 Days, United States, Unthrottled"
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            maxLength: 4
            minLength: 3
            type: string
            example: USA
        country_name:
          type: array
          items:
            type: string
            example: United States
        bundle_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price:
          type: number
          description: in dollars
          format: float
          example: 12
        bundle_retail_price_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 12
        currency_code:
          type: string
        additional_currency_code:
          maxLength: 14
          type: string
          example: SAR
        client_name:
          type: string
        client_email:
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          type: string
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          description: in dollars
          format: float
          example: 1000
        remaining_wallet_balance_in_additional_currency:
          type: number
          description: in dollars
          format: float
          example: 1000
        date_created:
          type: string
          format: date-time
        order_reference:
          maxLength: 30
          type: string
        otp:
          type: string
          format: uuid
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean

        has_related_active_topups:
          type: boolean

      additionalProperties: false

    GenerateVoucherRequest: &GenerateVoucherRequest
      type: object
      additionalProperties: false
      required:
      - voucher_name
      - amount
      - quantity
      - currency_code
      - expiry_datetime
      - generate_csv_file
      properties:

        voucher_name:
          type: string
          example: Adventure30
          maxLength: 30
          minLength: 1
        amount:
          type: number
          format: float
          exclusiveMinimum: true
          minimum: 0
          example: 30
        quantity:
          type: integer
          format: int32
          minimum: 1
          example: 1
        currency_code:
          type: string
          enum:
          - USD

        is_active:
          readOnly: true
          type: boolean

        expiry_datetime:
          type: string
          format: date-time

        generate_csv_file:
          type: boolean

        reason:
          type: string
          maxLength: 300
          example: When adventure calls, you gotta answer!
        voucher_type:
          type: string
          enum:
            - default
            - triggerable


    GetVouchersResponse: &GetVouchersResponse
      type: object
      additionalProperties: false
      properties:
        total_vouchers_count:
          type: integer
          minimum: 0

        vouchers:
          type: array
          items:
            type: object
            additionalProperties: false
            required:
            - voucher_name
            - voucher_code
            - amount
            - reseller_amount
            - expiry_datetime
            properties:

              voucher_name:
                type: string
                example: Adventure30
                maxLength: 30

              voucher_code:
                type: string

              amount:
                type: number
                format: float
                minimum: 0
                example: 30

              reseller_amount:
                type: number
                format: float
                minimum: 0
                example: 30

              is_used:
                type: boolean

              is_active:
                readOnly: true
                type: boolean

              expiry_datetime:
                type: string
                format: date-time

              datetime:
                type: string
                format: date-time


              reason:
                type: string
                maxLength: 300
                example: When adventure calls, you gotta answer!

              voucher_type:
                type: string
                maxLength: 30

              status:
                type: string
                maxLength: 30

              voucher_id:
                <<: *ObjectID

              reseller_id:
                <<: *ObjectID

        <<: *ResponseEntityProperties

    GetVouchersBundledResponse: &GetVouchersBundledResponse
      type: object
      additionalProperties: false
      properties:
        total_vouchers_count:
          type: integer
          minimum: 0

        vouchers:
          type: array
          items:
            type: object
            additionalProperties: false
            required:
            - voucher_name
            - quantity
            - expiry_datetime
            properties:

              voucher_name:
                type: string
                example: Adventure30
                maxLength: 30

              quantity:
                type: integer
                format: int32
                minimum: 1


              expiry_datetime:
                type: string
                format: date-time

              datetime:
                type: string
                format: date-time

              voucher_type:
                type: string
                maxLength: 30
                default: default

              reseller_id:
                <<: *ObjectID

        <<: *ResponseEntityProperties

    GenerateVoucherResponse: &GenerateVoucherResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          example: Voucher Generation Successful

        csv_file:
          type: string
          format: binary
          description: The csv file for the user

        <<: *ResponseEntityProperties

    TopupBundleRequest: &TopupBundleRequest
      type: object
      additionalProperties: false
      properties: &TopupBundleProperties

        order_reference:
          type: string
          description: custom order reference
          maxLength: 100

        previous_order_reference:
          type: string
          description: previous custom order reference to topup bundle
          maxLength: 100

        bundle_code:
          type: string

        order_id:
          <<: *ObjectID

        whatsapp_number:
          type: string

    TopupBundleResponse: &TopupBundleResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          example: Bundle Assigned Successfully

        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        remaining_wallet_balance:
          type: number
          format: float
          example: 30
          description: in dollars

        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 30
          description: in additional currency

        additional_currency_code:
          type: string
          maxLength: 15
          example: SAR

        iccid:
          type: string
          pattern: '^\d{18,20}$'

        <<: *ResponseEntityProperties

    ContactObject: &ContactObject
      type: object
      properties:
        emails:
            type: array
            uniqueItems: true
            items:
              type: string
              pattern: '^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$'
              example: <EMAIL>
            minItems: 1
        phones:
            type: array
            uniqueItems: true
            items:
              type: string
              pattern: '^\+(?:[0-9] ?){6,14}[0-9]$'
            minItems: 1
        website:
          type: string
          pattern: '(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com

        address:
          type: string
          maxLength: 300
      required:
      - emails
      - phones

    Reseller: &Reseller
      type: object
      additionalProperties: false
      required:
      - reseller_id
      - reseller_name
      - reseller_type
      - support_topup
      - is_active
      - date_created
      - currency_code
      - balance
      - contact

      properties: &ResellerProperties
        reseller_id:
            readOnly: true
            <<: *ObjectID

        reseller_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
        reseller_type:
          type: string
          enum:
          - prepaid
          - postpaid
          default: prepaid

        callback_url:
          type: string
          pattern: '(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com

        support_topup:
          type: boolean
          default: false

        is_active:
          type: boolean
          default: false

        supports_multibranches:
          type: boolean
          default: false

        supports_promo:
          type: boolean

        supports_vouchers:
          type: boolean

        date_created:
          readOnly: true
          type: string
          format: date


        currency_code:
          type: string
          enum:
          - USD

          default: USD

        additional_currency_code:
          type: string
          maxLength: 15

          example: SAR

        default_currency_code:
          type: string
          maxLength: 15
          default: USD

        balance:
          type: number
          format: float
          minimum: 0

        balance_in_additional_currency:
          type: number
          format: float
          minimum: 0

        balance_warning_limit:
          type: number
          format: float
          example: 1000
          minimum: 0
          description: in dollars

        credit_limit:
          type: number
          format: float
          minimum: 0
          default: 0
          description: postpaid limit

        credit_warning_limit:
          type: number
          format: float
          minimum: 0
          maximum: 100
          default: 0
          description: credit warning limit in percentage

        credit_limit_in_additional_currency:
          type: number
          format: float
          minimum: 0

        rate_revenue:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 200
          description: rate revenue in percent
          default: 0

        corp_rate_revenue:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 200
          description: corporate revenue in percent

        voucher_rate:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 100

        contact:
          <<: *ContactObject

        email_settings:
          type: object
          additionalProperties: false
          required:
          - username
          - password
          - smtp_port
          - smtp_server
          properties:
            username:
              type: string
              pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
              example: <EMAIL>

            password:
              type: string
              minimum: 8
              maximum: 50
            smtp_server:
              type: string


            smtp_port:
              type: string
              pattern: '^[0-9]*$'
              maxLength: 5



        custom_email_template_qr:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_data:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_expired:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        image:
          type: string
          format: binary
          description: The image file of the user

        image_type:
          type: string


        is_whitelabel:
           type: boolean
           default: false

        tenant_name:
          type: string

        reseller_category:
          type: string


        active_vendors_list:
          type: array
          uniqueItems: true
          items:
            type: string

        vendors_for_balance_deduction_list:
          type: array
          uniqueItems: true
          items:
            type: string

        request_custom_email:
          type: boolean
          default: true

        data_consumption_email:
            type: object
            properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_consumption:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_consumption:
                  type: string

                footer:
                  type: string

        data_expired_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_expired:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_expired:
                  type: string

                footer:
                  type: string

        qr_code_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                android_users:
                  type: string

                smdp_address:
                  type: string

                activation_code:
                  type: string

                ios_users:
                  type: string

                matching_id:
                  type: string


                invoice_details:
                  type: string

                data_bundle_details:
                   type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_qrcode:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                bundle_consumption_sentence:
                  type: string

                bundle_consumption_link:
                  type: string

                topup_sentence:
                  type: string

                topup_link:
                  type: string

                instructions_link:
                  type: string

                subject_qrcode:
                  type: string

                footer:
                  type: string

        <<: *ResponseEntityProperties

    AddResellerRequest: &AddResellerRequest
      type: object
      additionalProperties: false
      required:
      - reseller_name
      - reseller_type
      - support_topup
      - is_active
      - currency_code
      - balance
      - contact
      - supports_multibranches
      - agent

      properties:

        reseller_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
        reseller_type:
          type: string
          enum:
          - prepaid
          - postpaid
          default: prepaid

        callback_url:
          type: string
          pattern: '(https?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com
          description: callback url where reseller received the push notifications

        consumption_url:
          type: string
          pattern: '(https?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com
          description: consumption url where reseller received the consumption notifications

        support_topup:
          type: boolean
          default: true
        is_active:
          type: boolean
          default: true
        supports_promo:
          type: boolean
        supports_vouchers:
          type: boolean
        supports_multibranches:
          type: boolean
          default: false
        currency_code:
          type: string
          enum:
          - USD
          default: USD
        default_currency_code:
          type: string
          maxLength: 15
          example: SAR

        balance:
          type: number
          format: float
          minimum: 0

        credit_limit:
          type: number
          format: float
          minimum: 0
          default: 0
          description: postpaid limit

        credit_warning_limit:
          type: number
          format: float
          minimum: 0
          maximum: 100
          default: 0
          description: credit warning limit in percentage


        balance_warning_limit:
          type: number
          format: float
          example: 1000
          minimum: 0
          description: in dollars


        voucher_rate:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 100
        rate_revenue:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 200
          default: 0


          description: rate revenue in percent

        corp_rate_revenue:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 200

          description: rate revenue in percent for the corporate

        contact:
          <<: *ContactObject

        agent:
          type: object
          additionalProperties: false
          required:
            - username
            - email
            - name
            - password
          properties:

            email:
              type: string
              pattern: '^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$'
              example: <EMAIL>
            username:
              type: string
              minLength: 1
              maxLength: 30
              example: john.snow
            name:
              type: string
              minLength: 1
              maxLength: 20
              pattern: "^[a-zA-Z ']+$"
              example: john snow


            password:
              writeOnly: true
              type: string
              pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
              example: $3343JcS2412345
              maxLength: 30

        email_settings:
          type: object
          additionalProperties: false
          required:
          - username
          - password
          - smtp_port
          - smtp_server
          properties:
            username:
              type: string
              pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
              example: <EMAIL>

            password:
              type: string
              minimum: 8
              maximum: 50
            smtp_server:
              type: string


            smtp_port:
              type: string
              pattern: '^[0-9]*$'
              maxLength: 5

        custom_email_template_qr:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_data:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_expired:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        image:
          type: string
          format: binary
          description: The image file of the user


        is_whitelabel:
           type: boolean
           default: false

        tenant_name:
          type: string

        reseller_category:
            type: string


        active_vendors_list:
          type: array
          uniqueItems: true
          items:
            type: string


        vendors_for_balance_deduction_list:
          type: array
          uniqueItems: true
          items:
            type: string

        request_custom_email:
          type: boolean
          default: true

        data_consumption_email:
            type: object
            properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_consumption:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_consumption:
                  type: string

                footer:
                  type: string

        data_expired_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_expired:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_expired:
                  type: string

                footer:
                  type: string

        qr_code_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                android_users:
                  type: string

                smdp_address:
                  type: string

                activation_code:
                  type: string

                ios_users:
                  type: string

                matching_id:
                  type: string


                invoice_details:
                  type: string

                data_bundle_details:
                   type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_qrcode:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                bundle_consumption_sentence:
                  type: string

                bundle_consumption_link:
                  type: string

                topup_sentence:
                  type: string

                topup_link:
                  type: string

                instructions_link:
                  type: string

                subject_qrcode:
                  type: string

                footer:
                  type: string

        notification_type:
          type: string
          enum:
            - webhook
          default: webhook
        retry_on_failed_after:
          type: integer
          default: 1

    TopupBalanceRequest: &TopupBalanceRequest
      type: object
      additionalProperties: false
      properties:
        topup_amount:
          type: number
          minimum: 0
          format: float
          example: 30

        credit_limit:
          type: number
          minimum: 0
          format: float
          example: 100


    TopupBalanceResponse: &TopupBalanceResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Wallet Topup Successful
        wallet_balance:
          type: number
          format: float
          example: 1000
          description: in dollars

        <<: *ResponseEntityProperties


    EditTopupBalanceResponse: &EditTopupBalanceResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Wallet Topup Edited Successfully
        wallet_balance:
          type: number
          format: float
          example: 1000
          description: in dollars

        <<: *ResponseEntityProperties


    EditResellerRequest: &EditResellerRequest
      type: object
      additionalProperties: false
      properties: &EditResellerProperties
        reseller_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
        balance_warning_limit:
          type: number
          format: float
          example: 1000
          minimum: 0
          description: in dollars
        reseller_type:
          type: string
          enum:
          - prepaid
          - postpaid
          default: prepaid

        callback_url:
          type: string
          pattern: '(https?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com

        consumption_url:
          type: string
          pattern: '(https?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)'
          nullable: true
          example: www.xyz.com

        default_currency_code:
          type: string
          maxLength: 15
          default: USD

        support_topup:
          type: boolean
          default: false

        supports_promo:
          type: boolean

        supports_vouchers:
          type: boolean

        is_active:
          type: boolean
          default: false



        currency_code:
          type: string
          enum:
          - USD

          default: USD


        voucher_rate:
          type: number
          format: float
          example: 12
          minimum: 0
          maximum: 100


        contact:
          <<: *ContactObject

        email_settings:
          type: object
          additionalProperties: false

          properties:
            username:
              type: string
              pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
              example: <EMAIL>

            password:
              type: string
              minimum: 8
              maximum: 50
            smtp_server:
              type: string


            smtp_port:
              type: string
              pattern: '^[0-9]*$'
              maxLength: 5
              minimum: 1

        custom_email_template_qr:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_data:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        custom_email_template_expired:
          type: string
          format: binary
          maxLength: 1048576
          description: Preferably HTML FILE

        image:
          type: string
          format: binary
          description: The image file of the user

        is_whitelabel:
           type: boolean
           default: false

        tenant_name:
          type: string

        reseller_category:
          type: string


        active_vendors_list:
          type: array
          uniqueItems: true
          items:
            type: string


        vendors_for_balance_deduction_list:
          type: array
          uniqueItems: true
          items:
            type: string

        request_custom_email:
          type: boolean
          default: true

        data_consumption_email:
            type: object
            properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_consumption:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_consumption:
                  type: string

                footer:
                  type: string

        data_expired_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_expired:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                subject_expired:
                  type: string

                footer:
                  type: string

        qr_code_email:
          type: object
          properties:

                greetings:
                  type: string

                body_1:
                  type: string

                body_2:
                  type: string

                android_users:
                  type: string

                smdp_address:
                  type: string

                activation_code:
                  type: string

                ios_users:
                  type: string

                matching_id:
                  type: string


                invoice_details:
                  type: string

                data_bundle_details:
                   type: string

                whatsapp_number:
                  type: string

                instagram_link:
                  type: string

                facebook_link:
                  type: string

                website_link:
                  type: string

                email-image_type:
                  type: string

                email_image_qrcode:
                  type: string

                email-logo_type:
                  type: string

                email_logo:
                  type: string

                company_name_team:
                  type: string

                bundle_consumption_sentence:
                  type: string

                bundle_consumption_link:
                  type: string

                topup_sentence:
                  type: string

                topup_link:
                  type: string

                instructions_link:
                  type: string

                subject_qrcode:
                  type: string

                footer:
                  type: string

        notification_type:
          type: string
          enum:
            - webhook
          default: webhook
        retry_on_failed_after:
          type: integer
          default: 1

    GetResellerResponse: &GetResellerResponse
      <<: *Reseller

    GetResellersResponse: &GetResellersResponse
      type: object
      additionalProperties: false
      properties:
        total_resellers_count:
          type: integer
          minimum: 0

        resellers:
          type: array
          items:
            <<: *Reseller

        <<: *ResponseEntityProperties

    AddResellerResponse: &AddResellerResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Reseller Added Successfully
        reseller_id:
          <<: *ObjectID
        agent_id:
          <<: *ObjectID

        <<: *ResponseEntityProperties


    EditResellerResponse: &EditResellerResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Reseller Edited Successfully

        <<: *ResponseEntityProperties

    DeleteResellerResponse: &DeleteResellerResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Reseller Deleted Successfully

        <<: *ResponseEntityProperties

    CustomizePriceRequest: &CustomizePriceRequest
      type: object
      additionalProperties: false
      properties:
        reset_customizations:
          type: boolean
          description: if true, all custom prices will be reset and the rate revenue will be activated.
        set_selling_revenue:
          type: number
          format: float
          minimum: -100
          maximum: 200
          example: 12
          description: percentage

        bundles:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            required:
            - bundle_code
            properties:

              bundle_code:
                type: string
                example: esim_10gb_30days_unitedstates__0905202216214562
              custom_price:
                type: number
                format: float
                example: 12
                minimum: 0
                maximum: 99999999999999
                description: in dollars

              custom_bundle_name:
                type: string
                example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
                maxLength: 100

              bundle_tag:
                type: array
                items:
                  type: string
                  maxLength: 30

              is_active:
                type: boolean






    CustomizeCorpPriceRequest: &CustomizeCorpPriceRequest
      type: object
      additionalProperties: false
      properties:
        reset_customizations:
          type: boolean
          description: if true, all custom prices will be reset and the rate revenue will be activated.
        set_selling_revenue:
          type: number
          format: float
          minimum: -100
          maximum: 200
          example: 12
          description: percentage

        bundles:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            required:
            - bundle_code
            properties:

              bundle_code:
                type: string
                example: esim_10gb_30days_unitedstates__0905202216214562
              custom_price:
                type: number
                format: float
                example: 12
                minimum: 0
                maximum: 99999999999999
                description: in dollars



              is_active:
                type: boolean


    CsvFile:
      type: object
      additionalProperties: false
      required:
      - csv_file

      properties:
        csv_file:
          type: string
          format: binary
    CustomizePriceResponse: &CustomizePriceResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Bundle Retail Price Edited Successfully
        detail:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Bundle Does not exist.
        invalid_bundle_prices:
          type: array
          uniqueItems: true
          items:

            type: object
            additionalProperties: false
            properties:
              bundle_code:
                  type: string
                  example: esim_10gb_30days_unitedstates__0905202216214562
              custom_price:
                type: number
                format: float
                example: 12
                description: in dollars

              unit_price:
                type: number
                format: float
                example: 12
                description: in dollars

        bundles_not_found:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            properties:
              bundle_code:
                type: string
                example: esim_10gb_30days_unitedstates__0905202216214562

        <<: *ResponseEntityProperties



    CustomizeCurrenciesRequest: &CustomizeCurrenciesRequest
      type: object
      additionalProperties: false
      properties:


        currencies:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            required:
            - currency_code
            properties:

              currency_code:
                type: string
                maxLength: 15
                pattern: "^[a-zA-Z ']+$"
                example: SAR
              currency_rate:
                type: number
                format: float
                example: 12
                minimum: 0
                maximum: 99999999999999
                description: rate for one US dollar

              currency_name:
                type: string
                example: Saudi Arabian Rial
                maxLength: 100



              is_available:
                type: boolean


    CustomizeCurrenciesResponse: &CustomizeCurrenciesResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Currencies have been modified Successfully
        detail:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Currencies have been modified Successfully

        invalid_currency_rates:
          type: array
          uniqueItems: true
          items:

            type: object
            additionalProperties: false
            properties:
              currency_code:
                  type: string
                  example: ZZZ
              currency_rate:
                type: number
                format: float
                example: 12



        currencies_not_found:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            properties:
              currency_code:
                type: string
                example: SAR
              currency_name:
                type: string
                example: Saudii Arabian Rial
        <<: *ResponseEntityProperties





    Branch: &Branch
      type: object
      additionalProperties: false
      required:
      - reseller_id
      - branch_id
      - branch_name
      - is_active
      - date_created
      # - balance

      properties: &BranchProperties
        reseller_id:
            readOnly: true
            <<: *ObjectID

        branch_id:
            readOnly: true
            <<: *ObjectID

        branch_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"


        is_active:
          type: boolean
          default: true




        date_created:
          readOnly: true
          type: string
          format: date




        # balance:
        #   type: number
        #   format: float
        #   minimum: 0


        limit:
          type: number
          format: float
          minimum: -1
          exclusiveMinimum: false
          maximum: 1000000000000000
          nullable: true

        limit_consumption:
          type: number
          format: float
          minimum: 0
          maximum: 1000000000000000
          nullable: true



        contact:
          <<: *ContactObject
          nullable: true


        <<: *ResponseEntityProperties







    AddBranchRequest: &AddBranchRequest
      type: object
      additionalProperties: false
      required:
      - branch_name
      - is_active
      # - balance
      - contact
      - agent
      properties:

        branch_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"


        is_active:
          type: boolean
          default: true


        # balance:
        #   type: number
        #   format: float
        #   minimum: 0

        limit:
          type: number
          format: float
          minimum: -1
          exclusiveMinimum: false
          maximum: 1000000000000000


        limit_consumption:
          readOnly: true
          type: number
          format: float
          minimum: 0
          maximum: 1000000000000000
          nullable: true


        contact:
          <<: *ContactObject
          nullable: true

        agent:
          type: object
          additionalProperties: false
          required:
            - username
            - email
            - name
            - password
          properties:

            email:
              type: string
              pattern: '^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$'
              example: <EMAIL>
            username:
              type: string
              minLength: 1
              maxLength: 30
              example: john.snow
            name:
              type: string
              minLength: 1
              maxLength: 20
              pattern: "^[a-zA-Z ']+$"
              example: john snow


            password:
              writeOnly: true
              type: string
              pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
              example: $3343JcS2412345
              maxLength: 30




    EditBranchRequest: &EditBranchRequest
      type: object
      additionalProperties: false
      properties: &EditBranchProperties
        branch_name:
          type: string
          example: Nakhal
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"

        limit:
          type: number
          format: float
          minimum: -1
          exclusiveMinimum: false
          maximum: 1000000000000000
          nullable: true


        limit_consumption:
          readOnly: true
          type: number
          format: float
          minimum: 0
          maximum: 1000000000000000
          nullable: true


        contact:
          <<: *ContactObject

        is_active:
          type: boolean
          default: true


    GetBranchResponse: &GetBranchResponse
      <<: *Branch

    GetBranchesResponse: &GetBranchesResponse
      type: object
      additionalProperties: false
      properties:
        total_branches_count:
          type: integer
          minimum: 0

        branches:
          type: array
          items:
            <<: *Branch

        <<: *ResponseEntityProperties

    AddBranchResponse: &AddBranchResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Branch Added Successfully
        branch_id:
          <<: *ObjectID
        agent_id:
          <<: *ObjectID

        <<: *ResponseEntityProperties


    EditBranchResponse: &EditBranchResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Branch Edited Successfully

        <<: *ResponseEntityProperties

    DeleteBranchResponse: &DeleteBranchResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Branch Deleted Successfully

        <<: *ResponseEntityProperties


    AgentProfile: &AgentProfile
      type: object
      additionalProperties: false
      required:
      - agent_id
      - name
      - email
      - username
      - is_active
      - date_created
      - role_id
      properties: &AgentProfileProperties
        reseller:
          <<: *Reseller
        agent_id:
          readOnly: true
          <<: *ObjectID

        branch_id:
          readOnly: true
          <<: *ObjectID
        email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        username:
          type: string
          minimum: 1
          maximum: 30
          example: john.doe
        name:
          type: string
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
          example: john
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_id:
          <<: *ObjectID


    Agent: &Agent
      type: object
      additionalProperties: false
      required:
        - reseller_id
        - agent_id
        - name
        - email
        - username
        - is_active
        - date_created
        - role_name
      properties: &AgentProperties
        reseller_id:
          <<: *ObjectID

        branch_id:
          <<: *ObjectID
        agent_id:
          readOnly: true
          <<: *ObjectID
        email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        username:
          type: string
          minimum: 1
          maximum: 30
          example: john.doe
        name:
          type: string
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
          example: john snow
        is_active:
          type: boolean
          default: false
        date_created:
          type: string
          format: date-time
        role_name:
          type: string
          minLength: 0
          maxLength: 45
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"




    AddAgentRequest: &AddAgentRequest
      type: object
      additionalProperties: false
      required:
        - username
        - email
        - name
        - password
        - role_name
      properties: &AddAgentProperties

        email:
          type: string
          pattern: '^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-]+)(\.[a-zA-Z]{2,5}){1,2}$'
          example: <EMAIL>
        username:
          type: string
          minLength: 1
          maxLength: 30
          pattern: "^[a-zA-Z][a-zA-Z0-9_.-]*$"  # Ensures valid username format
          example: john.snow
        name:
          type: string
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
          example: john.snow
        is_active:
          type: boolean
          default: false
          readOnly: true
        role_name:
          type: string
          minLength: 0
          maxLength: 45
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"
        password:
          type: string
          writeOnly: true
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
          example: $3343JcS2412345
          maxLength: 30


    EditAgentRequest: &EditAgentRequest
      type: object
      additionalProperties: false


      properties: &EditAgentProperties

        name:
          type: string
          minLength: 1
          maxLength: 20
          pattern: "^[a-zA-Z ']+$"
          example: john snow

        is_active:
          type: boolean
          default: false

        role_name:
          type: string
          minLength: 0
          maxLength: 45
          example: ResellerAdmin
          pattern: "^[a-zA-Z ']+$"

        password:
          writeOnly: true
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
          example: $3343JcS2412345
          maxLength: 30

        password_confirmation:
          writeOnly: true
          type: string
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{15,})'
          example: $3343JcS2412345
          maxLength: 30

    GetAgentResponse: &GetAgentResponse
      <<: *Agent

    GetAgentsResponse: &GetAgentsResponse
      type: object
      additionalProperties: false
      properties:
        total_agents_count:
          type: integer
          minimum: 0

        agents:
          type: array
          items:
            <<: *Agent

        <<: *ResponseEntityProperties

    AddAgentResponse: &AddAgentResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Agent Added Successfully
        agent_id:
          <<: *ObjectID
        <<: *ResponseEntityProperties

    EditAgentResponse: &EditAgentResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Agent Edited Successfully

        <<: *ResponseEntityProperties

    DeleteAgentResponse: &DeleteAgentResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Agent Deleted Successfully


        <<: *ResponseEntityProperties



    GetOrderHistorySuccessfulResponse: &GetOrderHistorySuccessfulResponse
      type: object
      additionalProperties: false
      properties:
        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        branch_id:
          <<: *ObjectID
        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
            type: string
            example: 6380d4efa3fb4
            description: Plan uid related to profiles
        plan_status:
          type: string
          description: It could be Pending, Active or Expired
        plan_started:
          type: boolean
          description: Only if plan started
        expiry_date:
          type: string
          format: date-time
          description: Expiration date for the bundle
        bundle_code:
            type: string
            example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: eSIM, 10GB, 30 Days, United States, Unthrottled
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            type: string
            example: USA
            minLength: 3
            maxLength: 4
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          type: string
          pattern: '^\d{18,20}$'
        bundle_price:
          type: number
          format: float
          example: 12
          description: Price in dollars
        bundle_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: Price in currency code updated by reseller
        bundle_retail_price:
          type: number
          format: float
          example: 12
          description: Price in dollars
        bundle_retail_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: Price in currency code updated by reseller
        currency_code:
          type: string
          description: By default it is dollars
        additional_currency_code:
          type: string
          maxLength: 14
          example: SAR
          description: Currency code updated by reseller
        matching_id:
          type: string
          description: Data for qr code value
        smdp_address:
          type: string
          description: Data for qr code value
        activation_code:
          type: string
          description: Data for qr code value
        client_name:
          type: string
        client_email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          format: float
          example: 1000
          description: Remaining wallet for reseller in dollars
        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 1000
          description: Remaining wallet for reseller in updated currency
        date_created:
          type: string
          format: date-time
          description: Creation date for the order
        refund_reason:
          type: string
          example: User Did not have good coverage.
          description: Refund reason in case refund occured
        order_reference:
          type: string
          maxLength: 30
          description: Order reference number
        otp:
          type: string
          format: uuid
          description: Order reference number
        profile_expiry_date:
          type: string
          description: Expiration date for the profile
        bundle_expiry_date:
          type: string
          description: Expiration date for the bundle
        order_type:
          type: string
          example: BuyBundle
          description: To specify bundle or topup
        whatsapp_number:
          type: string
          description: Whatsapp number for the client
        topup_an_expired_plan:
          type: boolean
          description: If topup a plan that have plan_status = Expired
        has_related_active_topups:
          type: boolean
          description: If order type is BuyBundle and the order has related topup(s)

    GetOrderHistoryFailedResponse: &GetOrderHistoryFailedResponse
      type: object
      additionalProperties: false
      properties:
        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        branch_id:
          <<: *ObjectID
        order_status:
          type: string
        bundle_code:
            type: string
            example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: eSIM, 10GB, 30 Days, United States, Unthrottled
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise
        country_code:
          type: array
          items:
            type: string
            example: USA
            minLength: 3
            maxLength: 4
        country_name:
          type: array
          items:
            type: string
            example: United States
        bundle_price:
          type: number
          format: float
          example: 12
          description: in dollars
        bundle_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars
        bundle_retail_price:
          type: number
          format: float
          example: 12
          description: in dollars
        bundle_retail_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars
        currency_code:
          type: string
        additional_currency_code:
          type: string
          maxLength: 14
          example: SAR
        client_name:
          type: string
        client_email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          format: float
          example: 1000
          description: in dollars
        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 1000
          description: in dollars
        date_created:
          type: string
          format: date-time
        order_reference:
          type: string
          maxLength: 30
        otp:
          type: string
          format: uuid
        order_type:
          type: string
          example: BuyBundle
        whatsapp_number:
          type: string
        topup_an_expired_plan:
          type: boolean
        has_related_active_topups:
          type: boolean

    RefundOrderRequest:
      type: object
      additionalProperties: false
      properties:
        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_id:
          <<: *ObjectID

    RefundOrderResponse:
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          example: Order Refunded Successfully
        <<: *ResponseEntityProperties

    GetPromoDashboardResponse: &GetPromoDashboardResponse
      type: object
      additionalProperties: false
      properties:
        sales_per_day:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              commission_amount:
                  type: number
                  format: float
                  example: 0.522
              bundles_sold:
                type: integer
                minimum: 0
                example: 2
              total_sales_volume:
                type: number
                format: float
                example: 5.22
              date:
                type: string
                example: "2023-04-27"


    GetDashboardResponse: &GetDashboardResponse
      type: object
      additionalProperties: false
      properties:
        top_five_bundles:
          type: array
          uniqueItems: true
          maxItems: 5
          items:
            type: object
            additionalProperties: false
            properties:
              bundle_name:
                  type: string
                  example: eSIM_ 10GB_ 30 Days_ United States_  10000 GB
              bundle_code:
                type: string
                example: esim_10gb_30days_unitedstates__0905202216214562
              bundle_marketing_name:
                type: string
                example: eSIM, 10GB, 30 Days, United States, Unthrottled

              sales_number:
                type: integer
                minimum: 0



        gross_sales_volume_usd:
          type: number
          format: float
          example: 3000.5

        net_sales_volume_usd:
          type: number
          format: float
          example: 300

        bundles_sold:

          type: array
          items:

            type: object
            additionalProperties: false
            properties:
              date:
                type: string
                pattern: '^\d{4}-\d{2}$'

              sales_number:
                type: integer
                minimum: 0


        <<: *ResponseEntityProperties


    ResendOrderEmailResponse: &ResendOrderEmailResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          example: Email Sent Successfully

        <<: *ResponseEntityProperties


    GetTransactionHistoryResponse: &GetTransactionHistoryResponse
      type: object
      additionalProperties: false
      properties:
        total_transactions_count:
          type: integer
          minimum: 0
        transactions:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              type:
                type: string
              amount:
                type: number
                format: float
                example: 20
              amount_additional_currency:
                type: number
                format: float
                example: 20
              additional_currency_code:
                type: string
                maxLength: 15
                example: SAR
              date_created:
                type: string
                format: date-time
              order_id:
                <<: *ObjectID
              branch_id:
                <<: *ObjectID
              destination_branch_id:
                <<: *ObjectID
              reseller_id:
                <<: *ObjectID
              transaction_id:
                <<: *ObjectID
              refund_reason:
                type: string
                example: User Did not have good coverage.
        <<: *ResponseEntityProperties

    GetOrderHistoryResponse: &GetOrderHistoryResponse
      type: object
      additionalProperties: false
      properties:
        total_orders_count:
          type: integer
          minimum: 0
        orders:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/GetOrderHistorySuccessfulResponse'
              - $ref: '#/components/schemas/GetOrderHistoryFailedResponse'

        <<: *ResponseEntityProperties

    GetPromocodeHistoryResponse: &GetPromocodeHistoryResponse
      type: object
      additionalProperties: false
      properties:
        total_orders_count:
          type: integer
          minimum: 0
        orders:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              order_id:
                <<: *ObjectID
              reseller_id:
                <<: *ObjectID
              promocode:
                type: string
                example: Monty2023
              affiliate_program:
                type: boolean
              affiliate_marketing_message:
                type: string
                maxLength: 1000
              branch_id:
                <<: *ObjectID
              order_status:
                type: string
                enum:
                - Successful
                - Refunded
              plan_uid:
                  type: string
                  example: 6380d4efa3fb4
              plan_status:
                type: string
              bundle_code:
                  type: string
                  example: esim_10gb_30days_unitedstates__0905202216214562
              bundle_marketing_name:
                type: string
                example: eSIM, 10GB, 30 Days, United States, Unthrottled
              bundle_name:
                type: string
                example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
              bundle_category:
                type: string
                enum:
                - country
                - global
                - region
                - cruise
              country_code:
                type: array
                items:
                  type: string
                  example: USA
                  minLength: 3
                  maxLength: 4
              country_name:
                type: array
                items:
                  type: string
                  example: United States
              iccid:
                type: string
                pattern: '^\d{18,20}$'
              bundle_price:
                type: number
                format: float
                example: 12
                description: in dollars
              bundle_retail_price:
                type: number
                format: float
                example: 12
                description: in dollars
              matching_id:
                type: string
              smdp_address:
                type: string
              activation_code:
                type: string
              client_name:
                type: string
              client_email:
                type: string
                pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
                example: <EMAIL>
              remaining_wallet_balance:
                type: number
                format: float
                example: 1000
                description: in dollars
              date_created:
                type: string
                format: date-time
              refund_reason:
                type: string
                example: User Did not have good coverage.
              commission_rate:
                type: number
                format: float
                minimum: 0
                maximum: 100
              commision_amount:
                type: number
                format: float


        <<: *ResponseEntityProperties

    GetAffiliateProgramResponse: &GetAffiliateProgramResponse
      type: object
      additionalProperties: false
      properties:
        affiliate_id:
          <<: *ObjectID
        image_url:
          type: string
        promo_code:
          type: string
        discount_rate:
          type: number
          format: float
          minimum: 0
          maximum: 100
        amount:
          type: number
          format: float
          minimum: 0
        draft:
          type: boolean

    GetVoucherUseHistoryResponse: &GetVoucherUseHistoryResponse
      type: object
      additionalProperties: false
      properties:
        total_uses_count:
          type: integer
          minimum: 0
        data:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              voucher_use_id:
                <<: *ObjectID
              reseller_id:
                <<: *ObjectID
              branch_id:
                <<: *ObjectID
              voucher_code:
                type: string
                example: Adventure30
              voucher_name:
                type: string
                example: Adventure30

              username:
                type: string
                minLength: 1
                maxLength: 20
                example: john snow

              amount:
                type: number
                format: float

              date_created:
                type: string
                format: date-time

              currency_code:
                type: string
                enum:
                - USD


        <<: *ResponseEntityProperties

    IssueReport: &IssueReport
      type: object
      additionalProperties: false
      properties:
        total_issues_count:
          type: integer
          minimum: 0

        issues:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:


              feedback:
                type: object
                additionalProperties: false
                required:
                - satisfaction
                properties:
                  satisfaction:
                    type: integer
                    description: User satisfaction rating (e.g., 1-5, with 5 being the highest).
                    minimum: 0
                    maximum: 5
                    example: 4
                  comments:
                    type: string
                    description: Additional comments on the resolution process.

              report_id:
                <<: *ObjectID

              added_by:
                <<: *ObjectID

              reseller_id:
                <<: *ObjectID

              branch_id:
                <<: *ObjectID

              date_created:
                readOnly: true
                type: string
                format: date

              last_modified:
                readOnly: true
                type: string
                format: date

              chat:
                type: array
                items:
                  type: object
                  additionalProperties: false
                  properties:
                    message:
                      type: string
                      maxLength: 10000
                      description: Detailed description of the issue or concern.

                    attachement:
                      type: string
                      description: Attachement Url

                    date_created:
                      readOnly: true
                      type: string
                      format: date

                    added_by:
                      <<: *ObjectID

                    added_by_name:
                      type: string
                      minLength: 1
                      maxLength: 20
                      pattern: "^[a-zA-Z ']+$"
                      example: John Wick

                    added_by_type:
                      type: string
                      readOnly: true

                    file_size:
                      type: number
                      format: float
                      example: 16.7
                      description: in kiloBytes

              requester:
                type: string
                minLength: 1
                maxLength: 20
                pattern: "^[a-zA-Z ']+$"
                example: John Wick

              assignee:
                type: string
                minLength: 1
                maxLength: 20
                pattern: "^[a-zA-Z ']+$"
                example: Monty eSIM Support

              resolution:
                type: string
                maxLength: 10000
                description: Final resolution before closing the issue.

              status:
                type: string
                enum:
                - Open
                - Closed
                - Under Review

              issue_type:
                type: string
                enum:
                - Technical Issue
                - Price-related Concern
                description: Type of the issue (e.g., "Technical Issue" or "Price-related Concern").

              issue_subject:
                type: string
                maxLength: 500
                description: Summary of Issue

              issue_description:
                type: string
                maxLength: 10000
                description: Detailed description of the issue or concern.



              priority:
                type: integer
                minimum: 1
                maximum: 4
                default: 3

                description: Priority level of the issue ("Critical :1", "High:2", "Medium:3", or "Low:4").

        <<: *ResponseEntityProperties

    AddIssueReportRequest: &AddIssueReportRequest
      type: object
      additionalProperties: false
      required:
      - issue_type
      - issue_description

      properties:

        issue_type:
          type: string
          enum:
          - Technical Issue
          - Price-related Concern
          description: Type of the issue (e.g., "Technical Issue" or "Price-related Concern").

        issue_subject:
          type: string
          maxLength: 500
          description: Summary of Issue
          minLength: 3


        issue_description:
          type: string
          maxLength: 10000
          description: Detailed description of the issue or concern.
          minLength: 3



        priority:
          type: integer
          minimum: 1
          maximum: 4
          default: 3

          description: Priority level of the issue ("Critical :1", "High:2", "Medium:3", or "Low:4").



        attachments:
          type: array
          items:
            type: string
            format: binary
          description: List of attached files.

    EditIssueReportRequest: &EditIssueReportRequest
      type: object
      additionalProperties: false
      properties: &EditIssueReportProperties
        message:
          type: string
          maxLength: 10000
          description: Detailed description of the issue or concern.

        attachments:
          type: array
          items:
            type: string
            format: binary
          description: List of attached files.

    ResolveIssueRequest: &ResolveIssueRequest
      type: object
      additionalProperties: false
      required:
      - resolution
      properties:
        resolution:
          type: string
          maxLength: 10000
          example: "Customer has been satisfied."

    SubmitFeedbackRequest:
      type: object
      additionalProperties: false
      required:
      - satisfaction
      properties:
        satisfaction:
          type: integer
          description: User satisfaction rating (e.g., 1-5, with 5 being the highest).
          minimum: 0
          maximum: 5
          example: 4
        comments:
          type: string
          description: Additional comments on the resolution process.

    ResolveIssueResponse: &ResolveIssueResponse
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Issue Resolved Successfully

    GetIssueReportsResponse: &GetIssueReportsResponse
      type: array
      minItems: 0
      maxItems: 1000
      items:
        <<: *IssueReport

    AddIssueReportResponse: &AddIssueReportResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Issue Reported Successfully
        report_id:
          <<: *ObjectID


        <<: *ResponseEntityProperties

    EditIssueReportResponse: &EditIssueReportResponse
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Issue Report Modified Successfully


    SubmitFeedbackResponse: &SubmitFeedbackResponse
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Feedback Submitted Successfully


    DeleteIssueReportResponse: &DeleteIssueReportResponse
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Issue Report Deleted Successfully

    NetworkList: &NetworkList
      type: object
      additionalProperties: false
      required:
      - networks
      properties: &NetworkListProperties
        networks:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false

            properties:

              network_id:
                <<: *ObjectID
              vendor_name:
                type: string
                example: Saudi Arabian Rial
                maxLength: 100
                minLength: 2
                pattern: "^[a-zA-Z ']+$"

              country_code:
                type: string
                example: FRA
                minLength: 3
                maxLength: 3

              operator_list:
                type: array
                uniqueItems: true
                items:
                  type: string
                  example: Voda
                  maxLength: 100
                  minLength: 2
                  pattern: "^[a-zA-Z ']+$"

              is_shown:
                type: boolean
                default: true

        networks_count:
          type: integer
          example: 1000

        <<: *ResponseEntityProperties

    BundleNetworkList: &BundleNetworkList
      type: object
      additionalProperties: false
      required:
      - networks
      properties: &BundleNetworkListProperties
        networks:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false

            properties:



              country_code:
                type: string
                example: FRA
                minLength: 3
                maxLength: 3

              operator_list:
                type: array
                uniqueItems: true
                items:
                  type: string
                  example: Voda
                  maxLength: 100
                  minLength: 2
                  pattern: "^[a-zA-Z ']+$"


        networks_count:
          type: integer
          example: 1000

    ManageNetworkListRequest: &ManageNetworkListRequest
      type: object
      additionalProperties: false
      properties:


        networks:
          type: array
          uniqueItems: true
          items:
            type: object
            additionalProperties: false
            required:
            - vendor_name
            - country_code
            - operator_list
            properties:

              vendor_name:
                type: string
                example: Venderino
                maxLength: 100
                minLength: 2
                pattern: "^[a-zA-Z ']+$"

              country_code:
                type: string
                example: FRA
                minLength: 3
                maxLength: 3

              operator_list:
                type: array
                uniqueItems: true
                items:
                  type: string
                  example: Voda
                  maxLength: 100
                  minLength: 2
                  pattern: "^[a-zA-Z ']+$"

              is_shown:
                type: boolean
                default: true

    AllNetworkList_networksResponse: &AllNetworkList_networksResponse
      type: object
      properties:
        network_id:
          maxLength: 24
          minLength: 24
          pattern: "^[0-9a-fA-F]{24}$"
          type: string
          format: hex
          example: 507f191e810c19729de860ea
        vendor_code:
          maxLength: 100
          minLength: 2
          pattern: "^[a-zA-Z ']+$"
          type: string
          example: Saudi Arabian Rial
        country_code:
          maxLength: 3
          minLength: 3
          type: string
          example: FRA
        operator_list:
          uniqueItems: true
          type: array
          items:
            maxLength: 100
            minLength: 2
            pattern: "^[a-zA-Z ']+$"
            type: string
            example: Voda
        is_shown:
          type: boolean
          default: true
      additionalProperties: false
      example:
        country_code: FRA
        vendor_code: TEST
        network_id: 507f191e810c19729de860ea
        is_shown: true
        vendor_name: Saudi Arabian Rial
        operator_list:
        - Voda
        - Voda

    ManageNetworkListResponse: &ManageNetworkListResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Networks have been modified Successfully
        detail:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Networks have been modified Successfully

        invalid_networks:
          type: array
          uniqueItems: true
          items:

            type: object
            additionalProperties: false
            properties:
              country_code:
                  type: string
                  example: ZZZ
              vendor_name:
                type: string
                example: Johnnies net


        <<: *ResponseEntityProperties


    DeleteNetworkListResponse: &DeleteNetworkListResponse
      type: object
      additionalProperties: false
      properties:
        Message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: ^[\s\S]+$
          example: Network Deleted Successfully


    GetAvailableResellerPropertiesResponse: &GetAvailableResellerPropertiesResponse
      type: object
      additionalProperties: false
      properties:
        categories:
          type: array
          uniqueItems: true
          items:
              type: string

        <<: *ResponseEntityProperties


    GetAgentByEmailRequest: &GetAgentByEmailRequest
      type: object
      additionalProperties: false
      properties:
        email:
          type: string

        <<: *ResponseEntityProperties


    GetPlanHistorySuccessfulResponse: &GetPlanHistorySuccessfulResponse
      type: object
      additionalProperties: false
      properties:
        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID
        branch_id:
          <<: *ObjectID

        order_status:
          type: string
          enum:
          - Successful
          - Refunded
        plan_uid:
            type: string
            example: 6380d4efa3fb4

        plan_status:
          type: string

        plan_started:
          type: boolean

        expiry_date:
          type: string
          format: date-time


        bundle_code:
            type: string
            example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: eSIM, 10GB, 30 Days, United States, Unthrottled
        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled
        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise

        data_unit:
          type: string

        retail_price:
          type: number
          format: float

        data_amount:
          type: number
          format: float

        validity_amount:
          type: string

        region_name:
          type: string

        region_code:
          type: string

        bundle_duration:
          type: integer

        country_code:
          type: array
          items:
            type: string
            example: USA
            minLength: 3
            maxLength: 4
        country_name:
          type: array
          items:
            type: string
            example: United States
        iccid:
          type: string
          pattern: '^\d{18,20}$'
        bundle_price:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_retail_price:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_retail_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars

        currency_code:
          type: string


        additional_currency_code:
          type: string
          maxLength: 14
          example: SAR

        matching_id:
          type: string
        smdp_address:
          type: string
        activation_code:
          type: string

        client_name:
          type: string

        client_email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>

        remaining_wallet_balance:
          type: number
          format: float
          example: 1000
          description: in dollars

        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 1000
          description: in additional

        date_created:
          type: string
          format: date-time

        refund_reason:
          type: string
          example: User Did not have good coverage.
        order_reference:
          type: string
          maxLength: 30

        otp:
          type: string
          format: uuid

        profile_expiry_date:
          type: string
        bundle_expiry_date:
          type: string

        order_type:
          type: string
          example: BuyBundle

        whatsapp_number:
          type: string

        topup_an_expired_plan:
          type: boolean

        has_related_order:
          type: boolean

        has_related_active_topups:
          type: boolean

    GetPlanHistoryFailedResponse: &GetPlanHistoryFailedResponse
      type: object
      additionalProperties: false
      properties:
        order_id:
          <<: *ObjectID
        reseller_id:
          <<: *ObjectID

        branch_id:
          <<: *ObjectID

        order_status:
          type: string


        bundle_code:
            type: string
            example: esim_10gb_30days_unitedstates__0905202216214562
        bundle_marketing_name:
          type: string
          example: eSIM, 10GB, 30 Days, United States, Unthrottled

        bundle_name:
          type: string
          example: eSIM_ 10GB_ 30 Days_ United States_ Unthrottled

        bundle_category:
          type: string
          enum:
          - country
          - global
          - region
          - cruise

        country_code:
          type: array
          items:
            type: string
            example: USA
            minLength: 3
            maxLength: 4
        country_name:
          type: array
          items:
            type: string
            example: United States

        bundle_price:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_retail_price:
          type: number
          format: float
          example: 12
          description: in dollars

        bundle_retail_price_in_additional_currency:
          type: number
          format: float
          example: 12
          description: in dollars

        currency_code:
          type: string


        additional_currency_code:
          type: string
          maxLength: 14
          example: SAR



        client_name:
          type: string

        client_email:
          type: string
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          example: <EMAIL>
        remaining_wallet_balance:
          type: number
          format: float
          example: 1000
          description: in dollars

        remaining_wallet_balance_in_additional_currency:
          type: number
          format: float
          example: 1000
          description: in dollars

        date_created:
          type: string
          format: date-time

        order_reference:
          type: string
          maxLength: 30

        otp:
          type: string
          format: uuid

        order_type:
          type: string
          example: BuyBundle

        whatsapp_number:
          type: string

        topup_an_expired_plan:
          type: boolean

        has_related_order:
          type: boolean

        has_related_active_topups:
          type: boolean

    GetPlanHistoryResponse: &GetPlanHistoryResponse
      type: object
      additionalProperties: false
      properties:
        total_orders_count:
          type: integer
          minimum: 0

        orders:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/GetPlanHistorySuccessfulResponse'
              - $ref: '#/components/schemas/GetPlanHistoryFailedResponse'

        <<: *ResponseEntityProperties


    NetworkListByRegionsResponse: &NetworkListByRegionsResponse
      type: object
      additionalProperties: false
      properties:
        networks_count:
          type: integer
          example: 1000
        networks:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              region_code:
                type: string
                example: "1"
                description: Unique identifier for the region.
              region_name:
                type: string
                example: "Asia"
                description: Name of the region.
              countries:
                type: array
                description: List of countries in the region.
                items:
                  type: object
                  additionalProperties: false
                  properties:
                    country_name:
                      type: string
                      example: "India"
                      description: Name of the country.
                    country_code:
                      type: string
                      example: "IN"
                      description: ISO country code.
                    networks:
                      type: array
                      description: List of networks in the country.
                      items:
                        type: object
                        properties:
                          network_id:
                            type: string
                            example: "1"
                            description: Unique identifier for the network.
                          network_name:
                            type: array
                            items:
                              type: string
        <<: *ResponseEntityProperties

    UpdateVoucherAttributesResponse: &UpdateVoucherAttributesResponse
      type: object
      additionalProperties: false
      properties:
        message:
          type: string
          minLength: 0
          maxLength: 100
          pattern: '^[\s\S]+$'
          example: Voucher Updated Successfully.
        voucher_id:
          <<: *ObjectID

        <<: *ResponseEntityProperties

    UpdateVoucherAttributesRequest: &UpdateVoucherAttributesRequest
      type: object
      additionalProperties: false
      required:
        - status
      properties:
        status:
          type: string
          enum:
            - "active"
            - "inactive"



    ResellerScopedBundle:
      type: object
      properties:
        bundle_id:
          <<: *ObjectID
        bundle_name:
          type: string
        supported_countries:
          type: array
          items:
            type: string
        region:
          type: string
        data_size:
          type: string
        data_unit:
          type: string
        validity_days:
          type: integer
        price:
          type: number
    ScopedBundlesResponse:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total_bundles:
          type: integer
        bundles:
          type: array
          items:
            $ref: '#/components/schemas/ResellerScopedBundle'
    RegionGroupedBundlesResponse:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total_bundles:
          type: integer
        total_regions:
          type: integer
        regions:
          type: array
          items:
            type: object
            properties:
              region:
                type: string
              region_name:
                type: string
              bundles:
                type: array
                items:
                  $ref: '#/components/schemas/ResellerScopedBundle'


paths:

  /HealthCheck:
    get:
      operationId: healthCheck
      tags:
        - Welcome
      summary: Check Server
      description: a simple api to test if server is up and running
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                type: string
                example: Welcome To Reseller Portal! The server is Up and Running.

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Agent/login:
    post:
      operationId: login
      tags:
        - Agent
      summary: Login Agent to Platform.
      description: Logs in User to Platform and returns access token.
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/LoginRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /Agent/forgot-password:
    post:
      operationId: forgotPassword
      tags:
        - Agent
      summary: Submit a forgot Password request.
      description: Allows the user to reset password via email.
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPasswordResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Agent/reset-password:
    post:
      operationId: resetPassword
      tags:
        - Agent
      summary: Resets User Password .
      description: Allows the user to reset password.
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/PasswordResetRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /Agent/logout:
    post:
      operationId: logout
      tags:
        - Agent
      summary: Logout Agent from Platform
      description: Logs out Agent from Platform.
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        "200":
          description: Successful logout
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogoutResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Agent:

    post:

      operationId: addAgent
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: branch id for super admin and reseller admin

      tags:
        - Agent

      summary: Add Agent to Branch.
      description: Adds a Agent to branch, can be called by PERSONNEL.

      security:
      - ApiKeyAuth: []

      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/AddAgentRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddAgentResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: branch id for super admin and reseller admin


      operationId: getAgent
      tags:
        - Agent
      summary:  Gets all  Agents
      description: Gets all Agents , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAgentsResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Agent/{AgentID}:

    parameters:
        - name: AgentID
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            <<: *ObjectID

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin, reseller admin

    put:
      operationId: editAgent

      tags:
        - Agent
      summary: Edit Agent
      description: Allows admins to edit an Agent
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/EditAgentRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditAgentResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    delete:
      operationId: deleteAgent
      deprecated: true
      tags:
        - Agent
      summary: Delete Agent
      description: Allows PERSONNEL to delete Agent.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteAgentResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    get:
      operationId: getAgentById
      tags:
        - Agent
      summary: Gets Agent by ID
      description: Gets a Agent by ID , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentProfile'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Token/Refresh:
    post:
      operationId: refreshToken
      tags:
        - Token
      summary: Token Refresher
      description: Takes a refresh Token and grants the user a new Access token and refresh Token.
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /Bundles/Cancel:
    post:
      tags:
        - Bundles
      summary: Cancel Order.
      description: Allow to cancel an order.
      operationId: cancel_bundle
      parameters:
        - name: reseller_id
          in: query
          description: reseller id for super admin
          required: false
          style: form
          explode: true
          schema:
            maxLength: 24
            minLength: 24
            pattern: "^[0-9a-fA-F]{24}$"
            type: string
            format: hex
            example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelBundleRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelBundleResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []

  /Bundles/Reserve:
    post:
      tags:
        - Bundles
      summary: Reserve Bundle
      description: "Checks if the reseller has balance, and if the bundle is available,\
        \ and assigns it to a user."
      operationId: reserve_bundle
      parameters:
        - name: reseller_id
          in: query
          description: reseller id for super admin
          required: false
          style: form
          explode: true
          schema:
            maxLength: 24
            minLength: 24
            pattern: "^[0-9a-fA-F]{24}$"
            type: string
            format: hex
            example: 507f191e810c19729de860ea
        - name: branch_id
          in: query
          description: reseller id for super admin
          required: false
          style: form
          explode: true
          schema:
            maxLength: 24
            minLength: 24
            pattern: "^[0-9a-fA-F]{24}$"
            type: string
            format: hex
            example: 507f191e810c19729de860ea
        - name: currency_code
          in: query
          description: currency code to preview in
          required: false
          style: form
          explode: true
          schema:
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            type: string
            example: SAR
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReserveBundleRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReserveBundleResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []

  /Bundles/Complete:
    post:
      tags:
        - Bundles
      summary: Complete transaction
      description: "Complete transaction and send profile information"
      operationId: complete_transaction
      parameters:
        - name: reseller_id
          in: query
          description: reseller id for super admin
          required: false
          style: form
          explode: true
          schema:
            maxLength: 24
            minLength: 24
            pattern: "^[0-9a-fA-F]{24}$"
            type: string
            format: hex
            example: 507f191e810c19729de860ea
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompleteTransactionRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteTransactionResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []

  /Bundles/CSV:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3
        - in: query
          name: bundle_category
          schema:
            type: string
            enum:
              - global
              - region
              - country
              - cruise
        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
            - price_asc
            - price_dsc
            - bundle_name
            - data_asc
            - data_dsc

          description: sorting parameter
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean
        - in: query
          name: export
          schema:
            type: boolean

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in
      operationId: getBundlesOfCountryCsv
      tags:
        - Bundles
      summary: Get all country bundles Available CSV
      description: Can be called by users to view all bundles available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'




  /AvailableCountries:
    get:
      parameters:


        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean

      operationId: getCountries
      tags:
        - Bundles
      summary: Get all countries Available
      description: Can be called by users to view all counties available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCountriesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /AvailableCurrenciesCSV:


    post:

      operationId: manageCurrenciesCSV
      tags:
        - Bundles
      summary: Manage Currencies.
      description: Allows a Super admin to manage Currencies.
      # parameters:
      # - in: query
      #   name: reseller_id
      #   schema:
      #     <<: *ObjectID
      #   description: reseller id for super admin
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          multipart/form-data:
              schema:
                  type: object
                  additionalProperties: false
                  required:
                  - file
                  properties:
                    file:
                      type: string
                      format: binary
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizeCurrenciesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /AvailableCurrencies:

    post:

      operationId: manageCurrencies
      tags:
        - Bundles
      summary: Manage Currencies.
      description: Allows a Super admin to manage Currencies.
      # parameters:
      # - in: query
      #   name: reseller_id
      #   schema:
      #     <<: *ObjectID
      #   description: reseller id for super admin
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/CustomizeCurrenciesRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizeCurrenciesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      parameters:


        # - in: query
        #   name: reseller_id
        #   schema:
        #     <<: *ObjectID
        #   description: reseller id for super admin
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean


        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1


        - in: query
          name: export
          schema:
            type: boolean

      operationId: getCurrencies
      tags:
        - Bundles
      summary: Get all currencies Available
      description: Can be called by users to view all currencies available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCurrenciesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /AvailableRegions:
    get:
      parameters:


        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean

      operationId: getRegions
      tags:
        - Bundles
      summary: Get all regions Available
      description: Can be called by users to view all regions available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegionsResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Bundles:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3
        - in: query
          name: bundle_category
          schema:
            type: string
            enum:
              - global
              - region
              - country
              - cruise
        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
            - price_asc
            - price_dsc
            - bundle_name
            - data_asc
            - data_dsc

          description: sorting parameter
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean

        - in: query
          name: bundle_tag
          schema:
            type: string
            maxLength: 30
          description: filter parameter


        - in: query
          name: region_code
          schema:
            type: string
            maxLength: 10
          description: filter parameter

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

        - in: query
          name: bundle_code
          schema:
            type: string
            maxLength: 30
          description: filter parameter

      operationId: getBundlesOfCountry
      tags:
        - Bundles
      summary: Get all country bundles Available
      description: Can be called by users to view all bundles available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBundlesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    post:
      operationId: assignBundle
      tags:
        - Bundles
      summary: Assign Bundle
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency code to preview in

      description: Checks if the reseller has balance, and if the bundle is available, and assigns it to a user.

      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/AssignBundleRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignBundleResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Bundles/Networks:
    get:
      parameters:
      - in: query
        name: bundle_code
        required: true
        schema:
          type: string
          example: GBR_1213202219550292

      - in: query
        name: country_code
        schema:
          type: string
          minLength: 3
          maxLength: 3


      operationId: getBundleNetworkList
      tags:
        - Bundles
      summary:  Gets the bundle network list
      description: Gets the bundle network list , can be called by users.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BundleNetworkList'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'





  /Bundles/Topup:
    post:
      operationId: topupBundle
      tags:
        - Bundles
      summary: Topup Bundle
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: branch id for super admin and reseller admin

      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR

      description: Checks if the reseller has balance, and if the bundle is available, topups the bundle of the user.
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/TopupBundleRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopupBundleResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3

        - in: query
          name: bundle_code
          required: true
          schema:
            type: string
            example: GBR_1213202219550292
        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
            - price_asc
            - price_dsc
            - bundle_name
            - data_asc
            - data_dsc

          description: sorting parameter

        - in: query
          name: bundle_tag
          schema:
            type: string
            maxLength: 30
          description: filter parameter

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

      operationId: getCompatibleTopupBundles
      tags:
        - Bundles
      deprecated: true
      summary: Get compatible topup bundles
      description: Allows the user to view all bundles that are compatible with the Esim Profile Installed.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBundlesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /v2/Bundles/Topup:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3

        - in: query
          name: bundle_code
          required: true
          schema:
            type: string
            example: GBR_1213202219550292


        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
            - price_asc
            - price_dsc
            - bundle_name
            - data_asc
            - data_dsc

          description: sorting parameter

        - in: query
          name: bundle_tag
          schema:
            type: string
            maxLength: 30
          description: filter parameter

        - in: query
          name: order_id
          schema:
            <<: *ObjectID

        - in: query
          name: previous_order_reference
          schema:
            type: string
            description: previous custom order reference to topup bundle
            maxLength: 100


        - in: query
          name: region_code
          schema:
            type: string
            maxLength: 10
          description: filter parameter


        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

      operationId: getCompatibleTopupBundlesV2
      tags:
        - Bundles
      summary: Get compatible topup bundles
      description: Allows the user to view all bundles that are compatible with the Esim Profile Installed.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBundlesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /Reseller:
    post:
      parameters:
      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code for operation
      operationId: addReseller
      tags:
        - Reseller
      summary: Adds a Reseller to platform.
      description: Adds a Reseller to platform, can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/AddResellerRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddResellerResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      operationId: getReseller
      parameters:
      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1

      - in: query
        name: dropdown
        schema:
          type: boolean
      - in: query
        name: reseller_name
        schema:
          type: string
          maxLength: 100
        description: filter parameter
      - in: query
        name: supports_promo
        schema:
          type: boolean
        description: filter parameter

      - in: query
        name: supports_voucher
        schema:
          type: boolean
        description: filter parameter

      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code to preview

      - in: query
        name: is_whitelabel
        schema:
          type: boolean
        description: filter parameter

      - in: query
        name: reseller_category
        schema:
          type: string
        description: filter parameter

      tags:
        - Reseller
      summary:  Gets all  Resellers
      description: Gets all Resellers , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetResellersResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Reseller/Admin/custom_corporate_price:
    post:

      operationId: customizeCorporatePrice
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
        required: true
      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code to preview
      tags:
        - Reseller
      summary: Customize Bundle Unit Price.
      description: Allows a reseller agent to customize bundle retail price.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/CustomizeCorpPriceRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizePriceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Reseller/Admin/custom_corporate_price_csv:
    post:

      operationId: customizeCorporatePriceCsv
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
        required: true
      tags:
        - Reseller
      summary: Customize Bundle Unit Price CSV file.
      description: Allows a reseller agent to customize bundle retail price by CSV files.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          multipart/form-data:
              schema:
                  type: object
                  additionalProperties: false
                  required:
                  - file
                  properties:
                    file:
                      type: string
                      format: binary
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizePriceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Reseller/custom_price:
    post:

      operationId: customizePrice
      tags:
        - Reseller
      summary: Customize Bundle Price.
      description: Allows a reseller agent to customize bundle retail price.
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code to preview

      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/CustomizePriceRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizePriceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Reseller/custom_price_csv:
    post:

      operationId: customizePriceCsv
      tags:
        - Reseller
      summary: Customize Bundle Price.
      description: Allows a reseller agent to customize bundle retail price via Excel CSV File.
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          multipart/form-data:
              schema:
                  type: object
                  additionalProperties: false
                  required:
                  - file
                  properties:
                    file:
                      type: string
                      format: binary
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomizePriceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /Reseller/Topup/{ResellerID}:

    parameters:
        - name: ResellerID
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            <<: *ObjectID

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency_code to preview


    post:

      operationId: topupResellerBalance

      tags:
        - Reseller
      summary: Topup Reseller Balance.
      description: Allows a monty agent to topup reseller wallet balance.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/TopupBalanceRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopupBalanceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Reseller/{ResellerID}:

    parameters:
        - name: ResellerID
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            <<: *ObjectID

    put:
      operationId: editReseller

      tags:
        - Reseller
      summary: Edit Reseller
      description: Allows admins to edit Reseller
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/EditResellerRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditResellerResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    delete:
      operationId: deleteReseller
      tags:
        - Reseller
      summary: Delete Reseller
      description: Allows PERSONNEL to delete Reseller.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResellerResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    get:
      operationId: getResellerById
      tags:
        - Reseller

      parameters:
      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code to preview
      summary: Gets Reseller by ID
      description: Gets a Reseller by ID , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetResellerResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Branch:
    post:

      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      operationId: addBranch
      tags:
        - Branch
      summary: Adds a Branch to platform.
      description: Adds a Branch to platform, can be called by Reseller Admin.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/AddBranchRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddBranchResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      operationId: getBranch
      parameters:
      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1

      - in: query
        name: dropdown
        schema:
          type: boolean

      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      tags:
        - Branch
      summary:  Gets all  Branches
      description: Gets all Branches , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBranchesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Branch/{BranchID}:

    parameters:
        - name: BranchID
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            <<: *ObjectID
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

    put:
      operationId: editBranch

      tags:
        - Branch
      summary: Edit Branch
      description: Allows admins to edit Branch
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/EditBranchRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditBranchResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    delete:
      operationId: deleteBranch
      tags:
        - Branch
      summary: Delete Branch
      description: Allows PERSONNEL to delete Branch.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteBranchResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    get:
      operationId: getBranchById
      tags:
        - Branch
      summary: Gets Branch by ID
      description: Gets a Branch by ID , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBranchResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Transactions:
    get:
      parameters:

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin, reseller admin
        - in: query
          name: show_branches
          schema:
            type: boolean
          description: branch id for super admin, reseller admin


        - in: query
          name: export
          schema:
            type: boolean

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in



      operationId: getTransactionHistory
      tags:
        - Orders
      summary: Get Transaction History
      description: Can be called to view reseller transaction history.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetTransactionHistoryResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Transactions/{TransactionID}:
    parameters:
      - name: TransactionID
        in: path
        description: id value that needs to be considered for filter
        required: true
        schema:
          <<: *ObjectID
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: branch id for super admin, reseller admin

      - in: query
        name: currency_code
        schema:
          type: string
          maxLength: 10
          pattern: "^[a-zA-Z ]+$"
          example: SAR
        description: currency_code to preview

    put:
      operationId: editTransaction

      tags:
        - Orders
      summary: Edit Transaction
      description: Allows Super admin to edit a Transaction
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/TopupBalanceRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditTopupBalanceResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /Orders:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            example: USA
            minLength: 3
            maxLength: 3
        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin, reseller admin
        - in: query
          name: order_id
          schema:
            <<: *ObjectID

        - in: query
          name: order_reference
          schema:
            type: string
            maxLength: 30

        - in: query
          name: search
          schema:
            type: string

        - in: query
          name: fields_to_search
          description: |
            JSON array of fields to search within (e.g., ["Order Status", "Order Date"]).
          schema:
            type: array
            uniqueItems: true
            items:
              type: string
              enum:
                - order_status
                - bundle_name
                - client_email
                - client_name
                - bundle_price
                - bundle_retail_price
                - whatsapp_number

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in




      operationId: getOrderHistory
      tags:
        - Orders
      summary: Get Order History
      description: Can be called to view reseller order history.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetOrderHistoryResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Orders/Dashboard:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            example: USA
            minLength: 3
            maxLength: 3
        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin
      operationId: getDashboard
      tags:
        - Orders
      summary: Get Dashboard
      description: Can be called to view reseller bundles sold chard, top 5 bundles, and sales volume.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetDashboardResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /Promocode:
    get:
      parameters:
        - in: query
          name: promocode
          schema:
            type: string

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin, reseller admin
        - in: query
          name: customer_name
          schema:
            type: string
            minLength: 1
            maxLength: 20
            pattern: "^[a-zA-Z ']+$"
            example: john snow

        - in: query
          name: affiliate_program
          schema:
            type: boolean

      operationId: getPromocodeHistory
      tags:
        - Orders
      summary: Get Promocode Order History
      description: Can be called to view reseller promocode order history.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetPromocodeHistoryResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Affiliate:
    get:
      parameters:
        - in: query
          name: affiliate_id
          schema:
            <<: *ObjectID



      operationId: getAffiliateProgram
      tags:
        - Affiliate
      summary: Get Affiliate Program
      description: Can be called to view reseller Affiliate Program.

      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetAffiliateProgramResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'




  /Promocode/Dashboard:
    get:
      parameters:
        - in: query
          name: promocode
          schema:
            type: string

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin

        - in: query
          name: affiliate_program
          schema:
            type: boolean


      operationId: getPromocodeDashboard
      tags:
        - Orders
      summary: Get Promocode Dashboard
      description: Can be called to view reseller promocode bundles sold chart
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetPromoDashboardResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'





  /Orders/Consumption:
    get:
      parameters:
        - in: query
          name: order_id
          schema:
            <<: *ObjectID
          description: order id

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: order_reference
          schema:
            type: string
            maxLength: 30

      operationId: getBundleConsumption
      tags:
        - Orders
      summary: Get Bundle Consumption
      description: Can be called to view user bundle consumption and status.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:

                $ref: '#/components/schemas/GetBundleConsumptionResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Orders/MyeSimConsumption:
    get:
      parameters:
        - in: query
          name: order_id
          schema:
            <<: *ObjectID
          description: order id

        - in: query
          name: otp
          schema:
            type: string
            format: uuid

        - in: query
          name: order_reference
          schema:
            type: string
            maxLength: 30


      operationId: getMyBundleConsumption
      tags:
        - Orders
      summary: Get my Bundle Consumption
      description: Can be called to view user bundle consumption and status.

      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:

                $ref: '#/components/schemas/GetMyBundleConsumptionResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Orders/ResendEmail:
    post:
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
      operationId: resendOrderEmail
      tags:
        - Orders
      summary: Resend Order Email.
      description: Allows User to Resend an order email to deliver invoice and qr code.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                type: object
                additionalProperties: false
                required:
                - order_id
                properties:
                  order_id:

                    <<: *ObjectID




      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResendOrderEmailResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /Orders/SendConsumptionEmail:
    post:
      parameters:
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin
        - in: query
          name: notification_type
          required: true
          schema:
            type: string
            enum:
            - Expired
            - Eighty

          description: reseller id for super admin
      operationId: sendConsumptionEmail
      tags:
        - Orders
      summary: Send Consumption Email.
      description: Allows User to Send Consumption Email to the user upon certain thresholds.
      security:
        - ApiKeyAuth: [ ]
        - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: false
              required:
                - order_id
              properties:
                order_id:

                  <<: *ObjectID




      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResendOrderEmailResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Orders/Refund:
    post:
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin



      operationId: refundOrder
      tags:
        - Orders
      summary: Refund Order.
      description: Allows SuperAdmin to Refund an order.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/RefundOrderRequest'




      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundOrderResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'




  /Role/{RoleID}:

    parameters:
      - name: RoleID
        in: path
        description: id value of role that needs to be considered for filter
        required: true
        schema:
          <<: *ObjectID

    delete:
      operationId: deleteRole
      tags:
        - Role
      summary: Deletes specific role by ID
      description: Deletes specific role by ID
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      responses:
        "200":
          description: Deleted Successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteRoleResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    put:
      operationId: editRole

      tags:
        - Role
      summary: Edit Role
      description: Allows admins to edit a Role
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/EditRoleRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditRoleResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      operationId: getRoleById
      tags:
        - Role
      summary: Returns specific role by ID
      description: Returns specific role by ID

      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      responses:
        "200":
          description: Returned Successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRoleResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Role/All:
    get:
      operationId: getAllRoles
      tags:
        - Role
      summary: Returns all roles in platform
      description: Returns a list of roles on the platform. Gives the user the role's name, id and description.
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      responses:
        "200":
          description: Returned Successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllRolesResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Role:
    post:
      operationId: createRole
      tags:
        - Role
      summary: Creates a new role.
      description: Takes a role object and inserts it into the DB
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/CreateRoleRequest'
      responses:
        "201":
          description: Created Role Successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRoleResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'




  /Roles/UMAP:



    patch:
      operationId: updateMontyAdminPermissions
      tags:
        - Role
      summary: Updates Monty Admin Permissions.
      description: Updates Monty Admin Role with all Permissions Available.
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []

      responses:
        "200":
          description: Added APIs to role's permissions successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateAdminPermissionsResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'






  /Voucher/Generate:

    post:
      operationId: generateVoucher
      tags:
        - Voucher
      summary: Generate Voucher
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin


      description: Checks if the reseller has balance, and generates a voucher
      security:
      - ApiKeyAuth: []
      - InternalApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/GenerateVoucherRequest'
      responses:
        "200":
          description: Token found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateVoucherResponse'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /Voucher/History:
    get:
      parameters:
        - in: query
          name: voucher_name
          schema:
            type: string

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean


        - in: query
          name: username
          schema:
            type: string
            minLength: 1
            maxLength: 20
            pattern: "^[a-zA-Z ']+$"
            example: john snow

      operationId: getVoucherCodeHistory
      tags:
        - Voucher
      summary: Get Voucher Use History
      description: Can be called to view reseller Voucher Use history.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetVoucherUseHistoryResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Voucher/Details:
    get:
      parameters:
        - in: query
          name: voucher_name
          schema:
            type: string

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean



        - in: query
          name: available
          schema:
            type: boolean


      operationId: getVoucherCodes
      tags:
        - Voucher
      summary: Get Vouchers Generated
      description: Can be called to view reseller Vouchers generated.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetVouchersResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    patch:
      operationId: patchVoucher
      tags:
        - Voucher
      summary: Updates Voucher Attributes
      description: Updates Voucher Attributes.
      security:
        - ApiKeyAuth: [ ]
        - InternalApiKeyAuth: [ ]

      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: voucher_id
        required: true
        schema:
          <<: *ObjectID
        description: voucher id

      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateVoucherAttributesRequest'

      responses:
        "200":
          description: Updated Voucher Successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateVoucherAttributesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'




  /Voucher:
    get:
      parameters:
        - in: query
          name: voucher_name
          schema:
            type: string

        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean





      operationId: getVouchers
      tags:
        - Voucher
      summary: Get Vouchers Generated bundled in voucher names
      description: Can be called to view reseller Vouchers generated.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetVouchersBundledResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /IssueReport:
    post:
      parameters:
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
      operationId: addIssueReport
      tags:
        - IssueReport
      summary: Submit an Issue Report.
      description: Submit a technical issue or pricing concern report.
      security:
      - ApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/AddIssueReportRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddIssueReportResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



    get:
      parameters:
      - name: IssueReportID
        in: query
        description: id value that needs to be considered for filter
        schema:
          type: string

      - in: query
        name: startDate
        schema:
          type: string
          minLength: 0
          maxLength: 50
          format: date-time
        description: beginning date for filtering the order
      - in: query
        name: endDate
        schema:
          type: string
          minLength: 0
          maxLength: 50
          format: date-time
        description: end date for filtering the order

      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1

      - in: query
        name: branch_id
        schema:
          <<: *ObjectID
        description: branch id for super admin, reseller admin


      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin

      - in: query
        name: show_branches
        schema:
          type: boolean
        description: branch id for super admin, reseller admin


      - in: query
        name: export
        schema:
          type: boolean

      - in: query
        name: search
        schema:
          type: string
          pattern: '^[^\[\]\{\}]*$'

      operationId: getIssueReport
      tags:
        - IssueReport
      summary:  Gets all  Issues Reported
      description: Gets all Issues Reported , can be called by PERSONNEL.
      security:
      - ApiKeyAuth: []
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IssueReport'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /IssueReport/{ReportID}:



    parameters:
        - name: ReportID
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            type: string
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin



    put:
      operationId: editIssueReport



      tags:
        - IssueReport
      summary: Modify Issue Report
      description: Manage the issue report
      security:
      - ApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/EditIssueReportRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditIssueReportResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
    delete:
      operationId: deleteIssueReport
      tags:
        - IssueReport
      summary: Delete IssueReport
      description: Allows PERSONNEL to delete IssueReport.
      security:
      - ApiKeyAuth: []
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteIssueReportResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /ResolveIssue/{ReportID}:
    put:
      parameters:
      - name: ReportID
        in: path
        description: id value that needs to be considered for filter
        required: true
        schema:
          type: string
      - in: query
        name: reseller_id
        schema:
          <<: *ObjectID
        description: reseller id for super admin
      operationId: resolveIssue
      tags:
        - IssueReport
      summary: Resolve an Issue Report.
      description: Resolve and finalize an Issue Report.
      security:
      - ApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/ResolveIssueRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveIssueResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /IssueReport/{ReportID}/Feedback:
    put:
      summary: Submit Feedback for Issue Resolution
      description: Submit feedback on the satisfaction of issue resolution.
      operationId: submitFeedback
      tags:
      - IssueReport

      parameters:
        - name: ReportID
          in: path
          required: true
          description: ID of the issue report.
          schema:
            type: string
      security:
      - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitFeedbackRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitFeedbackResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /AllNetworkList:
    get:
      tags:
         - NetworkList
      summary: Gets all Network lists from for all bundles
      description: "Gets all Network lists , can be called by users."
      operationId: get_all_network_list
      parameters:
        - name: page_size
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100
        - name: page_number
          in: query
          required: false
          style: form
          explode: true
          schema:
            minimum: 1
            type: integer
        - name: export
          in: query
          required: false
          style: form
          explode: true
          schema:
            type: boolean
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AllNetworkList_networksResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []

  /NetworkList:
    post:
      operationId: manageNetworkList
      tags:
        - NetworkList
      summary: manage network lists to branch.
      description: Adds a NetworkList to branch, can be called by PERSONNEL.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/ManageNetworkListRequest'
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManageNetworkListResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

    get:
      parameters:
      - in: query
        name: network_id
        schema:
          <<: *ObjectID

      - in: query
        name: country_code
        schema:
          type: string
          minLength: 3
          maxLength: 3

      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1


      - in: query
        name: export
        schema:
          type: boolean

      operationId: getNetworkList
      tags:
        - NetworkList
      summary:  Gets all Network lists
      description: Gets all Network lists , can be called by users.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkList'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'
  /NetworkList/{network_id}:

    parameters:
        - name: network_id
          in: path
          description: id value that needs to be considered for filter
          required: true
          schema:
            <<: *ObjectID



    delete:
      operationId: deleteNetworkList
      tags:
        - NetworkList
      summary: Delete NetworkList
      description: Allows admin to delete a Network.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteNetworkListResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'



  /NetworkListCSV:


    post:

      operationId: manageNetworksCSV
      tags:
        - NetworkList
      summary: Manage Networks CSV.
      description: Allows a Super admin to manage Networks Via CSV File.

      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      requestBody:
        content:
          multipart/form-data:
              schema:
                  type: object
                  additionalProperties: false
                  required:
                  - file
                  properties:
                    file:
                      type: string
                      format: binary
      responses:
        "201":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManageNetworkListResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Bundles/AvailableTopup:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3

        - in: query
          name: bundle_code
          required: true
          schema:
            type: string
            example: GBR_1213202219550292


        - in: query
          name: page_size
          schema:
            type: integer
            enum:
              - 10
              - 25
              - 50
              - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
              - price_asc
              - price_dsc
              - bundle_name
              - data_asc
              - data_dsc

          description: sorting parameter

        - in: query
          name: bundle_tag
          schema:
            type: string
            maxLength: 30
          description: filter parameter

        - in: query
          name: region_code
          schema:
            type: string
            maxLength: 10
          description: filter parameter


        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

      operationId: getAvailableTopupBundles
      tags:
        - Bundles
      summary: Get available topup bundles
      description: Allows the user to view all bundles that are available with the Esim Profile Installed.
      security:
        - ApiKeyAuth: [ ]
        - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBundlesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Reseller/Bundles/Scope:
    get:
      summary: Get reseller bundles grouped by scope (region, countries, or global)
      description: >
        Returns bundles grouped by their availability scope: region,
        country, or global.
  
        **Filtering Rules**: - Use the `filter` query parameter to specify grouping scope: `region`, `countries`, or `global`. - Pagination is supported via `page` and `limit`.
  
        **Region Rules**: - Bundles are grouped under one of the following display regions: Africa, Asia, Europe, Middle East, North America, South America. - **Turkey** is grouped under Europe by default. But if a bundle contains **Turkey and a Middle Eastern country**, it is grouped under Middle East. - **Oceania** countries are grouped under **Asia**. - **Central America** and **Caribbean** countries are grouped under **South America**. - Bundles are **never duplicated across regions**—each belongs to one region only.
  
        **Global Rules**: - Bundles in the `global` scope must cover **more than 50 countries**.
  
        **Data size input rules**: - If the value is **less than 1**, it is multiplied by 1000 and treated as megabytes. - If the value is **1 or more**, it is treated as gigabytes.
  
        **Examples**: - To filter for **500 MB**, pass `0.5` - To filter for **500 GB**, pass `500`
      operationId: getResellerBundlesByScope
      tags:
        - Bundles
      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []
      parameters:
        - in: query
          name: geoscope
          required: true
          schema:
            type: string
            enum:
              - region
              - countries
              - global
          description: >
            Determines how bundles are grouped: - `region`: groups bundles
            under regions like Europe, Asia, etc. - `countries`: returns all
            bundles scoped to specific countries, regions, or global (flat list).
            - `global`: returns only global bundles covering over 50 countries.
        - in: query
          name: page
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number for pagination.
        - in: query
          name: limit
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Maximum number of bundles per page.
        - in: query
          name: data_size_min
          required: false
          schema:
            type: string
            pattern: '^\d+(\.\d+)?$'
          description: >
            Minimum data size (e.g., "0.5" for 500MB or "1" for 1GB).  If value
            < 1, it is multiplied by 1000 and treated as megabytes.  If >= 1, it
            is treated as gigabytes.
        - in: query
          name: data_size_max
          required: false
          schema:
            type: string
            pattern: '^\d+(\.\d+)?$'
          description: >
            Maximum data size (e.g., "0.5" for 500MB or "1" for 1GB).  If value
            < 1, it is multiplied by 1000 and treated as megabytes.  If >= 1, it
            is treated as gigabytes.
        - in: query
          name: validity_days_min
          required: false
          schema:
            type: integer
            minimum: 0
          description: Minimum validity period in days.
        - in: query
          name: validity_days_max
          required: false
          schema:
            type: integer
            minimum: 1
          description: Maximum validity period in days.
        - in: query
          name: price_min
          required: false
          schema:
            type: string
            pattern: '^\d+(\.\d+)?$'
          description: Minimum bundle price.
        - in: query
          name: price_max
          required: false
          schema:
            type: string
            pattern: '^\d+(\.\d+)?$'
          description: Maximum bundle price.
      responses:
        "200":
          description: Successful response with bundles grouped by region or as a flat list
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: "#/components/schemas/ScopedBundlesResponse"
                  - $ref: "#/components/schemas/RegionGroupedBundlesResponse"

  /Reseller/AvailableResellerProperties:
    get:
      parameters:
        - in: query
          name: category_names_list
          description: category names list of resellers
          schema:
            type: array
            uniqueItems: true
            items:
              type: string



      operationId: AvailableResellerProperties
      tags:
        - Reseller
      summary: Get available properties of resellers
      description: Allows the user to view all properties that are available for resellers.
      security:
        - ApiKeyAuth: [ ]
        - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAvailableResellerPropertiesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Agent/GetAgentByEmail:
    post:
      operationId: GetAgentByEmail
      tags:
        - Agent
      summary: Gets Agent by Email
      description: Gets a Agent by Email, can be called by PERSONNEL.
      security:
        - ApiKeyAuth: []
        - InternalApiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
                $ref: '#/components/schemas/GetAgentByEmailRequest'
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAgentResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'


  /Orders/PlanHistory:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            example: USA
            minLength: 3
            maxLength: 3
        - in: query
          name: startDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: beginning date for filtering the order
        - in: query
          name: endDate
          schema:
            type: string
            minLength: 0
            maxLength: 50
            format: date-time
          description: end date for filtering the order
        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: export
          schema:
            type: boolean

        - in: query
          name: branch_id
          schema:
            <<: *ObjectID
          description: branch id for super admin, reseller admin
        - in: query
          name: order_id
          schema:
            <<: *ObjectID

        - in: query
          name: order_reference
          schema:
            type: string
            maxLength: 30

        - in: query
          name: search
          schema:
            type: string

        - in: query
          name: fields_to_search
          description: |
            JSON array of fields to search within (e.g., ["Order Status", "Order Date"]).
          schema:
            type: array
            uniqueItems: true
            items:
              type: string
              enum:
                - order_status
                - bundle_name
                - client_email
                - client_name
                - bundle_price
                - bundle_retail_price
                - whatsapp_number

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

        - in: query
          name: iccid
          schema:
            type: string
            maxLength: 30
            pattern: "^\\d{18,20}$"
            example: "8943108161000050000"

      operationId: getPlanHistory
      tags:
        - Orders
      summary: Get Plan History
      description: Can be called to view reseller order history.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:

              schema:
                $ref: '#/components/schemas/GetPlanHistoryResponse'

        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /Bundles/V2:
    get:
      parameters:
        - in: query
          name: country_code
          schema:
            type: string
            minLength: 3
            maxLength: 3
        - in: query
          name: bundle_category
          schema:
            type: string
            enum:
              - global
              - region
              - country
              - cruise
        - in: query
          name: page_size
          schema:
            type: integer
            enum:
            - 10
            - 25
            - 50
            - 100

        - in: query
          name: page_number
          schema:
            type: integer
            minimum: 1

        - in: query
          name: reseller_id
          schema:
            <<: *ObjectID
          description: reseller id for super admin

        - in: query
          name: bundle_name
          schema:
            type: string
            maxLength: 100
          description: filter parameter

        - in: query
          name: sort_by
          schema:
            type: string
            enum:
            - price_asc
            - price_dsc
            - bundle_name
            - data_asc
            - data_dsc

          description: sorting parameter
        - in: query
          name: reseller_admin_view
          schema:
            type: boolean

        - in: query
          name: bundle_tag
          schema:
            type: string
            maxLength: 30
          description: filter parameter


        - in: query
          name: region_code
          schema:
            type: string
            maxLength: 10
          description: filter parameter

        - in: query
          name: currency_code
          schema:
            type: string
            maxLength: 10
            pattern: "^[a-zA-Z ]+$"
            example: SAR
          description: currency code to preview in

        - in: query
          name: bundle_code
          schema:
            type: string
            maxLength: 30
          description: filter parameter

        - in: query
          name: country_code_array
          schema:
            type: array
            items:
              type: string
          description: filter parameter

      operationId: getBundlesOfCountryV2
      tags:
        - Bundles
      summary: Get all country bundles Available
      description: Can be called by users to view all bundles available
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBundlesResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

  /NetworkListByRegions:
    get:
      parameters:
      - in: query
        name: network_id
        schema:
          <<: *ObjectID

      - in: query
        name: country_code
        schema:
          type: string
          maxLength: 3

      - in: query
        name: region_code
        schema:
          type: string
          maxLength: 3


      - in: query
        name: page_size
        schema:
          type: integer
          enum:
          - 10
          - 25
          - 50
          - 100

      - in: query
        name: page_number
        schema:
          type: integer
          minimum: 1


      - in: query
        name: export
        schema:
          type: boolean

      operationId: getNetworkListByRegions
      tags:
        - NetworkList
      summary:  Gets all Network lists
      description: Gets all Network lists , can be called by users.
      security:
      - ApiKeyAuth: [ ]
      - InternalApiKeyAuth: [ ]
      responses:
        "200":
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkListByRegionsResponse'
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '204':
          $ref: '#/components/responses/204'

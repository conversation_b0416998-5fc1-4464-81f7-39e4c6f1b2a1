<link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
{% extends 'admin/base.html' %}
{% from 'admin/macros.html' import render_field %}
{% block head_css %}
    {{ super() }}
    <style>
        .navbar {
            border-color: black;
            border-radius: 10px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .active {
            font-weight: bolder;
        }
    </style>
{% endblock head_css %}
{% block brand %}
    {#      <div class="col-md-1">#}
    {#          <img height="45" width="45" src="{{ url_for('static', filename='logo.png') }}"/>#}
    {#      </div>#}
    <a class="navbar-brand" href="#" style="font-weight: bold">{{ admin_view.admin.name }}</a>

{% endblock %}
<link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
{% block page_body %}
    {{ super() }}

{% endblock %}

{% if current_user.is_authenticated %}
    {% block messages %}
        <h3 id="brand" style="color: black; font-weight: bold">{{ admin_view.name|capitalize }}</h3>
    {% endblock %}

    {% block access_control %}
        <div class="list-group pull-right">
            <p style="font-weight: bold;color:dodgerblue;font-size:12px">Welcome: {{ current_user.email }}</p>
            <p style="font-weight: bold;color:dodgerblue;font-size:12px"><a href="{{ url_for('admin.logout_view') }}">Logout</a>
            </p>
        </div>

    {% endblock %}
{% else %}
    <ul class="dropdown-menu">
        <li><a href="{{ url_for('admin.login_view') }}">You have to login to acces this pages</a></li>
    </ul>
{% endif %}
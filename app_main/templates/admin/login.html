{% extends "admin/base2.html" %}
{% from 'admin/macros.html' import render_field %}
{% block head_css %}
    {{ super() }}
    <style>
        .navbar {
            border-color: black;
            border-radius: 5px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
        }
        .navbar{
            font-weight: bold;
        }
    </style>
{% endblock head_css %}
{% block brand %}
      <div class="col-md-1"><a class="navbar-brand" href="#">{{ admin_view.admin.name }}</a></div>
 {% endblock %}

{% block page_body %}
    {{ super() }}

{% endblock %}
{% block body %}
      <form class="card" action="" method="post">
        <div class="card-header">
            <h3 class="card-title">{{form_title}}</h3>
        </div>
          <div class="card-body p-6">
            <div class="card-title text-center">
               <!-- <p>If this is your first visit please   <a href="{{ url_for('admin.login_factor_view') }}">this page</a> </p>
                -->
            </div>
             {{ form.hidden_tag() }}
            {% for field in form %}
                <div class="form-group">
                    {{ field(class_="form-control") }}
                </div>
            {% endfor %}


            <div class="form-footer">
              <button type="submit" id="submit" name="submit" class="btn btn-primary btn-block">Submit</button>
            </div>
          </div>
        </form>

<!--<style>-->
        <!--.body {-->
            <!--background-color:white;-->
        <!--}-->
    <!--</style>-->
              <div>{{ admin_view.admin.name }}</div>
{% endblock %}
  {% import 'admin/layout.html' as layout with context -%}
  {% import 'admin/static.html' as admin_static with context %}
  <!DOCTYPE html>
  <html>
    <head>
      <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}
          {{ admin_view.name }}
      {% endblock %}</title>
      {% block head_meta %}
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta name="description" content="">
          <meta name="author" content="">
      {% endblock %}
      {% block head_css %}
          <link href="{{ url_for('static',filename='css/bootstrap.min.css')}}" rel="stylesheet">
          <!--<link href="{{ admin_static.url(filename='bootstrap/bootstrap3/swatch/{swatch}/bootstrap.min.css'.format(swatch=config.get('FLASK_ADMIN_SWATCH', 'default')), v='3.3.5') }}" rel="stylesheet">-->
          {%if config.get('FLASK_ADMIN_SWATCH', 'default') == 'default' %}
         <!-- <link href="{{ admin_static.url(filename='bootstrap/bootstrap3/css/bootstrap-theme.min.css', v='3.3.5') }}" rel="stylesheet">-->
          {%endif%}
          <link href="{{ admin_static.url(filename='admin/css/bootstrap3/admin.css', v='1.1.1') }}" rel="stylesheet">
          <link href="{{url_for('static',filename='css/dashboard.css')}}" rel="stylesheet"></link>
             <link href="{{ url_for('static',filename='css/icons.css')}}" rel="stylesheet">

  	<link href="{{ admin_static.url(filename='admin/css/bootstrap3/submenu.css') }}" rel="stylesheet">
          {% if admin_view.extra_css %}
            {% for css_url in admin_view.extra_css %}
              <link href="{{ css_url }}" rel="stylesheet">
            {% endfor %}
          {% endif %}
          <style>
          body {
              padding-top: 4px;
          }
          </style>
      {% endblock %}
      {% block head %}
      {% endblock %}
      {% block head_tail %}
      {% endblock %}
    </head>
    <body>
      {% block page_body %}
      <div class="page" >
          <div class="page-single">
                 {% endblock %}

              <div class="container">
                  <div class="row">
                    <div class="col col-login mx-auto">
                      <div class="text-center mb-6">
                          <img src="{{ url_for('static', filename='logo2.png') }}" class="h-6" alt="">
                      </div>
                            {% block messages %}
                    {{ layout.messages() }}
                   {% endblock %}

                      {% block body %}{% endblock %}
                    </div>


                   {# store the jinja2 context for form_rules rendering logic #}
                {% set render_ctx = h.resolve_ctx() %}


                   </div>
              </div>

      </div>
            {% block menu_links %}



      </div>
      {% endblock %}

      {% block tail_js %}
      <script src="{{ url_for('static',filename='js/jquery-3.5.1.min.js')}}"  type="text/javascript"></script>
      <script src="{{ admin_static.url(filename='bootstrap/bootstrap3/js/bootstrap.min.js', v='3.3.5') }}" type="text/javascript"></script>
      <script src="{{ admin_static.url(filename='vendor/moment.min.js', v='2.22.2') }}" type="text/javascript"></script>
      <!--<script src="{{ admin_static.url(filename='vendor/select2/select2.min.js', v='3.5.2') }}" type="text/javascript"></script>
        -->
        <script
            src="{{ admin_static.url(filename='vendor/select2/select2.min.js', v='3.5.2') }}"
            type="text/javascript"
    ></script>
          <script src="{{ admin_static.url(filename='admin/js/helpers.js', v='1.0.0') }}" type="text/javascript"></script>
      {% if admin_view.extra_js %}
        {% for js_url in admin_view.extra_js %}
          <script src="{{ js_url }}" type="text/javascript"></script>
        {% endfor %}
      {% endif %}
      {% endblock %}

      {% block tail %}
      {% endblock %}
    </body>
  </html>

{% extends 'admin/master.html' %}
{% import'admin/layout.html' as layout with context %}
{% import 'admin/lib.html' as lib with context
%} {% import 'admin/static.html' as admin_static with context%} {% import
'admin/model/layout.html' as model_layout with context %} {% import
'admin/actions.html' as actionlib with context %} {% import
'admin/model/row_actions.html' as row_actions with context %} {% block head %}
{{ super() }}
{{ lib.form_css() }}
{% endblock %} {% block body %}
{{ layout.messages() }}

<div class="row">
    <div class="col-lg-12 col-md-12">
            <form class="card" action="" method="post">
<!--          <div class="row">-->
<!--              <div class="col-sm-2 col-md-5">-->
<!--              <div class="card-body p-10">-->
<!--                <div class="card-title text-center">-->
<!--                        </div>-->
            {{ form.hidden_tag() }}
            {{ lib.render_form_fields(form, form_opts=form_opts) }}

<!--            {% for field in form %}-->
<!--                <div class="form-group">-->
<!--                    {{ field(class_="form-control") }}-->
<!--                </div>-->
<!--            {% endfor %}-->



             <div class="form-group">
                    <div class="col-md-offset-2 col-md-10 submit-row">
                        <button type="submit" id="submit" name="submit" class="btn btn-primary ">Generate Script</button>

                    </div>
 </div>

</form>
    </div>
</div>

{% endblock %} {% block tail %}
{{ super() }}

{% if filter_groups %}
<div id="filter-groups-data" style="display:none;">
  {{ filter_groups | tojson | safe }}
</div>
<div id="active-filters-data" style="display:none;">
  {{ active_filters | tojson | safe }}
</div>
{% endif %}

<!-- The Modal -->
<div class="modal" id="full-content-modal" tabindex="-1" role="dialog"   aria-hidden="true">
  <div class="modal-dialog  modal-lg modal-dialog-scrollable"  role="document">
    <div class="modal-content">

      <!-- Modal Header -->
      <div class="modal-header">
        <h4 class="modal-title"></h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <!-- Modal body -->
      <div class="modal-body">
      </div>
      <!-- Modal footer -->
    </div>
  </div>
</div>

{{ lib.form_js() }}
<!--<script src="{{ admin_static.url(filename='admin/js/filters.js', v='1.0.0') }}"></script>-->
    <script src="{{ url_for('static',filename='js/filters.js')}}"></script>
<script>
$(function () {
   $(".show-full_content").on( "click", function() {
  $("#full-content-modal .modal-body").html($(this).data('original-content'))
  $("#full-content-modal .modal-title").html($(this).data('title'))
  $('#full-content-modal').modal('show')
});
  $('.public_tooltip').tooltip({
      template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner tooltip-key-full"></div></div>'
  })
   $('.action-rowtoggle').click(function () {
    $('.action-checkbox').prop('checked', this.checked);
});




})
$( document ).ready(function() {
      $("#from_date").daterangepicker({
                  timePicker: false,
                  showDropdowns: true,
                  timePickerIncrement: 1,
                  timePicker12Hour: false,
                  singleDatePicker: true,
                  format: "YYYY-MM-DD 00:00:00"
                })


      $("#to_date").daterangepicker({
          timePicker: false,
          showDropdowns: true,
          timePickerIncrement: 1,
          timePicker12Hour: false,
          singleDatePicker: true,
        format: "YYYY-MM-DD 00:00:00"
                })
})

</script>

{% endblock %}
<!--comment2-->

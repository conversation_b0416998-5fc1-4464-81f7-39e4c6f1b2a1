{% extends 'admin/master.html' %}
{% import
  'admin/model/layout.html' as model_layout with context %}
{% import 'admin/lib.html' as lib with context %}
{% from 'admin/lib.html' import extra with context %} {# backward compatible #}

{% block head %}
  {{ super() }}
  {{ lib.form_css() }}
{% endblock %}

{% block body %}
{{ model_layout.messages() }}
  {% block navlinks %}
  <ul class="nav nav-tabs">
    <li class="nav-item ">
        <a class="nav-link " href="{{ return_url }}">{{ _gettext('List') }}</a>
    </li>
    {%- if admin_view.can_create -%}
    <li class="nav-item">
        <a class="nav-link " href="{{ get_url('.create_view', url=return_url) }}">{{ _gettext('Create') }}</a>
    </li>
    {%- endif -%}
        <li class="nav-item ">
        <a class="nav-link active" href="javascript:void(0)">{{ _gettext('Edit') }}</a>
    </li>
    {%- if admin_view.can_view_details -%}
    <li class="nav-item ">
        <a class="nav-link "  href="{{ get_url('.details_view', id=request.args.get('id'), url=return_url) }}">{{ _gettext('Details') }}</a>
    </li>
    {%- endif -%}
  </ul>
  {% endblock %}

  {% block edit_form %}
    {{ lib.render_form(form, return_url, extra(), form_opts) }}
  {% endblock %}
{% endblock %}

{% block tail %}
    <script src="{{ url_for('static', filename='js/decode.js')}}" type="text/javascript"></script>

  {{ super() }}
  {{ lib.form_js() }}
{% endblock %}

from datetime import datetime
from flask_wtf import <PERSON>laskForm
from wtforms import TextAreaField, DecimalField, IntegerField, \
    BooleanField, DateTimeField, FloatField, StringField
from wtforms.validators import (DataRequired, Optional)
from app_models.reseller_models import Reseller
from wtforms import SelectField


class GenerateVoucherForm(FlaskForm):
    reseller_code = SelectField('Reseller code')
    voucher_name= TextAreaField('Voucher name', validators=[DataRequired()], render_kw={"placeholder": "Voucher name"})
    quantity = IntegerField('Quantity', validators=[DataRequired()], render_kw={"placeholder": "Quantity"})
    amount = FloatField('Amount', validators=[DataRequired()], render_kw={"placeholder": "Amount"})
    currency = SelectField('Currency code', validators=[DataRequired()],
                           choices=[("USD", "USD")],
                           default="USD")
    #active = <PERSON>oleanField('Is active')
    status = SelectField('Status', validators=[DataRequired()],
                           choices=[("Active", "Active"), ("Used", "Used"),("Inactive", "Inactive") ],
                           default="Active")
    qr_code = BooleanField('QrCode generation', default=False)
    reason = TextAreaField('Reason', validators=[Optional()], render_kw={"placeholder": "Reason"})
    expiry_datetime = StringField('Expiry date')
    reseller_id=SelectField('Reseller Id')

    def __init__(self, *args, **kwargs):
        reseller_codes = [(item['reseller_code'], item['reseller_code']) for item in
                          Reseller.objects()]
        reseller_codes.insert(0, ("", 'None'))
        self.reseller_code.kwargs['choices'] = reseller_codes
        choices = [(str(item['id']), str(item['id']) + ' - ' + item['reseller_name']) for item in Reseller.objects()]
        choices.insert(0, ("", 'None'))
        self.reseller_id.kwargs['choices'] = choices
        FlaskForm.__init__(self, *args, **kwargs)


class PromoCodeForm(FlaskForm):
    promo_name= TextAreaField('Promo name', validators=[DataRequired()], render_kw={"placeholder": "Promo name"})
    quantity=IntegerField('Quantity', validators=[DataRequired()], render_kw={"placeholder": "Quantity"})
    amount= IntegerField('Amount', validators=[DataRequired()],
                                    render_kw={"placeholder": "Amount"})
    #is_active=BooleanField('Is active')
    status = SelectField('Status', validators=[DataRequired()],
                         choices=[("Active", "Active"), ("Used", "Used"), ("Inactive", "Inactive")],
                         default="Active")
    create_datetime = StringField('Create Date', default=datetime.utcnow().now())
    expiry_datetime = StringField('Expiry Date', validators=[DataRequired()])



from app_main.views.main_views import ModelView1
import flask_admin as admin
from flask_admin import expose
from app_main.views import reseller_form as forms
from flask import flash
import datetime
import string
import random
from flask_login import current_user
from app_models import main_models
from flask_admin.babel import gettext, ngettext, lazy_gettext
from instance import consumer_config as instance_config
from app_helpers.encrypt_helper import Crypt

REPORT = 'app_main/exported_files'
REPORT_PROMOCODE = 'app_main/exported_files/report_promo_code.csv'
FILEPATH = 'app_main/exported_files/qrCode'
EXPORT_FOOLDER_ = 'app_main/exported_files/'


def random_voucher(length):
    ran = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
    return str(ran)





def prepare_runnable_scripts(script, redeem_name, amount, status, create_datetime,
                             expiry_datetime,
                             quantity, reseller_code, reason, generate_qr_code, reseller_id ):
    try:
        doc = {"script": script,"redeem_name":redeem_name, "datetime": datetime.datetime.utcnow(),
               "amount": amount, "status": status, "create_datetime": create_datetime,
               "expiry_datetime": expiry_datetime, "reseller_code": reseller_code, "reason": reason,
               "state": "Waiting", "informer": current_user.email, "quantity": quantity,
               "generate_qr_code": generate_qr_code, "reseller_id":reseller_id}

        return main_models.GenerateCsv(**doc).save()
    except Exception as e:
        print("Exception in prepare_runnable_scripts as ", str(e))
        return False


def prepare_runnable_script_action_for_csv(action_name, redeem_name, amount, status, create_datetime,
                                           expiry_datetime,
                                           quantity,reseller_code=None, reason=None, generate_qr_code=None, reseller_id= None
                                          ):
    try:
        result = prepare_runnable_scripts(action_name, redeem_name, amount, status, create_datetime,
                                          expiry_datetime,
                                          quantity, reseller_code, reason, generate_qr_code, reseller_id)

        if not result:
            error_text = "Failed to plan a runnable batch for action: {}; " \
                         "you can delete an old action ".format(action_name)
            flash(gettext(error_text))
        else:
            flash(gettext(
                'Planning a runnable batch was done successfully. Please wait until it finishes to get the report from ' + REPORT))
    except Exception as e:

        flash(gettext('Exception as ' + str(e)))


class Reseller(ModelView1):
    column_list = ("reseller_code",
                   'reseller_name', 'reseller_type', 'support_topup', 'is_active', 'datetime', 'balance',
                   'currency_code', 'rate_revenue')

    column_filters = column_list
    pass


class GenerateVoucher(admin.BaseView):
    @expose('/', methods=('GET', 'POST'))
    def index(self):
        form = forms.GenerateVoucherForm()
        if form.validate_on_submit():
            try:
                if prepare_runnable_script_action_for_csv("GenerateVoucherCode",
                                                          form.voucher_name.data,
                                                          form.amount.data,
                                                          form.status.data,
                                                          datetime.datetime.now(),
                                                          form.expiry_datetime.data,
                                                          form.quantity.data,
                                                          form.reseller_code.data,
                                                          form.reason.data,
                                                          form.qr_code.data,
                                                          form.reseller_id.data
                                                          ):
                    flash('Please check runnable script ', "success")

                return self.render('admin/voucher.html', form=form)

            except Exception as e:
                print("Exception as ", e)

        return self.render('admin/voucher.html', form=form)


class PromoCodeGeneration(admin.BaseView):
    @expose('/', methods=('GET', 'POST'))
    def index(self):

        form = forms.PromoCodeForm()
        if form.validate_on_submit():
            try:
                if prepare_runnable_script_action_for_csv("GeneratePromoCode",
                                                          form.promo_name.data,
                                                          form.amount.data, form.status.data,
                                                          form.create_datetime.data,
                                                          form.expiry_datetime.data,
                                                          form.quantity.data, ):
                    flash('Please check runnable script ', "success")

                return self.render('admin/promo_code.html', form=form)

            except Exception as e:
                print("Exception as ", e)

        return self.render('admin/promo_code.html', form=form)

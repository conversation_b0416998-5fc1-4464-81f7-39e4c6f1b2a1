import datetime
import os
import flask_admin as admin
import pandas
from cryptography.fernet import Fernet
from wtforms.fields import Password<PERSON>ield, <PERSON><PERSON>ield, StringField
from flask import redirect, flash, Markup
from flask import request, url_for
from flask_admin import expose
from flask_admin.actions import action
from flask_admin.babel import gettext, ngettext, lazy_gettext
from flask_admin.base import MenuLink
from flask_admin.contrib.mongoengine import Model<PERSON>iew
from flask_admin.contrib.mongoengine.helpers import format_error
from flask_admin.form import FormOpts
from flask_admin.helpers import (get_redirect_target)
from flask_admin.model.helpers import get_mdict_item_or_list
from flask_admin.model.template import TemplateLinkRowAction, EndpointLinkRowAction, LinkRowAction
from flask_bcrypt import generate_password_hash
from flask_login import current_user
from mongoengine.errors import NotUniqueError
from pymongo.errors import Duplicate<PERSON>ey<PERSON>rror
from wtforms.fields import Pass<PERSON><PERSON>ield
from wtforms.validators import ValidationError
from instance import consumer_config as instance_config
from app_helpers.encrypt_helper import Crypt
from app_main.views import admin_forms
from app_helpers import db_helper, encrypt_helper
from app_helpers.admin_helper import password_check, show_count
from app_helpers.db_helper import check_access, prepare_runnable_scripts, get_vendor_name, update_runnable_scripts, \
    reset_to_waiting_scripts_states, update_generate_csv, reset_to_waiting_generate_csv
from app_helpers.main_helper import random_password, get_dates
from app_models import main_models
import random
from flask_admin.contrib.fileadmin import FileAdmin


def prepare_form_app_list(form):
    form.op_name.choices = [(obj.op_name, obj.op_name) for obj in main_models.OperatorList.objects()]
    return form


def prepare_form_promo_code(form):
    promo_code = form.promo_code.data
    print("promo_code", promo_code)
    str_key = instance_config.promo_code_key
    encrypt_helper = Crypt()
    decrypted_promo_code = encrypt_helper.decrypt(promo_code, str_key)
    print("decrypted_promo_code", decrypted_promo_code)
    form.promo_code.data = decrypted_promo_code
    return form


def prepare_runnable_script_action(model, action_name, id):
    try:
        vendor_name = get_vendor_name(id)
        result = prepare_runnable_scripts(script=action_name, vendor_name=vendor_name)
        if not result:
            error_text = "Failed to plan a runnable batch for action: {}; " \
                         "you can delete an old action ".format(action_name)
            flash(gettext(error_text))
        else:

            flash(gettext('Planning a runnable batch was done successfully.'))
    except Exception as e:
        flash(gettext('Exception as ' + str(e)))


def user_can_access(reference):
    current = main_models.User.objects(id=current_user.get_id()).first()
    reference.can_export = current.can_export
    reference.can_create = current.can_create
    reference.can_edit = current.can_edit
    reference.can_delete = current.can_delete
    reference.can_export = current.can_export
    return current


def print_exception(e, other_info="Exception as: "):
    print(other_info + str(e))


class AuthenticatedMenuLink(MenuLink):
    def is_accessible(self):
        try:
            current = main_models.User.objects(id=current_user.get_id()).first()
            return current.is_admin
        except Exception as e:
            print_exception(e)
            return False


class ModelViewWithDuplicate(ModelView):
    page_size = 500
    column_extra_row_actions = [
        TemplateLinkRowAction('row_actions.duplicate_row', gettext('Duplicate Record'))
    ]

    def create_model(self, form):
        model = None
        try:

            model = self.model()
            form.populate_obj(model)
            self._on_model_change(form, model, True)
            model.save()

        except (NotUniqueError, DuplicateKeyError) as e:

            flash(gettext('Failed to create record. %(error)s',
                          error=format_error(e)),
                  'error')

        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash(gettext('Failed to create record. %(error)s',
                              error=format_error(ex)),
                      'error')

            return False
        else:
            self.after_model_change(form, model, True)
        return model

    @expose('/duplicate/', methods=('GET', 'POST'))
    def duplicate_view(self):
        """
            Edit model view
        """
        return_url = get_redirect_target() or self.get_url('.index_view')
        if not self.can_edit:
            return redirect(return_url)

        if get_mdict_item_or_list(request.args, 'id') is None:
            return redirect(return_url)
        id_object = get_mdict_item_or_list(request.args, 'id')

        model = self.get_one(id_object)

        if model is None:
            flash(gettext('Record does not exist.'), 'error')
            return redirect(return_url)

        form = self.edit_form(obj=model)
        if not hasattr(form, '_validated_ruleset') or not form._validated_ruleset:
            self._validate_form_instance(
                ruleset=self._form_edit_rules, form=form)

        if self.validate_form(form):
            model = self.create_model(form)
            if model:
                flash(gettext('Record was successfully created.'), 'success')
                return redirect(self.get_url('.duplicate_view', url=return_url))

        if request.method == 'GET' or form.errors:
            self.on_form_prefill(form, id_object)

        form_opts = FormOpts(widget_args=self.form_widget_args,
                             form_rules=self._form_edit_rules)

        if self.edit_modal and request.args.get('modal'):
            template = self.edit_modal_template
        else:
            template = 'admin/model/duplicate.html'

        return self.render(template,
                           model=model,
                           form=form,
                           form_opts=form_opts,
                           return_url=return_url)


class ModelViewWithImport(ModelViewWithDuplicate):

    @expose('/import', methods=['POST'])
    def import_csv(self):
        name = self.name
        name = name.lower().replace(" ", "")
        redirect_response = redirect(url_for('{}.index_view'.format(name)))

        if 'file' not in request.files:
            flash('No file part', 'error')
        else:
            uploaded_file = request.files['file']
            if uploaded_file.filename == '':
                flash('No selected file', 'error')
            else:
                try:
                    df = pandas.read_csv(uploaded_file, encoding='ISO-8859-1')
                    df["datetime"] = datetime.datetime.utcnow()
                    df.columns = self.column_list
                    data = df.to_dict(orient='records')
                    class_objects = []
                    model_class = self.model
                    for record in data:
                        class_objects.append(model_class(**record))
                    model_class.objects.insert(class_objects)
                except Exception as e:
                    print("exception in file upload " + str(e))
        return redirect_response


class ModelView1(ModelViewWithDuplicate):
    page_size = 100

    def is_accessible(self):
        try:
            current = user_can_access(self)
            return current.is_view1 or current.is_admin
        except Exception as e:
            print_exception(e)
            return False


class ModelViewAdmin(ModelViewWithDuplicate):
    page_size = 500

    def is_accessible(self):
        try:
            current = main_models.User.objects(
                id=current_user.get_id()).first()
            self.can_create = current.can_create
            self.can_edit = 1
            self.can_delete = current.can_delete
            self.can_export = current.can_export
            return current.is_admin
        except Exception as e:
            print_exception(e)
            return False

    def on_model_change(self, form, model, is_created):

        # If the password field isn't blank...
        if len(model.password):
            print(model.password)
            # ... then encrypt the new password prior to storing it in the database. If the password field is blank,
            # the existing password in the database will be retained.
            try:
                new_pass = generate_password_hash(model.password)
                print(str(new_pass))
                print(type(new_pass))
                model.password = new_pass.decode("utf-8")
            except Exception as e:
                print_exception(e)


class User(ModelViewAdmin):
    form_extra_fields = {
        'new_password': PasswordField('new password')
    }
    form_excluded_columns = ('password', 'last_login')

    def get_query(self):
        return super().get_query()

    def get_count_query(self):
        return self.get_query().count()

    @action('reset_password', 'Reset Password', 'Are you sure you want to reset password?')
    def action_new(self, ids):
        try:
            for id_ in ids:
                user = self.model.objects(id=id_).first()
                new_pass = random_password(12)
                user.password = generate_password_hash(new_pass).decode('utf-8')
                user.is_expired = True
                user.save()
                flash(Markup('the new password of user ' + user.email + ' is ' + new_pass))
        except Exception as e:
            print("Exception in generate_output as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to reset password. %(error)s', error=str(e)),
                  'error')

    def on_model_change(self, form, model, is_created):
        if is_created or (form.new_password.data != '' and model.password != form.new_password.data):
            if not password_check(form.new_password.data):
                raise ValidationError("Must be a valid password format!")
            else:
                model.password = generate_password_hash(form.new_password.data).decode('utf-8')
                del model.new_password
                model.save()


class ModelViewWithImport(ModelViewWithDuplicate):

    def is_accessible(self):
        try:
            current = main_models.User.objects(
                id=current_user.get_id()).first()
            self.can_export = current.can_export
            self.can_create = current.can_create
            self.can_edit = current.can_edit

            self.can_delete = current.can_delete
            self.can_export = current.can_export
            self.can_duplicate = 0
            if current.can_import:
                self.list_template = 'admin/import_list.html'
            return current.is_view1 or current.is_admin
        except Exception as e:
            return False

    @expose('/import', methods=['POST'])
    def import_csv(self):
        name = self.name
        name = name.lower().replace(" ", "")
        redirect_response = redirect(url_for('{}.index_view'.format(name)))

        if 'file' not in request.files:
            flash('No file part', 'error')
        else:
            uploaded_file = request.files['file']
            if uploaded_file.filename == '':
                flash('No selected file', 'error')
            try:
                add_data = True
                print("before panda ready")
                df = pandas.read_csv(uploaded_file, encoding='UTF-8')
                df.columns = self.column_list
                data = df.to_dict(orient='records')
                class_objects = []
                model_class = self.model
                if add_data:
                    for record in data:
                        class_objects.append(model_class(**record))
                    model_class.objects.insert(class_objects)


            except Exception as e:
                print("exception in file upload " + str(e))
        return redirect(url_for('{}.index_view'.format(name)))


class ModelView1(ModelViewWithDuplicate):
    page_size = 100

    def is_accessible(self):
        try:
            current = user_can_access(self)
            return current.is_view1 or current.is_admin
        except Exception as e:
            print_exception(e)
            return False


class ModelView2(ModelViewWithImport):
    page_size = 100

    def is_visible(self):
        try:
            if self.model.views:
                current, actions = check_access(current_user.get_id(), self.model.views)

                if current.access in actions.access:
                    return True
                return False
        except Exception as e:
            print_exception(e)
            return False

    def is_accessible(self):
        try:
            current = user_can_access(self)
            if current.can_import:
                self.list_template = 'admin/import_list.html'
            return current.is_view2 or current.is_admin
        except Exception as e:
            print_exception(e)
            return False


class ModelView3(ModelView):
    page_size = 1000

    def is_visible(self):
        try:
            if self.model.views:
                current, actions = check_access(current_user.get_id(), self.model.views)

                if current.access in actions.access:
                    return True
                return False
        except Exception as e:
            print_exception(e)
            return False

    def is_accessible(self):
        try:
            current = main_models.User.objects(
                id=current_user.get_id()).first()
            self.can_export = current.can_export
            self.can_create = 0
            self.can_edit = 1
            self.can_delete = 0
            return current.is_view3 or current.is_admin
        except Exception as e:
            print_exception(e)
            return False


class Actions(ModelView1):
    can_create = 0
    can_duplicate = 0
    can_delete = 1
    column_exclude_list = ('collection',)
    form_excluded_columns = ('collection', 'depend_on_tenant')
    form_widget_args = {
        'actions': {
            'disabled': True
        },
    }

    def is_accessible(self):
        try:

            current = main_models.User.objects(id=current_user.get_id()).first()
            return current.is_admin
        except Exception as e:
            print_exception(e)
            return False

    @action('show count',
            lazy_gettext('Show count'),
            lazy_gettext('Are you sure you want to show count selected records?'))
    def show_count(self, ids):
        try:

            count = show_count(self.model, ids, True)
            flash(ngettext('Record was successfully changed.',
                           '%(count)s records were successfully changed.',
                           count,
                           count=count), 'success')
        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash(gettext('Failed to change records. %(error)s', error=str(ex)),
                      'error')

    @action('hide count',
            lazy_gettext('Hide count'),
            lazy_gettext('Are you sure you want to hide count selected records?'))
    def hide_count(self, ids):
        try:

            count = show_count(self.model, ids, False)
            flash(ngettext('Record was successfully changed.',
                           '%(count)s records were successfully changed.',
                           count,
                           count=count), 'success')
        except Exception as ex:
            if not self.handle_view_exception(ex):
                flash(gettext('Failed to change records. %(error)s', error=str(ex)),
                      'error')


class Versions(ModelView1):
    column_filters = ('datetime', 'host_name', 'commit_hash',
                      'commit_msg', 'author_name', 'relative_date',
                      'affected_files')

    @action('restore', 'Restore', 'Going to Restore to the selected version')
    def restore(self, ids):
        commit_hash = self.model.objects(id=ids[0]).first().commit_hash
        os.system('echo "{}" > /usr/local/pyapps/rsp_extra/.version_hash'.format(commit_hash))


class VersionForm(admin.BaseView):
    @expose('/', methods=('GET', 'POST'))
    def index(self):
        form = admin_forms.VersionForm()
        if form.validate_on_submit():
            commit_hash = request.form.getlist('version')[0]
            os.system('echo "{}" > /usr/local/pyapps/rsp_extra/.version_hash'.format(commit_hash))
        return self.render('admin/version.html', form=form)


class DailyScripts(ModelView1):
    column_list = ('script', 'datetime', 'state', 'from_date', 'to_date', 'failure_reason', 'affected_rows')
    column_filters = ('datetime', 'state', 'from_date', 'to_date', 'failure_reason', 'affected_rows')
    pass


class RunnableScripts(ModelView1):
    column_filters = ('datetime', 'script', 'state', 'vendor_name', 'bundle_code',  'daily_used','profile_added',  'from_date','to_date', 'duration', 'end_date',
                      'affected_rows',  'success_rows',  'informer', 'acceptor','failure_reason', 'status', 'status_date')

    @action('Accept Waiting States', 'Accept selected waiting states from another user', '')
    def accept_waiting_states(self, ids):
        success, failed = update_runnable_scripts(ids)

        flash(Markup(str(success)
                     + ' successfully accepted states '
                     + str(failed)
                     + ' failed to accept states; note same user cannot request and accept'))

    @action('Reset to Waiting States', 'Reset Selected to Waiting states', '')
    def reset_to_waiting_states(self, ids):
        total = reset_to_waiting_scripts_states(ids)

        flash(Markup(str(total) + ' successfully reset states '))

    pass


class RunnableCSV(ModelView1):
    column_list = (
    'script', 'datetime', 'state', 'duration', 'end_date', 'redeem_name', 'quantity', 'amount', 'currency_code',
    'generate_qr_code', 'reseller_code', 'reason', 'expiry_datetime',
    'affected_rows', 'success_rows', 'informer', 'acceptor', 'failure_reason', 'status', 'status_date')

    column_filters = (
    'script', 'datetime', 'state', 'duration', 'end_date', 'redeem_name', 'quantity', 'amount', 'currency_code',
    'generate_qr_code', 'reseller_code', 'reason', 'expiry_datetime',
    'affected_rows', 'success_rows', 'informer', 'acceptor', 'failure_reason', 'status', 'status_date')


    PATH = 'app_main/exported_files'
    REPORT = 'app_main/exported_files/report_voucher_code.csv'
    REPORT_PROMOCODE = 'app_main/exported_files/report_promo_code.csv'

    # flash("Please wait until state = Finished to get the report from "+ PATH)

    @action('Accept Waiting States', 'Accept selected waiting states from another user', '')
    def accept_waiting_states(self, ids):
        success, failed = update_generate_csv(ids)

        flash(Markup(str(success)
                     + ' successfully accepted states '
                     + str(failed)
                     + ' failed to accept states; note same user cannot request and accept'))

    @action('Reset to Waiting States', 'Reset Selected to Waiting states', '')
    def reset_to_waiting_states(self, ids):
        total = reset_to_waiting_generate_csv(ids)

        flash(Markup(str(total) + ' successfully reset states '))

    pass


class Vendors(ModelView1):
    @action('RunUpdateProfiles', 'Run update profiles in the background', '')
    def run_update_profiles(self, ids):
        prepare_runnable_script_action(self.model, action_name="UpdateProfiles", id=ids[0])

    @action('RunSaveBundles', 'Run save bundles in the background', '')
    def run_save_bundles(self, ids):
        prepare_runnable_script_action(self.model, action_name="SaveBundles", id=ids[0])

    @action('RunSaveProfiles', 'Run save profiles in the background', '')
    def run_save_profiles(self, ids):
        prepare_runnable_script_action(self.model, action_name="SaveProfiles", id=ids[0])

    @action('RunAllocateProfiles', 'Run allocate profiles in the background', '')
    def run_allocate_profiles(self, ids):
        prepare_runnable_script_action(self.model, action_name="AllocateProfiles", id=ids[0])

    @action('RunSetProfiles', 'Run reset profiles in the background', '')
    def run_set_profiles(self, ids):
        prepare_runnable_script_action(self.model, action_name="ResetProfiles", id=ids[0])

    @action('RunUpdatePrices', 'Run update retail price in the background', '')
    def run_update_prices(self, ids):
        prepare_runnable_script_action(self.model, action_name="UpdatePrices", id=ids[0])

    @action('RunGetFlexiroamToken', 'Run get flexiroam token in the background', '')
    def run_get_flexiroam_token(self, ids):
        prepare_runnable_script_action(self.model, action_name="GetFlexiroamToken", id=ids[0])

    @action('RunVodafoneSyncProfilesCount', 'Run vodafone sync profiles count in the background', '')
    def run_vodafone_sync_profiles_count(self, ids):
        prepare_runnable_script_action(self.model, action_name="VodafoneSyncProfilesCount", id=ids[0])

    @action('RunGetMontyMobileToken', 'Run get monty mobile token in the background', '')
    def run_get_montymobile_token(self, ids):
        prepare_runnable_script_action(self.model, action_name="GetMontyMobileToken", id=ids[0])

    @action('RunAllocateProfilesBasedOnSupplyDemand', 'Run Allocate Profiles Based On Supply Demand in the background', '')
    def run_allocate_profiles(self, ids):
        prepare_runnable_script_action(self.model, action_name="AllocateProfilesBasedOnSupplyDemand", id=ids[0])

    column_list = ('vendor_name', 'is_active', 'bundles_count', 'sent_data','currency_exchange_rate')
    form_excluded_columns = ('vendor_prefix', 'vendor_suffix')


class BundleCategoriesVendor(ModelView1):
    column_list = ('vendor_name', 'category_id', 'category_name', 'is_region', 'is_active')
    column_filters = column_list


class Profiles(ModelView1):
    column_filters = ('vendor_name', 'sku', 'iccid', 'profile_names', 'availability', 'status')
    pass


class TopupBundles(ModelView1):
    column_list = (
        'vendor_name', 'iccid', 'email', 'bundle_code', 'topup_code', 'plan_uid', 'datetime', 'payment_date', 'status',
        'validy_date')
    column_filters = column_list
    pass


class RegionBundles(ModelView1):
    def get_query(self):
        objects = self.model.objects(bundle_category="region")
        return objects

    def get_count_query(self):
        return self.get_query().count()

    column_list = (
        'region_code', 'region_name', 'create_datetime', 'vendor_name', 'bundle_code', 'bundle_marketing_name',
        'bundle_name', 'bundle_vendor_code',
        'supplier_vendor', 'unit_price', 'retail_price', 'currency_code',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount', 'country_list',
        'country_code_list', 'category_name', 'bundle_category', 'profile_names', 'bundle_duration',
        'allocated_unit', 'consumed_unit', 'plan_type', 'activity_policy', 'top_up_plan', 'available_netwok',
        'is_active', 'deleted')

    column_filters = (
        'create_datetime', 'vendor_name', 'bundle_code', 'bundle_name', 'bundle_marketing_name', 'bundle_vendor_code',
        'supplier_vendor',
        'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount',
        'category_name', 'bundle_category', 'profile_names',
        'allocated_unit', 'consumed_unit',
        'is_active', 'deleted')

    @action('manually_inactive', 'Inactive manually', 'Are you sure you want to inactive bundles?')
    def action_manually_inactive(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = True
                bundle.is_active = False
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually inactive  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in manually_inactive as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to inactive bundle . %(error)s', error=str(e)),
                  'error')

    @action('manually_active', 'Activate bundles', 'Are you sure you want to activate bundles?')
    def action_manually_active(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = False
                bundle.is_active = True
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually active  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in action_manually_active as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to activate bundle . %(error)s', error=str(e)),
                  'error')


class GlobalBundles(ModelView1):
    def get_query(self):
        objects = self.model.objects(bundle_category="global")
        return objects

    def get_count_query(self):
        return self.get_query().count()

    column_list = (
        'create_datetime', 'vendor_name', 'bundle_code', 'bundle_name', 'bundle_marketing_name', 'bundle_vendor_code',
        'supplier_vendor', 'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount', 'country_list',
        'country_code_list', 'category_name', 'bundle_category', 'profile_names',
        'allocated_unit', 'consumed_unit', 'plan_type', 'activity_policy', 'top_up_plan', 'available_netwok',
        'is_active', 'rate_revenue', 'deleted')
    column_filters = (
        'create_datetime', 'vendor_name', 'bundle_code', 'bundle_name', 'bundle_vendor_code', 'supplier_vendor',
        'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount',
        'category_name', 'bundle_category', 'profile_names',
        'allocated_unit', 'consumed_unit',
        'is_active', 'deleted')
    form_columns = (
        'vendor_name', 'bundle_code', 'bundle_name', 'bundle_marketing_name', 'bundle_vendor_code', 'supplier_vendor',
        'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount',
        'category_name', 'bundle_category', 'profile_names', 'country_list',
        'country_code_list',
        'allocated_unit', 'consumed_unit',
        'is_active', 'deleted')

    @action('manually_inactive', 'Inactive manually', 'Are you sure you want to inactive bundles?')
    def action_manually_inactive(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = True
                bundle.is_active = False
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually inactive  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in manually_inactive as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to inactive bundle . %(error)s', error=str(e)),
                  'error')

    @action('manually_active', 'Activate bundles', 'Are you sure you want to activate bundles?')
    def action_manually_active(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = False
                bundle.is_active = True
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually active  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in action_manually_active as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to activate bundle . %(error)s', error=str(e)),
                  'error')


class CountryBundles(ModelView1):
    def get_query(self):
        objects = self.model.objects(bundle_category="country")
        return objects

    def get_count_query(self):
        return self.get_query().count()

    column_list = (
        'create_datetime', 'vendor_name', 'bundle_code', 'bundle_name', 'bundle_marketing_name', 'bundle_vendor_code',
        'supplier_vendor', 'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount', 'country_list',
        'country_code_list', 'category_name', 'bundle_category', 'profile_names',
        'allocated_unit', 'consumed_unit', 'plan_type', 'activity_policy', 'top_up_plan', 'available_netwok',
        'is_active', 'deleted')
    column_filters = (
        'create_datetime', 'vendor_name', 'bundle_code', 'bundle_name', 'bundle_vendor_code', 'supplier_vendor',
        'unit_price', 'retail_price', 'currency_code',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount', 'bundle_duration',
        'category_name', 'bundle_category', 'profile_names',
        'allocated_unit', 'consumed_unit',
        'is_active', 'deleted')
    form_columns = (
        'vendor_name', 'bundle_code', 'bundle_name', 'bundle_marketing_name', 'bundle_vendor_code', 'supplier_vendor',
        'unit_price', 'retail_price', 'currency_code', 'bundle_duration',
        'data_amount', 'fullspeed_data_amount', 'data_unit', 'validity_amount',
        'category_name', 'bundle_category', 'country_list',
        'country_code_list', 'profile_names',
        'allocated_unit', 'consumed_unit',
        'is_active', 'deleted')

    @action('manually_inactive', 'Inactive manually', 'Are you sure you want to inactive bundles?')
    def action_manually_inactive(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = True
                bundle.is_active = False
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually inactive  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in manually_inactive as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to inactive bundle . %(error)s', error=str(e)),
                  'error')

    @action('manually_active', 'Activate bundles', 'Are you sure you want to activate bundles?')
    def action_manually_active(self, ids):
        try:
            is_deleted = 0
            for id_ in ids:
                bundle = self.model.objects(id=id_).first()
                bundle.deleted = False
                bundle.is_active = True
                bundle.save()
                is_deleted = is_deleted + 1
            flash(Markup('the number of bundles manually active  ' + str(is_deleted)))
        except Exception as e:
            print("Exception in action_manually_active as: ", str(e))
            if not self.handle_view_exception(e):
                raise
            flash(gettext('Failed to activate bundle . %(error)s', error=str(e)),
                  'error')


class OperatorList(ModelView1):
    column_list = ('op_name', 'cert_name', 'sk_name', 'profile_type', 'from_email', 'op_country')
    form_extra_fields = {
        'new_op_fci': StringField('new call request identifier'),
    }
    form_excluded_columns = ('op_fci', 'op_salt', 'salt', 'op_psalt')

    def get_query(self):
        return super().get_query()

    def get_count_query(self):
        return self.get_query().count()

    def on_model_change(self, form, model, is_created):
        if is_created or (form.new_op_fci.data != '' and model.op_fci != form.new_op_fci.data):
            '''public_key, private_key =  rsa.newkeys(512)
            model.op_salt=str(public_key)
            model.op_psalt = str(private_key)
            crypto = rsa.encrypt(form.new_op_fci.data.encode('utf-8').strip(), public_key)
            model.op_fci = crypto
            del model.new_op_fci
            model.save()'''
            key = Fernet.generate_key()
            f = Fernet(key)
            print("f ", f, type(f))
            print("key ", key, type(key))
            model.salt = key

            encrypted_data = f.encrypt(bytes(form.new_op_fci.data, 'utf-8'))

            model.op_fci = encrypted_data
            del model.new_op_fci
            model.save()


class EmailSettings(ModelView1):
    pass


class LpaAutoTests(ModelView1):
    pass


class PromoCode(ModelView1):
    column_list = (
        'promo_code', 'amount', 'currency_code', 'is_active', 'create_date', 'expiry_datetime')

    def edit_form(self, obj=None):
        form = super().edit_form(obj)
        return prepare_form_promo_code(form)

    pass


class NotificationLogs(ModelView1):
    column_list = ('notification_id', 'datetime', 'email', 'iccid', 'transaction', 'status', 'transaction_message',
                   'transaction_status')
    column_filters = column_list
    pass


class HistoryLogs(ModelView1):
    pass


class UserIccid(ModelView1):
    column_list = ("bundle_code",
                   'email', 'iccid', 'payment_otp', 'cancel_otp', 'activation_code', 'counter', 'wp_counter',
                   'expiry_date',
                   'smdp_address', 'status', 'plan_uid', 'msisdn')
    column_filters = column_list
    pass


class StripePaymentIntent(ModelView1):
    column_list = (
        'email', 'created', 'updated', 'payment_intent_client_secret', 'payment_intent_id', 'bundle', 'status_string',
        'status')
    pass


class TransactionLogs(ModelView1):
    column_list = (
        'order_number', 'transaction_status', 'bundle_status', 'datetime', 'transaction_message', 'error_message')
    column_filters = column_list
    pass


class UserBundleLog(ModelView1):
    column_list = (
        'email', 'wp_code_pin', 'otp', 'cancel_otp', 'qr_code_pin', 'payment_status', 'payment_topup', 'bundle',
        'bundle_code', 'topup_code', 'country_code', 'data_code', 'currency_code', 'amount', 'version_token',
        'validity_days', 'order_number', 'payment_date', 'validy_date')
    column_filters = (
        'email', 'wp_code_pin', 'otp', 'cancel_otp', 'qr_code_pin', 'payment_status', 'payment_topup', 'bundle',
        'bundle_code', 'topup_code', 'country_code', 'data_code', 'currency_code', 'amount', 'version_token',
        'validity_days', 'payment_date', 'validy_date')


class Settings(ModelView1):
    column_list = (
        'contact_email', 'esim_email', 'merchant_key', 'merchant_password', 'fcm_registration', 'whatsapp_misisdn')
    pass


class KeycloackSettings(ModelView1):
    form_overrides = {'op_name': SelectField}

    def create_form(self, obj=None):
        form = super().create_form(obj)
        return prepare_form_app_list(form)

    def edit_form(self, obj=None):
        form = super().edit_form(obj)
        return prepare_form_app_list(form)

    pass


class UserBundle(ModelView1):
    column_list = (
        'email', 'wp_code_pin', 'otp', 'qr_code_pin', 'payment_status', 'payment_topup', 'bundle', 'bundle_code',
        'topup_code', 'currency_code', 'amount', 'payment_date', 'validy_date')
    column_filters = (
        'email', 'wp_code_pin', 'otp', 'qr_code_pin', 'payment_status', 'payment_topup', 'bundle', 'bundle_code',
        'currency_code', 'amount', 'payment_date', 'validy_date')


class ExportedFiles(FileAdmin):
    pass


class OldData(admin.BaseView):
    @expose('/', methods=('GET', 'POST'))
    def index(self):
        form = admin_forms.OldData()
        if form.validate_on_submit():

            try:
                if prepare_runnable_scripts(form.script_name.data, '', form.from_date.data, form.to_date.data):
                    flash('Please check runnable script', "success")
                    return self.render('admin/model/old_data.html', form=form)
            except Exception as e:
                print("exception " + str(e))

        return self.render('admin/model/old_data.html', form=form)

from flask_wtf import F<PERSON>kForm
from datetime import datetime
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, TextAreaField, SelectField, FileField, DateTimeField
from wtforms.validators import (DataRequired, Email)
from wtforms.widgets.core import CheckboxInput


class LoginForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()], render_kw={"placeholder": "Email"})
    password = PasswordField('Password', validators=[DataRequired()], render_kw={"placeholder": "Password"})


class LoginFactorForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()], render_kw={"placeholder": "Email"})
    password = PasswordField('Password', validators=[DataRequired()], render_kw={"placeholder": "Password"})


class SetForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Em<PERSON>()], render_kw={"placeholder": "Email"})
    password = PasswordField('Password', validators=[DataRequired()], render_kw={"placeholder": "Password"})
    new_password = PasswordField('NewPassword', validators=[DataRequired()], render_kw={"placeholder": "New Password"})
    confirm_password = PasswordField('ConfirmPassword', validators=[DataRequired()],
                                     render_kw={"placeholder": "Confirm Password"})


class TenantForm(FlaskForm):
    tenant = SelectField('Tenant', render_kw={"placeholder": "Tenant"})


class UpdateVersionForm(FlaskForm):
    version = StringField('Version Code', validators=[DataRequired()], render_kw={"placeholder": "version code"})


class ProfileMetaDecoded(FlaskForm):
    profile_meta = TextAreaField('Profile meta', validators=[DataRequired()],
                                 render_kw={"placeholder": "Profile meta decoded"})
    decoded_meta = TextAreaField('Decoded meta', render_kw={"placeholder": "Profile meta decoded", 'readonly': True})


class DecodeInput(FlaskForm):
    data_to_decode = TextAreaField('Data to decode', validators=[DataRequired()],
                                   render_kw={"placeholder": "Data to decode"})
    encoded_data = TextAreaField('Encoded data',
                                 render_kw={"placeholder": "Encoded Data", 'readonly': True})


class Converter(FlaskForm):
    input_data = TextAreaField('Input data', validators=[DataRequired()],
                               render_kw={"placeholder": "Input data (Hex, Base 64 or Asn1 Format)"})
    file_to_import = FileField('Input file',  render_kw={"placeholder": "Input data (Hex, Base 64 or Asn1 Format)"})

    uart0_en = CheckboxInput('Enable UART0',)

    input_data_type = SelectField('Input Data type', validators=[DataRequired()],
                                  choices=[("ProfileElement", "ProfileElement"),
                                           ("StoreMetadataRequest", "StoreMetadataRequest"),
                                           ("EUICCResponse", "EUICCResponse"),
                                           ("PprIds", "PprIds"),
                                           ("EUICCInfo1", "EUICCInfo1"),
                                           ])
    input_data_format = SelectField('Input Data format', validators=[DataRequired()],
                                    choices=[("der", "der"), ("asn1", "asn1")])
    output_data_format = SelectField('Output Data format', validators=[DataRequired()],
                                     choices=[("asn1", "asn1"), ("der", "der"), ("get_val", "get_val"),
                                              ("get_val_paths", "get_val_paths"), ("get_proto", "get_proto"),
                                              ("json", "json"), ("aper", "aper"), ("der_ws", "der_ws"), ("ber", "ber"),
                                              ("ber_ws", "ber_ws"), ("cer", "cer"), ("cer_ws", "cer_ws"),
                                              ("jer", "jer"), ("uper", "uper")])
    output_data = TextAreaField('Output data', render_kw={"placeholder": "Output Data (Hex, Base 64 or Asn1 Format)",
                                                          'readonly': True})


class VersionForm(FlaskForm):
    version = StringField('Version Hash', validators=[DataRequired()], render_kw={"placeholder": "version hash"})


class OldData(FlaskForm):
    from_date =StringField('Starting date')
    to_date =StringField('End date' )
    script_name = SelectField('Script name', choices=[("SendUserBundles", "Send user bundle"),
                                           ("SendBundleInventory", "SendBundleInventory"),
                                           ("SendProfiles", "Send profiles"),
                                           ("SendUsersRegistred", "Users registred"),
                                            ('BuyBundle', "bundle history"),
                                            ('BuyTopup', "bundle topup  history"),
                                            ('ReSaveOldData', "Re-Save Old Data")
                                           ],validators=[DataRequired()])


import atexit
import datetime
import logging
import os.path as op
from datetime import <PERSON><PERSON><PERSON>

import flask_admin
from app_models import main_models, consumer_models
from apscheduler.events import EVENT_JOB_MAX_INSTANCES
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.schedulers.background import BackgroundScheduler
from bson import ObjectId
from flask import Flask, redirect, url_for, flash, request, session, json
from flask_bootstrap import <PERSON><PERSON><PERSON>
from flask_login import LoginManager, current_user
from flask_mongoengine import MongoEngine
from flask_wtf.csrf import CSRFProtect

from app_helpers import cron_helpers, script_run, main_helper, generate_csv
from app_main.views import (
    admin_views,
    reseller_views,
    main_views,
    consumer_views,
    mobiles as mobiles_views,
)
from instance import consumer_config as instance_config

# Configure the logging format
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(funcName)s() - Line:%(lineno)d - %(message)s",
    level=logging.DEBUG,
)


class JSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        if isinstance(o, datetime.datetime):
            return str(o)
        return json.JSONEncoder.default(self, o)


try:
    import os

    os.system("/usr/local/pyapps/rsp_extra/.update_version.sh")
    with open("/usr/local/pyapps/rsp_extra/.version") as file:
        rsp_version_file = file.read()
except Exception as e:
    rsp_version_file = ""
    print("exception for rsp version ", str(e))
    # rsp_version_file = 'unknown_version123'+str(e)

app = Flask(__name__)
csrf = CSRFProtect()

app.config["FLASK_ADMIN_FLUID_LAYOUT"] = True
app.config[
    "SECRET_KEY"
] = "asdfkljasdfasdnfaslkdjf10293ruaposijdfljkAJADFasdflkjasdflkajsdlfkjasdflkjasdfnasdf"
app.config["FLASK_ADMIN_SWATCH"] = "pulse"
app.config["MONGODB_HOST"] = instance_config.new_host_

if instance_config.ENVIRONMENT != "production":
    app.config["MONGODB_SETTINGS"] = instance_config.lst_db_

if app.json_encoder is None:
    app.json_encoder = JSONEncoder

try:
    mongo_db = MongoEngine()
    mongo_db.init_app(app=app)

except Exception as e:
    logging.error("COUDLN'T ESTABLISH DATABASE CONNECTION %s", str(e))

Bootstrap(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = "login"
login_manager.refresh_view = "login"
login_manager.needs_refresh_message = u"Session timeout, please re-login"
login_manager.needs_refresh_message_category = "info"
csrf.init_app(app)


@login_manager.user_loader
def load_user(user_id):
    try:
        return main_models.User.objects(id=user_id).first()
    except Exception as e:
        print("exception ", e)
    return False


@app.errorhandler(Exception)
def server_error(err):
    return "Error.. Please send this error to Support or DevTeam: " + str(err), 500


@app.after_request
def after_request_func(response):
    try:
        if response.status_code == 403:
            flash("You don't have access to this link!", "success")
            return redirect(url_for("admin.login_view", next=request.full_path))
        session.permanent = True
        app.permanent_session_lifetime = timedelta(minutes=20)
        if current_user.is_authenticated:
            user = main_models.User.objects(id=current_user.get_id()).first()
            if not user or user.is_expired:
                return redirect(url_for("admin.logout_view"))
        return response
    except Exception as e:
        print("exception ,", e)
        return response


def job_already_running(event):
    print("Job already running! {}".format(event))


date1, date2, date_time = main_helper.get_pday_time_string_format(1)

jobstores = {
    "default": MongoDBJobStore(
        database=instance_config.mongo_alias,
        username=instance_config.mongo_username,
        password=instance_config.mongo_password,
        host=[str(instance_config.mongo_host) + ":" + str(instance_config.mongo_port)],
    )
}

scheduler = BackgroundScheduler({"apscheduler.jobstores.mongo": {"type": "mongodb"}})

scheduler.configure(jobstores=jobstores)
scheduler.configure()
scheduler.add_listener(job_already_running, EVENT_JOB_MAX_INSTANCES)

scheduler.add_job(
    func=script_run.run_cron_jobs_runnable,
    trigger="cron",
    minute="*/5",
    replace_existing=True,
    id="run_cron_jobs_runnable",
)

scheduler.add_job(
    func=generate_csv.run_cron_jobs_runnable_csv,
    trigger="cron",
    minute="*/15",
    replace_existing=True,
    id="run_cron_jobs_runnable_csv",
)

scheduler.add_job(
    cron_helpers.montymobile_save_token_,
    trigger="cron",
    max_instances=1,
    hour="*/5",
    replace_existing=True,
    id="montymobile_save_token_",
)

scheduler.add_job(
    func=cron_helpers.save_vodafone_bundles,
    trigger="cron",
    max_instances=1,
    hour="23",
    minute="10",
    replace_existing=True,
    id="vodafone_save_bundles",
)

scheduler.add_job(
    func=cron_helpers.indosat_save_bundles,
    trigger="cron",
    max_instances=1,
    hour="23",
    minute="10",
    replace_existing=True,
    id="indosat_save_bundles",
)

scheduler.add_job(
    func=cron_helpers.indosat_save_profiles,
    trigger="cron",
    max_instances=1,
    hour="*/23",
    replace_existing=True,
    id="indosat_save_profiles",
)

scheduler.add_job(
    func=cron_helpers.flexi_save_bundles,
    trigger="cron",
    max_instances=1,
    hour="23",
    minute="40",
    replace_existing=True,
    id="flexi_save_bundles",
)

scheduler.add_job(
    func=cron_helpers.montymobile_save_bundles,
    trigger="cron",
    max_instances=1,
    hour="12",
    minute="40",
    replace_existing=True,
    id="montymobile_save_bundles",
)

scheduler.add_job(
    func=cron_helpers.montymobile_save_profiles,
    trigger="cron",
    max_instances=1,
    hour="22",
    minute=10,
    replace_existing=True,
    id="montymobile_save_profiles",
)

scheduler.add_job(
    func=cron_helpers.flexi_save_profiles,
    trigger="cron",
    hour="23",
    minute="40",
    replace_existing=True,
    id="flexi_save_profiles",
)

scheduler.add_job(
    func=cron_helpers.flexiroamv2_allocate_profiles,
    trigger="cron",
    hour="23",
    minute="55",
    replace_existing=True,
    id="flexiroamv2_allocate_profiles",
)

scheduler.add_job(
    func=cron_helpers.vodafone_save_profiles,
    trigger="cron",
    hour="23",
    minute="30",
    replace_existing=True,
    id="vodafone_save_profiles",
)

scheduler.add_job(
    func=cron_helpers.expire_scripts,
    trigger="cron",
    hour="23",
    minute="15",
    replace_existing=True,
    id="expire_scripts",
)

scheduler.add_job(
    func=cron_helpers.rest_profiles,
    trigger="cron",
    hour="*/3",
    replace_existing=True,
    id="rest_profiles",
)

scheduler.add_job(
    func=cron_helpers.expire_bundles,
    trigger="cron",
    hour="*/3",
    replace_existing=True,
    id="expire_bundles",
)

scheduler.add_job(
    func=cron_helpers.check_profile_expiry,
    trigger="cron",
    hour="23",
    minute="40",
    replace_existing=True,
    id="check_profile_expiry",
)

scheduler.add_job(
    func=cron_helpers.customer_feedback_send_email,
    trigger="cron",
    hour="2",
    minute="1",
    replace_existing=True,
    id="customer_feedback_send_email",
    args=(app,),
)

scheduler.add_job(
    func=cron_helpers.notify_profile_before_expiry,
    trigger="cron",
    hour="23",
    minute="25",
    replace_existing=True,
    id="notify_profile_before_expiry",
)

scheduler.add_job(
    func=cron_helpers.esimgo_save_profiles,
    trigger="cron",
    hour="23",
    minute="25",
    max_instances=1,
    replace_existing=True,
    id="esimgo_save_profiles",
)

scheduler.add_job(
    func=cron_helpers.flexi_check_available_profiles,
    trigger="cron",
    hour="*/2",
    minute="1",
    replace_existing=True,
    id="check_available_profiles",
)

scheduler.add_job(
    func=cron_helpers.notify_system_with_inventory_details,
    trigger="cron",
    hour="*/12",
    replace_existing=True,
    id="notify_system_with_inventory_details",
)

scheduler.add_job(
    func=cron_helpers.save_play_integrity_token,
    trigger="cron",
    hour="*/10",
    replace_existing=True,
    id="save_play_integrity_token",
)

scheduler.add_job(
    func=cron_helpers.refill_old_bundles_flexiroam,
    trigger="cron",
    max_instances=1,
    hour="*/23",
    replace_existing=True,
    id="refill_old_bundles_flexiroam",
)

scheduler.add_job(
    func=cron_helpers.get_all_monty_reseller_bundles,
    trigger="cron",
    max_instances=1,
    minute="*/10",
    replace_existing=True,
    id="get_all_monty_reseller_bundles",
)

scheduler.add_job(
    func=cron_helpers.run_manage_subscriptions,
    trigger="cron",
    max_instances=1,
    day="*/1",
    replace_existing=True,
    id="outlook_webhook_subscriptions",
)
scheduler.start()
admin = flask_admin.Admin(
    app=app,
    name=rsp_version_file,
    index_view=admin_views.AppAdminView(url="/", name="Dashboard"),
    template_mode="bootstrap4",
)

scheduler.add_job(
    func=cron_helpers.get_all_monty_reseller_bundles,
    trigger="cron",
    max_instances=1,
    minute="*/10",
    replace_existing=True,
    id="get_all_monty_reseller_bundles",
)

scheduler.add_job(
    func=cron_helpers.retry_failed_reseller_notification,
    trigger="cron",
    max_instances=1,
    minute="*/10",
    replace_existing=True,
    id="retry_failed_reseller_notification",
)

#telkomsel schedulers
scheduler.add_job(
    func=cron_helpers.try_first_time_notification,
    trigger="cron",
    max_instances=1,
    minute="*/10",
    replace_existing=True,
    id="try_first_time_notification",
)

#   main common views
admin.add_view(main_views.Vendors(consumer_models.Vendors, category="Admin"))
admin.add_view(
    main_views.RunnableScripts(main_models.RunnableScripts, category="Admin")
)
admin.add_view(main_views.DailyScripts(main_models.DailyScripts, category="Admin"))
admin.add_view(
    main_views.OldData(category="Admin", name="Generate old data", endpoint="old_data")
)
admin.add_view(main_views.RunnableCSV(main_models.GenerateCsv, category="Admin"))

#   reseller views
admin.add_view(
    reseller_views.GenerateVoucher(
        category="Reseller", name="Generate voucher Code", endpoint="generate_voucher"
    )
)
admin.add_view(
    reseller_views.PromoCodeGeneration(
        category="Reseller", name="Generate Promo Code", endpoint="promo_code"
    )
)

path = op.join(op.dirname(__file__), "exported_files")
admin.add_view(
    main_views.ExportedFiles(path, name="Exported Files", category="Reseller")
)
atexit.register(lambda: scheduler.shutdown())

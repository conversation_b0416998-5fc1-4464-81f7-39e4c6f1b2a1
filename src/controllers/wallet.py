from src.global_helpers.utils import Endpoint
from src.global_helpers.wrappers import try_except, validate_keycloak_token, validate_permissions
from flask import request
from src.services.wallet_helpers import get_rewards_history, validate_promo_code_and_referral_code, redeem


class Redeem(Endpoint):
    def __init__(self):
        super(Redeem, self).__init__()

    @validate_keycloak_token
    @try_except
    def post(self):
        wallet_input = request.get_json()
        return self.make_http_response(redeem(wallet_input, self.email))


class ValidatePromoAndReferral(Endpoint):
    @validate_keycloak_token
    @try_except
    def post(self):
        request_doc = request.get_json()
        device_id = request.headers.get('device_id')
        accept_language = request.headers.get('accept_language', "en")
        return self.make_http_response(validate_promo_code_and_referral_code(request_doc, self.email, device_id, accept_language))


class RewardsHistory(Endpoint):
    def __init__(self):
        super().__init__()

    @try_except
    @validate_keycloak_token
    @validate_permissions
    def get(self):
        return self.make_http_response(get_rewards_history(self.email))

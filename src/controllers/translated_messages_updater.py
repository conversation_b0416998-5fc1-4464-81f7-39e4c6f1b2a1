import json
import logging
import re
import time

from app_models.main_models import Settings
from expiringdict import ExpiringDict
from flask import request, make_response
from src import cache
from src.cache import get_translations
from src.global_helpers.utils import Endpoint
from src.global_helpers.db_helper import get_setting
from src.global_helpers.wrappers import internal_api_key_required


class PatchLocalization(Endpoint):
    def __init__(self):
        super(PatchLocalization, self).__init__()

    @internal_api_key_required
    def put(self):
        try:
            cache.cached_messages = ExpiringDict(max_len=100, max_age_seconds=3600, items=get_translations())
        except Exception as e:
            logger.info(str(e))


class TranslatedMessages(Endpoint):
    CONFIGURABLE_PATTERN = r'\{([\w_]+)\}'

    def get(self):
        try:
            configurables = Settings.objects().first()
            accept_language = request.headers.get("Accept-Language", "")
            language_mapping_json = cache.cached_messages
            time.sleep(1)
            if not language_mapping_json:
                language_mapping_json = get_translations(accept_language)

            final_result = []
            for message_id, message_data in language_mapping_json.items():
                message_structure = {
                    "message_id": message_data.get("message_id"),  # Convert message_id to a string
                    "mll": []  # List to store translations for other languages
                }
                for field_name, field_value in message_data.items():
                    if field_name != 'data':
                        continue
                    if field_value:
                        for item in field_value:
                            if not accept_language or item['language_code'] == accept_language:
                                # Use re.findall() to find all matches in the string
                                variable_names = re.findall(self.CONFIGURABLE_PATTERN, item['message'])
                                for variable_name in variable_names:
                                    try:
                                        item['message'] = item['message'].format(**{variable_name: getattr(configurables, variable_name)})
                                    except Exception as e:
                                        logging.error("Could not format string due to %s", e)

                                language_translation = {item['language_code']: item['message']}
                                message_structure["mll"].append(language_translation)
                    else:
                        language_translation = {'en': message_data.get('message_code')}
                        message_structure["mll"].append(language_translation)

                final_result.append(message_structure)
            self.default_response["data"] = final_result
            self.default_response["totalCount"] = len(final_result)
            return self.make_http_response(
                self.default_response,
                status=200,
                **{"language-version": configurables.lng_version}
            )

        except Exception as exception:
            logging.error("Couldn't get translations due to exception %s", exception)

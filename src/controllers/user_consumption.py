import logging
import uuid
from flask import request
from src import literals
from src.global_helpers.utils import Endpoint
from src.global_helpers.wrappers import (validate_keycloak_token,
                                         validate_permissions,
                                         try_except)
from src.request_parsers.user_consumption_input import (EsimDetailsInput, UserBundlesInput)
from src.services.user_consumption_helpers import (get_user_bundles,
                                                   get_accumulated_transaction_history,
                                                   get_accumulated_user_consumption,
                                                   post_bundle_label,
                                                   get_iccid_details_of_user)
from src.request_parsers.label_name_input import (LabelForEsimPlansInput)


class UserActiveBundles(Endpoint):
    def __init__(self):
        super(UserActiveBundles, self).__init__()

    @validate_keycloak_token
    @validate_permissions
    @try_except
    def get(self):
        user_bundles_input = UserBundlesInput.parse_args()
        data, bundles_count = get_user_bundles(email=self.email, user_bundles_input=user_bundles_input)
        default_response = {
            "status": False,
            "totalCount": 0,
            "data": {},
            "responseCode": 2,
            "title": "Failure",
            "message": "No bundles",
            "developerMessage": ""
        }
        if data:
            default_response = {
                "status": True,
                "totalCount": bundles_count,
                "data": data,
                "responseCode": 1,
                "title": "Success",
                "message": literals.OPERATION_COMPLETE,
                "developerMessage": ""
            }
        return self.make_http_response(default_response)


class AccumulatedTransactionHistory(Endpoint):
    def __init__(self):
        super().__init__()

    @validate_keycloak_token
    @validate_permissions
    @try_except
    def get(self, iccid):
        esim_details_input = EsimDetailsInput.parse_args()
        number_of_page = esim_details_input["number_of_page"]
        page_size = esim_details_input["page_size"]
        language = request.headers.get("Accept-Language", "en")
        data, count = get_accumulated_transaction_history(self.email, iccid, number_of_page, page_size, language)
        default_response = {
            "status": True,
            "data": data,
            "responseCode": 1,
            "title": "Success",
            "message": literals.OPERATION_COMPLETE,
            "developerMessage": "",
            "totalCount": count
        }
        return self.make_http_response(default_response)


class AccumulatedBundleConsumptionByIccid(Endpoint):
    def __init__(self):
        super().__init__()

    @validate_keycloak_token
    @validate_permissions
    @try_except
    def get(self, iccid):
        if data := get_accumulated_user_consumption(self.email, iccid):
            return self.make_http_response({
                "status": True,
                "data": data,
                "responseCode": 1,
                "title": "Success",
                "message": literals.OPERATION_COMPLETE,
                "developerMessage": "",
                "totalCount": 0
            })
        return self.make_http_response({
            "status": False,
            "data": {},
            "responseCode": 2,
            "title": "Failure",
            "message": literals.CANNOT_GET_CONSUMPTION,
            "developerMessage": "No data associated with this iccid/email combo",
            "totalCount": 0
        })


class LabelForEsimPlans(Endpoint):
    def __init__(self):
        super().__init__()

    @validate_keycloak_token
    @validate_permissions
    @try_except
    def post(self):
        request_doc = LabelForEsimPlansInput.parse_args()
        return self.make_http_response(post_bundle_label(self.email, request_doc))


class TransactionHistory(Endpoint):
    def __init__(self):
        super().__init__()

    @validate_keycloak_token
    @validate_permissions
    @try_except
    def get(self, iccid):
        esim_details_input = EsimDetailsInput.parse_args()
        number_of_page = esim_details_input["number_of_page"]
        page_size = esim_details_input["page_size"]
        data, count = get_iccid_details_of_user(self.email, iccid, number_of_page, page_size)
        self.set_default_response(1 if data else 2, data=data, message=literals.OPERATION_COMPLETE, total_count=count)
        return self.make_http_response(self.default_response)

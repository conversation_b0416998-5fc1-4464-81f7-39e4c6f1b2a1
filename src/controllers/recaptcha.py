import logging

from app_models.mobiles import AppUserDetails
from flask import request

import instance.consumer_config as cfg
from src.global_helpers.db_helper import verify_recaptcha, get_email_list_from_device_id
from src.global_helpers.utils import Endpoint
from src.global_helpers.wrappers import validate_keycloak_token, try_except
from src.request_parsers.captcha_input import CaptchaInput


class ReCaptcha(Endpoint):

    @try_except
    @validate_keycloak_token
    def post(self):
        req_input = CaptchaInput.parse_args()
        device_id = request.headers.get('device_id')
        if not device_id:
            logging.info('device id is required to verify recaptcha')
            raise Exception("device id is required to verify recaptcha")
        token = req_input.token
        if verify_recaptcha(cfg.recaptcha_secret_key, token):
            logging.info("captcha token is valid")
            users = AppUserDetails.objects
            emails = get_email_list_from_device_id(device_id)
            if not (users := users.filter(user_email__in=emails)):
                raise Exception("User does not exist")
            if users.filter(unblock_counter__gt=0):
                raise Exception("User unblocked before, contact support")
            users.update(block_transactions=False, inc__unblock_counter=1)
            self.set_default_response(code=1, developer_message="Token is valid")
        else:
            self.set_default_response(code=2, developer_message="Invalid token")
        return self.make_http_response(self.default_response)

from flask import request
from app_models import main_models, reseller_models as rm
from src.global_helpers.utils import Endpoint
from src.global_helpers.wrappers import try_except
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

import instance.consumer_config as cfg


def decrypt_data(encrypted_data):
    try:
        key = cfg.ENC_FEEDBACK_KEY[:16].encode('utf-8')
        iv = bytes.fromhex(encrypted_data[:32])
        encrypted_data = bytes.fromhex(encrypted_data[32:])
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted_data = cipher.decrypt(encrypted_data)
        unpadded_data = unpad(decrypted_data, AES.block_size)
        return unpadded_data.decode('utf-8')
    except:
        return ''

class FeedbackQuestions(Endpoint):
    def __init__(self):
        super(FeedbackQuestions, self).__init__()

    @try_except
    def get(self, token):
        response_json = {
            "status": True,
            "data": {},
            "responseCode": 3,
            "title": "Success",
            "message": "",
            "totalCount": None
        }
        accept_language_header = request.headers.get('Accept-Language')
        email = decrypt_data(token)
        if not email or not rm.Order_history.objects.filter(client_email=email):
            response_json["message"] = "Invalid Token"
            response_json["title"] = "Failed"

            return self.make_http_response(response_json, status=401)

        email = main_models.Answer.objects(user_id=email).first()
        if email and email.is_submited:
            response_json["message"] = "Feedback has been already submitted"
            response_json["title"] = "Failed"
            return self.make_http_response(response_json, status=400)

        language_code = accept_language_header if accept_language_header else 'en'
        questions = main_models.Question.objects(status=True, language_code=language_code).order_by('order')
        if questions:
            questions_list = [{"id": str(question.id), "text": question.question_text, "order": question.order,
                               "question_code": question.question_code}
                              for question in questions]

            response_json["data"] = questions_list
            response_json["totalCount"] = len(questions_list)
        else:
            response_json["message"] = "Language Provided doesnt exist"

        return self.make_http_response(response_json)


class AnswerQuestions(Endpoint):
    def __init__(self):
        super(AnswerQuestions, self).__init__()

    @try_except
    def post(self, token):
        response_json = {'status': True,
                         "responseCode": 3,
                         "title": "Success",
                         "message": "",
                         }

        data = request.get_json()
        answers = data.get('answers')
        user_id = decrypt_data(token)
        if not user_id or not rm.Order_history.objects.filter(client_email=user_id):
            response_json["message"] = "Invalid Token"
            response_json["title"] = "Failed"
            return self.make_http_response(response_json, 401)

        if not answers:
            response_json['message'] = 'User ID and answers are required.'
            response_json["title"] = "Failed"

            return self.make_http_response(response_json, 400)

        email = main_models.Answer.objects(user_id=user_id).first()
        if email and email.is_submited:
            response_json["message"] = "Feedback has been already submitted"
            response_json["title"] = "Failed"
            return self.make_http_response(response_json, status=400)

        for answer in answers:
            question_id = answer.get('question_id')
            rating = answer.get('rating')

            if not question_id or not rating:
                response_json['message'] = 'Question ID and rating are required for each answer.'
                response_json["title"] = "Failed"
                return self.make_http_response(response_json, 400)

            try:
                question = main_models.Question.objects.get(id=question_id)
            except main_models.Question.DoesNotExist:
                response_json["title"] = "Failed"
                response_json['message'] = 'Invalid question ID: {}'.format(question_id)
                return self.make_http_response(response_json, 400)

            if rating < 0 or rating > 5:
                response_json["message"] = 'Rating should be between 0 and 5.'
                response_json["title"] = "Failed"
                return self.make_http_response(response_json, 400)

            if main_models.Answer.objects(question=question, user_id=user_id):
                response_json["message"] = 'User has already answered this question'
                response_json["title"] = "Failed"
                return self.make_http_response(response_json, 400)

            answer_doc = main_models.Answer(question=question, user_id=user_id, rating=rating, is_submited=True)
            answer_doc.save()
            response_json["message"] = 'Answers submitted successfully.'
        return self.make_http_response(response_json)

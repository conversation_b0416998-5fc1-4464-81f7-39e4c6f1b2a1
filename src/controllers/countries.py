from src import literals
from src.global_helpers.utils import Endpoint
from src.global_helpers.wrappers import try_except
from src.request_parsers.countries_input import TopSellingCountriesInput
from src.services.countries_helpers import get_top_selling_countries


class TopSellingCountries(Endpoint):
    def __init__(self):
        super(TopSellingCountries, self).__init__()

    @try_except
    def get(self):
        top_selling_countries_input = TopSellingCountriesInput.parse_args()
        data = get_top_selling_countries(top_selling_countries_input)
        total_count = len(data) if data else 0
        self.set_default_response(code=1, data=data, message=literals.OPERATION_COMPLETE, total_count=total_count)
        return self.make_http_response(self.default_response)

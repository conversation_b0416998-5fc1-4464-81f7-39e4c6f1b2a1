import logging
import stripe
import datetime
from src.models import User
import os
from src.config import Config
from src.utils.encryption_decryption import AESCipher

aes_client = AESCipher()
LOCK_FILE = "cancel_expired_payment_intents.lock"


def acquire_lock():
    try:
        with open(LOCK_FILE, 'x') as lock_file:
            return lock_file
    except FileExistsError as e:
        logging.info(f"[{datetime.datetime.utcnow()}] lock file already exists and job is running")
        return None


def release_lock(lock_file):
    try:
        lock_file.close()
        os.remove(LOCK_FILE)
    except FileNotFoundError as e:
        logging.info(f"Lock file not found:{e}")
    except Exception as e:
        logging.info(f"Error occurred while releasing lock: {e}")


def cancel_expired_payment_intents():
    lock_fd = acquire_lock()

    if lock_fd is None:
        return

    users = User.objects(role=1)
    try:
        for user in users:
            try:
                stripe.api_key = Config.STRIPE_API_KEY
                if 'stripe_secret_key' in user:
                    user_stripe_secret_key = aes_client.decrypt(user.stripe_secret_key)
                    if user_stripe_secret_key != '':
                        stripe.api_key = user_stripe_secret_key
                ten_minutes_ago = datetime.datetime.utcnow() - datetime.timedelta(minutes=10)
                twenty_minutes_ago = datetime.datetime.utcnow() - datetime.timedelta(minutes=20)
                # Retrieve Payment Intents that are incomplete and older than 10 min
                payment_intents = stripe.PaymentIntent.list(
                    created={
                        "lt": int(ten_minutes_ago.timestamp()),
                        "gt": int(twenty_minutes_ago.timestamp())
                    }
                )
                for intent in payment_intents.auto_paging_iter():
                    # Cancel the Payment Intent
                    if intent.status != 'requires_payment_method' and intent.status != 'requires_confirmation':
                        continue
                    intent.cancel()
                    logging.info(f"[{datetime.datetime.utcnow()}] Canceled Payment Intent: {intent.id}")
            except Exception as e:
                logging.info(f"[{datetime.datetime.utcnow()} an error occurred while canceling user orders: {e}]")
                continue
    except Exception as e:
        logging.info(f"[{datetime.datetime.utcnow()}] Exception in cancel_expired_payment_intents: {e}")
    release_lock(lock_fd)

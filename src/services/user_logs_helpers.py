import datetime
import json
from typing import List
from mongoengine import Q
from src import literals
from app_models.consumer_models import CashbackHistory, Labels
from app_models.main_models import NotificationLogs
from app_models import main_models, mobiles
from instance import consumer_config
from src.global_helpers.db_helper import get_user_notifications
from src.global_helpers.wrappers import try_except_func
from src.global_helpers import db_helper
import logging
logger = logging.getLogger(__name__)

def get_localized_notification_list(notification_list_input, email, language="en"):
    default_response = {
        "status": True,
        "data": [],
        "title": "",
        "responseCode": 1,
        "message": "",
        "developerMessage": "",
        "totalCount": 0
    }

    template_mapping = consumer_config.notification_mapping.get(language, {})

    number_of_page = notification_list_input.number_of_page
    page_size = notification_list_input.page_size
    status_update = notification_list_input.status_update

    main_pipeline = [
        {
            "$match": {
                "email": email
            }
        },
        {
            "$lookup": {
                "from": "bundles",  # The collection name of the Bundles model
                "localField": "bundle_code",
                "foreignField": "bundle_code",
                "as": "bundle_data"
            }
        },
        {
            "$unwind": {
                'path': '$bundle_data',
                'preserveNullAndEmptyArrays': False
            },
        },
        {
            "$project": {
                "bundle_marketing_name": "$bundle_data.bundle_marketing_name",
                "data_unit": "$bundle_data.data_unit",
                "data_amount": "$bundle_data.data_amount",
                "transaction": 1,
                "notification_id": 1,
                "datetime": 1,
                "email": 1,
                "iccid": 1,
                "transaction_status": 1,
                "transaction_message": 1,
                "status": 1,
                "bundle_code": 1,
                "validity_date": {
                    "$add": [
                        "$datetime",
                        {
                            "$multiply": [1000, 60, 60, 24, "$bundle_data.bundle_duration"]
                        }
                    ]
                }
            }
        },
        {
            "$sort": {
                "datetime": -1
            }
        },
        {
            "$skip": number_of_page * page_size
        },
        {
            "$limit": page_size
        }

    ]
    pipeline = [
        {
            "$facet": {
                "total_count": [

                    {"$count": "count"}
                ],
                "unread_count": [
                    {
                        "$match": {

                            "transaction_status": False
                        }},
                    {"$count": "count"}
                ],
                "data": main_pipeline
            }
        }
    ]

    results = NotificationLogs.objects(email=email, bundle_code__nin=[None, ''], reseller_type="subscriber").aggregate(*pipeline)
    # Convert results to a list for further processing
    result_list = list(results)

    # The result will have a structure like this:
    # [{"total_count": [{"count": xx}], "data": [{...}, {...}, ...]}]

    # Extract total count and data
    total_count = result_list[0]['total_count'][0]['count'] if result_list and result_list[0]['total_count'] else 0
    unread_count = result_list[0]['unread_count'][0]['count'] if result_list and result_list[0]['unread_count'] else 0
    data = result_list[0]['data'] if result_list and result_list[0]['data'] else []

    for item in data:
        del item["_id"]
        item["datetime"] = str(item["datetime"])
        validity_date = item.get("validity_date") if item.get(
            "validity_date") is not None else datetime.datetime.utcnow()
        item["validity_date"] = validity_date.strftime('%Y-%m-%d')
        translated_message = template_mapping.get(item["transaction"]).replace('{{','{').replace('}}', '}')

        label = Labels.objects(iccid=item['iccid'], client_email=item['email']).first()
        if label:
            bundle_marketing_name = label["label_name"]
        else:
            bundle_marketing_name = item.get("bundle_marketing_name")
        username = mobiles.AppUserDetails.objects(user_email=item.get("email")).first()

        username = mobiles.AppUserDetails.objects(user_email = item.get("email")).first()
        translated_message = translated_message.format(username = username["first_name"] or item.get("email"),
                                                       customer_email=item.get("email"),
                                                       bundle_marketing_name=bundle_marketing_name,
                                                       data_unit=item.get("data_unit"),
                                                       data_amount=item.get("data_amount"),
                                                       validity_date=item.get("validity_date"))
        # its giving key error lets try another approach to delete the keys:
        if "bundle_marketing_name" in item:
            del item["bundle_marketing_name"]
        if "bundle_code" in item:
            del item["bundle_code"]
        if "data_unit" in item:
            del item["data_unit"]
        if "data_amount" in item:
            del item["data_amount"]
        if "validity_date" in item:
            del item["validity_date"]
        item["translated_message"] = translated_message
        default_response['data'].append(item)
    default_response['totalCount'] = total_count
    default_response['unreadCount'] = unread_count
    if status_update:
        NotificationLogs.objects(email=email, reseller_type="subscriber").update(set__transaction_status=True)
    return default_response


def get_notification_list(request_doc, current_user):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }
    change_status = False
    transaction_status = None
    if 'transaction_status' in request_doc:
        transaction_status = int(request_doc['transaction_status'])
    number_of_page = 1
    if 'number_of_page' in request_doc:
        number_of_page = int(request_doc['number_of_page'])
    page_size = 10
    if 'page_size' in request_doc:
        page_size = int(request_doc['page_size'])
    if 'status_update' in request_doc and int(request_doc['status_update']) == 1:
        change_status = True
    notification_list = db_helper.get_notifications_list(email=current_user)
    query_set = Q(email=current_user) & Q(transaction_status=False) & Q(reseller_type="subscriber")
    unread_count = db_helper.get_notifications_count(query_set)
    # if transaction_status is not None:
    #    notification_list = notification_list.filter(transaction_status=transaction_status)
    if number_of_page:
        notification_list = notification_list.skip((number_of_page - 1) * page_size)
    if page_size:
        notification_list = notification_list.limit(page_size)
    obj_data = []
    for obj in notification_list:
        new_obj = {
            "notification_id": str(obj.notification_id),
            "datetime": str(obj.datetime),
            "transaction_status": obj.transaction_status,
            "transaction": str(obj.transaction),
            "transaction_message": str(obj.transaction_message),
            "status": obj.status,
            "iccid": str(obj.iccid),
            "reseller_type": "subscriber"
        }
        if change_status:
            obj.transaction_status = True
            obj.save()
        obj_data.append(new_obj)
    default_response['totalCount'] = notification_list.count()
    default_response['data'] = obj_data
    default_response['unreadCount'] = unread_count
    return default_response


def change_notification_list_status(request_doc, current_user):
    default_response = {"status": True,
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        }
    notification_id_list = request_doc['notification_id_list']
    try:
        notifications = main_models.NotificationLogs. \
            objects(notification_id__in=notification_id_list, email=current_user, reseller_type="subscriber").update(set__transaction_status=True)
    except Exception as e:
        default_response["status"] = False
        default_response["responseCode"] = 3
        default_response["title"] = "failed"
        default_response["message"] = "failed to perform action"
        default_response["developerMessage"] = str(e)
    return default_response


def set_notification_status(notification_id, current_user, request_doc):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }
    notification = db_helper.get_notification(notification_id)
    if notification:
        if notification.email != current_user:
            default_response['message'] = literals.TRY_AGAIN
            default_response['status'] = False
            default_response['responseCode'] = 2

        else:

            notification.transaction_status = request_doc['transaction_status']
            notification.reseller_type = "subscriber"
            notification.save()

    return default_response


def get_notification(notification_id, current_user):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }

    notification = db_helper.get_notification(notification_id=notification_id)
    if notification:
        if notification.email != current_user:
            default_response['message'] = literals.TRY_AGAIN
            default_response['status'] = False
            default_response['responseCode'] = 2
        else:

            data = {
                "notification_id": str(notification.notification_id),
                "datetime": str(notification.datetime),
                "email": str(notification.email),
                "iccid": str(notification.iccid),
                "bundle_code": notification.bundle_code,
                "topup_code": notification.topup_code,
                "price": notification.price,
                "currency": notification.currency,
                "transaction_status": notification.transaction_status,
                "transaction": str(notification.transaction),
                "status": notification.status,
                "qr_code_link": notification.qr_code_link,
                "reseller_type" : "subscriber"
            }
            default_response['data'] = data

    return default_response


def get_order_history_details(order_number, current_user):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }

    order_history = db_helper.get_order_history(order_number=str(order_number))
    if order_history:
        pipeline = db_helper.get_vendor_from_bundle_code(order_history.bundle_data["bundle_code"])
        result_list = list(pipeline)
        vendor = result_list[0]["vendor"]
        if order_history.client_email != current_user and order_history.shared_with != current_user:
            default_response['message'] = literals.TRY_AGAIN

        else:
            label = Labels.objects(iccid=order_history["iccid"], client_email=current_user).first()
            if label:
                label_name = label["label_name"]
            else:
                label_name = ""


            data = {
                "history_log_id": str(order_number),
                "datetime": str(order_history.payment_date),
                "iccid": str(order_history.iccid),
                "coverage": str(order_history.bundle_data["coverage"]),
                "bundle_marketing_name": str(order_history.bundle_data["bundle_marketing_name"]),
                "bundle_code": order_history.bundle_code,
                "topup_code": order_history.bundle_code,
                "bundle_duration": order_history.bundle_data["bundle_duration"],
                "price": order_history.bundle_data["retail_price"],
                "currency_code": order_history.currency_code,
                "order_number": order_history.order_number,
                "transaction": str(order_history.order_type),
                "qr_code_link": order_history.qr_code_link,
                "smdp_address": order_history.smdp_address,
                "activation_code": order_history.activation_code,
                "transaction_status": "unread",
                "data_amount": order_history.bundle_data["data_amount"],
                "data_unit": order_history.bundle_data["data_unit"],
                "sent_using": "Email",
                "label_name": label_name,
                "esim_installation": vendor["esim_installation"]
            }
            default_response['data'] = data

    return default_response


def get_limited_order(current_user, token, order_number):
    default_response = {"status": False,
                        "data": {},
                        "title": "",
                        "responseCode": 2,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }

    keycloack_setting = {
        'username': consumer_config.keycloack_admin_user,
        'password': consumer_config.keycloack_admin_password,
        'admin_releam': consumer_config.keycloack_admin_releam,
        'releam_name': consumer_config.keycloack_realm_name,
        'client_id': consumer_config.keycloack_client_id_temporary,
        'admin_key': consumer_config.keycloack_admin_key,
        'realm_name': consumer_config.keycloack_realm_name,
        'client_key': consumer_config.keycloack_client_key_temporary}

    keycloak_openid = db_helper.call_keyloack_user(keycloack_setting)

    # Get Token
    userinfo = keycloak_openid.userinfo(token)
    if userinfo:
        order_id = ""
        if 'history_id' in userinfo:
            order_id = userinfo['history_id']

        order_history = db_helper.get_order_history(order_number=str(order_number))
        if order_history:
            pipeline = db_helper.get_vendor_from_bundle_code(order_history.bundle_data["bundle_code"])
            result_list = list(pipeline)
            vendor = result_list[0]["vendor"]
            data = {
                "history_log_id": str(order_number),
                "datetime": str(order_history.payment_date),
                "iccid": str(order_history.iccid),
                "coverage": str(order_history.bundle_data["coverage"]),
                "bundle_marketing_name": str(order_history.bundle_data["bundle_marketing_name"]),
                "bundle_code": order_history.bundle_code,
                "topup_code": order_history.bundle_code,
                "bundle_duration": order_history.bundle_data["bundle_duration"],
                "price": order_history.bundle_data["retail_price"],
                "currency_code": order_history.currency_code,
                "order_number": order_history.order_number,
                "transaction": str(order_history.order_type),
                "qr_code_link": order_history.qr_code_link,
                "smdp_address": order_history.smdp_address,
                "activation_code": order_history.activation_code,
                "transaction_status": "unread",
                "data_amount": order_history.bundle_data["data_amount"],
                "data_unit": order_history.bundle_data["data_unit"],
                "sent_using": "Email",
                "esim_installation": vendor["esim_installation"]
            }
            default_response['responseCode'] = 1
            default_response['status'] = True
            default_response['data'] = data

        else:
            default_response['message'] = literals.TRY_AGAIN
            default_response['responseCode'] = 6

    return default_response


@try_except_func
def get_cashback_history(email: str, **kwargs):
    cashback_history: List[CashbackHistory]
    cashback_history = CashbackHistory.objects(**{
        **kwargs,
        "user_email": email
    })
    cashback_history_filtered = []
    for ch in cashback_history:
        if ch.rewarded_amount <= 0:
            continue
        del ch.id
        purchase_date = str(ch.purchase_date.strftime(literals.DATETIME_FORMAT_2))
        ch = json.loads(ch.to_json())
        ch["purchase_date"] = purchase_date
        ch["rewarded_amount"] = round(ch["rewarded_amount"], 2)
        cashback_history_filtered.append(ch)
    return cashback_history_filtered, len(cashback_history_filtered)


def get_user_notification_list(current_user):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }
    notifications = get_user_notifications(email=current_user)
    notification_ids = []

    obj_data = []
    for obj in notifications:
        coverage = "Global"
        display_name = "Global"
        if obj["bundle_category"] == "region":
            coverage = obj["region_code"]
            display_name = str(obj["region_name"]).capitalize()
        elif obj["bundle_category"] == "country":
            coverage = str(obj["country_code_list"][0])
            display_name = str(obj["country_list"][0]).capitalize()

        status = str(obj['status']).lower()
        if status == 'Pending':
            status = 'Active'

        label = Labels.objects(iccid=obj['iccid'], client_email=obj['email']).first()
        if label:
            obj['label_name'] = label["label_name"]
        else:
            obj['label_name'] = ""

        new_obj = {
            "email": str(obj['email']),
            "iccid": str(obj['iccid']),
            "datetime": str(obj['last_datetime']),
            "bundle_code": str(obj['bundle_code']),
            "bundle_name": str(obj['bundle_name']),
            "coverage": coverage,
            "display_name": display_name,
            "bundle_marketing_name": str(obj['bundle_marketing_name']),
            "expiry_date": str(obj['expiry_date']),
            "transaction": str(obj['transaction']),
            "status": status,
            "searched_countries": obj['searched_countries'],
            "searched_code_list": obj['searched_code_list'],
            "label_name": obj['label_name'],
        }
        obj_data.append(new_obj)
        notification_ids.append(obj['_id'])

    default_response['totalCount'] = len(list(obj_data))
    default_response['data'] = obj_data

    return default_response



def get_orders(request_doc, current_user):
    default_response = {"status": True,
                        "data": {},
                        "title": "",
                        "responseCode": 1,
                        "message": "",
                        "developerMessage": "",
                        "totalCount": 0
                        }

    number_topup_page = 1
    if 'number_topup_page' in request_doc and request_doc['number_topup_page'] != '':
        number_topup_page = int(request_doc['number_topup_page'])

    page_topup_size = 10
    if 'page_topup_size' in request_doc and request_doc['page_topup_size'] != '':
        page_topup_size = int(request_doc['page_topup_size'])

    history_bundle_topup_logs = db_helper.get_order_history_logs(client_email=current_user, order_type="BuyTopup")
    if number_topup_page:
        history_bundle_topup_logs = history_bundle_topup_logs.skip((number_topup_page - 1) * page_topup_size)
    if page_topup_size:
        history_bundle_topup_logs = history_bundle_topup_logs.limit(page_topup_size)

    number_of_page = 1
    if 'number_of_page' in request_doc and request_doc['number_of_page'] != '':
        number_of_page = int(request_doc['number_of_page'])
    page_size = 10
    if 'page_size' in request_doc and request_doc['page_size'] != '':
        page_size = int(request_doc['page_size'])
    history_bundle_logs = db_helper.get_order_history_logs(client_email=current_user, order_type="BuyBundle")
    if number_of_page:
        history_bundle_logs = history_bundle_logs.skip((number_of_page - 1) * page_size)
    if page_size:
        history_bundle_logs = history_bundle_logs.limit(page_size)
    obj_topup_data = []
    obj_data = []

    for obj in history_bundle_logs:
        new_obj = {
            "history_log_id": str(obj.order_number),
            "datetime": str(obj.date_created),
            "iccid": str(obj.iccid),
            "coverage": str(obj.bundle_data.coverage),
            "bundle_marketing_name": str(obj.bundle_data.bundle_marketing_name),
            "bundle_code": obj.bundle_data.bundle_code,
            "topup_code": obj.bundle_data.bundle_code,
            "bundle_duration": obj.bundle_data.bundle_duration,
            "price": obj.bundle_data.retail_price,
            "data_amount": obj.bundle_data.data_amount,
            "data_unit": obj.bundle_data.data_unit,
            "currency_code": obj.bundle_data.currency_code,
            "order_number": obj.order_number,
            "transaction": str(obj.order_type),
            "sent_using": "Email"

        }
        obj_data.append(new_obj)
    for topup_obj in history_bundle_topup_logs:
        new_topup_obj = {
            "history_log_id": str(topup_obj.order_number),
            "datetime": str(topup_obj.date_created),
            "coverage": str(topup_obj.bundle_data.coverage),
            "bundle_marketing_name": str(topup_obj.bundle_data.bundle_marketing_name),
            "bundle_code": topup_obj.bundle_data.bundle_code,
            "topup_code": topup_obj.bundle_data.bundle_code,
            "bundle_duration": topup_obj.bundle_data.bundle_duration,
            "price": topup_obj.bundle_data.retail_price,
            "data_amount": topup_obj.bundle_data.data_amount,
            "data_unit": topup_obj.bundle_data.data_unit,
            "currency_code": topup_obj.bundle_data.currency_code,
            "order_number": topup_obj.order_number,

        }
        obj_topup_data.append(new_topup_obj)
    default_response['totalCount'] = 0
    default_response['data']['buyBundle'] = obj_data
    default_response['data']['buyBundleCount'] = history_bundle_logs.count()
    default_response['data']['BuyTopup'] = obj_topup_data
    default_response['data']['buyBuyTopup'] = history_bundle_topup_logs.count()
    return default_response

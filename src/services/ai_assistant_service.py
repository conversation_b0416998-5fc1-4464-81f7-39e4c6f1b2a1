from app_models.ai_assistant_models import AdvancedSearch, advanced_search_category_category_code, \
    AdvancedSearchBundleResult
from app_models.consumer_models import Countries, Regions, Bundles
from app_models.mobiles import AppUserDetails
from b2c_helpers.db_helper import get_supported_bundles
from mongoengine.queryset.visitor import Q

from src import literals
from src.global_helpers.utils import is_list_nonempty, is_nonempty_nonspace
from src.request_parsers.ai_assistant_requests_responses import DataDTO, FilterCriteriaRequest
from src.request_parsers.common import generate_response_message

import logging
logger = logging.getLogger(__name__)

def find_by_category_or_code(data, search_terms):
    logger.info("Trying to find by category or code with search terms : %s", search_terms)
    # Create a list to store the results
    results = []
    # Loop through each dictionary in the list
    for entry in data:
        # Check if the 'category' or 'category_code' of the dictionary is in the search_terms
        if entry['category'] in search_terms or entry['category_code'] in search_terms:
            results.append(entry)  # Append the matching dictionary to the results list
    logger.info("results : %s " ,results)
    return results


def build_ranged_query(requestDTO):
    """Builds a MongoEngine query to include selectable and either consumption or duration."""
    # Initialize the query with selectable=True
    logger.debug("Trying to build ranged query with request dto : %s " ,requestDTO)
    query = Q(selectable=True)
    criteria_added = False  # To track if any additional criteria are added
    # Add condition based on 'consumption' if valid
    if is_nonempty_nonspace(requestDTO.consumption) and is_nonempty_nonspace(requestDTO.duration):
        query &= Q(category_code=requestDTO.consumption) | Q(category_code=requestDTO.duration)
        criteria_added = True
    elif is_nonempty_nonspace(requestDTO.consumption) and not is_nonempty_nonspace(requestDTO.duration):
        query &= Q(category_code=requestDTO.consumption)
        criteria_added = True
    elif not is_nonempty_nonspace(requestDTO.consumption) and is_nonempty_nonspace(requestDTO.duration):
        query &= Q(category_code=requestDTO.duration)
        criteria_added = True
    return query if criteria_added else None


def get_advanced_search_categories(accept_language: str):
    logger.info("Trying to get advanced search categories with accept language : %s " , accept_language)
    # Get all category-category_code combinations
    advanced_search_combination_query = AdvancedSearch.objects(selectable=True).all()
    advanced_search_combination_list = [doc.to_mongo().to_dict() for doc in advanced_search_combination_query]

    # Handle Destination selection
    # if single_country and/or multiple_countries, get all countries in pairs
    retrieved_countries = []
    is_single_country = False
    is_multiple_country = False

    if find_by_category_or_code(advanced_search_combination_list,
                                [advanced_search_category_category_code.destination.single_country.name]):
        is_single_country = True

    if find_by_category_or_code(advanced_search_combination_list,
                                [advanced_search_category_category_code.destination.multiple_countries.name]):
        is_multiple_country = True

    if is_single_country or is_multiple_country:
        countries = Countries.objects().all()
        for country in countries:
            retrieved_countries.append(
                DataDTO(id=country.iso3_code, display_name=country.country_name)
            )

    # if region, get all regions
    retrieved_regions = []
    is_region = False

    if find_by_category_or_code(advanced_search_combination_list,
                                [advanced_search_category_category_code.destination.region.name]):
        is_region = True
        regions = Regions.objects().all()
        for region in regions:
            retrieved_regions.append(
                DataDTO(id=region.region_code, display_name=region.region_name)
            )

    destination_dict = {
        "country": {
            "is_single_country": is_single_country,
            "is_multiple_countries": is_multiple_country,
            "data": retrieved_countries
        },
        "region": {
            "is_region": is_region,
            "data": retrieved_regions
        }
    }

    # Handle Duration selection

    durations = find_by_category_or_code(advanced_search_combination_list,
                                         [advanced_search_category_category_code.duration.name])
    retrieved_durations = []
    is_duration = False

    if durations:
        is_duration = True
        for duration in durations:
            duration_name = ""
            min_max_duration = "{min}-{max}"
            min_only_duration = "{min}+"

            min_duration = int(duration['min']) if (duration['min'] or duration['min'] >= 0) else None
            max_duration = int(duration['max']) if (duration['max'] or duration['max'] >= 0) else None

            if (min_duration or min_duration >= 0) and ((
                                                                not max_duration and not max_duration == 0) or max_duration == advanced_search_category_category_code.INFINITY):
                duration_name = min_only_duration.format(min=min_duration)
            else:
                duration_name = min_max_duration.format(min=min_duration, max=max_duration)

            # Create a DataDTO object and append it to the list
            retrieved_durations.append(DataDTO(id=duration['category_code'], display_name=duration_name))

    duration_dict = {
        "is_duration": is_duration,
        "data": retrieved_durations
    }

    # Handle Consumption selection

    consumptions = find_by_category_or_code(advanced_search_combination_list,
                                            [advanced_search_category_category_code.consumption.name])
    retrieved_consumptions = []
    is_consumption = False

    if consumptions:
        is_consumption = True
        for consumption in consumptions:

            filtered_display_name = consumption.get("display_name", "")
            for item in consumption["display_name_translation"]:
                if item.get("language_code") == accept_language:
                    filtered_display_name = item.get("display_name", consumption.get("display_name", ""))
                    break

            retrieved_consumptions.append(
                DataDTO(id=consumption['category_code'], display_name=filtered_display_name))

    consumption_dict = {
        "is_consumption": is_consumption,
        "data": retrieved_consumptions
    }

    return {
        "status": True,
        "data": dict(
            destination=destination_dict,
            duration=duration_dict,
            consumption=consumption_dict
        ),
        "responseCode": 1,
        "title": "Success",
        "message": literals.OPERATION_COMPLETE,
        "developerMessage": ""
    }


def format_bundle(bundles):
    logger.info("Trying Format Bundle With Bundles : %s " , bundles)
    bundles_formatted = []
    for bundle in bundles:
        # separate bundles into category according to category_name

        rounded_retail_price = round(bundle.get("retail_price", 0), 2)
        data_amount = bundle.get("data_amount", 0)

        country_code_list = bundle.get("country_code_list", [])
        if 'ISR' in country_code_list:
            country_code_list.remove('ISR')

        country_name_list = bundle.get("country_list", [])
        if 'Israel' in country_name_list:
            country_name_list.remove('Israel')

        formatted_bundle = dict(
            bundle_code=bundle.get("bundle_code", ""),
            bundle_category=bundle.get('bundle_category', ""),
            bundle_name=bundle.get('bundle_name', ""),
            bundle_marketing_name=bundle.get('bundle_marketing_name', ""),
            country_code=country_code_list,
            country_name=country_name_list,
            currency_code_list=[
                bundle.get('currency_code', "")
            ],
            data_unit=bundle.get("data_unit", ""),
            data_amount=data_amount,
            gprs_limit=data_amount,
            region_code=bundle.get('region_code', ""),
            region_name=bundle.get('region_name', ""),
            retail_price=rounded_retail_price,
            subscriber_price=rounded_retail_price,
            unlimited=bundle.get("unlimited", False),
            validity=bundle.get("bundle_duration", 0),
            validity_amount=bundle.get("validity_amount", ""),
        )

        bundles_formatted.append(formatted_bundle)
    logger.info("Formatted Bundles : %s ", bundles_formatted)
    return bundles_formatted


def process_data_amount(data_amount: float):
    logger.info("Trying to Process Data Amount : %s ", data_amount)
    if data_amount < 1:
        converted_data_amount = data_amount * 1000
        data_amount_unit = "MB"
        logger.info("Data Amount Unit : %s , Data Amount : %s ", data_amount_unit, data_amount)
        return data_amount_unit, converted_data_amount
    else:
        data_amount_unit = "GB"
        logger.info("Data amount unit : %s, anf data amount :%s ", data_amount_unit , data_amount)
        return data_amount_unit, data_amount


def filter_data_by_categories(requestDTO: FilterCriteriaRequest, email: str):
    logger.info("Trying to filter data by categories with request dto : %s, and email :%s", requestDTO, email)
    user: AppUserDetails = AppUserDetails.objects(user_email=email).first()
    if not user:
        return generate_response_message(is_success=False, message="Invalid user", response_title="Invalid User")

    saved_advanced_search_criteria = {}

    main_search_query = Q(is_active=True) & Q(deleted=False)
    main_criteria_added = False

    # **************************************************************************************
    # countries and regions categories Handling
    # **************************************************************************************
    if is_list_nonempty(requestDTO.countries) and is_list_nonempty(requestDTO.regions):
        main_criteria_added = True
        main_search_query &= Q(country_code_list__all=requestDTO.countries) | Q(region_code__in=requestDTO.regions)

        saved_advanced_search_criteria["country_code_list"] = requestDTO.countries
        saved_advanced_search_criteria["region_code_list"] = requestDTO.regions

    elif is_list_nonempty(requestDTO.countries) and not is_list_nonempty(requestDTO.regions):
        main_criteria_added = True
        main_search_query &= Q(country_code_list__all=requestDTO.countries)

        saved_advanced_search_criteria["country_code_list"] = requestDTO.countries
    elif not is_list_nonempty(requestDTO.countries) and is_list_nonempty(requestDTO.regions):
        main_criteria_added = True
        main_search_query &= Q(region_code__in=requestDTO.regions)

        saved_advanced_search_criteria["region_code_list"] = requestDTO.regions

    # **************************************************************************************
    # Ranged categories Handling
    # **************************************************************************************
    ranged_query = build_ranged_query(requestDTO)
    if ranged_query:

        ranged_documents = AdvancedSearch.objects(ranged_query).limit(2)

        consumption_min = None
        consumption_max = None

        duration_min = None
        duration_max = None

        for document in ranged_documents:
            if document.category == advanced_search_category_category_code.consumption.name:
                consumption_min = document.min
                consumption_max = document.max
            elif document.category == advanced_search_category_category_code.duration.name:
                duration_min = document.min
                duration_max = document.max

        # **************************************************************************************
        # Consumption Handling
        # **************************************************************************************
        is_infinity = False

        min_data_unit = None
        min_search_criteria = None
        if consumption_min != None:
            main_criteria_added = True

            if consumption_min != advanced_search_category_category_code.INFINITY:

                min_data_unit, normalized_data_amount = process_data_amount(consumption_min)

                min_search_criteria = (
                        Q(data_amount__gte=normalized_data_amount) & Q(data_unit__icontains=min_data_unit))

                saved_advanced_search_criteria["consumption_min"] = normalized_data_amount
                saved_advanced_search_criteria["consumption_min_unit"] = min_data_unit
            elif consumption_min == advanced_search_category_category_code.INFINITY:
                is_infinity = True
                saved_advanced_search_criteria["consumption_min"] = advanced_search_category_category_code.INFINITY
                saved_advanced_search_criteria["consumption_min_unit"] = advanced_search_category_category_code.INFINITY


        max_data_unit = None
        max_search_criteria = None
        if consumption_max != None:
            main_criteria_added = True

            if consumption_max != advanced_search_category_category_code.INFINITY:
                max_data_unit, normalized_data_amount = process_data_amount(consumption_max)
                max_search_criteria = (
                        Q(data_amount__lte=normalized_data_amount) & Q(data_unit__icontains=max_data_unit))

                saved_advanced_search_criteria["consumption_max"] = normalized_data_amount
                saved_advanced_search_criteria["consumption_max_unit"] = max_data_unit
            elif consumption_max == advanced_search_category_category_code.INFINITY:
                is_infinity = True
                saved_advanced_search_criteria["consumption_max"] = advanced_search_category_category_code.INFINITY
                saved_advanced_search_criteria["consumption_max_unit"] = advanced_search_category_category_code.INFINITY

        if min_search_criteria and max_search_criteria:
            main_search_query &= Q(unlimited=False)

            if min_data_unit == max_data_unit:
                main_search_query &= (min_search_criteria & max_search_criteria)
            else:
                main_search_query &= (min_search_criteria | max_search_criteria)

        elif min_search_criteria and not max_search_criteria:
            tmp_query = min_search_criteria

            if min_data_unit and min_data_unit == "MB":
                tmp_query = ((tmp_query) | Q(data_unit__icontains="GB"))

            if is_infinity:
                tmp_query = ((tmp_query) | Q(unlimited=True))
            else:
                tmp_query = ((tmp_query) & Q(unlimited=False))

            main_search_query &= tmp_query

        elif not min_search_criteria and max_search_criteria:

            tmp_query = max_search_criteria

            if max_data_unit and max_data_unit == "GB":
                tmp_query = ((tmp_query) | Q(data_unit__icontains="MB"))

            if is_infinity:
                tmp_query = ((tmp_query) | Q(unlimited=True))
            else:
                tmp_query = ((tmp_query) & Q(unlimited=False))

            main_search_query &= tmp_query
        elif not min_search_criteria and not max_search_criteria and is_infinity:
            main_search_query &= Q(unlimited=True)

        # **************************************************************************************
        # Duration Handling
        # **************************************************************************************
        if duration_min != None and duration_min != advanced_search_category_category_code.INFINITY:
            main_criteria_added = True
            main_search_query &= Q(bundle_duration__gte=duration_min)

            saved_advanced_search_criteria["duration_min"] = duration_min

        if duration_max != None and duration_max != advanced_search_category_category_code.INFINITY:
            main_criteria_added = True
            main_search_query &= Q(bundle_duration__lte=duration_max)

            saved_advanced_search_criteria["duration_max"] = duration_max

    # **************************************************************************************
    # Get supported bundles by criteria
    # **************************************************************************************
    bundles = [] if not main_criteria_added else get_supported_bundles(
        page_size=requestDTO.bundles_limit,
        skip=0,
        bundle_category=None,
        country_code_list=None,
        bundle_code=None,
        profile_name=None,
        vendor_name=None,
        region_code=None,
        sort_field='retail_price',
        ascending=True,
        reseller_id="",
        main_query=main_search_query
    )

    bundle_codes = []
    bundle_list = format_bundle(bundles)

    for bundle in bundle_list:
        bundle_code = bundle.get("bundle_code", "")
        if bundle_code:
            bundle_codes.append(bundle_code)

    if main_criteria_added:
        saved_advanced_search_res = AdvancedSearchBundleResult(**{
            "user_oid": user.id,
            "user_email": email,
            "search_criteria": saved_advanced_search_criteria,
            "bundle_results": bundle_codes
        }).save()
    logger.info("bundle list data length : %s ", len(bundle_list))
    return {
        "status": True,
        "totalCount": len(bundle_list),
        "data": bundle_list,
        "responseCode": 1,
        "title": "Success",
        "message": literals.OPERATION_COMPLETE,
        "developerMessage": ""
    }

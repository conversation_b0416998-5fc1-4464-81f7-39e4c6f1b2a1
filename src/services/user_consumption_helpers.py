import datetime as dt
import logging

import app_models.consumer_models as cm
import app_models.reseller_models as rm
from b2c_helpers.get_accumulated_user_consumption import (
    AccumulatedBundleConsumptionByIccid,
)
from b2c_helpers.db_helper import accumulate_vendor_data
from b2c_helpers.get_accumulated_user_consumption import (
    AccumulatedBundleConsumptionByIccid,
)

from instance import consumer_config as instance_config
from src import literals
from src.global_helpers.api_helper import (
    get_all_topups_balance,
    get_iccid_topup_expiry_date,
)
from src.global_helpers.db_helper import (
    get_transaction_list_accumulation,
    check_user_bundles,
    get_user,
    get_iccid_bundles_for_user,
)

from src.global_helpers.wrappers import try_except_func

from b2c_helpers.db_helper import accumulate_vendor_data

from instance import consumer_config as instance_config
import logging
logger = logging.getLogger(__name__)
ACCUMULATED_VENDOR_LIST = ["Vodafone"]


def get_user_bundles(email, user_bundles_input):
    plan_status, page_number, page_size, iccid = (
        user_bundles_input["status"],
        user_bundles_input["number_of_page"],
        user_bundles_input["page_size"],
        user_bundles_input["iccid"],
    )
    data = {}
    if plan_status == "used":
        plan_status = "Active"
    else:
        plan_status = "Expired"

    user_bundles_data = check_user_bundles(
        email=email,
        page_number=page_number,
        page_size=page_size,
        plan_status=plan_status,
        iccid=iccid,
    )

    user_bundles = user_bundles_data.get("user_bundles")
    bundles_count = user_bundles_data.get("user_bundles_total_count")

    retrieved_vendors_dict = {}
    acc_user = AccumulatedBundleConsumptionByIccid(config=instance_config)

    for user_bundle in user_bundles:

        if plan_status != "Expired":

            logging.info(
                f"[BACK-VENDOR-REFRESH-ORDER] Initiating background refresh for order: {user_bundle['order_number']}"
            )

            retrieved_vendor_name = user_bundle.get("bundle_data", {}).get(
                "vendor_name", None
            )

            acu_user = None
            if retrieved_vendor_name:
                if retrieved_vendor_name not in retrieved_vendors_dict:
                    vendor = cm.Vendors.objects(
                        vendor_name=retrieved_vendor_name
                    ).first()

                    if vendor:
                        retrieved_vendors_dict[retrieved_vendor_name] = (
                            vendor.enable_background_bundle_refresh
                        )
                    else:
                        retrieved_vendors_dict[retrieved_vendor_name] = False

                if retrieved_vendors_dict[retrieved_vendor_name] is True:
                    logging.info(
                        f"[BACK-VENDOR-REFRESH-ORDER] Refresh operation in progress"
                    )

                    acu_user = acc_user.get_accumulated_user_consumption(
                        email=user_bundle["client_email"], iccid =user_bundle["iccid"]
                    )
                else:
                    logging.info(
                        f"[BACK-VENDOR-REFRESH-ORDER] Refresh not started. Background refresh disabled for vendor: '{retrieved_vendor_name}'"
                    )

            logging.info(
                f"[BACK-VENDOR-REFRESH-ORDER] Refresh operation completed, with accumulated_user_consumption: {acu_user}"
            )

        if not user_bundle.get("shared_with"):  # user did not share this bundle
            user_bundle["shared_with"] = 0
        elif (
            user_bundle.get("client_email") != email
        ):  # original owner of bundle is different from self.email
            user_bundle["shared_with"] = 2
        else:  # is viewing his own shared bundle
            user_bundle["shared_with"] = 1

        if user_bundle["data_unit"] == "GB":
            user_bundle["data_amount"] *= 1000
            user_bundle["data_unit"] = "MB"

        if plan_status == "Active" and not iccid:
            user_bundle["bundle_expired"] = False
        user_bundle.get("bundle_data", {}).pop("vendor_name")
        user_bundle.pop("plan_status", "")
        user_bundle["esim_installation"] = user_bundle["vendor"]["esim_installation"]
        user_bundle.pop("vendor", "")
        label = cm.Labels.objects(
            iccid=user_bundle["iccid"], client_email=email
        ).first()
        if label:
            user_bundle["label_name"] = label["label_name"]
        else:
            user_bundle["label_name"] = ""

    data["bundles"] = user_bundles
    return data, bundles_count


def profile_is_topupable(iccid):
    try:
        return cm.Profiles.objects(iccid=iccid).first().availability != "Expired"
    except Exception as e:
        logger.info(
            f"[{dt.datetime.utcnow()} exception while checking profile {iccid} availability as {e}]"
        )
        return False


def expired_history(iccid):
    try:
        return (
            rm.Order_history.objects(
                iccid=iccid, order_status="Successful", plan_status__ne="Expired"
            ).count()
            > 0
        )
    except Exception as e:
        logger.info(
            f"[{dt.datetime.utcnow()} exception while checking user iccid {iccid} status as {e}]"
        )
        return False


def get_profile_expiry_date_for_vendor(vendor: cm.Vendors):
    return vendor.number_of_expiry_days if vendor.number_of_expiry_days else 365


@try_except_func
def get_accumulated_transaction_history(
    email, iccid, skip, page_size, language
) -> tuple:
    accumulated_bundle = {}
    transaction_history = []
    total_amount_paid = 0
    view_topup = profile_is_topupable(iccid)
    view_consumption = expired_history(iccid)
    shared_with = 0
    data = {
        "active_bundle": accumulated_bundle,
        "transaction_history": transaction_history,
        "view_topup": view_topup,
        "view_consumption": view_consumption,
    }
    bundles_cursor = get_transaction_list_accumulation(email, iccid, skip, page_size)

    transaction_records = bundles_cursor[0]["records"] if bundles_cursor else []
    records_count = bundles_cursor[0]["count"] if bundles_cursor else 0
    for bundle in transaction_records:
        coverage = bundle.get("coverage")
        display_name = bundle.get("display_name")
        validity_date = bundle.get("validity_date", dt.datetime.utcnow()).strftime(
            literals.DATETIME_FORMAT_2
        )
        payment_date = bundle.get("payment_date", dt.datetime.utcnow()).strftime(
            literals.DATETIME_FORMAT_2
        )
        amount_paid = bundle.get("amount_paid", 0)
        bundle_type = bundle.get("bundle_type")
        unlimited = bundle.get("unlimited")

        mapping = instance_config.transaction_history_mapping
        bundle_type_translated = mapping.get(language, {}).get(bundle_type, bundle_type)

        bundle_object = dict(
            bundle_type=bundle_type_translated,
            order_number=bundle.get("order_number"),
            amount_paid=round(amount_paid, 2),
            bundle_name=bundle.get("bundle_name"),
            bundle_code=bundle.get("bundle_code"),
            display_name=display_name,
            coverage=coverage,
            payment_date=payment_date,
            validity_date=validity_date,
            validity_amount=bundle.get("validity_amount"),
            data_amount=bundle.get("data_amount"),
            data_unit=bundle.get("data_unit"),
            sent_via="Email",
            unlimited=unlimited,
            bundle_category=bundle.get("bundle_category"),
        )

        # total_amount_paid += bundle["amount_paid"]
        if bundle_type == "Primary Bundle":
            if not bundle.get("shared_with"):  # user did not share this bundle
                shared_with = 0
            elif (
                bundle.get("client_email") != email
            ):  # original owner of bundle is different from self.email
                shared_with = 2
            else:  # is viewing his own shared bundle

                shared_with = 1
            accumulated_bundle = bundle_object.copy()
            accumulated_bundle["country_list"] = bundle.get("country_list", [])
            accumulated_bundle["country_code_list"] = bundle.get(
                "country_code_list", []
            )

        transaction_history.append(bundle_object)

    total_data_amount, total_amount_paid, total_bundle_duration, queued_bundles = (
        accumulate_vendor_data(iccid)
    )
    if skip == 0:
        accumulated_bundle["data_amount"] = total_data_amount
        accumulated_bundle["data_unit"] = "GB"
        if total_data_amount < 1:
            accumulated_bundle["data_unit"] = "MB"
            accumulated_bundle["data_amount"] = total_data_amount * 1000
        accumulated_bundle["validity_amount"] = str(total_bundle_duration)
        accumulated_bundle["amount_paid"] = round(total_amount_paid, 2)

        if transaction_history:
            accumulated_bundle["validity_date"] = transaction_history[-1][
                "validity_date"
            ]
    data["active_bundle"] = accumulated_bundle
    data["transaction_history"] = transaction_history
    profile_expiry_date = dt.datetime.utcnow()
    if profile := cm.Profiles.objects(iccid=iccid).first():
        vendor = cm.Vendors.objects(vendor_name=profile.vendor_name).first()
        months_till_profile_exp = get_profile_expiry_date_for_vendor(vendor=vendor)
        profile_expiry_date = profile.create_datetime + dt.timedelta(
            days=months_till_profile_exp
        )

    profile_expiry_date = profile_expiry_date.strftime(literals.DATETIME_FORMAT_2)

    label = cm.Labels.objects(iccid=iccid, client_email=email).first()
    if label:
        data["active_bundle"]["label_name"] = label["label_name"]
    else:
        data["active_bundle"]["label_name"] = ""

    data["active_bundle"]["profile_expiry_date"] = profile_expiry_date
    data["active_bundle"]["shared_with"] = shared_with
    return data, records_count


def get_accumulated_user_consumption(email: str, iccid: int) -> object:
    acc_user = AccumulatedBundleConsumptionByIccid(config=instance_config)
    return acc_user.get_accumulated_user_consumption(email, iccid)


def post_bundle_label(current_user, request_doc):
    default_response = {
        "status": False,
        "responseCode": 3,
        "title": "Failed",
        "message": literals.ERROR_GETTING_DATA,
        "developerMessage": "Iccid not found",
    }

    found_user = get_user(current_user)
    iccid = request_doc.get("iccid")
    label_name = request_doc.get("label_name")

    order_history = rm.Order_history.objects(
        iccid=iccid, client_email=current_user, order_status="Successful"
    ).first()
    if not order_history:
        return default_response

    label = cm.Labels.objects(iccid=iccid, client_email=current_user).first()
    if not label:
        doc = {"iccid": iccid, "client_email": current_user, "label_name": label_name}
        cm.Labels(**doc).save()
    else:
        cm.Labels.objects(iccid=iccid, client_email=current_user).update(
            set__label_name=label_name
        )

    default_response = {
        "status": True,
        "data": {
            "email": found_user.user_email,
            "label_name": label_name,
        },
        "responseCode": 1,
        "title": "Success",
        "message": literals.USER_INFO_UPDATED,
        "developerMessage": "",
    }

    return default_response


@try_except_func
def get_iccid_details_of_user(email, iccid, skip, page_size):
    data = {"active_bundle": {}, "transaction_history": []}
    active_bundle = {}
    primary_bundle = {}

    transaction_history = []
    transaction_history_wo_primary = (
        []
    )  # transaction history list but without the primary bundle
    sim_expired = True  # bool to check whether the sim has all bundles depleted or not
    bundles_cursor = list(get_iccid_bundles_for_user(email, iccid, skip, page_size + 1))

    transaction_records = bundles_cursor[0]["records"] if bundles_cursor else []
    records_count = bundles_cursor[0]["count"] - 1 if bundles_cursor else 0
    for bundle in transaction_records:
        if not bundle.get("amount_paid"):
            bundle["amount_paid"] = 0
        coverage = display_name = "Global"
        if bundle["bundle_category"] == "region":
            coverage = bundle["region_code"]
            display_name = str(bundle["region_name"]).capitalize()
        elif bundle["bundle_category"] == "country":
            coverage = str(bundle["country_code_list"][0])
            display_name = str(bundle["country_list"][0]).capitalize()
        validity_date = bundle.get("validity_date", dt.datetime.utcnow()).strftime(
            literals.DATETIME_FORMAT_2
        )
        payment_date = bundle.get("payment_date", dt.datetime.utcnow()).strftime(
            literals.DATETIME_FORMAT_2
        )
        bundle_type = bundle.get("bundle_type")
        status = bundle.get("status")
        bundle_object = dict(
            bundle_type=bundle_type,
            order_number=bundle.get("order_number"),
            amount_paid=round(bundle.get("amount_paid"), 2),
            bundle_name=bundle.get("bundle_name"),
            bundle_code=bundle.get("bundle_code"),
            display_name=display_name,
            coverage=coverage,
            payment_date=payment_date,
            validity_date=validity_date,
            validity_amount=bundle.get("validity_amount"),
            data_amount=bundle.get("data_amount"),
            data_unit=bundle.get("data_unit"),
            sent_via="Email",
        )

        if status == "Active":
            bundle_object["display_status"] = "active"
            bundle_object["country_list"] = bundle.get("country_list", [])
            bundle_object["country_code_list"] = bundle.get("country_code_list", [])
            active_bundle = bundle_object
            sim_expired = False  # means that there is at least 1 active bundle and thus the sim is till active
            continue
        elif status == "Expired":
            bundle_object["display_status"] = "used"
        else:
            bundle_object["display_status"] = "pending"

        if bundle_type == "Primary Bundle":
            primary_bundle = bundle_object
            primary_bundle["country_list"] = bundle.get("country_list", [])
            primary_bundle["country_code_list"] = bundle.get("country_code_list", [])
        else:
            transaction_history_wo_primary.append(bundle_object)
        transaction_history.append(bundle_object)
    accumulate_data = (
        isinstance(
            cm.Profiles.objects(
                iccid=iccid, vendor_name__in=ACCUMULATED_VENDOR_LIST
            ).first(),
            cm.Profiles,
        )
        and records_count != 0
    )
    if not accumulate_data:
        data["active_bundle"] = primary_bundle if sim_expired else active_bundle
        data["transaction_history"] = (
            transaction_history_wo_primary if sim_expired else transaction_history
        )
    else:
        primary_bundle = active_bundle if not primary_bundle else primary_bundle
        primary_bundle["data_amount"] = round(get_all_topups_balance(iccid) / 1024, 1)
        primary_bundle["validity_date"] = get_iccid_topup_expiry_date(iccid)
        data["active_bundle"] = primary_bundle
        data["transaction_history"] = []
        records_count = 0

    return data, records_count

import datetime
import logging
import threading
import uuid
from datetime import timedel<PERSON>
from enum import Enum
from hashlib import md5
from typing import Union

import mongoengine
import pymongo.command_cursor
from mongoengine import Q, NotUniqueError

import src
from app_models import mobiles, consumer_models
from app_models import reseller_models
from app_models.consumer_models import Countries
from app_models.consumer_models import (
    Profiles,
    CashbackHistory,
    Bundles,
    Labels,
    RewardHistory,
    Vendors
)
from app_models.main_models import Settings, NotificationLogs
from app_models.mobiles import AppUserDetails, RedeemCodes, AppVersionList
from app_models.reseller_models import Order_history, BundleData, SharedProfiles
from b2c_helpers.constaints import BAYOBAB_VENDOR
from b2c_helpers.constaints import CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE, TELKOMSEL_VENDOR, LOTUS_FLARE_VENDOR
from b2c_helpers.db_helper import (
    SubscriberFallbackUpdated,
    check_inventory_availability,
    accumulate_vendor_data
)
from b2c_helpers.email_helper import SubscriberEmailHelper
from b2c_helpers.firebase import Firebase
from b2c_helpers.support_helper import add_data_amount_to_consumption_cache
from b2c_helpers.support_helper import (
    send_custom_support_email,
    send_custom_monitor_email,
    deactivate_vendor,
)
from b2c_helpers.vendors import (
    MontyMobile,
    Flexiroam,
    ESIMGo,
    Vodafone,
    Indosat,
    Orange,
    MontyReseller,
    FlexiroamAPI,
    Bayobab, TelkomselVendor
)
from b2c_helpers.vendors_integrations.lotus_flare_vendor import LotusFlare
from instance import consumer_config as instance_config
from instance.consumer_config import firebase_config, decrypted_wp_qr_code
from src import literals
from src.cipher import hash_string, decrypt_pk
from src.global_helpers.api_helper import (
    get_vodafone_msisdn_by_iccid,
    send_order_email_api_reseller,
)
from src.global_helpers.db_helper import (
    deactivate_bundle,
    save_reward_history_callback,
    check_referring_user_count_limit,
    check_referring_user_amount_limit,
    save_payment_logs_transaction,
    change_customer_balance,
    keycloack_settings,
    call_keycloack_admin,
    get_user,
    reset_free_profile,
    get_empty_profile,
    save_payment_log,
    get_payment_log,
    get_setting,
    get_referring_user,
    user_was_referred,
    is_promo_used,
    get_redeem_record,
    save_promo_log,
    validate_app_token,
    is_email_blacklisted,
    reserve_promo_log,
    get_bundles_topup2,
    save_cron_vodafone_profile,
    save_callback,
    save_vodafone_profile,
    check_callback,
    add_to_update_bundle_version,
    get_bundle_object,
    create_profile,
    check_cooldown,
    get_customer_reserved_unpaid_profiles,
    get_email_list_from_device_id,
    countries_aggregate_pipeline,
    check_inventory_profiles_count,
    check_empty_profile_count,
    check_bundle_count, get_email_list_from_ipv4,
)
from src.global_helpers.utils import is_nonempty_nonspace
from src.global_helpers.utils import (
    split_activaton_code,
    InventoryDepleted,
    BundleDepleted,
    EmptyProfilesDepleted,
    NothingToFixHere,
    AssignCooldown,
    PermanentBlock,
)
from src.global_helpers.wrappers import try_except_func
from src.request_parsers.bundle_payment_operations_input import AssignStripeRequestDTO

logger = logging.getLogger(__name__)

buy_bundle_cat = "1"
buy_topup_cat = "2"
refer_and_earn_cat = "3"
cashback_cat = "4"
first = "$first"
country_code_list_col = "$country_code_list"
bundle_marketing_name = "$bundle_marketing_name"
group_expr = "$group"
bundle_name_col = "$bundle_name"
bundle_code_col = "$bundle_code"
bundle_category_col = "$bundle_category"
country_list_col = "$country_list"
retail_price_col = "$retail_price"
bundle_duration_col = "$bundle_duration"
region_code_col = "$region_code"
data_amount_col = "$data_amount"
data_unit_col = "$data_unit"
currency_code_col = "$currency_code"
validity_amount_col = "$validity_amount"
project_expr = "$project"
sort_operation = "$sort"
region_name = "$region_name"


def check_and_reward_referring_user(email, order_number):
    logger.info("Trying to check and reward referring user with email: %s , and order number : %s", email, order_number)
    referring_user = get_referring_user(email)
    customer = get_customer(referring_user.from_user_email) if referring_user else None
    settings = get_setting()
    reward_amount_limit_usd = settings.reward_amount_limit_usd
    reward_user_refer_limit = settings.reward_user_refer_limit
    reward_per_purchase = settings.reward_per_purchase
    logger.debug("Data retrieved : referring user: %s , customer: %s, settings: %s,"
                " reward amount limit usd : %s, reward user refer limit: %s, "
                "reward per purchase: %s", referring_user, customer, settings, reward_amount_limit_usd, reward_user_refer_limit, reward_per_purchase)
    if (
        customer
        and check_referring_user_count_limit(email, reward_user_refer_limit)
        and check_referring_user_amount_limit(email, reward_amount_limit_usd, reward_per_purchase)
        and not user_was_referred(email)
    ):
        change_customer_balance(customer=customer, amount=reward_per_purchase, type=1)
        logger.info(" added %s to customer %s", reward_per_purchase, referring_user.from_user_email)
        save_reward_history_callback(
            from_user=referring_user.from_user_email,
            to_user=email,
            order_number=order_number,
            rewarded_amount=reward_per_purchase,
        )
        logger.info("saved reward history")


@try_except_func
def refer_and_earn_v2(order):
    logger.info("order %s checking referral eligibility")
    configurables = Settings.objects().first()
    if order.referred_by and order.excess_amount <= 0.5:
        AppUserDetails.objects(user_email=order.client_email).update(inc__balance=order.excess_amount, referred_by=order.referred_by)
        AppUserDetails.objects(user_email=order.referred_by).update(inc__balance=configurables.referral_reward)
        RewardHistory(
            from_user_email=order.referred_by,
            to_user_email=order.client_email,
            order_number=order.order_number,
            rewarded_amount=configurables.referral_reward,
            purchase_date=datetime.datetime.utcnow(),
        ).save()
        logger.info("order %s referral applied and rewarded")


class AllocationBundle:
    def __init__(self):
        super().__init__()
        self.bundle_info = None
        self.user_iccid = None
        self.sim_info = None
        self.bundle_log = None

    @staticmethod
    def check_and_reward_referring_user(email, order_number):
        logger.info("Trying to check reward and referring user with email: %s and order number : %s", email,order_number)
        referring_user = get_referring_user(email)
        customer = get_customer(referring_user.from_user_email) if referring_user else None
        settings = get_setting()
        reward_amount_limit_usd = settings.reward_amount_limit_usd
        reward_user_refer_limit = settings.reward_user_refer_limit
        reward_per_purchase = settings.reward_per_purchase
        logger.debug("Data retrieved : referring user: %s , customer: %s, settings: %s,"
                    " reward amount limit usd : %s, reward user refer limit: %s, "
                    "reward per purchase: %s", referring_user, customer, settings, reward_amount_limit_usd,
                    reward_user_refer_limit, reward_per_purchase)
        if (
            customer
            and check_referring_user_count_limit(email, reward_user_refer_limit)
            and check_referring_user_amount_limit(email, reward_amount_limit_usd, reward_per_purchase)
            and not user_was_referred(email)
        ):
            change_customer_balance(customer=customer, amount=reward_per_purchase, type=1)
            logger.info("added %s to customer %s", reward_per_purchase,referring_user.from_user_email )
            save_reward_history_callback(
                from_user=referring_user.from_user_email,
                to_user=email,
                order_number=order_number,
                rewarded_amount=reward_per_purchase,
            )
            logger.info("saved reward history")

    def stripe_callback(self, data):
        logger.info("Trying String callback with data : %s",  data)
        # we added to check if data.get("event") is different to charge.succeeded as the client_secret is empty string
        if not data.get("client_secret") and data.get("event") != "charge.succeeded":
            logger.info("didn't receive client secret in this callback, returning")
            return {"status": False}

        event_id = data.get("stripe_request_id")
        event = data.get("event")
        status = data.get("status")
        stripe_client_secret = data.get("client_secret")
        logger.info("event %s received: status %s", event, status)

        stripe_client_secret_hashed = md5(stripe_client_secret.encode()).hexdigest()
        if not src.stripe_helper.stripe_transaction_status(event_id, event):
            logger.info("event %s callback verification failed for event_id %s", event, event_id)
            return {"status": False}
        if status == "succeeded" and event == "charge.succeeded":
            payment_intent = data.get("payment_intent", "")
        else:
            payment_intent = event_id

        order: Order_history = Order_history.objects(stripe_client_secret=stripe_client_secret_hashed).first()

        if order and payment_intent != "":
            stripe_intent_hashed = md5(payment_intent.encode()).hexdigest()
            logger.info(
                "get payment_intent %s and should be saved as  %s",
                payment_intent,
                stripe_intent_hashed,
            )
            order.update(payment_intent_secret=stripe_intent_hashed)
        else:

            if payment_intent:
                stripe_intent_hashed = md5(payment_intent.encode()).hexdigest()
                order = Order_history.objects(payment_intent_secret=stripe_intent_hashed).first()
                logger.info("get order based on  payment_intent %s", stripe_intent_hashed)

        if not order:
            logger.info(
                "couldn't find related order for client_secret %s",
                stripe_client_secret_hashed,
            )
            return {"status": False}
        vendor: Vendors = Vendors.objects(vendor_name=order.bundle_data.vendor_name).first()
        order.update(payment_date=datetime.datetime.utcnow())

        payment_log = {"order_number": order.order_number}
        payment_record = save_payment_log(payment_log)

        if status == "succeeded" and event == "charge.succeeded":
            order.update(stripe_request_id=data.get("event_id"))

        if order.order_status in ["Successful", "Canceled", "CanceledImplicitly"]:
            logger.info("[stripe_callback]||IGNORED EVENT||event_id: %s||reason: Order status indicates it was already processed or canceled", event_id)
            return {"status": False}

        if order.order_status == "Successful":
            return {"status": False}
        if status == "succeeded" and event == "payment_intent.succeeded":
            logger.info("order %s payment was successful, fulfilling order", order.order_number)
            try:
                logger.info("order %s checking referral eligibility")
                refer_and_earn_v2(order)
                payment_record.update(**{"transaction_status": True})
                #   if promo code is used then user cannot use it again
                if order.promo_code:
                    logger.info("order %s has promo code, reserving record")
                    reserve_promo_log(order.client_email, order.promo_code)
                user = get_customer(order.client_email)
                bundle_code = order.bundle_code
                bundle_info = Bundles.objects(bundle_code=bundle_code).first()
                #   if the user is paying through stripe then
                if order.payment_category == PaymentMethod.credit_card.value:
                    logger.info(
                        "order %s checking legacy referral eligibility",
                        order.order_number,
                    )
                    #   Reward user which referred the paying user
                    check_and_reward_referring_user(order.client_email, order.order_number)
                    #   check if cashback is eligible for this user
                    logger.info("order %s checking cashback eligibility", order.order_number)
                    cashback_rewarded = self.cashback_for_user(user, bundle_info, order.order_number)
                    order.update(cashback_rewarded=cashback_rewarded)
                fulfill_order(order.order_number)
                logger.info(
                    "order %s fulfilled successfully and user %s is notified",
                    order.order_number,
                    order.client_email,
                )
                payment_record.update(**{"bundle_status": True})

                return {"status": True, "message": "Bundle added and email sent"}
            except Exception as e:
                logger.error("Error receiving stripe callback , Error : %s", e)
                save_payment_logs_transaction(
                    user_email=order.client_email,
                    order_number=order.order_number,
                    method_name="stripe_callback",
                    method_details="allocate_per_vendor",
                    method_failure_message=str(e),
                )

                msg_text = f"{[datetime.datetime.utcnow()]} exception while allocating bundle for user {order.client_email} as {str(e)}"
                send_custom_support_email(
                    subject="---URGENT---Couldn't allocate bundle exception",
                    body=msg_text,
                )

        elif status == "canceled" and event == "payment_intent.canceled":
            logger.info(
                "order %s payment was canceled, freeing profile and cancelling order",
                order.order_number,
            )
            try:
                no_other_orders = not Order_history.objects(iccid=order.iccid, order_status="Successful")
                if no_other_orders:
                    #   free profile as the user canceled his order
                    reset_free_profile(iccid=order.iccid)
                order.update(order_status="Canceled")
                if vendor.supports_profile_reservation:
                    bundle_info = Bundles.objects(bundle_code=order.bundle_code).first()
                    cancel_vendor_reservation(bundle_info=bundle_info, order_reference=order.order_number)
            except Exception as e:
                logger.error("Couldn't reset profile for order_number : %s error : %s", order.order_number, str(e))
            return {"status": True, "message": "order canceled"}

        elif event == "payment_intent.payment_failed":
            try:
                check_rate_limit(order.version_token, order.client_email, order.device_id, order.ipv4_address)
            except AssignCooldown:
                encrypted_payment_intent_id = order.pid_hash
                payment_intent_id = decrypt_pk(encrypted_payment_intent_id, instance_config.private_key_path)
                status, message = src.stripe_helper.cancel_payment(payment_intent_id)
                if status:
                    logger.info("Canceled Stripe Payment for order %s , because of temp cooldown.",order.order_number)
            except PermanentBlock:
                client_orders = Order_history.objects(device_id=order.device_id, order_status="Pending")
                for order_to_be_cancelled in client_orders:
                    encrypted_payment_intent_id = order_to_be_cancelled.pid_hash
                    payment_intent_id = decrypt_pk(encrypted_payment_intent_id, instance_config.private_key_path)
                    status, message = src.stripe_helper.cancel_payment(payment_intent_id)
                if status:
                    logger.info("Canceled All Pending Stripe Payments for device %s, because of perm cooldown.",order.device_id)

    def cashback_for_user(self, user: AppUserDetails, bundle_info: Bundles, order_number: str) -> bool:
        """
            :param user:
            :param bundle_info:
            :param order_number:
            :return bool:

        This function saves a user cashback record and then proceeds to check if the user is eligible for cashback,
        if true, the user will then receive a certain amount added to his wallet according to configurable attributes
        """
        try:
            logger.info("Trying to cashback for user with : app user datails user: %s, bundle info :%s, order number :%s", user, bundle_info, order_number)
            settings: Settings = get_setting()
            if settings:
                percentage_of_reward: float = settings.percentage_of_reward
                purchase_threshold_for_reward: int = settings.purchase_threshold_for_reward
                rewarded_amount: float = bundle_info.retail_price * (percentage_of_reward / 100)

                logger.info("percentage of reward :%s, and purchase threshold for reward : %s, "
                            "rewarded amount : %s", percentage_of_reward, purchase_threshold_for_reward, rewarded_amount)
                """
                    save record before checking if user is eligible for cashback in order to benefit from current order
                """
                cashback_record = CashbackHistory(
                    **{
                        "user_email": user.user_email,
                        "bundle_code": bundle_info.bundle_code,
                        "bundle_name": bundle_info.bundle_name,
                        "bundle_marketing_name": bundle_info.bundle_marketing_name,
                        "order_number": order_number,
                    }
                ).save()
                logger.info("checking if user: %s reached reward threshold", user.user_email)
                if CashbackHistory.objects(**{"user_email": user.user_email}).count() >= purchase_threshold_for_reward:
                    cashback_record.update(
                        **{
                            "rewarded_amount": rewarded_amount,
                            "cashback_percentage": percentage_of_reward,
                        }
                    )
                    user.update(inc__balance=rewarded_amount)
                    user.reload()
                    logger.info("user: %s was rewarded with amount :%s, new balance is %s",user.user_email, rewarded_amount, user.balance)
                    return True
                return False
        except Exception as e:
            logger.error("exception on cashback for user %s : Error : %s", user.user_email, str(e))
            save_payment_logs_transaction(
                user.user_email,
                order_number,
                self.cashback_for_user.__name__,
                "failed",
                "exception on cashback",
                str(e),
            )
            return False
        except NotUniqueError as e:
            logger.error("trying to save existing order %s in cashback for user : %s", order_number, user.user_email)
            save_payment_logs_transaction(
                user.user_email,
                order_number,
                self.cashback_for_user.__name__,
                "failed",
                "trying to save existing order",
                str(e),
            )
            return False

    @staticmethod
    def cancel_payment(email, payment_intent_id):
        try:
            logger.info("Trying to cancel payment with email :%s, payment intent id : %s", email, payment_intent_id)
            if order_history := Order_history.objects(
                reseller_type="subscriber",
                client_email=email,
                pid_hash=payment_intent_id,
            ).first():
                if order_history["order_status"] == "Successful":
                    return {
                        "status": False,
                        "responseCode": 2,
                        "title": "Failed",
                        "message": literals.COULDNT_CANCEL,
                        "developerMessage": "Payment couldn't be canceled",
                    }
            else:
                return {
                    "status": False,
                    "responseCode": 2,
                    "title": "Failed",
                    "message": literals.COULDNT_CANCEL,
                    "developerMessage": "Unauthorized",
                }
            payment_intent_id = decrypt_pk(payment_intent_id, instance_config.private_key_path)
            status, message = src.stripe_helper.cancel_payment(payment_intent_id)
            default_response = {
                "status": status,
                "responseCode": 1 if status else 2,
                "title": "Success" if status else "Failed",
                "message": "",
                "developerMessage": message,
            }
            if status:
                logger.info("successful cancelling payment")
            else:
                logger.info("failure cancelling payment")
            return default_response
        except Exception as e:
            logger.error("exception cancelling payment : %s ", str(e))
            return {
                "status": False,
                "responseCode": 3,
                "title": "Failed",
                "message": literals.TRY_AGAIN,
                "developerMessage": str(e),
            }


@try_except_func
def enough_bundle_count(bundle_info):
    logger.info("Checking bundle info : %s ", bundle_info)
    if bundle_info.vendor_name == "Monty Mobile":
        return bundle_info.allocated_unit >= bundle_info.consumed_unit
    return bundle_info.allocated_unit > bundle_info.consumed_unit


def update_user_attributes(customer, **kwargs):
    logger.info("Trying to update user attributes of customer %s", customer)
    keycloak_settings = keycloack_settings(instance_config.operator_name)
    keycloak_admin = call_keycloack_admin(keycloak_settings)
    user_id_keycloak = keycloak_admin.get_user_id(customer.user_name)
    if user_id_keycloak:
        user_doc = {
            "email": customer.user_email,
            "username": customer.user_name,
            "enabled": True,
            "emailVerified": True,
            "attributes": {},
        }
        for key, value in kwargs.items():
            user_doc["attributes"][key] = value
        keycloak_admin.update_user(user_id=user_id_keycloak, payload=user_doc)


@try_except_func
def get_customer(email):
    return get_user(email)


def profile_taken_by_others(iccid, client_email):
    logger.info("Trying to check if profile taken by others , iccid :%s, and client email :%s", iccid,  client_email)
    return Order_history.objects(
        iccid=iccid,
        order_status__in=["Successful", "Pending"],
        client_email__ne=client_email,
    ).first()


@try_except_func
def check_reserved_iccid(client_email, device_id):
    for order in get_customer_reserved_unpaid_profiles(client_email, device_id):
        if not profile_taken_by_others(iccid=order.iccid, client_email=order.client_email):
            reset_free_profile(iccid=order.iccid)
            order.update(order_status="CanceledImplicitly")


def get_bundle_info(bundle_code, topup_code) -> Bundles:
    if topup_code:
        bundle_info = get_bundle_object(topup_code, check_active=False)
    else:
        bundle_info = get_bundle_object(bundle_code, check_active=True)
    return bundle_info


@try_except_func
def apply_promo_discount(retail_price, discount_percent):
    return round((retail_price - round((discount_percent * retail_price) / 100, 2)), 2) if discount_percent <= 100 else retail_price


class PaymentMethod(Enum):
    credit_card = 1
    wallet = 2
    applepay = 3
    googlepay = 4



def get_vodafone_profile_from_cron(get_profile_input):
    notification_type = get_profile_input["notificationType"]
    order_reference = get_profile_input["acknowledgementID"]
    order_number = get_profile_input.get("customReference", "")
    save_callback(get_profile_input, order_reference)
    if notification_type == "RSP:Allocated" and get_profile_input["activationCode"] != "":
        result = save_cron_vodafone_profile(get_profile_input)
    return {"data": {}}


def get_profile_info(input):
    return {
        "notification_type": input.get("notificationType", ""),
        "order_reference": input.get("acknowledgementID", ""),
        "order_number": input.get("customReference", ""),
        "activation_code": input.get("activationCode", ""),
        "iccid": str(input.get("idValue")),
        "settings": get_setting(),
    }


def update_order_profile(order, profile_input):
    smdp_address, matching_id = split_activaton_code(profile_input["activation_code"])
    qr_code_value = "LPA:1${}${}".format(smdp_address, matching_id)
    msisdn = get_vodafone_msisdn_by_iccid(profile_input["iccid"])
    Profiles.objects(iccid=profile_input["iccid"]).update(**{"msisdn": msisdn})
    current_date_plus_90_days = datetime.datetime.utcnow() + timedelta(90)
    qr_code_link = "{}/v2/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
        instance_config.decrypted_wp_qr_code, matching_id, smdp_address, True
    )
    new_data = {
        "iccid": str(profile_input["iccid"]),
        "smdp_address": smdp_address,
        "matching_id": matching_id,
        "activation_code": matching_id,
        "qr_code_value": qr_code_value,
        "qr_code_link": qr_code_link,
        "vendor_msisdn": msisdn,
        "expiry_date": current_date_plus_90_days,
        "plan_status": "Active",
        "plan_uid": profile_input["order_reference"],
        "order_status": "Successful",
    }
    if order.reseller_id and order.reseller_id.id:
        new_data["activation_code"] = qr_code_value
    order.update(**new_data)
    order.reload()


def process_transaction(order):
    logger.info("Trying to process transaction with order: %s", order)
    # Thread handling for sending notifications and updating order profiles remains here.
    if order.reseller_type == "reseller":
        send_order_email_api_reseller(str(order.reseller_id.id), str(order.id))
        logger.info("email sent successfully")
    elif order.reseller_type == "subscriber":
        settings = get_setting()

        firebase_helper = Firebase(settings=settings, credentials_file_path=instance_config.firebase_ios_file)
        transaction_log = get_payment_log(order.order_number)
        customer = AppUserDetails.objects(user_email=order.client_email).first()
        sub_email_helper = SubscriberEmailHelper(customer=customer, instance_config=instance_config)
        successful_cc_payment = (
            order.payment_category == PaymentMethod.credit_card.value and transaction_log and transaction_log.transaction_status
        )
        successful_wallet_payment = order.payment_category == PaymentMethod.wallet.value
        if successful_wallet_payment or successful_cc_payment:
            email_thread_target = sub_email_helper.send_invoice_email
            category = buy_bundle_cat
        fe_url = "{}/consumption?iccid={}".format(instance_config.front_end_url, order.iccid)

        notification = NotificationLogs(
            **{
                "email": order.client_email,
                "transaction": order.order_type,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "bundle_code": order.bundle_code,
                "iccid": order.iccid,
                "validity_date": order.expiry_date,
                "reseller_type": "subscriber",
            }
        ).save()
        if customer["first_name"] != "":
            username = customer["first_name"].capitalize()
        else:
            username = customer["user_email"]

        matching_id = f"LPA:1${order.smdp_address}${order.activation_code}"
        activate_esim_link = "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata={}".format(matching_id)
        with src.app.app_context():
            email_data = {
                "user": username,
                "price": order.paid_amount_wallet + order.paid_amount_credit_card,
                "coverage": order.bundle_data.coverage,
                "smdp_address": order.smdp_address,
                "activation_code": order.activation_code,
                "validity": order.bundle_data.bundle_duration,
                "data_amount": order.bundle_data.data_amount,
                "data_unit": order.bundle_data.data_unit,
                "bundle_name": order.bundle_data.bundle_name,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "bundle_code": order.bundle_data.bundle_code,
                "montyesim_msisdn": settings.whatsapp_misisdn,
                "qr_code_value": order.qr_code_value,
                "activate_esim_link": activate_esim_link,
                "fe_url": fe_url,
            }
            push_notification_data = {
                "bundle_code": order.bundle_code,
                "topup_code": order.bundle_code,
                "history_id": order.order_number,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "notf_id": notification.notification_id,
                "category": category,
            }
            cashback_data = {
                "category": cashback_cat,
                "cashback_percent": settings.percentage_of_reward,
            }
            email_thread = threading.Thread(target=email_thread_target, kwargs=email_data)

            push_notif_thread = threading.Thread(
                target=firebase_helper.send_notification,
                kwargs=dict(
                    customer=customer,
                    data=push_notification_data,
                    transaction=order.order_type,
                ),
            )
            if order.cashback_rewarded:
                cashback_thread = threading.Thread(
                    target=firebase_helper.send_notification,
                    kwargs=dict(customer=customer, data=cashback_data, silent=True, duration=15),
                )
                cashback_thread.start()
            email_thread.start()
            push_notif_thread.start()

    pass


def process_top_up(order, profile_input):
    # Thread handling for sending notifications and updating top-up profiles remains here.
    iccid = profile_input["iccid"]
    past_order = reseller_models.Order_history.objects(iccid=iccid).first()
    msisdn = get_vodafone_msisdn_by_iccid(iccid)
    Profiles.objects(iccid=iccid).update(**{"msisdn": msisdn})
    order.update(**{"vendor_msisdn": msisdn, "order_status": "Successful"})
    new_data = {
        "iccid": past_order.iccid,
        "smdp_address": past_order.smdp_address,
        "matching_id": past_order.matching_id,
        "qr_code_value": past_order.qr_code_value,
        "qr_code_link": past_order.qr_code_link,
        "activation_code": past_order.activation_code,
        "plan_uid": profile_input["order_reference"],
    }
    order.update(**new_data)
    order.reload()
    if order.reseller_type == "reseller":
        send_order_email_api_reseller(str(order.reseller_id.id), str(order.id))
    elif order.reseller_type == "subscriber":
        settings = get_setting()
        firebase_helper = Firebase(settings=settings, credentials_file_path=instance_config.firebase_ios_file)
        customer = AppUserDetails.objects(user_email=order.client_email).first()
        sub_email_helper = SubscriberEmailHelper(customer=customer, instance_config=instance_config)
        email_thread_target = sub_email_helper.send_topup_email
        category = buy_topup_cat

        if customer["first_name"] != "":
            username = customer["first_name"].capitalize()
        else:
            username = customer["user_email"]
        fe_url = "{}/consumption?iccid={}".format(instance_config.front_end_url, order.iccid)
        notification = NotificationLogs(
            **{
                "email": order.client_email,
                "transaction": order.order_type,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "bundle_code": order.bundle_code,
                "iccid": order.iccid,
                "validity_date": order.expiry_date,
                "reseller_type": "subscriber",
            }
        ).save()
        with src.app.app_context():
            email_data = {
                "user": username,
                "price": order.paid_amount_wallet + order.paid_amount_credit_card,
                "coverage": order.bundle_data.coverage,
                "smdp_address": order.smdp_address,
                "activation_code": order.activation_code,
                "validity": order.bundle_data.bundle_duration,
                "data_amount": order.bundle_data.data_amount,
                "data_unit": order.bundle_data.data_unit,
                "bundle_name": order.bundle_data.bundle_name,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "bundle_code": order.bundle_data.bundle_code,
                "montyesim_msisdn": settings.whatsapp_misisdn,
                "qr_code_value": order.qr_code_value,
                "fe_url": fe_url,
            }
            push_notification_data = {
                "bundle_code": order.bundle_code,
                "topup_code": order.bundle_code,
                "history_id": order.order_number,
                "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
                "notf_id": notification.notification_id,
                "category": category,
            }
            cashback_data = {
                "category": cashback_cat,
                "cashback_percent": settings.percentage_of_reward,
            }
            email_thread = threading.Thread(target=email_thread_target, kwargs=email_data)

            push_notif_thread = threading.Thread(
                target=firebase_helper.send_notification,
                kwargs=dict(
                    customer=customer,
                    data=push_notification_data,
                    transaction=order.order_type,
                ),
            )
            if order.cashback_rewarded:
                cashback_thread = threading.Thread(
                    target=firebase_helper.send_notification,
                    kwargs=dict(customer=customer, data=cashback_data, silent=True, duration=15),
                )
                cashback_thread.start()
            email_thread.start()
            push_notif_thread.start()


def get_vodafone_profile(payload):
    reseller_id = None
    notification_type = payload.get("notificationType", "")
    order_reference = payload.get("acknowledgementID", "")
    order_number = payload.get("customReference", "")
    iccid = payload.get("idValue")
    if check_callback(**payload):
        return {"success": False, "message": "already received"}
    save_callback(payload, order_number)
    profile_info = get_profile_info(payload)
    if notification_type == "RSP:Allocated":

        if order_number:
            order = Order_history.objects(order_number=order_number).first()
        else:
            order = Order_history.objects(plan_uid=order_reference).first()
        if order.reseller_id and order.reseller_id.id:
            payload["reseller_id"] = order.reseller_id.id
        save_vodafone_profile(payload)
        update_order_profile(order, profile_info)
        process_transaction(order)

    elif notification_type == "thing topped up":
        if order_number:
            order = Order_history.objects(order_number=order_number).first()
        else:
            order = Order_history.objects(plan_uid=order_reference).first()
        process_top_up(order, profile_info)
    logger.info("%s for iccid %s", notification_type, iccid)
    return {"success": True, "message": "callback processed successfully"}


def allocated_bundle_info(allocated_bundle_input):
    data = {}
    order_number = allocated_bundle_input["payment_otp"]
    order = Order_history.objects(order_number=order_number).first()

    if order:
        data["send_qr_code"] = False
        data["msisdn"] = None
        data["country_code"] = None
        data["verify_msisdn"] = None
        data["history_log_id"] = order.order_number
    return data


def create_stripe_order(customer, paid_amount_credit_card, access_permission):
    logger.info("creating stripe order for user %s", customer.user_email)
    (
        status,
        publishable_key,
        customer_id,
        ephemeral_key,
        client_secret,
        payment_intent_id,
    ) = src.stripe_helper.create_payment(
        customer=customer,
        price=paid_amount_credit_card * 100,
        save_credit_card=access_permission == 1,
        return_ephemeral_key=access_permission == 1,
    )
    client_secret = decrypt_pk(client_secret, instance_config.private_key_path)
    return publishable_key, customer_id, ephemeral_key, client_secret, payment_intent_id


def calculate_coverage_display_name(bundle_info: Bundles) -> (str, str):
    if bundle_info.bundle_category == "region":
        coverage = bundle_info.region_code
        display_name = str(bundle_info.region_name).capitalize()

    elif bundle_info.bundle_category == "country":
        coverage = str(bundle_info.country_code_list[0])
        display_name = str(bundle_info.country_list[0]).capitalize()

    elif bundle_info.bundle_category == "cruise":
        coverage = "cruise"
        display_name = "cruise"
    else:
        coverage = "Global"
        display_name = "Global"

    return coverage, display_name


def create_order(bundle_info: Bundles, successful: bool = True, **kwargs) -> Order_history:
    coverage, display_name = calculate_coverage_display_name(bundle_info)
    kwargs["order_status"] = "Pending"
    if successful:
        kwargs["order_status"] = "Successful"
    embedded_bundle: BundleData = BundleData(
        bundle_code=bundle_info.bundle_code,
        bundle_category=bundle_info.bundle_category,
        bundle_name=bundle_info.bundle_name,
        bundle_marketing_name=bundle_info.bundle_marketing_name,
        bundle_duration=bundle_info.bundle_duration,
        retail_price=bundle_info.retail_price,
        currency_code=bundle_info.currency_code,
        data_amount=bundle_info.data_amount,
        data_unit=bundle_info.data_unit,
        country_list=bundle_info.country_list,
        country_code_list=bundle_info.country_code_list,
        coverage=coverage,
        display_name=display_name,
        unlimited=bundle_info.unlimited,
        bundle_vendor_code=bundle_info.bundle_vendor_code,
        vendor_name=bundle_info.vendor_name,
    )
    kwargs["bundle_data"] = embedded_bundle
    return Order_history(**kwargs).save()


def generate_qr_code_link(base_url, smdp_address, matching_id, has_lpa):
    return f"{base_url}/generate-qr-code/{smdp_address}/{matching_id}/{has_lpa}/qr_code.jpg"


def update_order_with_profile(order, iccid, matching_id, qr_code_link, smdp_address, qr_code_value, plan_uid):
    order.update(
        iccid=iccid,
        matching_id=matching_id,
        qr_code_link=qr_code_link,
        smdp_address=smdp_address,
        activation_code=matching_id,
        qr_code_value=qr_code_value,
        plan_uid=plan_uid,
    )


def handle_flexi_order(
    order: Order_history,
    bundle: Bundles,
    flexiroam_helper,
    decrypted_wp_qr_code=None,
    profile=None,
    is_topup=False,
):
    # Determine whether to buy a plan or top-up
    if is_topup and profile:
        res = flexiroam_helper.buy_topup(bundle.bundle_vendor_code, int(profile.sku))
    else:
        res = flexiroam_helper.buy_plan(bundle.bundle_vendor_code, True)
    order_type = "Top-up" if is_topup else "Buy Bundle"
    if isinstance(res, dict) and res.get("error"):
        error_message = res["error"]

        logger.error("order_type :%s  order %s failed", order_type , order.order_number)

        send_custom_support_email(
            f": Could not {order_type} Flexiroam",
            f"Order {order.order_number} couldn't {order_type.lower()} {order.bundle_code} "
            f"for {'ICCID ' + profile.iccid if profile else 'new profile'} for error message {error_message}",
        )

        logger.error(
            f"Order %s couldn't {order_type.lower()} with bundle %s for error message %s",
            order.order_number,
            order.bundle_code,
            error_message,
        )

        if not is_topup:
            bundle.update(set__is_active=False)

        return False, False

    # Retrieve profile information
    profile_info = flexiroam_helper.get_sim_details(res.get("sku"))
    active_plans = profile_info.get("active_plans", [])
    plan_uid = active_plans[-1].get("plan_uuid") if active_plans else ""

    if is_topup and profile:
        matching_id = profile.matching_id
        iccid = profile.iccid
        smdp_address = profile.smdp_address
        qr_code_value = f"LPA:1${smdp_address}${matching_id}"
        qr_code_link = generate_qr_code_link(decrypted_wp_qr_code, matching_id, smdp_address, profile.has_lpa)
    else:
        profile_check = consumer_models.Profiles.objects(sku=str(res.get("sku"))).first()
        if profile_check and getattr(profile_check, "bundle_code", None):
            if profile_check.bundle_code.strip() != "":
                send_custom_support_email(
                    f": Could not {order_type} Flexiroam",
                    f"Profile already exists for SKU {res.get('sku')} and is already assigned to {profile_check.bundle_code}",
                )

                logger.error("Profile already exists for SKU :%s  and is already assigned to : %s", res.get('sku'), profile_check.bundle_code)
                deactivate_bundle(bundle.bundle_code)
                return False, False
        parts = res.get("esim_lpa").split("$")
        iccid = str(profile_info.get("details", {}).get("iccid", ""))
        qr_code_value = res.get("esim_lpa")
        qr_code_link = generate_qr_code_link(decrypted_wp_qr_code, parts[1], parts[2], True)

        create_profile(
            bundle.vendor_name,
            iccid,
            parts[1],
            parts[2],
            qr_code_value,
            res.get("sku"),
            bundle.profile_names,
            profile_check,
        )

    update_order_with_profile(
        order,
        iccid,
        parts[2] if not is_topup else matching_id,
        qr_code_link,
        parts[1] if not is_topup else smdp_address,
        qr_code_value,
        plan_uid,
    )

    logger.info(
        "Order %s %s updated successfully",
        order.order_number,
        "top-up" if is_topup else "bundle",
    )
    return True, True


@SubscriberFallbackUpdated
def load_vendor_order(order: Order_history, bundle: Bundles, profile: Union[Profiles, None]):
    sent_email: bool = False
    load_vendor: bool = False
    message: str = "No info"
    vendor: Vendors = Vendors.objects(vendor_name=bundle.vendor_name).first()
    if not vendor:
        logger.error("bundle with vendor name %s has no linked vendor", bundle.vendor_name)
        raise Exception(f"bundle with vendor name {bundle.vendor_name} has no linked vendor")
    if bundle.vendor_name == "Flexiroam":
        flexiroam_helper = Flexiroam()
        res = flexiroam_helper.load_bundle_to_profile(sku=profile.sku, iccid=profile.iccid, bundle_vendor_code=bundle.bundle_vendor_code)
        message = res[1]
        if not res[0]:
            logger.error("order %s couldn't load plan %s for profile %s", order.order_number, order.bundle_code, profile.iccid)
            return sent_email, load_vendor, order, message
        plan_uid = res[0].get("data", {}).get("plan_uid")
        order.update(plan_uid=plan_uid)
        profile.update(payment_date=datetime.datetime.utcnow())
        logger.info("order %s updated with plan uid %s successfully", order.order_number, plan_uid)
        sent_email = True
        if plan_uid:
            load_vendor = True

    elif bundle.vendor_name == "Monty Mobile":
        monty_mobile = MontyMobile()
        res, message = monty_mobile.load_bundle_to_profile(
            sku=profile.sku,
            iccid=profile.iccid,
            bundle_vendor_code=bundle.bundle_vendor_code,
        )
        if not res:
            logger.error(
                "order %s couldn't load plan %s for profile %s",
                order.order_number,
                order.bundle_code,
                profile.iccid,
            )
            return sent_email, load_vendor, order, message

        plan_uid = res.get("data", {}).get("simplan", {}).get("plan_uid")
        order.update(plan_uid=plan_uid)
        profile.update(payment_date=datetime.datetime.utcnow())
        logger.info(
            "order %s updated with plan uid %s successfully",
            order.order_number,
            plan_uid,
        )
        if plan_uid:
            load_vendor = True
        sent_email = True

    elif bundle.vendor_name == BAYOBAB_VENDOR:
        bayobab = Bayobab()

        res = bayobab.attach_offer(iccid=profile.iccid, offer_id=bundle.bundle_vendor_code)
        if res and not res.get("errorCode"):
            plan_uid = res.get("content", [{}])[0].get("id")
            order.update(plan_uid=plan_uid)
            profile.update(payment_date=datetime.datetime.utcnow())
            logger.info(
                "order %s updated with plan uid %s successfully",
                order.order_number,
                plan_uid,
            )
            load_vendor = True
            sent_email = True
        else:
            logger.error(
                "%s order %s couldn't load plan %s for profile %s", BAYOBAB_VENDOR,
                order.order_number,
                order.bundle_code,
                profile.iccid,
            )
            message = res.get("errorMessage")
            return sent_email, load_vendor, order, message

    elif bundle.vendor_name == "eSIMGo":
        esimgo_helper = ESIMGo()
        if order.order_type == "BuyBundle":
            esimgo_create_order = esimgo_helper.assign_profiles(bundle_vendor_code=bundle.bundle_vendor_code)
            if not esimgo_create_order:
                logger.error(
                    "order %s couldn't buy new profile with bundle %s",
                    order.order_number,
                    order.bundle_code,
                )
                return sent_email, load_vendor, order, message
            order_reference = esimgo_create_order.get("orderReference")
            logger.info("order %s getting order info", order.order_number)
            profile_info = esimgo_create_order.get("order", [{}])[0].get("esims", [{}])[0]
            iccid, matching_id, smdp_address = (
                profile_info.get("iccid"),
                profile_info.get("matchingId"),
                profile_info.get("smdpAddress"),
            )
            qr_code_value = f"LPA:1${smdp_address}${matching_id}"
            qr_code_link = f"{decrypted_wp_qr_code}/generate-qr-code/{matching_id}/{smdp_address}/{True}/qr_code.jpg"
            order.update(
                iccid=iccid,
                matching_id=matching_id,
                qr_code_link=qr_code_link,
                smdp_address=smdp_address,
                activation_code=matching_id,
                qr_code_value=qr_code_value,
                plan_uid=order_reference,
            )
            logger.info("order %s updated with profile info successfully", order.order_number)
            create_profile(bundle.vendor_name, iccid, smdp_address, matching_id, qr_code_value)
            logger.info("order %s saved new profile %s", order.order_number, iccid)
            sent_email = True
            load_vendor = True
        elif order.order_type == "BuyTopup":
            esimgo_create_order = esimgo_helper.assign_profiles(bundle_vendor_code=bundle.bundle_vendor_code, buy_bundle=False)
            if not esimgo_create_order:
                logger.error(
                    "order %s couldn't buy new plan %s to apply on profile %s",
                    order.order_number,
                    order.bundle_code,
                    order.iccid,
                )
                return sent_email, load_vendor, order, message

            order_reference = esimgo_create_order.get("orderReference")
            res = esimgo_helper.apply_topup(bundle_vendor_code=bundle.bundle_vendor_code, iccid=order.iccid)
            if not res:
                logger.error(
                    "order %s couldn't apply topup, try to assign new bundle recursively",
                    order.order_number,
                )
                order.update(order_type="BuyBundle", iccid="")
                order.reload()
                return load_vendor_order(order, bundle, None)

            matching_id = profile.matching_id
            iccid = profile.iccid
            smdp_address = profile.smdp_address
            qr_code_value = f"LPA:1${smdp_address}${matching_id}"
            qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
                instance_config.decrypted_wp_qr_code,
                matching_id,
                smdp_address,
                profile.has_lpa,
            )
            order.update(
                iccid=iccid,
                matching_id=matching_id,
                qr_code_link=qr_code_link,
                smdp_address=smdp_address,
                activation_code=matching_id,
                qr_code_value=qr_code_value,
                plan_uid=order_reference,
            )
            logger.info("order %s topup updated successfully with plan_uid ", order.order_number)
            sent_email = True
            load_vendor = True

    elif bundle.vendor_name == "Vodafone":
        vodafone_helper = Vodafone()
        sent_email = False
        if order.order_type == "BuyBundle":
            res = vodafone_helper.assign_profiles(
                bundle_vendor_code=bundle.bundle_vendor_code,
                order_number=order.order_number,
            )
            if not res:
                logger.error(
                    "order %s couldn't assign new profile for plan %s",
                    order.order_number,
                    order.bundle_code,
                )
                return sent_email, load_vendor, order, message
            plan_uid = res.get("orderReference")
            order.update(plan_uid=plan_uid)
            logger.info(
                "order %s updated with plan uid %s and waiting callback",
                order.order_number,
                plan_uid,
            )
            load_vendor = True
        elif order.order_type == "BuyTopup":
            res = vodafone_helper.assign_topup(
                bundle_vendor_code=bundle.bundle_vendor_code,
                order_number=order.order_number,
                iccid=profile.iccid,
            )
            if not res:
                logger.error(
                    "order %s couldn't allocate topup on profile %s",
                    order.order_number,
                    profile.iccid,
                )
                return sent_email, load_vendor, order, message
            order.update(plan_uid=res.get("acknowledgement", {}).get("id", None))
            load_vendor = True

    elif bundle.vendor_name == "Indosat":
        indosat_helper = Indosat()
        allocate_response = False

        indosat_profile = indosat_helper.get_profile_detailed(iccid=profile.iccid)

        if indosat_profile and indosat_profile.get("status") == "DEACTIVATED":
            change_base_rate_plan_status = indosat_helper.change_base_rate_plan(iccid=profile.iccid, rate_plan=bundle.bundle_vendor_code)
            if change_base_rate_plan_status:
                activate_sim_status = indosat_helper.activate_sim_status(iccid=profile.iccid)
                if activate_sim_status:
                    allocate_response = True

        else:

            allocate_response = indosat_helper.topup_iccid(iccid=profile.iccid, rate_plan=bundle.bundle_vendor_code)
            try:
                set_renewal_mode_response = indosat_helper.set_renewal_mode_to_named_plan(iccid=profile.iccid)
                if not set_renewal_mode_response:
                    logger.error(
                        "Exception: Could not set Renewal Mode to Named Plan for iccid",
                        str(profile.iccid),
                    )
                    send_custom_support_email(
                        "Indosat: Could not set Renewal Mode to Named Plan",
                        f"Indosat: Could not set Renewal Mode to Named Plan for iccid:{str(profile.iccid)}",
                    )
            except Exception as e:

                logger.error(
                    "Exception: Could not set Renewal Mode to Named Plan for iccid",
                    str(profile.iccid),
                )
                send_custom_support_email(
                    "Indosat: Could not set Renewal Mode to Named Plan",
                    f"Indosat Could not set Renewal Mode to Named Plan for iccid:{str(profile.iccid)} with exception:{str(e)}",
                )
        if not allocate_response:
            logger.error(
                "order %s %s unsuccessful for plan %s profile %s",
                order.order_number,
                order.order_type,
                order.bundle_code,
                order.iccid,
            )
            message = "vendor-side-issue"
            return sent_email, load_vendor, order, message
        logger.info("order %s successfully allocated from vendor indosat")
        load_vendor = True
        sent_email = True

    elif bundle.vendor_name == "Orange":
        orange_helper: Orange = Orange()

        data_amount = bundle.data_amount
        trigger_data_amount = bundle.data_amount

        end_date = order.expiry_date.strftime("%Y-%m-%d")

        data_unit = bundle.data_unit

        if data_unit == "MB":
            #   if bundle data unit is MB, the trigger data amount should be converted to GB
            trigger_data_amount /= 1024

        if data_unit == "GB":
            # on the other hand, if data unit is GB
            # data amount (to be topped up) should be converted to MB
            data_amount *= 1024

        total_data_amount, _, total_bundle_duration, _ = accumulate_vendor_data(profile.iccid)

        trigger_data_amount += total_data_amount

        data_amount = str(data_amount)
        trigger_data_amount = str(trigger_data_amount)

        allocate_response = orange_helper.topup_iccid(
            iccid=profile.iccid,
            data_amount=data_amount,
            zone_name=bundle.zone_name,
            emails=[instance_config.outlook_subscription_email],
            trigger_data_amount=trigger_data_amount,
            end_date=end_date,
            subscription_id=profile.subscription_id
        )
        if not allocate_response:
            logger.error(
                "order %s unsuccessful for plan %s profile %s",
                order.order_number,
                order.order_type,
                order.iccid,
            )
            message = "vendor-side-issue"
            return sent_email, load_vendor, order, message
        logger.info("order %s successfully allocated from vendor Orange")
        load_vendor = True
        sent_email = True

    elif bundle.vendor_name == "Monty Reseller":
        try:
            monty_reseller = MontyReseller()
            if order.order_type == "BuyBundle":
                res = monty_reseller.complete_order(order_reference=order.order_number).get("orders")[0]
                qr_code_link = (
                    f'{decrypted_wp_qr_code}/generate-qr-code/{res.get("matching_id")}/{res.get("smdp_address")}/{True}/qr_code.jpg'
                )
                order.update(
                    iccid=res.get("iccid"),
                    matching_id=res.get("matching_id"),
                    activation_code=res.get("matching_id"),
                    qr_code_value=res.get("activation_code"),
                    qr_code_link=qr_code_link,
                    smdp_address=res.get("smdp_address"),
                )
                logger.info("order %s completed successfully ", order.order_number)
                profile_expiry_date = datetime.datetime.utcnow() + datetime.timedelta(res.get("vendor_expiry_date_profile"))
                create_profile(
                    bundle.vendor_name,
                    res.get("iccid"),
                    res.get("smdp_address"),
                    res.get("matching_id"),
                    res.get("activation_code"),
                    profile_expiry_date,
                )
                logger.info("profile %s created successfully ", order.order_number)

                sent_email = True
                load_vendor = True
            elif order.order_type == "BuyTopup":
                base_order_id = (
                    Order_history.objects(order_status="Successful", iccid=order.iccid).order_by("-datetime").first().order_number
                )
                allocate_response: dict = monty_reseller.topup_order(
                    previous_order_reference=str(base_order_id), bundle_code=bundle.bundle_vendor_code
                )
                logger.info("order %s successfully allocated from vendor Monty")
                order.update(plan_uid=allocate_response.get("order_id"))
                load_vendor = True
                sent_email = True
        except Exception as e:
            logger.error(
                "order %s %s unsuccessful for plan %s profile %s",
                order.order_number,
                order.order_type,
                order.bundle_code,
                order.iccid,
            )
            message = str(e)
            return sent_email, load_vendor, order, message

    elif bundle.vendor_name == "FlexiroamV2":
        flexiroam_helper = FlexiroamAPI()
        if order.order_type == "BuyBundle":
            sent_email, load_vendor = handle_flexi_order(order, bundle, flexiroam_helper, decrypted_wp_qr_code)
        elif order.order_type == "BuyTopup":
            sent_email, load_vendor = handle_flexi_order(
                order,
                bundle,
                flexiroam_helper,
                decrypted_wp_qr_code=decrypted_wp_qr_code,
                profile=profile,
                is_topup=True,
            )

    elif bundle.vendor_name == TELKOMSEL_VENDOR:
        telkomsel_vendor = TelkomselVendor()

        res, msg = telkomsel_vendor.update_profile_status(sim_id=profile.sku, billing_status="IN-BILLING")
        if not res:
            logger.error(
                "%s order %s couldn't load plan %s for profile %s", TELKOMSEL_VENDOR,
                order.order_number,
                order.bundle_code,
                profile.iccid,
            )
            return sent_email, load_vendor, order, message
        plan_uid = res.get["data"]["attributes"]["uuid"]
        order.update(plan_uid=plan_uid)
        profile.update(payment_date=datetime.datetime.utcnow())
        logger.info(
            "order %s updated with plan uid %s successfully",
            order.order_number,
            plan_uid,
        )
        if plan_uid:
            load_vendor = True
        sent_email = True


    elif bundle.vendor_name == LOTUS_FLARE_VENDOR:
        lotusflare_vendor = LotusFlare()
        if order.order_type == "BuyBundle":
            sent_email, load_vendor, order, message = handle_lotusflare_buybundle(lotusflare_vendor, bundle, order, sent_email, load_vendor, message)
        elif order.order_type == "BuyTopup":
            sent_email, load_vendor, order, message = handle_lotusflare_buytopup(lotusflare_vendor, bundle, order, profile, sent_email, load_vendor, message)
    else:
        logger.error("Error with vendor configuration, please specify an API version for vendor %s", vendor.vendor_name)
        raise NotImplementedError(f"Error with vendor configuration, please specify an API version for vendor {vendor.vendor_name}")

    order.reload()
    return sent_email, load_vendor, order, message


def fulfill_order(order_number: str) -> None:
    logger.info("fulfilling order %s", order_number)
    settings: Settings = get_setting()
    order: Order_history = Order_history.objects(order_number=order_number).first()
    customer: AppUserDetails = AppUserDetails.objects(user_email=order.client_email).first()
    firebase_helper: Firebase = Firebase(settings=settings, credentials_file_path=instance_config.firebase_ios_file)
    sub_email_helper: SubscriberEmailHelper = SubscriberEmailHelper(customer=customer, instance_config=instance_config)
    bundle: Bundles = Bundles.objects(bundle_code=order.bundle_code).first()
    vendor: Vendors = Vendors.objects(vendor_name=bundle.vendor_name).first()
    profile: Union[Profiles, None] = Profiles.objects(iccid=order.iccid).first()
    profile_is_inventory: bool = False
    sent_email: bool = False
    load_vendor: bool = False
    message = "Failure reason unknown"

    if order.order_status == "Successful" and order.payment_category == PaymentMethod.credit_card.value:
        logger.info("order %s already fulfilled", order.order_number)
        return

    if order.promo_code:
        logger.info("order %s benefiting from promo code")
        reserve_promo_log(order.client_email, order.promo_code)

    if order.order_type == "BuyBundle":
        logger.info("order %s BuyBundle %s", order_number, order.bundle_code)
        email_thread_target = sub_email_helper.send_invoice_email
        category = buy_bundle_cat
        #   profile has a plan uid and thus is loaded with a bundle (from inventory)
        if profile and profile.plan_uid:
            logger.info("order %s has a loaded profile %s", order_number, profile.iccid)
            profile_is_inventory = True
            sent_email = True
            load_vendor = True
            bundle.update(inc__daily_used=1)
        #   profile does not exist or profile is not loaded with a bundle
        if not profile or not profile.plan_uid:
            logger.info(
                "order %s has no profile or profile is unloaded, checking inventory",
                order_number,
            )
            #   recheck inventory (maybe inventory was populated in the meantime)
            inventory_profile = check_inventory_availability(bundle, False, True)
            if inventory_profile:
                # found inventory profile with loaded bundle
                profile = inventory_profile
                order.update(
                    iccid=profile.iccid,
                    plan_uid=profile.plan_uid,
                    matching_id=profile.matching_id,
                    smdp_address=profile.smdp_address,
                    qr_code_value=profile.qr_code_value,
                )
                logger.info("order %s found inventory profile %s", order_number, profile.iccid)

            elif bundle.vendor_name in ["eSIMGo", "Vodafone"]:
                profile = None

    elif order.order_type == "BuyTopup":
        email_thread_target = sub_email_helper.send_topup_email
        category = buy_topup_cat

    if not profile_is_inventory:
        logger.info(
            "order %s will load from vendor %s directly for bundle %s",
            order_number,
            bundle.vendor_name,
            order.bundle_code,
        )
        sent_email, load_vendor, order, message = load_vendor_order(order, bundle, profile)
        if load_vendor and not vendor.has_constant_bundle_count:
            bundle.update(inc__allocated_unit=1)
        check_vendor_balance(bundle)

    if not load_vendor:
        logger.error(
            "Error: %s when %s %s, vendor %s for user %s order %s",
            message,
            order.order_type,
            bundle.bundle_code,
            bundle.vendor_name,
            customer.user_email,
            order.order_number,
        )
        order.update(order_status="Failed", failure_reason=message)
        if order.order_type == "BuyBundle" and profile and isinstance(profile, Profiles):
            logger.info("Freeing profile %s after failed order %s for user %s",
                        profile.iccid, order.order_number, order.client_email)
            profile.update(availability="Free")
        raise Exception(f"Couldn't {order.order_type} for bundle {bundle.bundle_code}")

    if order.order_type == "BuyBundle":
        update_bundle_info(bundle, update_consumed=True)

    if not sent_email:
        return
    #   if we are issued to send an email this means the allocation is completed successfully
    #   *** here we do not check load_vendor boolean because some vendors use asynchronous allocation ***
    logger.info(
        "order %s sending %s email to customer %s",
        order.order_number,
        order.order_type,
        order.client_email,
    )

    order.update(order_status="Successful")
    # assign the consumption with initial value
    try:
        add_data_amount_to_consumption_cache(order.iccid, bundle.bundle_code)

    except ValueError as e:
        logger.error(
            "Error occurred while calculating initial consumption cache for ICCID: %s. Exception: %s",
            profile.iccid, e
        )

    notification = NotificationLogs(
        **{
            "email": order.client_email,
            "transaction": order.order_type,
            "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
            "bundle_code": order.bundle_code,
            "iccid": order.iccid,
            "validity_date": order.expiry_date,
            "reseller_type": order.reseller_type,
        }
    ).save()

    username = customer["first_name"].capitalize() or customer["user_email"]
    fe_url = "{}/consumption?iccid={}".format(instance_config.front_end_url, order.iccid)
    matching_id = f"LPA:1${order.smdp_address}${order.activation_code}"
    activate_esim_link = "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata={}".format(matching_id)
    with src.app.app_context():
        email_data = {
            "price": order.paid_amount_wallet + order.paid_amount_credit_card,
            "coverage": order.bundle_data.coverage,
            "smdp_address": order.smdp_address,
            "activation_code": order.activation_code,
            "validity": order.bundle_data.bundle_duration,
            "data_amount": order.bundle_data.data_amount,
            "data_unit": order.bundle_data.data_unit,
            "bundle_name": order.bundle_data.bundle_name,
            "montyesim_msisdn": settings.whatsapp_misisdn,
            "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
            "bundle_code": order.bundle_data.bundle_code,
            "qr_code_value": order.qr_code_value,
            "activate_esim_link": activate_esim_link,
            "fe_url": fe_url,
        }
        if customer["first_name"] != "":
            username_email = customer["first_name"].capitalize()
            email_data["user"] = username_email.capitalize()

        push_notification_data = {
            "bundle_code": order.bundle_code,
            "topup_code": order.bundle_code,
            "history_id": order.order_number,
            "bundle_marketing_name": order.bundle_data.bundle_marketing_name,
            "notf_id": notification.notification_id,
            "category": category,
        }
        cashback_data = {
            "category": cashback_cat,
            "cashback_percent": settings.percentage_of_reward,
        }
        email_thread = threading.Thread(target=email_thread_target, kwargs=email_data)

        push_notif_thread = threading.Thread(
            target=firebase_helper.send_notification,
            kwargs=dict(
                customer=customer,
                data=push_notification_data,
                transaction=order.order_type,
            ),
        )

        if order.cashback_rewarded:
            cashback_thread = threading.Thread(
                target=firebase_helper.send_notification,
                kwargs=dict(customer=customer, data=cashback_data, silent=True, duration=15),
            )
            cashback_thread.start()
        email_thread.start()
        push_notif_thread.start()


def update_bundle_info(bundle: Bundles, update_consumed: bool = True):
    now: datetime.datetime = datetime.datetime.utcnow()
    message: str = ""
    try:
        vendor: Vendors = Vendors.objects(vendor_name=bundle.vendor_name).first()
        if not vendor.is_active:
            raise Exception("huh? how did you get here, check GET bundles APIs ASAP")

        if update_consumed:
            bundle.update(inc__consumed_unit=1)
            bundle.reload()

        check_bundle_count(bundle)  # could raise the below exceptions
        check_inventory_profiles_count(bundle)  # could raise the below exceptions
        check_empty_profile_count(bundle)  # could raise the below exceptions

    except InventoryDepleted:
        bundle.update(is_active=False, update_at=now)
        message = f"Deactivated bundle {bundle.bundle_code} | Inventory Profiles depleted"
    except BundleDepleted:
        bundle.update(is_active=False, update_at=now)
        message = f"Deactivated Bundle {bundle.bundle_code} | Bundle depleted | allocated={bundle.allocated_unit} | consumed={bundle.consumed_unit}"
    except EmptyProfilesDepleted:
        bundle.update(is_active=False, update_at=now)
        message = f"Deactivated Bundle {bundle.bundle_code} | Empty Profiles depleted"
    except NothingToFixHere:
        logger.info("everything looks normal for bundle %s", bundle.bundle_code)
        return
    except Exception as e:
        logger.error(
            "while updating [profile count] / [bundle info] for bundle %s we faced %s",
            bundle.bundle_code,
            e,
        )

    bundle.reload()
    if bundle.is_active:
        return
    #   bundle was deactivated
    logger.error("Error updating bundle info : %s", message)
    add_to_update_bundle_version([message])
    send_custom_monitor_email(
        subject=f"----URGENT---- Bundle: {bundle.bundle_vendor_code} deactivated",
        body=CUSTOMER_SUPPORT_MESSAGE_FOR_DEACTIVATE_BUNDLE.format(now, bundle.bundle_code, bundle.vendor_name, message),
    )


def get_tester_profile(bundle_info: Bundles) -> Profiles:
    return Profiles.objects(iccid__contains="husain_", vendor_name=bundle_info.vendor_name, status=True).first()


def check_rate_limit(version_token: str, email: str, device_id: str, ipv4_address: str) -> None:
    """
        check user/device-id/ipv4-address order count per specific range of time and cooldown/permanently block if user
        passed certain threshold
        :param version_token:
        :param email:
        :param device_id:
        :param ipv4_address:
    """
    device_id_rate_limit_enabled_for_platform: bool = AppVersionList.objects(version_token=version_token).first().device_id_rate_limit
    ipv4_rate_limit_enabled_for_platform: bool = AppVersionList.objects(version_token=version_token).first().ipv4_address_rate_limit
    id_types = []
    if not device_id_rate_limit_enabled_for_platform and not ipv4_rate_limit_enabled_for_platform:
        return

    if device_id_rate_limit_enabled_for_platform:
        logger.info("checking device_id rate limit for user %s", email)
        id_types.append("device_id")

    if ipv4_rate_limit_enabled_for_platform:
        logger.info("checking ipv4 rate limit for user %s", email)
        id_types.append("ip")

    if device_id_rate_limit_enabled_for_platform and not device_id:
        raise Exception("Missing Device ID")

    if ipv4_rate_limit_enabled_for_platform and not ipv4_address:
        raise Exception("Missing IPv4 Address")

    logger.info("checking malicious user activity for user %s", email)
    configurables: Settings = Settings.objects.first()

    emails_from_device_id: list = get_email_list_from_device_id(device_id)
    emails_from_ipv4: list = get_email_list_from_ipv4(ipv4_address)
    emails: list = []
    emails.extend(emails_from_device_id)
    emails.extend(emails_from_ipv4)
    emails.append(email)
    #   remove duplicates
    emails = list(set(emails))

    customers: mongoengine.QuerySet = AppUserDetails.objects(user_email__in=emails)
    if not customers:
        raise Exception("no customers found, how did you get here :O")

    utcnow: datetime.datetime = datetime.datetime.utcnow()
    cooldown_time_since: datetime.datetime = utcnow - datetime.timedelta(minutes=configurables.orders_assigned_since_minutes)
    permanent_time_since: datetime.datetime = utcnow - datetime.timedelta(
        minutes=configurables.orders_assigned_since_minutes_permanent_block
    )

    if customers.filter(block_transactions=True, block_date__gt=cooldown_time_since):
        logger.info("user %s is on cooldown", email)
        raise AssignCooldown("assign cooldown")

    if customers.filter(block_transactions=True, permanent_block=True):
        logger.info("user %s is permanently blocked", email)
        raise PermanentBlock("permanent block")

    orders: mongoengine.QuerySet = Order_history.objects(reseller_type="subscriber")

    #   we want to get all pending orders (not canceled, not failed, not successful, but pending!)
    query_set: Q = (Q(client_email=email) | Q(device_id=device_id) | Q(ipv4_address=ipv4_address)) & Q(order_status="Pending")

    query_set_cooldown: Q = query_set & Q(date_created__gt=cooldown_time_since)
    query_set_permanent: Q = query_set & Q(date_created__gt=permanent_time_since)

    non_suc_orders_cooldown: mongoengine.QuerySet = orders.filter(query_set_cooldown)
    non_suc_orders_permanent: mongoengine.QuerySet = orders.filter(query_set_permanent)

    users_should_get_perm_block = non_suc_orders_permanent.count() >= configurables.second_captcha_block_count + 1
    if users_should_get_perm_block:
        logger.info("users %s blocked for the second time", str(emails))
        if customers.count() > 100 and len(customers.distinct("user_email")) > 100:
            send_custom_monitor_email(
                subject="WARNING, SYSTEM IS TRYING TO BLOCK USERS",
                body=f"System is trying to block {len(customers.distinct('user_email'))} users with device id {device_id} and ip {ipv4_address}",
            )
            raise PermanentBlock("permanent block")

        customers.update(
            block_transactions=True,
            permanent_block=True,
            block_date=datetime.datetime.utcnow(),
        )
        mobiles.RateLimitLogs(
            device_id=device_id,
            email_list=emails,
            ip_address=ipv4_address,
            id_types=id_types,
            block_type="permanent",
        ).save()
        send_custom_monitor_email(
            subject=f"--- PERMANENT BLOCK --- BLOCKED {len(emails)} USERS",
            body=f"Permanently blocked users with device id {device_id} and ip {ipv4_address},"
                 f"\n {emails}"
        )
        raise PermanentBlock("permanent block")

    users_should_get_temp_block = non_suc_orders_cooldown.count() == configurables.first_captcha_block_count + 1
    if users_should_get_temp_block:
        if customers.count() > 100 and len(customers.distinct("user_email")) > 100:
            send_custom_monitor_email(
                subject="WARNING, SYSTEM IS TRYING TO BLOCK USERS",
                body=f"System is trying to block " f"{len(customers.distinct('user_email'))} users " f"with device id {device_id}",
            )
            raise AssignCooldown(f"assign cooldown")
        customers.update(
            block_transactions=True,
            permanent_block=False,
            block_date=datetime.datetime.utcnow(),
        )
        mobiles.RateLimitLogs(
            device_id=device_id,
            email_list=emails,
            ip_address=ipv4_address,
            id_types=id_types,
            block_type="temporary",
        ).save()
        logger.info("users %s blocked for the first time", str(emails))
        send_custom_monitor_email(
            subject=f"--- TEMPORARY BLOCK --- BLOCKED {len(emails)} USERS",
            body=f"Temporarily blocked users with device id {device_id} and ip {ipv4_address},"
                 f"\n {emails}"
        )
        raise AssignCooldown(f"assign cooldown")


def check_vendor_balance(bundle_info: Bundles):
    vendor: Vendors = Vendors.objects(vendor_name=bundle_info.vendor_name).first()
    if not vendor.has_balance:
        logger.info("vendor has no balance, ignoring balance check")
        return

    minimal_balance: int = vendor.minimal_balance
    organisation_balance: int = ESIMGo().get_balance()
    enough_balance = organisation_balance > minimal_balance
    if enough_balance:
        return

    subject: str = "INSUFFICIENT BALANCE"
    body: str = f"[{datetime.datetime.utcnow()}] Insufficient balance for {vendor.vendor_name} VENDOR DEACTIVATED"
    send_custom_monitor_email(subject, body)
    deactivate_bundle(bundle_info.bundle_code, f"Insufficient {vendor.vendor_name} balance")
    logger.info("deactivated bundle %s", bundle_info.bundle_code)
    deactivate_vendor(vendor.vendor_name)
    logger.info("deactivated vendor %s", bundle_info.vendor_name)
    return


def reserve_profile(bundle_info: Bundles, order_reference: str) -> str:
    """
    :param bundle_info:
    :return plan_uid:
    :raise ValueError:
    """
    if bundle_info.vendor_name == "Monty Reseller":
        monty_reseller = MontyReseller()
        reserve_object = monty_reseller.reserve_profile(bundle_code=bundle_info.bundle_vendor_code, order_reference=order_reference)
        plan_uid = reserve_object.get("order_id")
        if plan_uid:
            return plan_uid
        logger.error(
            "failed to reserve monty reseller bundle %s due to error %s",
            bundle_info.bundle_code,
            reserve_object,
        )
        raise ValueError("""Something went wrong while reserving profile. Please contact support.""")


def cancel_vendor_reservation(bundle_info: Bundles, order_reference: str) -> str:
    """
    :param bundle_info:
    :param order_reference:
    """
    if bundle_info.vendor_name == "Monty Reseller":
        monty_reseller = MontyReseller()
        cancel_object = monty_reseller.cancel_transaction(order_reference=order_reference)
        if cancel_object.get("response_code") != "1":
            logger.error("""Something went wrong while canceling profile. Please contact support.""")


def assign_stripe(email, assign_bundle_input: AssignStripeRequestDTO, access_permission):
    # ------------------------------------------------------------------------------------
    # Check the validity of version_token in db
    # ------------------------------------------------------------------------------------
    logger.info(
        f"Trying to assign stripe with email :{email}, and assign bundle input dto:{assign_bundle_input}, access permissions :{access_permission}")
    version_token_list = AppVersionList.objects.values_list("version_token")
    if assign_bundle_input.version_token not in version_token_list:
        return False, "Invalid version_token.", None

    # ------------------------------------------------------------------------------------
    # Check the validity of country_codes in db
    # ------------------------------------------------------------------------------------
    searched_country_names = []
    if assign_bundle_input.searched_countries is not None and len(assign_bundle_input.searched_countries) > 0:
        # Remove Do not include None, "", "  "
        # Set will take care of duplicate values
        sanitized_country_codes: set = {
            iso3_code for iso3_code in assign_bundle_input.searched_countries if is_nonempty_nonspace(iso3_code)
        }
        countries_pipeline = countries_aggregate_pipeline(sanitized_country_codes)
        if countries_pipeline:
            retrieved_countries_data_cursor: pymongo.command_cursor.CommandCursor = Countries.objects.aggregate(countries_pipeline)
            retrieved_countries_data = list(retrieved_countries_data_cursor)
            if len(sanitized_country_codes) != len(retrieved_countries_data):
                code_in_retrieved_countries_data = {d["iso3_code"] for d in retrieved_countries_data}
                code_difference = [item for item in sanitized_country_codes if item not in code_in_retrieved_countries_data]
                logger.info("code difference : %s ", code_difference)
                if len(code_difference) > 4:
                    invalid_codes = f'{", ".join(code_difference[:4])}....'
                else:
                    invalid_codes = ", ".join(code_difference)

                error_msg = f"The following provided country codes are invalid: {invalid_codes}"
                logger.error("The following provided country codes are invalid: %s",invalid_codes)
                return False, error_msg, None

            searched_country_names = [d["country_name"] for d in retrieved_countries_data]

    request_id = f"req-{uuid.uuid4()}"
    version_token = assign_bundle_input.version_token
    promo_code = assign_bundle_input.promo_code
    affiliate_id = assign_bundle_input.affiliate_id
    app_token = assign_bundle_input.app_token
    bundle_code = assign_bundle_input.bundle_code
    topup_code = assign_bundle_input.topup_code
    iccid = assign_bundle_input.iccid
    payment_method = assign_bundle_input.payment_method
    country_code = assign_bundle_input.country_code
    ipv4_address = assign_bundle_input.ipv4_address
    searched_country_codes = assign_bundle_input.searched_countries
    referral_code = assign_bundle_input.referral_code
    device_id = assign_bundle_input.device_id
    referred_by = None
    referring_user = None
    configurables = Settings.objects().first()

    orders = Order_history.objects(reseller_type="subscriber")
    successful_orders = orders.filter(order_status="Successful")

    all_users = AppUserDetails.objects()

    logger.info("assign bundle input : %s", assign_bundle_input)
    if referral_code:
        referring_user = all_users.filter(referral_code=referral_code, user_email__ne=email).first()
    customer = all_users.filter(user_email=email).first()
    users_with_this_ip = all_users.filter(ipv4_address=ipv4_address)
    time_since = datetime.datetime.utcnow() - datetime.timedelta(minutes=configurables.orders_assigned_since_minutes)

    if users_with_this_ip.filter(permanent_block=True):
        raise ValueError("permanent block")

    last_blocked_user = users_with_this_ip.filter(block_transactions=True).order_by("-block_date").first()
    if users_with_this_ip.filter(block_transactions=True) and last_blocked_user and last_blocked_user.block_date > time_since:
        raise ValueError("assign cooldown")

    device_id_validation_enabled_for_platform = (AppVersionList.objects(version_token=customer.version_token).first().referral_using_device_id)

    if referring_user and configurables.referral_program_enabled:
        referred_by = referring_user.user_email

    used_before_query = Q(client_email=email)
    if device_id_validation_enabled_for_platform and device_id:
        used_before_query = used_before_query | Q(device_id=device_id)
    used_before = successful_orders.filter(used_before_query)

    if used_before:
        referred_by = None

    if referred_by:
        promo_code = None
        affiliate_id = None

    empty_profile = False
    success = False
    message = ""
    response_data = None
    smdp_address = None
    qr_code_link = None
    activation_code = None
    qr_code_value = None
    plan_uid = None
    vendor_msisdn = None
    partial_purchase = False
    referral_reward = 0
    excess_amount = 0
    remaining_amount = 0
    shared_with = ""
    plan_started = False
    profile: Profiles = None
    order_number = str(uuid.uuid4())

    #   If guest user is trying with wallet payment method, then change it to credit card payment method
    if access_permission == 2 and payment_method == PaymentMethod.wallet.value:
        payment_method = PaymentMethod.credit_card.value

    logger.info("%s checking if user %s is blacklisted", request_id, email)
    if not is_email_blacklisted(email):
        logger.info("user %s is blacklisted", email)
        return False, literals.NO_PROFILES_AVAILABLE, None

    logger.info("%s checking app_token validity for user %s", request_id, email)
    if not validate_app_token(email, version_token, app_token):
        logger.info("%s Invalid app_token for user %s", request_id, email)
        return False, literals.NO_PROFILES_AVAILABLE, None

    bundle_info: Bundles = get_bundle_info(bundle_code=bundle_code, topup_code=topup_code)
    if not bundle_info:
        return False, literals.BUNDLE_NOT_FOUND, None

    if referred_by:
        referral_reward = configurables.referral_reward
        minimum_allowed_bundle_price = configurables.minimum_allowed_bundle_price
        partial_purchase = bundle_info.retail_price > referral_reward and bundle_info.retail_price > minimum_allowed_bundle_price
        if partial_purchase:
            remaining_amount = round(bundle_info.retail_price - referral_reward, 2)  # $3.33 - $3
            minimum_stripe = 0.5
            if remaining_amount < minimum_stripe:  # True if remaining_amount = $0.33
                excess_amount = round(minimum_stripe - remaining_amount, 2)  # 0.16 to be added to wallet
                remaining_amount = minimum_stripe  # pay $0.5 by stripe

    logger.info("%s checking user unpaid reserved profiles", request_id)
    check_reserved_iccid(email, device_id=device_id)

    #   Apply affiliate code for reseller's benefit, as well as apply discount if it exists
    affiliate_promo_record = RedeemCodes.objects(
        id=affiliate_id,
        status__ne="inactive",
        affiliate_program=True,
        expiry_datetime__gte=datetime.datetime.utcnow(),
    ).first()

    if affiliate_promo_record:
        logger.info("%s user %s is using affiliate program", request_id, email)
        already_used = is_promo_used(promo_code=affiliate_promo_record.redeem_code, user_name=email)
        if not already_used:
            bundle_info.retail_price = apply_promo_discount(bundle_info.retail_price, affiliate_promo_record.discount_percent)
        save_promo_log(affiliate_promo_record, email)
        promo_code = affiliate_promo_record.redeem_code

    #   Apply promo code for subscriber's benefit
    if promo_code and not (affiliate_promo_record and promo_code == affiliate_promo_record.redeem_code):
        redeem_record, message_list, valid = get_redeem_record(redeem_code=promo_code)
        message = ", ".join(message_list)
        if valid:
            already_used = is_promo_used(promo_code=redeem_record.redeem_code, user_name=email)
            mobiles.AppRedeemCodeRecord.objects(username=email).delete()
            save_promo_log(redeem_record, email)
            if not already_used and redeem_record.category == "promo_code":
                bundle_info.retail_price = apply_promo_discount(bundle_info.retail_price, redeem_record.discount_percent)
                promo_code = redeem_record.redeem_code

    vendor: Vendors = Vendors.objects(vendor_name=bundle_info.vendor_name, is_active=True).first()
    logger.info("vendor : %s", vendor)
    if not vendor:
        return False, "Error processing request", None

    if topup_code:  # Topping up a profile
        plan_status = "Pending"
        logger.info(
            "%s topping up profile %s with bundle %s for user %s",
            request_id,
            iccid,
            topup_code,
            email,
        )
        logger.info(
            "%s getting assigned profile %s from inventory to top it up",
            request_id,
            iccid,
        )
        profile = Profiles.objects(iccid=iccid, status=True).first()
        order_type = "BuyTopup"
        prev_order_for_this_iccid = successful_orders.filter(iccid=iccid).order_by("-date_created").first()
        shared_with = getattr(prev_order_for_this_iccid, "shared_with", "")

        if not profile:
            return False, "Cannot Topup Profile", None

        # TODO should address later this is only for orange
        if vendor.vendor_name == "Orange" and not successful_orders.filter(plan_started=True):
            logger.info("user %s trying to topup profile %s before starting consumption, " "which is not supported right now by orange")
            raise ValueError(literals.SCAN_PROFILE_FIRST)

        # Bayobab supports only specific number of active plans per profile at a time.
        if vendor.vendor_name == BAYOBAB_VENDOR:
            successful_orders_count = Order_history.objects(iccid=iccid, order_status="Successful", plan_status__ne="Expired").count()
            if successful_orders_count >= Bayobab().bayobab_plan_limit:
                raise ValueError(literals.BUNDLE_CANNOT_TOPUP)

        if prev_order_for_this_iccid.plan_status == "Expired":
            expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=bundle_info.bundle_duration)
            plan_status = "Active"
        else:
            expiry_date = prev_order_for_this_iccid.expiry_date + datetime.timedelta(days=bundle_info.bundle_duration)

        if prev_order_for_this_iccid.plan_started:
            plan_started = True
        logger.info("%s checking if profile %s is close to expiring", request_id, iccid)
        topupable = get_bundles_topup2(
            bundle_category=bundle_info.bundle_category,
            profile_names=bundle_info.profile_names,
            region_code=bundle_info.region_code,
            vendor_name=bundle_info.vendor_name,
            country_code_list=bundle_info.country_code_list,
            iccid=iccid,
            bundle_code=topup_code,
            group_id=bundle_info.group_id,
        )

        if not topupable:
            return False, "Profile is close to expiring, unable to topup further", None

    else:  # Assigning new bundle
        logger.info("%s assigning new bundle %s for user %s", request_id, bundle_code, email)
        logger.info(
            "%s getting free profile from inventory carrying bundle_code %s",
            request_id,
            bundle_code,
        )

        plan_status = "Active"
        order_type = "BuyBundle"
        expiry_date = datetime.datetime.utcnow() + datetime.timedelta(days=365 + bundle_info.bundle_duration)

        if email.endswith(instance_config.tester_domain):
            profile = get_tester_profile(bundle_info)

        elif vendor.apply_inventory:
            profile = check_inventory_availability(bundle_info=bundle_info, is_topup=topup_code)

        if not profile:
            check_vendor_balance(bundle_info)
            if vendor.supports_empty_profiles:
                logger.info(
                    "%s Inventory missing profiles with bundle_code %s",
                    request_id,
                    bundle_code,
                )
                logger.info(
                    "%s Looking for empty profiles to load directly from vendor",
                    request_id,
                )
                profile = get_empty_profile(bundle_info)

            elif vendor.get_profile_from_vendor:
                logger.info("No profiles in inventory. we will obtain profile directly from vendor")
                if vendor.supports_profile_reservation:
                    empty_profile = True
                    plan_uid = reserve_profile(bundle_info=bundle_info, order_reference=order_number)
                else:
                    empty_profile = True

            else:
                logger.error(
                    "%s problem with vendor configuration for vendor %s",
                    request_id,
                    vendor.vendor_name,
                )
                raise ValueError("Error processing request")

            if not empty_profile and not profile:  # No profiles available for bundle
                logger.error(
                    "%s did not find any profiles for bundle %s while trying to assign for user %s",
                    request_id,
                    bundle_code,
                    email,
                )
                subject = "INSUFFICIENT PROFILES"
                body = (
                    f"[{datetime.datetime.utcnow()}] User {customer['user_email']} tried to buy bundle {bundle_info.bundle_code}, "
                    f"Insufficient profiles for vendor {bundle_info.vendor_name}"
                )
                send_custom_monitor_email(subject, body)
                deactivate_bundle(bundle_info.bundle_code, "insufficient profiles")
                return False, literals.NO_PROFILES_AVAILABLE, None

    if not enough_bundle_count(bundle_info):  # Insufficient bundles
        logger.info(
            "%s User %s tried to buy bundle %s Insufficient bundles",
            request_id,
            email,
            bundle_info.bundle_code,
        )
        subject = "INSUFFICIENT BUNDLES"
        body = (
            f"[{datetime.datetime.utcnow()}] User {customer.user_email} tried to buy bundle {bundle_info.bundle_code}, "
            f"Insufficient bundles for vendor {bundle_info.vendor_name}"
        )
        send_custom_monitor_email(subject, body)
        deactivate_bundle(bundle_info.bundle_code, "allocated <= consumed")
        return False, "Unavailable bundle", None

    paid_amount_wallet = 0
    paid_amount_credit_card = bundle_info.retail_price

    if partial_purchase:
        paid_amount_credit_card = remaining_amount
        payment_method = 1

    if not topup_code:
        update_bundle_info(bundle_info, update_consumed=False)

    if access_permission == 1 and payment_method == PaymentMethod.wallet.value:
        if customer.balance >= bundle_info.retail_price:
            logger.info(
                "%s user %s has enough balance to pay for bundle %s",
                request_id,
                email,
                bundle_info.bundle_code,
            )
            paid_amount_wallet = bundle_info.retail_price
            paid_amount_credit_card = 0
            customer.update(dec__balance=paid_amount_wallet)
        else:  # pay amount by credit card
            logger.info(
                "%s user %s does not have enough balance to pay for bundle %s",
                request_id,
                email,
                bundle_info.bundle_code,
            )
            payment_method = PaymentMethod.credit_card.value

    if not empty_profile:
        smdp_address = profile.smdp_address
        activation_code = profile.matching_id
        qr_code_value = profile.qr_code_value
        plan_uid = ""
        if order_type == "BuyBundle" and hasattr(profile, "plan_uid"):
            plan_uid = profile.plan_uid
        vendor_msisdn = profile.msisdn
        iccid = profile.iccid
        qr_code_link = "{}/generate-qr-code/{}/{}/{}/qr_code.jpg".format(
            instance_config.decrypted_wp_qr_code,
            activation_code,
            smdp_address,
            profile.has_lpa,
        )

    order_data = dict(
        order_number=order_number,
        reseller_type="subscriber",
        plan_status=plan_status,
        client_email=email,
        access_permission=access_permission,
        paid_amount_wallet=paid_amount_wallet,
        paid_amount_credit_card=paid_amount_credit_card,
        payment_category=payment_method,
        qr_code_link=qr_code_link,
        order_type=order_type,
        smdp_address=smdp_address,
        activation_code=activation_code,
        qr_code_value=qr_code_value,
        plan_uid=plan_uid,
        vendor_msisdn=vendor_msisdn,
        iccid=iccid,
        bundle_code=bundle_info.bundle_code,
        searched_countries=searched_country_names,
        searched_code_list=searched_country_codes,
        payment_date=datetime.datetime.utcnow(),
        plan_started=plan_started,
        country_code=country_code,
        expiry_date=expiry_date,
        cashback_rewarded=False,
        promo_code=promo_code,
        ipv4_address=ipv4_address,
        version_token=version_token,
        referral_reward=referral_reward,
        partial_purchase=partial_purchase,
        excess_amount=excess_amount,
        referred_by=referred_by,
        shared_with=shared_with,
        device_id=device_id,
    )

    #   Choose method to call based on payment method
    if payment_method == PaymentMethod.wallet.value:
        order = create_order(bundle_info, False, **order_data)
        logger.info("%s created wallet order %s", request_id, order.order_number)
        fulfill_order(order.order_number)
        logger.info("%s fulfilled wallet order %s", request_id, order.order_number)
        response_data = {
            "history_log_id": order.order_number,
            "payment_otp": order.order_number,
            "qr_code_link": order.order_number,
            "access_permission": access_permission,
            "payment_method": payment_method,
        }
        success, message = True, "Success"
    elif payment_method == PaymentMethod.credit_card.value:
        order = create_order(bundle_info, False, **order_data)
        check_rate_limit(version_token, email, device_id, ipv4_address)

        logger.info("%s created wallet order %s", request_id, order.order_number)
        (
            publishable_key,
            customer_id,
            ephemeral_key,
            client_secret,
            payment_intent_id,
        ) = create_stripe_order(customer, paid_amount_credit_card, access_permission)
        logger.info("%s finished creating stripe order %s", request_id, order.order_number)
        if access_permission == 2:
            attributes = {
                "role_name": [mobiles.limited_role_],
                "history_id": order.order_number,
            }
            update_user_attributes(customer, **attributes)

        order.update(
            stripe_client_secret=md5(client_secret.encode()).hexdigest(),
            pid_hash=payment_intent_id,
        )

        response_data = {
            "payment_otp": order.order_number,
            "qr_code_pin": order.order_number,
            "history_log_id": order.order_number,
            "access_permission": order.access_permission,
            "client_secret": client_secret,
            "customer_id": customer_id,
            "ephemeral_key": ephemeral_key,
            "payment_intent_id": payment_intent_id,
            "publishable_key": publishable_key,
            "payment_method": payment_method,
            "url": "",
        }
        success, message = True, "Success"
    return success, message, response_data


def get_share_profile_link(iccid: str = None, email: str = None):
    #   verify if profile has been claimed previously
    if SharedProfiles.objects(issued_by=email, iccid=iccid, claimed__ne=False):
        raise ValueError("Profile has been claimed by another user")

    #   verify if user owns this iccid
    user_orders = Order_history.objects(
        client_email=email,
        iccid=iccid,
        order_status="Successful",
        reseller_type="subscriber",
    )
    if not user_orders:
        raise Exception("Error sharing profile")

    if not user_orders.filter(plan_status__ne="Expired"):
        raise ValueError("Cannot share an expired profile")

    last_order = user_orders.order_by("-date_created").first()
    #   generate dynamic link to redirect to app with claim token
    bundle_name = getattr(Labels.objects(iccid=iccid).first(), "label_name", "") or last_order.bundle_data.bundle_marketing_name
    claim_token = uuid.uuid4().hex

    claim_link = Firebase(**firebase_config, credentials_file_path=instance_config.firebase_ios_file).generate_dynamic_link(
        analytics=False, ct=claim_token, email=email, bundle_name=bundle_name
    )
    record = dict(
        claim_token=hash_string(claim_token),
        date_created=datetime.datetime.utcnow(),
        expiry_date=datetime.datetime.utcnow() + datetime.timedelta(days=3),
        claimed=False,
        canceled_date=None,
    )
    SharedProfiles.objects(issued_by=email, iccid=iccid).update(upsert=True, **record)
    return {"claim_link": claim_link}


def claim_profile(claim_token: str = None, email: str = None):
    shared_profile = SharedProfiles.objects(claim_token=hash_string(claim_token)).first()
    if not shared_profile:
        logger.info("record for claim token %s does not exist", claim_token)
        check_cooldown(email=email, category="profile_claim")
        raise ValueError("Invalid claim token")
    if shared_profile.claimed:
        logger.info("user is trying to claim an already claimed profile")
        raise ValueError("Profile already claimed")
    if shared_profile.expiry_date < datetime.datetime.utcnow():
        raise ValueError("shared profile request has expired, ask for a new sharing link")
    if shared_profile.issued_by == email:
        raise ValueError("User cannot claim his own profile")
    iccid = shared_profile.iccid
    user_orders = Order_history.objects(iccid=iccid, order_status="Successful", reseller_type="subscriber")
    user_orders.update(shared_with=email)
    last_order = user_orders.order_by("-date_created").first()
    logger.info(
        "updated order history for iccid %s",
    )
    shared_profile.update(claimed_by=email, claim_date=datetime.datetime.utcnow(), claimed=True)
    firebase_helper = Firebase(settings=get_setting(), credentials_file_path=instance_config.firebase_ios_file)
    bundle_name = getattr(Labels.objects(iccid=iccid).first(), "label_name", "") or last_order.bundle_data.bundle_marketing_name
    data = {
        "category": "7",
        "bundle_marketing_name": bundle_name,
        "claimer_email": last_order.shared_with,
    }
    sharing_customer = get_customer(shared_profile.issued_by)
    firebase_helper.send_notification(customer=sharing_customer, data=data, transaction="profile_claimed")


def revoke_profile_claiming(iccid: str = None, email: str = None):
    shared_profile = SharedProfiles.objects(issued_by=email, iccid=iccid).first()
    if not shared_profile:
        logger.error("record for iccid does not exist")
        raise Exception("Not found")
    shared_profile.update(claim_token="", canceled_date=datetime.datetime.utcnow())
    logger.info("revoked profile sharing link")


def remove_shared_profile(email: str = None, iccid: str = None):
    master_orders = Order_history.objects(
        iccid=iccid,
        client_email=email,
        order_status="Successful",
        reseller_type="subscriber",
    )
    if master_orders:
        if not master_orders.first().shared_with:
            raise ValueError("Profile isn't shared with anyone, please refresh to see changes")
        firebase_helper = Firebase(
            settings=get_setting(),
            credentials_file_path=instance_config.firebase_ios_file,
        )
        customer = get_customer(master_orders.first().shared_with)
        last_order = master_orders.order_by("-date_created").first()
        bundle_name = getattr(Labels.objects(iccid=iccid).first(), "label_name", "") or last_order.bundle_data.bundle_marketing_name

        data = {"category": "7", "bundle_marketing_name": bundle_name}
        master_orders.update(shared_with="")
        shared_profile = SharedProfiles.objects(issued_by=email, iccid=iccid).first()
        shared_profile.update(claim_token="", canceled_date=datetime.datetime.utcnow(), claimed=False)
        firebase_helper.send_notification(customer=customer, data=data, transaction="sharing_canceled")
        return

    slave_orders = Order_history.objects(
        iccid=iccid,
        shared_with=email,
        order_status="Successful",
        reseller_type="subscriber",
        shared_with__ne="",
    )
    if not slave_orders:
        raise Exception("Error performing operation")
    slave_orders.update(shared_with="")
    shared_profile = SharedProfiles.objects(claimed_by=email, iccid=iccid).first()
    shared_profile.update(claim_token="", canceled_date=datetime.datetime.utcnow(), claimed=False)


def handle_lotusflare_buybundle(vendor, bundle, order, sent_email, load_vendor, message):
    activation_date = str(datetime.datetime.utcnow())

    create_order_resp = vendor.create_order(
        operation_type="NEW",
        product_id=bundle.bundle_vendor_code,
        activation_date=activation_date,
        iccid=order.iccid,
        auto_allocate_esim=False,
    )

    if not create_order_resp:
        logger.error(
            "order %s couldn't buy new profile with bundle %s, for vendor: %s, and iccid : %s",
            order.order_number,
            order.bundle_code,
            order.vendor,
            order.iccid,
        )
        return sent_email, load_vendor, order, message

    order_reference = create_order_resp["id"]
    logger.info("order %s getting order info", order.order_number)

    order.update(
        iccid=order.iccid,
        plan_uid=order_reference,
    )
    logger.info("order %s updated with profile info successfully", order.order_number)

    sent_email = True
    load_vendor = True
    return sent_email, load_vendor, order, message


def handle_lotusflare_buytopup(vendor, bundle, order, profile, sent_email, load_vendor, message):
    create_order_resp = vendor.create_order(
        operation_type="TOPUP",
        product_id=bundle.bundle_vendor_code,
        iccid=order.iccid,
        auto_allocate_esim=False,
    )

    if not create_order_resp:
        logger.error(
            "order %s couldn't buy new plan %s to apply on profile %s",
            order.order_number,
            order.bundle_code,
            order.iccid,
        )
        return sent_email, load_vendor, order, message

    order_reference = create_order_resp["id"]
    iccid = profile.iccid

    order.update(
        iccid=iccid,
        plan_uid=order_reference,
    )
    logger.info("order %s topup updated successfully with plan_uid and iccid: %s ", order.order_number, order.iccid)

    sent_email = True
    load_vendor = True
    return sent_email, load_vendor, order, message


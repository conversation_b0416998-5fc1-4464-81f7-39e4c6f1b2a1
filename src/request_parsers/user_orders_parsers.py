import datetime
import marshmallow
from marshmallow import Schema, fields, validates_schema, ValidationError, post_load, validates, validate, pre_load
from src.global_helpers.utils import is_nonempty_nonspace
from app_models.reseller_models import _order_status
from src.literals import DATETIME_FORMAT_2

from typing import Optional, List
from dataclasses import dataclass, field
from src.cipher import Crypt
from instance.consumer_config import token_key

__cipher_id_1 = Crypt(aes_key=token_key)


def _encrypt_order_number(order_number: str) -> str:
    if is_nonempty_nonspace(order_number):
        return __cipher_id_1.encrypt(order_number)

    return order_number


def _decrypt_order_number(order_number: str) -> str:
    if is_nonempty_nonspace(order_number):
        return __cipher_id_1.decrypt(order_number)

    return order_number


def validate_order_number(field_name: str = "order_number"):
    def validate_name_internal(value: str):
        if not is_nonempty_nonspace(value):
            raise ValidationError(f"Invalid {field_name}")

    return validate_name_internal


class BaseOrderHistoryResponseDTO:
    errors = []

    def __init__(self,
                 bundle_name: Optional[str] = "",
                 coverage: Optional[str] = "",
                 country_code_list: Optional[List[str]] = None,
                 currency: Optional[str] = "",
                 data_amount: Optional[int] = 0,
                 date_created: Optional[datetime.datetime] = None,
                 data_unit: Optional[str] = "",
                 order_number: Optional[str] = "",
                 order_status: Optional[str] = "",
                 payment_details: Optional[str] = "",
                 region_code: Optional[str] = "",
                 validity_amount: Optional[int] = 0,
                 bundle_category: Optional[str] = "",
                 **kwargs
                 ):
        self._1 = order_number
        self.bundle_name = bundle_name
        self.coverage = coverage
        self.country_code_list = country_code_list if country_code_list else []
        self.currency = currency
        self.data_amount = data_amount
        self.date_created = self._format_datetime(date=date_created)
        self.data_unit = data_unit
        self.order_number = _encrypt_order_number(order_number)
        self.order_status = order_status
        self.payment_details = payment_details
        self.region_code = region_code
        self.validity_amount = validity_amount
        self.bundle_category = bundle_category

    @staticmethod
    def _format_datetime(date: Optional[datetime.datetime], fmt=DATETIME_FORMAT_2) -> str:
        return date.strftime(fmt) if date else ""

    @staticmethod
    def _payment_method_category(value: str) -> str:
        categories = {
            "1": "Credit Card",
            "2": "Wallet",
            "3": "Applepay",
            "4": "Googlepay"
        }
        return categories.get(value, "")

    @staticmethod
    def _f_round(value: float = 0.0, n_digits: int = 2):
        return round(value, n_digits) if value else 0.0

    @staticmethod
    def _mask_credit_card(credit_card_number: str):
        """mask the credit card number except for the last 4 digits"""
        if not is_nonempty_nonspace(credit_card_number):
            return ""

        unmasked_numbers = credit_card_number
        if len(credit_card_number) > 4:
            unmasked_numbers = credit_card_number[-4:]

        return f"{'*' * 4} {unmasked_numbers}"


class OrderHistoryBriefResponseDTO(BaseOrderHistoryResponseDTO):
    def __init__(self,
                 # COMMON
                 bundle_name: Optional[str] = "",
                 coverage: Optional[str] = "",
                 country_code_list: Optional[List[str]] = None,
                 currency: Optional[str] = "",
                 data_amount: Optional[int] = 0,
                 data_unit: Optional[str] = "",
                 date_created: Optional[datetime.datetime] = None,
                 order_number: Optional[str] = "",
                 order_status: Optional[str] = "",
                 payment_details: Optional[str] = "",
                 region_code: Optional[str] = "",
                 validity_amount: Optional[int] = 0,

                 # SPECIFIC
                 retail_price: Optional[float] = 0.0,
                 unlimited: Optional[bool] = None,
                 bundle_category: Optional[str] = "",
                 **kwargs
                 ):
        super().__init__(bundle_name=bundle_name, coverage=coverage, country_code_list=country_code_list,
                         currency=currency,
                         data_amount=data_amount, date_created=date_created, data_unit=data_unit,
                         order_number=order_number, order_status=order_status,
                         payment_details=payment_details, region_code=region_code, validity_amount=validity_amount, bundle_category=bundle_category)

        self.retail_price = super()._f_round(retail_price)
        self.unlimited = unlimited

    @classmethod
    def create_list_from_document_list(cls, mongo_documents):
        if not mongo_documents or len(mongo_documents) == 0:
            return []

        return [cls(**doc).__dict__ for doc in mongo_documents if len(doc) > 0]


class OrderHistoryDetailedResponseDTO(BaseOrderHistoryResponseDTO):
    def __init__(self,
                 # COMMON
                 bundle_name: Optional[str] = "",
                 coverage: Optional[str] = "",
                 country_code_list: Optional[List[str]] = None,
                 currency: Optional[str] = "",
                 data_amount: Optional[int] = 0,
                 data_unit: Optional[str] = "",
                 date_created: Optional[datetime.datetime] = None,
                 order_number: Optional[str] = "",
                 order_status: Optional[str] = "",
                 payment_details: Optional[str] = "",
                 region_code: Optional[str] = "",
                 validity_amount: Optional[int] = 0,

                 # SPECIFIC
                 retail_price: Optional[float] = 0.0,
                 total_price: Optional[float] = 0.0,
                 credit_card_brand: Optional[str] = "",
                 credit_card_number: Optional[str] = "",
                 payment_category: Optional[int] = 0,
                 unlimited: Optional[bool] = None,
                 bundle_category: Optional[bool] = "",
                 **kwargs
                 ):
        super().__init__(bundle_name=bundle_name, coverage=coverage, country_code_list=country_code_list,
                         currency=currency,
                         data_amount=data_amount, date_created=date_created, data_unit=data_unit,
                         order_number=order_number, order_status=order_status,
                         payment_details=payment_details, region_code=region_code, validity_amount=validity_amount, bundle_category=bundle_category)

        self.credit_card_brand = credit_card_brand
        self.credit_card_number = super()._mask_credit_card(credit_card_number)
        self.retail_price = super()._f_round(retail_price)
        self.total_price = super()._f_round(total_price)
        self.payment_category = super()._payment_method_category(str(payment_category))
        self.unlimited = unlimited


class OrderHistoryInvoiceResponseDTO(BaseOrderHistoryResponseDTO):
    def __init__(self,
                 # COMMON
                 bundle_name: Optional[str] = "",
                 coverage: Optional[str] = "",
                 country_code_list: Optional[List[str]] = None,
                 currency: Optional[str] = "",
                 data_amount: Optional[int] = 0,
                 data_unit: Optional[str] = "",
                 date_created: Optional[datetime.datetime] = None,
                 order_number: Optional[str] = "",
                 order_status: Optional[str] = "",
                 payment_details: Optional[str] = "",
                 region_code: Optional[str] = "",
                 validity_amount: Optional[int] = 0,
                 credit_card_brand: Optional[str] = "",
                 credit_card_number: Optional[str] = "",

                 # SPECIFIC
                 billing_address: Optional[dict] = None,
                 bundle_marketing_name: Optional[str] = "",
                 client_email: Optional[str] = "",
                 client_name: Optional[str] = "",
                 company_address: Optional[str] = "",
                 contact_email: Optional[str] = "",
                 company_logo_url: Optional[str] = "",
                 company_name: Optional[str] = "",
                 payment_category: Optional[int] = 0,
                 payment_date: Optional[datetime.datetime] = None,
                 retail_price: Optional[float] = 0.0,
                 total_paid: Optional[float] = 0.0,
                 total_price: Optional[float] = 0.0,

                 # Not permanent, will change later, they are both because the code does not handle more than one product.
                 quantity: Optional[int] = 1,
                 **kwargs
                 ):

        super().__init__(bundle_name=bundle_name, coverage=coverage, country_code_list=country_code_list,
                         currency=currency, data_amount=data_amount, date_created=date_created, data_unit=data_unit,
                         order_number=order_number, order_status=order_status,
                         payment_details=payment_details, region_code=region_code, validity_amount=validity_amount)

        if billing_address:
            self.billing_address = {
                key: (billing_address.get(key, "") if is_nonempty_nonspace(billing_address.get(key, "")) else "")
                for key in ['city', 'country', 'line1', 'line2', 'postal_code', 'state']}
        else:
            self.billing_address = {}

        self.bundle_marketing_name = bundle_marketing_name
        self.credit_card_brand = credit_card_brand
        self.credit_card_number = super()._mask_credit_card(credit_card_number)
        self.client_email = client_email
        self.client_name = client_name
        self.company_address = company_address
        self.company_email = contact_email
        self.company_logo_url = company_logo_url
        self.company_name = company_name
        self.invoice_generated_on = super()._format_datetime(datetime.datetime.utcnow())
        self.payment_category = super()._payment_method_category(str(payment_category))
        self.payment_date = super()._format_datetime(payment_date)
        self.unit_price = super()._f_round(retail_price)
        self.total_paid = super()._f_round(total_paid)
        self.total_price = super()._f_round(total_price)

        self.quantity = quantity


@dataclass
class GetOrdersSearchCriteriaDto:
    page: Optional[int] = field(default=None)
    size: Optional[int] = field(default=None)
    order_date_start: Optional[datetime.date] = field(default=None)
    order_date_end: Optional[datetime.date] = field(default=None)
    status: Optional[str] = field(default=None)
    item_name: Optional[str] = field(default=None)


class GetAllOrdersByUserIdentifierParser(Schema):
    _DATE_FORMAT = '%Y-%m-%d'

    page = fields.Integer(
        required=False,
        allow_none=False,
        error_messages={
            "required": "Invalid page number",
            "null": "Invalid page number",
        }
    )

    size = fields.Integer(
        required=False,
        allow_none=False,
        error_messages={
            "required": "Invalid size",
            "null": "Invalid size",
        },
        validate=validate.Range(min=1, error="Size number must be greater than or equal to 1.")
    )

    order_date_start = fields.Date(
        format=_DATE_FORMAT,
        required=False,
        allow_none=True,
        load_default=None,
    )

    order_date_end = fields.Date(
        format=_DATE_FORMAT,
        required=False,
        allow_none=True,
        load_default=None,
    )

    status = fields.String(
        required=False,
        allow_none=True,
        validate=validate.OneOf([_order_status[0]],
                                error=f"Invalid order status: must be one of [{', '.join([_order_status[0]])}].")
    )

    item_name = fields.Str(
        required=False,
        allow_none=True,
        load_default=None,
    )

    @validates_schema
    def dates_are_chronological(self, data, **kwargs):
        start_date = data.get('order_date_start')
        end_date = data.get('order_date_end')
        if start_date and end_date:
            if start_date > end_date:
                raise ValidationError("The order start date must be before the end date.",
                                      fields=['order_date_start', 'order_date_end'])

    @post_load
    def post_load(self, data, **kwargs) -> GetOrdersSearchCriteriaDto:

        if "status" not in data or not is_nonempty_nonspace(data["status"]):
            data["status"] = _order_status[0]

        return GetOrdersSearchCriteriaDto(**data)

    @pre_load
    def transform_empty_to_none(self, data, **kwargs):

        empty_to_none_excluded_list = []

        for key, value in data.items():
            if key not in empty_to_none_excluded_list and isinstance(value, str) and value == "":
                data[key] = None

        return data


@dataclass
class OrderHistoryRequestBody:
    order_number: str


class OrderHistoryRequestBodyParser(Schema):
    __order_number_invalid_msg = "Order number is invalid"
    __order_number_required_msg = "Order number is required"

    order_number = fields.String(
        required=True,
        allow_none=False,
        validate=is_nonempty_nonspace,
        error_messages={
            'validator_failed': __order_number_invalid_msg,
            "required": __order_number_required_msg,
            "null": __order_number_invalid_msg,
        }
    )

    @post_load
    def post_load(self, data, **kwargs) -> OrderHistoryRequestBody:
        if "order_number" in data:
            try:
                data["order_number"] = _decrypt_order_number(data["order_number"])
            except Exception:
                raise ValidationError(self.__order_number_invalid_msg, fields=['order_number'])
        return OrderHistoryRequestBody(**data)

from flask_restful import reqparse


class NotificationsListInput:
    notification_list_input = reqparse.RequestParser()
    notification_list_input.add_argument(type=int, name="number_of_page", help="Invalid number_of_page", default=0,
                                    location="args")
    notification_list_input.add_argument(type=int, name="page_size", default=10, help="Invalid page_size", location="args")
    notification_list_input.add_argument(type=int, choices=[0, 1], name="status_update", default=0, help="Invalid status_update", location="args")

    @staticmethod
    def validate_number_of_page(number_of_page):
        if not isinstance(number_of_page, int) or number_of_page < 0:
            return ValueError("invalid number_of_page.")

    @staticmethod
    def validate_page_size(page_size):
        if not isinstance(page_size, int) or page_size < 0:
            return ValueError("invalid page_size.")

    @staticmethod
    def validate_status_update(status_update):
        if not isinstance(status_update, int):
            return ValueError("invalid status_update.")

    @classmethod
    def parse_args(cls):
        parsed_args = cls.notification_list_input.parse_args()
        # get list of all validation_error
        for field_name, field_value in parsed_args.items():

            # get validation function or none
            validation_function_exists = getattr(cls, f"validate_{field_name}", None)

            # if validation function is none ignore
            if not validation_function_exists:
                continue

            validate_response = validation_function_exists(parsed_args.get(field_name))
            if isinstance(validate_response, ValueError):
                raise ValueError(validate_response)
        return parsed_args

import datetime
import logging
import traceback

import flask
import flask_wtf.csrf
import flask_restful
import flask_mongoengine
import flask_cors
import apispec.ext.marshmallow

import werkzeug.exceptions
import firebase_admin

import stripe
import instance.consumer_config as config
from utils.json_encoder import JSONEncoder

default_response = {"status": False, "data": {}, "responseCode": 4, "title": "", "message": "", "developerMessage": "", "totalCount": None}

app: flask.Flask = flask.Flask(__name__)


if not app.json_encoder:
	app.json_encoder = JSONEncoder

app.config["SECRET_KEY"] = "asdfkljasdfasdnfaslkdjf10293ruaposijdfljkAJADFasdflkjasdflkajsdlfkjasdflkjasdfnasdf"
app.config["MONGODB_HOST"] = config.new_host_
app.config["SESSION_COOKIE_HTTPONLY"] = True
app.config["CORS_HEADERS"] = "Content-Type"
app.config["CORS_RESOURCES"] = {r"/*": {"origins": ["*"]}}

logger = logging.getLogger(__name__)

try:
	logger.info("Attempting to establish MongoDB connection")
	mongo_db = flask_mongoengine.MongoEngine()
	mongo_db.init_app(app)
	logger.info("Successfully established MongoDB connection")
except Exception as e:
	error_type = type(e).__name__
	error_msg = str(e)
	error_traceback = traceback.format_exc()
	logger.critical(
		"COULDN'T ESTABLISH DATABASE CONNECTION. Error Type: %s, Error Message: %s, MongoDB Host: %s", 
		error_type, 
		error_msg, 
		app.config["MONGODB_HOST"]
	)
	logger.debug("Error Traceback: %s", error_traceback)

_firebase_app = None


def get_firebase_app():
	global _firebase_app
	if not _firebase_app:
		now = datetime.datetime.now()
		app_name = "ios_" + now.strftime("%Y-%m-%d %H:%M:%S")
		try:
			# Try to get the app with the specific name
			_firebase_app = firebase_admin.get_app(app_name)
		except ValueError:
			# If the app does not exist, initialize a new one
			cred = firebase_admin.credentials.Certificate(config.firebase_ios_file)
			_firebase_app = firebase_admin.initialize_app(credential=cred, name=app_name)

	return _firebase_app


# Use the retrieved app throughout your code
firebase_app = get_firebase_app()
csrf = flask_wtf.csrf.CSRFProtect(app)

cors_config = {
	"origins": "*",
	"allow_headers": ["*"],
	"expose_headers": "*",
	"methods": ["OPTIONS", "GET", "PUT", "POST", "DELETE"],
}
cors = flask_cors.CORS(app, resources={r"/*": cors_config}, supports_credentials=True)
api = flask_restful.Api(app, decorators=[csrf.exempt])
api_bp = flask.Blueprint("api", __name__)


default_response = {
	"status": False,
	"data": {},
	"responseCode": 4,
	"title": "",
	"message": "",
	"developerMessage": "",
	"totalCount": None,
}


def make_http_response(response, status=200):
	response = flask.json.dumps(response, indent=2)
	response_http = flask.make_response(response, status)
	response_http.headers.add("Access-Control-Allow-Origin", "*")
	response_http.headers.extend({"X-Admin-Protocol": "gsma/rsp/v2.2.2"})
	response_http.headers["Content-Type"] = "application/json"
	response_http.headers["X-Frame-Options"] = "SAMEORIGIN"
	response_http.headers["X-Content-Type-Options"] = "nosniff"
	response_http.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
	response_http.headers["X-XSS-Protection"] = "1; mode=block"
	response_http.headers["Referrer-Policy"] = "no-referrer"
	return response_http


@app.errorhandler(500)
def internal_error(error):
	default_response["message"] = "Internal error"
	default_response["developerMessage"] = str(error)
	return make_http_response(default_response, status=500)


@app.errorhandler(werkzeug.exceptions.HTTPException)
def handle_exception(error):
	default_response["message"] = "The method is not allowed for the requested URL"
	default_response["developerMessage"] = str(error)
	return make_http_response(default_response, status=405)


@app.errorhandler(404)
def page_not_found(err):
	default_response["message"] = "Url not found"
	default_response["developerMessage"] = str(err)
	return make_http_response(default_response, status=404)


@app.errorhandler(403)
def page_forbidden(err):
	default_response["message"] = "Page forbidden"
	default_response["developerMessage"] = str(err)
	return make_http_response(default_response, status=403)


@app.errorhandler(Exception)
def handle_exception(error):
	default_response["message"] = "Faced Exception"
	default_response["developerMessage"] = str(error)
	return make_http_response(default_response, status=501)


app.config.update(
	{
		"APISPEC_SPEC": apispec.APISpec(
			title="b2c-api", version="v2.1", plugins=[apispec.ext.marshmallow.MarshmallowPlugin()], openapi_version="3.0.0"
		),
		"APISPEC_SWAGGER_UI_URL": "/swagger-ui/",
	}
)

from src import routers
try:
	stripe_helper = stripe.StripeHelper(config.stripe_url, config.stripe_superadmin_username, config.stripe_superadmin_password)
except (Exception, ConnectionRefusedError) as e:
	logger.critical("Couldn't couple flask app to stripe endpoints on deployment %s", str(e))
	stripe_helper = stripe.StripeHelper(login=False)

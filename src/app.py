import logging
from logging.config import dictConfig

from flask import Flask
from flask_restful import Api
from flask_cors import CORS
import socket
from . import config
from .models import mongo
from .auth.create_super_user import create_super_user
from .cron.cron import cancel_expired_payment_intents
from .auth.permissions import get_client_ip, read_list_from_file
from flask_swagger_ui import get_swaggerui_blueprint
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.events import EVENT_JOB_MAX_INSTANCES
from apscheduler.jobstores.mongodb import MongoDBJobStore
from flask import Flask, request, abort

app = Flask(__name__)

app.config.from_object(config.Config)
app.config['SECRET_KEY'] = "asdfkljasdfasdnfaslkdjf10293ruaposijdfljkAJADFasdflkjasdflkajsdlfkjasdflkjasdfnasdf"
app.config["MONGODB_HOST"] = config.Config.new_host_

CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)

from flasgger import Swagger

swagger = Swagger(app)

mongo.init_app(app)
create_super_user()

api_app = Api(app)

from . import routers

SWAGGER_URL = '/swagger'
API_URL = '/static/swagger.json'

swaggerui_blueprint = get_swaggerui_blueprint(
    SWAGGER_URL,
    API_URL,
    config={
        'app_name': 'My API'
    }
)
app.register_blueprint(swaggerui_blueprint, url_prefix=SWAGGER_URL)

scheduler = BackgroundScheduler({
    'apscheduler.jobstores.mongo': {
        'type': 'mongodb'
    }})


def job_already_running(event):
    print("Job already running! {}".format(event))


scheduler.configure()
scheduler.add_listener(job_already_running, EVENT_JOB_MAX_INSTANCES)
scheduler.add_job(func=cancel_expired_payment_intents,
                  trigger='cron',
                  minute='*/7',
                  replace_existing=True,
                  id="incomplete_transactions",max_instances=1 )
#scheduler.start()


@app.before_request
def check_trusted_host():
    user_agent = request.headers.get("User-Agent")
    if user_agent == "Stripe/1.0 (+https://stripe.com/docs/webhooks)":
        return

    client_ip = get_client_ip(request)
    blocked_list = read_list_from_file(config.Config.blocked_ip)

    blocked_ips = [item["blocked_ip"] for item in blocked_list]

    if client_ip in blocked_ips:
        abort(403)


dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console']
    }
})
logging.info("bugs, bugs everywhere")

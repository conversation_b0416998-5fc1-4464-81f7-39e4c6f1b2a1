import datetime
from ..config import Config
from .authenticate import <PERSON><PERSON><PERSON>ak<PERSON><PERSON><PERSON>
from ..models import ROL<PERSON>, User


def create_super_user():
    try:
        print("<create_super_user> start creating super user.")
        keycloak_helper = KeycloakHelper()
        user_data = {
            "username": Config.admin_username,
            "email": Config.admin_email,
            "password": Config.admin_password,
            "is_active": True,
            "role": ROLE.SUPER_ADMIN,
        }
        print(
            f"[{datetime.datetime.utcnow()}], <create_super_user  > Getting user info"
        )
        if User.objects(username=user_data["username"]).first():
            print(f"<create_super_user> already exists: {user_data['username']}")
            return
        print(f"[{datetime.datetime.utcnow()}], <create_super_user> saving superuser")
        admin_object = User(**user_data)
        print(
            f"[{datetime.datetime.utcnow()}], <create_super_user> admin_object {admin_object}"
        )
        keycloak_helper.create_user(user_data, admin_object)
        admin_object.save()
        print(f"<create_super_user> user created: {user_data['username']}")
        return
    except Exception as e:
        print(
            f"[{datetime.datetime.utcnow()} <create_super_user> exception occurred: {e}]"
        )

{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "Stripe Service API"}, "schemes": ["https", "http"], "servers": [{"url": "http://localhost:5000", "description": "Local server"}, {"url": "http://localhost:7011", "description": "Docker Local server"}, {"url": "https://payment.localrsp.com", "description": "QA server"}, {"url": "http://***********:7000", "description": "QA server"}, {"url": "http://***********:7011", "description": "UAT server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"bearerAuth": []}], "paths": {"/publishable-key": {"get": {"summary": "Retrieve publishable key", "description": "Retrieves the publishable key used to authenticate and process payments.", "produces": ["application/json"], "responses": {"200": {"description": "Successful response", "schema": {"type": "object", "properties": {"publishableKey": {"type": "string"}}}, "examples": {"application/json": {"publishableKey": "pk_test_sadsdasdsdsds"}}}}}}, "/customers": {"post": {"summary": "Create new customer", "description": "", "tags": ["Customer"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The customer's name"}, "email": {"type": "string", "description": "The customer's email address"}, "username": {"type": "string", "description": "The customer's username", "nullable": true}, "mobile": {"type": "string", "description": "The customer's mobile number", "nullable": true}}, "required": ["name", "email"]}}}}, "responses": {"200": {"description": "Customer created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "username": {"type": "string"}, "mobile": {"type": "string"}, "id": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}}}}}}}, "get": {"summary": "Get list of customers", "description": "", "tags": ["Customer"], "produces": ["application/json"], "parameters": [{"name": "email", "in": "query", "description": "Email address of user to search for", "type": "string"}], "responses": {"200": {"description": "List of customers", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "customer_id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "idempotency_key": {"type": "string"}, "mobile": {"type": "string"}, "created": {"type": "string"}, "created_by": {"type": "string"}, "updated": {"type": "string"}, "updated_by": {"type": "string"}}}}}}}}}}, "/customer/{customer_id}": {"get": {"summary": "Get customer by ID", "description": "Get the customer with the given ID.", "tags": ["Customer"], "parameters": [{"name": "customer_id", "in": "path", "description": "The ID of the customer to retrieve", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "The customer was found.", "schema": {"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "customer_id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "idempotency_key": {"type": "string"}, "mobile": {"type": "string"}, "created": {"type": "string"}, "created_by": {"type": "string"}, "updated": {"type": "string"}, "updated_by": {"type": "string"}}}}}}}, "put": {"summary": "Update customer by ID", "description": "Update the customer with the given ID using the provided data.", "tags": ["Customer"], "parameters": [{"name": "customer_id", "in": "path", "description": "The ID of the customer to be updated", "required": true, "type": "string"}], "consumes": ["application/json"], "produces": ["application/json"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "The customer's name"}, "email": {"type": "string", "description": "The customer's email address"}, "username": {"type": "string", "description": "The customer's username", "nullable": true}, "mobile": {"type": "string", "description": "The customer's mobile number", "nullable": true}}}}}}, "responses": {"200": {"description": "The customer was updated successfully."}}}}, "definitions": {"Customer": {"type": "object", "properties": {"id": {"type": "string", "example": "1234"}, "name": {"type": "string", "example": "o1maara"}, "email": {"type": "string", "example": "<EMAIL>"}}}}, "/create-payment": {"post": {"summary": "Create a new PaymentIntent", "description": "", "tags": ["PaymentIntent"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"price": {"type": "integer"}, "currency_code": {"type": "string"}, "payment_method": {"type": "array", "items": {"type": "string"}}, "customer": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "mobile": {"type": "string"}}}, "customer_id": {"type": "string"}, "save_payment_card": {"type": "string", "default": "false"}}, "required": ["price", "currency_code", "payment_method"]}}}}, "responses": {"200": {"description": "Customer created successfully"}}}}, "/login": {"post": {"summary": "Authenticate user", "description": "", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "User authenticated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"access_token": {"type": "string"}}}}}}, "401": {"description": "Unauthorized"}}}}, "/register": {"post": {"summary": "Register a new user", "description": "", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "stripe_secret_key": {"type": "string"}, "stripe_publishable_key": {"type": "string"}, "webhook_url": {"type": "string"}, "healthcheck_url": {"type": "string"}}}}}}, "responses": {"200": {"description": "User registered successfully"}}}}, "/user": {"put": {"summary": "Update User", "description": "Update the user using the provided data.", "tags": ["<PERSON><PERSON>"], "consumes": ["application/json"], "produces": ["application/json"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"webhook_url": {"type": "string", "description": "The user's webhook_url"}, "healthcheck_url": {"type": "string", "description": "The user's healthcheck_url"}, "public_key": {"type": "string", "description": "The user's public key"}, "account_id": {"type": "string", "description": "stripe_account account id for direct charges"}, "application_fee_amount": {"type": "integer", "description": "application_fee_amount for connected account"}}}}}}, "responses": {"200": {"description": "The user was updated successfully."}}}}, "/user/{username}": {"put": {"summary": "Update User", "description": "Update the user using the provided data.", "tags": ["<PERSON><PERSON>"], "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "username", "in": "path", "description": "The Username of the user to be updated", "required": true, "type": "string"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"stripe_secret_key": {"type": "string", "description": "The user's stripe_secret_key"}, "stripe_publishable_key": {"type": "string", "description": "The user's stripe_publishable_key"}, "webhook_url": {"type": "string", "description": "The user's webhook_url"}, "healthcheck_url": {"type": "string", "description": "The user's healthcheck_url"}}}}}}, "responses": {"200": {"description": "The user was updated successfully."}}}}, "/decrypt": {"post": {"tags": ["Decryption"], "summary": "Decrypt a payment intent", "description": "Decrypts the given encrypted payment intent and returns the decrypted payment intent.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"encrypted_payment_intent": {"type": "string"}}}}}}, "responses": {"200": {"description": "The decrypted payment intent"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}}}, "/transactions": {"get": {"summary": "Retrieve a list of transactions", "parameters": [{"name": "stripe_request_id", "in": "query", "description": "Stripe request ID", "type": "string"}, {"name": "client_secret", "in": "query", "description": "Client Secret", "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"type": "object", "properties": {"stripe_request_id": {"type": "string"}, "customer": {"type": "string"}, "name": {"type": "string"}, "event": {"type": "string"}, "created": {"type": "integer"}, "balance": {"type": "integer"}, "amount_received": {"type": "integer"}, "paid": {"type": "boolean"}, "email": {"type": "string"}, "livemode": {"type": "string"}, "status": {"type": "string"}, "failure_balance_transaction": {"type": "string"}, "failure_message": {"type": "string"}, "failure_code": {"type": "string"}, "receipt_url": {"type": "string"}, "payment_method_types": {"type": "array", "items": {"type": "string"}}, "created_by": {"type": "string"}}}}}}}}}, "definitions": {"PaymentIntent": {"type": "object", "properties": {"id": {"type": "string"}, "amount": {"type": "integer"}, "currency": {"type": "string"}, "description": {"type": "string"}}}}}
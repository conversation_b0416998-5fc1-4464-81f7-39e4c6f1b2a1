import datetime
import importlib
import operator
from functools import wraps, reduce
from typing import Dict, List

# from keycloak import KeycloakError
from keycloak.exceptions import KeycloakError
from mongoengine.queryset.visitor import Q
from werkzeug import exceptions
import requests
import json


def keycloak_error_to_api_exception(decorated_function):
    message = "Unauthorized"

    def wrapper(*args, **kwargs):
        try:
            return decorated_function(*args, **kwargs)
        except KeycloakError as e:
            print(
                f"""<JWT> Refresh Token Error body: {e.response_body} 
                   status_code: {e.response_code} message: {e.error_message}"""
            )
            raise exceptions.Unauthorized(message)
        except Exception as e:
            print(f"<JWT> Refresh Token Error body: {e}")
            raise exceptions.Unauthorized(message)
        else:
            return True

    return wrapper


def auth_exception_handling(function):
    message = "Invalid token"

    @wraps(function)
    def _auth_exception_handling(*args, **kwargs):
        try:
            reply = function(*args, **kwargs)
            return reply
        except:
            raise exceptions.Unauthorized(message)

    return _auth_exception_handling


def make_request(url):
    # Make a request to the provided URL
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return "success"
        else:
            return None
    except:
        return None


def make_post_request(url, data):
    try:
        response = requests.post(url, json=data)
        return response
    except:
        return None


def try_except(function):
    @wraps(function)
    def wrapper(self, *args, **kwargs):
        try:
            print(f"[{datetime.datetime.utcnow()}] calling {function.__name__}")
            return function(self, *args, **kwargs)

        except Exception as e:
            class_name = self.__class__.__name__  # Get the name of the class
            print(
                f"[{datetime.datetime.utcnow()}] Exception at method {class_name}.{function.__name__}: {e}"
            )
            return {"status": False, "message": f"{e}"}

    return wrapper

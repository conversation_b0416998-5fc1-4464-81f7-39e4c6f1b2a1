import logging
import flask
from src.global_helpers.utils import Endpoint
from instance import consumer_config
from b2c_helpers import webhook_helpers

class FlexiroamV2LimitWebhook(Endpoint):

    def post(self):
        try:
            payload = (
                webhook_helpers.FlexiroamV2WebhookHelper.FlexiroamV2NotificationSchema(
                    **flask.request.json[0]
                )
            )
            logging.debug("we got payload: %s", payload)
            webhook_helpers.FlexiroamV2WebhookHelper(
                instance_config=consumer_config
            ).limit_notification(payload)
            return self.make_http_response({"status": True})
        except Exception as e:
            logging.error(
                f"request: %s faced exception while receiving Flexiroam v2 limit push notification: %s",
                flask.request.json,
                str(e),
            )
            return self.make_http_response({"status": False, "reason": str(e)}, 200)

import datetime
import logging
import json
from app_models.consumer_models import Profiles
from flask import request
from src.global_helpers.utils import Endpoint
from b2c_helpers import webhook_helpers
from instance import consumer_config

logger = logging.getLogger(__name__)
class VodafoneLimitWebhook(Endpoint):
    def __init__(self):
        super().__init__()

    def post(self):
        try:
            vodafone_limit_input = json.loads(request.data)
            logger.info("vodafone_limit_input is %s JUST FOR TESTING MUST BE REMOVED LATER ON", vodafone_limit_input)
            webhook_helpers.VodafoneWebhookHelper(instance_config=consumer_config).limit_notification(
                vodafone_limit_input)
            return self.make_http_response({"status": True})
        except Exception as exception:
            logger.info("faced exception while receiving vodafone limit push notification: %s " , str(exception))
            return self.make_http_response({"status": False, "reason": str(exception)}, 400)

    def patch(self):
        try:
            vodafone_limit_input = json.loads(request.data)
            logging.info("received event from vodafone, trying to suspend profile")
            id_type = vodafone_limit_input.get("idType")
            id_value = vodafone_limit_input.get("idValue")
            ack_id = vodafone_limit_input.get("acknowledgementID")
            notif_type = vodafone_limit_input.get("notificationType")
            if notif_type != "thing suspended":
                logging.error("Received unsupported notificationType from Vodafone's side: %s ", notif_type)

            if id_type == "iccids":
                Profiles.objects(iccid=id_value, vendor_name="Vodafone").update(set__status=False, set__ack_id=ack_id)
            elif id_type == "msisdns":
                Profiles.objects(msisdn=id_value, vendor_name="Vodafone").update(set__status=False)
            else:
                logging.error("Received unsupported idtype from Vodafone's side type: %s , value: %s", id_type, id_value)
                logger.info("event received: %s", vodafone_limit_input)
            return self.make_http_response({"status": True})
        except Exception as exception:
            logger.error("faced exception while receiving vodafone limit push notification: %s",  str(exception))
            return self.make_http_response({"status": False, "reason": str(exception)}, 400)

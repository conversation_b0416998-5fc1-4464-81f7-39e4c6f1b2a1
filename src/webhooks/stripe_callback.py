import json
from src.global_helpers.utils import Endpoint
from src.services.allocation_helpers import AllocationBundle
from flask import request
import threading
import logging
logger = logging.getLogger(__name__)

class StripeWebhook(Endpoint):
    allocation_helper: AllocationBundle

    def __init__(self):
        super().__init__()
        self.allocation_helper = AllocationBundle()

    def post(self):
        data = json.loads(request.get_data())
        thread = threading.Thread(target=self.allocation_helper.stripe_callback, args=(data,))
        thread.start()
        return self.make_http_response({"status": True, "message": "Acknowledged"})

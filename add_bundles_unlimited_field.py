import logging
from main import app
from app_models import consumer_models as cm

with app.app_context():

    cm.Bundles.objects(data_amount__gt=0).update(unlimited=False)
    logging.info("Updated limited bundles --> set unlimited to false")
    cm.Bundles.objects(data_amount__lte=0).update(unlimited=True, support_topup=True)
    logging.info("Updated unlimited bundles with data_amount = -1 --> set unlimited to true")
# pyproject.toml

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "b2c_helpers"
version = "1.1"
description = "Common Code Used In B2C Services"
# readme = "README.md"  # Removed/Commented out as README.md is missing in your tree
authors = [
    {name = "Yaman Awad", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>j", email = "<EMAIL>"},
    {name = "<PERSON><PERSON> Bourgi", email = "<EMAIL>"},
    {name = "<PERSON>"},
    {name = "Karam Thebian"},
]
# Consider using a specific SPDX identifier like "BSD-3-Clause" if applicable
license = {text = "BSD"}
requires-python = ">=3.8,<3.14"
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Intended Audience :: Developers",
    # "License :: OSI Approved :: BSD License", # Removed deprecated classifier
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    # Python 3.14 is not released yet as of April 2025, you might want to adjust the upper bound later.
    # "Programming Language :: Python :: 3.14",
]
dependencies = [
    "curlify",
    "google-auth",
    # Add any other direct dependencies from your requirements.txt if needed for the package itself
]

[project.urls]
"Homepage" = "https://gitlab.com/monty-mobile1/esim/b2c/esim_b2c_helpers"
"Bug Tracker" = "https://gitlab.com/monty-mobile1/esim/b2c/esim_b2c_helpers/issues"
"Source" = "https://gitlab.com/monty-mobile1/esim/b2c/esim_b2c_helpers"

[tool.setuptools]
# *** CRITICAL CHANGE HERE ***
# Point to the actual directory containing your source code
packages = ["b2c_helpers"]
include-package-data = true
zip-safe = false


[tool.black]
line-length = 140

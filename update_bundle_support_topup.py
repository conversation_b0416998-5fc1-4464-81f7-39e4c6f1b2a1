import logging
from app_models.consumer_models import Bundles
from main import app

logger = logging.getLogger(__name__)

"""
    This script updates Bundles to set support_topup=True where the field is not already set.
"""
with app.app_context():
    try:
        bundles = Bundles.objects(support_topup__exists=False)

        count = bundles.count()
        logger.info("Found %d bundles with no support_topup field", count)

        if count > 0:
            updated = bundles.update(set__support_topup=True)
            logger.info("Updated %d bundles to set support_topup=True", updated)
        else:
            logger.info("No bundles needed updating.")

    except Exception:
        logger.exception("Error updating bundles")

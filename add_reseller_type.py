#!/usr//venv3.7/bin/python
import sys
from main import app
from app_models import reseller_models

with app.app_context():
    sys.path.append('/')
    reseller_models.Order_history.objects(reseller_id__exists=True).update(set__reseller_type="reseller")
    updated_records_count = reseller_models.Order_history.objects(
        reseller_type="reseller"
    ).count()

    print(f"Total Updated Records for reseller_type = reseller: {updated_records_count}")

from connexion.exceptions import ProblemException

from swagger_server.models.customize_price_request import CustomizePriceRequest

from app_helpers.helpers import TenancyHelpers, DataHelpers
from Services.reseller_services.customize_price_service import customize_price
import csv
import logging

logger = logging.getLogger(__name__)
def customize_price_csv(csv_file=None, reseller_id=None):
    try:
        i = 1
        bundles = []
        bundle_codes = set()
        invalid_csv_content_msg = "Invalid CSV Contents!"

        if not csv_file.filename.lower().endswith('.csv'):
            raise ProblemException(status=400, title="Error", detail="File is not a CSV file")

        content = csv_file.read()

        # Decode the bytes into a string and split the lines, removing BOM character
        lines = csv.reader(content.decode('utf-8-sig').splitlines())

        # Use the first line as the header for the dictionary
        headers = next(lines)

        fieldnames = ['bundle_code', 'custom_price', 'is_active']
        if set(fieldnames) != set(headers):
            raise ProblemException(status=400, title="Error", detail=invalid_csv_content_msg,
                                   ext={"reason": "Headers should be ['bundle_code', 'custom_price', 'is_active']"})

        for row in lines:
            row = DataHelpers.dictCleaner(dict(zip(headers, row)))
            if not row.get("bundle_code"):
                raise ProblemException(status=400, title="Error", detail=invalid_csv_content_msg,
                                       ext={"reason": "'bundle_code' is a required property - 'bundles." + str(i) + "'"})

            if row["bundle_code"] in bundle_codes:
                # continue
                raise ProblemException(status=400, title="Error", detail="Duplicate bundle code!",
                                       ext={"reason": "Duplicate bundle code in row " + str(i)})

            bundle_codes.add(row["bundle_code"])

            custom_price = row.get("custom_price")
            # custom_price is not None
            if custom_price:
                try:
                    custom_price = float(custom_price)
                    if not 0 <= custom_price <= 99999999999999:
                        raise ValueError()
                except:
                    raise ProblemException(status=400, title="Error", detail=invalid_csv_content_msg,
                                           ext={"reason": "'custom_price' is invalid! - 'bundles." + str(i) + "'"})
                row["custom_price"] = custom_price

            is_active = row.get("is_active")
            # is_active is not None
            if is_active:
                if is_active.lower() == 'true':
                    row["is_active"] = True
                elif is_active.lower() == 'false':
                    row["is_active"] = False
                else:
                    raise ProblemException(status=400, title="Error", detail=invalid_csv_content_msg,
                                           ext={"reason": "'is_active' is invalid! - 'bundles." + str(i) + "'"})

            bundles.append(row)
            i = i + 1
        request = CustomizePriceRequest.from_dict({"bundles":bundles})
        return customize_price(request=request,reseller_id=reseller_id)
    except ProblemException as e:
        raise e
    except Exception as e:
        logger.error("Error customizing price, Error : %s", e)
        raise ProblemException(status=400, title="Error", detail="Invalid CSV File")

from connexion.exceptions import ProblemException

from Services.reseller_services.notify_reseller_service import pre_notify_reseller
from app_helpers import mongodb
from helpers.db_helper import format_bundles
import connexion
from swagger_server.models.customize_price_response import CustomizePriceResponse
from swagger_server.models.customize_price_request import CustomizePriceRequest
from bson import ObjectId
from app_helpers.helpers import TenancyHelpers
import logging

logger = logging.getLogger(__name__)

def customize_price(request: CustomizePriceRequest, reseller_id=None, currency_code=None):
    try:
        logger.info("customizing corporate price, with request : %s , and reseller id : %s", request, reseller_id)
        one_Failure = False
        one_success = False
        super_admin_view = False
        response_code = 200
        reset_customizations = request.reset_customizations

        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        filter_query = {}
        updated_fields={}
        if apply_Tenancy == 0:
            filter_query = {"reseller_id": ObjectId(reseller_id)}
            super_admin_view = True
        if request.set_selling_revenue is not None:
            mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)},
                               {"$set": {"rate_revenue": request.set_selling_revenue}}, apply_Tenancy=0)
            updated_fields["rate_revenue"] = request.set_selling_revenue
            one_success = True

        if reset_customizations:
            mongodb.update_many("reseller_bundle_price", filter_query,
                                {"$unset": {"bundle_retail_price": ""}}, apply_Tenancy=apply_Tenancy)
            one_success = True
        bundles_not_found = []
        invalid_bundle_prices = []
        if request.bundles:

            for item in request.bundles:
                bundle_code = item.bundle_code
                custom_price = item.custom_price
                custom_bundle_name = item.custom_bundle_name
                bundle_status = item.is_active
                bundle_tag = item.bundle_tag
                bundle = format_bundles(reseller_id=reseller_id, bundle_code=bundle_code,
                                        super_admin_view=super_admin_view, reseller_admin_view=True)

                if bundle.get('bundle_code'):
                    new_values = {"$set": {}}
                    filter_query["bundle_code"] = bundle_code

                    if custom_price is not None:
                        currency_rate = False
                        if currency_code:
                            currency = mongodb.find_one("currency_codes",
                                                        {"currency_code": currency_code, "is_available": True},
                                                        apply_Tenancy=0)
                            if not currency:
                                raise ProblemException(status=400, title="Error", detail="Currency Not Found!")
                            currency_rate = currency.get("currency_rate")
                            custom_price = custom_price / currency_rate
                        upper_limit = round(bundle['subscriber_price'] * 3, 2)
                        lower_limit = 0
                        if lower_limit <= custom_price <= upper_limit:
                            new_values["$set"]['bundle_retail_price'] = custom_price
                            updated_fields["bundle_retail_price"] = custom_price
                        else:
                            if currency_code:
                                invalid_bundle_prices.append({"bundle_code": bundle_code,
                                                              "bundle_retail_price": custom_price * currency_rate,
                                                              "bundle_unit_price": bundle['subscriber_price'] * currency_rate})

                            else:
                                invalid_bundle_prices.append({"bundle_code": bundle_code,
                                                              "bundle_retail_price": custom_price,
                                                              "bundle_unit_price": bundle['subscriber_price']})
                            one_Failure = True
                            continue
                    if custom_bundle_name is not None:
                        new_values["$set"]['custom_bundle_name'] = custom_bundle_name
                        updated_fields["custom_bundle_name"] = custom_bundle_name
                    if bundle_status is not None:
                        new_values["$set"]["is_active"] = bundle_status
                        updated_fields["is_active"] = bundle_status
                    if bundle_tag is not None:
                        new_values["$set"]["bundle_tag"] = bundle_tag
                        updated_fields["bundle_tag"] = bundle_tag
                    if custom_price is not None or bundle_status is not None or bundle_tag is not None or custom_bundle_name is not None:
                        one_success = True
                        mongodb.update_one("reseller_bundle_price", filter_query, new_values,
                                           apply_Tenancy=apply_Tenancy,
                                           upsert=True)
                        pre_notify_reseller(reseller_id=reseller_id, bundle=bundle, updated_fields=updated_fields, notified_on="update_bundle")


                else:
                    bundles_not_found.append({"bundle_code": bundle_code})
                    one_Failure = True

        if one_success:
            message = "Bundle Edited Successfully."
            response_code = 200
            if bundles_not_found or invalid_bundle_prices:
                message = "Some " + message + " However, errors occurred."
                if bundles_not_found:
                    message = message + " Some Bundles were not found."
                if invalid_bundle_prices:
                    message = message + " Some Bundle Custom Prices were out of range!(0-200% of Unit price)."
                    response_code = 207

        elif one_Failure:
            message = "Could not update prices! Errors Occured."
            if bundles_not_found:
                message = message + " Some Bundles were not found."
            if invalid_bundle_prices:
                message = message + " Some Bundle Custom Prices were out of range!(0-200% of Unit price)."
            response_code = 400

        else:
            message = "No Changes Were Made"
        return CustomizePriceResponse(detail=message, message=message, bundles_not_found=bundles_not_found,
                                      invalid_bundle_prices=invalid_bundle_prices), response_code
    except ProblemException as e:
        raise e
    except Exception as e:
        logger.error("Error customizing price : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

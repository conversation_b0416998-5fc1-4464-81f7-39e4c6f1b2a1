import connexion
import pymongo.errors
from swagger_server.models.topup_balance_request import TopupBalanceRequest  # noqa: E501
from swagger_server.models.topup_balance_response import TopupBalanceResponse  # noqa: E501
from connexion.exceptions import ProblemException
from app_helpers import mongodb
from app_helpers.helpers import DataHelpers
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)
def topup_reseller_balance(reseller_id, topupRequest: TopupBalanceRequest = None,currency_code = None):
    '''

    :param reseller_id:
    :param topupRequest:
    :return:
    '''

    try:
        logger.info("Trying to topup reseller balance, reseller id : %s, topup request : %s", reseller_id, topupRequest)
        if not (topupRequest.topup_amount or topupRequest.credit_limit):
            raise ProblemException(status=400, title="Error", detail="Provide topup_amount amount or credit_limit")

        if topupRequest.topup_amount and topupRequest.credit_limit:
            raise ProblemException(status=400, title="Validation Error", detail="You cannot provide both topup_amount "
                                                                                "and credit_limit at the same time.")
        topup_amount = topupRequest.topup_amount
        credit_limit = topupRequest.credit_limit

        # Handle currency conversion if applicable
        if currency_code:
            currency = mongodb.find_one("currency_codes",
                                        {"currency_code": currency_code, "is_available": True},
                                        apply_Tenancy=0)
            if not currency:
                raise ProblemException(status=400, title="Error", detail="Currency Not Found!")

            currency_rate = currency.get("currency_rate")
            if currency_rate:
                topup_amount = (topup_amount or 0) / currency_rate
                credit_limit = (credit_limit or 0) / currency_rate

        # Prepare transaction details
        transaction_type = None
        update_query = {}
        if topup_amount:
            transaction_type = "Topup"
            update_query = {"$inc": {"balance": topup_amount}}
        if credit_limit:
            transaction_type = "Credit Limit"
            update_query = {"$set": {"credit_limit": credit_limit}}

        return_balance_result = mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)}, update_query,
                                                   apply_Tenancy=0)
        if return_balance_result.matched_count == 0:
            raise ProblemException(status=404, title="Error", detail="Reseller Not Found")

        transaction_history = {
            "type": transaction_type,
            "amount": topup_amount or credit_limit,
            "reseller_id": ObjectId(reseller_id)
        }
        mongodb.insert_one("transaction_history", transaction_history, apply_Tenancy=0)

        response = TopupBalanceResponse(message=f"Reseller {transaction_type} request Successful")
        return response, 201

    except pymongo.errors.PyMongoError as e:
        logger.error("Error : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")


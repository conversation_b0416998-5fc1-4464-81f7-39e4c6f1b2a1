from Services.authorization_services.keyCloak import KeycloakHelper
from Services.role_services.get_role_by_id_service import get_role_by_id
from app_helpers.helpers import DataHelpers
from connexion.exceptions import ProblemException, AuthenticationProblem

import connexion
import logging
logger = logging.getLogger(__name__)


def check_ApiKeyAuth(api_key, required_scopes):
    logger.debug("Starting check_ApiKeyAuth with scopes: %s", required_scopes)

    keycloakHelper = KeycloakHelper()
    logger.debug("Validating token: %s", api_key[:5] + "..." if api_key else "None")
    claims = keycloakHelper.validate_token(api_key)
    logger.debug("Token validated, claims received: %s",
                 {k: v for k, v in claims.items() if k != 'sub'})

    role_id = claims.get('role_id', '')
    logger.debug("Role ID from claims: %s", role_id)

    role = DataHelpers.to_good_dict(get_role_by_id(role_id)[0])
    logger.debug("Role details fetched: %s", {k: v for k, v in role.items() if k != 'permissions'})

    permissions = role.get("permissions", [])
    api_names = [d['api_name'] for d in permissions]
    logger.debug("Available API names: %s", api_names)

    operation_id = DataHelpers.to_camel_case(connexion.request.endpoint.split("controller_")[-1])
    logger.debug("Current operation ID: %s", operation_id)

    allowedToAccess = operation_id in api_names

    if allowedToAccess:
        claims['token'] = api_key
        claims['token_type'] = 'Access-Token'
        claims['role_name'] = role['name']
        claims['permission_level'] = role.get('permission_level', "")
        claims['access_level'] = role.get('access_level', "basic")
        logger.debug("Authentication successful, returning claims for user: %s",
                     claims.get('sub', {}))
        return {'sub': claims.get("sub", {}), 'test_key': claims}
    else:
        logger.warning("Authentication failed: Operation %s not in allowed API names for role %s",
                       operation_id, role.get('name'))
        raise AuthenticationProblem(401, "Unauthenticated", "You are not allowed to Use this API")

import connexion
import pymongo.errors
from swagger_server.models.add_agent_request import AddAgentRequest  # noqa: E501
from swagger_server.models.add_agent_response import AddAgentResponse  # noqa: E501
from connexion.exceptions import ProblemException
from app_helpers import mongodb
from app_helpers.helpers import DataHelpers
from Services.authorization_services.keyCloak import KeycloakHelper
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from keycloak import KeycloakGetError
from app_helpers.helpers import TenancyHelpers
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

def add_agent(agentRequest: AddAgentRequest = None, reseller_id=None, branch_id=None):
    try:
        logger.info("Adding agent with username: %s, reseller ID: %s, branch ID: %s",
                   agentRequest.username if agentRequest else "<none>", reseller_id, branch_id)
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id_for_add_agent(reseller_id, branch_id)
        if reseller_id != "000000000000000000000000":
            reseller = get_reseller_by_id(reseller_id)

        """converting agent object to dictionary and preparing the ObjectID format"""
        agentRequest.is_active = True
        agentRequest = DataHelpers.serializer(agentRequest)
        agentRequestDict = DataHelpers.to_good_dict(agentRequest)
        agentRequestDict["reseller_id"] = ObjectId(reseller_id)

        if branch_id is not None and apply_Tenancy in [0, 1]:
            agentRequestDict["branch_id"] = ObjectId(branch_id)

        """Checking if the role provided Exists"""
        role_name = agentRequestDict.get("role_name")
        role = mongodb.find_one("roles", {"name": role_name}, apply_Tenancy=0)

        if not role:
            logger.warning("Attempt to add agent with non-existent role: %s", role_name)
            raise ProblemException(status=400, title="Error", detail="Role Provided Does not Exist!")
        user_permission_level = connexion.context.get("token_info", {}).get("test_key", {}).get("permission_level", 5)
        if role.get("permission_level") < user_permission_level:
            logger.warning("Unauthorized attempt to add agent with higher permission role: %s (level: %s) by user with permission level: %s",
                         role.get("name"), role.get("permission_level"), user_permission_level)
            raise ProblemException(status=401, title="Error", detail="Cannot Add agent with Role Above than your permission!")

        if role.get("permission_level") == 1 and not reseller_id == "000000000000000000000000":
            logger.warning("Attempt to use admin role (level 1) for non-admin reseller: %s", reseller_id)
            raise ProblemException(status=403, title="Forbidden", detail="You are not allowed to use this role!")
        if role.get("permission_level") == 2 and branch_id:
            logger.warning("Attempt to use reseller role (level 2) for branch: %s", branch_id)
            raise ProblemException(
                status=403,
                title="Forbidden",
                detail="You are not allowed to use this role in Branches! Use Permission Level 3 Roles instead.",
            )
        if role.get("permission_level") == 3 and not branch_id:
            logger.warning("Attempt to use branch role (level 3) outside of branch context")
            raise ProblemException(
                status=403,
                title="Forbidden",
                detail="You are not allowed to use this role outside Branches! Use Permission Level 2 Roles instead.",
            )

        agentRequestDict["role_id"] = role.get("_id")
        # TODO CHECK ROLE PERMISSION MBRESELLERADMIN AND RESELLER ADMIN

        """adding user to keycloak, assigning roles, and adding to mongodb"""
        keycloak_helper = KeycloakHelper()
        agentRequestDict["username"] = agentRequestDict.get("username").lower()

        keycloakAgentRequestDict = agentRequestDict.copy()

        keycloakAgentRequestDict.pop("role_name", "")
        user_id_in_keycloak = keycloak_helper.create_user(keycloakAgentRequestDict)

        agentRequestDict.pop("password", "")
        agentRequestDict["keycloak_id"] = user_id_in_keycloak
        if reseller_id == "000000000000000000000000":
            agentRequestDict["reseller_id"] = ObjectId("111111111111111111111111")
        insertAgentResult = mongodb.insert_one("agent", agentRequestDict, apply_Tenancy=apply_Tenancy)
        keycloakAgentRequestDict["user_id"] = str(insertAgentResult.inserted_id)
        keycloak_helper.update_user(user_id_in_keycloak, keycloakAgentRequestDict)
        logger.info("Agent created successfully - Username: %s, ID: %s", agentRequest.username, insertAgentResult.inserted_id)
        return AddAgentResponse(message="Agent Added Successfully.", agent_id=str(insertAgentResult.inserted_id))

    except pymongo.errors.DuplicateKeyError as e:
        duplicate_field = "unknown"
        try:
            duplicate_field = e.details["errmsg"].split("index: ")[1].split(" ")[0]
            logger.warning("Duplicate key error when adding agent - Field: %s, Username: %s",
                         duplicate_field, agentRequest.username if agentRequest else "<none>")
        except (KeyError, IndexError):
            logger.error("Error parsing duplicate key message: %s", str(e))

        if duplicate_field == "email":
            raise ProblemException(status=400, title="Error", detail="Email already Exists")
        elif duplicate_field == "username":
            raise ProblemException(status=400, title="Error", detail="Username already Exists")
        raise ProblemException(status=400, title="Error", detail="Username or Email already Exists")

    except pymongo.errors.PyMongoError as e:
        logger.error("MongoDB error when adding agent - Username: %s, Error: %s",
                    agentRequest.username if agentRequest else "<none>", str(e))
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

    except KeycloakGetError as e:
        error_msg = str(e.error_message) if hasattr(e, 'error_message') else str(e)
        logger.warning("Keycloak error when adding agent - Username: %s, Error: %s",
                     agentRequest.username if agentRequest else "<none>", error_msg)

        if hasattr(e, 'error_message'):
            if e.error_message == b'{"errorMessage":"User exists with same username or email"}':
                raise ProblemException(status=400, title="Error", detail="Username already Exists")
            elif e.error_message == b'{"errorMessage":"User exists with same email"}':
                raise ProblemException(status=400, title="Error", detail="Email already Exists")

        raise ProblemException(status=400, title="Error", detail="Error creating user in authentication system")

    except ProblemException as e:
        logger.error("Problem exception when adding agent - Username: %s, Error: %s",
                    agentRequest.username if agentRequest else "<none>", str(e))
        raise e

    except Exception as e:
        logger.error("Unexpected error when adding agent - Username: %s, Error: %s",
                    agentRequest.username if agentRequest else "<none>", str(e))
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

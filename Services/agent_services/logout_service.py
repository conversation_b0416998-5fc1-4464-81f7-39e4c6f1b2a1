from swagger_server.models.logout_response import LogoutResponse  # noqa: E501
from swagger_server.models.refresh_token_request import RefreshTokenRequest  # noqa: E501
from connexion.exceptions import ProblemException
from Services.authorization_services import keyCloak
from keycloak.exceptions import KeycloakGetError
import logging

logger = logging.getLogger(__name__)

def logout(logoutRequest: RefreshTokenRequest = None):
    try:
        refresh_token = logoutRequest.refresh_token if logoutRequest else None
        logger.info("Logout process initiated")
        logger.debug("Processing logout request with refresh token: %s...", refresh_token[:10] if refresh_token and len(refresh_token) > 10 else "<none>")

        keycloak = keyCloak.KeycloakHelper()
        logger.debug("Getting Keycloak user realm client")
        client = keycloak.get_user_releam_client()

        try:
            logger.debug("Sending logout request to Keycloak")
            keycloakResponse = client.logout(logoutRequest.refresh_token)
            logger.debug("Keycloak logout response received: %s", keycloakResponse)
        except KeycloakGetError as e:
            logger.error("Keycloak logout failed - Invalid refresh token: %s", str(e))
            raise ProblemException(status=400, title="Error", detail="Invalid refresh token")

        logger.info("Logout successful")
        return LogoutResponse(message="Logout Successful")

    except ProblemException as e:
        logger.error("Problem during logout process: %s", str(e))
        raise e
    except Exception as e:
        logger.error("Unexpected error during logout: %s", str(e))
        raise ProblemException(status=500, title="Internal Server Error",
                               detail="The server encountered an internal error and was unable to complete your request.")

import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.get_agent_response import GetAgentResponse  # noqa: E501
from connexion.exceptions import ProblemException
from app_helpers.helpers import DataHelpers
import logging

logger = logging.getLogger(__name__)

def get_agent_by_username(username=None, email=None):
    logger.info("Get agent by username with username : %s and email : %s", username, email)
    if not username and not email:
        return {"message": "No user found"}, "", 404
    getQuery = {"$or": [{"username": username}, {"email": email}]}
    try:
        result = mongodb.find_one("agent", getQuery, apply_Tenancy=0)
        if not result:
            logger.error("Result is : %s", result)
            return {"message": "No user found"}, "", 404
        result = DataHelpers.deserializer(result)
        result["agent_id"] = str(result.pop("_id", ""))
        result.pop("added_by", "")
        response = GetAgentResponse.from_dict(result)
        if response.email == email:
            found_by = "email"
        else:
            found_by = "username"
        return response, found_by, 200
    except pymongo.errors.PyMongoError:
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

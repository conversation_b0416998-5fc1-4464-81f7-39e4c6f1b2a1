import datetime
from app_helpers import mongodb
from swagger_server.models.get_affiliate_program_response import (
    GetAffiliateProgramResponse,
)  # noqa: E501
from connexion.exceptions import ProblemException
from bson import ObjectId
from instance import consumer_config as instance_config
from app_helpers.encrypt_helper import Crypt

import logging

logger = logging.getLogger(__name__)

def get_affiliate_program(affiliate_id=None):
    if not affiliate_id:
        logger.error("Missing required parameter: affiliate_id")
        raise ProblemException(status=400, title="Error", detail="affiliate_id is a required parameter!")

    logger.info("Fetching affiliate program for ID: %s ", affiliate_id)
    pipeline = [
        {
            "$match": {
                "_id": ObjectId(affiliate_id),
                "category": "promo_code",
                "affiliate_program": True,
            }
        },
        {
            "$lookup": {
                "from": "reseller",
                "localField": "reseller_id",
                "foreignField": "_id",
                "as": "reseller",
            }
        },
        {"$unwind": {"path": "$reseller", "preserveNullAndEmptyArrays": True}},
    ]

    result = mongodb.aggregate("redeem_codes", pipeline, apply_Tenancy=False)
    result = list(result)
    if result:
        logger.info(f"Affiliate program found for ID: {affiliate_id}")
        affiliate_program = result[0]

        # check if campaign is expired

        date_now = datetime.datetime.utcnow()
        if affiliate_program.get("expiry_datetime") < date_now:
            logger.warning("Affiliate campaign expired for ID: %s ",  affiliate_id)
            raise ProblemException(status=400, title="Error", detail="Affiliate Campaign Expired.")

        image_exists = affiliate_program.get("reseller", {}).get("image_type", False)
        reseller_id = affiliate_program.get("reseller", {}).get("_id", False)
        image_type = affiliate_program.get("reseller", {}).get("image_type", "png")
        if image_exists and instance_config.ENVIRONMENT == "production":
            logo_uri = "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/" + str(reseller_id) + "." + image_type
        elif image_exists:
            logo_uri = "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/monty-esim-logo.png"
        else:
            logo_uri = None

        promocode_encrypted = affiliate_program.get("redeem_code", "")
        if promocode_encrypted:
            str_key = instance_config.promo_code_key
            encrypt_helper = Crypt()
            promo_code = encrypt_helper.decrypt(promocode_encrypted, str_key)
        else:
            logger.error("Promo-code not found for affiliate program.")
            raise ProblemException(status=400, title="Error", detail="Promo-code not found.")
        discount_rate = affiliate_program.get("discount_percent", None)
        amount = affiliate_program.get("amount", None)
        status = affiliate_program.get("status", "")
        draft = affiliate_program.get("draft", False)
        if status.lower() == "inactive":
            logger.warning("Inactive affiliate campaign for ID: %s",  affiliate_id)
            raise ProblemException(status=400, title="Error", detail="Affiliate Campaign Expired.")

        logger.info("Affiliate program successfully retrieved for ID:%s ",  affiliate_id)
        response = GetAffiliateProgramResponse(
            affiliate_id=affiliate_id,
            image_url=logo_uri,
            promo_code=promo_code,
            discount_rate=discount_rate,
            amount=amount,
            draft=draft,
        )

        return response

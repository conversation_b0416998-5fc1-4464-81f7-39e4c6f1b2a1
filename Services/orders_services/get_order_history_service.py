import datetime
import pymongo.errors
from app_helpers import mongodb, main_helper
from swagger_server.models.get_order_history_failed_response import (
    GetOrderHistoryFailedResponse,
)  # noqa: E501
from swagger_server.models.get_order_history_successful_response import (
    GetOrderHistorySuccessfulResponse,
)  # noqa: E501
from swagger_server.models.get_order_history_response import GetOrderHistoryResponse
from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers, TenancyHelpers, filter_sensitive_fields
from helpers.db_helper import join_subscriber_into_reseller
import re
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from instance import consumer_config

import logging

logger = logging.getLogger(__name__)


def get_order_history(
    country_code=None,
    date_from=None,
    date_to=None,
    reseller_id=None,
    page_size=25,
    page_number=1,
    export=False,
    branch_id=None,
    order_id=None,
    order_reference=None,
    search=None,
    fields_to_search=None,
    currency_code=None,
):
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(
        reseller_id, branch_id
    )
    reseller = get_reseller_by_id(reseller_id)
    logger.info(
        f"reseller_id {reseller_id} branch_id {branch_id} apply_Tenancy {apply_Tenancy}"
    )
    database_name = reseller[0].tenant_name
    if database_name is None:
        database_name = consumer_config.decrypted_db_name_alias

    getQuery = {}
    pipeline = []
    number_search_required = False
    searchQuery = {}
    try:
        if reseller_id != "111111111111111111111111":
            if order_reference is not None:
                getQuery["order_reference"] = order_reference

            if search:
                escaped_search = re.escape(search)

                searchQuery = {"$or": []}
                if not fields_to_search:
                    fields_to_search = [
                        "order_status",
                        "bundle_name",
                        "client_email",
                        "client_name",
                        "bundle_price",
                        "bundle_retail_price",
                        "whatsapp_number",
                    ]

                for field in fields_to_search:
                    if field == "client_name":
                        searchQuery["$or"].append(
                            {field: {"$regex": escaped_search, "$options": "i"}}
                        )
                    elif field not in [
                        "client_name",
                        "bundle_price",
                        "bundle_retail_price",
                    ]:
                        searchQuery["$or"].append(
                            {field: {"$regex": search, "$options": "i"}}
                        )

                if ("bundle_price" in fields_to_search) or (
                    "bundle_retail_price" in fields_to_search
                ):
                    integer_detected = re.match("^[0-9]+$", search)

                    number_ending_in_dot = re.match("^[0-9]+\.$", search)

                    number_detected = re.match("^[0-9]+\.?[0-9]*$", search)

                    if integer_detected or number_ending_in_dot or number_detected:
                        number_search_required = True

            if country_code:
                getQuery["country_code"] = country_code
            if order_id:
                # Decode URL-encoded spaces
                getQuery["_id"] = ObjectId(order_id)

            if date_from and date_to:

                if date_from > date_to:
                    raise ProblemException(
                        status=400, title="Error", detail="Invalid Date Inputs"
                    )

                getQuery["date_created"] = {"$gte": date_from, "$lte": date_to}

                if date_from == date_to:
                    getQuery["date_created"] = date_from

            elif date_from:

                getQuery["date_created"] = {"$gte": date_from}
            elif date_to:

                getQuery["date_created"] = {"$lte": date_to}

            if apply_Tenancy == 0:
                if branch_id:
                    getQuery["branch_id"] = ObjectId(branch_id)

                getQuery["reseller_id"] = ObjectId(reseller_id)
            elif apply_Tenancy == 1:
                if branch_id:
                    getQuery["branch_id"] = ObjectId(branch_id)

            if not export:
                if not page_size:
                    page_size = 25
                if not page_number:
                    page_number = 1
                skip = (page_number - 1) * page_size
            else:
                skip = 0
                page_size = 0
            pipeline.append(
                {"$match": getQuery}
            )  # Equivalent to the find query's filter

            if number_search_required and not currency_code:
                search_number_stringify_stage = {"$addFields": {}}
                if "bundle_price" in fields_to_search:
                    search_number_stringify_stage["$addFields"][
                        "converted_bundle_price_string"
                    ] = {"$toString": "$bundle_price"}
                    searchQuery["$or"].append(
                        {
                            "converted_bundle_price_string": {
                                "$regex": search,
                                "$options": "i",
                            }
                        }
                    )

                if "bundle_retail_price" in fields_to_search:
                    search_number_stringify_stage["$addFields"][
                        "converted_retail_price_string"
                    ] = {"$toString": "$bundle_retail_price"}
                    searchQuery["$or"].append(
                        {
                            "converted_retail_price_string": {
                                "$regex": search,
                                "$options": "i",
                            }
                        }
                    )
                pipeline.append(search_number_stringify_stage)

            if not (currency_code and number_search_required) and searchQuery:
                pipeline.append(
                    {"$match": searchQuery}
                )  # Equivalent to the find query's filter

            # Join with the Bundles collection to get the region_code
            pipeline.append(
                {
                    "$lookup": {
                        "from": "bundles",
                        "localField": "bundle_code",  # Match bundle_code in order_history
                        "foreignField": "bundle_code",  # Match bundle_code in bundles
                        "as": "bundle_details",  # Add the joined details under this field
                    }
                }
            )
            # Unwind to deconstruct the array from the $lookup
            pipeline.append(
                {
                    "$unwind": {
                        "path": "$bundle_details",
                        "preserveNullAndEmptyArrays": True,
                    }
                }  # Keep the result even if there's no match
            )

            # Add region_code to the projection
            pipeline.append(
                {"$addFields": {"region_code": "$bundle_details.region_code"}}
            )  # Get region_code from the bundle details

            pipeline.append(
                {"$sort": {"date_created": pymongo.DESCENDING}}
            )  # Sort by 'date_created' in descending order
            if not export:
                if not (number_search_required and currency_code):
                    pipeline.append({"$skip": skip})
                    pipeline.append({"$limit": page_size})
            if currency_code:
                pipeline.extend(
                    [
                        {"$addFields": {"additional_currency_code": currency_code}},
                        {
                            "$lookup": {
                                "from": "currency_codes",
                                "let": {
                                    "additional_currency_code": "$additional_currency_code"
                                },
                                "pipeline": [
                                    {
                                        "$match": {
                                            "$expr": {
                                                "$eq": [
                                                    "$currency_code",
                                                    "$$additional_currency_code",
                                                ]
                                            },
                                            "is_available": True,
                                        }
                                    }
                                ],
                                "as": "currency",
                            }
                        },
                        {
                            "$unwind": {
                                "path": "$currency",
                                "preserveNullAndEmptyArrays": True,
                            }
                        },
                        {
                            "$addFields": {
                                "bundle_price_in_additional_currency": {
                                    "$cond": {
                                        "if": {"$ifNull": ["$currency", False]},
                                        "then": {
                                            "$multiply": [
                                                "$bundle_price",
                                                "$currency.currency_rate",
                                            ]
                                        },
                                        "else": None,
                                    }
                                }
                            }
                        },
                        {
                            "$addFields": {
                                "bundle_retail_price_in_additional_currency": {
                                    "$cond": {
                                        "if": {"$ifNull": ["$currency", False]},
                                        "then": {
                                            "$multiply": [
                                                "$bundle_retail_price",
                                                "$currency.currency_rate",
                                            ]
                                        },
                                        "else": None,
                                    }
                                }
                            }
                        },
                        {
                            "$addFields": {
                                "bundle_retail_price": {
                                    "$round": ["$bundle_retail_price", 2]
                                },
                                "bundle_price": {"$round": ["$bundle_price", 2]},
                                "bundle_price_in_additional_currency": {
                                    "$round": [
                                        "$bundle_price_in_additional_currency",
                                        2,
                                    ]
                                },
                                "bundle_retail_price_in_additional_currency": {
                                    "$round": [
                                        "$bundle_retail_price_in_additional_currency",
                                        2,
                                    ]
                                },
                            }
                        },
                    ]
                )
                if number_search_required:

                    search_number_stringify_stage = {"$addFields": {}}
                    if "bundle_price" in fields_to_search:
                        search_number_stringify_stage["$addFields"][
                            "converted_bundle_price_string"
                        ] = {"$toString": "$bundle_price_in_additional_currency"}
                        searchQuery["$or"].append(
                            {
                                "converted_bundle_price_string": {
                                    "$regex": search,
                                    "$options": "i",
                                }
                            }
                        )

                    if "bundle_retail_price" in fields_to_search:
                        search_number_stringify_stage["$addFields"][
                            "converted_retail_price_string"
                        ] = {"$toString": "$bundle_retail_price_in_additional_currency"}
                        searchQuery["$or"].append(
                            {
                                "converted_retail_price_string": {
                                    "$regex": search,
                                    "$options": "i",
                                }
                            }
                        )
                    pipeline.append(search_number_stringify_stage)
                    pipeline.append(
                        {"$match": searchQuery}
                    )  # Equivalent to the find query's filter
                    if not export:
                        pipeline.append({"$skip": skip})
                        pipeline.append({"$limit": page_size})

            pipeline.append({"$project": {"currency": 0, "bundle_details": 0}})
            pipeline.append(
                {
                    "$lookup": {
                        "from": "profiles",
                        "localField": "iccid",
                        "foreignField": "iccid",
                        "as": "profile",
                    }
                }
            )

            result = mongodb.aggregate(
                "order_history",
                pipeline,
                apply_Tenancy=apply_Tenancy,
                database_name=database_name,
            )
            combinedQuery = {**getQuery, **searchQuery}
            total_orders_count = mongodb.count_documents(
                "order_history",
                combinedQuery,
                apply_Tenancy=apply_Tenancy,
                database_name=database_name,
            )
            result = list(result)
            vendors = mongodb.find(
                "vendors", {}, apply_Tenancy=0, database_name=database_name
            )
            vendors = list(vendors)

            vendors_expiry_days = {}
            if vendors:
                for vendor in vendors:
                    if vendor_name := vendor.get("vendor_name"):
                        vendors_expiry_days[vendor_name] = vendor.get(
                            "number_of_expiry_days", 365
                        )
            if not result:
                return (
                    GetOrderHistoryResponse(
                        response_code="0404",
                        developer_message="No Orders Found",
                        title="No Data",
                        total_orders_count=total_orders_count,
                        orders=[],
                    ),
                    200,
                )
            results_formatted = []

            for item in result:
                item = DataHelpers.deserializer(item)
                item["order_id"] = str(item.pop("_id", ""))
                item.pop("added_by", "")
                item["bundle_expiry_date"] = item.pop("expiry_date", None)
                filter_sensitive_fields(item, "order_history")

                # Check for related active top-ups
                has_related_active_topups = main_helper.check_related_active_topups(
                    item.get("iccid"), apply_Tenancy
                )
                if has_related_active_topups is not None:
                    item["has_related_active_topups"] = True
                else:
                    item["has_related_active_topups"] = False

                if item.get("order_status") == "Successful":
                    try:
                        profile = item.get("profile", [])[0]

                        profile_vendor_name = profile.get("vendor_name", None)
                        profile_create_date = profile.get("create_datetime", None)
                        if profile_vendor_name and profile_create_date:
                            vendor_days_till_profile_expiry = vendors_expiry_days.get(
                                profile_vendor_name, 365
                            )
                            item["profile_expiry_date"] = (
                                profile_create_date
                                + datetime.timedelta(
                                    days=vendor_days_till_profile_expiry
                                )
                            )
                    except:
                        pass

                    reseller = GetOrderHistorySuccessfulResponse.from_dict(item)
                elif item.get("order_status") == "Refunded":
                    try:
                        profile = item.get("profile", [])[0]
                        profile_vendor_name = profile.get("vendor_name", None)
                        profile_create_date = profile.get("create_datetime", None)
                        if profile_vendor_name and profile_create_date:
                            vendor_days_till_profile_expiry = vendors_expiry_days.get(
                                profile_vendor_name, 365
                            )
                            item["profile_expiry_date"] = (
                                profile_create_date
                                + datetime.timedelta(
                                    days=vendor_days_till_profile_expiry
                                )
                            )
                    except:
                        pass
                    reseller = GetOrderHistorySuccessfulResponse.from_dict(item)
                else:
                    item.pop("profile", {})
                    reseller = GetOrderHistoryFailedResponse.from_dict(item)

                results_formatted.append(reseller)

            return (
                GetOrderHistoryResponse(
                    response_code="0200",
                    developer_message="Success",
                    title="Success",
                    total_orders_count=total_orders_count,
                    orders=results_formatted,
                ),
                200,
            )

        else:
            if country_code:
                getQuery["bundle_data.country_code_list"] = [country_code]

            if date_from and date_to:

                if date_from > date_to:
                    raise ProblemException(
                        status=400, title="Error", detail="Invalid Date Inputs"
                    )

                getQuery["payment_date"] = {"$gte": date_from, "$lte": date_to}

                if date_from == date_to:
                    getQuery["payment_date"] = date_from

            elif date_from:

                getQuery["payment_date"] = {"$gte": date_from}
            elif date_to:

                getQuery["payment_date"] = {"$lte": date_to}
            reseller_id = "111111111111111111111111"
            if not export:
                if not page_size:
                    page_size = 25
                if not page_number:
                    page_number = 1
                skip = (page_number - 1) * page_size

            else:
                skip = 0
                page_size = 0

            result = join_subscriber_into_reseller(
                reseller_id=reseller_id,
                page_size=page_size,
                skip=skip,
                getQuery=getQuery,
            )

            result = list(result)
            if not result[0]["orders"]:
                total_orders_count = result[0]["total_orders_count"]
                if len(total_orders_count) > 0:
                    total_order_count = total_orders_count[0].get("count", 0)
                else:
                    total_order_count = 0

                return (
                    GetOrderHistoryResponse(
                        response_code="0404",
                        developer_message="No Orders Found",
                        title="No Data",
                        total_orders_count=total_order_count,
                        orders=[],
                    ),
                    200,
                )
            results_formatted = []
            total_orders_count = result[0]["total_orders_count"]
            total_order_count = total_orders_count[0]["count"]

            for item in result[0]["orders"]:
                item = DataHelpers.deserializer(item)
                filter_sensitive_fields(item, "order_history")
                results_formatted.append(item)

            return (
                GetOrderHistoryResponse(
                    response_code="0200",
                    developer_message="Success",
                    title="Success",
                    total_orders_count=total_order_count,
                    orders=results_formatted,
                ),
                200,
            )
    except pymongo.errors.PyMongoError as e:
        logger.exception(f"Exception in get order history as pymongo error, {str(e)}")

        return (
            GetOrderHistoryResponse(
                response_code="0404",
                developer_message="No Orders Found",
                title="No Data",
                orders=[],
            ),
            200,
        )
    except Exception as e:
        logger.exception(f"Exception in get order history, {str(e)}")
        return (
            GetOrderHistoryResponse(
                response_code="0404",
                developer_message="No Orders Found",
                title="No Data",
                orders=[],
            ),
            200,
        )

import pymongo.errors
from swagger_server.models.topup_balance_request import TopupBalanceRequest  # noqa: E501
from swagger_server.models.topup_balance_response import TopupBalanceResponse  # noqa: E501
from connexion.exceptions import ProblemException
from app_helpers import mongodb
from app_helpers.helpers import TenancyHelpers
from bson import ObjectId
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from instance import consumer_config
import logging

logger = logging.getLogger(__name__)

def edit_transaction(transaction_id, topupRequest: TopupBalanceRequest = None, reseller_id=None, branch_id=None, currency_code=None):
    """

    :param reseller_id:
    :param topupRequest:
    :return:
    """

    try:
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id, branch_id)
        reseller = get_reseller_by_id(reseller_id)
        logger.info("Trying to edit transaction transaction id : %s, with topup request : %s, reseller id : %s, branch id :%s", transaction_id, topupRequest, reseller_id, branch_id)
        database_name = reseller[0].tenant_name
        if database_name is None:
            database_name = consumer_config.decrypted_db_name_alias
        filterQuery = {"_id": ObjectId(transaction_id)}
        if apply_Tenancy == 0:
            if branch_id:
                filterQuery["branch_id"] = ObjectId(branch_id)
            filterQuery["reseller_id"] = ObjectId(reseller_id)
        else:
            raise ProblemException(status=403, title="Error", detail="You are not authorized to use this API")

        old_transaction = mongodb.find_one("transaction_history", filterQuery, apply_Tenancy=0, database_name=database_name)
        if not old_transaction:
            raise ProblemException(status=404, title="Error", detail="Transaction Not Found!")

        if old_transaction.get("type") != "Topup":
            raise ProblemException(status=404, title="Error", detail="You can Only Edit Topup Transactions")

        old_amount = old_transaction.get("amount")

        topup_amount = topupRequest.topup_amount
        if currency_code:
            currency = mongodb.find_one(
                "currency_codes", {"currency_code": currency_code, "is_available": True}, apply_Tenancy=0, database_name=database_name
            )
            if not currency:
                raise ProblemException(status=400, title="Error", detail="Currency Not Found!")
            currency_rate = currency.get("currency_rate")

            topup_amount = topup_amount / currency_rate
        new_amount_increment = -old_amount + topup_amount

        # update reseller wallet:

        topup_balance_query = {"$inc": {"balance": new_amount_increment}}

        update_wallet = mongodb.update_one(
            "reseller",
            {"_id": ObjectId(reseller_id), "balance": {"$gte": -new_amount_increment}},
            topup_balance_query,
            apply_Tenancy=0,
            database_name=database_name,
        )

        if update_wallet.matched_count == 0:
            raise ProblemException(status=404, title="Error", detail="Reseller balance cannot become Negative!")

        topup_transaction_query = {"$inc": {"amount": new_amount_increment}}
        transaction_response = mongodb.update_one(
            "transaction_history", filterQuery, topup_transaction_query, apply_Tenancy=0, database_name=database_name
        )

        response = TopupBalanceResponse(message="Edit Top Up Balance Successful")
        return response, 201

    except pymongo.errors.PyMongoError as e:
        logger.error("Error editing transaction : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

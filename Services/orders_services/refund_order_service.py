import datetime
from swagger_server.models.refund_order_request import RefundOrderRequest
from swagger_server.models.refund_order_response import RefundOrderResponse
from Services.branch_services.get_branch_by_id_service import get_branch_by_id
from app_helpers import mongodb
from bson import ObjectId
from connexion import ProblemException
from app_helpers.helpers import TenancyHelpers
from Services.orders_services.get_bundle_consumption_service import get_bundle_consumption


def refund_order(refund_request: RefundOrderRequest = None, reseller_id=None):
    try:

        branch = False
        branch_limit = None

        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)

        order_id = refund_request.order_id
        refund_reason = refund_request.refund_reason
        getQuery = {"_id": ObjectId(order_id)}

        if apply_Tenancy == 0:

            getQuery["reseller_id"] = ObjectId(reseller_id)

        # Checking if the order exists and is successful.
        order_history = mongodb.find_one("order_history", getQuery, apply_Tenancy=apply_Tenancy)
        if not order_history:
            raise ProblemException(status=404, title="Error", detail="Order Not Found!")
        else:
            order_status = order_history.get("order_status")
            if order_status == "Refunded":
                raise ProblemException(status=400, title="Error", detail="Order Already Refunded!")
            elif order_status == "Failed":
                raise ProblemException(status=400, title="Error", detail="Cannot Refund a Failed Order!")

            if order_history.get("plan_status", "") == "Expired":
                raise ProblemException(status=400, title="Error", detail="Cannot Refund an expired bundle! Contact Administration!")

            branch_id = order_history.get("branch_id", None)
            if branch_id:
                branch = get_branch_by_id(branch_id, reseller_id)[0]
                if branch:
                    branch_limit = branch.limit
                    branch_limit_consumption = branch.limit_consumption

            # Checking if the order is made less than 60 days ago.
            date_now = datetime.datetime.utcnow()
            date_difference = date_now - order_history.get("date_created")
            if date_difference.days > 60:
                raise ProblemException(status=400, title="Error", detail="Cannot Refund an order older than 60 days!")

            if apply_Tenancy != 0:
                refund_accepted = False

                try:
                    consumption = get_bundle_consumption(order_id=order_id)
                    plan_status = consumption.plan_status
                    if plan_status == "Plan Not Started":
                        refund_accepted = True

                except:
                    refund_accepted = False
                if not refund_accepted:
                    raise ProblemException(status=400, title="Error", detail="Cannot Refund a started bundle! Contact Administration!")

            # returning balance to client.
            print("returning balance to client")
            subscriber_price = order_history.get("bundle_price")
            return_balance_query = {"$inc": {"balance": subscriber_price}}

            # Activate below if branch balance is added
            # if branch:
            #     return_balance_result = mongodb.update_one("branch", {"_id": ObjectId(branch_id)}, return_balance_query,
            #                                                  apply_Tenancy=0)
            # else:
            #     return_balance_result = mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)}, return_balance_query,
            #                                                  apply_Tenancy=0)

            return_balance_result = mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)}, return_balance_query, apply_Tenancy=0)

            if return_balance_result.matched_count == 0:
                raise ProblemException(status=404, title="Error", detail="Reseller Not Found!")

            # creating a transaction
            transaction_history = {
                "type": "Refund",
                "refund_reason": refund_reason,
                "amount": subscriber_price,
                "order_id": ObjectId(order_id),
                "reseller_id": ObjectId(reseller_id),
            }

            if branch_id:
                transaction_history["branch_id"] = ObjectId(branch_id)
            if refund_reason:
                transaction_history["refund_reason"] = refund_reason

            transaction_response = mongodb.insert_one("transaction_history", transaction_history, apply_Tenancy=apply_Tenancy)

            # returning limit to client.

            if branch_limit is not None:
                if subscriber_price > branch_limit_consumption:
                    subscriber_price = branch_limit_consumption
                limit_consumption_update = {"$inc": {"limit_consumption": 0 - subscriber_price}}
                limit_consumption_update_result = mongodb.update_one(
                    "branch", {"_id": ObjectId(branch_id)}, limit_consumption_update, apply_Tenancy=0
                )
            updateQuery = {"$set": {"order_status": "Refunded"}}
            if refund_reason:
                updateQuery["$set"]["refund_reason"] = refund_reason
            update_order = mongodb.update_one("order_history", getQuery, updateQuery, apply_Tenancy=0)

            return RefundOrderResponse(message="Order Refunded Successfully.")

    except ProblemException as e:
        raise e
    except Exception as e:
        print(str(e))
        raise ProblemException(status=500, title="Error", detail="Cound Not Resend Email!")

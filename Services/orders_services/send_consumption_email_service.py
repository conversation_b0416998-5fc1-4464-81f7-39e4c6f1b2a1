import base64
import datetime
import json

from Services.email_services.send_email import send_email
from app_models.reseller_models import NotificationHistory
from swagger_server.models.orders_send_consumption_email_body import (
    OrdersSendConsumptionEmailBody,
)
from instance import consumer_config
from app_helpers import mongodb
from bson import ObjectId
from connexion import ProblemException
from app_helpers.helpers import TenancyHelpers
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from app_helpers import helpers
from app_helpers.encrypt_helper import Crypt

import logging

logger = logging.getLogger(__name__)


def send_consumption_email(
    notification_type=None,
    request: OrdersSendConsumptionEmailBody = None,
    reseller_id=None,
):
    try:
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        order_id = request.order_id
        getQuery = {"_id": ObjectId(order_id), "order_status": "Successful"}
        logger.info(f"Starting send_consumption_email for reseller_id: {reseller_id}, order_id: {order_id}")

        if apply_Tenancy == 0:

            getQuery["reseller_id"] = ObjectId(reseller_id)

        order_history = mongodb.find_one("order_history", getQuery, apply_Tenancy=apply_Tenancy)
        if not order_history:
            logger.error(f"Order not found for order_id: {order_id}")
            raise ProblemException(status=404, title="Error", detail="Order Not Found!")
        else:
            try:
                logger.info(f"Order found for order_id: {order_id}, proceeding with email preparation")

                TEMPLATE_FILES = {
                    # "Hundred": "./Services/email_services/mail_templates/data_consumption.html",
                    # "Fifty": "./Services/email_services/mail_templates/data_consumption.html",
                    "Eighty": "./Services/email_services/mail_templates/data_consumption.html",
                    "Activated": "./Services/email_services/mail_templates/plan_started.html",
                    "Expired": "./Services/email_services/mail_templates/plan_expired.html",
                }

                event_type = None
                if notification_type in TEMPLATE_FILES:
                    reseller = get_reseller_by_id(ObjectId(reseller_id))[0]
                    reseller = helpers.DataHelpers.to_good_dict(reseller)
                    custom_email_template_qr = reseller.get("custom_email_template_qr", False)
                    custom_email_template_data = reseller.get("custom_email_template_data", False)
                    custom_email_template_expired = reseller.get("custom_email_template_expired", False)
                    request_custom_email = reseller.get("request_custom_email", False)
                    email_settings = reseller.get("email_settings")
                    if notification_type == "Eighty":
                        custom_email_template = custom_email_template_data
                        event_type = "Eighty"
                    elif notification_type == "Expired":
                        custom_email_template = custom_email_template_expired
                        event_type = "Expired"
                    else:
                        custom_email_template = False
                        event_type = "Started"
                    if request_custom_email and custom_email_template and email_settings:
                        binary_data = base64.b64decode(custom_email_template)
                        template = binary_data.decode("utf-8")
                    else:
                        with open(TEMPLATE_FILES[notification_type], "r") as file:
                            template = file.read()

                else:
                    logger.error(f"Template for notification_type '{notification_type}' not found")
                    raise ProblemException(
                        500,
                        title="Internal Server Error",
                        detail="Email Template Not Found",
                    )

                # start pre-notify reseller for consumption notification
                payload = {
                    "event_date": datetime.datetime.utcnow().isoformat(),
                    "event_type": event_type,
                    "iccid": order_history.get("iccid"),
                    "order_id": order_id
                }

                # get reseller by id
                reseller = get_reseller_by_id(reseller_id)[0]
                reseller = helpers.DataHelpers.to_good_dict(reseller)
                # no need to continue in case notification type is not webhook
                if reseller.get("notification_type") != "webhook":
                    logger.debug("Reseller %s will not be notified with new updated bundles", reseller_id)
                    return
                # no need to continue in case webhook url not found
                if not reseller.get("consumption_url"):
                    logger.debug("Reseller %s will not be notified due webhook url not found", reseller_id)
                    return

                history = NotificationHistory(
                    reseller_id=str(reseller_id),
                    iccid = order_history.get("iccid"),
                    order_id=order_id,
                    notification_type=reseller.get("notification_type"),
                    notified_on=event_type,
                    request_payload=json.dumps(payload),
                    status=False
                )
                history.save()

            except FileNotFoundError:
                raise ProblemException(
                    500,
                    title="Internal Server Error",
                    detail="Email Template Not Found",
                )

            except:
                fail_user = True

                if fail_user:
                    raise ProblemException(
                        500,
                        title="Internal Server Error",
                        detail="Issue encountered when building user email",
                    )
            try:

                image_exists = reseller.get("image", False)

                if email_settings:
                    email_username = email_settings.get("username", "")
                    email_encypted_password = email_settings.get("password", "")
                    str_key = consumer_config.password_key
                    encrypt_helper = Crypt()
                    decrypted_password = encrypt_helper.decrypt(email_encypted_password, str_key)
                    email_password = decrypted_password
                    smtp_port = email_settings.get("smtp_port", "")
                    smtp_server = email_settings.get("smtp_server", "")
                    if image_exists and consumer_config.ENVIRONMENT == "production":
                        logo_uri = (
                            "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/"
                            + str(reseller_id)
                            + "."
                            + reseller.get("image_type", "png")
                        )
                    else:
                        logo_uri = "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/monty-esim-logo.png"

                else:
                    email_username = consumer_config.email_username
                    email_password = consumer_config.email_password
                    smtp_port = consumer_config.smtp_port
                    smtp_server = consumer_config.smtp_server
                    logo_uri = "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/monty-esim-logo.png"

                client_name = order_history.get("client_name", "Customer")
                if not client_name:
                    client_name = "Customer"

                Body = (
                    template.replace("{{ USERNAME }}", client_name)
                    .replace("{{ DATA_USAGE }}", notification_type)
                    .replace(
                        "{{ BUNDLE_NAME }}",
                        order_history.get("bundle_marketing_name", ""),
                    )
                    .replace("{{ logo_uri }}", logo_uri)
                )
                email_settings = {
                    "username": email_username,
                    "password": email_password,
                    "smtp_port": smtp_port,
                    "smtp_server": smtp_server,
                }

                email_sent, message = send_email(
                    email_settings=email_settings,
                    to_user=order_history.get("client_email", ""),
                    subject="eSIM Reseller",
                    body=Body,
                    has_attchament=True,
                )

                if email_sent == False:
                    logger.error(f"Failed to send email to {order_history.get('client_email')}")
                    raise ProblemException(
                        status=500,
                        title="Error",
                        detail="Authentication unsuccessful, Couldn't send an email!",
                    )
                else:
                    logger.info(f"Email sent successfully to {order_history.get('client_email')}")
                    return "Email Sent Successfully!"
            except Exception as e:
                logger.error("Error consuming email : %s", str(e))
                raise ProblemException(status=500, title="Error", detail="Couldn't send an email!")

    except ProblemException as e:
        raise e
    except Exception as e:
        logger.error("Error consuming email : %s " , str(e))
        raise ProblemException(status=500, title="Error", detail="Couldn't send an email!")

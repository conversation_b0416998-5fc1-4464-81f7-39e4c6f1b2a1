from app_helpers.helpers import TenancyHelpers
from app_helpers import mongodb, helpers
from bson import ObjectId
from connexion import ProblemException
from instance import consumer_config as instance_config
from app_helpers.encrypt_helper import Crypt
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id

def get_promocode_dashboard(promocode=None, date_from=None, date_to=None, reseller_id=None, branch_id=None,affiliate_program = None):
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id, branch_id)

    reseller = get_reseller_by_id(reseller_id)[0]
    reseller = helpers.DataHelpers.to_good_dict(reseller)
    database_name = reseller.get('tenant_name')
    if database_name is None:
        database_name = instance_config.decrypted_db_name_alias
    supports_promo = reseller.get("supports_promo", False)
    if not supports_promo:
        raise ProblemException(status=403, title="Error", detail="Your Reseller does not support Promo codes!")
    lookup_query = [{
                            '$and': [
                                {
                                    '$eq': [
                                        '$promo_code', '$$redeem_code'
                                    ]
                                }, {
                                    '$or': [
                                        {
                                            '$eq': [
                                                '$payment_status', True
                                            ]
                                        }, {
                                            '$eq': [
                                                '$payment_topup', True
                                            ]
                                        }
                                    ]
                                }
                            ]

}
    ]

    if date_from and date_to:
        if date_from > date_to:
            raise ProblemException(status=400, title="Error", detail="Invalid Date Inputs")

    if date_from:
        lookup_query.append({
            '$gte': [
                '$datetime', date_from
            ]
        })

    if date_to:
        lookup_query.append({
            '$lte': [
                '$datetime', date_to
            ]
        })

    pipeline = [
        {
            '$match': {
                '$and': [
                    {
                        'reseller_id': ObjectId(reseller_id)
                    }, {}
                ]
            }
        }, {
            '$lookup': {
                'from': 'user_bundle_log',
                'let': {
                    'redeem_code': '$redeem_code'
                },
                'pipeline': [
                    {
                        '$match': {
                            '$expr': {
                                '$and': lookup_query
                            }
                        }
                    }
                ],
                'as': 'user_bundle_log'
            }
        }, {
            '$unwind': '$user_bundle_log'
        }, {
            '$group': {
                '_id': {
                    'date': {
                        '$dateToString': {
                            'format': '%Y-%m-%d',
                            'date': '$user_bundle_log.datetime'
                        }
                    },
                    'conversion_rate': '$commission_rate'
                },
                'total_sales_volume': {
                    '$sum': '$user_bundle_log.amount'
                },
                'bundles_sold': {
                    '$sum': 1
                }
            }
        }, {
            '$addFields': {
                'commission_amount': {
                    '$multiply': [
                        '$total_sales_volume', '$_id.conversion_rate', 0.01
                    ]
                }
            }
        }, {
            '$group': {
                '_id': {
                    'date': '$_id.date'
                },
                'total_sales_volume': {
                    '$sum': '$total_sales_volume'
                },
                'bundles_sold': {
                    '$sum': '$bundles_sold'
                },
                'commission_amount': {
                    '$sum': '$commission_amount'
                }
            },
        }, {
            '$sort': {
                '_id.date': 1
            }
        }, {
            '$group': {
                '_id': None,
                'sales_per_day': {
                    '$push': {
                        'date': '$_id.date',
                        'total_sales_volume': '$total_sales_volume',
                        'commission_amount': '$commission_amount',
                        'bundles_sold': '$bundles_sold'
                    }
                }
            }
        }, {
            '$project': {
                '_id': 0,
                'sales_per_day': 1
            }
        }
    ]

    if promocode:
        str_key = instance_config.promo_code_key
        encrypt_helper = Crypt()
        encrypted_promo_code = encrypt_helper.encrypt(promocode, str_key)
        promo_query = {"redeem_code": encrypted_promo_code}
        pipeline[0]["$match"]["$and"].append(promo_query)
    if affiliate_program:
        affiliate_query = {"affiliate_program": affiliate_program}
        pipeline[0]["$match"]["$and"].append(affiliate_query)

    results = mongodb.aggregate("redeem_codes", pipeline, apply_Tenancy=0, database_name= database_name)
    results = list(results)
    if results:
        results = results[0]
    return results

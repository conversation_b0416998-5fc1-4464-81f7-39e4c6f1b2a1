import datetime
from uuid import uuid4

from app_helpers import mongodb
from connexion.exceptions import ProblemException
from app_helpers.helpers import DataHelpers, FileHelpers, TenancyHelpers
import pymongo.errors
from swagger_server.models.add_issue_report_response import AddIssueReportResponse
from swagger_server.models.add_issue_report_request import AddIssueReportRequest
from instance import consumer_config
from Services.agent_services.get_agent_by_id_service import get_agent_by_id
from app_helpers.email_helper import reseller_issue_report_message, support_report_issued_message
from Services.email_services.send_email import send_email
import threading

from bson import ObjectId
import connexion
import logging

logger = logging.getLogger(__name__)

def thread_to_send_email(email, subject, message):
    logger.info("Thread to send email : %s " , email)
    email_username = consumer_config.email_username
    email_password = consumer_config.email_password
    smtp_port = consumer_config.smtp_port
    smtp_server = consumer_config.smtp_server
    logo_uri = "https://montyesim-logos.s3.eu-west-1.amazonaws.com/logos/monty-esim-logo.png"

    email_settings = {"username": email_username,
                      "password": email_password,
                      "smtp_port": smtp_port,
                      "smtp_server": smtp_server,
                      }

    email_sending = send_email(email_settings=email_settings, to_user=email, subject=subject, body=message)

def add_issue_report(issue: AddIssueReportRequest = None, reseller_id=None):
    try:
        logger.info("Trying to add issue report with request : %s", issue)
        '''
        Creates an issue request 
        '''

        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id, None)
        if apply_Tenancy == 0:
            raise ProblemException(status=401, title="Error", detail="You cannot Submit an Issue instead of a customer.")
        user_id = connexion.context.get("token_info", {}).get('test_key', {}).get("user_id", "")
        user = get_agent_by_id(user_id)
        user = user[0]
        users_to_be_notified = [{"name": user.name.title(),
                                 "email": user.email,
                                 "user_id": ObjectId(user_id),
                                 "role": "Requester"
                                 },
                                {"name": "Support",
                                 "email": consumer_config.support_email,
                                 "role": "Support"
                                 }]

        # Reseller EMAIL CAN BE USED AS WELL

        issue_report_dict = DataHelpers.to_good_dict(issue)
        if apply_Tenancy == 0:
            issue_report_dict["reseller_id"] = ObjectId(reseller_id)
        attachements = issue_report_dict.get("attachments",[]) if issue_report_dict.get("attachments",[]) else []
        chat_list = []
        image_error = False
        description = issue.issue_description

        for attachement in attachements:
            # todo store in database and retrieve id.
            try:
                if not attachement.startswith('data'):
                    raise ProblemException(status=400,title="Error",detail="Files must be base64 format!")
                image_tuple = attachement.split(";base64,")
                image_extension = image_tuple[0] + ";base64,"
                image_type = image_tuple[0].split('/',1)[1]



            except Exception as e:
                logger.error("Exception at splitting image in add issue report service : %s", str(e))
                image_error = True


            if attachement and not image_error:
                try:
                    file_name = str(uuid4()) + "." + image_type

                    # todo change logos_directory to issues directory
                    file_path = consumer_config.logos_directory + file_name


                    file_size = FileHelpers.save_base64_image(image_tuple[1], file_path)
                    file_dict = {"attachement": consumer_config.bucket_url + file_name,
                                 "date_created": datetime.datetime.utcnow(),
                                 "added_by": user_id,
                                 "added_by_name":user.name.title(),
                                 "added_by_type":"Requester",
                                 "file_size":file_size}
                    chat_list.append(file_dict)
                except Exception as e:
                    logger.error("Exception at save_base64_image :%s" , str(e))
                    image_error = True
        if chat_list and description:
            chat_list[0]['message'] = description
        elif description:
            description_dict = {"message": description,
                                "date_created": datetime.datetime.utcnow(),
                                "added_by": user_id,
                                "added_by_name": user.name.title(),
                                "added_by_type": "Requester"
                                }
            chat_list.append(description_dict)

        issue_report_dict["last_modified"]= datetime.datetime.utcnow()
        issue_report_dict["chat"] = chat_list
        issue_report_dict["status"] = "Open"
        issue_report_dict.pop('attachments', '')
        issue_report_dict["requester"] = user.name.title()
        issue_report_dict["assignee"] = "Monty eSIM Support"
        issue_report_dict["users_to_be_notified"] = users_to_be_notified
        inserted_issue = mongodb.insert_one("IssueReports",
                                            issue_report_dict,
                                            apply_Tenancy=apply_Tenancy)
        issue_id = str(inserted_issue.inserted_id)
        subject = issue.issue_type +" " + issue_id
        issue_tracking_link = consumer_config.front_end_url + "Support?issue_id=" + issue_id
        requester_thread = threading.Thread(
            target=thread_to_send_email, args=(
                user.email, subject,
                reseller_issue_report_message.format(user.name.title(), issue_id, issue_tracking_link))
        )
        requester_thread.start()
        support_thread = threading.Thread(
            target=thread_to_send_email, args=(
                consumer_config.support_email, subject,
                support_report_issued_message.format(user.name.title(), issue_id, issue_tracking_link))
        )
        support_thread.start()

    except pymongo.errors.DuplicateKeyError:
        raise ProblemException(status=400, title="Error", detail="Issue already Exists")
    except pymongo.errors.PyMongoError as e:
        logger.error("Error adding issue report : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

    return AddIssueReportResponse(message="Issue Created Successfully",
                                  report_id=str(inserted_issue.inserted_id)), 201

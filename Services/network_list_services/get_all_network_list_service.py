import connexion
from bson import ObjectId

from app_helpers import mongodb
from connexion.exceptions import ProblemException
from app_helpers.helpers import TenancyHelpers
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from swagger_server.models.network_list import NetworkList
import logging

logger = logging.getLogger(__name__)

def get_network_list(page_size=None, page_number=None, export=None):
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(
        reseller_id=None
    )
    logger.info( "reseller_id : %s", reseller_id)
    reseller = get_reseller_by_id(reseller_id)
    logger.info("reseller: %s", reseller)
    if reseller[0].reseller_category == "Internal":
        if not export:
            if not page_size:
                page_size = 25
            if not page_number:
                page_number = 1
            skip = (page_number - 1) * page_size
        else:
            skip = 0
            page_size = 0

        pipeline = []

        if not export:
            pipeline.append({"$skip": skip})
            pipeline.append({"$limit": page_size})
        pipeline.append(
            {
                "$lookup": {
                    "from": "vendors",
                    "localField": "vendor_name",
                    "foreignField": "vendor_name",
                    "as": "result",
                }
            }
        )
        pipeline.append({"$unwind": {"path": "$result"}})
        pipeline.append({"$addFields": {"network_id": {"$toString": "$_id"}}})
        pipeline.append({"$addFields": {"vendor_name": "$result.vendor_code"}})
        pipeline.append({"$project": {"_id": 0, "result": 0}})
        networks = mongodb.aggregate("network_list", pipeline, apply_Tenancy=0)
        tota.l_networks_count = mongodb.count_documents(
            "network_list", {}, apply_Tenancy=0
        )
        if networks:
            networks = list(networks)
            return NetworkList(
                response_code="200",
                networks=networks,
                networks_count=total_networks_count,
            )
        else:
            return NetworkList(
                response_code="404",
                networks=[],
                developer_message="No Other networks Available",
                title="No other networks Available",
                networks_count=0,
            )

    else:
        raise ProblemException(
            status=401,
            title="Error",
            detail="Invalid Reseller! You are not Authorized to do this!",
        )

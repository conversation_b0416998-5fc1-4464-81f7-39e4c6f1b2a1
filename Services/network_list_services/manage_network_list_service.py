import datetime

from connexion.exceptions import ProblemException
from app_helpers import mongodb
from swagger_server.models.manage_network_list_response import ManageNetworkListResponse
from swagger_server.models.manage_network_list_request import ManageNetworkListRequest
import connexion
import logging

logger = logging.getLogger(__name__)
def manage_network_list(request: ManageNetworkListRequest):
    try:
        permission_level = connexion.context.get("token_info", {}).get('test_key', {}).get("permission_level", "")
        if permission_level != 1:
            raise ProblemException(status=403, title="Error", detail="You are not Authorized to use this API")
        one_Failure = False
        one_success = False
        response_code = 200

        filter_query = {}

        invalid_network_lists = []
        if request.networks:

            for item in request.networks:
                vendor_name = item.vendor_name
                country_code = item.country_code
                operator_list = item.operator_list
                is_shown = item.is_shown

                new_values = {"$set": {}}
                filter_query["country_code"] = country_code
                filter_query["vendor_name"] = vendor_name

                countries = mongodb.distinct("countries","iso3_code")
                vendors = mongodb.distinct("vendors",'vendor_name')
                if (country_code not in countries) or (vendor_name not in vendors):
                    invalid_network_lists.append({"country_code": country_code,"vendor_name":  vendor_name})
                    one_Failure = True
                    continue

                if is_shown is not None:
                    new_values["$set"]['is_shown'] = is_shown

                if operator_list is not None:
                    new_values["$set"]['operator_list'] = operator_list
                else:
                    invalid_network_lists.append({"country_code": country_code,"vendor_name":  vendor_name})
                    one_Failure = True
                    continue


                if operator_list is not None:
                    new_values["$set"]["last_modified"] = datetime.datetime.utcnow()
                    update_result = mongodb.update_one("network_list", filter_query, new_values,
                                       apply_Tenancy=0,upsert = True)

                    one_success = True

        if one_success:
            message = "Networks Edited Successfully."
            response_code = 200
            if invalid_network_lists:
                message = "Some " + message + " However, errors occurred."

                if invalid_network_lists:
                    message = message + " Some Networks were invalid!"
                    response_code = 207

        elif one_Failure:
            message = "Could not update Networks! Errors Occured."
            if invalid_network_lists:
                message = message + " Some Networks were invalid!"

            response_code = 400

        else:
            message = "No Changes Were Made"
        return ManageNetworkListResponse(detail=message, message=message,invalid_networks=invalid_network_lists), response_code
    except ProblemException as e:
        raise e
    except Exception as e:
        logger.error("Error managing network list : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")



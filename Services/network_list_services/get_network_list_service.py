import connexion
from bson import ObjectId

from app_helpers import mongodb
from connexion.exceptions import ProblemException
from swagger_server.models.network_list import NetworkList

import logging

logger = logging.getLogger(__name__)
def get_network_list(network_id=None, country_code= None,page_size=None, page_number=None, export=None):
    permission_level = connexion.context.get("token_info", {}).get('test_key', {}).get("permission_level", "")
    getQuery = {}

    if not export:
        if not page_size:
            page_size = 25
        if not page_number:
            page_number = 1
        skip = (page_number - 1) * page_size
    else:
        skip = 0
        page_size = 0

    if network_id:
        getQuery["_id"] = ObjectId(network_id)
    if country_code:
        getQuery["country_code"] = country_code
    pipeline = [
        {"$match": getQuery}
    ]

    if not export:
        pipeline.append({"$skip": skip})
        pipeline.append({"$limit": page_size})
    pipeline.append({
        '$addFields': {
            'network_id': {
                '$toString': '$_id'
            }        }
    })
    pipeline.append({'$project':{'_id':0}})
    logger.info("pipeline : %s", pipeline)
    networks = mongodb.aggregate("network_list", pipeline, apply_Tenancy=0)
    total_networks_count = mongodb.count_documents("network_list", getQuery, apply_Tenancy=0)
    if networks:
        networks = list(networks)

        return NetworkList(response_code="200", networks=networks, networks_count=total_networks_count)
    else:
        return NetworkList(response_code="404", networks=[],
                                     developer_message="No Other Currencies Available",
                                     title="No other Currencies Available",networks_count=0)





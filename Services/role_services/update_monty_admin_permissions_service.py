import app_helpers.helpers
from app_helpers import mongodb
from connexion.exceptions import ProblemException
from app_helpers.helpers import FileHelpers
import pymongo.errors
from swagger_server.models.update_admin_permissions_response import UpdateAdminPermissionsResponse

import json
import logging

logger = logging.getLogger(__name__)

def update_monty_admin_permissions():
    try:
        '''
        Creates a custom role with different permissions.
        '''

        try:
            apis = json.loads(FileHelpers.read_file('./missing_permissions.json')).get("Reseller")

        except:
            raise ProblemException(status=404, title="Error", detail="No Apis Found")
        pass
        permissions = []

        for api in apis:
            permissions.append({"api_name": api,
                                "permission_type": "Placeholder"})
        role = {"$set": {"name": "MontyAdmin",
                         "access_level": "sensitive",
                         "description": "Monty Admins, have full authority",
                         "permissions": permissions}}
        print(role)
        result = mongodb.update_one("roles", {"name": "<PERSON><PERSON><PERSON><PERSON>"}, role, apply_Tenancy=0, upsert=True)
        role_id = str(result.upserted_id)
        if result.upserted_id is None:
            role_id = str(mongodb.find_one("roles", {"name": "MontyAdmin"}, apply_Tenancy=0).pop('_id', ''))

    except pymongo.errors.DuplicateKeyError as e:
        logger.error("Error updating admin permissions , %s", e)
        raise ProblemException(status=400, title="Error", detail="Role Name already Exists!")
    except pymongo.errors.PyMongoError as e:
        logger.error("Error updating admin permissions , %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

    return UpdateAdminPermissionsResponse(message="Monty Admin Role Updated Successfully",
                                          role_id=role_id), 201

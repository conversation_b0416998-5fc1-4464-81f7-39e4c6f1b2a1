import connexion
import pymongo.errors
from swagger_server.models.edit_role_request import EditRoleRequest  # noqa: E501
from swagger_server.models.edit_role_response import EditRoleResponse  # noqa: E501
from Services.role_services.get_role_by_name_service import get_role_by_name
from Services.role_services.get_role_by_id_service import get_role_by_id
from connexion.exceptions import ProblemException
from app_helpers import mongodb
from app_helpers.helpers import DataHelpers
from bson import ObjectId
import logging

logger = logging.getLogger(__name__)

def edit_role(role_id, roleRequest: EditRoleRequest = None):
    '''

    :param role_id:
    :param roleRequest:
    :return:
    '''

    try:


        logger.info("Edit role : %s, request: %s", role_id, roleRequest)
        role_dict = DataHelpers.to_good_dict(roleRequest)
        role_permissions = role_dict.get("permissions")
        role_api_names = [d['api_name'] for d in role_permissions]
        user_role_id = connexion.context.get("token_info", {}).get('test_key', {}).get("role_id","")
        user_role = get_role_by_id(user_role_id)[0]
        user_permission_level = connexion.context.get("token_info", {}).get('test_key', {}).get("permission_level", 5)
        if role_dict.get("permission_level"):
            if role_dict.get("permission_level") < user_permission_level:
                raise ProblemException(status=401, title="Error", detail="Cannot Edit Permission Level Above then Yours.")
        user_permissions = DataHelpers.to_good_dict(user_role).get("permissions")
        user_api_names = [d['api_name'] for d in user_permissions]
        not_allowed_permissions = list(set(role_api_names).difference(set(user_api_names)))
        if not_allowed_permissions:
            raise ProblemException(status=401, title="Error", detail="You are Unauthorized to add the following Permissions!",ext={"unauthorized_permissions":not_allowed_permissions})






        filterQuery = {"_id": ObjectId(role_id)}
        roleRequest = DataHelpers.serializer(roleRequest)
        roleRequestDict = DataHelpers.to_good_dict(roleRequest)
        roleRequestDict = DataHelpers.dictCleaner(roleRequestDict)

        editRoleResult = mongodb.update_one("roles", filterQuery, {"$set": roleRequestDict},
                                            apply_Tenancy=0)
        if editRoleResult.matched_count == 0:
            raise ProblemException(status=404, title="Error", detail="Role Not Found")

        response = EditRoleResponse(message="Role Edited Successfully")
        return response, 201
    except pymongo.errors.DuplicateKeyError as e:
        logger.error("Error editing role , %s", e)
        raise ProblemException(status=400, title="Error", detail="Role Name already Exists")

    except pymongo.errors.PyMongoError:

        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

import connexion
import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.get_role_response import GetRoleResponse

from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers
import logging

logger = logging.getLogger(__name__)

def get_all_roles():

    getQuery = {}
    try:
        result = list(mongodb.find("roles", getQuery, apply_Tenancy=0))
        if not result:
            raise ProblemException(status=404, title="Error", detail="Roles Not Found!")
        results_formatted = []

        for item in result:
            item = DataHelpers.deserializer(item)
            item["role_id"] = str(item.pop('_id', ''))
            item.pop("added_by", "")
            response = GetRoleResponse.from_dict(item)
            results_formatted.append(response)

        return results_formatted, 200
    except pymongo.errors.PyMongoError as e:
        logger.error("Error getting all roles : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")






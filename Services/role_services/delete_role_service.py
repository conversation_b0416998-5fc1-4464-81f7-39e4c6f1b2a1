from app_helpers import mongodb
from bson import ObjectId
from swagger_server.models.delete_role_response import DeleteRoleResponse
from connexion.exceptions import ProblemException
import logging

logger = logging.getLogger(__name__)

def delete_role(role_id):
    '''
    deletes a role in a certain tenant.
    WARNING: DO NOT DELETE A ROLE THAT IS ASSIGNED TO USERS. A USAGE CHECK NEEDS TO BE ADDED BEFORE ITS DONE.
    '''
    logger.info("Trying to delete role , with id : %s", role_id)
    users_with_role = mongodb.find_one("agent",
                                       {"role_id": ObjectId(role_id)},
                                       apply_Tenancy=0)
    if users_with_role:
        raise ProblemException(status=400, title="Error", detail="Role In use by agents! cannot delete!")
    deleted_r = mongodb.delete_one("roles",
                                   {"_id": ObjectId(role_id)},
                                   apply_Tenancy=0)
    if deleted_r.deleted_count:
        return DeleteRoleResponse(message="Role deleted successfully."), 200
    else:
        raise ProblemException(status=404, title="Not found", detail="Role to be deleted not found")

from Services.email_services.send_email import send_email
from connexion.exceptions import ProblemException
from swagger_server.models.forgot_password_response import ForgotPasswordResponse
from instance import consumer_config
import logging
logger = logging.getLogger(__name__)

def send_reset_password(body=None):
    requests_to_send = body
    sent_user = True

    for single_request in requests_to_send:
        to_user = single_request.get('to_user', '')
        user_name = single_request.get('user_name', '')
        otp = single_request.get('otp', '')

        # Send email to user
        fail_user = False
        try:
            with open("./Services/email_services/mail_templates/reset-password.html", 'r') as file:
                template = file.read()
                Subject, Body = template.split("{SubjectLineBreak}")
                Body = Body.replace("{USER-FULL-NAME}", user_name)
                Body = Body.replace("{OTP}", otp)

                file.close()
        except FileNotFoundError:
            logger.error("File not found")
            raise ProblemException(500, title="Internal Server Error", detail="Email Template Not Found")
        except:
            fail_user = True

        if fail_user:
            logger.error("Error sending reset password")
            raise ProblemException(500, title="Internal Server Error",
                                   detail="Issue encountered when building user email")

        email_username = consumer_config.email_username
        email_password = consumer_config.email_password
        smtp_port = consumer_config.smtp_port
        smtp_server = consumer_config.smtp_server
        email_settings = {"username": email_username,
                          "password": email_password,
                          "smtp_port": smtp_port,
                          "smtp_server": smtp_server}
        sent_user_new, message_patient = send_email(email_settings=email_settings, to_user=to_user, subject=Subject,
                                                    body=Body, has_attchament=True)
        sent_user = sent_user * sent_user_new

    # Return response
    if sent_user:
        return ForgotPasswordResponse(message='Emails Sent Successfully')


    else:
        raise ProblemException(status=424, title='Mails failed to send to user.')

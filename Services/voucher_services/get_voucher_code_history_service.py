import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.get_voucher_use_history_response import GetVoucherUseHistoryResponse  # noqa: E501
from swagger_server.models.get_voucher_use_history_response_data import \
    GetVoucherUseHistoryResponseData  # noqa: F401,E501
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers, TenancyHelpers
import re
import urllib.parse

import logging

logger = logging.getLogger(__name__)


def get_voucher_code_history(voucher_name=None, date_from=None, date_to=None, reseller_id=None, page_size=None,
                             page_number=None, export=None, username=None):
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)

    getQuery = {}
    

    try:
        reseller = get_reseller_by_id(reseller_id)[0]
        reseller = DataHelpers.to_good_dict(reseller)

        supports_vouchers = reseller.get("supports_vouchers", False)
        if not supports_vouchers:
            raise ProblemException(status=403, title="Error", detail="Your Reseller does not support Vouchers!")
        
        if voucher_name:
            # Decode URL-encoded spaces
            voucher_name = urllib.parse.unquote(voucher_name)

            # Escape special characters in reseller_name for regex pattern
            voucher_name = re.escape(voucher_name)

            # Define the regex pattern
            regex_pattern = re.compile('.*' + voucher_name + '.*', re.IGNORECASE)
            getQuery["voucher_name"] = {"$regex": regex_pattern}

        if date_from and date_to:

            if date_from > date_to:
                logger.info("Invalid Date Inputs")
                raise ProblemException(status=400, title="Error", detail="Invalid Date Inputs")

            getQuery["datetime"] = {"$gte": date_from,
                                    "$lte": date_to}

            if date_from == date_to:
                getQuery["datetime"] = date_from


        elif date_from:
            getQuery["datetime"] = {"$gte": date_from}

        elif date_to:
            getQuery["datetime"] = {"$lte": date_to}

        if username:
            # Decode URL-encoded spaces
            username = urllib.parse.unquote(username)

            # Escape special characters in username for regex pattern
            username = re.escape(username)

            # Define the regex pattern
            regex_pattern = re.compile('.*' + username + '.*', re.IGNORECASE)
            getQuery["username"] = {"$regex": regex_pattern}

        if apply_Tenancy == 0:
            if branch_id:
                getQuery["branch_id"] = ObjectId(branch_id)

            getQuery["reseller_id"] = ObjectId(reseller_id)
        elif apply_Tenancy == 1:
            if branch_id:
                getQuery["branch_id"] = ObjectId(branch_id)

        if not export:
            if not page_size:
                page_size = 25
            if not page_number:
                page_number = 1
            skip = (page_number - 1) * page_size
        else:
            skip = 0
            page_size = 0

        result = mongodb.find("voucher_logs", getQuery, apply_Tenancy=apply_Tenancy, skip=skip,
                              limit=page_size).sort('datetime', pymongo.DESCENDING)
        total_vouchers_count = mongodb.count_documents("voucher_logs", getQuery, apply_Tenancy=apply_Tenancy)
        result = list(result)
        if not result:
            logger.error("No Orders Found")
            return GetVoucherUseHistoryResponse(response_code="0404", developer_message="No Orders Found",
                                                title="No Data",
                                                total_uses_count=total_vouchers_count, data=[]), 200
        results_formatted = []

        for item in result:
            item = DataHelpers.deserializer(item)
            item["voucher_use_id"] = str(item.pop('_id', ''))
            item["date_created"] = item.pop("datetime","")
            item["currency_code"] = "USD"
            voucher = GetVoucherUseHistoryResponseData.from_dict(item)
            results_formatted.append(voucher)
        return GetVoucherUseHistoryResponse(response_code="0200", developer_message="Success", title="Success",
                                            total_uses_count=total_vouchers_count, data=results_formatted), 200

    except pymongo.errors.PyMongoError:
        logger.exception("Unexpected error during get vouchers.")
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

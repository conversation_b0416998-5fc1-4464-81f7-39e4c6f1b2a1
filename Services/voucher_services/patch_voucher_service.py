import logging

from typing import <PERSON><PERSON>
from bson import ObjectId
from connexion.exceptions import ProblemException
from swagger_server.models import UpdateVoucherAttributesResponse
from swagger_server.models import UpdateVoucherAttributesRequest
from app_helpers import mongodb, helpers
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id


logger = logging.getLogger(__name__)


def patch_voucher(
        body: UpdateVoucherAttributesRequest = None,
        voucher_id: str = None,
        reseller_id: str = None
) -> Tuple[UpdateVoucherAttributesResponse, int]:

    """
        Updates voucher fields by voucher id, could be used by reseller admin or by super admin.
        For superadmin, a specific reseller_id should be provided.
        :param body: JSON API request body
        :param voucher_id: Mongo ObjectID of voucher
        :param reseller_id: Mongo ObjectID of reseller
    """
    branch_id: str
    apply_Tenancy: int

    reseller_id, branch_id, apply_Tenancy = helpers.TenancyHelpers.check_reseller_id(reseller_id)
    reseller = get_reseller_by_id(reseller_id)[0]
    reseller: dict = helpers.DataHelpers.to_good_dict(reseller)
    if not reseller:
        logger.error("Reseller %s does not exist", reseller_id)
        raise ProblemException(status=404, title="Error", detail="Reseller does not exist!")

    supports_vouchers: bool = reseller.get("supports_vouchers", False)
    if not supports_vouchers:
        logger.error("Reseller %s does not support vouchers.", reseller_id)
        raise ProblemException(status=403, title="Error", detail="Your Reseller does not support Vouchers!")

    logger.info("Attempting to change patch voucher %s for reseller %s")
    update = {"$set": helpers.DataHelpers.to_good_dict(body)}
    update["$set"]["is_used"] = True if (body.status == "active") else False

    voucher_updated_count: int = mongodb.update_one("redeem_codes",
                                                    {"_id": ObjectId(voucher_id)},
                                                    update,
                                                    apply_Tenancy=apply_Tenancy).matched_count
    if voucher_updated_count == 0:
        logger.error("Reseller %s does not support vouchers.")
        raise ProblemException(status=404, title="Error", detail="Voucher does not exist!")
    logger.info("Voucher %s successfully updated by reseller %s.", voucher_id, reseller_id)
    return UpdateVoucherAttributesResponse(response_code="0200",
                                           developer_message="Success",
                                           message="Voucher Successfully Updated!",
                                           title="Success",
                                           voucher_id=voucher_id), 200

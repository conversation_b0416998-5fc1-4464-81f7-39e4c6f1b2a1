import datetime
import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.get_vouchers_response import GetVouchersResponse  # noqa: E501
from swagger_server.models.get_vouchers_response_vouchers import GetVouchersResponseVouchers  # noqa: F401,E501
from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers, TenancyHelpers
from instance import consumer_config as instance_config
from app_helpers.encrypt_helper import Crypt
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id

import logging

logger = logging.getLogger(__name__)


def get_voucher_codes(
    voucher_name=None, date_from=None, date_to=None, reseller_id=None, page_size=None, page_number=None, export=None, available=None
):
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)

    getQuery = {"category": "voucher_code"}

    try:
        reseller = get_reseller_by_id(reseller_id)[0]
        reseller = DataHelpers.to_good_dict(reseller)

        supports_vouchers = reseller.get("supports_vouchers", False)
        if not supports_vouchers:
            raise ProblemException(status=403, title="Error", detail="Your Reseller does not support Vouchers!")

        if voucher_name:
            # Decode URL-encoded spaces
            # voucher_name = urllib.parse.unquote(voucher_name)

            # Escape special characters in reseller_name for regex pattern
            # voucher_name = re.escape(voucher_name)

            # Define the regex pattern
            # regex_pattern = re.compile('.*' + voucher_name + '.*', re.IGNORECASE)
            getQuery["redeem_name"] = voucher_name

        if date_from and date_to:
            if date_from > date_to:
                raise ProblemException(status=400, title="Error", detail="Invalid Date Inputs")

            getQuery["datetime"] = {"$gte": date_from, "$lte": date_to}

            if date_from == date_to:
                getQuery["datetime"] = date_from

        elif date_from:
            getQuery["datetime"] = {"$gte": date_from}

        elif date_to:
            getQuery["datetime"] = {"$lte": date_to}

        if available:
            getQuery["times_used"] = 0
            getQuery["expiry_datetime"] = {"$lte": datetime.datetime.utcnow()}

        if apply_Tenancy == 0:
            if branch_id:
                getQuery["branch_id"] = ObjectId(branch_id)
            getQuery["reseller_id"] = ObjectId(reseller_id)

        elif apply_Tenancy == 1:
            if branch_id:
                getQuery["branch_id"] = ObjectId(branch_id)

        if not export:
            if not page_size:
                page_size = 25
            if not page_number:
                page_number = 1
            skip = (page_number - 1) * page_size
        else:
            skip = 0
            page_size = 0

        result = mongodb.find("redeem_codes", getQuery, apply_Tenancy=apply_Tenancy, skip=skip, limit=page_size).sort(
            "datetime", pymongo.DESCENDING
        )
        total_vouchers_count = mongodb.count_documents("redeem_codes", getQuery, apply_Tenancy=apply_Tenancy)
        result = list(result)
        if not result:
            logger.exception("No orders found")
            return (
                GetVouchersResponse(
                    response_code="0404",
                    developer_message="No Orders Found",
                    title="No Data",
                    total_vouchers_count=total_vouchers_count,
                    vouchers=[],
                ),
                200,
            )
        results_formatted = []

        for item in result:
            item = DataHelpers.deserializer(item)
            item["voucher_id"] = str(item.pop("_id", ""))
            item.pop("added_by", "")
            if item.get("reseller_paid", False):
                item["reseller_amount"] = item.pop("reseller_paid", "")
            item["is_used"] = True if item["times_used"] == 1 else False
            item["voucher_code"] = item.pop("redeem_code", "")
            item["voucher_name"] = item.pop("redeem_name", "")
            item["status"] = item.pop("status", "inactive")

            current_time = datetime.datetime.utcnow()
            if item["status"] == "inactive" or item["is_used"] is True or item.get("expiry_datetime", current_time) < current_time:
                logger.info(f"Voucher is no active/ not valid")
                item["is_active"] = False

            # get the decrypted value
            encrypted_voucher_code = item.get("encrypted_voucher_code", "")
            if encrypted_voucher_code != "":
                str_key = instance_config.promo_code_key
                encrypt_helper = Crypt()
                decrypted_voucher_code = encrypt_helper.decrypt(encrypted_voucher_code, str_key)
                logger.info(f"Decrypted voucher code: {decrypted_voucher_code}")
                item["voucher_code"] = decrypted_voucher_code

            voucher = GetVouchersResponseVouchers.from_dict(item)

            results_formatted.append(voucher)
        return (
            GetVouchersResponse(
                response_code="0200",
                developer_message="Success",
                title="Success",
                total_vouchers_count=total_vouchers_count,
                vouchers=results_formatted,
            ),
            200,
        )

    except pymongo.errors.PyMongoError:
        logger.exception("Unexpected error during get voucher.")
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

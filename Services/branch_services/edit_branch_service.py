import connexion
import pymongo.errors
from swagger_server.models.edit_branch_request import EditBranchRequest  # noqa: E501
from swagger_server.models.edit_branch_response import EditBranchResponse  # noqa: E501
from connexion.exceptions import ProblemException
from app_helpers import mongodb
from app_helpers.helpers import DataHelpers, FileHelpers, TenancyHelpers
from bson import ObjectId
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id

from instance import consumer_config
from app_helpers.encrypt_helper import Crypt
import logging
logger = logging.getLogger(__name__)

def edit_branch(branch_id, branchRequest: EditBranchRequest = None, reseller_id=None):
    '''

    :param branch_id:
    :param branchRequest:
    :return:
    '''

    try:
        logger.info("Trying to edit branch with id : %s , branch request : %s , reseller id : %s", branch_id, branchRequest, reseller_id)
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id, branch_id)
        reseller = get_reseller_by_id(reseller_id)[0]
        if not reseller.supports_multibranches:
            raise ProblemException(status=400, title="Error", detail="Reseller does not support multiple branches!")
        branch_limit = branchRequest.limit
        logger.info("Branch limit : %s", branch_limit)
        reseller_balance = reseller.balance
        if branch_limit == -1:
            branchRequest.limit = None
            branchRequest.limit_consumption = None

        elif branch_limit is None:
            pass

        elif branch_limit < 0:
            raise ProblemException(status=400, title="Error", detail="Branch Limit should be zero and higher, or -1 "
                                                                     "to reset!")

        elif branch_limit is not None:
            if branch_limit > reseller_balance:
                raise ProblemException(status=400, title="Error", detail="Branch Limit higher than reseller balance!")
            branchRequest.limit_consumption = 0

        filterQuery = {"_id": ObjectId(branch_id)}
        branchRequest = DataHelpers.serializer(branchRequest)
        branchRequestDict = DataHelpers.to_good_dict(branchRequest)
        branchRequestDict = DataHelpers.dictCleaner(branchRequestDict)

        if apply_Tenancy == 0:
            filterQuery["reseller_id"] = ObjectId(reseller_id)
        update = {"$set": branchRequestDict}
        if branch_limit == -1:
            update["$unset"] = {"limit": 1, "limit_consumption": 1}
        editBranchResult = mongodb.update_one("branch", filterQuery, update,
                                              apply_Tenancy=apply_Tenancy)
        logger.info("Edit branch result : %s", editBranchResult)
        if editBranchResult.matched_count == 0:
            raise ProblemException(status=404, title="Error", detail="Branch Not Found")

        message = "Branch Edited Successfully"

        response = EditBranchResponse(message=message)
        return response, 201
    except pymongo.errors.DuplicateKeyError as e:
        logger.error("Error editing branch , Error : %s", e)
        raise ProblemException(status=400, title="Error", detail="Branch already Exists")

    except pymongo.errors.PyMongoError as e:
        logger.error("Error editing branch , Error : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

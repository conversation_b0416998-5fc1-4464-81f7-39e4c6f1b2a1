import connexion
import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.get_branch_response import GetBranchResponse  # noqa: E501
from swagger_server.models.get_branches_response import GetBranchesResponse  # noqa: E501
from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers, TenancyHelpers
import base64
from instance import consumer_config
from app_helpers.encrypt_helper import Crypt

import logging
logger = logging.getLogger(__name__)

def get_branch(page_size=None, page_number=None, dropdown=False,reseller_id = None):
    getQuery = {}

    try:
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id_for_get_branch(reseller_id)
        logger.info("Trying to get branch, reseller id : %s, branch id : %s", reseller_id, branch_id)
        if apply_Tenancy == 0:
            getQuery["reseller_id"] = ObjectId(reseller_id)
        if apply_Tenancy == 2:
            apply_Tenancy = 1
            getQuery["_id"] = ObjectId(branch_id)
        if not dropdown:
            if not page_size:
                page_size = 25
            if not page_number:
                page_number = 1
            skip = (page_number - 1) * page_size
        else:
            skip = 0
            page_size = 0


        result = mongodb.find("branch", getQuery, apply_Tenancy=apply_Tenancy, skip=skip,
                              limit=page_size).sort('date_created', pymongo.DESCENDING)
        total_branches_count = mongodb.count_documents("branch", getQuery, apply_Tenancy=apply_Tenancy)

        result = list(result)
        if not result:
            return GetBranchesResponse(response_code="0404", developer_message="No Branches Found", title="No Data",
                                        total_branches_count=total_branches_count, branches=[]), 200
        results_formatted = []

        for item in result:
            item = DataHelpers.deserializer(item)
            item["branch_id"] = str(item.pop('_id', ''))
            item.pop("added_by", "")
            if not apply_Tenancy == "0":
                item.pop("corp_rate_revenue", "")

            branch = GetBranchResponse.from_dict(item)
            results_formatted.append(branch)

        return GetBranchesResponse(response_code="0200", developer_message="Success", title="Success",
                                    total_branches_count=total_branches_count, branches=results_formatted), 200
    except pymongo.errors.PyMongoError as e:
        logger.error("Error getting branch , error : %s", e)
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

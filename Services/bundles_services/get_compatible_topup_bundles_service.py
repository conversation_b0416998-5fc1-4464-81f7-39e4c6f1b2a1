from swagger_server.models.get_bundles_response import GetBundlesResponse
from helpers.db_helper import get_related_topup_bundles
import connexion
from app_helpers.helpers import TenancyHelpers
import logging
logger = logging.getLogger(__name__)

def get_compatible_topup_bundles(bundle_code=None, country_code=None, page_size=None, page_number=None,reseller_id=None, bundle_name=None, sort_by="price",bundle_tag=None,currency_code=None):
    try:
        '''
        ******************************************************************
        
                                                        **********************
        
        DEPRECATED      *********************************
        
                                                        **********************
        
        ******************************************************************
        
        '''
        raise connexion.ProblemException(status=404, title="DEPRECATED",detail="Deprecated API, Please utilize V2!")






        sorting_order = 1
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        if not page_size:
            page_size = 50
        if not page_number:
            page_number = 1

        if sort_by:
            if sort_by == "price_asc":
                sort_by = "reseller_retail_price"
                sorting_order = 1
            elif sort_by == "price_dsc":
                sort_by = "reseller_retail_price"
                sorting_order = -1
            elif sort_by == "bundle_name":
                sort_by = "bundle_name"
                sorting_order = 1
            elif sort_by == "data_asc":
                sort_by = "data_amount"
                sorting_order = 1
            elif sort_by == "data_dsc":
                sort_by = "data_amount"
                sorting_order = -1

        all_bundles = get_related_topup_bundles(reseller_id, country_code=country_code, bundle_code=bundle_code, page_size=page_size,
                                                number_of_page=page_number,sort_by=sort_by,bundle_name=bundle_name, sorting_order=sorting_order,bundle_tag=bundle_tag,currency_code=currency_code)
        if all_bundles.get("bundles"):
            if currency_code and all_bundles.get("bundles")[0].get("subscriber_price_in_additional_currency") is None:
                raise connexion.ProblemException(status=400, title="Error", detail="Invalid Currency Code!")
    except connexion.ProblemException as e:
        logger.error("Error geting compatible topup bundles, Error : %s", e)
        raise e
    except Exception as e:
        logger.error("Error geting compatible topup bundles, Error : %s", e)
        raise connexion.ProblemException(status=404, title="Error", detail=str(e))
    if all_bundles and all_bundles.get('status',''):
        return GetBundlesResponse.from_dict(all_bundles['data'])
    else:
        raise connexion.ProblemException(status=200, title=all_bundles['title'], detail=all_bundles['message'])
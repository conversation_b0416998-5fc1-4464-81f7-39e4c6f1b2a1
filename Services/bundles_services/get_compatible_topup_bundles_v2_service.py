from helpers.constaints import DESCENDING_ORDER, ASCENDING_ORDER
from helpers.get_related_topup_bundle_reseller import get_topup_related_v2_reseller
from swagger_server.exceptions import OrderObjectNotFound
from swagger_server.models.get_bundles_response import GetBundlesResponse
from app_helpers.helpers import TenancyHelpers
from connexion import ProblemException
from bson import ObjectId
from app_helpers import mongodb
import connexion


def get_compatible_topup_bundles_v2(
    bundle_code,
    country_code=None,
    page_size=50,
    page_number=1,
    reseller_id=None,
    bundle_name=None,
    sort_by=None,
    bundle_tag=None,
    order_id=None,
    previous_order_reference=None,
    region_code=None,
    currency_code=None,
):
    if not page_size:
        page_size = 50
    if not page_number:
        page_number = 1
    apply_tenancy = 0
    if previous_order_reference and order_id:
        raise OrderObjectNotFound(detail="Please choose either order_id or previous_order_reference")
    elif previous_order_reference:
        filterQuery = {"order_reference": previous_order_reference}
    elif order_id:
        filterQuery = {"_id": ObjectId(order_id)}
    else:
        raise OrderObjectNotFound(detail="Please provide either order_id or previous_order_reference!")

    order_history = mongodb.find_one("order_history", filterQuery, apply_Tenancy=apply_tenancy)
    if not order_history:
        raise ProblemException(status=400, title="Error", detail="Order Not Finalized, ICCID Not Found")
    iccid = order_history.get("iccid")
    if not iccid:
        raise ProblemException(status=400, title="Error", detail="Order Not Finalized, ICCID Not Found")

    plan_started_order = mongodb.find_one(
        "order_history", {"plan_started": True, "iccid": iccid, "order_status": "Successful"}, apply_Tenancy=apply_tenancy
    )
    current_bundle = mongodb.find_one(
        "bundles",
        {
            "bundle_code": order_history["bundle_code"],
        },
        apply_Tenancy=0,
    )
    if current_bundle and current_bundle.get("vendor_name") == "Orange" and not plan_started_order:
        raise ProblemException(status=400, title="Error", detail="Can Only Topup This Order After Scanning Profile!")

    reseller_id, _, _ = TenancyHelpers.check_reseller_id(reseller_id)

    DEFAULT_SORTING_AND_ORDERING = {"sort_by": "retail_price", "sorting_order": ASCENDING_ORDER}
    sorting_key_and_ordering = {
        "price_asc": {"sort_by": "reseller_retail_price", "sorting_order": ASCENDING_ORDER},
        "price_dsc": {"sort_by": "reseller_retail_price", "sorting_order": DESCENDING_ORDER},
        "bundle_name": {"sort_by": "bundle_name", "sorting_order": ASCENDING_ORDER},
        "data_asc": {"sort_by": "data_amount", "sorting_order": ASCENDING_ORDER},
        "data_des": {"sort_by": "data_amount", "sorting_order": DESCENDING_ORDER},
    }

    bundles, total_count = get_topup_related_v2_reseller(
        reseller_id=reseller_id,
        bundle_code=bundle_code,
        country_code=country_code,
        page_number=page_number,
        page_size=page_size,
        region_code=region_code,
        bundle_tag=bundle_tag,
        **sorting_key_and_ordering.get(sort_by, DEFAULT_SORTING_AND_ORDERING),
        bundle_name=bundle_name,
        currency_code=currency_code,
        iccid=iccid
    )
    if bundles:
        if currency_code and bundles[0].get("subscriber_price_in_additional_currency") is None:
            raise connexion.ProblemException(status=400, title="Error", detail="Invalid Currency Code!")
    return GetBundlesResponse.from_dict({"bundles": bundles, "total_bundles_count": total_count})

from helpers.constaints import DESCENDING_ORDER, ASCENDING_ORDER
from helpers.get_related_topup_bundle_reseller import get_topup_related_v2_reseller
from swagger_server.models.get_bundles_response import GetBundlesResponse
from app_helpers.helpers import TenancyHelpers
import connexion

def get_available_topup_bundles(
        bundle_code, country_code=None, page_size=50, page_number=1, reseller_id=None, bundle_name=None, sort_by=None,
        bundle_tag=None, region_code=None,currency_code=None
):
    if not page_size:
        page_size = 50
    if not page_number:
        page_number = 1
    apply_tenancy = 0


    reseller_id, _, _ = TenancyHelpers.check_reseller_id(reseller_id)

    DEFAULT_SORTING_AND_ORDERING = {'sort_by': 'retail_price', 'sorting_order': ASCENDING_ORDER}
    sorting_key_and_ordering = {
        'price_asc': {'sort_by': 'reseller_retail_price', 'sorting_order': ASCENDING_ORDER},
        'price_dsc': {'sort_by': 'reseller_retail_price', 'sorting_order': DESCENDING_ORDER},
        'bundle_name': {'sort_by': 'bundle_name', 'sorting_order': ASCENDING_ORDER},
        'data_asc': {'sort_by': 'data_amount', 'sorting_order': ASCENDING_ORDER},
        'data_des': {'sort_by': 'data_amount', 'sorting_order': DESCENDING_ORDER},
    }

    bundles, total_count = get_topup_related_v2_reseller(
        reseller_id=reseller_id, bundle_code=bundle_code, country_code=country_code,
        page_number=page_number, page_size=page_size, region_code=region_code, bundle_tag=bundle_tag,
        **sorting_key_and_ordering.get(sort_by, DEFAULT_SORTING_AND_ORDERING), bundle_name=bundle_name, currency_code=currency_code, iccid=None
    )
    if bundles:
        if currency_code and bundles[0].get("subscriber_price_in_additional_currency") is None:
            raise connexion.ProblemException(status=400, title="Error", detail="Invalid Currency Code!")
    return GetBundlesResponse.from_dict(
        {'bundles': bundles, 'total_bundles_count': total_count}
    )

from swagger_server.models.get_countries_response import GetCountriesResponse
from helpers.db_helper import get_all_countries
from app_helpers.helpers import TenancyHelpers
import connexion
import logging
logger = logging.getLogger(__name__)

def get_countries(reseller_id=None, reseller_admin_view=False):
    try:

        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        super_admin_view = False
        logger.info("Tryng to get countries with reseller id : %s, and super admin view is set to : %s", reseller_id, super_admin_view)
        if apply_Tenancy == 0:
            if reseller_admin_view:
                super_admin_view = True

        elif apply_Tenancy == 1:
            super_admin_view = False
        else:
            if reseller_admin_view:
                raise connexion.ProblemException(status=401, title="Error",
                                                 detail="You are not Authorized to Use Reseller Admin View")


        all_countries = get_all_countries(reseller_id=reseller_id, bundle_category=None, region_code=None,
                                          reseller_admin_view=reseller_admin_view, super_admin_view=super_admin_view)
        all_countries = list(all_countries)
        if all_countries:
            all_countries = all_countries[0]
            response = GetCountriesResponse.from_dict(all_countries), 200
        else:
            response = GetCountriesResponse(response_code=404, developer_message="No Countries are available",
                                            title="No Countries are available",countries=[]), 200
    except connexion.ProblemException as e:
        logger.error("Error getting countries, Error : %s", e)
        raise e
    except Exception as e:
        logger.error("Error getting countries, Error : %s", e)
        raise connexion.ProblemException(status=404, title="Error", detail=str(e))

    return response

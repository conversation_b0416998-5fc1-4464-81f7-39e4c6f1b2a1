import datetime
import swagger_server.__main__
from helpers import literals
from swagger_server.models.assign_bundle_request import AssignBundleRequest
from swagger_server.models.assign_bundle_response import AssignBundleResponse
from app_helpers.helpers import (
    TenancyHelpers,
    DataHelpers,
    get_reseller_usage_limit,
    thread_to_send_email_warning,
    check_credit_balance,
    check_prepaid_balance
)

from app_helpers import mongodb, helpers
from connexion.exceptions import ProblemException
from helpers.db_helper import AllocationBundle, format_bundles, validate_email
from app_helpers.email_helper import (
    branch_limit_exceed_message,
)
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from Services.branch_services.get_branch_by_id_service import get_branch_by_id
from bson import ObjectId
from instance import consumer_config as instance_config
import threading
from uuid import uuid4

import logging

logger = logging.getLogger(__name__)


def reserve_bundle(
    request: AssignBundleRequest = None,
    reseller_id=None,
    branch_id=None,
    currency_code=None,
):
    try:
        try:
            client_email = request.email
            if client_email is not None:
                reseller = get_reseller_by_id(reseller_id)
                database_name = reseller[0].tenant_name
                logger.info("Trying to check if email : %s has already an old order", client_email)
                if database_name is None:
                    database_name = instance_config.decrypted_db_name_alias

                old_order = list(mongodb.find_order_history("order_history",
                                                            {"client_email": client_email, "reseller_type": "reseller"},
                                                            database_name=database_name))
                if not old_order or len(old_order) == 0:
                    msg_list = []
                    is_email_valid, msg_lst = validate_email(True, msg_list, client_email, "email")  # validate email operation
                    if is_email_valid is False:
                        logger.error("Error on reserve_bundle, email %s is invalid", client_email)
                        raise ValueError(literals.VALID_EMAIL)  # fire invalid email format message exception


            converted_prices = False
            remaining_wallet_balance_in_additional_currency = False
            subscriber_price_in_additional_currency = False
            branch = False
            branch_limit = None
            reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(
                reseller_id, branch_id
            )
            reseller = get_reseller_by_id(reseller_id)
            if reseller[0].reseller_category == "Internal":
                database_name = reseller[0].tenant_name
                if database_name is None:
                    database_name = instance_config.decrypted_db_name_alias
                if apply_Tenancy in [0, 1]:
                    if branch_id:
                        branch = get_branch_by_id(branch_id, reseller_id)[0]

                elif apply_Tenancy == 2:
                    branch = get_branch_by_id(branch_id)[0]

                if branch:
                    branch_limit = branch.limit
                    branch_limit_consumption = branch.limit_consumption
                    if branch_limit is not None:
                        branch_limit_remaining = branch_limit - branch_limit_consumption

                # if branch:
                #     reseller_balance = branch.balance
                # else:
                #   reseller_balance = reseller[0].balance
            reseller_balance = reseller[0].balance
            reseller_usage_limit = get_reseller_usage_limit(reseller[0])
        except ProblemException as e:
            raise e

        except ValueError as e:
            logger.error("Error reserve bundle, Error: %s", e)
            raise ProblemException(
                status=400,
                title="Error",
                detail=str(e),
            )

        except Exception as general_exception:
            logger.exception(f"general_exception {general_exception}")
            raise ProblemException(
                status=401,
                title="Error",
                detail="Invalid Reseller! You are not Authorized to do this purchase!",
            )

        if reseller:
            is_active = reseller[0].is_active
            if is_active == False:
                raise ProblemException(
                    status=401,
                    title="Error",
                    detail="Reseller is disabled, Contact " "administration!",
                )

        if branch:
            is_active = branch.is_active
            if is_active == False:
                raise ProblemException(
                    status=400,
                    title="Error",
                    detail="Branch is disabled, Contact " "administration!",
                )

        if request.order_reference is not None:
            repeated_order_reference = mongodb.find_one(
                "order_history",
                {"order_reference": request.order_reference},
                apply_Tenancy=0,
                database_name=database_name,
            )
            if repeated_order_reference:
                raise ProblemException(
                    status=400,
                    title="Error",
                    detail="Please choose a unique order reference",
                )

        # Check Bundle Price
        bundle_code = request.bundle_code
        email = request.email
        logger.info("Checking If Bundle Exists")

        allocation_helper = AllocationBundle()
        bundle_info = format_bundles(reseller_id=reseller_id, bundle_code=bundle_code)
        bundle_category = bundle_info.get("bundle_category")
        if not bundle_category:
            raise ProblemException(
                status=404, title="Error", detail="Bundle Code does not exist!"
            )

        bundle_name = bundle_info.get("bundle_name")
        subscriber_price = bundle_info.get("subscriber_price")
        reseller_retail_price = bundle_info.get("reseller_retail_price")
        bundle_marketing_name = bundle_info.get("bundle_marketing_name")

        logger.info("Checking If Reseller has Sufficient Balance")

        if reseller_usage_limit < subscriber_price:
            raise ProblemException(
                status=400, title="Error", detail="Insufficient Balance"
            )

        if branch_limit is not None:
            if branch_limit_remaining < subscriber_price:
                try:
                    reseller_email = reseller[0].contact.emails[0]
                    branch_name = branch.branch_name
                    reseller_name = reseller[0].reseller_name
                    subject = "Branch Limit Reached"
                    thread = threading.Thread(
                        target=thread_to_send_email_warning,
                        args=(
                            reseller_email,
                            subject,
                            branch_limit_exceed_message.format(
                                reseller_name, branch_name, str(branch_limit)
                            ),
                        ),
                    )
                    thread.start()
                except Exception as e:
                    logger.error("Error reserving bundles, Error : %s", e)

                raise ProblemException(
                    status=400, title="Error", detail="Branch Limit Reached"
                )

            limit_consumption_update = {"$inc": {"limit_consumption": subscriber_price}}
            limit_consumption_update_result = mongodb.update_one(
                "branch",
                {"_id": ObjectId(branch_id)},
                limit_consumption_update,
                apply_Tenancy=0,
                database_name=database_name,
            )

        logger.info("Removing Balance from reseller")

        # Remove Balance from reseller
        new_reseller_balance = reseller_balance - subscriber_price
        subtract_amount = 0 - subscriber_price
        subtract_query = {"$inc": {"balance": subtract_amount}}
        if currency_code:
            prices_to_be_converted = {
                "subscriber_price_at_order": subscriber_price,
                "remaining_wallet_balance_at_order": new_reseller_balance,
            }
            converted_prices = DataHelpers.convert_currency(
                currency_code, prices_to_be_converted
            )

            converted_prices["additional_currency_code_at_order"] = (
                converted_prices.pop("additional_currency_code")
            )

        # if branch:
        #     subtract_balance_result = mongodb.update_one("branch", {"_id": ObjectId(branch_id)}, subtract_query,
        #                                                  apply_Tenancy=0)
        # else:
        #     subtract_balance_result = mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)}, subtract_query,
        #                                                  apply_Tenancy=0

        subtract_balance_result = mongodb.update_one(
            "reseller",
            {"_id": ObjectId(reseller_id)},
            subtract_query,
            apply_Tenancy=0,
            database_name=database_name,
        )

        if subtract_balance_result.matched_count == 0:
            raise ProblemException(
                status=404, title="Error", detail="Reseller Not Found!"
            )

        logger.info("Assign Bundle, if successful, Send QR CODE AND CONFIG TO CUSTOMER.")

        # Assign Bundle, if successful, Send QR CODE AND CONFIG TO CUSTOMER.
        try:
            allocation_result = allocation_helper.assign_bundle(
                bundle_code=bundle_code,
                iccid="",
                email=email,
                instance_config=instance_config,
                category=1,
                reseller_id=reseller_id,
                update_daily_used=False,
                assign_by_reserve=True,
            )
        except Exception as e:
            allocation_result = {"failure_reason": str(e)}
            logger.error("<error> %s while processing bundle: %s ", e, bundle_code)
        logger.info( "Allocation result status: %s", allocation_result.get("status"))

        # Add order History
        if allocation_result.get("status"):
            bundle_info = allocation_result.get("bundle_info", {})

            # update payment date in profiles
            update_reservation_date = {
                "$set": {
                    "reserved": True,
                    "date_of_reservation": datetime.datetime.utcnow(),
                    "reseller_id": reseller_id,
                }
            }
            profile_updated = mongodb.update_one(
                "profiles",
                {"iccid": allocation_result.get("iccid", "")},
                update_reservation_date,
                apply_Tenancy=0,
                database_name=database_name,
            )

            order_history = {
                "order_status": "Pending",
                "iccid": allocation_result.get("iccid", ""),
                "bundle_price": subscriber_price,
                "bundle_retail_price": reseller_retail_price,
                "bundle_code": bundle_info.get("bundle_code"),
                "bundle_marketing_name": bundle_info.get("bundle_marketing_name"),
                "bundle_name": bundle_info.get("bundle_name"),
                "matching_id": allocation_result.get("matching_id", ""),
                "smdp_address": allocation_result.get("smdp_address", ""),
                "activation_code": allocation_result.get("activation_code", ""),
                "client_name": request.name,
                "client_email": request.email,
                "plan_uid": allocation_result.get("plan_uid", ""),
                "vendor_msisdn": allocation_result.get("vendor_msisdn"),
                "remaining_wallet_balance": new_reseller_balance,
                "bundle_category": bundle_info.get("bundle_category"),
                "order_reference": request.order_reference,
                "otp": str(uuid4()),
                "original_bundle_code": bundle_code,
                "reseller_id": ObjectId(reseller_id),
                "order_type": "BuyBundle",
                "vendor_start_bundle": allocation_result.get(
                    "vendor_start_bundle", False
                ),
                "vendor_expiry_days": allocation_result.get("vendor_expiry_days", 365),
                "vendor_expiry_date_profile": allocation_result.get(
                    "vendor_expiry_date_profile", 0
                ),
            }  # reseller_id and agent_id will be automatically added by mongodb helper.

            if converted_prices:
                order_history.update(converted_prices)

            if bundle_category == "country":
                bundle_country_code = bundle_info.get("country_code")
                bundle_country_name = bundle_info.get("country_name")
                order_history["country_name"] = bundle_country_name
                order_history["country_code"] = bundle_country_code
            else:
                bundle_country_code_list = bundle_info.get("country_code")
                bundle_country_name_list = bundle_info.get("country_name")
                order_history["country_name"] = bundle_country_name_list
                order_history["country_code"] = bundle_country_code_list

            if apply_Tenancy == 0:
                if branch_id:
                    order_history["branch_id"] = ObjectId(branch_id)
                order_history["reseller_id"] = ObjectId(reseller_id)
            elif apply_Tenancy == 1:
                if branch_id:
                    order_history["branch_id"] = ObjectId(branch_id)

            update_query = {
                "$set": {field: value for field, value in order_history.items()}
            }
            check_order = mongodb.find_one(
                "order_history",
                {"plan_uid": allocation_result.get("plan_uid")},
                apply_Tenancy=0,
                database_name=database_name,
            )
            if check_order and check_order.get("plan_uid") and check_order.get("order_status") != "Canceled":
                successful_transaction_response = mongodb.update_one(
                    "order_history",
                    {"_id": check_order.get("_id"),},
                    update_query,
                    apply_Tenancy=0,
                    database_name=database_name,
                )
                order_id = check_order.get("_id")
                logger.info("Order already exist and updated with plan_uid = %s", allocation_result.get("plan_uid"))
            else:
                successful_transaction_response = mongodb.insert_one(
                    "order_history",
                    order_history,
                    apply_Tenancy=0,
                    database_name=database_name,
                )
                order_id = successful_transaction_response.inserted_id
                logger.info("Order successfully created with ID: %s",successful_transaction_response.inserted_id)

            transaction_history = {
                "type": "Order",
                "amount": (0 - subscriber_price),
                "order_id": order_id,
                "reseller_id": ObjectId(reseller_id),
            }
            if apply_Tenancy == 0:
                if branch_id:
                    transaction_history["branch_id"] = ObjectId(branch_id)
                transaction_history["reseller_id"] = ObjectId(reseller_id)
            elif apply_Tenancy == 1:
                if branch_id:
                    transaction_history["branch_id"] = ObjectId(branch_id)

            transaction_response = mongodb.insert_one(
                "transaction_history",
                transaction_history,
                apply_Tenancy=0,
                database_name=database_name,
            )
            # print("sending Email to client")
            # qr_code_list = []
            # qr_code_list.append(order_history)
            # send_qr_code(qr_code_list, reseller_id)
            # print("email sent to client")
            # TO Do check if reseller should send email or not
            # if order_history["order_status"] == "Successful" and order_history["iccid"] != "" and order_history[
            #    "activation_code"] != "" and order_history.get("client_email", False):
            #    order_history["_id"] = str(order_id)

            # thread = threading.Thread(target=thread_to_send_email_qr,
            #                          args=(order_history, reseller_id, order_id))
            # thread.start()
            reseller_email = reseller[0].contact.emails[0]
            reseller_name = reseller[0].reseller_name
            reseller_type = reseller[0].reseller_type

            if reseller_type == "prepaid":
                check_prepaid_balance(reseller[0], reseller_email, reseller_name, new_reseller_balance)
            else:
                check_credit_balance(reseller[0], reseller_email, reseller_name, new_reseller_balance)

            return AssignBundleResponse(
                message="Bundle Reserved Successfully",
                order_id=str(order_id),
                reseller_id=reseller_id,
                remaining_wallet_balance=new_reseller_balance,
                iccid=allocation_result.get("iccid", ""),
            )

        # If not successful, don't remove money from customer, create failed transaction for customer
        else:
            logger.info("returning balance to client")
            return_balance_query = {"$inc": {"balance": subscriber_price}}

            # if branch:
            #     return_balance_result = mongodb.update_one("branch", {"_id": ObjectId(branch_id)}, return_balance_query,
            #                                                  apply_Tenancy=0)
            # else:
            #     return_balance_result = mongodb.update_one("reseller", {"_id": ObjectId(reseller_id)}, return_balance_query,
            #                                                  apply_Tenancy=0)

            return_balance_result = mongodb.update_one(
                "reseller",
                {"_id": ObjectId(reseller_id)},
                return_balance_query,
                apply_Tenancy=0,
                database_name=database_name,
            )

            if return_balance_result.matched_count == 0:
                raise ProblemException(
                    status=404, title="Error", detail="Reseller Not Found!"
                )

            if branch_limit is not None:
                limit_consumption_update = {
                    "$inc": {"limit_consumption": 0 - subscriber_price}
                }
                limit_consumption_update_result = mongodb.update_one(
                    "branch",
                    {"_id": ObjectId(branch_id)},
                    limit_consumption_update,
                    apply_Tenancy=0,
                    database_name=database_name,
                )

            order_history = {
                "order_status": "Failed",
                "bundle_price": subscriber_price,
                "bundle_code": bundle_code,
                "bundle_marketing_name": bundle_marketing_name,
                "bundle_name": bundle_name,
                "client_name": request.name,
                "client_email": request.email,
                "order_reference": request.order_reference,
                "failure_reason": allocation_result.get("failure_reason", ""),
                "remaining_wallet_balance": new_reseller_balance,
                "bundle_category": bundle_category,
            }

            if bundle_category == "country":
                bundle_country_code = bundle_info.get("country_code")
                bundle_country_name = bundle_info.get("country_name")
                order_history["country_name"] = bundle_country_name
                order_history["country_code"] = bundle_country_code
            else:
                bundle_country_code_list = bundle_info.get("country_code")
                bundle_country_name_list = bundle_info.get("country_name")
                order_history["country_name"] = bundle_country_name_list
                order_history["country_code"] = bundle_country_code_list
            if apply_Tenancy == 0:
                if branch_id:
                    order_history["branch_id"] = ObjectId(branch_id)
                order_history["reseller_id"] = ObjectId(reseller_id)
            elif apply_Tenancy == 1:
                if branch_id:
                    order_history["branch_id"] = ObjectId(branch_id)
            order_history_response = mongodb.insert_one(
                "order_history",
                order_history,
                apply_Tenancy=0,
                database_name=database_name,
            )
            logger.info("Failed order_history created")

            if allocation_result.get("failure_reason", ""):
                detail = (
                    "Purchase Unsuccessful, contact Administration, failure reason: "
                    + allocation_result.get("failure_reason", "")
                )
            else:
                detail = "Purchase Unsuccessful, contact Administration."
            raise ProblemException(status=500, title="Error", detail=detail)
    except ProblemException as e:
        raise e
    except Exception as e:
        logger.error("Error reserving bundle, Error : %s", e)
        raise ProblemException(
            status=500,
            title="Internal Server Error",
            detail="Purchase Unsuccessful, contact Administration.",
        )

import logging

from app_models.consumer_models import Bundles
from connexion import ProblemException
from keycloak import KeycloakGetError

from helpers.db_helper import (
    get_reseller_global_or_countries_geoscope_bundle_pipeline,
    get_reseller_region_geoscope_bundle_pipeline,
    map_flat_bundles,
    map_region_grouped_bundles,
)

logger = logging.getLogger(__name__)


def get_reseller_bundles_by_scope(geoscope, page:int=None, limit:int=None,
                                  data_size_min:str=None, data_size_max:str=None,
                                  validity_days_min:int=None, validity_days_max:int=None, price_min:str=None, price_max:str=None):

    try:
        if geoscope == "region":
            pipeline = get_reseller_region_geoscope_bundle_pipeline(
                page=page,
                limit=limit,
                data_size_min=data_size_min,
                data_size_max=data_size_max,
                validity_days_min=validity_days_min,
                validity_days_max=validity_days_max,
                price_min=price_min,
                price_max=price_max,
                show_unidentified=False,
            )
            raw_result = list(Bundles.objects.aggregate(*pipeline))
            return map_region_grouped_bundles(raw_result, page, limit)

        elif geoscope in {"countries", "global"}:
            pipeline = get_reseller_global_or_countries_geoscope_bundle_pipeline(
                geoscope=geoscope,
                page=page,
                limit=limit,
                data_size_min=data_size_min,
                data_size_max=data_size_max,
                validity_days_min=validity_days_min,
                validity_days_max=validity_days_max,
                price_min=price_min,
                price_max=price_max,
            )
            raw_result = list(Bundles.objects.aggregate(*pipeline))
            return map_flat_bundles(raw_result, page, limit)

    except KeycloakGetError as e:
        logger.error("Keycloak error: %s", str(e))
        raise ProblemException(status=401, title="Unauthorized", detail="Invalid or expired token")

    except ProblemException as e:
        logger.error("ProblemException: %s", e.detail)
        raise e

    except Exception:
        logger.exception("Unhandled exception in get_reseller_bundles_by_scope")
        raise ProblemException(status=500, title="Internal Error", detail="Unexpected server error occurred")

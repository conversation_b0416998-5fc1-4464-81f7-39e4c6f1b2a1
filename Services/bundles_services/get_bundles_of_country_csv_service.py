from helpers.db_helper import format_bundles, get_version
from app_helpers.helpers import TenancyHelpers,DataHelpers
import connexion
from flask import send_file
from swagger_server.models.get_bundles_response import GetBundlesResponseBundles,GetBundlesResponse
import csv
import logging
logger = logging.getLogger(__name__)

def write_csv_file(data, file_path):
    logger.info("Trying to write to csv file , path : %s", file_path)
    # Extract the headers from the first dictionary in the array
    headers = GetBundlesResponseBundles().attribute_map.keys()

    with open(file_path, 'w', newline='') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=headers)

        # Write the headers to the CSV file
        writer.writeheader()

        # Write each row to the CSV file
        for row in data:
            writer.writerow(row)

def get_bundles_of_country_csv(country_code=None, bundle_category=None, page_size=None, page_number=None, reseller_id=None,bundle_name= None,sort_by=None, reseller_admin_view = False,export = False,currency_code=None):
    try:
        logger.info("Trying to get bundles of country csv format")
        sorting_order = 1
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        super_admin_view = False
        if apply_Tenancy == 0:
            if reseller_admin_view:
                super_admin_view = True

        elif apply_Tenancy == 1:
            super_admin_view = False
        else:
            if reseller_admin_view:
                raise connexion.ProblemException(status=401, title="Error", detail="You are not Authorized to Use Reseller Admin View")
        if not export:
            if not page_size:
                page_size = 50
            if not page_number:
                page_number = 1
        else:
            page_size = 100000
            page_number = 1
        version = get_version()
        print("version ", version)
        if sort_by:
            if sort_by == "price_asc":
                sort_by = "reseller_retail_price"
                sorting_order = 1
            elif sort_by == "price_dsc":
                sort_by = "reseller_retail_price"
                sorting_order = -1
            elif sort_by == "bundle_name":
                sort_by = "lowercase_bundle_name"
                sorting_order = 1
            elif sort_by == "data_asc":
                sort_by = "data_amount"
                sorting_order = 1
            elif sort_by == "data_dsc":
                sort_by = "data_amount"
                sorting_order = -1

        all_bundles = format_bundles(reseller_id, bundle_category, country_code, page_size, page_number,
                                     sort_by=sort_by,sorting_order=sorting_order,bundle_name=bundle_name,reseller_admin_view = reseller_admin_view,super_admin_view=super_admin_view,currency_code=currency_code)

        if all_bundles.get("bundles"):
            if currency_code and all_bundles.get("bundles")[0].get("subscriber_price_in_additional_currency") is None:
                raise connexion.ProblemException(status=400, title="Error", detail="Invalid Currency Code!")

        all_bundles = GetBundlesResponse.from_dict(all_bundles)
        all_bundles = DataHelpers.to_good_dict(all_bundles)
        all_bundles = DataHelpers.testdictCleaner(all_bundles)




        file_path = './swagger_server/output.csv'
        write_csv_file(all_bundles["bundles"], file_path)





    except connexion.ProblemException as e:
        raise e
    except Exception as e:
        print(str(e))
        raise connexion.ProblemException(status=404, title="Error", detail=str(e))
    file_path='output.csv'
    return send_file(file_path,as_attachment=True)

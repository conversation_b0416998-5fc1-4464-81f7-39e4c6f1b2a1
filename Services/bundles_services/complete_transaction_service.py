import datetime

import pymongo.errors
from app_helpers import mongodb
from swagger_server.models.complete_transaction_successful_response import (
    CompleteTransactionSuccessfulResponse,
)
from swagger_server.models.complete_transaction_failed_response import (
    CompleteTransactionFailedResponse,
)
from swagger_server.models.complete_transaction_response import (
    CompleteTransactionResponse,
)  # noqa: E501
from swagger_server.models.complete_transaction_request import (
    CompleteTransactionRequest,
)  # noqa: E501
from connexion.exceptions import ProblemException
from bson import ObjectId
from app_helpers.helpers import DataHelpers, TenancyHelpers
from Services.bundles_services.common_service import thread_to_send_email_qr
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from instance import consumer_config
import threading
from helpers.support_helper import add_data_amount_to_consumption_cache
import logging

logger = logging.getLogger(__name__)


def complete_transaction(
    request: CompleteTransactionRequest = None,
    reseller_id=None,
):
    logger.info("Starting complete_transaction process")
    reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
    order_reference = request.order_reference
    logger.info(f"Processing order_reference: {order_reference} for reseller_id: {reseller_id}")
    reseller = get_reseller_by_id(reseller_id)
    if reseller[0].reseller_category == "Internal":
        database_name = reseller[0].tenant_name
        if database_name is None:
            database_name = consumer_config.decrypted_db_name_alias

        getQuery = {"reseller_id": ObjectId(reseller_id)}
        pipeline = []
        try:
            if reseller_id != "111111111111111111111111":

                if order_reference != "":
                    getQuery["order_reference"] = order_reference
                    logger.info("Fetching order details from order_history")

                    requested_order = mongodb.find_one(
                        "order_history",
                        getQuery,
                        apply_Tenancy=0,
                        database_name=database_name,
                    )

                    if requested_order is None:
                        logger.warning("No Orders Found for the given order_reference %s", order_reference)
                        return (
                            CompleteTransactionResponse(
                                response_code="404",
                                developer_message="No Orders Found",
                                title="No Data",
                                orders=[],
                            ),
                            404,
                        )
                    elif requested_order.get("order_status") == "Pending":
                        logger.info("Order found in Pending state, updating to Successful")

                        update_query = {
                            "$set": {
                                "order_status": "Successful",
                                "payment_date": datetime.datetime.utcnow(),
                            }
                        }
                        # add data amount from the consumption cache
                        add_data_amount_to_consumption_cache(
                            requested_order.get("iccid"),
                            requested_order.get("bundle_code"),
                        )
                        update_order = mongodb.update_one(
                            "order_history",
                            {"_id": requested_order.get("_id")},
                            update_query,
                            apply_Tenancy=0,
                            database_name=database_name,
                        )
                        update_profile = mongodb.update_one(
                            "profiles",
                            {"iccid": requested_order.get("iccid")},
                            {"$set": {"payment_date": datetime.datetime.utcnow()}},
                            apply_Tenancy=0,
                            database_name=database_name,
                        )
                        update_daily_used = mongodb.update_one(
                            "bundles",
                            {"bundle_code": requested_order.get("bundle_code")},
                            {"$inc": {"daily_used": 1}},
                            apply_Tenancy=0,
                            database_name=database_name,
                        )

                        logger.info("Fetching additional order details with aggregation pipeline")
                        pipeline.append({"$match": getQuery})  # Equivalent to the find query's filter

                        pipeline.append({"$sort": {"date_created": pymongo.DESCENDING}})  # Sort by 'date_created' in descending order
                        pipeline.append({"$project": {"currency": 0}})
                        pipeline.append(
                            {
                                "$lookup": {
                                    "from": "profiles",
                                    "localField": "iccid",
                                    "foreignField": "iccid",
                                    "as": "profile",
                                }
                            }
                        )
                        result = mongodb.aggregate(
                            "order_history",
                            pipeline,
                            apply_Tenancy=apply_Tenancy,
                            database_name=database_name,
                        )

                        result = list(result)
                        vendors = mongodb.find("vendors", {}, apply_Tenancy=0, database_name=database_name)
                        vendors = list(vendors)
                        vendors_expiry_days = {}
                        if vendors:
                            for vendor in vendors:
                                if vendor_name := vendor.get("vendor_name"):
                                    vendors_expiry_days[vendor_name] = vendor.get("number_of_expiry_days", 365)
                        if not result:
                            logger.warning("No order data found after aggregation pipeline execution")
                            return (
                                CompleteTransactionResponse(
                                    response_code="0404",
                                    developer_message="No Orders Found",
                                    title="No Data",
                                    total_orders_count=0,
                                    orders=[],
                                ),
                                200,
                            )
                        results_formatted = []

                        for item in result:
                            item = DataHelpers.deserializer(item)
                            item["order_id"] = str(item.pop("_id", ""))
                            item.pop("added_by", "")
                            # item["bundle_expiry_date"] = item.get(
                            #     "bundle_expiry_date", None
                            # )

                            item["vendor_start_bundle"]: item.get("vendor_start_bundle", False)
                            item["vendor_expiry_days"]: item.get("vendor_expiry_days", 365)
                            item["vendor_expiry_date_profile"]: item.get("vendor_expiry_date_profile", 0)

                            if item.get("order_status") == "Successful":

                                reseller = CompleteTransactionSuccessfulResponse.from_dict(item)
                            else:
                                item.pop("profile", {})
                                reseller = CompleteTransactionFailedResponse.from_dict(item)

                            bundle_code = item["bundle_code"]
                            iccid = item["iccid"]

                            profile_exists = mongodb.find_one(
                                "profiles",
                                {"iccid": iccid, "bundle_code": bundle_code},
                                apply_Tenancy=0,
                            )
                            if profile_exists:
                                from_inventory = True
                            else:
                                from_inventory = False

                            if not from_inventory:
                                mongodb.update_one(
                                    "bundles",
                                    {"bundle_code": bundle_code},
                                    {"$inc": {"allocated_unit": 1}},
                                    apply_Tenancy=0,
                                    database_name=database_name,
                                )
                            mongodb.update_one(
                                "bundles",
                                {"bundle_code": bundle_code},
                                {"$inc": {"consumed_unit": 1}},
                                apply_Tenancy=0,
                                database_name=database_name,
                            )
                            results_formatted.append(reseller)

                        logger.info("Starting email notification thread")
                        thread = threading.Thread(
                            target=thread_to_send_email_qr,
                            args=(item, reseller_id, item["order_id"]),
                        )
                        thread.start()

                        logger.info("Transaction completed successfully")
                        return (
                            CompleteTransactionResponse(
                                response_code="200",
                                developer_message="Success",
                                title="Success",
                                total_orders_count=1,
                                orders=results_formatted,
                            ),
                            200,
                        )
                    else:
                        logger.error("Order Provided Is Not Suitable: %s", requested_order.get("_id"))

                        return (
                            CompleteTransactionResponse(
                                response_code="404",
                                developer_message="Order Provided Is Not Suitable",
                                title="No Data",
                                orders=[],
                            ),
                            404,
                        )
        except pymongo.errors.PyMongoError as e:
            logger.exception(f"Exception in mongo, order not found: {str(e)}")

            return (
                CompleteTransactionResponse(
                    response_code="404",
                    developer_message="No Orders Found",
                    title="No Data",
                    orders=[],
                ),
                404,
            )
        except Exception as e:
            logger.exception(f"Exception in complete bundle: {str(e)}")
            return (
                CompleteTransactionResponse(
                    response_code="404",
                    developer_message="No Orders Found",
                    title="No Data",
                    orders=[],
                ),
                404,
            )

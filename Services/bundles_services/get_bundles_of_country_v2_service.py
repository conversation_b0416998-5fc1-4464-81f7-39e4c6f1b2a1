from swagger_server.models.get_bundles_response import GetBundlesResponse
from helpers.db_helper import format_bundles, get_version
from app_helpers.helpers import TenancyHelpers
import connexion

import logging
logger = logging.getLogger(__name__)
def get_bundles_of_country_v2(country_code=None, bundle_category=None, page_size=None, page_number=None, reseller_id=None,
                           bundle_name=None, sort_by=None, reseller_admin_view=False, bundle_tag=None, region_code=None,
                           currency_code=None, bundle_code =None, country_code_array = None):
    try:

        sorting_order = 1
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        super_admin_view = False
        if apply_Tenancy == 0:
            if reseller_admin_view:
                super_admin_view = True

        elif apply_Tenancy == 1:
            super_admin_view = False
        else:
            if reseller_admin_view:
                raise connexion.ProblemException(status=401, title="Error",
                                                 detail="You are not Authorized to Use Reseller Admin View")

        if not page_size:
            page_size = 50
        if not page_number:
            page_number = 1
        version = get_version()
        print("version ", version)
        if sort_by:
            if sort_by == "price_asc":
                sort_by = "reseller_retail_price"
                sorting_order = 1
            elif sort_by == "price_dsc":
                sort_by = "reseller_retail_price"
                sorting_order = -1
            elif sort_by == "bundle_name":
                sort_by = "lowercase_bundle_name"
                sorting_order = 1
            elif sort_by == "data_asc":
                sort_by = "data_amount"
                sorting_order = 1
            elif sort_by == "data_dsc":
                sort_by = "data_amount"
                sorting_order = -1

        all_bundles = format_bundles(reseller_id=reseller_id,
                                     bundle_category=bundle_category,
                                     country_code=country_code,
                                     page_size=page_size,
                                     number_of_page=page_number,
                                     bundle_name=bundle_name,
                                     sort_by=sort_by,
                                     sorting_order=sorting_order,
                                     reseller_admin_view=reseller_admin_view,
                                     super_admin_view=super_admin_view,
                                     bundle_tag=bundle_tag,
                                     region_code=region_code,
                                     currency_code=currency_code,
                                     country_code_array =country_code_array)

        if all_bundles.get("bundles"):
            if currency_code and all_bundles.get("bundles")[0].get("subscriber_price_in_additional_currency") is None:
                raise connexion.ProblemException(status=400, title="Error", detail="Invalid Currency Code!")
    except connexion.ProblemException as e:
        logger.error("Error getting bundles of country v2 , Error : %s", e)
        raise e
    except Exception as e:
        logger.error("Error getting bundles of country , Error : %s", e)
        raise connexion.ProblemException(status=404, title="Error", detail=str(e))

    return GetBundlesResponse.from_dict(all_bundles)

from swagger_server.models.get_regions_response import GetRegionsResponse
from helpers.db_helper import get_all_regions
from app_helpers.helpers import TenancyHelpers
import connexion

import logging
logger = logging.getLogger(__name__)
def get_regions(reseller_id=None, reseller_admin_view=None):
    try:
        reseller_id, branch_id, apply_Tenancy = TenancyHelpers.check_reseller_id(reseller_id)
        super_admin_view = False
        if apply_Tenancy == 0:
            if reseller_admin_view:
                super_admin_view = True

        elif apply_Tenancy == 1:
            super_admin_view = False
        else:
            if reseller_admin_view:
                raise connexion.ProblemException(status=401, title="Error",
                                                 detail="You are not Authorized to Use Reseller Admin View")

        regions_dict = {
            'af': 'Africa',
            'an': 'Antarctica',
            'as': 'Asia',
            'eu': 'Europe',
            'na': 'North America',
            'sa': 'South America',
            'me': 'Middle East'
        }

        region_codes = ['', 'af', 'an', 'as', 'eu', 'na', 'sa', 'me']
        regions_result = []


        all_regions = get_all_regions(reseller_id=reseller_id, bundle_category=None, region_code=None,
                                      reseller_admin_view=reseller_admin_view, super_admin_view=super_admin_view)
        all_regions = list(all_regions)
        if all_regions:
            all_regions = all_regions[0]
            for region in all_regions.get("regions", []):
                if region in region_codes:
                    regions_result.append({"region_name": regions_dict[region], "region_code": region})
            response = GetRegionsResponse(response_code=200, regions=regions_result), 200
        else:
            response = GetRegionsResponse(response_code=404, developer_message="No regions are available",
                                          title="No regions are available", regions=[]), 200
    except connexion.ProblemException as e:
        logger.error("Error getting regions , Error : %s", e)
        raise e
    except Exception as e:
        logger.error("Error getting regions, Error : %s", e)
        raise connexion.ProblemException(status=404, title="Error", detail=str(e))

    return response

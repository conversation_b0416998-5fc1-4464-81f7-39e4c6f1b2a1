from bson import ObjectId
from connexion.exceptions import ProblemException
from keycloak.exceptions import KeycloakGetError
from Services.authorization_services import keyCloak
from Services.reseller_services.get_reseller_by_id_service import get_reseller_by_id
from app_helpers import helpers
from app_helpers import mongodb
from swagger_server.models.refresh_token_request import RefreshTokenRequest  # noqa: E501
from swagger_server.models.refresh_token_response import RefreshTokenResponse  # noqa: E501

import logging

logger = logging.getLogger(__name__)

def refresh_token(refreshTokenRequest: RefreshTokenRequest = None):
    try:
        refresh_token_str = refreshTokenRequest.refresh_token if refreshTokenRequest else None
        logger.info("Token refresh process initiated")
        logger.debug("Processing refresh token request with token: %s...",
                   refresh_token_str[:10] if refresh_token_str and len(refresh_token_str) > 10 else "<none>")

        keycloak = keyCloak.KeycloakHelper()
        logger.debug("Calling Keycloak to refresh token")
        keycloakResponse = keycloak.refresh_token(refresh_token_str)

        access_token = keycloakResponse["access_token"]
        logger.debug("Access token received, validating token")
        claims = keycloak.validate_token(access_token)

        expires_in = claims["exp"]
        refresh_token = keycloakResponse["refresh_token"]
        refresh_expires_in = keycloakResponse["refresh_expires_in"]
        reseller_id = claims.get("reseller_id", "")
        branch_id = claims.get("branch_id", "")

        logger.debug("Token validated successfully. Reseller ID: %s, Branch ID: %s", reseller_id, branch_id)

        supports_promo = True
        supports_vouchers = True
        reseller_currency_code = "USD"
        if reseller_id != "000000000000000000000000":
            logger.debug("Retrieving reseller information for reseller_id: %s", reseller_id)
            reseller = get_reseller_by_id(reseller_id)[0]
            reseller = helpers.DataHelpers.to_good_dict(reseller)

            if reseller:
                supports_promo = reseller.get("supports_promo", False)
                supports_vouchers = reseller.get("supports_vouchers", False)
                reseller_currency_code = reseller.get("default_currency_code", "USD")
                is_active = reseller.get("is_active")

                logger.debug("Reseller details - supports_promo: %s, supports_vouchers: %s, currency: %s, is_active: %s",
                           supports_promo, supports_vouchers, reseller_currency_code, is_active)

                if not is_active:
                    logger.warning("Token refresh attempt for disabled reseller: %s", reseller_id)
                    raise ProblemException(status=401, title="Error", detail="Reseller is disabled, Contact administration!")

            if branch_id:
                logger.debug("Checking branch status for branch_id: %s", branch_id)
                getQuery = {"_id": ObjectId(branch_id)}

                branch = mongodb.find_one("branch", getQuery, apply_Tenancy=0)
                if branch:
                    is_active = branch.get("is_active")
                    logger.debug("Branch status - is_active: %s", is_active)

                    if not is_active:
                        logger.warning("Token refresh attempt for disabled branch: %s", branch_id)
                        raise ProblemException(status=400, title="Error", detail="Branch is disabled, Contact administration!")
                else:
                    logger.warning("Token refresh attempt for non-existent branch: %s", branch_id)
                    raise ProblemException(status=400, title="Error", detail="Branch is disabled, Contact administration!")

        logger.info("Token refresh successful for reseller_id: %s", reseller_id)

        return RefreshTokenResponse(
            message="Credentials Valid, Access Token Refreshed",
            access_token=access_token,
            token_type="ApiKey",
            expires_in=expires_in,
            refresh_expires_in=refresh_expires_in,
            refresh_token=refresh_token,
            supports_promo=supports_promo,
            supports_vouchers=supports_vouchers,
            reseller_currency_code=reseller_currency_code,
        )

    except KeycloakGetError as e:
        logger.error("Keycloak error during token refresh: %s", str(e))
        raise ProblemException(status=400, title="Error", detail="Invalid refresh token")
    except ProblemException as e:
        logger.error("Problem exception during token refresh: %s", str(e))
        raise e
    except Exception as e:
        logger.error("Unexpected error during token refresh: %s", str(e))
        raise ProblemException(status=500, title="Error", detail="Internal Server Error")

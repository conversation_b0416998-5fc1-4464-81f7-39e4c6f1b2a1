#mongo ************************************* Testing/InitDB/mongo-init.js

#for FILE in Testing/InitDB/*.json; do
#
#    [ -f "$FILE" ] || continue
#
#    COLLECTION_TMP="${FILE##*/}"
#    COLLECTION="${COLLECTION_TMP%.*}"
#
#    echo "$COLLECTION"
#
#    mi --uri ****************************************************** --collection="$COLLECTION" "$FILE" --jsonArray
#
#done

mongorestore --port=27017 --host=mongo Testing/InitDB/ --drop
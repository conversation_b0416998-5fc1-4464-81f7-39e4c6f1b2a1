#!/bin/bash

# MongoDB connection parameters
HOST='mongo'
PORT=27017
DB_NAME='rsp_test'
USERNAME='user'
PASSWORD='password'

# Command to drop all collections
echo "Dropping all collections in the MongoDB database"
mongo --host $HOST --port $PORT --username $USERNAME --password $PASSWORD --authenticationDatabase admin $DB_NAME --eval "db.getCollectionNames().forEach(function(c) { if (c.indexOf('system.') === -1) db[c].drop(); })"

echo "All collections dropped"

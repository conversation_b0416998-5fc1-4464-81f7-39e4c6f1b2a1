import datetime
import unittest

from app_models.consumer_models import Bundles, Countries, Regions

from Services.bundles_services.get_reseller_bundles_by_scope_service import get_reseller_bundles_by_scope
from swagger_server.models.region_grouped_bundles_response import RegionGroupedBundlesResponse
from swagger_server.models.scoped_bundles_response import ScopedBundlesResponse


class TestServiceLayer(unittest.TestCase):

    def setUp(self):
        Regions.drop_collection()
        Countries.drop_collection()
        Bundles.drop_collection()

        Regions(region_code="eu", region_name="Europe", zone_name="Z1").save()
        Regions(region_code="me", region_name="Middle East", zone_name="Z2").save()
        Regions(region_code="af", region_name="Africa", zone_name="Z3").save()
        Regions(region_code="as", region_name="Asia", zone_name="Z4").save()

        Countries(country_name="France", iso2_code="FR", region_code=["eu"], currency="EUR", currency_symbol="€", zone_name="Z1").save()
        Countries(country_name="Lebanon", iso2_code="LB", region_code=["me"], currency="LBP", currency_symbol="L.L.", zone_name="Z2").save()
        Countries(country_name="Nigeria", iso2_code="NG", region_code=["af"], currency="NGN", currency_symbol="₦", zone_name="Z3").save()
        Countries(
            country_name="Australia", iso2_code="AU", region_code=["oceania"], currency="AUD", currency_symbol="$", zone_name="Z4"
        ).save()

    def create_bundle(self, **kwargs):
        default = dict(
            vendor_name="TestVendor",
            bundle_code="B1",
            bundle_name="Test Bundle",
            bundle_category="country",
            region_code="",
            region_name="",
            create_datetime=datetime.datetime.utcnow(),
            retail_price=10.0,
            data_amount=1.0,
            data_unit="GB",
            validity_amount="30",
            is_active=True,
            deleted=False,
            preview_for=["reseller"],
            country_code_list=[],
            is_region=False,
        )
        default.update(kwargs)
        return Bundles(**default).save()

    def test_service_scope_region_returns_grouped_response(self):
        self.create_bundle(
            bundle_name="EU1", bundle_code="EU1", country_code_list=["FR"], region_code="eu", region_name="Europe", is_region=True
        )
        self.create_bundle(
            bundle_name="ME1", bundle_code="ME1", country_code_list=["LB"], region_code="me", region_name="Middle East", is_region=True
        )

        response = get_reseller_bundles_by_scope(geoscope="region", page=1, limit=10)

        self.assertIsInstance(response, RegionGroupedBundlesResponse)

        codes = {b.bundle_name for r in response.regions for b in r.bundles}
        self.assertSetEqual(codes, {"EU1", "ME1"})

    def test_service_scope_countries_returns_flat_response(self):
        self.create_bundle(bundle_name="B1", bundle_code="B1", bundle_category="country")
        self.create_bundle(bundle_name="B2", bundle_code="B2", bundle_category="region")
        self.create_bundle(bundle_name="B3", bundle_code="B3", bundle_category="global")

        response = get_reseller_bundles_by_scope(geoscope="countries", page=1, limit=10)

        self.assertIsInstance(response, ScopedBundlesResponse)

        codes = {b.bundle_name for b in response.bundles}
        self.assertSetEqual(codes, {"B1", "B2", "B3"})

    def test_service_scope_global_returns_only_large_global_bundles(self):
        self.create_bundle(bundle_name="B1", bundle_code="B1", bundle_category="global", country_code_list=["US"] * 30)
        self.create_bundle(bundle_name="B2", bundle_code="B2", bundle_category="global", country_code_list=["US"] * 51)

        response = get_reseller_bundles_by_scope(geoscope="global", page=1, limit=10)

        self.assertIsInstance(response, ScopedBundlesResponse)

        codes = {b.bundle_name for b in response.bundles}
        self.assertSetEqual(codes, {"B2"})

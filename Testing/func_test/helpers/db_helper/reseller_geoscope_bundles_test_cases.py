import datetime
import unittest
from decimal import Decimal
from time import sleep

from app_models import consumer_models
from app_models.consumer_models import Bundles, Countries, Regions

from helpers import db_helper
from helpers.db_helper import (
    build_filter_stages,
    get_cached_region_mappings,
    get_reseller_global_or_countries_geoscope_bundle_pipeline,
    get_reseller_region_geoscope_bundle_pipeline,
    parse_data_size_value,
    parse_price_value,
)


class TestParseDataSizeValue(unittest.TestCase):
    def test_gb_and_mb_correctness(self):
        self.assertEqual(parse_data_size_value("2"), ("GB", Decimal("2")))
        self.assertEqual(parse_data_size_value("0.5"), ("MB", Decimal("500")))

    def test_invalid_raises(self):
        with self.assertRaises(ValueError):
            parse_data_size_value("invalid")


class TestParsePriceValue(unittest.TestCase):
    def test_valid(self):
        self.assertEqual(parse_price_value("15.99"), Decimal("15.99"))

    def test_invalid(self):
        with self.assertRaises(ValueError):
            parse_price_value("not-a-price")


class TestRegionMappingUtils(unittest.TestCase):

    def setUp(self):
        Countries.drop_collection()
        Regions.drop_collection()

        # Reset cache before each test
        db_helper._cached_region_mappings = None
        db_helper._cache_expiration = 0

        Regions(**{"region_code": "as", "region_name": "Asia", "zone_name": "Z1"}).save()

        Regions(**{"region_code": "eu", "region_name": "Europe", "zone_name": "Z2"}).save()

        Countries(
            **{
                "country_name": "France",
                "iso2_code": "FR",
                "region_code": ["eu"],
                "currency": "EUR",
                "currency_symbol": "€",
                "zone_name": "Z2",
            }
        ).save()

        Countries(
            **{
                "country_name": "Australia",
                "iso2_code": "AU",
                "region_code": ["oceania"],
                "currency": "AUD",
                "currency_symbol": "$",
                "zone_name": "Z3",
            }
        ).save()

    def test_get_cached_region_mappings_returns_expected_structure(self):
        get_cached_region_mappings(ttl_seconds=60)
        self.assertIsNotNone(db_helper._cached_region_mappings)
        self.assertGreater(db_helper._cache_expiration, 0)
        eu_entry = next((m for m in db_helper._cached_region_mappings if m["region"] == "eu"), None)
        self.assertIsNotNone(eu_entry)
        self.assertIn("FR", eu_entry["codes"])

    def test_oceania_countries_are_mapped_to_asia(self):
        get_cached_region_mappings(ttl_seconds=60)
        asia_entry = next((m for m in db_helper._cached_region_mappings if m["region"] == "as"), None)
        self.assertIsNotNone(asia_entry)
        self.assertIn("AU", asia_entry["codes"])

    def test_region_name_fallbacks_to_unknown_if_missing(self):
        Countries(
            **{
                "country_name": "Narnia",
                "iso2_code": "NA",
                "region_code": ["magicland"],
                "currency": "NRN",
                "currency_symbol": "N$",
                "zone_name": "Z9",
            }
        ).save()
        get_cached_region_mappings(ttl_seconds=60)
        unknowns = [m for m in db_helper._cached_region_mappings if m["region"] == "magicland"]
        self.assertTrue(all(m["region_name"] == "Unknown" for m in unknowns))

    def test_cached_value_is_reused_within_ttl(self):
        first = get_cached_region_mappings(ttl_seconds=999)
        old_expiration = db_helper._cache_expiration
        Countries(
            **{
                "country_name": "FakeLand",
                "iso2_code": "FL",
                "region_code": ["eu"],
                "currency": "FLD",
                "currency_symbol": "F$",
                "zone_name": "Z10",
            }
        ).save()
        second = get_cached_region_mappings(ttl_seconds=999)
        self.assertEqual(first, second)
        self.assertEqual(db_helper._cache_expiration, old_expiration)
        self.assertFalse(any("FL" in m["codes"] for m in second))

    def test_cache_refreshes_after_expiration(self):
        first = get_cached_region_mappings(ttl_seconds=1)
        old_expiration = db_helper._cache_expiration
        sleep(1.5)  # Ensure TTL expires
        Countries(
            **{
                "country_name": "RefreshLand",
                "iso2_code": "RL",
                "region_code": ["eu"],
                "currency": "RLD",
                "currency_symbol": "R$",
                "zone_name": "Z11",
            }
        ).save()
        second = get_cached_region_mappings(ttl_seconds=1)
        new_expiration = db_helper._cache_expiration
        self.assertNotEqual(first, second)
        self.assertTrue(any("RL" in m["codes"] for m in second))
        self.assertGreater(new_expiration, old_expiration)


class TestBuildFilterStages(unittest.TestCase):

    def tearDown(self):
        Bundles.drop_collection()

    def create_bundle(self, **kwargs):
        default = dict(
            vendor_name="TestVendor",
            bundle_code="TEST",
            bundle_name="Bundle",
            bundle_category="country",
            region_code="",
            region_name="",
            create_datetime=datetime.datetime.utcnow(),
            retail_price=10.0,
            data_amount=1.0,
            data_unit="GB",
            validity_amount="30",
            is_active=True,
            deleted=False,
            preview_for=["reseller"],
        )
        default.update(kwargs)
        return Bundles(**default).save()

    def assert_bundle_codes(self, pipeline, expected):
        result = list(Bundles.objects.aggregate(pipeline))
        actual = {r["bundle_code"] for r in result}
        self.assertSetEqual(actual, expected, f"Expected: {expected}, Got: {actual}")

    # DataSize FILTER TESTS
    def test_datasize_min_mb_only(self):
        for code, amount in zip(["A", "B", "C", "D"], [200, 400, 600, 800]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")
        for code, amount in zip(["E", "F"], [0.5, 1.5]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")
        pipeline = build_filter_stages("0.5", None, None, None, None, None)
        self.assert_bundle_codes(pipeline, {"C", "D", "E", "F"})

    def test_datasize_max_mb_only(self):
        for code, amount in zip(["A", "B", "C", "D"], [200, 400, 800, 1000]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")
        for code, amount in zip(["E", "F"], [0.5, 1.0]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")
        pipeline = build_filter_stages(None, "0.9", None, None, None, None)
        self.assert_bundle_codes(pipeline, {"A", "B", "C"})

    def test_datasize_min_max_mb_to_mb(self):
        for code, amount in zip(["A", "B", "C", "D"], [400, 600, 800, 1200]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")

        for code, amount in zip(["E", "F", "G"], [0.5, 1.0, 2.0]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")

        pipeline = build_filter_stages("0.5", "0.9", None, None, None, None)
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_datasize_min_gb_only(self):

        for code, amount in zip(["A", "B", "C"], [0.5, 1.0, 2.0]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")

        for code, amount in zip(["E", "F", "G"], [0.6, 600, 800]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")

        pipeline = build_filter_stages("1", None, None, None, None, None)
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_datasize_max_gb_only(self):
        for code, amount in zip(["A", "B", "C"], [0.5, 1.0, 2.0]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")

        for code, amount in zip(["E", "F", "G"], [0.6, 600, 800]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")

        pipeline = build_filter_stages(None, "1.5", None, None, None, None)
        self.assert_bundle_codes(pipeline, {"A", "B", "E", "F", "G"})

    def test_datasize_min_max_gb_to_gb(self):
        for code, amount in zip(["A", "B", "C", "D"], [0.5, 1.0, 1.5, 2.5]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="GB")

        for code, amount in zip(["E", "F", "G"], [0.6, 600, 800]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit="MB")

        pipeline = build_filter_stages("1", "2", None, None, None, None)
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_datasize_min_mb_max_gb(self):
        for code, (amount, unit) in zip(["A", "B", "C", "D", "E"], [(200, "MB"), (700, "MB"), (1.0, "GB"), (1.5, "GB"), (2.5, "GB")]):
            self.create_bundle(bundle_code=code, data_amount=amount, data_unit=unit)
        pipeline = build_filter_stages("0.5", "2", None, None, None, None)
        self.assert_bundle_codes(pipeline, {"B", "C", "D"})

    def test_datasize_min_gb_max_mb_should_error(self):
        with self.assertRaises(ValueError):
            build_filter_stages("1", "0.5", None, None, None, None)

    def test_datasize_same_unit_min_greater_than_max_should_error(self):
        with self.assertRaises(ValueError):
            build_filter_stages("4", "2", None, None, None, None)
        with self.assertRaises(ValueError):
            build_filter_stages("0.6", "0.2", None, None, None, None)

    def test_datasize_no_min_or_max(self):
        self.create_bundle(bundle_code="A", data_amount=250.0, data_unit="MB")
        self.create_bundle(bundle_code="B", data_amount=1.0, data_unit="GB")
        pipeline = build_filter_stages(None, None, None, None, None, None)
        self.assert_bundle_codes(pipeline, {"A", "B"})

    # VALIDITY FILTER TESTS

    def test_validity_min_and_max_range(self):
        for code, val in zip(["A", "B", "C", "D"], [5, 10, 15, 20]):
            self.create_bundle(bundle_code=code, validity_amount=str(val))
        pipeline = build_filter_stages(None, None, 10, 15, None, None)
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_validity_min_only(self):
        for code, val in zip(["A", "B", "C", "D"], [5, 10, 15, 20]):
            self.create_bundle(bundle_code=code, validity_amount=str(val))
        pipeline = build_filter_stages(None, None, 15, None, None, None)
        self.assert_bundle_codes(pipeline, {"C", "D"})

    def test_validity_max_only(self):
        for code, val in zip(["A", "B", "C", "D"], [5, 10, 15, 20]):
            self.create_bundle(bundle_code=code, validity_amount=str(val))
        pipeline = build_filter_stages(None, None, None, 10, None, None)
        self.assert_bundle_codes(pipeline, {"A", "B"})

    def test_validity_min_greater_than_max_should_error(self):
        with self.assertRaises(ValueError):
            build_filter_stages(None, None, 5, 4, None, None)

    def test_validity_none_should_not_filter(self):
        self.create_bundle(bundle_code="A", validity_amount="30")
        pipeline = build_filter_stages(None, None, None, None, None, None)
        self.assert_bundle_codes(pipeline, {"A"})

    # PRICE FILTER TESTS

    def test_price_min_and_max_range(self):
        for code, price in zip(["A", "B", "C", "D"], [5, 10, 15, 20]):
            self.create_bundle(bundle_code=code, retail_price=price)
        pipeline = build_filter_stages(None, None, None, None, "10", "15")
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_price_min_only(self):
        for code, price in zip(["A", "B", "C"], [5, 10, 15]):
            self.create_bundle(bundle_code=code, retail_price=price)
        pipeline = build_filter_stages(None, None, None, None, "10", None)
        self.assert_bundle_codes(pipeline, {"B", "C"})

    def test_price_max_only(self):
        for code, price in zip(["A", "B", "C"], [5, 10, 15]):
            self.create_bundle(bundle_code=code, retail_price=price)
        pipeline = build_filter_stages(None, None, None, None, None, "10")
        self.assert_bundle_codes(pipeline, {"A", "B"})

    def test_price_min_greater_than_max_should_error(self):
        with self.assertRaises(ValueError):
            build_filter_stages(None, None, None, None, "15", "10")

    def test_price_none_should_not_filter(self):
        self.create_bundle(bundle_code="A", retail_price=12.5)
        pipeline = build_filter_stages(None, None, None, None, None, None)
        self.assert_bundle_codes(pipeline, {"A"})

    # All filters combined
    def test_combined_filters_integration(self):
        # A & B match all filters
        self.create_bundle(bundle_code="A", data_amount=700, data_unit="MB", retail_price=15.0, validity_amount="20")
        self.create_bundle(bundle_code="B", data_amount=1.0, data_unit="GB", retail_price=18.0, validity_amount="25")

        # C fails price
        self.create_bundle(bundle_code="C", data_amount=1.0, data_unit="GB", retail_price=5.0, validity_amount="25")
        # D fails validity
        self.create_bundle(bundle_code="D", data_amount=1.0, data_unit="GB", retail_price=15.0, validity_amount="5")
        # E fails data size
        self.create_bundle(bundle_code="E", data_amount=100, data_unit="MB", retail_price=15.0, validity_amount="25")

        pipeline = build_filter_stages(
            data_size_min="0.5",  # Includes MB >= 500 and all GB
            data_size_max="1.5",  # Includes GB <= 1.5
            validity_days_min=10,
            validity_days_max=30,
            price_min="10",
            price_max="20",
        )
        self.assert_bundle_codes(pipeline, {"A", "B"})


class BaseGeoscopeTest(unittest.TestCase):
    def setUp(self):
        # Create required region mappings
        Regions.drop_collection()
        Countries.drop_collection()
        Bundles.drop_collection()

        Regions(region_code="eu", region_name="Europe", zone_name="Z1").save()
        Regions(region_code="me", region_name="Middle East", zone_name="Z2").save()
        Regions(region_code="af", region_name="Africa", zone_name="Z3").save()
        Regions(region_code="as", region_name="Asia", zone_name="Z4").save()
        Regions(region_code="oc", region_name="Oceania", zone_name="Z5").save()

        Countries(country_name="France", iso2_code="FR", region_code=["eu"], currency="EUR", currency_symbol="€", zone_name="Z1").save()
        Countries(country_name="Lebanon", iso2_code="LB", region_code=["me"], currency="LBP", currency_symbol="L.L.", zone_name="Z2").save()
        Countries(country_name="Nigeria", iso2_code="NG", region_code=["af"], currency="NGN", currency_symbol="₦", zone_name="Z3").save()
        Countries(
            country_name="Australia", iso2_code="AU", region_code=["oceania"], currency="AUD", currency_symbol="$", zone_name="Z4"
        ).save()
        Countries(country_name="Turkey", iso2_code="TR", region_code=["eu"], currency="TRY", currency_symbol="₺", zone_name="Z5").save()

        Countries(
            country_name="Saudi Arabia", iso2_code="SA", region_code=["me"], currency="SAR", currency_symbol="S.A.R", zone_name="Z6"
        ).save()

        Countries(country_name="Somalia", iso2_code="SO", region_code=["af"], currency="SO", currency_symbol="₦", zone_name="Z7").save()

        self.add_oc_symbol_directly = False
        if "oc" not in consumer_models.region_names_:
            self.add_oc_symbol_directly = True
            consumer_models.region_names_.append("oc")

        self.add_oc_name_directly = False
        if "Oceania" not in consumer_models.regions_:
            self.add_oc_name_directly = True
            consumer_models.regions_.append("Oceania")

    def tearDown(self):

        if self.add_oc_symbol_directly:
            consumer_models.region_names_.remove("oc")

        if self.add_oc_name_directly:
            consumer_models.regions_.remove("Oceania")

        Regions.drop_collection()
        Countries.drop_collection()
        Bundles.drop_collection()

    def create_bundle(self, **kwargs):
        default = dict(
            bundle_category="country",
            data_amount=1.0,
            data_unit="GB",
            validity_amount="30",
            is_active=True,
            deleted=False,
            preview_for=["reseller"],
            country_code_list=[],
            is_region=False,
            vendor_name="TestVendor",
            bundle_name="Test Bundle",
            region_code="",
            region_name="",
            create_datetime=datetime.datetime.utcnow(),
        )
        default.update(kwargs)
        return Bundles(**default).save()

    def extract_codes(self, pipeline):
        raw = list(Bundles.objects.aggregate(pipeline))
        return {b["bundle_code"] for b in raw[0]["data"]}


class TestCountriesAndGlobalGeoscope(BaseGeoscopeTest):
    def test_scope_countries_includes_country_region_global(self):
        self.create_bundle(bundle_code="B1", bundle_category="country")
        self.create_bundle(bundle_code="B2", bundle_category="region")
        self.create_bundle(bundle_code="B3", bundle_category="global")
        pipeline = get_reseller_global_or_countries_geoscope_bundle_pipeline(geoscope="countries", page=1, limit=10)
        codes = self.extract_codes(pipeline)
        self.assertSetEqual(codes, {"B1", "B2", "B3"})

    def test_scope_global_includes_only_large_global_bundles(self):
        self.create_bundle(bundle_code="B1", bundle_category="global", country_code_list=["US"] * 30)
        self.create_bundle(bundle_code="B2", bundle_category="global", country_code_list=["US"] * 51)
        pipeline = get_reseller_global_or_countries_geoscope_bundle_pipeline(geoscope="global", page=1, limit=10)
        codes = self.extract_codes(pipeline)
        self.assertSetEqual(codes, {"B2"})

    def test_pagination_limits_result_count(self):
        for i in range(30):
            self.create_bundle(bundle_code=f"PG{i}", bundle_category="country")
        pipeline = get_reseller_global_or_countries_geoscope_bundle_pipeline(geoscope="countries", page=1, limit=10)
        result = list(Bundles.objects.aggregate(pipeline))
        self.assertEqual(len(result[0]["data"]), 10)


class TestRegionGeoscope(BaseGeoscopeTest):
    def extract_grouped_data(self, pipeline):
        raw = list(Bundles.objects.aggregate(pipeline))
        grouped = {}
        for region_group in raw[0]["data"]:
            region = region_group["_id"]
            bundles = region_group["bundles"]
            grouped[region] = bundles
        return grouped

    def test_scope_region_groups_bundles_by_mapped_region(self):
        self.create_bundle(bundle_code="EU1", country_code_list=["FR"], region_code="eu", region_name="Europe", is_region=True)
        self.create_bundle(bundle_code="ME1", country_code_list=["LB"], region_code="me", region_name="Middle East", is_region=True)
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10)
        data = self.extract_grouped_data(pipeline)
        self.assertIn("eu", data)
        self.assertIn("me", data)
        self.assertEqual({b["bundle_code"] for b in data["eu"]}, {"EU1"})
        self.assertEqual({b["bundle_code"] for b in data["me"]}, {"ME1"})

    def test_turkey_alone_is_grouped_under_europe(self):
        self.create_bundle(bundle_code="EU1", country_code_list=["FR"], region_code="eu", region_name="Europe", is_region=True)
        self.create_bundle(bundle_code="ME1", country_code_list=["LB"], region_code="me", region_name="Middle East", is_region=True)
        self.create_bundle(bundle_code="TR1", country_code_list=["TR"], region_code="me", region_name="Middle East", is_region=True)
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10)
        data = self.extract_grouped_data(pipeline)
        self.assertIn("eu", data)
        self.assertEqual({b["bundle_code"] for b in data["eu"]}, {"TR1", "EU1"})

    def test_oceania_is_mapped_to_asia(self):
        self.create_bundle(bundle_code="OC1", country_code_list=["AU"], region_code="oc", region_name="Oceania", is_region=True)
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10)
        data = self.extract_grouped_data(pipeline)
        self.assertIn("as", data)
        self.assertEqual({b["bundle_code"] for b in data["as"]}, {"OC1"})

    def test_bundle_with_me_and_africa_creates_two_entries(self):
        self.create_bundle(bundle_code="MIX", country_code_list=["LB", "NG"], region_code="me", region_name="Middle East", is_region=True)
        self.create_bundle(
            bundle_code="MIX_Reverse", country_code_list=["SA", "SO"], region_code="af", region_name="Africa", is_region=True
        )
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10)
        data = self.extract_grouped_data(pipeline)
        self.assertIn("me", data)
        self.assertIn("af", data)
        self.assertEqual({b["bundle_code"] for b in data["me"]}, {"MIX", "MIX_Reverse"})
        self.assertEqual({b["bundle_code"] for b in data["af"]}, {"MIX", "MIX_Reverse"})

    def test_unidentified_bundles_are_included_when_flag_is_true(self):
        # Create a bundle with an unmapped country (e.g. ZZ = unknown)
        self.create_bundle(bundle_code="UNK", country_code_list=["ZZ"], is_region=True)

        # Run pipeline with default (show_unidentified=True)
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10, show_unidentified=True)
        data = self.extract_grouped_data(pipeline)

        # Assert it appears under "unidentified"
        self.assertIn("unidentified", data)
        self.assertEqual({b["bundle_code"] for b in data["unidentified"]}, {"UNK"})

    def test_unidentified_bundles_are_excluded_when_flag_is_false(self):
        self.create_bundle(bundle_code="UNK", country_code_list=["ZZ"], is_region=True)

        # Explicitly disable unidentified inclusion
        pipeline = get_reseller_region_geoscope_bundle_pipeline(page=1, limit=10, show_unidentified=False)
        data = self.extract_grouped_data(pipeline)

        self.assertNotIn("unidentified", data)

from app_models import mobiles as mb
from main import app
from bson import ObjectId
with app.app_context():
    app_user_details = mb.AppUserDetails.objects(create_date__exists=False)
    app_user_detail: mb.AppUserDetails
    for app_user_detail in app_user_details:
        if not app_user_detail.create_date:
            create_date = ObjectId(app_user_detail.id).generation_time
            app_user_detail.update(create_date=create_date)


